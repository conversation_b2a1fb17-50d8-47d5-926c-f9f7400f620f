<!--
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2023-07-16 11:49:31
 * @LastEditors: zhai
 * @LastEditTime: 2023-07-18 10:59:33
-->
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

const time = ref('');

const time_bit = reactive({
  hours: [0, 0, 0, 0, 0, 0],
  minutes: [0, 0, 0, 0, 0, 0],
  seconds: [0, 0, 0, 0, 0, 0]
});

const props = defineProps({
  digital: {
    type: Boolean,
    default: () => true
  },
  binary: {
    type: Boolean,
    default: () => false
  }
});

onMounted(() => {
  setInterval(render, 1000);
});

const render = () => {
  const d = new Date();
  const h = d.getHours();
  const m = d.getMinutes();
  const s = d.getSeconds();

  time.value = `${addZero(h)} : ${addZero(m)} : ${addZero(s)}`;

  time_bit.seconds = convert(s);
  time_bit.minutes = convert(m);
  time_bit.hours = convert(h);
};

const convert = num => {
  let bin = '';
  let conv = [];

  while (num > 0) {
    bin = (num % 2) + bin;
    num = Math.floor(num / 2);
  }

  conv = bin.split('');

  while (conv.length < 6) {
    conv.unshift('0');
  }

  return conv;
};

const addZero = i => {
  if (i < 10) {
    i = `0${i}`;
  }
  return i;
};
</script>

<template>
  <div class="dashboard-clock">
    <div v-if="digital" class="dashboard-clock-digital">{{ time }}</div>
    <table v-if="binary" class="dashboard-clock-binary">
      <tr class="hours">
        <td v-for="n in 6" :class="`num${time_bit.hours[n - 1]}`"></td>
      </tr>
      <tr class="minutes">
        <td v-for="n in 6" :class="`num${time_bit.minutes[n - 1]}`"></td>
      </tr>
      <tr class="seconds">
        <td v-for="n in 6" :class="`num${time_bit.seconds[n - 1]}`"></td>
      </tr>
    </table>
  </div>
</template>

<style lang="scss">
$cGood: #2eb35a;

.dashboard {
  &-clock {
    display: flex;
    align-items: center;

    &-digital {
      font-weight: bold;
      font-size: 2.5rem;
    }

    &-binary {
      margin-left: 8px;
      border-collapse: separate;

      td {
        height: 0.5rem;
        width: 0.5rem;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
      }

      .num1 {
        background-color: $cGood;
      }
    }
  }
}
</style>

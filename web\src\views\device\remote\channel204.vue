<script setup lang="ts">
import { defineOptions, defineProps, onMounted } from 'vue';
import type { CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { ValueBuilderContext, dict, useFs } from '@fast-crud/fast-crud';

// interface Props {
//   link: string;
// }

// defineProps<Props>();

function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  return {
    crudOptions: {
      container: {
        // is: 'fs-layout-card'
      },
      mode: {
        name: 'local',
        isMergeWhenUpdate: true,
        isAppendWhenAdd: true
      },
      settings: {
        viewFormUseCellComponent: true
      },
      table: {
        striped: true,
        editable: {
          mode: 'free',
          activeDefault: true,
          showAction: false
        }
      },

      rowHandle: {
        show: false
      },
      search: {
        show: false
      },
      toolbar: {
        show: false
      },
      actionbar: {
        show: false
      },
      pagination: {
        show: false
      },
      form: {
        wrapper: {
          draggable: false
        },
        col: {
          span: 24,
        }
      },
      columns: {
        id: {
          title: 'Channel',
          type: 'number',
          column: { width: 80, align: 'center' },
          // 以下两种方式都可以禁止编辑
          form: {
            show: false
          }
        },
        On: {
          title: 'On',
          type: 'dict-switch',
          column: { width: 80, align: 'center' }
        },

        RangeType: {
          title: 'Range/Type',
          type: 'dict-select',
          dict: dict({
            value: 'value',
            label: 'label',
            data: [
              {
                label: '+/-10V',
                value: '+/-10V'
              },
              {
                label: 'IEPE',
                value: 'IEPE'
              }
            ]
          })
        },
        Sensitivity: {
          title: 'Sensitivity',
          type: 'number'
        },

        Units: {
          title: 'Units',
          type: 'dict-select',
          form: {
            component: {
              clearable: false,
            },
          },
          dict: dict({
            value: 'value',
            label: 'label',
            data: [
              {
                label: 'mvolts/g',
                value: 'mvolts/g'
              },
              {
                label: 'volts/g',
                value: 'volts/g'
              },
              {
                label: 'mv/pa',
                value: 'mv/pa'
              },
              {
                label: 'mvolts/N',
                value: 'mvolts/N'
              },
              {
                label: 'mvolts/lb',
                value: 'mvolts/lb'
              }
            ]
          })
        },
        InputConfig: {
          title: 'Input Config',
          type: 'dict-select',
          form: {
            component: {
              clearable: false,
            },
          },
          dict: dict({
            value: 'value',
            label: 'label',
            data: [
              {
                label: 'Pseudodifferential',
                value: 'Pseudodifferential'
              },
              {
                label: 'Differential',
                value: 'Differential'
              }
            ]
          })
        },
        InputCoupling: {
          title: 'Input Coupling',
          type: 'dict-select',
          form: {
            component: {
              clearable: false,
            },
          },
          dict: dict({
            value: 'value',
            label: 'label',
            data: [
              {
                label: 'AC',
                value: 'AC'
              },
              {
                label: 'DC',
                value: 'DC'
              }
            ]
          })
        }
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

defineExpose({
  getData: () => crudBinding.value.data
});

// 本地数据
crudBinding.value.data = [
  {
    id: 0,
    On: true,
    RangeType: '+/-10V',
    Sensitivity: 1.0,
    Units: 'volts',
    InputConfig: 'Pseudodifferential',
    InputCoupling: 'AC'
  },
  {
    id: 1,
    On: true,
    RangeType: '+/-10V',
    Sensitivity: 1.0,
    Units: 'volts',
    InputConfig: 'Pseudodifferential',
    InputCoupling: 'AC'
  },
  {
    id: 2,
    On: true,
    RangeType: '+/-10V',
    Sensitivity: 1.0,
    Units: 'volts',
    InputConfig: 'Pseudodifferential',
    InputCoupling: 'AC'
  },
  {
    id: 3,
    On: true,
    RangeType: '+/-10V',
    Sensitivity: 1.0,
    Units: 'volts',
    InputConfig: 'Pseudodifferential',
    InputCoupling: 'AC'
  }
];

onMounted(() => {
  crudExpose.doRefresh();
  crudExpose.editable.enable({ mode: 'free', activeDefault: true });
})

</script>

<template>
  <FsCrud ref="crudRef" v-bind="crudBinding"></FsCrud>
</template>

<style scoped>
/* 在父组件才有效 */
:deep(.fs-container .header) {
  display: none !important;
}
</style>

from datetime import date, datetime
from enum import Enum
from functools import lru_cache
import uuid
from pydantic import BaseModel
from tortoise import models, fields 
from tortoise.exceptions import IntegrityError
from fastapi import HTTPException
from typing import List, Dict, Any, Optional, Type, Union
import base64


STRICT_VALIDATE = True

# 请求模型
class SyncOperation(str, Enum):
    create = "create"
    update = "update"
    upsert = "upsert"
    delete = "delete"

# 定义可选的表名枚举
class SyncTableName(str, Enum):
    record = "daq_record"
    series = "daq_series"
    data_pack = "daq_data_pack"


class SyncRecord(BaseModel):
    table_name: SyncTableName
    operation: SyncOperation
    data: Optional[Dict[str, Any]] = None
    where: Optional[Dict[str, Any]] = None


class BulkSyncRecord(BaseModel):
    requests: List[SyncRecord]


class SyncResponse(BaseModel):
    success: bool
    message: str
    record_id: Optional[Any] = None
    affected_rows: Optional[int] = None


class SyncService:
    def __init__(self, model_map: Dict[SyncTableName, Type[models.Model]], strict: bool = STRICT_VALIDATE):
        """
        :param model_map: 动态传入的模型映射关系
        :param strict: 是否开启严格字段校验，默认为 STRICT_VALIDATE
        """
        self.model_map = model_map
        self.strict = strict

    def validate_fields(self, model: Type[models.Model], data: Dict[str, Any]):
        """ 校验字段是否在模型中 """
        model_fields = {field for field in model._meta.fields_db_projection.keys()}
        for field in data.keys():
            if field not in model_fields:
                raise HTTPException(status_code=400, detail=f"Field '{field}' not in model '{model.__name__}'")

    def filter_valid_fields(self, model: Type[models.Model], data: Dict[str, Any]) -> Dict[str, Any]:
        """ 过滤只保留在模型中的字段 """
        model_fields = {field for field in model._meta.fields_db_projection.keys()}
        return {key: value for key, value in data.items() if key in model_fields}


    @lru_cache(maxsize=128)
    def _get_field_types(self, model: Type[models.Model]) -> Dict[str, str]:
        """
        缓存每个模型的字段类型，用于解码。
        返回：{字段名: 类型字符串}
        """
        field_types = {}
        for name, field in model._meta.fields_map.items():
            if isinstance(field, fields.BinaryField):
                field_types[name] = "binary"
            # elif isinstance(field, fields.UUIDField):
            #     field_types[name] = "uuid"
            elif isinstance(field, fields.DatetimeField):
                field_types[name] = "datetime"
            elif isinstance(field, fields.DateField):
                field_types[name] = "date"
        return field_types

    def decode_fields(self, model: Type[models.Model], data: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据字段类型自动解码数据。
        """
        field_types = self._get_field_types(model)

        for field_name, field_type in field_types.items():
            if field_name not in data:
                continue

            value = data[field_name]

            if field_type == "binary" and isinstance(value, str):
                try:
                    data[field_name] = base64.b64decode(value)
                except Exception as e:
                    raise ValueError(f"Failed to decode base64 for field '{field_name}': {str(e)}")

            # elif field_type == "uuid" and isinstance(value, str):
            #     try:
            #         data[field_name] = uuid.UUID(value)
            #     except Exception as e:
            #         raise ValueError(f"Failed to parse UUID for field '{field_name}': {str(e)}")

            elif field_type == "datetime" and isinstance(value, str):
                try:
                    data[field_name] = datetime.fromisoformat(value)
                except Exception as e:
                    raise ValueError(f"Failed to parse datetime for field '{field_name}': {str(e)}")

            elif field_type == "date" and isinstance(value, str):
                try:
                    data[field_name] = date.fromisoformat(value)
                except Exception as e:
                    raise ValueError(f"Failed to parse date for field '{field_name}': {str(e)}")

        return data


    async def get_model(self, table_name: SyncTableName) -> Type[models.Model]:
        """ 根据表名获取模型 """
        model = self.model_map.get(table_name)
        if not model:
            raise HTTPException(status_code=404, detail=f"Model for table '{table_name}' not found")
        return model

    async def process(self, req: Union[SyncRecord, List[SyncRecord]]) -> Union[SyncResponse, List[SyncResponse]]:
        """处理单条记录或批量同步"""
        # 如果是单条记录，包装成列表进行统一处理
        is_bulk = isinstance(req, list)
        if not is_bulk:
            req = [req]

        responses = []
        for record in req:
            try:
                model = await self.get_model(record.table_name)
                if record.data:
                    # 根据 STRICT_VALIDATE 设置选择验证方式
                    if self.strict:
                        self.validate_fields(model, record.data)
                    else:
                        record.data = self.filter_valid_fields(model, record.data)

                    self.decode_fields(model, record.data)

                record_id = None
                affected_rows = 0
                message = ""

                if record.operation == SyncOperation.create:
                    created_record = await model.create(**record.data)
                    record_id = created_record.id
                    message = "Created"

                elif record.operation == SyncOperation.update:
                    if not record.where:
                        raise HTTPException(status_code=400, detail="Where clause required for update")
                    affected_rows = await model.filter(**record.where).update(**record.data)
                    message = f"Updated {affected_rows}"

                elif record.operation == SyncOperation.upsert:
                    if not record.where:
                        raise HTTPException(status_code=400, detail="Where clause required for upsert")
                    affected_rows = await model.filter(**record.where).update(**record.data)
                    if affected_rows == 0:
                        combined = {**record.where, **record.data}
                        created_record = await model.create(**combined)
                        record_id = created_record.id
                        message = "Inserted (upsert)"
                        affected_rows = 1
                    else:
                        message = "Updated (upsert)"

                elif record.operation == SyncOperation.delete:
                    if not record.where:
                        raise HTTPException(status_code=400, detail="Where clause required for delete")
                    affected_rows = await model.filter(**record.where).delete()
                    message = f"Deleted {affected_rows}"

                response = SyncResponse(
                    success=True,
                    message=f"{message} record(s) in '{record.table_name}'",
                    record_id=record_id,
                    affected_rows=affected_rows,
                )
                responses.append(response)

            except IntegrityError as e:
                responses.append(SyncResponse(success=False, message=f"Integrity error: {e}"))
            except Exception as e:
                responses.append(SyncResponse(success=False, message=f"Error: {str(e)}"))

        # 如果是批量请求，返回列表；单条请求返回单个响应
        if is_bulk:
            return responses
        return responses[0]

<script setup lang="ts" name="home">
import { inject, markRaw, nextTick, onActivated, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import WaveChart from '@/components/waveChart/index.vue';
import { decode, encode } from '@msgpack/msgpack';
import { ValueBuilderContext, useFs } from '@fast-crud/fast-crud';
import type {
  AddReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  DelReq,
  EditReq,
  UserPageQuery,
  UserPageRes,
  ValueResolveContext
} from '@fast-crud/fast-crud';
import { fast_records_api as api } from '@/service/api/record';
import dayjs from 'dayjs';
import { useMessage } from 'naive-ui'
import { localStg } from '@/utils/storage';
import { EventSourcePolyfill } from 'event-source-polyfill';


function get_token() {
  const token = localStg.get('token');
  return token;
}

const message = useMessage()

const selectedRowKeys = ref([]);
const selectedRow = ref(null);

function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery): Promise<UserPageRes> => {
    return api.GetList(query);
  };
  const editRequest = async (ctx: EditReq) => {
    const { form, row } = ctx;
    form.id = row.id;
    return api.UpdateObj(form);
  };
  const delRequest = async (ctx: DelReq) => {
    const { row } = ctx;
    return api.DelObj(row.id);
  };

  const addRequest = async (req: AddReq) => {
    const { form } = req;
    return api.AddObj(form);
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest
      },
      settings: {
        viewFormUseCellComponent: true,

        plugins: {
          // 这里使用行选择插件，生成行选择crudOptions配置，最终会与crudOptions合并
          rowSelection: {
            enabled: true,
            order: -2,
            before: true,
            props: {
              multiple: false,
              crossPage: true,
              selectedRowKeys
            }
          }
        }
      },
      table: {
        striped: true,
        rowProps: (row: Record<string, any>) => {
          return {
            onClick: () => {
              selectedRowKeys.value = [row.id];
              selectedRow.value = row;
            }
          };
        },
      },
      actionbar: {
        show: false
      },
      rowHandle: {
        width: 50,
        buttons: {
          edit: { show: false },
          remove: { show: true },
          view: { show: false },
        },
      },
      search: {
        show: false
      },
      form: {
        wrapper: {
          draggable: false
        }
      },
      toolbar: {
        show: false
      },
      columns: {
        name: {
          title: '名称',
          type: 'button',
          search: { show: true },

          column: {
            component: {
              type: 'success',
              size: 'small',
              on: {
                onClick({ row }) {
                  GetRecordSse(row.id);
                  // GetRecordSse(row.record_key);
                  // message.success('按钮点击:' + row.button);
                },
              },
            },
          },
        },

        record_time: {
          title: '记录时间',
          type: 'datetime',
          valueBuilder(context) {
            const { value, row, key } = context;
            if (value) {
              row[key] = dayjs(value).valueOf();
            }
          },
          valueResolve(context) {
            const { value, form, key } = context;
            if (value) {
              form[key] = dayjs(value).format('YYYY-MM-DD');
            }
          },
        },
        record_duration: {
          title: '记录时长',
          type: 'number',
          search: { show: true },
          column: {
            sorter: 'custom',
          }
        },
        description: {
          title: '备注',
          type: 'text',
          search: { show: true },
          column: {
            sorter: 'custom',
          }
        },
        record_key: {
          title: '记录key',
          type: 'text',
          column: {
            show: false,

            editable: {
              disabled: true
            }
          }
        }
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

const chartRefT = ref();
const chartRefP = ref();
const chartRefXY = ref();

const options_t = reactive({
  title: '时域',
  darkMode: false,
  yAxis: {
    type: 'value',
    max: 10,
    min: 0
  },
  autoYAxis: true
});


const Base64ToBytesArr = data => {
  const arr = decode(
    new Uint8Array(
      window
        .atob(data)
        .split('')
        .map(char => char.charCodeAt(0))
    )
  );
  return arr;
};

let channel_last_time = 0;
function base64ToFloat32Array(base64String) {
  // Convert base64 to binary array
  const binaryString = window.atob(base64String);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // Convert to Float32Array
  return new Float32Array(bytes.buffer);
}

const GetRecordSse = (key) => {
  chartRefT.value.clear();

  const token = get_token();

  const base_url = import.meta.env.VITE_SERVICE_BASE_URL
  const eventSource = new EventSourcePolyfill(`${base_url}/records/record_sse/${key}`, {
    headers: {
      token,
      Authorization: `Bearer ${token}`
    }
  });

  eventSource.onopen = function (event) {
    // 与服务器连接成功回调
    if (eventSource.readyState == 0) {
      console.log('sse通道未建立');
    }
    if (eventSource.readyState == 1) {
      console.log('sse通道连接成功');
    }
    if (eventSource.readyState == 2) {
      console.log('sse通道断开连接');
    }
  };

  eventSource.onmessage = function (event) {
    const event_data = event.data;
    if (event_data === '__END__') {
      console.log('Received end event, stop receiving data.');
      eventSource.close(); // 关闭EventSource对象，停止接收数据
    } else {

      // "{'0': {'series': 'CH0:acc', 'unit': 'mvolts/g', 'pack_data': 'OT0VPWfBLD5sHcI+vI8OP6eRSz8XwmQ/0z+eP6n5oT973LM/dfy+PzfDzD8Z/Ms/

      // Parse the JSON string
      const data = JSON.parse(event_data);

      let data_len = 0;
      let cur_time = 0;
      // Process each channel
      const chartdata_t = { x: [], y: [] };
      for (const ch in data) {
        const channel = data[ch];
        const series = channel.series;
        const unit = channel.unit;
        cur_time = channel.record_time;
        const vals = base64ToFloat32Array(channel.pack_data);
        data_len = vals.length;
        chartdata_t.y.push({ name: `通道${ch}`, value: vals });
      }

      if (data_len > 0) {
        const step = (cur_time - channel_last_time) / (data_len - 1);
        chartdata_t.x = Array.from({ length: data_len }, (_, index) => channel_last_time + index * step);
        chartRefT.value.appendData(chartdata_t, 10000);
      }

      channel_last_time = cur_time;

      // const sensorData = Base64ToBytesArr(data);
      // // 时域
      // if (sensorData.t.length) {
      // 	var chartdata_t = {}
      // 	chartdata_t.x = sensorData.t[0].value.x;
      // 	chartdata_t.y = [];

      // 	for (const it of sensorData.t) {
      // 		chartdata_t.y.push({ name: it.name, value: it.value.y })
      // 	}

      // 	chartRefT.value.appendData(chartdata_t, 100000);
      // }
      // // 频域
      // if (sensorData.p.length) {
      // 	var chartdata_p = {}
      // 	chartdata_p.x = sensorData.p[0].value.x;
      // 	chartdata_p.y = [];

      // 	for (const it of sensorData.p) {
      // 		chartdata_p.y.push({ name: it.name, value: it.value.y })
      // 	}

      // 	chartRefP.value.setData(chartdata_p);
      // }
      // // FREE
      // if (sensorData.xy.length) {
      // 	chartRefXY.value.appendData(sensorData.xy, 10000);
      // 	// chartRefXY.value.setData(sensorData.xy);
      // }
    }
  };

  eventSource.onerror = function (error) {
    console.error('Error:', error);
    eventSource.close(); // 关闭EventSource对象
  };
};

onMounted(() => {

  crudExpose.doRefresh();



});

const handleLinkClick = key => {

  GetRecordSse(key);
};

onUnmounted(() => { });

// 由于页面缓存原因，keep-alive
onActivated(() => { });

// 监听 pinia 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
// watch(
// 	() => isTagsViewCurrenFull.value,
// 	() => { }
// );

// 监听 pinia 中是否开启深色主题
// watch(
// 	() => themeConfig.value.isIsDark,
// 	(isIsDark) => {
// 		options_t.darkMode = isIsDark;
// 	},
// 	{
// 		deep: true,
// 		immediate: true,
// 	}
// );
</script>

<template>
  <div class="layout-padding">
    <NSplit direction="horizontal" class="h-full" :max="0.75" :min="0.25" :default-size="0.4">
      <template #1>
        <NCard class="h-full" :bordered="false">
          <FsCrud ref="crudRef" v-bind="crudBinding"></FsCrud>
        </NCard>
      </template>
      <template #2>
        <NCard :bordered="false" class="h-full card-wrapper">
          <WaveChart ref="chartRefT" height="100%" :options="options_t"></WaveChart>
        </NCard>
      </template>
    </NSplit>
  </div>
</template>

<style scoped lang="scss">
.table-card {
  height: 100%;
}

.chart-card {
  height: 100%;
  display: flex;
}

.chart-container {
  height: 100%;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  position: relative;
}
</style>

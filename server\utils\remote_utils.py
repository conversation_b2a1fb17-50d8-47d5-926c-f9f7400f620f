import requests
import json

from redis.asyncio import ConnectionPool, Redis
from utils.tools import get_uuid_str, get_uuid_str_without_hyphen
from settings import APP_SETTINGS


def get_mqtt_clients(url: str, username: str, password: str):
    headers = {"Content-Type": "application/json"}

    # 发送带有身份验证凭据的GET请求
    response = requests.get(url, headers=headers, auth=(username, password))

    # 检查响应状态码
    if response.status_code == 200:
        # 响应成功，解析JSON数据
        data = response.json()
        # 处理数据
        return data["data"]

    else:
        # 响应失败
        print("请求失败，状态码：", response.status_code)
        return None


fastapi_call_queue = APP_SETTINGS.FAST_API_CALL_QUEUE


async def remote_call_gateway(redis: Redis, topic: str, payload: dict, timeout: int = 10):
    # 生成唯一的回复ID
    reply_id = "remote_reply_" + get_uuid_str_without_hyphen()

    # 发送消息
    message = {"topic": topic, "payload": payload}

    request = {"message": message, "reply_id": reply_id}
    try:
        await redis.rpush(fastapi_call_queue, json.dumps(request, default=str).encode("utf-8"))

        # 等待服务端返回结果
        response = await redis.blpop(reply_id, timeout)
        if response:
            return json.loads(response[1].decode("utf-8"))
        else:
            return {"success": False, "msg": "timeout"}

    except Exception as e:
        return {"success": False, "msg": str(e)}


# 直接向mqtt消息队列追加消息，绕过RPC队列环节
gateway_mqtt_publish_queue = APP_SETTINGS.GATEWAY_MQTT_PUBLISH_QUEUE


async def remote_mqtt_gateway(
    redis: Redis, topic: str, payload: dict, reply_id: str | None = None, timeout: int = 10
):
    # 发送消息
    message = {"topic": topic, "payload": payload, "reply_id": reply_id}

    try:
        await redis.rpush(gateway_mqtt_publish_queue, json.dumps(message, default=str).encode("utf-8"))

        # 等待服务端返回结果
        if reply_id:
            response = await redis.blpop(reply_id, timeout)
            if response:
                return json.loads(response[1].decode("utf-8"))
            else:
                return {"success": False, "msg": "timeout"}
        else:
            return {"success": True, "msg": "success"}

    except Exception as e:
        return {"success": False, "msg": str(e)}

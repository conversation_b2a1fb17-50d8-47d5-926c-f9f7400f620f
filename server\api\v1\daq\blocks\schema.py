from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class BlocksCreateDTO(BaseModel):
    name: str
    code: Optional[str] = None
    description: Optional[str] = None
    status: Optional[bool] = True


class BlocksDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    status: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)

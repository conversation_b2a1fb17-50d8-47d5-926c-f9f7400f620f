#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/git/tvs/tvs_site/web/node_modules/.pnpm/bumpp@9.9.1/node_modules/bumpp/bin/node_modules:/mnt/e/git/tvs/tvs_site/web/node_modules/.pnpm/bumpp@9.9.1/node_modules/bumpp/node_modules:/mnt/e/git/tvs/tvs_site/web/node_modules/.pnpm/bumpp@9.9.1/node_modules:/mnt/e/git/tvs/tvs_site/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/git/tvs/tvs_site/web/node_modules/.pnpm/bumpp@9.9.1/node_modules/bumpp/bin/node_modules:/mnt/e/git/tvs/tvs_site/web/node_modules/.pnpm/bumpp@9.9.1/node_modules/bumpp/node_modules:/mnt/e/git/tvs/tvs_site/web/node_modules/.pnpm/bumpp@9.9.1/node_modules:/mnt/e/git/tvs/tvs_site/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../bumpp/bin/bumpp.js" "$@"
else
  exec node  "$basedir/../bumpp/bin/bumpp.js" "$@"
fi

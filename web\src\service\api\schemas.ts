/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-21 19:42:39
 * @LastEditors: zhai
 * @LastEditTime: 2024-06-21 20:01:57
 */
import { request } from '@/service/request';
import { CrudApi } from '../crud-api';
import { FastCrudApi } from '../fast-crud-api';

/// ///////////////////////////////////////////////////////////////////

export class FastSchemasApi extends FastCrudApi<FastSchemasApi> {
  constructor() {
    super('schemas');
  }

}

export const fast_schemas_api = FastSchemasApi.instance();

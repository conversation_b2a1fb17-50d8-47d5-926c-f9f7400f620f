import {
  initCustomFormatter,
  init_runtime_dom_esm_bundler,
  warn
} from "./chunk-TID6LRNE.js";

// node_modules/.pnpm/vue@3.5.13_typescript@5.7.2/node_modules/vue/dist/vue.runtime.esm-bundler.js
init_runtime_dom_esm_bundler();
init_runtime_dom_esm_bundler();
function initDev() {
  {
    initCustomFormatter();
  }
}
if (true) {
  initDev();
}
var compile = () => {
  if (true) {
    warn(
      `Runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".`
    );
  }
};

export {
  compile
};
/*! Bundled license information:

vue/dist/vue.runtime.esm-bundler.js:
  (**
  * vue v3.5.13
  * (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
  * @license MIT
  **)
*/
//# sourceMappingURL=chunk-7RC5I7IX.js.map

"""
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-01-12 20:12:46
LastEditors: zhai
LastEditTime: 2025-01-12 22:18:47
"""

from tortoise import fields
from fastapi_crud_tortoise import CrudModel


class DictCateModel(CrudModel):
    """字典类别表，用于存储字典的分类"""

    code = fields.CharField(max_length=50, unique=True)  # 类别代码
    name = fields.CharField(max_length=100)  # 类别名称
    description = fields.TextField(null=True)  # 描述
    items = fields.ReverseRelation["DictItemModel"]

    class Meta:
        table = "dict_cate"

    def __str__(self):
        return f"{self.code} - {self.name}"


class DictItemModel(CrudModel):
    """字典值表，用于存储字典类别下的具体字典值"""

    category = fields.ForeignKeyField(
        "app_system.DictCateModel", related_name="items", description="外键，指向字典类别表"
    )
    parent = fields.ForeignKeyField(
        "app_system.DictItemModel", related_name="children", null=True, description="父节点"
    )  # 新增字段
    code = fields.CharField(max_length=50, description="字典编码")
    value = fields.CharField(max_length=255, description="字典值")
    ext = fields.CharField(max_length=255, null=True, description="扩展字段")
    order = fields.IntField(default=0, description="排序字段")
    description = fields.TextField(null=True, description="描述")

    class Meta:
        table = "dict_item"
        unique_together = (("category", "code"),)  # 同一类别下编码唯一

    def __str__(self):
        return f"{self.code} - {self.value}"

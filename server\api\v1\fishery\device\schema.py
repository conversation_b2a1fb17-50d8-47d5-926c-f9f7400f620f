from pydantic import BaseModel, ConfigDict, Field, constr, conlist
from datetime import datetime, date
from typing import List, Optional, Literal, Annotated
from api.v1.fishery.organization.schema import Ref_OrganizationDTO
from api.v1.operations.schema import OperationsCreateDTO
from api.v1.system_manage.tenants import TenantDTO
from schemas.users import Ref_UserDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE, ExtraFieldBase
from annotated_types import Predicate

# 定义时间格式的正则表达式
TIME_FORMAT_REGEX = r"^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$"

# 定义时间类型
TimeFormat = Annotated[str, Field(pattern=TIME_FORMAT_REGEX)]


class DeviceCreateDTO(BaseModel):
    sn: str
    organization_id: Optional[MODEL_ID_TYPE] = None
    tags_refids: Optional[List[MODEL_ID_TYPE]] = None
    name: str
    type: str
    model: str
    version: Optional[str] = None


class Ref_DeviceDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    sn: Optional[str] = None
    name: Optional[str] = None
    organization_id: Optional[MODEL_ID_TYPE] = None
    organization: Optional[Ref_OrganizationDTO] = None
    binder_id: Optional[MODEL_ID_TYPE] = None
    binder: Optional[Ref_UserDTO] = None
    type: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class DeviceUpdateDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    sn: Optional[str] = None
    name: Optional[str] = None
    organization_id: Optional[MODEL_ID_TYPE] = None
    organization: Optional[Ref_OrganizationDTO] = None
    tags_refids: Optional[List[MODEL_ID_TYPE]] = None
    binder_id: Optional[MODEL_ID_TYPE] = None
    binder: Optional[Ref_UserDTO] = None
    type: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


###################################################################################
# 投喂计划
###################################################################################


class MealTask(BaseModel):
    time: TimeFormat  # 限制为 "01:00:00" 格式
    ratio: int
    weight: Optional[int] = None
    days: List[Literal["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"] | str]


class GapTask(BaseModel):
    # timeRange: Annotated[List[TimeFormat], Field(min_items=2, max_items=2)]  # 限制为两个时间的数组
    startTime: TimeFormat
    endTime: TimeFormat
    ratio: int
    weight: Optional[int] = None
    duration: int
    rest: Optional[int] = None
    days: List[Literal["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"] | str]


class ManualTaskR1(BaseModel):
    weight: int

class ManualTaskP(BaseModel):
    total_time: int
    duration: int
    gap_duration: int


class AutoTask(BaseModel):
    t: str
    d: int
    gd: int
    days: str | int

class Task(BaseModel):
    mode: Optional[Literal["meal", "gap", "manual", "auto"]] = None
    weight: Optional[int] = None
    meal: Optional[List[MealTask]] = None
    gap: Optional[List[GapTask]] = None
    auto: Optional[List[AutoTask]] = None
    manual: Optional[ManualTaskR1 | ManualTaskP] = None

class SystemR1(BaseModel):
    gear: int
    speed: int
    model: Optional[str] = None
    feed: Optional[str] = None

class SystemP(BaseModel):
    gear: int
    speed: int
    empty_time: int
    feed: str

class Lead(BaseModel):
    time: int
    gear: int
    speed: int


class DeviceParamsDTO(BaseModel):
    task: Optional[Task] = None
    system: Optional[SystemR1|SystemP] = None
    lead: Optional[Lead] = None


class DeviceUpdateParamsDTO(BaseModel):
    device_ids: list[MODEL_ID_TYPE]
    params: DeviceParamsDTO = None
    operation: Optional[OperationsCreateDTO] = None


###################################################################################


class Ref_TagDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


###################################################################################


class DeviceDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    sn: Optional[str] = None
    name: Optional[str] = None
    organization_id: Optional[MODEL_ID_TYPE] = None
    organization: Optional[Ref_OrganizationDTO] = None
    tags: Optional[List[Ref_TagDTO]] = None
    tags_refids: Optional[List[MODEL_ID_TYPE]] = None
    binder_id: Optional[MODEL_ID_TYPE] = None
    binder: Optional[Ref_UserDTO] = None
    type: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None
    params: Optional[DeviceParamsDTO] = None
    params_time: Optional[datetime] = None
    params_confirmed: Optional[DeviceParamsDTO] = None
    params_confirmed_time: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class DeviceStatus(ExtraFieldBase):
    last_online_time: Optional[str] = Field(default=None, description="最后在线时间")
    online_status: Optional[str] = Field(default=None, description="在线状态")
    sys_run_time: Optional[int] = Field(default=None, description="系统运行时间")
    fault_status: Optional[int] = Field(default=None, description="故障码")
    feed_status: Optional[int] = Field(default=None, description="投喂状态，0：空状态，1：投料中，2：已暂停")
    signal_strength: Optional[int] = Field(default=None, description="信号强度")


class DeviceDetailFeed(ExtraFieldBase):
    task_count: Optional[int] = Field(default=None, ge=0, description="任务总数")
    finish_count: Optional[int] = Field(default=None, ge=0, description="已完成任务数")
    weight: Optional[float] = Field(default=None, ge=0, description="当前重量")
    todayWeight: Optional[float] = Field(default=None, ge=0, description="今日总重量")


class DeviceDetailFilterDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = Field(default=None, description="ID")
    customer_name: Optional[str] = Field(default=None, description="客户名")
    binder_name: Optional[str] = Field(default=None, description="绑定人")
    name_sn: Optional[str] = Field(default=None, description="名称或SN")
    organization_ids: Optional[list[MODEL_ID_TYPE]] = Field(default=None, description="组织ID")
    tags: Optional[list[str]] = Field(default=None, description="标签")
    type: Optional[str] = Field(default=None, description="类型")
    model: Optional[str] = Field(default=None, description="型号")


class DeviceDetailDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    sn: Optional[str] = None
    name: Optional[str] = None
    tenant_id: Optional[MODEL_ID_TYPE] = None
    tenant: Optional[TenantDTO] = None
    organization_id: Optional[MODEL_ID_TYPE] = None
    organization: Optional[Ref_OrganizationDTO] = None
    fishery: Optional[str] = None
    workshop: Optional[str] = None
    tags: Optional[List[Ref_TagDTO]] = None
    tags_refids: Optional[List[MODEL_ID_TYPE]] = None
    binder_id: Optional[MODEL_ID_TYPE] = None
    binder: Optional[Ref_UserDTO] = None
    type: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None
    params: Optional[DeviceParamsDTO] = None
    params_time: Optional[datetime] = None
    params_confirmed: Optional[DeviceParamsDTO] = None
    params_confirmed_time: Optional[datetime] = None
    status: Optional[DeviceStatus] = None
    daily_feed: Optional[DeviceDetailFeed] = None

    model_config = ConfigDict(from_attributes=True)


class HandshakeDTO(BaseModel):
    sn: str
    timeout: Optional[int] = 30


class RebindDTO(BaseModel):
    device_ids: list[MODEL_ID_TYPE]
    user_account: str

<script setup lang="ts" name="home">
import { inject, markRaw, nextTick, onActivated, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import WaveChart from '/@/components/waveChart/index.vue';
import FreeChart from '/@/components/freeChart/index.vue';
import { decode, encode } from '@msgpack/msgpack';
import { Pane, Splitpanes } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import { RecordApi } from '/@/api/iot/record';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { Session } from '/@/utils/storage';
import config from '/@/config/config';
import moment from 'moment';

const storesTagsViewRoutes = useTagsViewRoutes();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);

const chartRefT = ref();
const chartRefP = ref();
const chartRefXY = ref();

const options_t = reactive({
  title: '时域',
  darkMode: false,
  yAxis: {
    type: 'value',
    max: 10,
    min: 0
  },
  autoYAxis: true
});

// chartRef.value.appendData(sensorData, 3000);

const Base64ToBytesArr = data => {
  const arr = decode(
    new Uint8Array(
      window
        .atob(data)
        .split('')
        .map(char => char.charCodeAt(0))
    )
  );
  return arr;
};

let channel_last_time = 0;

const GetRecordSse = key => {
  chartRefT.value.clear();

  const token = Session.get('token');

  const eventSource = new EventSourcePolyfill(`${config.BaseUrl}/sensor_record/record_sse/${key}`, {
    headers: {
      token,
      Authorization: `Bearer ${token}`
    }
  });

  eventSource.onopen = function (event) {
    // 与服务器连接成功回调
    if (eventSource.readyState == 0) {
      console.log('sse通道未建立');
    }
    if (eventSource.readyState == 1) {
      console.log('sse通道连接成功');
    }
    if (eventSource.readyState == 2) {
      console.log('sse通道断开连接');
    }
  };

  eventSource.onmessage = function (event) {
    const data = event.data;
    if (data === '__END__') {
      console.log('Received end event, stop receiving data.');
      eventSource.close(); // 关闭EventSource对象，停止接收数据
    } else {
      const sensorData = Base64ToBytesArr(data);
      const cur_time = sensorData.timestamp;
      console.log('SSE Received data: ', cur_time);

      const chartdata_t = { x: [], y: [] };
      let data_len = 0;
      for (const ch in sensorData.channels) {
        data_len = sensorData.channels[ch].length;
        chartdata_t.y.push({ name: `通道${ch}`, value: sensorData.channels[ch] });
      }

      if (data_len > 0) {
        const step = (cur_time - channel_last_time) / (data_len - 1);
        chartdata_t.x = Array.from({ length: data_len }, (_, index) => channel_last_time + index * step);
        chartRefT.value.appendData(chartdata_t, 10000);
      }

      channel_last_time = cur_time;

      // const sensorData = Base64ToBytesArr(data);
      // // 时域
      // if (sensorData.t.length) {
      // 	var chartdata_t = {}
      // 	chartdata_t.x = sensorData.t[0].value.x;
      // 	chartdata_t.y = [];

      // 	for (const it of sensorData.t) {
      // 		chartdata_t.y.push({ name: it.name, value: it.value.y })
      // 	}

      // 	chartRefT.value.appendData(chartdata_t, 100000);
      // }
      // // 频域
      // if (sensorData.p.length) {
      // 	var chartdata_p = {}
      // 	chartdata_p.x = sensorData.p[0].value.x;
      // 	chartdata_p.y = [];

      // 	for (const it of sensorData.p) {
      // 		chartdata_p.y.push({ name: it.name, value: it.value.y })
      // 	}

      // 	chartRefP.value.setData(chartdata_p);
      // }
      // // FREE
      // if (sensorData.xy.length) {
      // 	chartRefXY.value.appendData(sensorData.xy, 10000);
      // 	// chartRefXY.value.setData(sensorData.xy);
      // }
    }
  };

  eventSource.onerror = function (error) {
    console.error('Error:', error);
    eventSource.close(); // 关闭EventSource对象
  };
};

const crud_states_review = reactive({
  option: null,
  data: [],
  page: {},
  form: {},
  curRow: null,
  loading: false,
  key: 'crud_key_crud_table_review'
});

crud_states_review.page = {
  background: false,
  total: 1000,
  currentPage: 1,
  // layout: "total, sizes, prev, pager, next, jumper",
  layout: 'total, sizes, pager'
};

const sizeChange = val => {
  crud_states_review.page.currentPage = 1;
  crud_states_review.page.pageSize = val;
  getList();
};

const currentChange = val => {
  crud_states_review.page.currentPage = val;
  getList();
};

crud_states_review.option = {
  // index: true,
  // stripe: true,
  // rowKey: 'id',
  // viewBtn: true,
  header: false,
  menu: false,
  menuWidth: 280,
  dialogWidth: 600,
  highlightCurrentRow: true,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  column: [
    {
      width: 100,
      label: '名称',
      prop: 'name',
      hide: false,
      sortable: true
    },
    {
      width: 160,
      label: '记录时间',
      prop: 'record_time',
      hide: false,
      sortable: false,
      formatter: (val, value, label) => {
        return moment(value).format('YYYY-MM-DD HH:mm:ss');
      }
    },
    {
      width: 100,
      label: '记录长度',
      prop: 'record_duration',
      hide: false,
      sortable: false
    },
    {
      label: '备注',
      prop: 'description',
      sortable: false
    }
  ]
};

onMounted(() => {
  const option = localStorage.getItem(crud_states_review.key);
  if (option) {
    // this.option = JSON.parse(option)
  }

  getList();
});

// 初始化表格数据
const getList = () => {
  crud_states_review.loading = true;
  const cur_page = crud_states_review.page.currentPage;
  const page_size = crud_states_review.page.pageSize;

  new RecordApi()
    .list(null, null, (cur_page - 1) * page_size, page_size)
    .then(res => {
      crud_states_review.data = res.data;
      crud_states_review.page.total = res.meta.total;
    })
    .finally(() => {
      crud_states_review.loading = false;
    });
};

const handleLinkClick = key => {
  GetRecordSse(key);
};

onUnmounted(() => {});

// 由于页面缓存原因，keep-alive
onActivated(() => {});

// 监听 pinia 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
watch(
  () => isTagsViewCurrenFull.value,
  () => {}
);

// 监听 pinia 中是否开启深色主题
watch(
  () => themeConfig.value.isIsDark,
  isIsDark => {
    options_t.darkMode = isIsDark;
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <ElContainer>
        <Splitpanes class="default-theme" style="height: 100%">
          <Pane size="30">
            <ElCard class="table-card">
              <AvueCrud
                ref="crud"
                style="height: 100%"
                :option="crud_states_review.option"
                @row-save="rowSave"
                @row-update="rowUpdate"
                :data="crud_states_review.data"
                @row-del="rowDel"
                v-model="crud_states_review.form"
                @current-row-change="handleCurrentRowChange"
                v-model:page="crud_states_review.page"
                :table-loading="crud_states_review.loading"
                @size-change="sizeChange"
                @current-change="currentChange"
              >
                <template #name="scope">
                  <ElLink type="primary" :underline="false" @click="handleLinkClick(scope.row.record_key)">
                    {{ scope.row.name }}
                  </ElLink>
                </template>
              </AvueCrud>
            </ElCard>
          </Pane>
          <Pane>
            <ElCard class="chart-card">
              <div class="chart-container">
                <WaveChart ref="chartRefT" height="100%" :options="options_t"></WaveChart>
              </div>
            </ElCard>
          </Pane>
        </Splitpanes>
      </ElContainer>
    </div>
  </div>
</template>

<style scoped lang="scss">
.table-card {
  height: 100%;
}

.chart-card {
  height: 100%;
  display: flex;
}

.el-card ::v-deep .el-card__body {
  flex: 1;
}

.chart-container {
  height: 100%;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  position: relative;
}
</style>

from datetime import datetime
from tortoise import Model, fields
from fastapi_crud_tortoise import CrudModel


class UpdaterModel(CrudModel):
    name = fields.CharField(max_length=255)
    time = fields.DatetimeField(auto_now_add=True, description="发布时间")
    firmware = fields.ForeignKeyField(
        "app_system.FirmwareModel", related_name="updater", null=False, description="固件"
    )
    type = fields.CharField(max_length=255)
    model = fields.CharField(max_length=255)
    devices = fields.JSONField(null=True, default=None)
    result = fields.CharField(max_length=255, null=True)
    progress = fields.Char<PERSON>ield(max_length=255, null=True)
    notes = fields.Char<PERSON>ield(max_length=255, null=True)

    class Meta:
        table = "updater"

<script setup lang="ts">
import {
  computed,
  defineAsyncComponent,
  defineComponent,
  inject,
  nextTick,
  onMounted,
  reactive,
  ref,
  watch
} from 'vue';
// import 'font-awesome/css/font-awesome.min.css';
import { useMQTT } from 'mqtt-vue-hook';
import DashboardClock from '@/components/dashboardClock/index.vue';

import { useIotStore } from '@/store/iot';

const iotStore = useIotStore();

const title = ref('物联服务');
const state = reactive({
  servers: [
    {
      name: 'MQTT Broker',
      status: false,
      adr: iotStore.mqtt.host,
      ico: 'fa-recycle'
    },

    {
      name: 'Redis',
      status: true,
      adr: '',
      ico: 'fa-database'
    }
  ],
  terminals: []
});

watch(
  () => iotStore.card_map,
  card_map => {
    for (const [key, card] of card_map) {
      if (card.connected) {
        if (!state.terminals.includes(card)) {
          state.terminals.push(card);
        }
      } else {
        const index = state.terminals.indexOf(card);

        if (index !== -1) {
          state.terminals.splice(index, 1);
        }
      }
    }
  },
  {
    immediate: true,
    deep: true
  }
);


onMounted(() => {
  const mqttHook = useMQTT();
  state.servers[0].status = mqttHook.isConnected();

  // 定时检查在线状态
  setInterval(() => {
    state.servers[0].status = mqttHook.isConnected();
  }, 5000);

  // mqttHook.showAllClient().then(clients => {
  // 	console.log(clients)

  // })

  // getStatus()
});
</script>

<template>
  <div class="layout-padding background">
    <div class="dashboard">
      <div class="dashboard-header">
        <h1 class="dashboard-title">{{ title }}</h1>
        <DashboardClock digital="true" binary="true" />
      </div>
      <div class="server-list">
        <div v-for="(server, index) in state.servers" class="server" :class="{ 'has-failed': !server.status }">
          <div class="server-icon fa" :class="server.ico"></div>
          <div class="server-details">
            <div class="row">
              服务:
              <span class="data">{{ server.name }}</span>
            </div>
            <div class="row">
              状态:
              <span class="data signal">{{ server.status ? 'ONLINE' : 'OFFLINE' }}</span>
            </div>
            <div class="row">
              地址:
              <span class="data">{{ server.adr }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="dashboard-header">
        <h1 class="dashboard-title">采集卡</h1>
      </div>
      <div class="server-list">
        <div v-for="(card, index) in state.terminals" class="server" :class="{ 'has-failed': !card.connected }">
          <div class="server-icon fa" :class="card.ico"></div>
          <div class="server-details">
            <div class="row">
              名称:
              <span class="data">{{ card.name }}</span>
            </div>
            <div class="row">
              状态:
              <span class="data signal">{{ card.connected ? 'ONLINE' : 'OFFLINE' }}</span>
            </div>
            <div class="row">
              地址:
              <span class="data">{{ card.ip }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
$cGood: #2eb35a;
$cAlert: #d22c32;
$orange: #ff6b23;
$blue: #54c6fb;

* {
  user-select: none;
  cursor: default;
}

.background {
  color: #c1c6cb;
  background-color: #15202e;
}

.dashboard {
  display: block;
  // max-width: 1024px;
  // margin: 0 auto;

  &-header {
    display: flex;
    align-items: center;
    font-family: 'Lato', sans-serif;
    text-transform: uppercase;
    margin: 30px 0;
  }

  &-title {
    flex: 1;
    font-size: 2.5rem;
  }
}

.server {
  display: flex;
  align-items: center;

  padding: 0 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  white-space: nowrap;
  background-color: rgba(255, 255, 255, 0.1);

  &:hover {
    background-color: rgba($blue, 0.2);
  }

  &-icon {
    display: inline-block;
    font-size: 2.5rem;
    margin: 0 12px 0 22px;
  }

  &-details {
    flex: 1;
    padding: 10px;

    .row {
      font-size: 1.2rem;
      line-height: 1.5;
      color: #7e8794;
      display: flex;
      justify-content: space-between;
      padding: 2px 0;

      &:last-child {
        .data {
          font-weight: normal;
          color: rgba(230, 245, 255, 0.32);
        }
      }
    }

    .data {
      padding: 0 1rem 0 0;
      font-weight: 600;
      text-align: right;
      color: #c1c6cb;
    }

    .signal {
      color: $cGood;
    }
  }

  &-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(22rem, 1fr));
    grid-gap: 1rem;
  }

  &.has-failed {
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.2);
    border-color: $cAlert;
    animation: alertblink 2s ease-in-out infinite;

    .server-icon,
    .signal {
      color: $cAlert;
    }

    &:hover {
      background-color: rgba($cAlert, 0.2);
      animation: none;
    }
  }
}

@keyframes alertblink {
  0% {
    background: rgba($cAlert, 0);
  }

  50% {
    background: rgba($cAlert, 0.2);
  }

  100% {
    background: rgba($cAlert, 0);
  }
}
</style>

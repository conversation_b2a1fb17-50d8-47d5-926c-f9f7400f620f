<!--
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-10 19:30:05
 * @LastEditors: zhai
 * @LastEditTime: 2025-01-19 19:33:23
-->

<script setup lang="ts">
import { computed, onMounted, ref, h } from 'vue';
import { ValueBuilderContext, dict, useFs } from '@fast-crud/fast-crud';
import type {
  AddReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  DelReq,
  EditReq,
  ScopeContext,
  UserPageQuery,
  UserPageRes,
  ValueResolveContext
} from '@fast-crud/fast-crud';
import { NTag, useDialog, useMessage } from 'naive-ui';
import dayjs from 'dayjs';
import { fast_terminals_api as api } from '@/service/api/terminals';
import { getMqttClients } from '@/service/api/mqttsvr';

const selectedRowKeys = ref([]);

function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery): Promise<UserPageRes> => {
    let term_list = api.GetList(query);

    try {
      const mqtt_list = await getMqttClients();

      // 补充信息mqtt_list -> term_list
      const result = await term_list;

      for (const mq of mqtt_list.data) {
        const term = result.data.data.find(t => t.client_id == mq.clientid);
        if (term) {
          term.ip_address = mq.ip_address;
          term.connected = mq.connected;
          term.connected_at = mq.connected_at;
        }
      }

      return Promise.resolve(result);
    } catch (e) {
      console.error('获取mqtt客户端列表失败', e);
    }

    return term_list;
  };
  const editRequest = async (ctx: EditReq) => {
    const { form, row } = ctx;
    form.id = row.id;
    return api.UpdateObj(form);
  };
  const delRequest = async (ctx: DelReq) => {
    const { row } = ctx;
    return api.DelObj(row.id);
  };
  const addRequest = async (req: AddReq) => {
    const { form } = req;
    return api.AddObj(form);
  };

  return {
    crudOptions: {
      container: {
        // is: 'fs-layout-card'
      },
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest
      },
      settings: {
        viewFormUseCellComponent: true,
        plugins: {
          // 这里使用行选择插件，生成行选择crudOptions配置，最终会与crudOptions合并
          rowSelection: {
            enabled: true,
            order: -2,
            before: true,
            // handle: (pluginProps, useCrudProps)=>CrudOptions,
            props: {
              multiple: true,
              crossPage: true,
              selectedRowKeys,
              onSelectedChanged(selected) {
                console.log('已选择变化：', selected);
              }
            }
          }
        }
      },
      table: {
        striped: true
      },
      rowHandle: {
        width: 150
      },
      search: {
        show: false
      },
      form: {
        wrapper: {
          draggable: false
        },
        col: {
          span: 24,
        },
      },
      toolbar: {
        buttons: {
          search: {},
          compact: {
            show: false
          }
        }
      },

      columns: {
        name: {
          title: '设备名',
          type: 'text',
          search: { show: true },
          column: {
            sorter: 'custom'
          },
          form: {
            rule: [{ required: true, message: '请输入姓名' }]
          }
        },
        serial_code: {
          title: '序列号',
          type: 'text',
          editForm: {
            component: {
              disabled: true, //组件是否禁用
            }
          }
        },
        description: {
          title: '描述/备注',
          type: 'text',
        },
        connected: {
          title: '连接状态',

          column: {
            cellRender(scope: ScopeContext) {
              if (scope.row.connected) {
                return h(NTag, { type: 'success', size: "small" }, '已连接');
              }
              return h(NTag, { type: 'default', size: "small" }, '未连接');
            },

          },
          form: {
            show: false
          }
        },
        client_id: {
          title: 'ID',
          type: 'text',
          form: {
            show: false
          }
        },
        status: {
          title: '启用',
          type: "dict-switch",
          dict: dict({
            data: [
              { value: true, label: "启用", color: "success" },
              { value: false, label: "禁用", color: "default" },
            ],
          }),
          form: {
            value: true,
          },
        },
        ip_address: {
          title: 'IP',
          type: 'text',
          column: {
            width: 120,
            readonly: true
          },
          form: {
            show: false,
            component: {
              // disabled: true, //组件是否禁用
              readonly: true, //组件是否是只读
            }
          }
        },
        connected_at: {
          title: '连接时间',
          type: 'date',
          form: {
            show: false
          },
        }
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

const message = useMessage();
const dialog = useDialog();

const handleBatchDelete = async () => {
  if (selectedRowKeys.value?.length > 0) {
    await dialog.info({
      title: '确认',
      content: `确定要批量删除这${selectedRowKeys.value.length}条记录吗`,
      positiveText: '确定',
      negativeText: '取消',
      async onPositiveClick() {
        await api.BatchDelete(selectedRowKeys.value);
        message.info('删除成功');
        selectedRowKeys.value = [];
        await crudExpose.doRefresh();
      }
    });
  } else {
    message.error('请先勾选记录');
  }
};

// 页面打开后获取列表数据
onMounted(() => {
  crudExpose.doRefresh();
  // eslint-disable-next-line prettier/prettier
});
</script>

<template>
  <div class="h-full">
    <FsCrud ref="crudRef" v-bind="crudBinding">
      <!-- 批量删除 -->
      <template #pagination-left>
        <FsButton icon="ion:trash-outline" @click="handleBatchDelete" />
      </template>
    </FsCrud>
  </div>
</template>

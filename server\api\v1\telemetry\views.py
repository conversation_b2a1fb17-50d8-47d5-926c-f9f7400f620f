"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-20 20:39:56
LastEditors: zhai
LastEditTime: 2025-01-20 22:43:15
"""

import json
from fastapi import APIRouter, HTTPException, Query, Depends, Request
import asyncio

# 必须从 redis.asyncio 导入
# from redis import ConnectionPool, Redis
from redis.asyncio import ConnectionPool, Redis
from api.v1.fishery.device.schema import DeviceParamsDTO
from api.v1.telemetry.feed_r1 import FeedR1_CloudCmd
from api.v1.telemetry.feed_p import FeedP_CloudCmd
from models.fishery.device import CalLogModel, DeviceModel
from models.mqtt.mqtt import MqttMessageModel
from utils.remote_utils import remote_call_gateway, remote_mqtt_gateway
from utils.tools import get_uuid_str_without_hyphen

from .schema import MqttCmdDTO, MqttMsgDTO
from core.redis.dependency import get_redis_pool
from fastapi_crud_tortoise import (
    resp_success,
    resp_fail,
    pagination_factory,
    PAGINATION,
    convert_to_pydantic,
    insert_operation,
    OperationType,
)
from settings import APP_SETTINGS

router = APIRouter()


@router.post("/msg", summary="message with reply")
async def post_msg(
    request: Request,
    data: MqttMsgDTO,
    redis_pool: ConnectionPool = Depends(get_redis_pool),
):
    topic = "rpc/call/xxx"
    async with Redis(connection_pool=redis_pool) as redis:
        ret = await remote_call_gateway(redis, topic, data.payload, data.timeout or 10)
        if ret["success"]:
            return resp_success(ret["data"])
        else:
            return resp_fail(msg=ret["msg"])


@router.post("/cmd", summary="command")
async def post_cmd(
    request: Request,
    data: MqttCmdDTO,
    redis_pool: ConnectionPool = Depends(get_redis_pool),
):
    payload_dict = data.payload.model_dump()

    feed_map = {
        "R": FeedR1_CloudCmd,
        "P": FeedP_CloudCmd,
    }

    if data.type in feed_map:
        feed_cmd = feed_map[data.type]
    else:
        feed_cmd = FeedR1_CloudCmd

    try:
        await feed_cmd.parse_mqtt_cmd(data.sn, payload_dict)
    except Exception as e:
        return resp_fail(msg=f"parse mqtt cmd error: {str(e)}")

    type = "cmd"
    if "type" in data.payload:
        type = data.payload["type"]
        if type == "feed_mode":
            if "mode" in data.payload["data"]:
                type = type + "_" + data.payload["data"]["mode"]

    tenant_id = getattr(request.state, "tenant_id", None)
    user_id = getattr(request.state, "user_id", None)
    user_name = getattr(request.state, "user_name", "unknown")

    if data.operation:
        await insert_operation(
            tenant_id=tenant_id,
            user_id=user_id,
            user=user_name,
            action=data.operation.action,
            target=data.operation.target,
            notes=data.operation.notes,
            trace_id=getattr(request.state, "x_request_id", None),
        )

    async def process_sn(sn):
        topic = f"{APP_SETTINGS.MQTT_TOPIC_HEADER}/cmd/{sn}"
        reply_id = get_uuid_str_without_hyphen()
        payload_dict["id"] = reply_id

        mqtt_msg = MqttMessageModel(
            created_by=user_id,
            updated_by=user_id,
            tenant_id=tenant_id,
            session_id=reply_id,
            sender="cloud",
            type=type,
            system=False,
            operator=user_name,
            sn=sn,
            topic=topic,
            payload=json.dumps(payload_dict, default=str, indent=1),
        )
        await mqtt_msg.save()

        # 操作记录
        # insert_operation(
        #     user=getattr(request.state, "user_name", "unknown"),
        #     action=OperationType.SEND,
        #     target="device",
        #     notes=json.dumps(data, default=str, indent=1),
        #     tenant_id=getattr(request.state, "tenant_id", None),
        #     user_id=getattr(request.state, "user_id", None),
        #     trace_id=getattr(request.state, "x_request_id", None),
        # )

        async with Redis(connection_pool=redis_pool) as redis:
            ret = await remote_mqtt_gateway(redis, topic, payload_dict, reply_id, data.timeout or 10)
            # 校准记录
            if payload_dict["type"] == "cal_set":
                device_model = await DeviceModel.get_or_none(sn=sn)
                if device_model:
                    model = payload_dict["data"]["model"]
                    feed = payload_dict["data"]["feed"]
                    weight = payload_dict["data"]["weight"]
                    cal_log = CalLogModel(
                        device_id=device_model.id,
                        model=model,
                        feed=feed,
                        weight=weight,
                    )
                    await cal_log.save()

            return {"sn": sn, "result": ret}

    # 使用 asyncio.gather 并行处理所有 sn
    results = await asyncio.gather(*(process_sn(sn) for sn in data.sn))

    # 返回所有结果
    return resp_success(results)

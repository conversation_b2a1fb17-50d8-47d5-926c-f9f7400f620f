"""
Descripttion:
version: 0.x
Author: zhai
Date: 2024-05-25 19:05:58
LastEditors: zhai
LastEditTime: 2025-01-12 11:30:25
"""

"""
Descripttion: 
version: 0.x
Author: zhai
Date: 2023-08-14 09:46:56
LastEditors: zhai
LastEditTime: 2023-12-23 15:01:20
"""
"""
Descripttion: 
version: 0.x
Author: zhai
Date: 2023-08-13 15:56:19
LastEditors: zhai
LastEditTime: 2023-08-13 16:25:16
"""

from fastapi import Depends

from fastapi_crud_tortoise import TortoiseCRUDRouter, resp_success
from api.v1.dummy.schema import DummyCreateDTO, DummyDTO
from models.dummy import DummyModel
from tortoise.contrib.pydantic import pydantic_model_creator

from api.v1.dummy.schema import (
    DepartmentCreateDTO,
    DepartmentDTO,
    EmployeeCreateDTO,
    EmployeeDTO,
    TeamCreateDTO,
    TeamDTO,
)
from models.dummy import DepartmentModel, EmployeeModel, TeamModel

# from sqlalchemy.ext.asyncio import AsyncSession


# Example query: [["age", ">=", "25"], ["name", "=", "Alice"]]


# oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")

# def token_auth(token: str = Depends(oauth2_scheme)):
#     print(token)
#     if not token:
#         raise HTTPException(401, "Invalid token")

# router = SQLAlchemyCRUDRouter(schema=DummyDTO, dependencies=[Depends(token_auth)])


# DummyDTO = pydantic_model_creator(DummyModel, name=f"{DummyModel.__name__}DTO")
# DummyCreateDTO = pydantic_model_creator(DummyModel, name=f"{DummyModel.__name__}CreateDTO", exclude_readonly=True)

dummy_router = TortoiseCRUDRouter(
    schema=DummyDTO, create_schema=DummyCreateDTO, db_model=DummyModel, prefix="dummy"
)


@dummy_router.post("/custom_router")
async def test(para1: int, para2: str):
    return resp_success(data="test custom router")


# custom routes demo

# @dummy_router.get('')
# def overloaded_get_all():
#     return 'My overloaded route that returns all the items'

# @dummy_router.get('/{item_id}')
# def overloaded_get_one():
#     return 'My overloaded route that returns one item'


employee_router = TortoiseCRUDRouter(
    schema=EmployeeDTO,
    create_schema=EmployeeCreateDTO,
    db_model=EmployeeModel,
    prefix="employee",
)

team_router = TortoiseCRUDRouter(
    schema=TeamDTO,
    create_schema=TeamCreateDTO,
    db_model=TeamModel,
    prefix="team",
)

department_router = TortoiseCRUDRouter(
    schema=DepartmentDTO,
    create_schema=DepartmentCreateDTO,
    db_model=DepartmentModel,
    prefix="department",
)

# custom routes demo

# @router.post("/custom_router")
# async def test(para1: int, para2: str, session: AsyncSession = Depends(get_db_session)):
#     return resp_success(data="test custom router")

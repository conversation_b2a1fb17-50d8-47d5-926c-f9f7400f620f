<!--
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2023-07-07 18:23:43
 * @LastEditors: zhai
 * @LastEditTime: 2025-04-26 23:05:21
-->
<script lang="ts" setup>
import { it } from 'node:test';
import { onMounted, reactive, ref, watch } from 'vue';
import BlockEditor from '@/components/blockEditor/index.vue';
import { fast_blocks_api } from '@/service/api/blocks';
import { fast_schemas_api } from '@/service/api/schemas';

import { localStg } from '@/utils/storage';


// import { SchemaApi } from '/@/api/block/schema';

const block = reactive({
  schemas: null,
  cur_schema_name: '',
  base_blocks: []
});

const on_execute = schema => {
  // new SchemaApi().execute({
  //     blocks: 'Sensor',
  //     schemas: JSON.stringify(schema),
  // });
};

const on_save = (name: string) => {
  const schema = block.schemas?.find(it => it.name == name);
  if (!schema) {
    console.error('数据同步错误');
  }
  else {
    fast_schemas_api
      .upsert(schema)
      .then(res => {
        if (res.error == null) {
          schema.id = res.data.data.id;
        }
      })
      .finally(() => { });
  }
};

const on_rename = (old_name: string, new_name: string) => {
  const schema = block.schemas?.find(it => it.name == old_name);
  if (!schema) {
    console.error('数据同步错误');
  }
  else {
    schema.name = new_name;
    fast_schemas_api
      .upsert(schema)
      .then(res => {
        if (res.error == null) {
          block.cur_schema_name = new_name;
        }
      })
      .finally(() => { });
  }
};

const on_delete = (name: string) => {
  const schema = block.schemas?.find(it => it.name == name);
  if (!schema) {
    console.error('数据同步错误');
  }
  else {
    fast_schemas_api
      .delete(schema.id)
      .then(res => {
        fast_schemas_api.list().then(res => {
          block.schemas = res.data.data;
          if (block.schemas.length) {
            block.cur_schema_name = block.schemas[0].name;
          }
          else{
            block.cur_schema_name = '';
          }
        });
      })
      .finally(() => { });
  }
};

watch(
  () => block.cur_schema_name,
  (newVal) => {
    // 保存到 localStg
    localStg.set('curSchemaName', newVal);
  }
);

onMounted(() => {
  const p1 = fast_blocks_api
    .get_blocks(0)
    .then(res => {
      return res;
    })
    .finally(() => { });

  const p2 = fast_schemas_api
    .list()
    .then(res => {
      return res;
    })
    .finally(() => { });

  Promise.all([p1, p2]).then(results => {
    block.schemas = results[1].data.data;

    const cur_schema_name = localStg.get('curSchemaName') || '';
    const schema_names = block.schemas.map(it => it.name);
    if (cur_schema_name && schema_names.includes(cur_schema_name)) {
      block.cur_schema_name = cur_schema_name;
    }

    block.base_blocks = results[0].data.data;
  });
});
</script>

<template>
  <div class="layout-main" :showPadding="false">
    <BlockEditor v-if="block.base_blocks.length" v-model:schemas="block.schemas"
      v-model:cur_schema_name="block.cur_schema_name" :base_blocks="block.base_blocks" :disable_switch="false"
      :disable_edit="false" :disable_menu="false" @execute="on_execute" @save="on_save" @rename="on_rename"
      @delete="on_delete" />
  </div>
</template>

<style>
.block-container {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  overflow: hidden;
}

.block-container2 {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  width: 100%;
  height: 100%;
}

.root {
  --minimap-bg: rgba(38, 38, 38, 0.1);
}

:root {
  --minimap-bg: rgba(225, 225, 225, 0.1);
}

.minimap {
  background-color: var(--minimap-bg);
  position: absolute;
  height: 15%;
  width: 15%;
  min-width: 150px;
  max-width: 90%;
  top: 20px;
  right: 20px;
  z-index: 900;
}
</style>

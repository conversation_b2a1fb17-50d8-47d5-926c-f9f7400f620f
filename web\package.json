{"name": "soybean-admin", "type": "module", "version": "1.3.10", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build2": "node --max-old-space-size=8192 ./node_modules/vite/bin/vite.js build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/data-set": "0.11.8", "@antv/g2": "5.2.10", "@antv/g6": "5.0.35", "@baklavajs/core": "^1.10.2", "@baklavajs/plugin-engine": "^1.10.2", "@better-scroll/core": "2.5.1", "@fast-crud/fast-crud": "^1.20.2", "@fast-crud/fast-extends": "^1.20.2", "@fast-crud/ui-interface": "^1.20.2", "@fast-crud/ui-naive": "^1.20.2", "@iconify/vue": "4.2.0", "@kanbang/plugin-options-vue3": "^1.10.5", "@kanbang/plugin-renderer-vue3": "^1.10.6", "@msgpack/msgpack": "^3.1.0", "@sa/alova": "workspace:*", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@visactor/vchart": "1.13.1", "@visactor/vchart-theme": "1.12.2", "@visactor/vtable-editors": "1.13.1", "@visactor/vtable-gantt": "1.13.1", "@visactor/vue-vtable": "1.13.1", "@vueuse/components": "12.0.0", "@vueuse/core": "12.0.0", "ace-builds": "^1.39.1", "clipboard": "2.0.11", "cron-validate": "^1.4.5", "dayjs": "1.11.13", "defu": "6.1.4", "dhtmlx-gantt": "9.0.3", "dompurify": "3.2.3", "echarts": "5.5.1", "event-source-polyfill": "^1.0.31", "fft.js": "^4.0.4", "jsbarcode": "3.11.6", "json5": "2.2.3", "mqtt-vue-hook": "^1.2.20", "naive-ui": "2.40.3", "nprogress": "0.2.0", "pinia": "2.3.0", "pinyin-pro": "3.26.0", "print-js": "1.6.0", "swiper": "11.1.15", "tailwind-merge": "2.5.5", "typeit": "8.8.7", "vditor": "3.10.8", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-i18n": "10.0.5", "vue-pdf-embed": "2.1.1", "vue-router": "4.5.0", "vue3-ace-editor": "^2.2.4", "vue3-seven-segment-display": "^1.0.3", "wangeditor": "4.7.15", "xgplayer": "3.0.20", "xlsx": "0.18.5", "yaml": "^2.8.0"}, "devDependencies": {"@amap/amap-jsapi-types": "0.0.15", "@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.283", "@rollup/plugin-yaml": "^4.1.2", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.4.4", "@types/bmapgl": "0.0.7", "@types/dompurify": "3.2.0", "@types/node": "22.10.2", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "0.65.1", "@unocss/preset-icons": "0.65.1", "@unocss/preset-uno": "0.65.1", "@unocss/transformer-directives": "0.65.1", "@unocss/transformer-variant-group": "0.65.1", "@unocss/vite": "0.65.1", "@vitejs/plugin-vue": "5.2.1", "@vitejs/plugin-vue-jsx": "4.1.1", "eslint": "9.17.0", "eslint-plugin-vue": "9.32.0", "lint-staged": "15.2.11", "sass": "1.83.0", "simple-git-hooks": "2.11.1", "tsx": "4.19.2", "typescript": "5.7.2", "unplugin-icons": "0.21.0", "unplugin-vue-components": "0.28.0", "vite": "6.0.3", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.6.8", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.1.10"}, "simple-git-hooks": {}, "lint-staged": {"*": "eslint --fix"}, "website": "https://admin.soybeanjs.cn"}
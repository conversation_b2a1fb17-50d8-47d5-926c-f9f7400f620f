'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2024-07-07 22:05:23
LastEditors: zhai
LastEditTime: 2024-07-07 22:10:15
'''


from datetime import datetime
from typing_extensions import Annotated
from pydantic import BeforeValidator, SerializerFunctionWrapHandler, WrapSerializer


def ser_wrap_datetime(v: datetime, nxt: SerializerFunctionWrapHandler) -> int:
    return int(v.timestamp() * 1000)


def datetime_stamp_js2py(v: any) -> any:
    if isinstance(v, (int, float)):
        return v / 1000
    return v


datetime_stamp = Annotated[
    datetime,
    BeforeValidator(datetime_stamp_js2py),
    WrapSerializer(ser_wrap_datetime, when_used="json"),
]




# 返回的时候可以转成时间戳，输入的时候会引起错误
# class TimestampBaseModel(BaseModel):
#     @field_validator('*')
#     def convert_datetimes_to_timestamps(cls, value: Any, field: Field) -> Any:
#         if isinstance(value, datetime):
#             return int(value.timestamp())
#         return value


# class datetime_stamp(datetime):

# from pydantic.fields import FieldInfo
# class datetime_stamp(datetime):
#     @classmethod
#     def __get_validators__(cls):
#         yield cls.validate

#     @classmethod
#     def validate(cls, v: Any, field: FieldInfo) -> datetime:
#         if isinstance(v, (int, float)):
#             return datetime.fromtimestamp(v / 1000)
#         if isinstance(v, str):
#             return datetime.fromisoformat(v)
#         if isinstance(v, datetime):
#             return v
#         raise ValueError("Invalid value for datetime")

#     def __repr__(self) -> str:
#         return f"Timestamp({super().__repr__()})"

#     def __str__(self) -> str:
#         return str(int(self.timestamp() * 1000))

#     def __json__(self):
#         return int(self.timestamp() * 1000)

# 返回的时候可以转成时间戳，输入的时候会引起错误
# class TimestampBaseModel(BaseModel):
#     @field_validator('*')
#     def convert_datetimes_to_timestamps(cls, value: Any, field: Field) -> Any:
#         if isinstance(value, datetime):
#             return int(value.timestamp())
#         return value


# class datetime_stamp(datetime):

# from pydantic.fields import FieldInfo
# class datetime_stamp(datetime):
#     @classmethod
#     def __get_validators__(cls):
#         yield cls.validate

#     @classmethod
#     def validate(cls, v: Any, field: FieldInfo) -> datetime:
#         if isinstance(v, (int, float)):
#             return datetime.fromtimestamp(v / 1000)
#         if isinstance(v, str):
#             return datetime.fromisoformat(v)
#         if isinstance(v, datetime):
#             return v
#         raise ValueError("Invalid value for datetime")

#     def __repr__(self) -> str:
#         return f"Timestamp({super().__repr__()})"

#     def __str__(self) -> str:
#         return str(int(self.timestamp() * 1000))

#     def __json__(self):
#         return int(self.timestamp() * 1000)

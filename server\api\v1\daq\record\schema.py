'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-04-23 22:23:09
LastEditors: zhai
LastEditTime: 2025-04-28 21:27:07
'''
from enum import Enum
from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import Any, Dict, List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE

class RecordCreateDTO(BaseModel):
    name: str
    serial_code: Optional[str] = None
    device_type: Optional[str] = None
    description: Optional[str] = None
    record_time: datetime
    record_duration: float
    config: Optional[dict] = None
    sync_status: Optional[bool] = False


class RecordDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    serial_code: Optional[str] = None
    device_type: Optional[str] = None
    description: Optional[str] = None
    record_time: Optional[datetime] = None
    record_duration: Optional[float] = None
    config: Optional[dict] = None
    sync_status: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


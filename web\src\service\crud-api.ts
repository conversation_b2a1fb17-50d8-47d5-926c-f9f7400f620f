import { List } from 'echarts/core';
import { request } from '@/service/request';


// 分页元数据
export interface PaginationMeta {
  current: number | null;
  size: number | null;
  total: number;
}

// API响应数据结构
export interface ApiResponse<T> {
  code: string;
  msg: string;
  data: {
    data: T[];
    msg: string;
    meta: PaginationMeta;
  };
  success: boolean;
}



export class Singleton<T> {
  private static __instances: Map<string, any> = new Map();

  static instance<T>(this: new (...args: any[]) => T, ...args: any[]): T {
    const className = this.name;
    if (!Singleton.__instances.has(className)) {
      Singleton.__instances.set(className, new this(...args));
    }

    return Singleton.__instances.get(className);
  }
}

export function buildQueryParams(params: {
  sort_by?: string | null;
  relationships?: boolean | null;
  skip?: number | null;
  limit?: number | null;
  tenant_data_filter?: boolean | null;
  user_data_filter?: boolean | null;
}): string {
  const urlParams = new URLSearchParams();

  if (params.sort_by !== null && params.sort_by !== undefined) {
    urlParams.append('sort_by', params.sort_by);
  }
  if (params.relationships !== null && params.relationships !== undefined) {
    urlParams.append('relationships', params.relationships.toString());
  }
  if (params.skip !== null && params.skip !== undefined) {
    urlParams.append('skip', params.skip.toString());
  }
  if (params.limit !== null && params.limit !== undefined) {
    urlParams.append('limit', params.limit.toString());
  }
  if (params.tenant_data_filter !== null && params.tenant_data_filter !== undefined) {
    urlParams.append('tenant_data_filter', params.tenant_data_filter ? 'TENANT_DATA' : 'ALL_DATA');
  }
  if (params.user_data_filter !== null && params.user_data_filter !== undefined) {
    urlParams.append('user_data_filter', params.user_data_filter ? 'SELF_DATA' : 'ALL_DATA');
  }

  const queryString = urlParams.toString();
  return queryString.length > 0 ? `?${queryString}` : '';
}

export class CrudApi<CLS, T = object> extends Singleton<CLS> {
  prefix: string;

  protected constructor(prefix: string) {
    super();
    this.prefix = prefix;
  }

  resHandle(res: any) {
    return res.data;
  }

  // Create One
  async create(data?: T) {
    return request<any, 'json'>({
      url: `/${this.prefix}/create`,
      method: 'post',
      data
    });
  }

  // Batch Create
  async batch_create(data: T[]) {
    return request<any, 'json'>({
      url: `/${this.prefix}/batch_create`,
      method: 'post',
      data
    });
  }

  // Delete By Key
  async delete(key?: number | string) {
    const params = new URLSearchParams();
    params.append('item_id', `${key}`);
    let str_params = params.toString();
    if (str_params.length > 0) {
      str_params = `?${str_params}`;
    }

    return request<any, 'json'>({
      url: `/${this.prefix}/delete${str_params}`,
      method: 'post'
    });
  }

  async batch_delete(keys: Array<number | string>) {
    return request<any, 'json'>({
      url: `/${this.prefix}/batch_delete`,
      method: 'post',
      data: keys
    });
  }

  // Delete All
  async delete_all(data?: T) {
    return request<any, 'json'>({
      url: `/${this.prefix}/delete_all`,
      method: 'post'
    });
  }

  // Update One By Key
  async update(data?: T) {
    return request<any, 'json'>({
      url: `/${this.prefix}/update`,
      method: 'post',
      data
    });
  }

  // Get One By Filter Value
  async get_by_id(data?: number | string) {
    return request<any, 'json'>({
      url: `/${this.prefix}/get_by_id?item_id=${data}`,
      method: 'post'
    });
  }

  // Get One By Filter Value
  async get_one_by_filter(
    data?: T,
    relationships: boolean | null = null,
    tenant_data_filter: boolean | null = null,
    user_data_filter: boolean | null = null
  ) {
    const query_params = buildQueryParams({
      relationships,
      tenant_data_filter,
      user_data_filter
    });

    return request<any, 'json'>({
      url: `/${this.prefix}/get_one_by_filter${query_params}`,
      method: 'post',
      data
    });
  }

  // List All
  async list(
    sort_by: string | null = null,
    relationships: boolean | null = null,
    skip: number | null = null,
    limit: number | null = null,
    tenant_data_filter: boolean | null = null,
    user_data_filter: boolean | null = null
  ) {
    // 'http://localhost:8000/api/form_schema/list?sort_by=id&relationships=false&skip=0&limit=0' \

    // const params = new URLSearchParams(
    //   foo: 'bar',
    //   baz: 'boom',
    //   cow: 'milk',
    //   php: 'hypertext processor'
    // );

    const query_params = buildQueryParams({
      sort_by,
      relationships,
      skip,
      limit,
      tenant_data_filter,
      user_data_filter
    });

    return request<any, 'json'>({
      url: `/${this.prefix}/list${query_params}`,
      method: 'post'
    });
  }

  // Query Many By Filter Value
  async query(
    data?: T,
    sort_by: string | null = null,
    relationships: boolean | null = null,
    skip: number | null = null,
    limit: number | null = null,
    tenant_data_filter: boolean | null = null,
    user_data_filter: boolean | null = null
  ) {
    const query_params = buildQueryParams({
      sort_by,
      relationships,
      skip,
      limit,
      tenant_data_filter,
      user_data_filter
    });

    return request<any, 'json'>({
      url: `/${this.prefix}/query${query_params}`,
      method: 'post',
      data
    });
  }

  // Query Many By Filter Condition, [=, !=, >, <, >=, <=, like, in]
  async query_ex(
    data?: T,
    sort_by: string | null = null,
    relationships: boolean | null = null,
    skip: number | null = null,
    limit: number | null = null,
    tenant_data_filter: boolean | null = null,
    user_data_filter: boolean | null = null
  ) {
    const query_params = buildQueryParams({
      sort_by,
      relationships,
      skip,
      limit,
      tenant_data_filter,
      user_data_filter
    });

    return request<any, 'json'>({
      url: `/${this.prefix}/query_ex${query_params}`,
      method: 'post',
      data
    });
  }

  // Insert Or Update
  async upsert(data?: T) {
    return request<any, 'json'>({
      url: `/${this.prefix}/upsert`,
      method: 'post',
      data
    });
  }
}

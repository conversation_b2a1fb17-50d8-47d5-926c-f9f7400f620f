<script setup lang="ts">
import { useFullscreen } from '@vueuse/core';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { GLOBAL_HEADER_MENU_ID } from '@/constants/app';
import GlobalLogo from '../global-logo/index.vue';
import GlobalBreadcrumb from '../global-breadcrumb/index.vue';
import GlobalSearch from '../global-search/index.vue';
import ThemeButton from './components/theme-button.vue';
import UserAvatar from './components/user-avatar.vue';

defineOptions({
  name: 'GlobalHeader'
});

interface Props {
  /** Whether to show the logo */
  showLogo?: App.Global.HeaderProps['showLogo'];
  /** Whether to show the menu toggler */
  showMenuToggler?: App.Global.HeaderProps['showMenuToggler'];
  /** Whether to show the menu */
  showMenu?: App.Global.HeaderProps['showMenu'];
}

defineProps<Props>();

const appStore = useAppStore();
const themeStore = useThemeStore();
const { isFullscreen, toggle } = useFullscreen();
</script>

<template>
  <div class="relative w-full h-[60px] bg-black text-white shadow-header">
    <!-- ✅ 居中标题 -->
    <div class="absolute top-0 left-1/2 transform -translate-x-1/2 h-[34px] flex items-center justify-center px-2 text-center pointer-events-none
         bg-gradient-to-r from-blue-800 via-purple-800 to-pink-900 text-white
         rounded-bl-2xl rounded-br-2xl" style="width: fit-content; max-width: 100%;">
      <div class="leading-snug text-xl break-words">
        台山2号汽轮机组扭振在线监测与分析系统
      </div>
    </div>

    <!-- ✅ 第二行 Header 内容，底部对齐 -->
    <div class="absolute bottom-0 left-0 w-full h-[40px] flex items-center justify-between px-4 z-10">
      <!-- 左侧 -->
      <div class="flex items-center gap-2">
        <MenuToggler v-if="showMenuToggler" :collapsed="appStore.siderCollapse" @click="appStore.toggleSiderCollapse" />
        <div v-if="showMenu" :id="GLOBAL_HEADER_MENU_ID" />
        <GlobalBreadcrumb v-else />
      </div>
      <!-- 右侧 -->
      <div class="flex items-center gap-2">
        <GlobalSearch />
        <FullScreen v-if="!appStore.isMobile" :full="isFullscreen" @click="toggle" />
        <UserAvatar />
      </div>
    </div>
  </div>
</template>



<style scoped></style>

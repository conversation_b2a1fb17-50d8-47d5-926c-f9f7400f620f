"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-05 11:18:02
LastEditors: zhai
LastEditTime: 2025-01-05 11:29:13
"""

from pydantic import (
    BaseModel,
    BeforeValidator,
    SerializerFunctionWrapHandler,
    ConfigDict,
    <PERSON>,
    <PERSON><PERSON>,
    field_validator,
)
from datetime import datetime, date
from typing import Any, Dict, Optional, Type, Union
from tortoise.contrib.pydantic import pydantic_model_creator
from models.task import SchedulerTaskRecord
from task.task import JobExecStrategy
from utils.pydantic_utils import datetime_stamp


class TaskDTO(BaseModel):
    name: str
    group: Optional[str] = None
    job_class: str
    exec_strategy: Union[JobExecStrategy, str]
    expression: str
    is_active: Optional[bool] = True
    remark: Optional[str] = None
    start_date: Optional[datetime_stamp] = None
    end_date: Optional[datetime_stamp] = None
    model_config = ConfigDict(from_attributes=True)


class TaskSimpleOutDTO(TaskDTO):
    id: int
    create_datetime: Optional[datetime_stamp] = None
    update_datetime: Optional[datetime_stamp] = None
    last_run_datetime: Optional[datetime_stamp] = None
    running: Optional[bool] = None
    model_config = ConfigDict(from_attributes=True)


SchedulerTaskRecordDTO: Type[BaseModel] = pydantic_model_creator(
    SchedulerTaskRecord,
    optional=(
        "id",
        "job_id",
        "job_class",
        "name",
        "group",
        "exec_strategy",
        "expression",
        "start_time",
        "end_time",
        "process_time",
        "retval",
        "exception",
        "traceback",
    ),
    name=f"{SchedulerTaskRecord.__name__}DTO",
)

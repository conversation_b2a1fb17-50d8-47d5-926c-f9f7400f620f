<script lang="ts" setup>
import { arrayBuffer } from 'node:stream/consumers';
import { Editor } from '@baklavajs/core';
import { ViewPlugin } from '@kanbang/plugin-renderer-vue3';
import { OptionPlugin } from '@kanbang/plugin-options-vue3';
import { Engine } from '@baklavajs/plugin-engine';
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
import type { Nullable } from '@/utils/snippet';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { BlockBuilder } from './BlockBuilder';

const { formRef, validate, restoreValidation } = useNaiveForm();

const emit = defineEmits(['execute', 'save', 'rename', 'delete']);

const schemas = defineModel('schemas', {
  type: Array,
  default: () => {
    return [];
  }
});

const cur_schema_name = defineModel('cur_schema_name', {
  type: String,
  default: ''
});


// 定义父组件传过来的值
const props = defineProps({
  base_blocks: {
    type: Array,
    default: () => {
      return [];
    }
  },
  disable_switch: {
    type: Boolean,
    default: false
  },
  disable_edit: {
    type: Boolean,
    default: false
  },
  disable_menu: {
    type: Boolean,
    default: false
  }
});


const editor = new Editor();

const viewPlugin = reactive(new ViewPlugin());
const engine = new Engine(true);

const BlockNameID = {};

const editor_schema = computed(() => {
  if (!schemas.value) return null;

  const item_schema = schemas.value.find(it => it.name == cur_schema_name.value);

  if (item_schema?.schemas) {
    // deep clone 切断联系
    const schema2 = JSON.parse(JSON.stringify(item_schema.schemas));
    return schema2;
  }

  return null;
});

watch(
  () => editor_schema,
  () => {
    if (editor) {
      if (editor_schema.value) {
        editor.load(editor_schema.value);
      }
      else {
        editor.load(
          {
            nodes: [],
            connections: [],
            panning: { x: 0, y: 0 },
            scaling: 1
          }
        );
      }
    }
  },
  { deep: true, immediate: true }
);

const refInput = ref();

const state = reactive({
  isShowDialog: false,
  rename: false,
  saveForm: {
    name: '',
    description: '',
    rules: {
      name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
    }
  }
});

const __Save = (name: string, description: Nullable<string> = null) => {
  const schema = editor.save();
  // deep clone 切断联系
  const schema2 = JSON.parse(JSON.stringify(schema));

  const item_schema = schemas.value.find(it => it.name == name);
  if (item_schema) {
    item_schema.schemas = schema2;
    if (description != null) {
      item_schema.description = description;
    }
  } else {
    schemas.value.push({ name, description, schemas: schema2 });
  }

  emit('save', name);
};

const onSave = () => {
  if (cur_schema_name.value) {
    __Save(cur_schema_name.value);
  } else {
    onSaveAs();
  }
};

const onSaveAs = () => {
  if (cur_schema_name.value) {
    state.saveForm.name = cur_schema_name.value;
    const item_schema = schemas.value.find(it => it.name == cur_schema_name.value);
    if (item_schema) {
      state.saveForm.description = item_schema.description;
    } else {
      state.saveForm.description = '';
    }
  }

  state.isShowDialog = true;
  state.rename = false;
  nextTick(() => {
    restoreValidation();
    nextTick(() => {
      refInput.value.focus();
    });
  });
};

const onRename = () => {
  if (cur_schema_name.value) {
    state.saveForm.name = cur_schema_name.value;
    const item_schema = schemas.value.find(it => it.name == cur_schema_name.value);
    if (item_schema) {
      state.saveForm.description = item_schema.description;
    } else {
      state.saveForm.description = '';
    }
  }

  state.isShowDialog = true;
  state.rename = true;
  nextTick(() => {
    restoreValidation();
    nextTick(() => {
      refInput.value.focus();
    });
  });
};

const onDelete = () => {
  const tmp_schemas = schemas.value;
  const index = tmp_schemas.findIndex(it => it.name == cur_schema_name.value);
  if (index != -1) {
    emit('delete', cur_schema_name.value);
  }
};

const onDlgSave = async () => {
  await validate();

  if (state.rename) {
    const index = schemas.value.findIndex(it => it.name == cur_schema_name.value);
    if (index != -1) {
      emit('rename', cur_schema_name.value, state.saveForm.name);
    }
  }
  else {
    __Save(state.saveForm.name, state.saveForm.description);
    cur_schema_name.value = state.saveForm.name;
    state.isShowDialog = false
  }

  nextTick(() => {
    restoreValidation();
  });
};



const init = () => {
  // Register the plugins
  // The view plugin is used for rendering the nodes
  editor.use(viewPlugin);
  // The option plugin provides some default option UI elements
  editor.use(new OptionPlugin());
  // The engine plugin computes the nodes in the graph in the
  // correct order using the "compute" methods of the nodes
  editor.use(engine);

  // Show a minimap in the top right corner
  viewPlugin.enableMinimap = true;
  viewPlugin.disable_context_menu = props.disable_menu;

  // Read the infos on the node passed in from Streamlit
  // and register them.
  props.base_blocks.forEach(el => {
    const Block = BlockBuilder({
      BlockName: el.name,
      Inputs: el.inputs,
      Outputs: el.outputs,
      Options: el.options
    });

    // register the nodes we have defined, so they can be
    // added by the user as well as saved & loaded. Add a
    // category to it if it exists.

    if (Object.hasOwn(el, 'category')) {
      editor.registerNodeType(el.name, Block, el.category);
    } else {
      editor.registerNodeType(el.name, Block);
    }

    // editor.registerNodeType(el.name, Block);
    BlockNameID[el.name] = 1;
  });

  // Load the editor data if load_editor_schema not equal to null.
  if (editor_schema.value) {
    editor.load(editor_schema.value);
  }

  // Change name of the added node to get a unique name.
  editor.events.addNode.addListener(this, data => {
    editor._nodes.forEach(node => {
      if (node.id === data.id) {
        node.name = `${node.name}-${BlockNameID[data.name]++}`;
      }
    });
  });
};

init();

onMounted(() => {

});

const onExecute = () => {
  emit('execute', editor.save());
};

</script>

<template>
  <div id="editor-container">
    <!-- 保存对话框 -->
    <NModal v-model:show="state.isShowDialog" preset="dialog" title="保存" positive-text="保存" negative-text="取消"
      @positive-click="onDlgSave" @negative-click="state.isShowDialog = false">
      <NForm ref="formRef" :model="state.saveForm" :rules="state.saveForm.rules" size="small" label-placement="left"
        label-width="auto">
        <NFormItem label="名称" path="name">
          <NInput ref="refInput" v-model:value="state.saveForm.name" placeholder="请输入名称" clearable />
        </NFormItem>
        <NFormItem label="描述" path="description">
          <NInput v-model:value="state.saveForm.description" placeholder="请输入描述" clearable />
        </NFormItem>
      </NForm>
    </NModal>

    <!-- Block Link Editor -->
    <baklava-editor :plugin="viewPlugin" />

    <!-- 按钮菜单 -->
    <div v-if="!props.disable_switch" class="button-menu flex">
      <NSelect v-model:value="cur_schema_name" label-field="name" value-field="name" size="small" class="mr-3 w-50"
        :options="schemas" />

      <div v-if="!props.disable_edit">
        <NButton size="small" type="primary" class="mr-2" @click="onSave">
          <template #icon>
            <icon-fad:save />
          </template>
          保存
        </NButton>
        <NButton size="small" type="primary" class="mr-2" @click="onSaveAs">
          <template #icon>
            <icon-fad:saveas />
          </template>
          另存
        </NButton>
        <NButton size="small" type="primary" class="mr-2" @click="onRename">
          <template #icon>
            <icon-mdi:rename-box-outline />
          </template>
          重命名
        </NButton>

        <n-popconfirm @positive-click="onDelete">
          <template #trigger>
            <n-button size="small" type="warning">
              <template #icon>
                <icon-material-symbols:delete-outline />
              </template>
              删除
            </n-button>
          </template>
          确定要删除吗？
        </n-popconfirm>
      </div>

      <!-- <n-button size="small" type="primary" @click="onExecute">Execute</n-button> -->
    </div>
  </div>
</template>

<style>
#editor-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.button-menu {
  position: absolute;
  top: 1rem;
  left: 1rem;
}
</style>

from typing import List, cast
from fastapi import APIRouter, Depends, Query, Request
from tortoise.expressions import Q

from api.v1.utils import insert_log
from models.fishery.device import DeviceModel
from models.fishery.organization import OrganizationModel
from models.mqtt.mqtt import MqttMessageModel
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    OperationsModel,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
    PAGINATION,
    pagination_factory,
    apply_user_tenant_filters,
    TenantDataFilter,
    UserDataFilter,
)
from .schema import MqttMessageDTO, MqttMessageFilterDTO, MqttMessageCreateDTO, MqttMessageDetailDTO
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=MqttMessageDTO,
    create_schema=MqttMessageCreateDTO,
    db_model=MqttMessageModel,
    prefix="mqttmsg",
    tags=["MQTT消息记录"],
    kcreate_route=True,
    kbatch_create_route=False,
    kdelete_route=False,
    kbatch_delete_route=False,
    kdelete_all_route=False,
    kupdate_route=False,
    kbatch_update_route=False,
    kget_by_id_route=False,
    kget_one_by_filter_route=False,
    klist_route=False,
    kquery_route=False,
    kquery_ex_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.SELF_DEFAULT,
    tenant_data_option=TenantDataOption.TENANT_DEFAULT,
)


@router.post(
    "/query_cloud_msg",
    response_model=RespModelT[List[MqttMessageDetailDTO]],
    summary="查询云端发送的MQTT消息",
)
async def query_mqtt_cloud_message_detail(
    filter: MqttMessageFilterDTO,
    request: Request,
    pagination: PAGINATION = pagination_factory(),
    sort_by: str = Query(None, description="Sort records by this field"),
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """根据过滤条件查询MQTT消息"""
    try:
        skip, limit = pagination.get("skip"), pagination.get("limit")

        # 初始化查询
        query = MqttMessageModel.filter(sender="cloud")

        query = router._autoload_options(query)

        # 应用用户和租户过滤条件
        query = apply_user_tenant_filters(query, request, user_data_filter, tenant_data_filter)

        # 动态添加过滤条件
        if filter.tenant_id:
            query = query.filter(tenant_id=filter.tenant_id)
        if filter.sn:
            query = query.filter(sn=filter.sn)
        if filter.topic:
            query = query.filter(topic__icontains=filter.topic)
        if filter.type:
            query = query.filter(type=filter.type)
        if filter.system is not None:
            query = query.filter(system=filter.system)
        if filter.operator:
            query = query.filter(operator__icontains=filter.operator)
        if filter.status:
            query = query.filter(status=filter.status)
        if filter.start_time:
            query = query.filter(created_at__gte=filter.start_time)
        if filter.end_time:
            query = query.filter(created_at__lte=filter.end_time)

        total = await query.count()

        if sort_by:
            query = query.order_by(sort_by)

        if skip:
            query = query.offset(cast(int, skip))

        if limit:
            query = query.limit(limit)

        current = None
        size = None
        if skip and limit:
            size = limit
            current = skip // limit + 1

        # 执行查询
        messages = await query

        # 转换为DTO
        message_dtos = []
        map_sn_device = {}
        for message in messages:
            dto = convert_to_pydantic(message, MqttMessageDetailDTO, relationships=True)
            device = None
            if message.sn:
                if message.sn in map_sn_device:
                    device = map_sn_device[message.sn]
                else:
                    device = (
                        await DeviceModel.filter(sn=message.sn)
                        .prefetch_related("tenant", "tags", "organization", "organization__parent")
                        .first()
                    )
                    map_sn_device[message.sn] = device

                if device:
                    dto["device_name"] = device.name
                    dto["device_type"] = device.type
                    dto["device_model"] = device.model
                    dto["device_tags"] = [tag.name for tag in device.tags]

                    if device.organization:
                        dto["workshop"] = device.organization.name

                        if device.organization.parent:
                            dto["fishery"] = device.organization.parent.name

                    if device.tenant:
                        dto["customer"] = device.tenant.name

            message_dtos.append(dto)

        return resp_success(
            message_dtos,
            total,
            current,
            size,
        )

    except Exception as e:
        return resp_fail(msg=f"查询MQTT消息失败: {str(e)}")


@router.post(
    "/query_device_msg",
    response_model=RespModelT[List[MqttMessageDetailDTO]],
    summary="查询设备发送的MQTT消息",
)
async def query_mqtt_device_message_detail(
    filter: MqttMessageFilterDTO,
    request: Request,
    pagination: PAGINATION = pagination_factory(),
    sort_by: str = Query(None, description="Sort records by this field"),
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """根据过滤条件查询MQTT消息"""
    try:
        skip, limit = pagination.get("skip"), pagination.get("limit")

        # 初始化查询
        query = MqttMessageModel.filter(sender="device")

        query = router._autoload_options(query)

        # 应用用户和租户过滤条件
        query = apply_user_tenant_filters(query, request, None, tenant_data_filter)

        if user_data_filter == UserDataFilter.SELF_DATA and not filter.sn:
            cur_user_id = getattr(request.state, "user_id", None)
            device_sns = await DeviceModel.filter(binder_id=cur_user_id).values_list("sn", flat=True)
            query = query.filter(sn__in=device_sns)

        # 动态添加过滤条件
        if filter.tenant_id:
            query = query.filter(tenant_id=filter.tenant_id)
        if filter.sn:
            query = query.filter(sn=filter.sn)
        if filter.topic:
            query = query.filter(topic__icontains=filter.topic)
        if filter.type:
            query = query.filter(type=filter.type)
        if filter.system is not None:
            query = query.filter(system=filter.system)
        if filter.operator:
            query = query.filter(operator__icontains=filter.operator)
        if filter.status:
            query = query.filter(status=filter.status)
        if filter.start_time:
            query = query.filter(created_at__gte=filter.start_time)
        if filter.end_time:
            query = query.filter(created_at__lte=filter.end_time)

        total = await query.count()

        if sort_by:
            query = query.order_by(sort_by)

        if skip:
            query = query.offset(cast(int, skip))

        if limit:
            query = query.limit(limit)

        current = None
        size = None
        if skip and limit:
            size = limit
            current = skip // limit + 1

        # 执行查询
        messages = await query

        # 转换为DTO
        message_dtos = []
        map_sn_device = {}
        for message in messages:
            dto = convert_to_pydantic(message, MqttMessageDetailDTO, relationships=True)
            device = None
            if message.sn:
                if message.sn in map_sn_device:
                    device = map_sn_device[message.sn]
                else:
                    device = (
                        await DeviceModel.filter(sn=message.sn)
                        .prefetch_related("tenant", "tags", "organization", "organization__parent")
                        .first()
                    )
                    map_sn_device[message.sn] = device

                if device:
                    dto["device_name"] = device.name
                    dto["device_type"] = device.type
                    dto["device_model"] = device.model
                    dto["device_tags"] = [tag.name for tag in device.tags]

                    if device.organization:
                        dto["workshop"] = device.organization.name

                        if device.organization.parent:
                            dto["fishery"] = device.organization.parent.name

                    if device.tenant:
                        dto["customer"] = device.tenant.name

            message_dtos.append(dto)

        return resp_success(
            message_dtos,
            total,
            current,
            size,
        )

    except Exception as e:
        return resp_fail(msg=f"查询MQTT消息失败: {str(e)}")


# @router.post(
#     "/query_detail", response_model=RespModelT[List[MqttMessageDetailDTO]], summary="根据SN查询MQTT消息"
# )
# async def query_mqtt_message_detail(
#     filter: MqttMessageFilterDTO,
#     request: Request,
#     pagination: PAGINATION = pagination_factory(),
#     sort_by: str = Query(None, description="Sort records by this field"),
#     # tenant_data_filter: TenantDataFilter = TenantDataFilter.TENANT_DATA,
#     tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
# ):
#     """根据过滤条件查询MQTT消息"""
#     try:
#         skip, limit = pagination.get("skip"), pagination.get("limit")

#         # 初始化查询
#         query = MqttMessageModel.filter()

#         query = router._autoload_options(query)

#         # 应用用户和租户过滤条件
#         query = apply_user_tenant_filters(query, request, None, tenant_data_filter)

#         # 动态添加过滤条件
#         if filter.tenant_id:
#             query = query.filter(tenant_id=filter.tenant_id)
#         if filter.sender:
#             query = query.filter(sender=filter.sender)
#         if filter.topic:
#             query = query.filter(topic__icontains=filter.topic)
#         if filter.type:
#             query = query.filter(type=filter.type)
#         if filter.system is not None:
#             query = query.filter(system=filter.system)
#         if filter.operator:
#             query = query.filter(operator__icontains=filter.operator)
#         if filter.status:
#             query = query.filter(status=filter.status)
#         if filter.start_time:
#             query = query.filter(created_at__gte=filter.start_time)
#         if filter.end_time:
#             query = query.filter(created_at__lte=filter.end_time)

#         total = await query.count()

#         if sort_by:
#             query = query.order_by(sort_by)

#         if skip:
#             query = query.offset(cast(int, skip))

#         if limit:
#             query = query.limit(limit)

#         current = None
#         size = None
#         if skip and limit:
#             size = limit
#             current = skip // limit + 1

#         # 执行查询
#         messages = await query

#         # 转换为DTO
#         message_dtos = []
#         for message in messages:
#             dto = convert_to_pydantic(message, MqttMessageDetailDTO, relationships=True)
#             topic = message.topic
#             if topic:
#                 sn = topic.split("/")[-1]
#             else:
#                 sn = None

#             device = (
#                 await DeviceModel.filter(sn=sn)
#                 .prefetch_related("tenant", "tags", "organization", "organization__parent")
#                 .first()
#             )
#             if device:
#                 dto["device_name"] = device.name
#                 dto["device_type"] = device.type
#                 dto["device_model"] = device.model
#                 dto["device_tags"] = [tag.name for tag in device.tags]

#                 if device.organization:
#                     dto["workshop"] = device.organization.name

#                     if device.organization.parent:
#                         dto["fishery"] = device.organization.parent.name

#                 if device.tenant:
#                     dto["customer"] = device.tenant.name

#                 message_dtos.append(dto)

#         return resp_success(
#             message_dtos,
#             total,
#             current,
#             size,
#         )

#     except Exception as e:
#         return resp_fail(msg=f"查询MQTT消息失败: {str(e)}")

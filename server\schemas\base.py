"""
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-01-04 15:11:53
LastEditors: zhai
LastEditTime: 2025-01-18 10:40:49
"""

from typing import Any
import uuid

import asyncpg
from fastapi.responses import JSONResponse, ORJSONResponse
from pydantic import BaseModel, Field


# JSONResponse 不能处理UUID类型的数据, 会报错, 所以使用ORJSONResponse
# class Custom(JSONResponse):


class Custom(ORJSONResponse):

    def __init__(
        self,
        code: str | int = "0000",
        status_code: int = 200,
        msg: str = "OK",
        data: Any = None,
        **kwargs,
    ):
        content = {"code": str(code), "msg": msg, "data": data}
        content.update(kwargs)
        super().__init__(content=content, status_code=status_code)

    def render(self, content: Any) -> bytes:
        # 递归转换 UUID 为字符串
        def convert(obj):
            if isinstance(obj, (uuid.UUID, asyncpg.pgproto.pgproto.UUID)):
                return str(obj)
            if isinstance(obj, dict):
                return {k: convert(v) for k, v in obj.items()}
            if isinstance(obj, (list, tuple)):
                return [convert(item) for item in obj]
            return obj

        converted_content = convert(content)
        return super().render(converted_content)


class Success(Custom):
    pass


class Fail(Custom):
    def __init__(
        self,
        code: str | int = "4000",
        msg: str = "OK",
        data: Any = None,
        **kwargs,
    ):
        super().__init__(code=code, msg=msg, data=data, status_code=200, **kwargs)


class SuccessExtra(Custom):
    def __init__(
        self,
        code: str | int = "0000",
        msg: str = "OK",
        data: Any = None,
        total: int = 0,
        current: int | None = 1,
        size: int | None = 20,
        **kwargs,
    ):
        if isinstance(data, dict):
            data.update({"total": total, "current": current, "size": size})
        super().__init__(code=code, msg=msg, data=data, status_code=200, **kwargs)


class CommonIds(BaseModel):
    ids: list[int] = Field(title="通用ids")

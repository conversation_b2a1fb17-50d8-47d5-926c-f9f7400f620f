<script lang="ts" setup name="BlockCode">
import { computed, h, onMounted, reactive, ref, watch } from 'vue';
import { NButton, NCard, NGi, NGrid, NTag, NText, useMessage } from 'naive-ui';
import { ValueBuilderContext, useFs } from '@fast-crud/fast-crud';
import type {
  AddReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  DelReq,
  EditReq,
  UserPageQuery,
  UserPageRes,
} from '@fast-crud/fast-crud';
import { VAceEditor } from 'vue3-ace-editor';

import '@/utils/ace-config';

import { fast_blocks_api as api } from '@/service/api/blocks';
const message = useMessage();

const selectedRowKeys = ref([]);
const selectedRow = computed(() => {
  if (selectedRowKeys.value?.length > 0) {
    const firstMatch = crudBinding.value.data?.find(item => item.id === selectedRowKeys.value[0]);
    if (firstMatch) {
      return firstMatch;
    }
  }
  return null;
})

function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery): Promise<UserPageRes> => {
    return api.GetList(query);
  };
  const editRequest = async (ctx: EditReq) => {
    const { form, row } = ctx;
    form.id = row.id;
    return api.UpdateObj(form);
  };
  const delRequest = async (ctx: DelReq) => {
    const { row } = ctx;
    return api.DelObj(row.id);
  };

  const addRequest = async (req: AddReq) => {
    const { form } = req;
    return api.AddObj(form);
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest
      },
      settings: {
        viewFormUseCellComponent: true,

        plugins: {
          // 这里使用行选择插件，生成行选择crudOptions配置，最终会与crudOptions合并
          rowSelection: {
            enabled: true,
            order: -2,
            before: true,
            props: {
              multiple: false,
              crossPage: false,
              selectedRowKeys
            }
          }
        }
      },
      table: {
        striped: true,
        rowProps: (row: Record<string, any>) => {
          return {
            onClick: () => {
              selectedRowKeys.value = [row.id];
            }
          };
        },
        editable: {
          mode: 'free',
          activeDefault: true,
          showAction: false
        }
      },
      rowHandle: {
        width: 100,
        group: {
          editable: {
            // 自由编辑 switch 按钮
            remove: { show: false }
          }
        }
      },
      search: {
        show: false
      },
      form: {
        wrapper: {
          draggable: false,
        },
        col: {
          span: 24,
        },

      },
      toolbar: {
        buttons: {
          search: { show: false },
          export: { show: false },
          columns: { show: false },
          compact: { show: false }
        }
      },
      columns: {
        // sel: {
        //   type: 'selection',
        //   multiple: false,
        // },

        name: {
          title: '名称',
          type: 'text',
          search: { show: true },
          column: {
            sorter: 'custom',
            editable: {
              disabled: true
            }
          }
        },

        description: {
          title: '备注',
          type: 'text',
          search: { show: true },
          column: {
            sorter: 'custom',
            editable: {
              disabled: true
            }
          }
        },
        status: {
          title: '状态',
          type: 'dict-switch',
          column: {
            editable: {
              disabled: false
            },
            valueChange(context) {
              api.update({id:context.row.id, status: context.value}).then(() => {
                if(context.value) {
                  message.success("启用脚本");
                } else {
                  message.warning("禁用脚本");
                }
              });
            }
          },
          form: {
            value: true
          }
        }
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

onMounted(() => {
  crudExpose.doRefresh();
  // 自由编辑 switch 按钮
  crudExpose.editable.enable({ mode: 'free', activeDefault: true });
  crudBinding.value.rowHandle.group.editable.edit0 = crudBinding.value.rowHandle.buttons.edit;
  crudBinding.value.rowHandle.group.editable.remove0 = crudBinding.value.rowHandle.buttons.remove;
});

watch(selectedRow, newVal => {
  if (newVal) {
    if (newVal.code) {
      ace_states.content = newVal.code;
    } else {
      ace_states.content = demo_code;
    }

    originalContent = ace_states.content;
  }
});


const ace_states = reactive({
  lang: 'python',
  theme: 'monokai',
  content: null
});

const demo_code = `
# 示例代码
import numpy as np

# name需要保持唯一
block = Block(name="FourierTransform1")
block.add_input("I-NumList")
block.add_output("O-NumList")
block.add_option(name="绝对值", type="checkbox", value=True)

# 傅里叶变换
def fourier_transform(vlist):
    n = len(vlist)
    frq = np.arange(n)
    half_x = frq[range(int(n / 2))]  # 取一半区间

    fft_y = np.fft.fft(vlist)
    abs_y = np.abs(fft_y)  # 取复数的绝对值，即复数的模(双边频谱)
    gui_y = abs_y / n  # 归一化处理（双边频谱）
    gui_half_y = gui_y[range(int(n / 2))]  # 由于对称性，只取一半区间（单边频谱）

    data = {"x": half_x.tolist(), "y": gui_half_y.tolist()}

    return data

def fourier_func(self):
    i_data = self.get_interface(name="I-NumList")
    data = i_data["data"]["y"]
    abs = self.get_option("绝对值")
    fdata = fourier_transform(data)

    self.set_interface(name="O-NumList", value={"type": "fourier", "data": fdata})


block.add_compute(fourier_func)
`;

const isContentChanged = ref(false);
let originalContent = demo_code;

const onAceInit = () => {
  console.log('onAceInit');
};

const onAceChange = delta => {
  console.log('onAceChange:', delta);
  isContentChanged.value = ace_states.content !== originalContent;
};

const onSaveCode = () => {
  selectedRow.value.code = ace_states.content;
  api.update(selectedRow.value).then(() => {
    message.success('操作成功');
    originalContent = ace_states.content || "";
  });
};
</script>

<template>
  <div class="layout-padding">
    <NSplit direction="horizontal" class="h-full" :max="0.75" :min="0.25" :default-size="0.4">
      <template #1>
        <NCard class="h-full" :bordered="false">
          <FsCrud ref="crudRef" v-bind="crudBinding"></FsCrud>
        </NCard>
      </template>
      <template #2>
        <NFlex vertical class="h-full">
          <NCard content-style="padding: 3px 5px;" :bordered="false">
            <NFlex>
              <!-- 保存按钮 -->
              <NButton type="primary" size="small" :disabled="!isContentChanged" @click="onSaveCode">
                <template #icon>
                  <icon-material-symbols:check />
                </template>
                保存
              </NButton>

              <!-- 当前编辑信息 -->
              <div v-if="selectedRow" class="flex flex-grow items-center gap-2">
                <NText class="text-gray-600">当前编辑代码：</NText>
                <NText type="primary" class="font-medium">{{ selectedRow?.name }}</NText>
              </div>

              <!-- 未选中时的提示 -->
              <div v-else class="flex flex-grow items-center gap-2">
                <NText class="text-gray-400">请从左侧选择一个脚本进行编辑</NText>
              </div>
            </NFlex>
          </NCard>
          <VAceEditor v-model:value="ace_states.content" class="flex-1" :readonly="!selectedRow" :lang="ace_states.lang"
            :theme="ace_states.theme" placeholder="Enter Formily Schema ..." @init="onAceInit" :options="{
              useWorker: true,
              enableBasicAutocompletion: true,
              enableSnippets: true,
              enableLiveAutocompletion: true
            }" @change="onAceChange" style="height: 100%" />
        </NFlex>
      </template>
    </NSplit>
  </div>
</template>

<style scoped></style>

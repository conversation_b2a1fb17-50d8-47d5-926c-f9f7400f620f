{"version": 3, "sources": ["../../.pnpm/@alova+mock@2.0.10_alova@3.2.6/node_modules/@alova/mock/dist/alova-mock.esm.js"], "sourcesContent": ["/**\n  * @alova/mock 2.0.10 (https://github.com/alovajs/mock)\n  * Document https://github.com/alovajs/mock\n  * Copyright 2024 Scott <PERSON>. All Rights Reserved\n  * Licensed under MIT (git://github.com/alovajs/alova/blob/main/LICENSE)\n*/\n\nimport { isSpecialRequestBody, falseValue, trueValue, undefinedValue, isFn, usePromise, isString, newInstance, promiseResolve, isNumber, promiseReject, globalToString } from '@alova/shared';\n\n// Predefined styles and fixed text\nconst mockLabel = 'mock';\nconst mockLabelColor = '#64bde8';\nconst mockLabelBg = '#ccefff';\nconst realRequestLabel = 'Realtime';\nconst realRequestLabelColor = '#999999';\nconst realRequestLabelBg = '#ededed';\nconst labelStyle = (bgColor, color) => `padding: 2px 6px; background: ${bgColor}; color: ${color};`;\nconst titleStyle = 'color: black; font-size: 12px; font-weight: bolder';\nconst transform2TableData = (obj) => {\n    const tableData = {};\n    for (const key in obj) {\n        tableData[key] = { value: obj[key] };\n    }\n    return tableData;\n};\n// Print request information, dedicated to simulated data requests\nconst consoleRequestInfo = ({ isMock, url, method, headers, query, data, responseHeaders, response }) => {\n    const cole = console;\n    cole.groupCollapsed(`%c${isMock ? mockLabel : realRequestLabel}`, labelStyle(isMock ? mockLabelBg : realRequestLabelBg, isMock ? mockLabelColor : realRequestLabelColor), url);\n    // Request method\n    cole.log('%c[Method]', titleStyle, method.toUpperCase());\n    // OutputRequestHeaders\n    cole.log('%c[Request Headers]', titleStyle);\n    cole.table(transform2TableData(headers));\n    // 输出Query String Parameters\n    cole.log('%c[Query String Parameters]', titleStyle);\n    cole.table(transform2TableData(query));\n    // Output request body\n    cole.log('%c[Request Body]', titleStyle, data || '');\n    // Output response body\n    if (isMock) {\n        // When the response header has data, output Response Headers\n        if (Object.keys(responseHeaders).length > 0) {\n            cole.log('%c[Response Headers]', titleStyle);\n            cole.table(transform2TableData(responseHeaders));\n        }\n        cole.log('%c[Response Body]', titleStyle, response || '');\n    }\n    cole.groupEnd();\n};\n\n/**\n * The default response data interceptor and returns Response data\n */\nconst defaultMockResponse = ({ status = 200, responseHeaders, statusText = 'ok', body }) => ({\n    response: new Response(isSpecialRequestBody(body) ? body : JSON.stringify(body), {\n        status,\n        statusText\n    }),\n    headers: new Headers(responseHeaders)\n});\n/**\n * Return the error message itself\n * @param error error message\n * @returns itself\n */\nconst defaultMockError = (error) => error;\n\n/**\n * parse url\n * @param url url\n * @returns Parsed information object\n */\nconst parseUrl = (url) => {\n    url = /^[^/]*\\/\\//.test(url) ? url : `//${url}`;\n    const splitedFullPath = url.split('/').slice(3);\n    const query = {};\n    let pathContainedParams = splitedFullPath.pop();\n    let pathname = '';\n    let hash = '';\n    if (pathContainedParams) {\n        pathContainedParams = pathContainedParams.replace(/\\?[^?#]+/, mat => {\n            // Parse url parameters\n            mat\n                .substring(1)\n                .split('&')\n                .forEach(paramItem => {\n                const [key, value] = paramItem.split('=');\n                key && (query[key] = value);\n            });\n            return '';\n        });\n        pathContainedParams = pathContainedParams.replace(/#[^#]*/, mat => {\n            hash = mat;\n            return '';\n        });\n        splitedFullPath.push(pathContainedParams);\n        pathname = `/${splitedFullPath.join('/')}`;\n    }\n    return {\n        pathname,\n        query,\n        hash\n    };\n};\n\nfunction MockRequest({ \n// This enable is the main switch\nenable = trueValue, delay = 2000, httpAdapter, mockRequestLogger = consoleRequestInfo, mock, onMockResponse = defaultMockResponse, onMockError = defaultMockError, matchMode = 'pathname' } = { mock: {} }) {\n    return (elements, method) => {\n        // Get the simulation data collection of the current request. If enable is false, no simulation data will be returned.\n        mock = (enable && mock) || {};\n        const { url, data, type, headers: requestHeaders } = elements;\n        let pathname = method.url;\n        let query = method.config.params || {};\n        if (matchMode === 'pathname') {\n            const parsedUrl = parseUrl(url);\n            pathname = parsedUrl.pathname;\n            query = parsedUrl.query;\n        }\n        const params = {};\n        const pathnameSplited = pathname.split('/');\n        const foundMockDataKeys = Object.keys(mock).filter(key => {\n            // If the key is preceded by , it means that this simulation data is ignored, and false is also returned at this time.\n            if (key.startsWith('-')) {\n                return falseValue;\n            }\n            // Match request method\n            let methodType = 'GET';\n            key = key.replace(/^\\[(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\\]/i, (_, $1) => {\n                methodType = $1.toUpperCase();\n                return '';\n            });\n            // The request method does not match and returns false.\n            if (methodType !== type.toUpperCase()) {\n                return falseValue;\n            }\n            const keySplited = key.split('/');\n            if (keySplited.length !== pathnameSplited.length) {\n                return falseValue;\n            }\n            // Determine whether the path matches by matching with the same subscript\n            // If a wildcard is encountered, pass it directly\n            for (const i in keySplited) {\n                const keySplitedItem = keySplited[i];\n                const matchedParamKey = (keySplitedItem.match(/^\\{(.*)\\}$/) || ['', ''])[1];\n                if (!matchedParamKey) {\n                    if (keySplitedItem !== pathnameSplited[i]) {\n                        return falseValue;\n                    }\n                }\n                else {\n                    params[matchedParamKey] = pathnameSplited[i];\n                }\n            }\n            return trueValue;\n        });\n        // If there are multiple matches, the one without wildcards will be used first. If there are both wildcards, the first matched one will be used.\n        let finalKey = foundMockDataKeys.find(key => !/\\{.*\\}/.test(key));\n        finalKey = finalKey || foundMockDataKeys.shift();\n        const mockDataRaw = finalKey ? mock[finalKey] : undefinedValue;\n        // If no simulated data is matched, it means that a request is to be initiated and the http adapter is used to send the request.\n        if (mockDataRaw === undefinedValue) {\n            if (httpAdapter) {\n                isFn(mockRequestLogger) &&\n                    mockRequestLogger({\n                        isMock: falseValue,\n                        url,\n                        method: type,\n                        params,\n                        headers: requestHeaders,\n                        query,\n                        data: {},\n                        responseHeaders: {}\n                    });\n                return httpAdapter(elements, method);\n            }\n            throw new Error(`cannot find the httpAdapter.\\n[url]${url}`);\n        }\n        const promiseResolver = usePromise();\n        const { resolve } = promiseResolver;\n        let { promise: resonpsePromise, reject } = promiseResolver;\n        const timeout = method.config.timeout || 0;\n        if (timeout > 0) {\n            setTimeout(() => {\n                reject(new Error('request timeout'));\n            }, timeout);\n        }\n        const timer = setTimeout(() => {\n            // Response supports returning promise objects\n            try {\n                const res = isFn(mockDataRaw)\n                    ? mockDataRaw({\n                        query,\n                        params,\n                        data: isString(data) || !data ? {} : data,\n                        headers: requestHeaders\n                    })\n                    : mockDataRaw;\n                // This code means that the internal reject is assigned to the outside, and if the timeout occurs, the reject is triggered immediately, or waits for res (if res is a promise) to resolve\n                resolve(newInstance((Promise), (resolveInner, rejectInner) => {\n                    reject = rejectInner;\n                    promiseResolve(res).then(resolveInner).catch(rejectInner);\n                }));\n            }\n            catch (error) {\n                reject(error);\n            }\n        }, delay);\n        resonpsePromise = resonpsePromise\n            .then((response) => {\n            let status = 200;\n            let statusText = 'ok';\n            let responseHeaders = {};\n            let body = undefinedValue;\n            // If there is no return value, it is considered 404\n            if (response === undefinedValue) {\n                status = 404;\n                statusText = 'api not found';\n            }\n            else if (response && isNumber(response.status) && isString(response.statusText)) {\n                // Returned a custom status code and status text as the response message\n                status = response.status;\n                statusText = response.statusText;\n                responseHeaders = response.responseHeaders || responseHeaders;\n                body = response.body;\n            }\n            else {\n                // Otherwise, use response directly as response data\n                body = response;\n            }\n            return newInstance(Promise, (resolve, reject) => {\n                try {\n                    const res = onMockResponse({ status, statusText, responseHeaders, body }, {\n                        headers: requestHeaders,\n                        query,\n                        params,\n                        data: data || {}\n                    }, method);\n                    resolve(res);\n                }\n                catch (error) {\n                    reject(error);\n                }\n            }).then(response => {\n                // Print simulation data request information\n                isFn(mockRequestLogger) &&\n                    mockRequestLogger({\n                        isMock: trueValue,\n                        url,\n                        method: type,\n                        params,\n                        headers: requestHeaders,\n                        query,\n                        data: data || {},\n                        responseHeaders,\n                        response: body\n                    });\n                return response;\n            });\n        })\n            .catch(error => promiseReject(onMockError(error, method)));\n        // Return response data\n        return {\n            response: () => resonpsePromise.then(({ response }) => response && globalToString(response) === '[object Response]' ? response.clone() : response),\n            headers: () => resonpsePromise.then(({ headers }) => headers),\n            abort: () => {\n                clearTimeout(timer);\n                reject(new Error('The user abort request'));\n            }\n        };\n    };\n}\n\n/**\n * Create alova mock data request adapter\n * @param baseURL The simulated base URL, used for namespace use, is consistent with the baseURL parameter of the createAlova function.\n * @returns Create a mock definer\n */\nfunction createAlovaMockAdapter(mockWrapper, options = { enable: true }) {\n    let uniqueMockMap = {};\n    mockWrapper\n        .filter(({ enable }) => enable)\n        .forEach(({ data }) => {\n        uniqueMockMap = {\n            ...data,\n            ...uniqueMockMap\n        };\n    });\n    return MockRequest({\n        ...options,\n        mock: uniqueMockMap\n    });\n}\n\n/**\n * Define simulation data\n * @param mock Simulated data collection, which can be a function or data. If it is a function, it will receive a parameter containing three attributes: query, params, and data, which represent query parameters, path parameters, and request body data respectively.\n * @param enable Whether to use this simulation data collection, the default is true\n */\nvar defineMock = (mock, enable = true) => ({\n    enable,\n    data: mock\n});\n\nexport { createAlovaMockAdapter, defineMock };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAUA,IAAM,YAAY;AAClB,IAAM,iBAAiB;AACvB,IAAM,cAAc;AACpB,IAAM,mBAAmB;AACzB,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAC3B,IAAM,aAAa,CAAC,SAAS,UAAU,iCAAiC,OAAO,YAAY,KAAK;AAChG,IAAM,aAAa;AACnB,IAAM,sBAAsB,CAAC,QAAQ;AACjC,QAAM,YAAY,CAAC;AACnB,aAAW,OAAO,KAAK;AACnB,cAAU,GAAG,IAAI,EAAE,OAAO,IAAI,GAAG,EAAE;AAAA,EACvC;AACA,SAAO;AACX;AAEA,IAAM,qBAAqB,CAAC,EAAE,QAAQ,KAAK,QAAQ,SAAS,OAAO,MAAM,iBAAiB,SAAS,MAAM;AACrG,QAAM,OAAO;AACb,OAAK,eAAe,KAAK,SAAS,YAAY,gBAAgB,IAAI,WAAW,SAAS,cAAc,oBAAoB,SAAS,iBAAiB,qBAAqB,GAAG,GAAG;AAE7K,OAAK,IAAI,cAAc,YAAY,OAAO,YAAY,CAAC;AAEvD,OAAK,IAAI,uBAAuB,UAAU;AAC1C,OAAK,MAAM,oBAAoB,OAAO,CAAC;AAEvC,OAAK,IAAI,+BAA+B,UAAU;AAClD,OAAK,MAAM,oBAAoB,KAAK,CAAC;AAErC,OAAK,IAAI,oBAAoB,YAAY,QAAQ,EAAE;AAEnD,MAAI,QAAQ;AAER,QAAI,OAAO,KAAK,eAAe,EAAE,SAAS,GAAG;AACzC,WAAK,IAAI,wBAAwB,UAAU;AAC3C,WAAK,MAAM,oBAAoB,eAAe,CAAC;AAAA,IACnD;AACA,SAAK,IAAI,qBAAqB,YAAY,YAAY,EAAE;AAAA,EAC5D;AACA,OAAK,SAAS;AAClB;AAKA,IAAM,sBAAsB,CAAC,EAAE,SAAS,KAAK,iBAAiB,aAAa,MAAM,KAAK,OAAO;AAAA,EACzF,UAAU,IAAI,SAAS,qBAAqB,IAAI,IAAI,OAAO,KAAK,UAAU,IAAI,GAAG;AAAA,IAC7E;AAAA,IACA;AAAA,EACJ,CAAC;AAAA,EACD,SAAS,IAAI,QAAQ,eAAe;AACxC;AAMA,IAAM,mBAAmB,CAAC,UAAU;AAOpC,IAAM,WAAW,CAAC,QAAQ;AACtB,QAAM,aAAa,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG;AAC7C,QAAM,kBAAkB,IAAI,MAAM,GAAG,EAAE,MAAM,CAAC;AAC9C,QAAM,QAAQ,CAAC;AACf,MAAI,sBAAsB,gBAAgB,IAAI;AAC9C,MAAI,WAAW;AACf,MAAI,OAAO;AACX,MAAI,qBAAqB;AACrB,0BAAsB,oBAAoB,QAAQ,YAAY,SAAO;AAEjE,UACK,UAAU,CAAC,EACX,MAAM,GAAG,EACT,QAAQ,eAAa;AACtB,cAAM,CAAC,KAAK,KAAK,IAAI,UAAU,MAAM,GAAG;AACxC,gBAAQ,MAAM,GAAG,IAAI;AAAA,MACzB,CAAC;AACD,aAAO;AAAA,IACX,CAAC;AACD,0BAAsB,oBAAoB,QAAQ,UAAU,SAAO;AAC/D,aAAO;AACP,aAAO;AAAA,IACX,CAAC;AACD,oBAAgB,KAAK,mBAAmB;AACxC,eAAW,IAAI,gBAAgB,KAAK,GAAG,CAAC;AAAA,EAC5C;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,SAAS,YAAY;AAAA;AAAA,EAErB,SAAS;AAAA,EAAW,QAAQ;AAAA,EAAM;AAAA,EAAa,oBAAoB;AAAA,EAAoB;AAAA,EAAM,iBAAiB;AAAA,EAAqB,cAAc;AAAA,EAAkB,YAAY;AAAW,IAAI,EAAE,MAAM,CAAC,EAAE,GAAG;AACxM,SAAO,CAAC,UAAU,WAAW;AAEzB,WAAQ,UAAU,QAAS,CAAC;AAC5B,UAAM,EAAE,KAAK,MAAM,MAAM,SAAS,eAAe,IAAI;AACrD,QAAI,WAAW,OAAO;AACtB,QAAI,QAAQ,OAAO,OAAO,UAAU,CAAC;AACrC,QAAI,cAAc,YAAY;AAC1B,YAAM,YAAY,SAAS,GAAG;AAC9B,iBAAW,UAAU;AACrB,cAAQ,UAAU;AAAA,IACtB;AACA,UAAM,SAAS,CAAC;AAChB,UAAM,kBAAkB,SAAS,MAAM,GAAG;AAC1C,UAAM,oBAAoB,OAAO,KAAK,IAAI,EAAE,OAAO,SAAO;AAEtD,UAAI,IAAI,WAAW,GAAG,GAAG;AACrB,eAAO;AAAA,MACX;AAEA,UAAI,aAAa;AACjB,YAAM,IAAI,QAAQ,gEAAgE,CAAC,GAAG,OAAO;AACzF,qBAAa,GAAG,YAAY;AAC5B,eAAO;AAAA,MACX,CAAC;AAED,UAAI,eAAe,KAAK,YAAY,GAAG;AACnC,eAAO;AAAA,MACX;AACA,YAAM,aAAa,IAAI,MAAM,GAAG;AAChC,UAAI,WAAW,WAAW,gBAAgB,QAAQ;AAC9C,eAAO;AAAA,MACX;AAGA,iBAAW,KAAK,YAAY;AACxB,cAAM,iBAAiB,WAAW,CAAC;AACnC,cAAM,mBAAmB,eAAe,MAAM,YAAY,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC;AAC1E,YAAI,CAAC,iBAAiB;AAClB,cAAI,mBAAmB,gBAAgB,CAAC,GAAG;AACvC,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,iBAAO,eAAe,IAAI,gBAAgB,CAAC;AAAA,QAC/C;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AAED,QAAI,WAAW,kBAAkB,KAAK,SAAO,CAAC,SAAS,KAAK,GAAG,CAAC;AAChE,eAAW,YAAY,kBAAkB,MAAM;AAC/C,UAAM,cAAc,WAAW,KAAK,QAAQ,IAAI;AAEhD,QAAI,gBAAgB,gBAAgB;AAChC,UAAI,aAAa;AACb,aAAK,iBAAiB,KAClB,kBAAkB;AAAA,UACd,QAAQ;AAAA,UACR;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,UACA,SAAS;AAAA,UACT;AAAA,UACA,MAAM,CAAC;AAAA,UACP,iBAAiB,CAAC;AAAA,QACtB,CAAC;AACL,eAAO,YAAY,UAAU,MAAM;AAAA,MACvC;AACA,YAAM,IAAI,MAAM;AAAA,OAAsC,GAAG,EAAE;AAAA,IAC/D;AACA,UAAM,kBAAkB,WAAW;AACnC,UAAM,EAAE,QAAQ,IAAI;AACpB,QAAI,EAAE,SAAS,iBAAiB,OAAO,IAAI;AAC3C,UAAM,UAAU,OAAO,OAAO,WAAW;AACzC,QAAI,UAAU,GAAG;AACb,iBAAW,MAAM;AACb,eAAO,IAAI,MAAM,iBAAiB,CAAC;AAAA,MACvC,GAAG,OAAO;AAAA,IACd;AACA,UAAM,QAAQ,WAAW,MAAM;AAE3B,UAAI;AACA,cAAM,MAAM,KAAK,WAAW,IACtB,YAAY;AAAA,UACV;AAAA,UACA;AAAA,UACA,MAAM,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI;AAAA,UACrC,SAAS;AAAA,QACb,CAAC,IACC;AAEN,gBAAQ,YAAa,SAAU,CAAC,cAAc,gBAAgB;AAC1D,mBAAS;AACT,yBAAe,GAAG,EAAE,KAAK,YAAY,EAAE,MAAM,WAAW;AAAA,QAC5D,CAAC,CAAC;AAAA,MACN,SACO,OAAO;AACV,eAAO,KAAK;AAAA,MAChB;AAAA,IACJ,GAAG,KAAK;AACR,sBAAkB,gBACb,KAAK,CAAC,aAAa;AACpB,UAAI,SAAS;AACb,UAAI,aAAa;AACjB,UAAI,kBAAkB,CAAC;AACvB,UAAI,OAAO;AAEX,UAAI,aAAa,gBAAgB;AAC7B,iBAAS;AACT,qBAAa;AAAA,MACjB,WACS,YAAY,SAAS,SAAS,MAAM,KAAK,SAAS,SAAS,UAAU,GAAG;AAE7E,iBAAS,SAAS;AAClB,qBAAa,SAAS;AACtB,0BAAkB,SAAS,mBAAmB;AAC9C,eAAO,SAAS;AAAA,MACpB,OACK;AAED,eAAO;AAAA,MACX;AACA,aAAO,YAAY,SAAS,CAACA,UAASC,YAAW;AAC7C,YAAI;AACA,gBAAM,MAAM,eAAe,EAAE,QAAQ,YAAY,iBAAiB,KAAK,GAAG;AAAA,YACtE,SAAS;AAAA,YACT;AAAA,YACA;AAAA,YACA,MAAM,QAAQ,CAAC;AAAA,UACnB,GAAG,MAAM;AACT,UAAAD,SAAQ,GAAG;AAAA,QACf,SACO,OAAO;AACV,UAAAC,QAAO,KAAK;AAAA,QAChB;AAAA,MACJ,CAAC,EAAE,KAAK,CAAAC,cAAY;AAEhB,aAAK,iBAAiB,KAClB,kBAAkB;AAAA,UACd,QAAQ;AAAA,UACR;AAAA,UACA,QAAQ;AAAA,UACR;AAAA,UACA,SAAS;AAAA,UACT;AAAA,UACA,MAAM,QAAQ,CAAC;AAAA,UACf;AAAA,UACA,UAAU;AAAA,QACd,CAAC;AACL,eAAOA;AAAA,MACX,CAAC;AAAA,IACL,CAAC,EACI,MAAM,WAAS,cAAc,YAAY,OAAO,MAAM,CAAC,CAAC;AAE7D,WAAO;AAAA,MACH,UAAU,MAAM,gBAAgB,KAAK,CAAC,EAAE,SAAS,MAAM,YAAY,eAAe,QAAQ,MAAM,sBAAsB,SAAS,MAAM,IAAI,QAAQ;AAAA,MACjJ,SAAS,MAAM,gBAAgB,KAAK,CAAC,EAAE,QAAQ,MAAM,OAAO;AAAA,MAC5D,OAAO,MAAM;AACT,qBAAa,KAAK;AAClB,eAAO,IAAI,MAAM,wBAAwB,CAAC;AAAA,MAC9C;AAAA,IACJ;AAAA,EACJ;AACJ;AAOA,SAAS,uBAAuB,aAAa,UAAU,EAAE,QAAQ,KAAK,GAAG;AACrE,MAAI,gBAAgB,CAAC;AACrB,cACK,OAAO,CAAC,EAAE,OAAO,MAAM,MAAM,EAC7B,QAAQ,CAAC,EAAE,KAAK,MAAM;AACvB,oBAAgB;AAAA,MACZ,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AAAA,EACJ,CAAC;AACD,SAAO,YAAY;AAAA,IACf,GAAG;AAAA,IACH,MAAM;AAAA,EACV,CAAC;AACL;AAOA,IAAI,aAAa,CAAC,MAAM,SAAS,UAAU;AAAA,EACvC;AAAA,EACA,MAAM;AACV;", "names": ["resolve", "reject", "response"]}
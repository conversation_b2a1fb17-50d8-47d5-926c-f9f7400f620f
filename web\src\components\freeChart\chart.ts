


export var colorPalette_macarons = [
    '#2ec7c9',
    '#b6a2de',
    '#5ab1ef',
    '#ffb980',
    '#d87a80',
    '#8d98b3',
    '#e5cf0d',
    '#97b552',
    '#95706d',
    '#dc69aa',
    '#07a2a4',
    '#9a7fd1',
    '#588dd5',
    '#f5994e',
    '#c05050',
    '#59678c',
    '#c9ab00',
    '#7eb00a',
    '#6f5553',
    '#c14089',
];

export var colorPalette_dark = ['#4992ff', '#7cffb2', '#fddd60', '#ff6e76', '#58d9f9', '#05c091', '#ff8a45', '#8d48e3', '#dd79ff'];
export var colorPalette_default = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];



let data_buf = {};

export function __appendData(chart, data: object, max_len: number = 100000) {

    var legend_data = [];
    var series_data = [];

    // 清理多余的buf
    // for (var k of Object.keys(y_buf)) {
    //     if (!data.y.find(item => item.name == k))
    //         delete y_buf[k];
    // }



    for (const it of data) {
        legend_data.push(it.name);
        if (!data_buf[it.name]) {
            data_buf[it.name] = [];
        }

        for (let i = 0; i < it.value.x.length; i++)
            data_buf[it.name].push([it.value.x[i], it.value.y[i]]);

        if (data_buf[it.name].length > max_len) {
            data_buf[it.name] = data_buf[it.name].slice(-max_len);
        }

        series_data.push({
            name: it.name,
            type: 'line',
            data: data_buf[it.name],
            // sampling: 'lttb',
            smooth: false,
            symbol: 'none',
            // showSymbol: false,
            large: true,
            largeThreshold: 1000,
            itemStyle: {
                normal: {
                    lineStyle: {
                        width: 1, //折线宽度
                    },
                },
            },
        });
    }

    // 清理多余的series
    var series_del = false;

    var chart_option = chart.getOption();
    for (var i = chart_option.series.length - 1; i >= 0; i--) {
        // if (!data.y.find(item => item.name == chart_option.series[i].name)) {
        if (!legend_data.includes(chart_option.series[i].name)) {
            series_del = true;
            // chart_series.splice(i, 1);
        }
    }

    if (series_del) {
        chart_option.series = series_data;
        chart.setOption(chart_option, true)
    }
    else {
        chart.setOption({
            xAxis: {
            },
            legend: {
                data: legend_data,
            },
            series: series_data,
        });
    }

    return legend_data;
};

export function __setData(chart, data: object) {
    var legend_data = [];
    var series_data = [];

    // legend_data.push(it.name);
    // if (!data_buf[it.name]) {
    //     data_buf[it.name] = [];
    // }

    // for( let i=0;i<it.value.x.length; i++)
    //     data_buf[it.name].push([it.value.x[i],it.value.y[i]]);

    for (const it of data) {
        legend_data.push(it.name);

        line_data = [];
        for (let i = 0; i < it.value.x.length; i++)
            line_data.push([it.value.x[i], it.value.y[i]]);

        series_data.push({
            name: it.name,
            type: 'line',
            data: line_data,
            smooth: false,
            symbol: 'none',
            large: true,
            largeThreshold: 1000,
            itemStyle: {
                normal: {
                    lineStyle: {
                        width: 1, //折线宽度
                    },
                },
            },
        });
    }

    // 清理多余的series
    var series_del = false;

    var chart_option = chart.getOption();
    for (var i = chart_option.series.length - 1; i >= 0; i--) {
        // if (!data.y.find(item => item.name == chart_option.series[i].name)) {
        if (!legend_data.includes(chart_option.series[i].name)) {
            series_del = true;
            // chart_series.splice(i, 1);
        }
    }

    if (series_del) {
        chart_option.series = series_data;
        chart.setOption(chart_option, true)
    }
    else {
        chart.setOption({
            xAxis: {
            },
            legend: {
                data: legend_data,
            },
            series: series_data,
        });
    }

    return legend_data;
};

const __getChartData = (chart) => {
    var option = chart.getOption();
    var xAxis = option.xAxis[0]; // x 轴

    var header;
    var seriesData;
    var dataZoomComponent = option.dataZoom[0]; // dataZoom 组件
    if (dataZoomComponent) {
        var startPercent = dataZoomComponent.start;
        var endPercent = dataZoomComponent.end;
        var dataLength = xAxis.data.length;
        var start = Math.floor((startPercent / 100) * dataLength);
        var end = Math.ceil((endPercent / 100) * dataLength);

        var arr = ['time'];
        header = arr.concat(xAxis.data.slice(start, end));

        seriesData = option.series.map(function (serie) {
            var arr = [serie.name];
            return arr.concat(serie.data.slice(start, end));
        });
    } else {
        var arr = ['time'];
        header = arr.concat(xAxis.data);

        seriesData = option.series.map(function (serie) {
            var arr = [serie.name];
            return arr.concat(serie.data);
        });
    }

    var csvContent = header.join(',');

    seriesData.forEach(function (dataArray) {
        var dataString = dataArray.join(',');
        csvContent += '\r\n' + dataString;
    });

    return csvContent;
};

export function __exportCSV(chart, filename) {
    // 获取导出的数据
    var data = __getChartData(chart);
    // 创建 Blob 对象
    var blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
    // 创建下载链接
    var downloadLink = document.createElement('a');
    downloadLink.setAttribute('download', filename);
    downloadLink.setAttribute('href', URL.createObjectURL(blob));
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    // 触发下载链接
    downloadLink.click();
    // 移除下载链接
    document.body.removeChild(downloadLink);
};

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue';
import { useMQTT } from 'mqtt-vue-hook';
import WaveChart from '@/components/waveChart/index.vue';
import { NDropdown, NButton, NTable, NDatePicker, NTabs, NTabPane } from 'naive-ui';
// import { decode, encode } from '@msgpack/msgpack';
import FFT from 'fft.js/lib/fft';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { localStg } from '@/utils/storage';
import { NCheckboxGroup, NCheckbox, NSpace } from 'naive-ui';
import yamlData from './ts1.yaml'

function get_token() {
  const token = localStg.get('token');
  return token;
}

// 构造 steamOptions 结构
const steamOptions: Record<string, { label: string, key: string }[]> = {};
const checkedSteamKeys = ref<string[]>([]);


const groups = yamlData.Node.Groups;
for (const group of groups) {
  const name = group.Name;
  steamOptions[name] = group.Points.map((p: any) => ({
    label: p.AN,
    key: p.KEY?.toUpperCase() ?? ''
  }))
}
const allKeys = Object.values(steamOptions).flat().map(item => item.key);
checkedSteamKeys.value = [...allKeys]; // 默认全选
// checkbox-选中框
function onSteamSelectionChange(newKeys: string[]) {
  console.log('✅ 当前选中的蒸汽参数:', newKeys);
  // 你可以在这里触发数据过滤、图表更新等逻辑
  if (!fullData) return;

  const allKeys = Object.keys(fullData);
  const x = fullData[allKeys[0]]?.map(p => p.x) || [];

  const makeChartData = (groupKey: string) => {
    const y = allKeys
      .filter(k => k.startsWith(groupKey))
      .filter(k => checkedSteamKeys.value.includes(k.split('-')[1])) // 注意：split 后取 label
      .map(k => {
        const name = k.split('-')[1]; // e.g. '3号低压缸进汽压力'
        return {
          name,
          value: fullData[k].map(p => p.y)
        };
      });
    return { x, y };
  };

  const chartDataP = makeChartData('pressure');
  const chartDataT = makeChartData('temperature');

  PressureRef.value?.setData(chartDataP);
  TemperatureRef.value?.setData(chartDataT);
}
let PressureRef = ref();
let TemperatureRef = ref();

let PressureOptions = reactive({ title: '压力', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let TemperatureOptions = reactive({ title: '温度', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });

const showRightPanel = ref(true);
const showEventPanel = ref(true);

let fullData = reactive({});
let tableData = ref([]);
let eventList = ref([
  { time: '2025-07-20 10:00:00', name: '过电压', type: '报警' },
  { time: '2025-07-20 10:05:00', name: '低电流', type: '提示' },
]);
const selectedLine = ref('-未选择');
// 外部保存 eventSource 引用
let sseInstance: EventSourcePolyfill | null = null;

const dropdownOptions = (groups || []).map(group => {
  const groupLabel = group.Name;
  const groupKey = groupLabel === '蒸汽压力' ? 'pressure'
    : groupLabel === '蒸汽温度' ? 'temperature'
      : groupLabel;

  return {
    label: groupLabel,
    key: groupKey,
    children: group.Points.map(point => ({
      label: point.AN,
      key: `${groupKey}-${point.AN}`
    }))
  };
});
console.log(dropdownOptions);
// 时间选择
const dateRange = ref<[number, number] | null>(null);
const setRecent = (type: 'hour' | 'day' | 'week' | 'month') => {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  const oneDay = 24 * 60 * 60 * 1000;
  const ranges = {
    hour: [now - oneHour, now],
    day: [now - oneDay, now],
    week: [now - 7 * oneDay, now],
    month: [now - 30 * oneDay, now],
  };
  dateRange.value = ranges[type];
  handleQuery();
};

// 查询函数
const handleQuery = () => {
  if (!dateRange.value) {
    window.$message?.warning('请先选择时间范围');
    return;
  }
  if (sseInstance) {
    console.log('🛑 页面卸载，手动关闭 SSE');
    sseInstance?.close();
    sseInstance = null;
  }
  if (checkedSteamKeys.value.length === 0) {
    checkedSteamKeys.value = [...allKeys]; // 自动全选
  }

  const [start, end] = dateRange.value;
  const recordType = 'Thermals';
  const startISO = new Date(start).toISOString();
  const endISO = new Date(end).toISOString();
  console.log(startISO, endISO)
  const token = get_token();
  const base_url = import.meta.env.VITE_SERVICE_BASE_URL;
  sseInstance = new EventSourcePolyfill(`${base_url}/tvs/record_sse/${recordType}/${startISO}/${endISO}`, {
    headers: {
      token,
      Authorization: `Bearer ${token}`
    }
  });

  sseInstance.onopen = () => {
    console.log('📡 SSE连接已建立');
  };

  sseInstance.addEventListener('data', (event) => {
    try {
      const raw = event.data;
      const fixedStr = raw
        .replace(/'/g, '"')
        .replace(/\bTrue\b/g, 'true')
        .replace(/\bFalse\b/g, 'false')
        .replace(/\bNone\b/g, 'null');

      const data = JSON.parse(fixedStr);
      console.log('解析后数据', data.batch);
      const batch = data.batch || [];
      if (batch.length < 2) return;

      // const t0 = Number(batch[0].Tstamp);
      // const t1 = Number(batch[1].Tstamp);
      // const fs = 1 / (t1 - t0);
      const N = batch.length;

      const x = batch.map(item => {
        const d = new Date(item.Tstamp * 1000);
        const pad = (n: number, len = 2) => String(n).padStart(len, '0');
        return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ` +
          `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}.${pad(d.getMilliseconds(), 3)}`;
      });

      const getY = (key: string) => batch.map(row => Number(row[key]));

      const chartDataP = {
        x,
        y: steamOptions['蒸汽压力']
          .filter(item => checkedSteamKeys.value.includes(item.key))
          .map(item => ({
            name: item.label,
            value: getY(item.key)
          }))
      };

      const chartDataT = {
        x,
        y: steamOptions['蒸汽温度']
          .filter(item => checkedSteamKeys.value.includes(item.key))
          .map(item => ({
            name: item.label,
            value: getY(item.key)
          }))
      };
      PressureRef.value?.appendData(chartDataP, 10000);
      TemperatureRef.value?.appendData(chartDataT, 10000);

      const getGroupKey = (name: string): string => {
        for (const groupName in steamOptions) {
          if (steamOptions[groupName].some(item => item.label === name)) {
            return groupName === '蒸汽压力' ? 'pressure'
              : groupName === '蒸汽温度' ? 'temperature'
                : groupName;
          }
        }
        return 'unknown';
      };

      const assignFullData = (chartData) => {
        chartData.y.forEach(series => {
          fullData[`${getGroupKey(series.name)}-${series.name}`] = chartData.x.map((xi, i) => ({ x: xi, y: series.value[i] }));
        });
      };

      assignFullData(chartDataP);
      assignFullData(chartDataT);

      tableData.value = fullData[selectedLine.value] || [];

      if (data.has_more === false) {
        console.log('🔚 数据发送完毕（has_more = false），关闭连接');
        sseInstance?.close();
        sseInstance = null;
      }
    } catch (err) {
      console.error('❌ JSON 解析失败:', err);
    }
  });

  sseInstance.addEventListener('end', () => {
    console.log('✅ 数据已接收完毕，关闭连接');
    sseInstance?.close();
    sseInstance = null;
  });

  sseInstance.onerror = (err) => {
    console.error('❌ SSE 错误:', err);
  };
};

const isExporting = ref(false);
const handleExport = async () => {
  console.log('导出数据:')
  if (!dateRange.value) {
    window.$message?.warning('请先选择时间范围');
    return;
  }
  isExporting.value = true;

  const [start, end] = dateRange.value;
  const startISO = new Date(start).toISOString();
  const endISO = new Date(end).toISOString();

  const recordType = 'Thermals'; // 🔁 根据你的页面配置修改
  const token = get_token();
  const base_url = import.meta.env.VITE_SERVICE_BASE_URL;

  try {
    const response = await fetch(`${base_url}/tvs/export_csv`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'token': token // 如果后端使用 token 字段做鉴权
      },
      body: JSON.stringify({
        record_type: recordType,
        start_time: startISO,
        end_time: endISO
      })
    });

    if (!response.ok) {
      throw new Error(`导出失败：${response.status}`);
    }

    // 创建 Blob 下载文件
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    const now = new Date();
    const pad = (n: number) => String(n).padStart(2, '0');
    const timestamp = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}_${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(now.getSeconds())}`;

    // const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    a.download = `tvs_export_${timestamp}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  } catch (err) {
    console.error('❌ 导出失败:', err);
    window.$message?.error('导出失败');
  } finally {
    isExporting.value = false;
  }
};

watch([showRightPanel, showEventPanel], async () => {
  await nextTick();
  [PressureRef, TemperatureRef].forEach(refItem => {
    refItem.value?.resize?.();
  });
});
onMounted(() => {
  checkedSteamKeys.value = Object.values(steamOptions).flat().map(item => item.key);
});
onUnmounted(() => {
  if (sseInstance) {
    console.log('🛑 页面卸载，手动关闭 SSE');
    sseInstance.close();
    sseInstance = null;
  }
});
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <!-- 查询栏 -->
    <div class="flex items-center p-2 gap-2 border-b border-gray-600 bg-black text-white">
      <n-date-picker v-model:value="dateRange" type="datetimerange" size="small" />
      <n-button type="primary" size="small" @click="handleQuery">查询</n-button>
      <n-button size="small" @click="setRecent('hour')">最近一小时</n-button>
      <n-button size="small" @click="setRecent('day')">最近一天</n-button>
      <n-button size="small" @click="setRecent('week')">最近一周</n-button>
      <n-button size="small" @click="setRecent('month')">最近一月</n-button>

      <n-button ghost size="small" @click="handleExport" :disabled="isExporting">{{ isExporting ? '下载中,请稍等...' : '导出数据'
      }}</n-button>
    </div>

    <!-- 图表及右侧面板 -->
    <div class="flex flex-1 overflow-hidden">
      <!-- 图表区 -->
      <div class="flex flex-col flex-1" :style="{
        width:
          showRightPanel && showEventPanel
            ? 'calc(100% - 32rem)'
            : showRightPanel || showEventPanel
              ? 'calc(100% - 16rem)'
              : '100%'
      }">
        <div class="flex h-1/2" style="border-bottom: 2px solid #4d4b4b;">
          <div class="h-full overflow-auto px-2 steam-group steam-check">
            <div class="title-check">蒸汽压力</div>
            <n-checkbox-group v-model:value="checkedSteamKeys" @update:value="onSteamSelectionChange">
              <n-space vertical class="n-space-check">
                <n-checkbox v-for="item in steamOptions['蒸汽压力']" :key="item.key" :value="item.key">
                  {{ item.label }}
                </n-checkbox>
              </n-space>
            </n-checkbox-group>
          </div>
          <div class="flex-1 h-full">
            <WaveChart ref="PressureRef" height="100%" :options="PressureOptions" class="w-full h-full" />
          </div>

        </div>
        <div class="h-1/2 flex">
          <div class="h-full overflow-auto px-2 steam-group steam-check">
            <div class="title-check">蒸汽温度</div>
            <n-checkbox-group v-model:value="checkedSteamKeys" @update:value="onSteamSelectionChange">
              <n-space vertical class="n-space-check">
                <n-checkbox v-for="item in steamOptions['蒸汽温度']" :key="item.key" :value="item.key">
                  {{ item.label }}
                </n-checkbox>
              </n-space>
            </n-checkbox-group>
          </div>
          <div class="flex-1 h-full">
            <WaveChart ref="TemperatureRef" :options="TemperatureOptions" class="w-full h-full" />
          </div>
        </div>

      </div>

      <!-- 数据表格面板 -->
      <div class="relative h-full">
        <n-tooltip trigger="hover" placement="left">
          <template #trigger>
            <n-button v-show="!showRightPanel" class="expand-icon" size="tiny" text @click="showRightPanel = true"
              type="primary">
              <template #icon>
                <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
              </template>
            </n-button>
          </template>
          展开-数据列表
        </n-tooltip>
        <transition name="slide">
          <div v-show="showRightPanel" class="w-48 h-full border-l border-gray-700 bg-black text-white flex flex-col">
            <div class="flex p-2 gap-1">
              <n-tooltip trigger="hover" placement="right">
                <template #trigger>
                  <n-button quaternary size="small" @click="showRightPanel = false"
                    style="background-color: #2a2a2a; border: 1px solid #888;width: 28px;height:28px;">
                    <template #icon>
                      <img src="@/assets/imgs/TablerChevronRightPipe.svg" class="sicon-white " />
                    </template>
                  </n-button>
                </template>
                收起-数据列表
              </n-tooltip>
              <n-dropdown trigger="click" :options="dropdownOptions" @select="(key) => { selectedLine = key }">
                <n-button size="small" class="flex-1">选择线路 - {{ selectedLine.split('-')[1] }}</n-button>
              </n-dropdown>
            </div>
            <div class="flex-1 overflow-auto p-2">
              <n-table :bordered="false" :single-line="false" size="small">
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>X</th>
                    <th>Y</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(row, index) in tableData" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ row.x }}</td>
                    <td>{{ row.y }}</td>
                  </tr>
                </tbody>
              </n-table>
            </div>
          </div>
        </transition>
      </div>

      <!-- 事件面板 -->
      <div class="relative h-full">
        <n-tooltip trigger="hover" placement="left">
          <template #trigger>
            <n-button v-show="!showEventPanel" class="expand-icon" style="top: 200px;" size="tiny" text
              @click="showEventPanel = true" type="primary">
              <template #icon>
                <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
              </template>
            </n-button>
          </template>
          展开-事件列表
        </n-tooltip>

        <transition name="slide">
          <div v-show="showEventPanel" class="w-48 h-full border-l border-gray-700 bg-black text-white flex flex-col">
            <div class="flex p-2 gap-1">
              <n-tooltip trigger="hover" placement="right">
                <template #trigger>
                  <n-button quaternary size="small" @click="showEventPanel = false"
                    style="background-color: #2a2a2a; border: 1px solid #888;width: 28px;height:28px;">
                    <template #icon>
                      <img src="@/assets/imgs/TablerChevronRightPipe.svg" class="sicon-white " />
                    </template>
                  </n-button>
                </template>
                收起-事件列表
              </n-tooltip>
              <span class="text-sm flex-1 text-white">事件列表</span>
            </div>
            <div class="flex-1 overflow-auto p-2">
              <n-table :bordered="false" :single-line="false" size="small">
                <thead>
                  <tr>
                    <th>时间</th>
                    <th>名称</th>
                    <th>类</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, idx) in eventList" :key="idx">
                    <td>{{ item.time }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.type }}</td>
                  </tr>
                </tbody>
              </n-table>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<style scoped>
.expand-icon {
  position: fixed;
  right: 0;
  z-index: 1000;
  width: 28px;
  height: 28px;
  padding: 0;
  background-color: #2a2a2a;
  border: 1px solid #888;
  border-radius: 4px;
}

.sicon-white {
  filter: brightness(0) invert(1);
  width: 16px;
  height: 16px;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

:deep(.n-tabs-tab) {
  padding: 10px 6px 5px 0px !important;
}

:deep(.n-table) {
  font-size: 10px;
}

:deep(.n-checkbox) {
  font-size: 12px;
}

.n-space-check {
  gap: 2px 12px !important;
}

.title-check {
  color: #eee;
  margin: 7px 0px;
}

.steam-check {
  border-right: 1px solid #565656;
  background: #000000;
  min-width: 165px;
}
</style>

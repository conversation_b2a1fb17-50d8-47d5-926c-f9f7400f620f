from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.system_manage.tenants import TenantDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE, OperationType


class OperationsCreateDTO(BaseModel):
    action: OperationType
    target: str
    notes: Optional[str] = None


class OperationsDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    tenant_id: Optional[MODEL_ID_TYPE] = None
    tenant: Optional[TenantDTO] = None
    user: Optional[str] = None
    action: Optional[str] = None
    target: Optional[str] = None
    time: Optional[datetime] = None
    notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

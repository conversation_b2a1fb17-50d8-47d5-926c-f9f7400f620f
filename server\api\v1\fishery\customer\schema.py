from pydantic import BaseModel, Config<PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from fastapi_crud_tortoise import MODEL_ID_TYPE


class CustomerCreateDTO(BaseModel):
    name: str
    contact_person: str
    contact_info: str
    location: Optional[str] = None
    account: str
    password: str
    status: Optional[bool] = True


class CustomerDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    contact_person: Optional[str] = None
    contact_info: Optional[str] = None
    location: Optional[str] = None
    account: Optional[str] = None
    password: Optional[str] = None
    status: Optional[bool] = True

    model_config = ConfigDict(from_attributes=True)


class CustomerPasswordDTO(BaseModel):
    id: MODEL_ID_TYPE
    password: str

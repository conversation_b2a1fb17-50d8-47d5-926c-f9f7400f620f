<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useMQTT } from 'mqtt-vue-hook';
import WaveChart from '@/components/waveChart/index.vue';
import PulseChart from '@/components/pulseChart/index.vue';
import { NDropdown, NButton, NTable, NTabs, NTabPane } from 'naive-ui';
import FFT from 'fft.js/lib/fft';

const mqttHook = useMQTT();

// Chart Refs
const InstRef = ref();
const InstFreqRef = ref();
const TorsRef = ref();
const TorsFreqRef = ref();
const AngleRef = ref();
const AngleFreqRef = ref();
const PulseRef = ref();

const InstOptions = reactive({ title: '角速度', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const InstFreqOptions = reactive({ title: '频谱', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true })
const TorsOptions = reactive({ title: '角速度', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const TorsFreqOptions = reactive({ title: '频谱', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const AngleOptions = reactive({ title: '角度', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const AngleFreqOptions = reactive({ title: '频谱', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const PulseOptions = reactive({ title: '转速脉冲', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });

const fullData = reactive({});
const tableData = ref([]);
const selectedLine = ref('pulse-脉冲(转速脉冲0)');
const showRightPanel = ref(true);
const tabKey = ref('inst');
const sectabKey = ref('volt');

const dropdownOptions = [
  {
    label: '角速度',
    key: 'angle',
    children: [
      { label: '瞬时角速度', key: 'angle-瞬时角速度' },
      { label: '扭振角速度', key: 'angle-扭振角速度' },
      { label: '扭振角度', key: 'angle-扭振角度' }
    ]
  },
  {
    label: '频谱(角速度)',
    key: 'freq',
    children: [
      { label: '瞬时角速度', key: 'freq-频谱(瞬时角速度)' },
      { label: '扭振角速度', key: 'freq-频谱(扭振角速度)' },
      { label: '扭振角度', key: 'freq-频谱(扭振角度)' }
    ]
  },
  {
    label: '转速脉冲',
    key: 'pulse',
    children: [
      { label: '转速脉冲0', key: 'pulse-脉冲(转速脉冲0)' },
      { label: '转速脉冲1', key: 'pulse-脉冲(转速脉冲1)' }
    ]
  }
];

function getGroupKey(name: string): string {
  if (['瞬时角速度', '扭振角速度', '扭振角度'].includes(name)) return 'angle';
  if (['频谱(瞬时角速度)', '频谱(扭振角速度)', '频谱(扭振角度)'].includes(name)) return 'freq';
  if (['脉冲(转速脉冲0)', '脉冲(转速脉冲1)'].includes(name)) return 'pulse';
  return 'unknown';
}

function updateTable(key: string) {
  tableData.value = fullData[key] || [];
}

function handleSelect(key: string) {
  selectedLine.value = key;
  updateTable(key);
}

watch(showRightPanel, async () => {
  await nextTick();
  [InstRef, InstFreqRef, TorsRef, TorsFreqRef, AngleRef, AngleFreqRef, PulseRef].forEach(refItem => {
    refItem.value?.resize?.();
  });
});
// 脉冲图图数据
function transformGearPulses(gearPulses) {
  // 提取表头
  const headers = gearPulses[0];

  // 创建映射对象，方便后续访问字段
  const tstampIndex = headers.indexOf("Tstamp");
  const chanNoIndex = headers.indexOf("ChanNo");
  const isHighIndex = headers.indexOf("IsHigh");

  // 按ChanNo分组
  const result = {};

  // 从第二行开始处理数据（跳过表头）
  for (let i = 1; i < gearPulses.length; i++) {
    const row = gearPulses[i];
    const chanNo = row[chanNoIndex];
    const tstamp = row[tstampIndex];
    const value = row[isHighIndex] ? 5 : 0;

    if (!result[chanNo]) {
      result[chanNo] = [];
    }

    result[chanNo].push([tstamp, value]);
  }

  // 转换为目标格式
  return Object.keys(result).map(chanNo => ({
    name: `脉冲(转速脉冲${chanNo})`,
    value: result[chanNo]
  }));
}

// 初始化事件
onMounted(() => {
  mqttHook.registerEvent('TVS/data/type2', (_, message) => {
    try {
      const jsonStr = new TextDecoder().decode(message);
      const decoded = JSON.parse(jsonStr);
      console.log('解码成功', decoded)
      const raw = decoded.GearPhys;
      const gearPulses = decoded.GearPulses;
      // 非脉冲图
      if (!raw || raw.length <= 1) return;

      const headers = raw[0]; // 表头
      const rows = raw.slice(1); // 数据行

      const tIndex = headers.indexOf('Tstamp');

      const channelIndices = {
        InstSpeed: headers.indexOf('InstSpeed'),
        TorsSpeed: headers.indexOf('TorsSpeed'),
        TorsAngle: headers.indexOf('TorsAngle'),
      };
      const lineName = {
        'InstSpeed': '瞬时角速度',
        'TorsSpeed': '扭振角速度',
        'TorsAngle': '扭振角度'
      }

      const x = rows.map(row => {
        const rawT = Number(row[tIndex]);
        const ms = Math.floor(rawT / 1000);
        const d = new Date(ms - 8 * 60 * 60 * 1000);
        const pad = (n: number, len = 2) => String(n).padStart(len, '0');
        return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ` +
          `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}.${pad(d.getMilliseconds(), 3)}`;
      });

      const buildY = (names: string[]) =>
        names.map(name => ({
          name: lineName[name],
          value: rows.map(row => row[channelIndices[name]])
        }));

      const chartDataV = { x, y: buildY(['InstSpeed']) };
      const chartDataI = { x, y: buildY(['TorsSpeed']) };
      const chartDataE = { x, y: buildY(['TorsAngle']) };

      InstRef.value?.appendData(chartDataV, 10000);
      TorsRef.value?.appendData(chartDataI, 10000);
      AngleRef.value?.appendData(chartDataE, 10000);

      const t0 = Number(rows[0][tIndex]);
      const t1 = Number(rows[1][tIndex]);
      const dt_us = t1 - t0;
      const fs = 1e6 / dt_us;  // Hz，采样率
      function nearestPowerOfTwo(n: number): number {
        return 2 ** Math.floor(Math.log2(n));
      }
      const N = nearestPowerOfTwo(rows.length);
      // 增加频谱处理逻辑（每组计算一次），提取时间戳（微秒）并计算采样率
      if (N < 2) {
        console.warn('❗️数据不足，跳过频谱计算');
        return;
      }
      // 准备信号通道数据
      const getSignal = (ch: keyof typeof channelIndices) =>
        rows.map(row => Number(row[channelIndices[ch]]));

      const signalVA = getSignal('InstSpeed');
      const signalVB = getSignal('TorsSpeed');
      const signalVC = getSignal('TorsAngle');

      const computeFFT = (signal: number[]) => {
        const fft = new FFT(N);
        const out = fft.createComplexArray();
        fft.realTransform(out, signal);
        fft.completeSpectrum(out);
        const mag = [];
        for (let i = 0; i < N / 2; i++) {
          const re = out[2 * i], im = out[2 * i + 1];
          mag.push(Math.sqrt(re * re + im * im));
        }
        return mag;
      };
      // 频率轴（仅 N/2 点）
      const freq = Array.from({ length: N / 2 }, (_, i) => i * fs / N);

      const chartDataFV = {
        x: freq,
        y: [
          { name: '频谱(瞬时角速度)', value: computeFFT(signalVA) }
        ],
      };

      const chartDataFI = {
        x: freq,
        y: [
          { name: '频谱(扭振角速度)', value: computeFFT(signalVB) },
        ],
      };
      const chartDataFA = {
        x: freq,
        y: [
          { name: '频谱(扭振角度)', value: computeFFT(signalVC) },
        ],
      };

      InstFreqRef.value?.setData(chartDataFV);
      TorsFreqRef.value?.setData(chartDataFI);
      AngleFreqRef.value?.setData(chartDataFA);

      // 脉冲图加载
      const pulseChartData = transformGearPulses(gearPulses);
      PulseRef.value?.appendData(pulseChartData, 10000);

      // 更新 fullData（用于表格展示）
      const assignFullData = (chartData: typeof chartDataV) => {
        chartData.y.forEach(series => {
          fullData[`${getGroupKey(series.name)}-${series.name}`] = chartData.x.map((xi, i) => ({
            x: xi,
            y: series.value[i]
          }));
        });
      };
      // 分别更新表格数据源
      assignFullData(chartDataV);
      assignFullData(chartDataI);
      assignFullData(chartDataE);
      assignFullData(chartDataFV);
      assignFullData(chartDataFI);
      assignFullData(chartDataFA);

      // 填充脉冲表格
      // 单独构造 fullData 所需结构：{ x, y: [...] }
      const xList = pulseChartData[0]?.value.map(item => item[0]) || [];

      const chartDataPulse = {
        x: xList.map(t => {
          const ms = Math.floor(Number(t) / 1000);
          const d = new Date(ms - 8 * 60 * 60 * 1000);
          const pad = (n: number, len = 2) => String(n).padStart(len, '0');
          return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ` +
            `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}.${pad(d.getMilliseconds(), 3)}`;
        }),
        y: pulseChartData.map(series => ({
          name: series.name,
          value: series.value.map(item => item[1])
        }))
      };

      // 3. ✅ 用于右侧表格展示
      assignFullData(chartDataPulse);

      // 更新右侧表格展示
      updateTable(selectedLine.value);
    } catch (err) {
      console.error('解析失败:', err);
    }
  }, 'mqtt-gear-key');
});

onUnmounted(() => {
  mqttHook.unRegisterEvent('TVS/data/type2', 'mqtt-gear-key');
});
</script>

<template>
  <div class="flex h-full">
    <div class="flex flex-col" :style="{ width: showRightPanel ? 'calc(100% - 13rem)' : '100%' }">
      <div class="flex h-1/2 border-b border-gray-700">
        <n-tabs v-model:value="tabKey" type="line" placement="left">
          <n-tab-pane name="inst" tab="瞬时角速度">
            <div class="flex h-full">
              <WaveChart ref="InstRef" :options="InstOptions" class="flex-1 border-r" />
              <WaveChart ref="InstFreqRef" :options="InstFreqOptions" class="flex-1" />
            </div>
          </n-tab-pane>
          <n-tab-pane name="tors" tab="扭振角速度">
            <div class="flex h-full">
              <WaveChart ref="TorsRef" :options="TorsOptions" class="flex-1 border-r" />
              <WaveChart ref="TorsFreqRef" :options="TorsFreqOptions" class="flex-1" />
            </div>
          </n-tab-pane>
          <n-tab-pane name="angle" tab="扭振角度">
            <div class="flex h-full">
              <WaveChart ref="AngleRef" :options="AngleOptions" class="flex-1 border-r" />
              <WaveChart ref="AngleFreqRef" :options="AngleFreqOptions" class="flex-1" />
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
      <div class="h-1/2 flex">
        <n-tabs v-model:value="sectabKey" placement="left" type="line">
          <n-tab-pane name="volt" tab="转速脉冲">
            <div class="flex h-full">
              <PulseChart ref="PulseRef" :options="PulseOptions" class="flex-1" />
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
    </div>

    <!-- ✅ 右侧表格面板 -->
    <div class="relative h-full">
      <n-button v-show="!showRightPanel" class="expand-icon" size="tiny" text @click="showRightPanel = true"
        type="primary">
        <template #icon>
          <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
        </template>
      </n-button>

      <transition name="slide">
        <div v-show="showRightPanel" class="w-52 h-full border-l border-gray-700 bg-black text-white flex flex-col">
          <div class="flex p-2 gap-1">
            <n-button quaternary size="small" @click="showRightPanel = false"
              style="background-color: #2a2a2a; border: 1px solid #888;width: 28px;height:28px;">
              <template #icon>
                <img src="@/assets/imgs/TablerChevronRightPipe.svg" class="sicon-white " />
              </template>
            </n-button>
            <n-dropdown trigger="click" :options="dropdownOptions" @select="handleSelect">
              <n-button size="small" class="flex-1 selectline">
                选择线路 - {{ selectedLine.split('-')[1] || '未选中' }}
              </n-button>
            </n-dropdown>
          </div>

          <div class="flex-1 overflow-auto p-2">
            <n-table :bordered="false" :single-line="false" size="small">
              <thead>
                <tr>
                  <th>序号</th>
                  <th>X</th>
                  <th>Y</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(row, index) in tableData" :key="index">
                  <td>{{ index + 1 }}</td>
                  <td>{{ row.x }}</td>
                  <td>{{ row.y }}</td>
                </tr>
              </tbody>
            </n-table>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<style scoped>
.expand-icon {
  position: fixed;
  top: 120px;
  right: 0;
  z-index: 1000;
  width: 28px;
  height: 28px;
  padding: 0;
  background-color: #2a2a2a;
  border: 1px solid #888;
  border-radius: 4px;
}

.sicon-white {
  filter: brightness(0) invert(1);
  width: 16px;
  height: 16px;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.toggle-btn {
  writing-mode: vertical-lr;
  transform: rotate(180deg);
}


.expand-icon {
  position: fixed;
  /* 固定在页面右侧 */
  top: 123px;
  /* 可根据需要调整垂直位置 */
  right: 0;
  z-index: 1000;

  width: 28px;
  height: 28px;
  padding: 0;
  background-color: #2a2a2a;
  border: 1px solid #888;
  border-radius: 4px;
}

.sicon-white {
  filter: brightness(0) invert(1);
  width: 16px;
  height: 16px;
}

:deep(.n-tabs-tab) {
  padding: 10px 6px 5px 0px !important;
}

:deep(.n-table) {
  font-size: 10px;
}

.selectline {
  font-size: 12px;
}

:deep(.n-tabs-nav) {
  min-width: 78px;
}
</style>

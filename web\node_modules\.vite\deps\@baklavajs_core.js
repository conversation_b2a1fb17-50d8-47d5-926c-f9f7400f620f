import {
  __commonJS
} from "./chunk-ULBN3QDT.js";

// node_modules/.pnpm/@baklavajs+core@1.10.2/node_modules/@baklavajs/core/dist/index.cjs
var require_dist = __commonJS({
  "node_modules/.pnpm/@baklavajs+core@1.10.2/node_modules/@baklavajs/core/dist/index.cjs"(exports) {
    (() => {
      "use strict";
      var e = { 163: (e2, t2, n2) => {
        n2.r(t2), n2.d(t2, { __assign: () => i, __asyncDelegator: () => g, __asyncGenerator: () => _, __asyncValues: () => O, __await: () => b, __awaiter: () => f, __classPrivateFieldGet: () => P, __classPrivateFieldSet: () => j, __createBinding: () => l, __decorate: () => s, __exportStar: () => d, __extends: () => r, __generator: () => p, __importDefault: () => E, __importStar: () => x, __makeTemplateObject: () => I, __metadata: () => u, __param: () => c, __read: () => v, __rest: () => a, __spread: () => y, __spreadArray: () => w, __spreadArrays: () => m, __values: () => h });
        var o2 = function(e3, t3) {
          return o2 = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(e4, t4) {
            e4.__proto__ = t4;
          } || function(e4, t4) {
            for (var n3 in t4) Object.prototype.hasOwnProperty.call(t4, n3) && (e4[n3] = t4[n3]);
          }, o2(e3, t3);
        };
        function r(e3, t3) {
          if ("function" != typeof t3 && null !== t3) throw new TypeError("Class extends value " + String(t3) + " is not a constructor or null");
          function n3() {
            this.constructor = e3;
          }
          o2(e3, t3), e3.prototype = null === t3 ? Object.create(t3) : (n3.prototype = t3.prototype, new n3());
        }
        var i = function() {
          return i = Object.assign || function(e3) {
            for (var t3, n3 = 1, o3 = arguments.length; n3 < o3; n3++) for (var r2 in t3 = arguments[n3]) Object.prototype.hasOwnProperty.call(t3, r2) && (e3[r2] = t3[r2]);
            return e3;
          }, i.apply(this, arguments);
        };
        function a(e3, t3) {
          var n3 = {};
          for (var o3 in e3) Object.prototype.hasOwnProperty.call(e3, o3) && t3.indexOf(o3) < 0 && (n3[o3] = e3[o3]);
          if (null != e3 && "function" == typeof Object.getOwnPropertySymbols) {
            var r2 = 0;
            for (o3 = Object.getOwnPropertySymbols(e3); r2 < o3.length; r2++) t3.indexOf(o3[r2]) < 0 && Object.prototype.propertyIsEnumerable.call(e3, o3[r2]) && (n3[o3[r2]] = e3[o3[r2]]);
          }
          return n3;
        }
        function s(e3, t3, n3, o3) {
          var r2, i2 = arguments.length, a2 = i2 < 3 ? t3 : null === o3 ? o3 = Object.getOwnPropertyDescriptor(t3, n3) : o3;
          if ("object" == typeof Reflect && "function" == typeof Reflect.decorate) a2 = Reflect.decorate(e3, t3, n3, o3);
          else for (var s2 = e3.length - 1; s2 >= 0; s2--) (r2 = e3[s2]) && (a2 = (i2 < 3 ? r2(a2) : i2 > 3 ? r2(t3, n3, a2) : r2(t3, n3)) || a2);
          return i2 > 3 && a2 && Object.defineProperty(t3, n3, a2), a2;
        }
        function c(e3, t3) {
          return function(n3, o3) {
            t3(n3, o3, e3);
          };
        }
        function u(e3, t3) {
          if ("object" == typeof Reflect && "function" == typeof Reflect.metadata) return Reflect.metadata(e3, t3);
        }
        function f(e3, t3, n3, o3) {
          return new (n3 || (n3 = Promise))(function(r2, i2) {
            function a2(e4) {
              try {
                c2(o3.next(e4));
              } catch (e5) {
                i2(e5);
              }
            }
            function s2(e4) {
              try {
                c2(o3.throw(e4));
              } catch (e5) {
                i2(e5);
              }
            }
            function c2(e4) {
              var t4;
              e4.done ? r2(e4.value) : (t4 = e4.value, t4 instanceof n3 ? t4 : new n3(function(e5) {
                e5(t4);
              })).then(a2, s2);
            }
            c2((o3 = o3.apply(e3, t3 || [])).next());
          });
        }
        function p(e3, t3) {
          var n3, o3, r2, i2, a2 = { label: 0, sent: function() {
            if (1 & r2[0]) throw r2[1];
            return r2[1];
          }, trys: [], ops: [] };
          return i2 = { next: s2(0), throw: s2(1), return: s2(2) }, "function" == typeof Symbol && (i2[Symbol.iterator] = function() {
            return this;
          }), i2;
          function s2(i3) {
            return function(s3) {
              return function(i4) {
                if (n3) throw new TypeError("Generator is already executing.");
                for (; a2; ) try {
                  if (n3 = 1, o3 && (r2 = 2 & i4[0] ? o3.return : i4[0] ? o3.throw || ((r2 = o3.return) && r2.call(o3), 0) : o3.next) && !(r2 = r2.call(o3, i4[1])).done) return r2;
                  switch (o3 = 0, r2 && (i4 = [2 & i4[0], r2.value]), i4[0]) {
                    case 0:
                    case 1:
                      r2 = i4;
                      break;
                    case 4:
                      return a2.label++, { value: i4[1], done: false };
                    case 5:
                      a2.label++, o3 = i4[1], i4 = [0];
                      continue;
                    case 7:
                      i4 = a2.ops.pop(), a2.trys.pop();
                      continue;
                    default:
                      if (!((r2 = (r2 = a2.trys).length > 0 && r2[r2.length - 1]) || 6 !== i4[0] && 2 !== i4[0])) {
                        a2 = 0;
                        continue;
                      }
                      if (3 === i4[0] && (!r2 || i4[1] > r2[0] && i4[1] < r2[3])) {
                        a2.label = i4[1];
                        break;
                      }
                      if (6 === i4[0] && a2.label < r2[1]) {
                        a2.label = r2[1], r2 = i4;
                        break;
                      }
                      if (r2 && a2.label < r2[2]) {
                        a2.label = r2[2], a2.ops.push(i4);
                        break;
                      }
                      r2[2] && a2.ops.pop(), a2.trys.pop();
                      continue;
                  }
                  i4 = t3.call(e3, a2);
                } catch (e4) {
                  i4 = [6, e4], o3 = 0;
                } finally {
                  n3 = r2 = 0;
                }
                if (5 & i4[0]) throw i4[1];
                return { value: i4[0] ? i4[1] : void 0, done: true };
              }([i3, s3]);
            };
          }
        }
        var l = Object.create ? function(e3, t3, n3, o3) {
          void 0 === o3 && (o3 = n3), Object.defineProperty(e3, o3, { enumerable: true, get: function() {
            return t3[n3];
          } });
        } : function(e3, t3, n3, o3) {
          void 0 === o3 && (o3 = n3), e3[o3] = t3[n3];
        };
        function d(e3, t3) {
          for (var n3 in e3) "default" === n3 || Object.prototype.hasOwnProperty.call(t3, n3) || l(t3, e3, n3);
        }
        function h(e3) {
          var t3 = "function" == typeof Symbol && Symbol.iterator, n3 = t3 && e3[t3], o3 = 0;
          if (n3) return n3.call(e3);
          if (e3 && "number" == typeof e3.length) return { next: function() {
            return e3 && o3 >= e3.length && (e3 = void 0), { value: e3 && e3[o3++], done: !e3 };
          } };
          throw new TypeError(t3 ? "Object is not iterable." : "Symbol.iterator is not defined.");
        }
        function v(e3, t3) {
          var n3 = "function" == typeof Symbol && e3[Symbol.iterator];
          if (!n3) return e3;
          var o3, r2, i2 = n3.call(e3), a2 = [];
          try {
            for (; (void 0 === t3 || t3-- > 0) && !(o3 = i2.next()).done; ) a2.push(o3.value);
          } catch (e4) {
            r2 = { error: e4 };
          } finally {
            try {
              o3 && !o3.done && (n3 = i2.return) && n3.call(i2);
            } finally {
              if (r2) throw r2.error;
            }
          }
          return a2;
        }
        function y() {
          for (var e3 = [], t3 = 0; t3 < arguments.length; t3++) e3 = e3.concat(v(arguments[t3]));
          return e3;
        }
        function m() {
          for (var e3 = 0, t3 = 0, n3 = arguments.length; t3 < n3; t3++) e3 += arguments[t3].length;
          var o3 = Array(e3), r2 = 0;
          for (t3 = 0; t3 < n3; t3++) for (var i2 = arguments[t3], a2 = 0, s2 = i2.length; a2 < s2; a2++, r2++) o3[r2] = i2[a2];
          return o3;
        }
        function w(e3, t3, n3) {
          if (n3 || 2 === arguments.length) for (var o3, r2 = 0, i2 = t3.length; r2 < i2; r2++) !o3 && r2 in t3 || (o3 || (o3 = Array.prototype.slice.call(t3, 0, r2)), o3[r2] = t3[r2]);
          return e3.concat(o3 || Array.prototype.slice.call(t3));
        }
        function b(e3) {
          return this instanceof b ? (this.v = e3, this) : new b(e3);
        }
        function _(e3, t3, n3) {
          if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
          var o3, r2 = n3.apply(e3, t3 || []), i2 = [];
          return o3 = {}, a2("next"), a2("throw"), a2("return"), o3[Symbol.asyncIterator] = function() {
            return this;
          }, o3;
          function a2(e4) {
            r2[e4] && (o3[e4] = function(t4) {
              return new Promise(function(n4, o4) {
                i2.push([e4, t4, n4, o4]) > 1 || s2(e4, t4);
              });
            });
          }
          function s2(e4, t4) {
            try {
              (n4 = r2[e4](t4)).value instanceof b ? Promise.resolve(n4.value.v).then(c2, u2) : f2(i2[0][2], n4);
            } catch (e5) {
              f2(i2[0][3], e5);
            }
            var n4;
          }
          function c2(e4) {
            s2("next", e4);
          }
          function u2(e4) {
            s2("throw", e4);
          }
          function f2(e4, t4) {
            e4(t4), i2.shift(), i2.length && s2(i2[0][0], i2[0][1]);
          }
        }
        function g(e3) {
          var t3, n3;
          return t3 = {}, o3("next"), o3("throw", function(e4) {
            throw e4;
          }), o3("return"), t3[Symbol.iterator] = function() {
            return this;
          }, t3;
          function o3(o4, r2) {
            t3[o4] = e3[o4] ? function(t4) {
              return (n3 = !n3) ? { value: b(e3[o4](t4)), done: "return" === o4 } : r2 ? r2(t4) : t4;
            } : r2;
          }
        }
        function O(e3) {
          if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
          var t3, n3 = e3[Symbol.asyncIterator];
          return n3 ? n3.call(e3) : (e3 = h(e3), t3 = {}, o3("next"), o3("throw"), o3("return"), t3[Symbol.asyncIterator] = function() {
            return this;
          }, t3);
          function o3(n4) {
            t3[n4] = e3[n4] && function(t4) {
              return new Promise(function(o4, r2) {
                !function(e4, t5, n5, o5) {
                  Promise.resolve(o5).then(function(t6) {
                    e4({ value: t6, done: n5 });
                  }, t5);
                }(o4, r2, (t4 = e3[n4](t4)).done, t4.value);
              });
            };
          }
        }
        function I(e3, t3) {
          return Object.defineProperty ? Object.defineProperty(e3, "raw", { value: t3 }) : e3.raw = t3, e3;
        }
        var C = Object.create ? function(e3, t3) {
          Object.defineProperty(e3, "default", { enumerable: true, value: t3 });
        } : function(e3, t3) {
          e3.default = t3;
        };
        function x(e3) {
          if (e3 && e3.__esModule) return e3;
          var t3 = {};
          if (null != e3) for (var n3 in e3) "default" !== n3 && Object.prototype.hasOwnProperty.call(e3, n3) && l(t3, e3, n3);
          return C(t3, e3), t3;
        }
        function E(e3) {
          return e3 && e3.__esModule ? e3 : { default: e3 };
        }
        function P(e3, t3, n3, o3) {
          if ("a" === n3 && !o3) throw new TypeError("Private accessor was defined without a getter");
          if ("function" == typeof t3 ? e3 !== t3 || !o3 : !t3.has(e3)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
          return "m" === n3 ? o3 : "a" === n3 ? o3.call(e3) : o3 ? o3.value : t3.get(e3);
        }
        function j(e3, t3, n3, o3, r2) {
          if ("m" === o3) throw new TypeError("Private method is not writable");
          if ("a" === o3 && !r2) throw new TypeError("Private accessor was defined without a setter");
          if ("function" == typeof t3 ? e3 !== t3 || !r2 : !t3.has(e3)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
          return "a" === o3 ? r2.call(e3, n3) : r2 ? r2.value = n3 : t3.set(e3, n3), n3;
        }
      }, 749: (e2, t2, n2) => {
        t2.p$ = t2.wD = t2.EZ = void 0;
        var o2 = n2(163), r = function() {
          function e3() {
            this.listeners = /* @__PURE__ */ new Map();
          }
          return e3.prototype.addListener = function(e4, t3) {
            this.listeners.set(e4, t3);
          }, e3.prototype.removeListener = function(e4) {
            this.listeners.has(e4) && this.listeners.delete(e4);
          }, e3.prototype.emit = function(e4) {
            this.listeners.forEach(function(t3) {
              return t3(e4);
            });
          }, e3;
        }();
        t2.EZ = r;
        var i = function(e3) {
          function t3() {
            return null !== e3 && e3.apply(this, arguments) || this;
          }
          return o2.__extends(t3, e3), t3.prototype.emit = function(e4) {
            var t4, n3;
            try {
              for (var r2 = o2.__values(Array.from(this.listeners.values())), i2 = r2.next(); !i2.done; i2 = r2.next()) if (false === (0, i2.value)(e4)) return true;
            } catch (e5) {
              t4 = { error: e5 };
            } finally {
              try {
                i2 && !i2.done && (n3 = r2.return) && n3.call(r2);
              } finally {
                if (t4) throw t4.error;
              }
            }
            return false;
          }, t3;
        }(r);
        t2.wD = i;
        var a = function(e3) {
          function t3() {
            return null !== e3 && e3.apply(this, arguments) || this;
          }
          return o2.__extends(t3, e3), t3.prototype.execute = function(e4) {
            var t4, n3, r2 = e4;
            try {
              for (var i2 = o2.__values(this.taps), a2 = i2.next(); !a2.done; a2 = i2.next()) r2 = (0, a2.value)(r2);
            } catch (e5) {
              t4 = { error: e5 };
            } finally {
              try {
                a2 && !a2.done && (n3 = i2.return) && n3.call(i2);
              } finally {
                if (t4) throw t4.error;
              }
            }
            return r2;
          }, t3;
        }(function() {
          function e3() {
            this.tapMap = /* @__PURE__ */ new Map(), this.taps = [];
          }
          return e3.prototype.tap = function(e4, t3) {
            this.tapMap.has(e4) && this.untap(e4), this.tapMap.set(e4, t3), this.taps.push(t3);
          }, e3.prototype.untap = function(e4) {
            if (this.tapMap.has(e4)) {
              var t3 = this.tapMap.get(e4);
              this.tapMap.delete(e4);
              var n3 = this.taps.indexOf(t3);
              n3 >= 0 && this.taps.splice(n3, 1);
            }
          }, e3;
        }());
        t2.p$ = a;
      } }, t = {};
      function n(o2) {
        var r = t[o2];
        if (void 0 !== r) return r.exports;
        var i = t[o2] = { exports: {} };
        return e[o2](i, i.exports, n), i.exports;
      }
      n.d = (e2, t2) => {
        for (var o2 in t2) n.o(t2, o2) && !n.o(e2, o2) && Object.defineProperty(e2, o2, { enumerable: true, get: t2[o2] });
      }, n.o = (e2, t2) => Object.prototype.hasOwnProperty.call(e2, t2), n.r = (e2) => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
      };
      var o = {};
      (() => {
        n.r(o), n.d(o, { Connection: () => i, DummyConnection: () => a, Editor: () => c, Node: () => p, NodeBuilder: () => d, NodeInterface: () => u, NodeOption: () => f });
        var e2 = 0;
        function t2() {
          return Date.now().toString() + (e2++).toString();
        }
        var r = n(749), i = function() {
          function e3(e4, n2) {
            if (this.isInDanger = false, this.destructed = false, this.events = { destruct: new r.EZ() }, !e4 || !n2) throw new Error("Cannot initialize connection with null/undefined for 'from' or 'to' values");
            this.id = t2(), this.from = e4, this.to = n2, this.from.connectionCount++, this.to.connectionCount++;
          }
          return e3.prototype.destruct = function() {
            this.events.destruct.emit(), this.from.connectionCount--, this.to.connectionCount--, this.destructed = true;
          }, e3;
        }(), a = function(e3, n2) {
          if (!e3 || !n2) throw new Error("Cannot initialize connection with null/undefined for 'from' or 'to' values");
          this.id = t2(), this.from = e3, this.to = n2;
        }, s = n(163), c = function() {
          function e3() {
            this._plugins = /* @__PURE__ */ new Set(), this._nodes = [], this._connections = [], this._nodeTypes = /* @__PURE__ */ new Map(), this._nodeCategories = /* @__PURE__ */ new Map([["default", []]]), this._loading = false, this.events = { beforeRegisterNodeType: new r.wD(), registerNodeType: new r.EZ(), beforeAddNode: new r.wD(), addNode: new r.EZ(), beforeRemoveNode: new r.wD(), removeNode: new r.EZ(), beforeAddConnection: new r.wD(), addConnection: new r.EZ(), checkConnection: new r.wD(), beforeRemoveConnection: new r.wD(), removeConnection: new r.EZ(), beforeUsePlugin: new r.wD(), usePlugin: new r.EZ() }, this.hooks = { save: new r.p$(), load: new r.p$() };
          }
          return Object.defineProperty(e3.prototype, "nodes", { get: function() {
            return this._nodes;
          }, enumerable: false, configurable: true }), Object.defineProperty(e3.prototype, "connections", { get: function() {
            return this._connections;
          }, enumerable: false, configurable: true }), Object.defineProperty(e3.prototype, "nodeTypes", { get: function() {
            return this._nodeTypes;
          }, enumerable: false, configurable: true }), Object.defineProperty(e3.prototype, "nodeCategories", { get: function() {
            return this._nodeCategories;
          }, enumerable: false, configurable: true }), Object.defineProperty(e3.prototype, "plugins", { get: function() {
            return this._plugins;
          }, enumerable: false, configurable: true }), Object.defineProperty(e3.prototype, "loading", { get: function() {
            return this._loading;
          }, enumerable: false, configurable: true }), e3.prototype.registerNodeType = function(e4, t3, n2) {
            void 0 === n2 && (n2 = "default"), this.events.beforeRegisterNodeType.emit({ typeName: e4, type: t3, category: n2 }) || (this._nodeTypes.set(e4, t3), this.nodeCategories.has(n2) || this._nodeCategories.set(n2, []), this.nodeCategories.get(n2).push(e4), this.events.registerNodeType.emit({ typeName: e4, type: t3, category: n2 }));
          }, e3.prototype.addNode = function(e4) {
            if (!this.events.beforeAddNode.emit(e4)) return e4.registerEditor(this), this._nodes.push(e4), this.events.addNode.emit(e4), e4;
          }, e3.prototype.removeNode = function(e4) {
            var t3 = this;
            if (this.nodes.includes(e4)) {
              if (this.events.beforeRemoveNode.emit(e4)) return;
              this.connections.filter(function(t4) {
                return t4.from.parent === e4 || t4.to.parent === e4;
              }).forEach(function(e5) {
                return t3.removeConnection(e5);
              }), this._nodes.splice(this.nodes.indexOf(e4), 1), this.events.removeNode.emit(e4);
            }
          }, e3.prototype.addConnection = function(e4, t3) {
            var n2 = this.checkConnection(e4, t3);
            if (n2 && !this.events.beforeAddConnection.emit({ from: e4, to: t3 })) {
              var o2 = new i(n2.from, n2.to);
              return this._connections.push(o2), this.events.addConnection.emit(o2), o2;
            }
          }, e3.prototype.removeConnection = function(e4) {
            if (this.connections.includes(e4)) {
              if (this.events.beforeRemoveConnection.emit(e4)) return;
              e4.destruct(), this._connections.splice(this.connections.indexOf(e4), 1), this.events.removeConnection.emit(e4);
            }
          }, e3.prototype.checkConnection = function(e4, t3) {
            if (!e4 || !t3) return false;
            if (e4.parent === t3.parent) return false;
            if (e4.isInput && !t3.isInput) {
              var n2 = e4;
              e4 = t3, t3 = n2;
            }
            return !(e4.isInput || !t3.isInput) && !this.connections.some(function(n3) {
              return n3.from === e4 && n3.to === t3;
            }) && !this.events.checkConnection.emit({ from: e4, to: t3 }) && new a(e4, t3);
          }, e3.prototype.load = function(e4) {
            var t3, n2, o2, r2;
            try {
              this._loading = true;
              for (var i2 = [], a2 = this.connections.length - 1; a2 >= 0; a2--) this.removeConnection(this.connections[a2]);
              for (a2 = this.nodes.length - 1; a2 >= 0; a2--) this.removeNode(this.nodes[a2]);
              try {
                for (var c2 = (0, s.__values)(e4.nodes), u2 = c2.next(); !u2.done; u2 = c2.next()) {
                  var f2 = u2.value, p2 = this.nodeTypes.get(f2.type);
                  if (p2) {
                    var l2 = new p2();
                    this.addNode(l2), l2.load(f2);
                  } else i2.push("Node type ".concat(f2.type, " is not registered"));
                }
              } catch (e5) {
                t3 = { error: e5 };
              } finally {
                try {
                  u2 && !u2.done && (n2 = c2.return) && n2.call(c2);
                } finally {
                  if (t3) throw t3.error;
                }
              }
              try {
                for (var d2 = (0, s.__values)(e4.connections), h = d2.next(); !h.done; h = d2.next()) {
                  var v = h.value, y = this.findNodeInterface(v.from), m = this.findNodeInterface(v.to);
                  if (y) if (m) {
                    var w = this.addConnection(y, m);
                    w ? w.id = v.id : i2.push("Unable to create connection from ".concat(v.from, " to ").concat(v.to));
                  } else i2.push("Could not find interface with id ".concat(v.to));
                  else i2.push("Could not find interface with id ".concat(v.from));
                }
              } catch (e5) {
                o2 = { error: e5 };
              } finally {
                try {
                  h && !h.done && (r2 = d2.return) && r2.call(d2);
                } finally {
                  if (o2) throw o2.error;
                }
              }
              return this.hooks.load.execute(e4), i2.forEach(function(e5) {
                return console.warn(e5);
              }), i2;
            } finally {
              this._loading = false;
            }
          }, e3.prototype.save = function() {
            var e4 = { nodes: this.nodes.map(function(e5) {
              return e5.save();
            }), connections: this.connections.map(function(e5) {
              return { id: e5.id, from: e5.from.id, to: e5.to.id };
            }) };
            return this.hooks.save.execute(e4);
          }, e3.prototype.use = function(e4) {
            return !this.events.beforeUsePlugin.emit(e4) && (this._plugins.add(e4), e4.register(this), this.events.usePlugin.emit(e4), true);
          }, e3.prototype.generateId = function(e4) {
            return void 0 === e4 && (e4 = ""), e4 + t2();
          }, e3.prototype.findNodeInterface = function(e4) {
            var t3, n2, o2, r2;
            try {
              for (var i2 = (0, s.__values)(this.nodes), a2 = i2.next(); !a2.done; a2 = i2.next()) {
                var c2 = a2.value;
                try {
                  for (var u2 = (o2 = void 0, (0, s.__values)(c2.interfaces.keys())), f2 = u2.next(); !f2.done; f2 = u2.next()) {
                    var p2 = f2.value;
                    if (c2.interfaces.get(p2).id === e4) return c2.interfaces.get(p2);
                  }
                } catch (e5) {
                  o2 = { error: e5 };
                } finally {
                  try {
                    f2 && !f2.done && (r2 = u2.return) && r2.call(u2);
                  } finally {
                    if (o2) throw o2.error;
                  }
                }
              }
            } catch (e5) {
              t3 = { error: e5 };
            } finally {
              try {
                a2 && !a2.done && (n2 = i2.return) && n2.call(i2);
              } finally {
                if (t3) throw t3.error;
              }
            }
          }, e3;
        }(), u = function() {
          function e3(e4, n2) {
            this.events = { setConnectionCount: new r.EZ(), beforeSetValue: new r.wD(), setValue: new r.EZ(), updated: new r.EZ() }, this.hooks = { load: new r.p$(), save: new r.p$() }, this._connectionCount = 0, this._value = null, this.parent = e4, this.isInput = n2, this.id = "ni_" + t2();
          }
          return Object.defineProperty(e3.prototype, "connectionCount", { get: function() {
            return this._connectionCount;
          }, set: function(e4) {
            this._connectionCount = e4, this.events.setConnectionCount.emit(e4);
          }, enumerable: false, configurable: true }), Object.defineProperty(e3.prototype, "value", { get: function() {
            return this._value;
          }, set: function(e4) {
            this.events.beforeSetValue.emit(e4) || (this._value = e4, this.events.setValue.emit(e4));
          }, enumerable: false, configurable: true }), e3.prototype.load = function(e4) {
            this.id = e4.id, this.value = e4.value, this.hooks.load.execute(e4);
          }, e3.prototype.save = function() {
            var e4 = { id: this.id, value: this.value };
            return this.hooks.save.execute(e4);
          }, e3;
        }(), f = function() {
          function e3(e4, t3, n2) {
            this.events = { beforeSetValue: new r.wD(), setValue: new r.EZ(), updated: new r.EZ() }, this.optionComponent = e4, this.sidebarComponent = n2, this._value = t3;
          }
          return Object.defineProperty(e3.prototype, "value", { get: function() {
            return this._value;
          }, set: function(e4) {
            this.events.beforeSetValue.emit(e4) || (this._value = e4, this.events.setValue.emit(e4));
          }, enumerable: false, configurable: true }), e3;
        }(), p = function() {
          function e3() {
            this.id = "node_" + t2(), this.interfaces = /* @__PURE__ */ new Map(), this.options = /* @__PURE__ */ new Map(), this.state = {}, this.events = { beforeAddInterface: new r.wD(), addInterface: new r.EZ(), beforeRemoveInterface: new r.wD(), removeInterface: new r.EZ(), beforeAddOption: new r.wD(), addOption: new r.EZ(), beforeRemoveOption: new r.wD(), removeOption: new r.EZ(), update: new r.EZ() }, this.hooks = { load: new r.p$(), save: new r.p$() };
          }
          return Object.defineProperty(e3.prototype, "inputInterfaces", { get: function() {
            var e4 = {};
            return this.interfaces.forEach(function(t3, n2) {
              t3.isInput && (e4[n2] = t3);
            }), e4;
          }, enumerable: false, configurable: true }), Object.defineProperty(e3.prototype, "outputInterfaces", { get: function() {
            var e4 = {};
            return this.interfaces.forEach(function(t3, n2) {
              t3.isInput || (e4[n2] = t3);
            }), e4;
          }, enumerable: false, configurable: true }), e3.prototype.load = function(e4) {
            var t3 = this;
            this.id = e4.id, this.name = e4.name, this.state = e4.state, e4.options.forEach(function(e5) {
              var n2 = (0, s.__read)(e5, 2), o2 = n2[0], r2 = n2[1];
              t3.options.has(o2) && (t3.options.get(o2).value = r2);
            }), e4.interfaces.forEach(function(e5) {
              var n2 = (0, s.__read)(e5, 2), o2 = n2[0], r2 = n2[1];
              t3.interfaces.has(o2) && t3.interfaces.get(o2).load(r2);
            }), this.hooks.load.execute(e4);
          }, e3.prototype.save = function() {
            var e4 = { type: this.type, id: this.id, name: this.name, options: Array.from(this.options.entries()).map(function(e5) {
              var t3 = (0, s.__read)(e5, 2);
              return [t3[0], t3[1].value];
            }), state: this.state, interfaces: Array.from(this.interfaces.entries()).map(function(e5) {
              var t3 = (0, s.__read)(e5, 2);
              return [t3[0], t3[1].save()];
            }) };
            return this.hooks.save.execute(e4);
          }, e3.prototype.calculate = function(e4) {
          }, e3.prototype.addInputInterface = function(e4, t3, n2, o2) {
            var r2 = this;
            if (void 0 === n2 && (n2 = null), !this.events.beforeAddInterface.emit({ name: e4, isInput: true, option: t3, defaultValue: n2 })) {
              var i2 = this.addInterface(true, e4, t3);
              return i2.events.setValue.addListener(this, function() {
                return r2.events.update.emit({ name: e4, interface: i2 });
              }), i2.value = n2, Object.entries(o2 || {}).forEach(function(e5) {
                var t4 = (0, s.__read)(e5, 2), n3 = t4[0], o3 = t4[1];
                i2[n3] = o3;
              }), this.events.addInterface.emit(i2), i2;
            }
          }, e3.prototype.addOutputInterface = function(e4, t3) {
            if (!this.events.beforeAddInterface.emit({ name: e4, isInput: false })) {
              var n2 = this.addInterface(false, e4);
              return Object.entries(t3 || {}).forEach(function(e5) {
                var t4 = (0, s.__read)(e5, 2), o2 = t4[0], r2 = t4[1];
                n2[o2] = r2;
              }), this.events.addInterface.emit(n2), n2;
            }
          }, e3.prototype.removeInterface = function(e4) {
            var t3 = this, n2 = this.getInterface(e4);
            if (n2) {
              if (this.events.beforeRemoveInterface.emit(n2)) return;
              if (n2.connectionCount > 0) {
                if (!this.editorInstance) throw new Error("Interface is connected, but no editor instance is specified. Unable to delete interface");
                this.editorInstance.connections.filter(function(e5) {
                  return e5.from === n2 || e5.to === n2;
                }).forEach(function(e5) {
                  t3.editorInstance.removeConnection(e5);
                });
              }
              this.interfaces.delete(e4), this.events.removeInterface.emit(n2);
            }
          }, e3.prototype.addOption = function(e4, t3, n2, o2, r2) {
            var i2 = this;
            if (void 0 === n2 && (n2 = null), !this.events.beforeAddOption.emit({ name: e4, component: t3, defaultValue: n2, sidebarComponent: o2 })) {
              var a2 = new f(t3, n2, o2);
              return Object.entries(r2 || {}).forEach(function(e5) {
                var t4 = (0, s.__read)(e5, 2), n3 = t4[0], o3 = t4[1];
                a2[n3] = o3;
              }), a2.events.setValue.addListener(this, function() {
                i2.events.update.emit({ name: e4, option: a2 });
              }), this.options.set(e4, a2), this.events.addOption.emit({ name: e4, option: a2 }), a2;
            }
          }, e3.prototype.removeOption = function(e4) {
            if (this.options.has(e4)) {
              var t3 = this.options.get(e4);
              if (this.events.beforeRemoveOption.emit({ name: e4, option: t3 })) return;
              this.options.delete(e4), this.events.removeOption.emit({ name: e4, option: t3 });
            }
          }, e3.prototype.getInterface = function(e4) {
            if (!this.interfaces.has(e4)) throw new Error("No interface named '".concat(e4, "'"));
            return this.interfaces.get(e4);
          }, e3.prototype.getOptionValue = function(e4) {
            if (!this.options.has(e4)) throw new Error("No option named '".concat(e4, "'"));
            return this.options.get(e4).value;
          }, e3.prototype.setOptionValue = function(e4, t3) {
            if (!this.options.has(e4)) throw new Error("No option named '".concat(e4, "'"));
            this.options.get(e4).value = t3;
          }, e3.prototype.registerEditor = function(e4) {
            this.editorInstance = e4;
          }, e3.prototype.addInterface = function(e4, t3, n2) {
            var o2 = new u(this, e4);
            return o2.option = n2, this.interfaces.set(t3, o2), o2;
          }, e3;
        }();
        function l(e3) {
          return "function" == typeof e3 ? e3() : e3;
        }
        var d = function() {
          function e3(e4, t3) {
            this.type = "", this.name = "", this.intfs = [], this.options = /* @__PURE__ */ new Map(), this.type = e4, this.name = e4, this.additionalProperties = t3;
          }
          return e3.prototype.build = function() {
            return e4 = this.type, t3 = this.name, n2 = this.additionalProperties, o2 = this.intfs, r2 = this.options, i2 = this.calcFunction, function(a2) {
              function c2() {
                var i3, c3, u2 = a2.call(this) || this;
                u2.type = e4, u2.name = t3, n2 && Object.assign(u2, n2);
                try {
                  for (var f2 = (0, s.__values)(o2), p2 = f2.next(); !p2.done; p2 = f2.next()) {
                    var d2 = p2.value;
                    d2.isInput ? u2.addInputInterface(d2.name, d2.option, l(d2.defaultValue), d2.additionalProperties) : u2.addOutputInterface(d2.name, d2.additionalProperties);
                  }
                } catch (e5) {
                  i3 = { error: e5 };
                } finally {
                  try {
                    p2 && !p2.done && (c3 = f2.return) && c3.call(f2);
                  } finally {
                    if (i3) throw i3.error;
                  }
                }
                return Array.from(r2.entries()).forEach(function(e5) {
                  var t4 = (0, s.__read)(e5, 2), n3 = t4[0], o3 = t4[1];
                  u2.addOption(n3, o3.optionComponent, l(o3.value), o3.sidebarComponent, o3.additionalProperties);
                }), u2;
              }
              return (0, s.__extends)(c2, a2), c2.prototype.calculate = function(e5) {
                if (i2) return i2.call(this, this, e5);
              }, c2;
            }(p);
            var e4, t3, n2, o2, r2, i2;
          }, e3.prototype.setName = function(e4) {
            return this.name = e4, this;
          }, e3.prototype.addInputInterface = function(e4, t3, n2, o2) {
            return void 0 === n2 && (n2 = null), this.checkDefaultValue(n2), this.intfs.push({ isInput: true, name: e4, option: t3, defaultValue: n2, additionalProperties: o2 }), this;
          }, e3.prototype.addOutputInterface = function(e4, t3) {
            return this.intfs.push({ isInput: false, name: e4, additionalProperties: t3 }), this;
          }, e3.prototype.addOption = function(e4, t3, n2, o2, r2) {
            return void 0 === n2 && (n2 = null), this.checkDefaultValue(n2), this.options.set(e4, { value: n2, optionComponent: t3, sidebarComponent: o2, additionalProperties: r2 }), this;
          }, e3.prototype.onCalculate = function(e4) {
            return this.calcFunction = e4, this;
          }, e3.prototype.checkDefaultValue = function(e4) {
            if ("object" == typeof e4 && null !== e4) throw new Error("If the default value is an object, provide a generator function instead of the object");
          }, e3;
        }();
      })(), exports.Connection = o.Connection, exports.DummyConnection = o.DummyConnection, exports.Editor = o.Editor, exports.Node = o.Node, exports.NodeBuilder = o.NodeBuilder, exports.NodeInterface = o.NodeInterface, exports.NodeOption = o.NodeOption, Object.defineProperty(exports, "__esModule", { value: true });
    })();
  }
});
export default require_dist();
/*! Bundled license information:

@baklavajs/core/dist/index.cjs:
  (*! For license information please see index.cjs.LICENSE.txt *)
*/
//# sourceMappingURL=@baklavajs_core.js.map

{"version": 3, "sources": ["../../.pnpm/@vueuse+components@12.0.0_typescript@5.7.2/node_modules/@vueuse/components/index.mjs"], "sourcesContent": ["import { onClickOutside as onClickOutside$1, useActiveElement, useBattery, useBrowserLocation, useClipboard, useDark, useDeviceMotion, useDeviceOrientation, useDevicePixelRatio, useDevicesList, useDocumentVisibility, useStorage as useStorage$1, isClient as isClient$1, useDraggable, useElementBounding, useElementSize as useElementSize$1, useElementVisibility as useElementVisibility$1, useEyeDropper, useFullscreen, useGeolocation, useIdle, useMouse, useMouseInElement, useMousePressed, useNetwork, useNow, useObjectUrl, useOffsetPagination, useOnline, usePageLeave, usePointer, usePointerLock, usePreferredColorScheme, usePreferredContrast, usePreferredDark as usePreferredDark$1, usePreferredLanguages, usePreferredReducedMotion, useTimeAgo, useTimestamp, useVirtualList, useWindowFocus, useWindowSize } from '@vueuse/core';\nimport { defineComponent, ref, h, watch, computed, reactive, getCurrentInstance, onMounted, watchEffect, shallowRef, nextTick, toRefs } from 'vue';\nimport { isClient, toValue, noop, isObject, tryOnScopeDispose, isIOS, pausableWatch, tryOnMounted, toRef, useToggle, notNullish, promiseTimeout, until, useDebounceFn, useThrottleFn, tryOnUnmounted } from '@vueuse/shared';\n\nconst OnClickOutside = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"OnClickOutside\",\n  props: [\"as\", \"options\"],\n  emits: [\"trigger\"],\n  setup(props, { slots, emit }) {\n    const target = ref();\n    onClickOutside$1(target, (e) => {\n      emit(\"trigger\", e);\n    }, props.options);\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default());\n    };\n  }\n});\n\nconst defaultWindow = isClient ? window : void 0;\n\nfunction unrefElement(elRef) {\n  var _a;\n  const plain = toValue(elRef);\n  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;\n}\n\nfunction useEventListener(...args) {\n  let target;\n  let events;\n  let listeners;\n  let options;\n  if (typeof args[0] === \"string\" || Array.isArray(args[0])) {\n    [events, listeners, options] = args;\n    target = defaultWindow;\n  } else {\n    [target, events, listeners, options] = args;\n  }\n  if (!target)\n    return noop;\n  if (!Array.isArray(events))\n    events = [events];\n  if (!Array.isArray(listeners))\n    listeners = [listeners];\n  const cleanups = [];\n  const cleanup = () => {\n    cleanups.forEach((fn) => fn());\n    cleanups.length = 0;\n  };\n  const register = (el, event, listener, options2) => {\n    el.addEventListener(event, listener, options2);\n    return () => el.removeEventListener(event, listener, options2);\n  };\n  const stopWatch = watch(\n    () => [unrefElement(target), toValue(options)],\n    ([el, options2]) => {\n      cleanup();\n      if (!el)\n        return;\n      const optionsClone = isObject(options2) ? { ...options2 } : options2;\n      cleanups.push(\n        ...events.flatMap((event) => {\n          return listeners.map((listener) => register(el, event, listener, optionsClone));\n        })\n      );\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return stop;\n}\n\nlet _iOSWorkaround = false;\nfunction onClickOutside(target, handler, options = {}) {\n  const { window = defaultWindow, ignore = [], capture = true, detectIframe = false } = options;\n  if (!window)\n    return noop;\n  if (isIOS && !_iOSWorkaround) {\n    _iOSWorkaround = true;\n    Array.from(window.document.body.children).forEach((el) => el.addEventListener(\"click\", noop));\n    window.document.documentElement.addEventListener(\"click\", noop);\n  }\n  let shouldListen = true;\n  const shouldIgnore = (event) => {\n    return toValue(ignore).some((target2) => {\n      if (typeof target2 === \"string\") {\n        return Array.from(window.document.querySelectorAll(target2)).some((el) => el === event.target || event.composedPath().includes(el));\n      } else {\n        const el = unrefElement(target2);\n        return el && (event.target === el || event.composedPath().includes(el));\n      }\n    });\n  };\n  function hasMultipleRoots(target2) {\n    const vm = toValue(target2);\n    return vm && vm.$.subTree.shapeFlag === 16;\n  }\n  function checkMultipleRoots(target2, event) {\n    const vm = toValue(target2);\n    const children = vm.$.subTree && vm.$.subTree.children;\n    if (children == null || !Array.isArray(children))\n      return false;\n    return children.some((child) => child.el === event.target || event.composedPath().includes(child.el));\n  }\n  const listener = (event) => {\n    const el = unrefElement(target);\n    if (event.target == null)\n      return;\n    if (!(el instanceof Element) && hasMultipleRoots(target) && checkMultipleRoots(target, event))\n      return;\n    if (!el || el === event.target || event.composedPath().includes(el))\n      return;\n    if (event.detail === 0)\n      shouldListen = !shouldIgnore(event);\n    if (!shouldListen) {\n      shouldListen = true;\n      return;\n    }\n    handler(event);\n  };\n  let isProcessingClick = false;\n  const cleanup = [\n    useEventListener(window, \"click\", (event) => {\n      if (!isProcessingClick) {\n        isProcessingClick = true;\n        setTimeout(() => {\n          isProcessingClick = false;\n        }, 0);\n        listener(event);\n      }\n    }, { passive: true, capture }),\n    useEventListener(window, \"pointerdown\", (e) => {\n      const el = unrefElement(target);\n      shouldListen = !shouldIgnore(e) && !!(el && !e.composedPath().includes(el));\n    }, { passive: true }),\n    detectIframe && useEventListener(window, \"blur\", (event) => {\n      setTimeout(() => {\n        var _a;\n        const el = unrefElement(target);\n        if (((_a = window.document.activeElement) == null ? void 0 : _a.tagName) === \"IFRAME\" && !(el == null ? void 0 : el.contains(window.document.activeElement))) {\n          handler(event);\n        }\n      }, 0);\n    })\n  ].filter(Boolean);\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nconst vOnClickOutside = {\n  mounted(el, binding) {\n    const capture = !binding.modifiers.bubble;\n    if (typeof binding.value === \"function\") {\n      el.__onClickOutside_stop = onClickOutside(el, binding.value, { capture });\n    } else {\n      const [handler, options] = binding.value;\n      el.__onClickOutside_stop = onClickOutside(el, handler, Object.assign({ capture }, options));\n    }\n  },\n  unmounted(el) {\n    el.__onClickOutside_stop();\n  }\n};\n\nfunction createKeyPredicate(keyFilter) {\n  if (typeof keyFilter === \"function\")\n    return keyFilter;\n  else if (typeof keyFilter === \"string\")\n    return (event) => event.key === keyFilter;\n  else if (Array.isArray(keyFilter))\n    return (event) => keyFilter.includes(event.key);\n  return () => true;\n}\nfunction onKeyStroke(...args) {\n  let key;\n  let handler;\n  let options = {};\n  if (args.length === 3) {\n    key = args[0];\n    handler = args[1];\n    options = args[2];\n  } else if (args.length === 2) {\n    if (typeof args[1] === \"object\") {\n      key = true;\n      handler = args[0];\n      options = args[1];\n    } else {\n      key = args[0];\n      handler = args[1];\n    }\n  } else {\n    key = true;\n    handler = args[0];\n  }\n  const {\n    target = defaultWindow,\n    eventName = \"keydown\",\n    passive = false,\n    dedupe = false\n  } = options;\n  const predicate = createKeyPredicate(key);\n  const listener = (e) => {\n    if (e.repeat && toValue(dedupe))\n      return;\n    if (predicate(e))\n      handler(e);\n  };\n  return useEventListener(target, eventName, listener, passive);\n}\n\nconst vOnKeyStroke = {\n  mounted(el, binding) {\n    var _a, _b;\n    const keys = (_b = (_a = binding.arg) == null ? void 0 : _a.split(\",\")) != null ? _b : true;\n    if (typeof binding.value === \"function\") {\n      onKeyStroke(keys, binding.value, {\n        target: el\n      });\n    } else {\n      const [handler, options] = binding.value;\n      onKeyStroke(keys, handler, {\n        target: el,\n        ...options\n      });\n    }\n  }\n};\n\nconst DEFAULT_DELAY = 500;\nconst DEFAULT_THRESHOLD = 10;\nfunction onLongPress(target, handler, options) {\n  var _a, _b;\n  const elementRef = computed(() => unrefElement(target));\n  let timeout;\n  let posStart;\n  let startTimestamp;\n  let hasLongPressed = false;\n  function clear() {\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = void 0;\n    }\n    posStart = void 0;\n    startTimestamp = void 0;\n    hasLongPressed = false;\n  }\n  function onRelease(ev) {\n    var _a2, _b2, _c;\n    const [_startTimestamp, _posStart, _hasLongPressed] = [startTimestamp, posStart, hasLongPressed];\n    clear();\n    if (!(options == null ? void 0 : options.onMouseUp) || !_posStart || !_startTimestamp)\n      return;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - _posStart.x;\n    const dy = ev.y - _posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    options.onMouseUp(ev.timeStamp - _startTimestamp, distance, _hasLongPressed);\n  }\n  function onDown(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    clear();\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    posStart = {\n      x: ev.x,\n      y: ev.y\n    };\n    startTimestamp = ev.timeStamp;\n    timeout = setTimeout(\n      () => {\n        hasLongPressed = true;\n        handler(ev);\n      },\n      (_d = options == null ? void 0 : options.delay) != null ? _d : DEFAULT_DELAY\n    );\n  }\n  function onMove(ev) {\n    var _a2, _b2, _c, _d;\n    if (((_a2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _a2.self) && ev.target !== elementRef.value)\n      return;\n    if (!posStart || (options == null ? void 0 : options.distanceThreshold) === false)\n      return;\n    if ((_b2 = options == null ? void 0 : options.modifiers) == null ? void 0 : _b2.prevent)\n      ev.preventDefault();\n    if ((_c = options == null ? void 0 : options.modifiers) == null ? void 0 : _c.stop)\n      ev.stopPropagation();\n    const dx = ev.x - posStart.x;\n    const dy = ev.y - posStart.y;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    if (distance >= ((_d = options == null ? void 0 : options.distanceThreshold) != null ? _d : DEFAULT_THRESHOLD))\n      clear();\n  }\n  const listenerOptions = {\n    capture: (_a = options == null ? void 0 : options.modifiers) == null ? void 0 : _a.capture,\n    once: (_b = options == null ? void 0 : options.modifiers) == null ? void 0 : _b.once\n  };\n  const cleanup = [\n    useEventListener(elementRef, \"pointerdown\", onDown, listenerOptions),\n    useEventListener(elementRef, \"pointermove\", onMove, listenerOptions),\n    useEventListener(elementRef, [\"pointerup\", \"pointerleave\"], onRelease, listenerOptions)\n  ];\n  const stop = () => cleanup.forEach((fn) => fn());\n  return stop;\n}\n\nconst OnLongPress = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"OnLongPress\",\n  props: [\"as\", \"options\"],\n  emits: [\"trigger\"],\n  setup(props, { slots, emit }) {\n    const target = ref();\n    onLongPress(\n      target,\n      (e) => {\n        emit(\"trigger\", e);\n      },\n      props.options\n    );\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default());\n    };\n  }\n});\n\nconst vOnLongPress = {\n  mounted(el, binding) {\n    if (typeof binding.value === \"function\")\n      onLongPress(el, binding.value, { modifiers: binding.modifiers });\n    else\n      onLongPress(el, ...binding.value);\n  }\n};\n\nconst UseActiveElement = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseActiveElement\",\n  setup(props, { slots }) {\n    const data = reactive({\n      element: useActiveElement()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseBattery = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseBattery\",\n  setup(props, { slots }) {\n    const data = reactive(useBattery(props));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseBrowserLocation = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseBrowserLocation\",\n  setup(props, { slots }) {\n    const data = reactive(useBrowserLocation());\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseClipboard = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseClipboard\",\n  props: [\n    \"source\",\n    \"read\",\n    \"navigator\",\n    \"copiedDuring\",\n    \"legacy\"\n  ],\n  setup(props, { slots }) {\n    const data = reactive(useClipboard(props));\n    return () => {\n      var _a;\n      return (_a = slots.default) == null ? void 0 : _a.call(slots, data);\n    };\n  }\n});\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__vueuse_ssr_handlers__\";\nconst handlers = /* @__PURE__ */ getHandlers();\nfunction getHandlers() {\n  if (!(globalKey in _global))\n    _global[globalKey] = _global[globalKey] || {};\n  return _global[globalKey];\n}\nfunction getSSRHandler(key, fallback) {\n  return handlers[key] || fallback;\n}\n\nfunction useMounted() {\n  const isMounted = ref(false);\n  const instance = getCurrentInstance();\n  if (instance) {\n    onMounted(() => {\n      isMounted.value = true;\n    }, instance);\n  }\n  return isMounted;\n}\n\nfunction useSupported(callback) {\n  const isMounted = useMounted();\n  return computed(() => {\n    isMounted.value;\n    return Boolean(callback());\n  });\n}\n\nfunction useMediaQuery(query, options = {}) {\n  const { window = defaultWindow } = options;\n  const isSupported = useSupported(() => window && \"matchMedia\" in window && typeof window.matchMedia === \"function\");\n  let mediaQuery;\n  const matches = ref(false);\n  const handler = (event) => {\n    matches.value = event.matches;\n  };\n  const cleanup = () => {\n    if (!mediaQuery)\n      return;\n    if (\"removeEventListener\" in mediaQuery)\n      mediaQuery.removeEventListener(\"change\", handler);\n    else\n      mediaQuery.removeListener(handler);\n  };\n  const stopWatch = watchEffect(() => {\n    if (!isSupported.value)\n      return;\n    cleanup();\n    mediaQuery = window.matchMedia(toValue(query));\n    if (\"addEventListener\" in mediaQuery)\n      mediaQuery.addEventListener(\"change\", handler);\n    else\n      mediaQuery.addListener(handler);\n    matches.value = mediaQuery.matches;\n  });\n  tryOnScopeDispose(() => {\n    stopWatch();\n    cleanup();\n    mediaQuery = void 0;\n  });\n  return matches;\n}\n\nfunction usePreferredDark(options) {\n  return useMediaQuery(\"(prefers-color-scheme: dark)\", options);\n}\n\nfunction guessSerializerType(rawInit) {\n  return rawInit == null ? \"any\" : rawInit instanceof Set ? \"set\" : rawInit instanceof Map ? \"map\" : rawInit instanceof Date ? \"date\" : typeof rawInit === \"boolean\" ? \"boolean\" : typeof rawInit === \"string\" ? \"string\" : typeof rawInit === \"object\" ? \"object\" : !Number.isNaN(rawInit) ? \"number\" : \"any\";\n}\n\nconst StorageSerializers = {\n  boolean: {\n    read: (v) => v === \"true\",\n    write: (v) => String(v)\n  },\n  object: {\n    read: (v) => JSON.parse(v),\n    write: (v) => JSON.stringify(v)\n  },\n  number: {\n    read: (v) => Number.parseFloat(v),\n    write: (v) => String(v)\n  },\n  any: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  string: {\n    read: (v) => v,\n    write: (v) => String(v)\n  },\n  map: {\n    read: (v) => new Map(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v.entries()))\n  },\n  set: {\n    read: (v) => new Set(JSON.parse(v)),\n    write: (v) => JSON.stringify(Array.from(v))\n  },\n  date: {\n    read: (v) => new Date(v),\n    write: (v) => v.toISOString()\n  }\n};\nconst customStorageEventName = \"vueuse-storage\";\nfunction useStorage(key, defaults, storage, options = {}) {\n  var _a;\n  const {\n    flush = \"pre\",\n    deep = true,\n    listenToStorageChanges = true,\n    writeDefaults = true,\n    mergeDefaults = false,\n    shallow,\n    window = defaultWindow,\n    eventFilter,\n    onError = (e) => {\n      console.error(e);\n    },\n    initOnMounted\n  } = options;\n  const data = (shallow ? shallowRef : ref)(typeof defaults === \"function\" ? defaults() : defaults);\n  if (!storage) {\n    try {\n      storage = getSSRHandler(\"getDefaultStorage\", () => {\n        var _a2;\n        return (_a2 = defaultWindow) == null ? void 0 : _a2.localStorage;\n      })();\n    } catch (e) {\n      onError(e);\n    }\n  }\n  if (!storage)\n    return data;\n  const rawInit = toValue(defaults);\n  const type = guessSerializerType(rawInit);\n  const serializer = (_a = options.serializer) != null ? _a : StorageSerializers[type];\n  const { pause: pauseWatch, resume: resumeWatch } = pausableWatch(\n    data,\n    () => write(data.value),\n    { flush, deep, eventFilter }\n  );\n  if (window && listenToStorageChanges) {\n    tryOnMounted(() => {\n      if (storage instanceof Storage)\n        useEventListener(window, \"storage\", update);\n      else\n        useEventListener(window, customStorageEventName, updateFromCustomEvent);\n      if (initOnMounted)\n        update();\n    });\n  }\n  if (!initOnMounted)\n    update();\n  function dispatchWriteEvent(oldValue, newValue) {\n    if (window) {\n      const payload = {\n        key,\n        oldValue,\n        newValue,\n        storageArea: storage\n      };\n      window.dispatchEvent(storage instanceof Storage ? new StorageEvent(\"storage\", payload) : new CustomEvent(customStorageEventName, {\n        detail: payload\n      }));\n    }\n  }\n  function write(v) {\n    try {\n      const oldValue = storage.getItem(key);\n      if (v == null) {\n        dispatchWriteEvent(oldValue, null);\n        storage.removeItem(key);\n      } else {\n        const serialized = serializer.write(v);\n        if (oldValue !== serialized) {\n          storage.setItem(key, serialized);\n          dispatchWriteEvent(oldValue, serialized);\n        }\n      }\n    } catch (e) {\n      onError(e);\n    }\n  }\n  function read(event) {\n    const rawValue = event ? event.newValue : storage.getItem(key);\n    if (rawValue == null) {\n      if (writeDefaults && rawInit != null)\n        storage.setItem(key, serializer.write(rawInit));\n      return rawInit;\n    } else if (!event && mergeDefaults) {\n      const value = serializer.read(rawValue);\n      if (typeof mergeDefaults === \"function\")\n        return mergeDefaults(value, rawInit);\n      else if (type === \"object\" && !Array.isArray(value))\n        return { ...rawInit, ...value };\n      return value;\n    } else if (typeof rawValue !== \"string\") {\n      return rawValue;\n    } else {\n      return serializer.read(rawValue);\n    }\n  }\n  function update(event) {\n    if (event && event.storageArea !== storage)\n      return;\n    if (event && event.key == null) {\n      data.value = rawInit;\n      return;\n    }\n    if (event && event.key !== key)\n      return;\n    pauseWatch();\n    try {\n      if ((event == null ? void 0 : event.newValue) !== serializer.write(data.value))\n        data.value = read(event);\n    } catch (e) {\n      onError(e);\n    } finally {\n      if (event)\n        nextTick(resumeWatch);\n      else\n        resumeWatch();\n    }\n  }\n  function updateFromCustomEvent(event) {\n    update(event.detail);\n  }\n  return data;\n}\n\nconst CSS_DISABLE_TRANS = \"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\";\nfunction useColorMode(options = {}) {\n  const {\n    selector = \"html\",\n    attribute = \"class\",\n    initialValue = \"auto\",\n    window = defaultWindow,\n    storage,\n    storageKey = \"vueuse-color-scheme\",\n    listenToStorageChanges = true,\n    storageRef,\n    emitAuto,\n    disableTransition = true\n  } = options;\n  const modes = {\n    auto: \"\",\n    light: \"light\",\n    dark: \"dark\",\n    ...options.modes || {}\n  };\n  const preferredDark = usePreferredDark({ window });\n  const system = computed(() => preferredDark.value ? \"dark\" : \"light\");\n  const store = storageRef || (storageKey == null ? toRef(initialValue) : useStorage(storageKey, initialValue, storage, { window, listenToStorageChanges }));\n  const state = computed(() => store.value === \"auto\" ? system.value : store.value);\n  const updateHTMLAttrs = getSSRHandler(\n    \"updateHTMLAttrs\",\n    (selector2, attribute2, value) => {\n      const el = typeof selector2 === \"string\" ? window == null ? void 0 : window.document.querySelector(selector2) : unrefElement(selector2);\n      if (!el)\n        return;\n      const classesToAdd = /* @__PURE__ */ new Set();\n      const classesToRemove = /* @__PURE__ */ new Set();\n      let attributeToChange = null;\n      if (attribute2 === \"class\") {\n        const current = value.split(/\\s/g);\n        Object.values(modes).flatMap((i) => (i || \"\").split(/\\s/g)).filter(Boolean).forEach((v) => {\n          if (current.includes(v))\n            classesToAdd.add(v);\n          else\n            classesToRemove.add(v);\n        });\n      } else {\n        attributeToChange = { key: attribute2, value };\n      }\n      if (classesToAdd.size === 0 && classesToRemove.size === 0 && attributeToChange === null)\n        return;\n      let style;\n      if (disableTransition) {\n        style = window.document.createElement(\"style\");\n        style.appendChild(document.createTextNode(CSS_DISABLE_TRANS));\n        window.document.head.appendChild(style);\n      }\n      for (const c of classesToAdd) {\n        el.classList.add(c);\n      }\n      for (const c of classesToRemove) {\n        el.classList.remove(c);\n      }\n      if (attributeToChange) {\n        el.setAttribute(attributeToChange.key, attributeToChange.value);\n      }\n      if (disableTransition) {\n        window.getComputedStyle(style).opacity;\n        document.head.removeChild(style);\n      }\n    }\n  );\n  function defaultOnChanged(mode) {\n    var _a;\n    updateHTMLAttrs(selector, attribute, (_a = modes[mode]) != null ? _a : mode);\n  }\n  function onChanged(mode) {\n    if (options.onChanged)\n      options.onChanged(mode, defaultOnChanged);\n    else\n      defaultOnChanged(mode);\n  }\n  watch(state, onChanged, { flush: \"post\", immediate: true });\n  tryOnMounted(() => onChanged(state.value));\n  const auto = computed({\n    get() {\n      return emitAuto ? store.value : state.value;\n    },\n    set(v) {\n      store.value = v;\n    }\n  });\n  return Object.assign(auto, { store, system, state });\n}\n\nconst UseColorMode = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseColorMode\",\n  props: [\"selector\", \"attribute\", \"modes\", \"onChanged\", \"storageKey\", \"storage\", \"emitAuto\"],\n  setup(props, { slots }) {\n    const mode = useColorMode(props);\n    const data = reactive({\n      mode,\n      system: mode.system,\n      store: mode.store\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseDark = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseDark\",\n  props: [\"selector\", \"attribute\", \"valueDark\", \"valueLight\", \"onChanged\", \"storageKey\", \"storage\"],\n  setup(props, { slots }) {\n    const isDark = useDark(props);\n    const data = reactive({\n      isDark,\n      toggleDark: useToggle(isDark)\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseDeviceMotion = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseDeviceMotion\",\n  setup(props, { slots }) {\n    const data = reactive(useDeviceMotion());\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseDeviceOrientation = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseDeviceOrientation\",\n  setup(props, { slots }) {\n    const data = reactive(useDeviceOrientation());\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseDevicePixelRatio = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseDevicePixelRatio\",\n  setup(props, { slots }) {\n    const data = reactive({\n      pixelRatio: useDevicePixelRatio()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseDevicesList = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseDevicesList\",\n  props: [\"onUpdated\", \"requestPermissions\", \"constraints\"],\n  setup(props, { slots }) {\n    const data = reactive(useDevicesList(props));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseDocumentVisibility = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseDocumentVisibility\",\n  setup(props, { slots }) {\n    const data = reactive({\n      visibility: useDocumentVisibility()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseDraggable = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseDraggable\",\n  props: [\n    \"storageKey\",\n    \"storageType\",\n    \"initialValue\",\n    \"exact\",\n    \"preventDefault\",\n    \"stopPropagation\",\n    \"pointerTypes\",\n    \"as\",\n    \"handle\",\n    \"axis\",\n    \"onStart\",\n    \"onMove\",\n    \"onEnd\",\n    \"disabled\",\n    \"buttons\",\n    \"containerElement\"\n  ],\n  setup(props, { slots }) {\n    const target = ref();\n    const handle = computed(() => {\n      var _a;\n      return (_a = props.handle) != null ? _a : target.value;\n    });\n    const containerElement = computed(() => {\n      var _a;\n      return (_a = props.containerElement) != null ? _a : void 0;\n    });\n    const disabled = computed(() => !!props.disabled);\n    const storageValue = props.storageKey && useStorage$1(\n      props.storageKey,\n      toValue(props.initialValue) || { x: 0, y: 0 },\n      isClient$1 ? props.storageType === \"session\" ? sessionStorage : localStorage : void 0\n    );\n    const initialValue = storageValue || props.initialValue || { x: 0, y: 0 };\n    const onEnd = (position, event) => {\n      var _a;\n      (_a = props.onEnd) == null ? void 0 : _a.call(props, position, event);\n      if (!storageValue)\n        return;\n      storageValue.value.x = position.x;\n      storageValue.value.y = position.y;\n    };\n    const data = reactive(useDraggable(target, {\n      ...props,\n      handle,\n      initialValue,\n      onEnd,\n      disabled,\n      containerElement\n    }));\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target, style: `touch-action:none;${data.style}` }, slots.default(data));\n    };\n  }\n});\n\nconst UseElementBounding = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseElementBounding\",\n  props: [\"box\", \"as\"],\n  setup(props, { slots }) {\n    const target = ref();\n    const data = reactive(useElementBounding(target));\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default(data));\n    };\n  }\n});\n\nfunction useElementHover(el, options = {}) {\n  const {\n    delayEnter = 0,\n    delayLeave = 0,\n    window = defaultWindow\n  } = options;\n  const isHovered = ref(false);\n  let timer;\n  const toggle = (entering) => {\n    const delay = entering ? delayEnter : delayLeave;\n    if (timer) {\n      clearTimeout(timer);\n      timer = void 0;\n    }\n    if (delay)\n      timer = setTimeout(() => isHovered.value = entering, delay);\n    else\n      isHovered.value = entering;\n  };\n  if (!window)\n    return isHovered;\n  useEventListener(el, \"mouseenter\", () => toggle(true), { passive: true });\n  useEventListener(el, \"mouseleave\", () => toggle(false), { passive: true });\n  return isHovered;\n}\n\nconst vElementHover = {\n  mounted(el, binding) {\n    const value = binding.value;\n    if (typeof value === \"function\") {\n      const isHovered = useElementHover(el);\n      watch(isHovered, (v) => value(v));\n    } else {\n      const [handler, options] = value;\n      const isHovered = useElementHover(el, options);\n      watch(isHovered, (v) => handler(v));\n    }\n  }\n};\n\nconst UseElementSize = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseElementSize\",\n  props: [\"width\", \"height\", \"box\", \"as\"],\n  setup(props, { slots }) {\n    const target = ref();\n    const data = reactive(useElementSize$1(target, { width: props.width, height: props.height }, { box: props.box }));\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default(data));\n    };\n  }\n});\n\nfunction useResizeObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...observerOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"ResizeObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => {\n    const _targets = toValue(target);\n    return Array.isArray(_targets) ? _targets.map((el) => unrefElement(el)) : [unrefElement(_targets)];\n  });\n  const stopWatch = watch(\n    targets,\n    (els) => {\n      cleanup();\n      if (isSupported.value && window) {\n        observer = new ResizeObserver(callback);\n        for (const _el of els) {\n          if (_el)\n            observer.observe(_el, observerOptions);\n        }\n      }\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const stop = () => {\n    cleanup();\n    stopWatch();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop\n  };\n}\n\nfunction useElementSize(target, initialSize = { width: 0, height: 0 }, options = {}) {\n  const { window = defaultWindow, box = \"content-box\" } = options;\n  const isSVG = computed(() => {\n    var _a, _b;\n    return (_b = (_a = unrefElement(target)) == null ? void 0 : _a.namespaceURI) == null ? void 0 : _b.includes(\"svg\");\n  });\n  const width = ref(initialSize.width);\n  const height = ref(initialSize.height);\n  const { stop: stop1 } = useResizeObserver(\n    target,\n    ([entry]) => {\n      const boxSize = box === \"border-box\" ? entry.borderBoxSize : box === \"content-box\" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;\n      if (window && isSVG.value) {\n        const $elem = unrefElement(target);\n        if ($elem) {\n          const rect = $elem.getBoundingClientRect();\n          width.value = rect.width;\n          height.value = rect.height;\n        }\n      } else {\n        if (boxSize) {\n          const formatBoxSize = Array.isArray(boxSize) ? boxSize : [boxSize];\n          width.value = formatBoxSize.reduce((acc, { inlineSize }) => acc + inlineSize, 0);\n          height.value = formatBoxSize.reduce((acc, { blockSize }) => acc + blockSize, 0);\n        } else {\n          width.value = entry.contentRect.width;\n          height.value = entry.contentRect.height;\n        }\n      }\n    },\n    options\n  );\n  tryOnMounted(() => {\n    const ele = unrefElement(target);\n    if (ele) {\n      width.value = \"offsetWidth\" in ele ? ele.offsetWidth : initialSize.width;\n      height.value = \"offsetHeight\" in ele ? ele.offsetHeight : initialSize.height;\n    }\n  });\n  const stop2 = watch(\n    () => unrefElement(target),\n    (ele) => {\n      width.value = ele ? initialSize.width : 0;\n      height.value = ele ? initialSize.height : 0;\n    }\n  );\n  function stop() {\n    stop1();\n    stop2();\n  }\n  return {\n    width,\n    height,\n    stop\n  };\n}\n\nconst vElementSize = {\n  mounted(el, binding) {\n    var _a;\n    const handler = typeof binding.value === \"function\" ? binding.value : (_a = binding.value) == null ? void 0 : _a[0];\n    const options = typeof binding.value === \"function\" ? [] : binding.value.slice(1);\n    const { width, height } = useElementSize(el, ...options);\n    watch([width, height], ([width2, height2]) => handler({ width: width2, height: height2 }));\n  }\n};\n\nconst UseElementVisibility = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseElementVisibility\",\n  props: [\"as\"],\n  setup(props, { slots }) {\n    const target = ref();\n    const data = reactive({\n      isVisible: useElementVisibility$1(target)\n    });\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default(data));\n    };\n  }\n});\n\nfunction useIntersectionObserver(target, callback, options = {}) {\n  const {\n    root,\n    rootMargin = \"0px\",\n    threshold = 0,\n    window = defaultWindow,\n    immediate = true\n  } = options;\n  const isSupported = useSupported(() => window && \"IntersectionObserver\" in window);\n  const targets = computed(() => {\n    const _target = toValue(target);\n    return (Array.isArray(_target) ? _target : [_target]).map(unrefElement).filter(notNullish);\n  });\n  let cleanup = noop;\n  const isActive = ref(immediate);\n  const stopWatch = isSupported.value ? watch(\n    () => [targets.value, unrefElement(root), isActive.value],\n    ([targets2, root2]) => {\n      cleanup();\n      if (!isActive.value)\n        return;\n      if (!targets2.length)\n        return;\n      const observer = new IntersectionObserver(\n        callback,\n        {\n          root: unrefElement(root2),\n          rootMargin,\n          threshold\n        }\n      );\n      targets2.forEach((el) => el && observer.observe(el));\n      cleanup = () => {\n        observer.disconnect();\n        cleanup = noop;\n      };\n    },\n    { immediate, flush: \"post\" }\n  ) : noop;\n  const stop = () => {\n    cleanup();\n    stopWatch();\n    isActive.value = false;\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    isActive,\n    pause() {\n      cleanup();\n      isActive.value = false;\n    },\n    resume() {\n      isActive.value = true;\n    },\n    stop\n  };\n}\n\nfunction useElementVisibility(element, options = {}) {\n  const { window = defaultWindow, scrollTarget, threshold = 0 } = options;\n  const elementIsVisible = ref(false);\n  useIntersectionObserver(\n    element,\n    (intersectionObserverEntries) => {\n      let isIntersecting = elementIsVisible.value;\n      let latestTime = 0;\n      for (const entry of intersectionObserverEntries) {\n        if (entry.time >= latestTime) {\n          latestTime = entry.time;\n          isIntersecting = entry.isIntersecting;\n        }\n      }\n      elementIsVisible.value = isIntersecting;\n    },\n    {\n      root: scrollTarget,\n      window,\n      threshold\n    }\n  );\n  return elementIsVisible;\n}\n\nconst vElementVisibility = {\n  mounted(el, binding) {\n    if (typeof binding.value === \"function\") {\n      const handler = binding.value;\n      const isVisible = useElementVisibility(el);\n      watch(isVisible, (v) => handler(v), { immediate: true });\n    } else {\n      const [handler, options] = binding.value;\n      const isVisible = useElementVisibility(el, options);\n      watch(isVisible, (v) => handler(v), { immediate: true });\n    }\n  }\n};\n\nconst UseEyeDropper = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseEyeDropper\",\n  props: {\n    sRGBHex: String\n  },\n  setup(props, { slots }) {\n    const data = reactive(useEyeDropper());\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseFullscreen = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseFullscreen\",\n  props: [\"as\"],\n  setup(props, { slots }) {\n    const target = ref();\n    const data = reactive(useFullscreen(target));\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default(data));\n    };\n  }\n});\n\nconst UseGeolocation = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseGeolocation\",\n  props: [\"enableHighAccuracy\", \"maximumAge\", \"timeout\", \"navigator\"],\n  setup(props, { slots }) {\n    const data = reactive(useGeolocation(props));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseIdle = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseIdle\",\n  props: [\"timeout\", \"events\", \"listenForVisibilityChange\", \"initialState\"],\n  setup(props, { slots }) {\n    const data = reactive(useIdle(props.timeout, props));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nfunction useAsyncState(promise, initialState, options) {\n  const {\n    immediate = true,\n    delay = 0,\n    onError = noop,\n    onSuccess = noop,\n    resetOnExecute = true,\n    shallow = true,\n    throwError\n  } = options != null ? options : {};\n  const state = shallow ? shallowRef(initialState) : ref(initialState);\n  const isReady = ref(false);\n  const isLoading = ref(false);\n  const error = shallowRef(void 0);\n  async function execute(delay2 = 0, ...args) {\n    if (resetOnExecute)\n      state.value = initialState;\n    error.value = void 0;\n    isReady.value = false;\n    isLoading.value = true;\n    if (delay2 > 0)\n      await promiseTimeout(delay2);\n    const _promise = typeof promise === \"function\" ? promise(...args) : promise;\n    try {\n      const data = await _promise;\n      state.value = data;\n      isReady.value = true;\n      onSuccess(data);\n    } catch (e) {\n      error.value = e;\n      onError(e);\n      if (throwError)\n        throw e;\n    } finally {\n      isLoading.value = false;\n    }\n    return state.value;\n  }\n  if (immediate)\n    execute(delay);\n  const shell = {\n    state,\n    isReady,\n    isLoading,\n    error,\n    execute\n  };\n  function waitUntilIsLoaded() {\n    return new Promise((resolve, reject) => {\n      until(isLoading).toBe(false).then(() => resolve(shell)).catch(reject);\n    });\n  }\n  return {\n    ...shell,\n    then(onFulfilled, onRejected) {\n      return waitUntilIsLoaded().then(onFulfilled, onRejected);\n    }\n  };\n}\n\nasync function loadImage(options) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    const { src, srcset, sizes, class: clazz, loading, crossorigin, referrerPolicy } = options;\n    img.src = src;\n    if (srcset)\n      img.srcset = srcset;\n    if (sizes)\n      img.sizes = sizes;\n    if (clazz)\n      img.className = clazz;\n    if (loading)\n      img.loading = loading;\n    if (crossorigin)\n      img.crossOrigin = crossorigin;\n    if (referrerPolicy)\n      img.referrerPolicy = referrerPolicy;\n    img.onload = () => resolve(img);\n    img.onerror = reject;\n  });\n}\nfunction useImage(options, asyncStateOptions = {}) {\n  const state = useAsyncState(\n    () => loadImage(toValue(options)),\n    void 0,\n    {\n      resetOnExecute: true,\n      ...asyncStateOptions\n    }\n  );\n  watch(\n    () => toValue(options),\n    () => state.execute(asyncStateOptions.delay),\n    { deep: true }\n  );\n  return state;\n}\n\nconst UseImage = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseImage\",\n  props: [\n    \"src\",\n    \"srcset\",\n    \"sizes\",\n    \"as\",\n    \"alt\",\n    \"class\",\n    \"loading\",\n    \"crossorigin\",\n    \"referrerPolicy\"\n  ],\n  setup(props, { slots }) {\n    const data = reactive(useImage(props));\n    return () => {\n      if (data.isLoading && slots.loading)\n        return slots.loading(data);\n      else if (data.error && slots.error)\n        return slots.error(data.error);\n      if (slots.default)\n        return slots.default(data);\n      return h(props.as || \"img\", props);\n    };\n  }\n});\n\nfunction resolveElement(el) {\n  if (typeof Window !== \"undefined\" && el instanceof Window)\n    return el.document.documentElement;\n  if (typeof Document !== \"undefined\" && el instanceof Document)\n    return el.documentElement;\n  return el;\n}\n\nconst ARRIVED_STATE_THRESHOLD_PIXELS = 1;\nfunction useScroll(element, options = {}) {\n  const {\n    throttle = 0,\n    idle = 200,\n    onStop = noop,\n    onScroll = noop,\n    offset = {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0\n    },\n    eventListenerOptions = {\n      capture: false,\n      passive: true\n    },\n    behavior = \"auto\",\n    window = defaultWindow,\n    onError = (e) => {\n      console.error(e);\n    }\n  } = options;\n  const internalX = ref(0);\n  const internalY = ref(0);\n  const x = computed({\n    get() {\n      return internalX.value;\n    },\n    set(x2) {\n      scrollTo(x2, void 0);\n    }\n  });\n  const y = computed({\n    get() {\n      return internalY.value;\n    },\n    set(y2) {\n      scrollTo(void 0, y2);\n    }\n  });\n  function scrollTo(_x, _y) {\n    var _a, _b, _c, _d;\n    if (!window)\n      return;\n    const _element = toValue(element);\n    if (!_element)\n      return;\n    (_c = _element instanceof Document ? window.document.body : _element) == null ? void 0 : _c.scrollTo({\n      top: (_a = toValue(_y)) != null ? _a : y.value,\n      left: (_b = toValue(_x)) != null ? _b : x.value,\n      behavior: toValue(behavior)\n    });\n    const scrollContainer = ((_d = _element == null ? void 0 : _element.document) == null ? void 0 : _d.documentElement) || (_element == null ? void 0 : _element.documentElement) || _element;\n    if (x != null)\n      internalX.value = scrollContainer.scrollLeft;\n    if (y != null)\n      internalY.value = scrollContainer.scrollTop;\n  }\n  const isScrolling = ref(false);\n  const arrivedState = reactive({\n    left: true,\n    right: false,\n    top: true,\n    bottom: false\n  });\n  const directions = reactive({\n    left: false,\n    right: false,\n    top: false,\n    bottom: false\n  });\n  const onScrollEnd = (e) => {\n    if (!isScrolling.value)\n      return;\n    isScrolling.value = false;\n    directions.left = false;\n    directions.right = false;\n    directions.top = false;\n    directions.bottom = false;\n    onStop(e);\n  };\n  const onScrollEndDebounced = useDebounceFn(onScrollEnd, throttle + idle);\n  const setArrivedState = (target) => {\n    var _a;\n    if (!window)\n      return;\n    const el = ((_a = target == null ? void 0 : target.document) == null ? void 0 : _a.documentElement) || (target == null ? void 0 : target.documentElement) || unrefElement(target);\n    const { display, flexDirection } = getComputedStyle(el);\n    const scrollLeft = el.scrollLeft;\n    directions.left = scrollLeft < internalX.value;\n    directions.right = scrollLeft > internalX.value;\n    const left = Math.abs(scrollLeft) <= (offset.left || 0);\n    const right = Math.abs(scrollLeft) + el.clientWidth >= el.scrollWidth - (offset.right || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"row-reverse\") {\n      arrivedState.left = right;\n      arrivedState.right = left;\n    } else {\n      arrivedState.left = left;\n      arrivedState.right = right;\n    }\n    internalX.value = scrollLeft;\n    let scrollTop = el.scrollTop;\n    if (target === window.document && !scrollTop)\n      scrollTop = window.document.body.scrollTop;\n    directions.top = scrollTop < internalY.value;\n    directions.bottom = scrollTop > internalY.value;\n    const top = Math.abs(scrollTop) <= (offset.top || 0);\n    const bottom = Math.abs(scrollTop) + el.clientHeight >= el.scrollHeight - (offset.bottom || 0) - ARRIVED_STATE_THRESHOLD_PIXELS;\n    if (display === \"flex\" && flexDirection === \"column-reverse\") {\n      arrivedState.top = bottom;\n      arrivedState.bottom = top;\n    } else {\n      arrivedState.top = top;\n      arrivedState.bottom = bottom;\n    }\n    internalY.value = scrollTop;\n  };\n  const onScrollHandler = (e) => {\n    var _a;\n    if (!window)\n      return;\n    const eventTarget = (_a = e.target.documentElement) != null ? _a : e.target;\n    setArrivedState(eventTarget);\n    isScrolling.value = true;\n    onScrollEndDebounced(e);\n    onScroll(e);\n  };\n  useEventListener(\n    element,\n    \"scroll\",\n    throttle ? useThrottleFn(onScrollHandler, throttle, true, false) : onScrollHandler,\n    eventListenerOptions\n  );\n  tryOnMounted(() => {\n    try {\n      const _element = toValue(element);\n      if (!_element)\n        return;\n      setArrivedState(_element);\n    } catch (e) {\n      onError(e);\n    }\n  });\n  useEventListener(\n    element,\n    \"scrollend\",\n    onScrollEnd,\n    eventListenerOptions\n  );\n  return {\n    x,\n    y,\n    isScrolling,\n    arrivedState,\n    directions,\n    measure() {\n      const _element = toValue(element);\n      if (window && _element)\n        setArrivedState(_element);\n    }\n  };\n}\n\nfunction useInfiniteScroll(element, onLoadMore, options = {}) {\n  var _a;\n  const {\n    direction = \"bottom\",\n    interval = 100,\n    canLoadMore = () => true\n  } = options;\n  const state = reactive(useScroll(\n    element,\n    {\n      ...options,\n      offset: {\n        [direction]: (_a = options.distance) != null ? _a : 0,\n        ...options.offset\n      }\n    }\n  ));\n  const promise = ref();\n  const isLoading = computed(() => !!promise.value);\n  const observedElement = computed(() => {\n    return resolveElement(toValue(element));\n  });\n  const isElementVisible = useElementVisibility(observedElement);\n  function checkAndLoad() {\n    state.measure();\n    if (!observedElement.value || !isElementVisible.value || !canLoadMore(observedElement.value))\n      return;\n    const { scrollHeight, clientHeight, scrollWidth, clientWidth } = observedElement.value;\n    const isNarrower = direction === \"bottom\" || direction === \"top\" ? scrollHeight <= clientHeight : scrollWidth <= clientWidth;\n    if (state.arrivedState[direction] || isNarrower) {\n      if (!promise.value) {\n        promise.value = Promise.all([\n          onLoadMore(state),\n          new Promise((resolve) => setTimeout(resolve, interval))\n        ]).finally(() => {\n          promise.value = null;\n          nextTick(() => checkAndLoad());\n        });\n      }\n    }\n  }\n  const stop = watch(\n    () => [state.arrivedState[direction], isElementVisible.value],\n    checkAndLoad,\n    { immediate: true }\n  );\n  tryOnUnmounted(stop);\n  return {\n    isLoading,\n    reset() {\n      nextTick(() => checkAndLoad());\n    }\n  };\n}\n\nconst vInfiniteScroll = {\n  mounted(el, binding) {\n    if (typeof binding.value === \"function\")\n      useInfiniteScroll(el, binding.value);\n    else\n      useInfiniteScroll(el, ...binding.value);\n  }\n};\n\nconst vIntersectionObserver = {\n  mounted(el, binding) {\n    if (typeof binding.value === \"function\")\n      useIntersectionObserver(el, binding.value);\n    else\n      useIntersectionObserver(el, ...binding.value);\n  }\n};\n\nconst UseMouse = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseMouse\",\n  props: [\"touch\", \"resetOnTouchEnds\", \"initialValue\"],\n  setup(props, { slots }) {\n    const data = reactive(useMouse(props));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseMouseInElement = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseMouseElement\",\n  props: [\"handleOutside\", \"as\"],\n  setup(props, { slots }) {\n    const target = ref();\n    const data = reactive(useMouseInElement(target, props));\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default(data));\n    };\n  }\n});\n\nconst UseMousePressed = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseMousePressed\",\n  props: [\"touch\", \"initialValue\", \"as\"],\n  setup(props, { slots }) {\n    const target = ref();\n    const data = reactive(useMousePressed({ ...props, target }));\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default(data));\n    };\n  }\n});\n\nconst UseNetwork = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseNetwork\",\n  setup(props, { slots }) {\n    const data = reactive(useNetwork());\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseNow = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseNow\",\n  props: [\"interval\"],\n  setup(props, { slots }) {\n    const data = reactive(useNow({ ...props, controls: true }));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseObjectUrl = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseObjectUrl\",\n  props: [\n    \"object\"\n  ],\n  setup(props, { slots }) {\n    const object = toRef(props, \"object\");\n    const url = useObjectUrl(object);\n    return () => {\n      if (slots.default && url.value)\n        return slots.default(url);\n    };\n  }\n});\n\nconst UseOffsetPagination = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseOffsetPagination\",\n  props: [\n    \"total\",\n    \"page\",\n    \"pageSize\",\n    \"onPageChange\",\n    \"onPageSizeChange\",\n    \"onPageCountChange\"\n  ],\n  emits: [\n    \"page-change\",\n    \"page-size-change\",\n    \"page-count-change\"\n  ],\n  setup(props, { slots, emit }) {\n    const data = reactive(useOffsetPagination({\n      ...props,\n      onPageChange(...args) {\n        var _a;\n        (_a = props.onPageChange) == null ? void 0 : _a.call(props, ...args);\n        emit(\"page-change\", ...args);\n      },\n      onPageSizeChange(...args) {\n        var _a;\n        (_a = props.onPageSizeChange) == null ? void 0 : _a.call(props, ...args);\n        emit(\"page-size-change\", ...args);\n      },\n      onPageCountChange(...args) {\n        var _a;\n        (_a = props.onPageCountChange) == null ? void 0 : _a.call(props, ...args);\n        emit(\"page-count-change\", ...args);\n      }\n    }));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseOnline = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseOnline\",\n  setup(props, { slots }) {\n    const data = reactive({\n      isOnline: useOnline()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UsePageLeave = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UsePageLeave\",\n  setup(props, { slots }) {\n    const data = reactive({\n      isLeft: usePageLeave()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UsePointer = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UsePointer\",\n  props: [\n    \"pointerTypes\",\n    \"initialValue\",\n    \"target\"\n  ],\n  setup(props, { slots }) {\n    const el = ref(null);\n    const data = reactive(usePointer({\n      ...props,\n      target: props.target === \"self\" ? el : defaultWindow\n    }));\n    return () => {\n      if (slots.default)\n        return slots.default(data, { ref: el });\n    };\n  }\n});\n\nconst UsePointerLock = /* #__PURE__ */ defineComponent({\n  name: \"UsePointerLock\",\n  props: [\"as\"],\n  setup(props, { slots }) {\n    const target = ref();\n    const data = reactive(usePointerLock(target));\n    return () => {\n      if (slots.default)\n        return h(props.as || \"div\", { ref: target }, slots.default(data));\n    };\n  }\n});\n\nconst UsePreferredColorScheme = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UsePreferredColorScheme\",\n  setup(props, { slots }) {\n    const data = reactive({\n      colorScheme: usePreferredColorScheme()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UsePreferredContrast = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UsePreferredContrast\",\n  setup(props, { slots }) {\n    const data = reactive({\n      contrast: usePreferredContrast()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UsePreferredDark = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UsePreferredDark\",\n  setup(props, { slots }) {\n    const data = reactive({\n      prefersDark: usePreferredDark$1()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UsePreferredLanguages = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UsePreferredLanguages\",\n  setup(props, { slots }) {\n    const data = reactive({\n      languages: usePreferredLanguages()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UsePreferredReducedMotion = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UsePreferredReducedMotion\",\n  setup(props, { slots }) {\n    const data = reactive({\n      motion: usePreferredReducedMotion()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst vResizeObserver = {\n  mounted(el, binding) {\n    if (typeof binding.value === \"function\")\n      useResizeObserver(el, binding.value);\n    else\n      useResizeObserver(el, ...binding.value);\n  }\n};\n\nfunction useMutationObserver(target, callback, options = {}) {\n  const { window = defaultWindow, ...mutationOptions } = options;\n  let observer;\n  const isSupported = useSupported(() => window && \"MutationObserver\" in window);\n  const cleanup = () => {\n    if (observer) {\n      observer.disconnect();\n      observer = void 0;\n    }\n  };\n  const targets = computed(() => {\n    const value = toValue(target);\n    const items = (Array.isArray(value) ? value : [value]).map(unrefElement).filter(notNullish);\n    return new Set(items);\n  });\n  const stopWatch = watch(\n    () => targets.value,\n    (targets2) => {\n      cleanup();\n      if (isSupported.value && targets2.size) {\n        observer = new MutationObserver(callback);\n        targets2.forEach((el) => observer.observe(el, mutationOptions));\n      }\n    },\n    { immediate: true, flush: \"post\" }\n  );\n  const takeRecords = () => {\n    return observer == null ? void 0 : observer.takeRecords();\n  };\n  const stop = () => {\n    stopWatch();\n    cleanup();\n  };\n  tryOnScopeDispose(stop);\n  return {\n    isSupported,\n    stop,\n    takeRecords\n  };\n}\n\nfunction useCssVar(prop, target, options = {}) {\n  const { window = defaultWindow, initialValue, observe = false } = options;\n  const variable = ref(initialValue);\n  const elRef = computed(() => {\n    var _a;\n    return unrefElement(target) || ((_a = window == null ? void 0 : window.document) == null ? void 0 : _a.documentElement);\n  });\n  function updateCssVar() {\n    var _a;\n    const key = toValue(prop);\n    const el = toValue(elRef);\n    if (el && window && key) {\n      const value = (_a = window.getComputedStyle(el).getPropertyValue(key)) == null ? void 0 : _a.trim();\n      variable.value = value || initialValue;\n    }\n  }\n  if (observe) {\n    useMutationObserver(elRef, updateCssVar, {\n      attributeFilter: [\"style\", \"class\"],\n      window\n    });\n  }\n  watch(\n    [elRef, () => toValue(prop)],\n    (_, old) => {\n      if (old[0] && old[1])\n        old[0].style.removeProperty(old[1]);\n      updateCssVar();\n    },\n    { immediate: true }\n  );\n  watch(\n    variable,\n    (val) => {\n      var _a;\n      const raw_prop = toValue(prop);\n      if (((_a = elRef.value) == null ? void 0 : _a.style) && raw_prop) {\n        if (val == null)\n          elRef.value.style.removeProperty(raw_prop);\n        else\n          elRef.value.style.setProperty(raw_prop, val);\n      }\n    }\n  );\n  return variable;\n}\n\nconst topVarName = \"--vueuse-safe-area-top\";\nconst rightVarName = \"--vueuse-safe-area-right\";\nconst bottomVarName = \"--vueuse-safe-area-bottom\";\nconst leftVarName = \"--vueuse-safe-area-left\";\nfunction useScreenSafeArea() {\n  const top = ref(\"\");\n  const right = ref(\"\");\n  const bottom = ref(\"\");\n  const left = ref(\"\");\n  if (isClient) {\n    const topCssVar = useCssVar(topVarName);\n    const rightCssVar = useCssVar(rightVarName);\n    const bottomCssVar = useCssVar(bottomVarName);\n    const leftCssVar = useCssVar(leftVarName);\n    topCssVar.value = \"env(safe-area-inset-top, 0px)\";\n    rightCssVar.value = \"env(safe-area-inset-right, 0px)\";\n    bottomCssVar.value = \"env(safe-area-inset-bottom, 0px)\";\n    leftCssVar.value = \"env(safe-area-inset-left, 0px)\";\n    update();\n    useEventListener(\"resize\", useDebounceFn(update));\n  }\n  function update() {\n    top.value = getValue(topVarName);\n    right.value = getValue(rightVarName);\n    bottom.value = getValue(bottomVarName);\n    left.value = getValue(leftVarName);\n  }\n  return {\n    top,\n    right,\n    bottom,\n    left,\n    update\n  };\n}\nfunction getValue(position) {\n  return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nconst UseScreenSafeArea = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseScreenSafeArea\",\n  props: {\n    top: Boolean,\n    right: Boolean,\n    bottom: Boolean,\n    left: Boolean\n  },\n  setup(props, { slots }) {\n    const {\n      top,\n      right,\n      bottom,\n      left\n    } = useScreenSafeArea();\n    return () => {\n      if (slots.default) {\n        return h(\"div\", {\n          style: {\n            paddingTop: props.top ? top.value : \"\",\n            paddingRight: props.right ? right.value : \"\",\n            paddingBottom: props.bottom ? bottom.value : \"\",\n            paddingLeft: props.left ? left.value : \"\",\n            boxSizing: \"border-box\",\n            maxHeight: \"100vh\",\n            maxWidth: \"100vw\",\n            overflow: \"auto\"\n          }\n        }, slots.default());\n      }\n    };\n  }\n});\n\nconst vScroll = {\n  mounted(el, binding) {\n    if (typeof binding.value === \"function\") {\n      const handler = binding.value;\n      const state = useScroll(el, {\n        onScroll() {\n          handler(state);\n        },\n        onStop() {\n          handler(state);\n        }\n      });\n    } else {\n      const [handler, options] = binding.value;\n      const state = useScroll(el, {\n        ...options,\n        onScroll(e) {\n          var _a;\n          (_a = options.onScroll) == null ? void 0 : _a.call(options, e);\n          handler(state);\n        },\n        onStop(e) {\n          var _a;\n          (_a = options.onStop) == null ? void 0 : _a.call(options, e);\n          handler(state);\n        }\n      });\n    }\n  }\n};\n\nfunction checkOverflowScroll(ele) {\n  const style = window.getComputedStyle(ele);\n  if (style.overflowX === \"scroll\" || style.overflowY === \"scroll\" || style.overflowX === \"auto\" && ele.clientWidth < ele.scrollWidth || style.overflowY === \"auto\" && ele.clientHeight < ele.scrollHeight) {\n    return true;\n  } else {\n    const parent = ele.parentNode;\n    if (!parent || parent.tagName === \"BODY\")\n      return false;\n    return checkOverflowScroll(parent);\n  }\n}\nfunction preventDefault(rawEvent) {\n  const e = rawEvent || window.event;\n  const _target = e.target;\n  if (checkOverflowScroll(_target))\n    return false;\n  if (e.touches.length > 1)\n    return true;\n  if (e.preventDefault)\n    e.preventDefault();\n  return false;\n}\nconst elInitialOverflow = /* @__PURE__ */ new WeakMap();\nfunction useScrollLock(element, initialState = false) {\n  const isLocked = ref(initialState);\n  let stopTouchMoveListener = null;\n  let initialOverflow = \"\";\n  watch(toRef(element), (el) => {\n    const target = resolveElement(toValue(el));\n    if (target) {\n      const ele = target;\n      if (!elInitialOverflow.get(ele))\n        elInitialOverflow.set(ele, ele.style.overflow);\n      if (ele.style.overflow !== \"hidden\")\n        initialOverflow = ele.style.overflow;\n      if (ele.style.overflow === \"hidden\")\n        return isLocked.value = true;\n      if (isLocked.value)\n        return ele.style.overflow = \"hidden\";\n    }\n  }, {\n    immediate: true\n  });\n  const lock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || isLocked.value)\n      return;\n    if (isIOS) {\n      stopTouchMoveListener = useEventListener(\n        el,\n        \"touchmove\",\n        (e) => {\n          preventDefault(e);\n        },\n        { passive: false }\n      );\n    }\n    el.style.overflow = \"hidden\";\n    isLocked.value = true;\n  };\n  const unlock = () => {\n    const el = resolveElement(toValue(element));\n    if (!el || !isLocked.value)\n      return;\n    if (isIOS)\n      stopTouchMoveListener == null ? void 0 : stopTouchMoveListener();\n    el.style.overflow = initialOverflow;\n    elInitialOverflow.delete(el);\n    isLocked.value = false;\n  };\n  tryOnScopeDispose(unlock);\n  return computed({\n    get() {\n      return isLocked.value;\n    },\n    set(v) {\n      if (v)\n        lock();\n      else unlock();\n    }\n  });\n}\n\nfunction onScrollLock() {\n  let isMounted = false;\n  const state = ref(false);\n  return (el, binding) => {\n    state.value = binding.value;\n    if (isMounted)\n      return;\n    isMounted = true;\n    const isLocked = useScrollLock(el, binding.value);\n    watch(state, (v) => isLocked.value = v);\n  };\n}\nconst vScrollLock = onScrollLock();\n\nconst UseTimeAgo = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseTimeAgo\",\n  props: [\"time\", \"updateInterval\", \"max\", \"fullDateFormatter\", \"messages\", \"showSecond\"],\n  setup(props, { slots }) {\n    const data = reactive(useTimeAgo(() => props.time, { ...props, controls: true }));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseTimestamp = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseTimestamp\",\n  props: [\"immediate\", \"interval\", \"offset\"],\n  setup(props, { slots }) {\n    const data = reactive(useTimestamp({ ...props, controls: true }));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseVirtualList = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseVirtualList\",\n  props: [\n    \"list\",\n    \"options\",\n    \"height\"\n  ],\n  setup(props, { slots, expose }) {\n    const { list: listRef } = toRefs(props);\n    const { list, containerProps, wrapperProps, scrollTo } = useVirtualList(listRef, props.options);\n    expose({ scrollTo });\n    if (containerProps.style && typeof containerProps.style === \"object\" && !Array.isArray(containerProps.style))\n      containerProps.style.height = props.height || \"300px\";\n    return () => h(\"div\", { ...containerProps }, [\n      h(\"div\", { ...wrapperProps.value }, list.value.map((item) => h(\"div\", { style: { overflow: \"hidden\", height: item.height } }, slots.default ? slots.default(item) : \"Please set content!\")))\n    ]);\n  }\n});\n\nconst UseWindowFocus = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseWindowFocus\",\n  setup(props, { slots }) {\n    const data = reactive({\n      focused: useWindowFocus()\n    });\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nconst UseWindowSize = /* @__PURE__ */ /* #__PURE__ */ defineComponent({\n  name: \"UseWindowSize\",\n  props: [\"initialWidth\", \"initialHeight\"],\n  setup(props, { slots }) {\n    const data = reactive(useWindowSize(props));\n    return () => {\n      if (slots.default)\n        return slots.default(data);\n    };\n  }\n});\n\nexport { OnClickOutside, OnLongPress, UseActiveElement, UseBattery, UseBrowserLocation, UseClipboard, UseColorMode, UseDark, UseDeviceMotion, UseDeviceOrientation, UseDevicePixelRatio, UseDevicesList, UseDocumentVisibility, UseDraggable, UseElementBounding, UseElementSize, UseElementVisibility, UseEyeDropper, UseFullscreen, UseGeolocation, UseIdle, UseImage, UseMouse, UseMouseInElement, UseMousePressed, UseNetwork, UseNow, UseObjectUrl, UseOffsetPagination, UseOnline, UsePageLeave, UsePointer, UsePointerLock, UsePreferredColorScheme, UsePreferredContrast, UsePreferredDark, UsePreferredLanguages, UsePreferredReducedMotion, UseScreenSafeArea, UseTimeAgo, UseTimestamp, UseVirtualList, UseWindowFocus, UseWindowSize, vOnClickOutside as VOnClickOutside, vOnLongPress as VOnLongPress, vElementHover, vElementSize, vElementVisibility, vInfiniteScroll, vIntersectionObserver, vOnClickOutside, vOnKeyStroke, vOnLongPress, vResizeObserver, vScroll, vScrollLock };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAM,iBAAiD,gBAAgB;AAAA,EACrE,MAAM;AAAA,EACN,OAAO,CAAC,MAAM,SAAS;AAAA,EACvB,OAAO,CAAC,SAAS;AAAA,EACjB,MAAM,OAAO,EAAE,OAAO,KAAK,GAAG;AAC5B,UAAM,SAAS,IAAI;AACnB,mBAAiB,QAAQ,CAAC,MAAM;AAC9B,WAAK,WAAW,CAAC;AAAA,IACnB,GAAG,MAAM,OAAO;AAChB,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC;AAAA,IAChE;AAAA,EACF;AACF,CAAC;AAED,IAAM,gBAAgB,WAAW,SAAS;AAE1C,SAAS,aAAa,OAAO;AAC3B,MAAI;AACJ,QAAM,QAAQ,QAAQ,KAAK;AAC3B,UAAQ,KAAK,SAAS,OAAO,SAAS,MAAM,QAAQ,OAAO,KAAK;AAClE;AAEA,SAAS,oBAAoB,MAAM;AACjC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,KAAK,CAAC,MAAM,YAAY,MAAM,QAAQ,KAAK,CAAC,CAAC,GAAG;AACzD,KAAC,QAAQ,WAAW,OAAO,IAAI;AAC/B,aAAS;AAAA,EACX,OAAO;AACL,KAAC,QAAQ,QAAQ,WAAW,OAAO,IAAI;AAAA,EACzC;AACA,MAAI,CAAC;AACH,WAAO;AACT,MAAI,CAAC,MAAM,QAAQ,MAAM;AACvB,aAAS,CAAC,MAAM;AAClB,MAAI,CAAC,MAAM,QAAQ,SAAS;AAC1B,gBAAY,CAAC,SAAS;AACxB,QAAM,WAAW,CAAC;AAClB,QAAM,UAAU,MAAM;AACpB,aAAS,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC7B,aAAS,SAAS;AAAA,EACpB;AACA,QAAM,WAAW,CAAC,IAAI,OAAO,UAAU,aAAa;AAClD,OAAG,iBAAiB,OAAO,UAAU,QAAQ;AAC7C,WAAO,MAAM,GAAG,oBAAoB,OAAO,UAAU,QAAQ;AAAA,EAC/D;AACA,QAAM,YAAY;AAAA,IAChB,MAAM,CAAC,aAAa,MAAM,GAAG,QAAQ,OAAO,CAAC;AAAA,IAC7C,CAAC,CAAC,IAAI,QAAQ,MAAM;AAClB,cAAQ;AACR,UAAI,CAAC;AACH;AACF,YAAM,eAAe,SAAS,QAAQ,IAAI,EAAE,GAAG,SAAS,IAAI;AAC5D,eAAS;AAAA,QACP,GAAG,OAAO,QAAQ,CAAC,UAAU;AAC3B,iBAAO,UAAU,IAAI,CAAC,aAAa,SAAS,IAAI,OAAO,UAAU,YAAY,CAAC;AAAA,QAChF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,EAAE,WAAW,MAAM,OAAO,OAAO;AAAA,EACnC;AACA,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AACT;AAEA,IAAI,iBAAiB;AACrB,SAASA,gBAAe,QAAQ,SAAS,UAAU,CAAC,GAAG;AACrD,QAAM,EAAE,QAAAC,UAAS,eAAe,SAAS,CAAC,GAAG,UAAU,MAAM,eAAe,MAAM,IAAI;AACtF,MAAI,CAACA;AACH,WAAO;AACT,MAAI,SAAS,CAAC,gBAAgB;AAC5B,qBAAiB;AACjB,UAAM,KAAKA,QAAO,SAAS,KAAK,QAAQ,EAAE,QAAQ,CAAC,OAAO,GAAG,iBAAiB,SAAS,IAAI,CAAC;AAC5F,IAAAA,QAAO,SAAS,gBAAgB,iBAAiB,SAAS,IAAI;AAAA,EAChE;AACA,MAAI,eAAe;AACnB,QAAM,eAAe,CAAC,UAAU;AAC9B,WAAO,QAAQ,MAAM,EAAE,KAAK,CAAC,YAAY;AACvC,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO,MAAM,KAAKA,QAAO,SAAS,iBAAiB,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,EAAE,CAAC;AAAA,MACpI,OAAO;AACL,cAAM,KAAK,aAAa,OAAO;AAC/B,eAAO,OAAO,MAAM,WAAW,MAAM,MAAM,aAAa,EAAE,SAAS,EAAE;AAAA,MACvE;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,iBAAiB,SAAS;AACjC,UAAM,KAAK,QAAQ,OAAO;AAC1B,WAAO,MAAM,GAAG,EAAE,QAAQ,cAAc;AAAA,EAC1C;AACA,WAAS,mBAAmB,SAAS,OAAO;AAC1C,UAAM,KAAK,QAAQ,OAAO;AAC1B,UAAM,WAAW,GAAG,EAAE,WAAW,GAAG,EAAE,QAAQ;AAC9C,QAAI,YAAY,QAAQ,CAAC,MAAM,QAAQ,QAAQ;AAC7C,aAAO;AACT,WAAO,SAAS,KAAK,CAAC,UAAU,MAAM,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,MAAM,EAAE,CAAC;AAAA,EACtG;AACA,QAAM,WAAW,CAAC,UAAU;AAC1B,UAAM,KAAK,aAAa,MAAM;AAC9B,QAAI,MAAM,UAAU;AAClB;AACF,QAAI,EAAE,cAAc,YAAY,iBAAiB,MAAM,KAAK,mBAAmB,QAAQ,KAAK;AAC1F;AACF,QAAI,CAAC,MAAM,OAAO,MAAM,UAAU,MAAM,aAAa,EAAE,SAAS,EAAE;AAChE;AACF,QAAI,MAAM,WAAW;AACnB,qBAAe,CAAC,aAAa,KAAK;AACpC,QAAI,CAAC,cAAc;AACjB,qBAAe;AACf;AAAA,IACF;AACA,YAAQ,KAAK;AAAA,EACf;AACA,MAAI,oBAAoB;AACxB,QAAM,UAAU;AAAA,IACd,iBAAiBA,SAAQ,SAAS,CAAC,UAAU;AAC3C,UAAI,CAAC,mBAAmB;AACtB,4BAAoB;AACpB,mBAAW,MAAM;AACf,8BAAoB;AAAA,QACtB,GAAG,CAAC;AACJ,iBAAS,KAAK;AAAA,MAChB;AAAA,IACF,GAAG,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,IAC7B,iBAAiBA,SAAQ,eAAe,CAAC,MAAM;AAC7C,YAAM,KAAK,aAAa,MAAM;AAC9B,qBAAe,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE;AAAA,IAC3E,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,IACpB,gBAAgB,iBAAiBA,SAAQ,QAAQ,CAAC,UAAU;AAC1D,iBAAW,MAAM;AACf,YAAI;AACJ,cAAM,KAAK,aAAa,MAAM;AAC9B,cAAM,KAAKA,QAAO,SAAS,kBAAkB,OAAO,SAAS,GAAG,aAAa,YAAY,EAAE,MAAM,OAAO,SAAS,GAAG,SAASA,QAAO,SAAS,aAAa,IAAI;AAC5J,kBAAQ,KAAK;AAAA,QACf;AAAA,MACF,GAAG,CAAC;AAAA,IACN,CAAC;AAAA,EACH,EAAE,OAAO,OAAO;AAChB,QAAM,OAAO,MAAM,QAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC/C,SAAO;AACT;AAEA,IAAM,kBAAkB;AAAA,EACtB,QAAQ,IAAI,SAAS;AACnB,UAAM,UAAU,CAAC,QAAQ,UAAU;AACnC,QAAI,OAAO,QAAQ,UAAU,YAAY;AACvC,SAAG,wBAAwBD,gBAAe,IAAI,QAAQ,OAAO,EAAE,QAAQ,CAAC;AAAA,IAC1E,OAAO;AACL,YAAM,CAAC,SAAS,OAAO,IAAI,QAAQ;AACnC,SAAG,wBAAwBA,gBAAe,IAAI,SAAS,OAAO,OAAO,EAAE,QAAQ,GAAG,OAAO,CAAC;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,UAAU,IAAI;AACZ,OAAG,sBAAsB;AAAA,EAC3B;AACF;AAEA,SAAS,mBAAmB,WAAW;AACrC,MAAI,OAAO,cAAc;AACvB,WAAO;AAAA,WACA,OAAO,cAAc;AAC5B,WAAO,CAAC,UAAU,MAAM,QAAQ;AAAA,WACzB,MAAM,QAAQ,SAAS;AAC9B,WAAO,CAAC,UAAU,UAAU,SAAS,MAAM,GAAG;AAChD,SAAO,MAAM;AACf;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,CAAC;AACf,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,KAAK,CAAC;AACZ,cAAU,KAAK,CAAC;AAChB,cAAU,KAAK,CAAC;AAAA,EAClB,WAAW,KAAK,WAAW,GAAG;AAC5B,QAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,YAAM;AACN,gBAAU,KAAK,CAAC;AAChB,gBAAU,KAAK,CAAC;AAAA,IAClB,OAAO;AACL,YAAM,KAAK,CAAC;AACZ,gBAAU,KAAK,CAAC;AAAA,IAClB;AAAA,EACF,OAAO;AACL,UAAM;AACN,cAAU,KAAK,CAAC;AAAA,EAClB;AACA,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,SAAS;AAAA,EACX,IAAI;AACJ,QAAM,YAAY,mBAAmB,GAAG;AACxC,QAAM,WAAW,CAAC,MAAM;AACtB,QAAI,EAAE,UAAU,QAAQ,MAAM;AAC5B;AACF,QAAI,UAAU,CAAC;AACb,cAAQ,CAAC;AAAA,EACb;AACA,SAAO,iBAAiB,QAAQ,WAAW,UAAU,OAAO;AAC9D;AAEA,IAAM,eAAe;AAAA,EACnB,QAAQ,IAAI,SAAS;AACnB,QAAI,IAAI;AACR,UAAM,QAAQ,MAAM,KAAK,QAAQ,QAAQ,OAAO,SAAS,GAAG,MAAM,GAAG,MAAM,OAAO,KAAK;AACvF,QAAI,OAAO,QAAQ,UAAU,YAAY;AACvC,kBAAY,MAAM,QAAQ,OAAO;AAAA,QAC/B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,OAAO;AACL,YAAM,CAAC,SAAS,OAAO,IAAI,QAAQ;AACnC,kBAAY,MAAM,SAAS;AAAA,QACzB,QAAQ;AAAA,QACR,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAC1B,SAAS,YAAY,QAAQ,SAAS,SAAS;AAC7C,MAAI,IAAI;AACR,QAAM,aAAa,SAAS,MAAM,aAAa,MAAM,CAAC;AACtD,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,iBAAiB;AACrB,WAAS,QAAQ;AACf,QAAI,SAAS;AACX,mBAAa,OAAO;AACpB,gBAAU;AAAA,IACZ;AACA,eAAW;AACX,qBAAiB;AACjB,qBAAiB;AAAA,EACnB;AACA,WAAS,UAAU,IAAI;AACrB,QAAI,KAAK,KAAK;AACd,UAAM,CAAC,iBAAiB,WAAW,eAAe,IAAI,CAAC,gBAAgB,UAAU,cAAc;AAC/F,UAAM;AACN,QAAI,EAAE,WAAW,OAAO,SAAS,QAAQ,cAAc,CAAC,aAAa,CAAC;AACpE;AACF,UAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI,SAAS,GAAG,WAAW,WAAW;AACjH;AACF,SAAK,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI;AAC9E,SAAG,eAAe;AACpB,SAAK,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAC5E,SAAG,gBAAgB;AACrB,UAAM,KAAK,GAAG,IAAI,UAAU;AAC5B,UAAM,KAAK,GAAG,IAAI,UAAU;AAC5B,UAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC5C,YAAQ,UAAU,GAAG,YAAY,iBAAiB,UAAU,eAAe;AAAA,EAC7E;AACA,WAAS,OAAO,IAAI;AAClB,QAAI,KAAK,KAAK,IAAI;AAClB,UAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI,SAAS,GAAG,WAAW,WAAW;AACjH;AACF,UAAM;AACN,SAAK,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI;AAC9E,SAAG,eAAe;AACpB,SAAK,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAC5E,SAAG,gBAAgB;AACrB,eAAW;AAAA,MACT,GAAG,GAAG;AAAA,MACN,GAAG,GAAG;AAAA,IACR;AACA,qBAAiB,GAAG;AACpB,cAAU;AAAA,MACR,MAAM;AACJ,yBAAiB;AACjB,gBAAQ,EAAE;AAAA,MACZ;AAAA,OACC,KAAK,WAAW,OAAO,SAAS,QAAQ,UAAU,OAAO,KAAK;AAAA,IACjE;AAAA,EACF;AACA,WAAS,OAAO,IAAI;AAClB,QAAI,KAAK,KAAK,IAAI;AAClB,UAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI,SAAS,GAAG,WAAW,WAAW;AACjH;AACF,QAAI,CAAC,aAAa,WAAW,OAAO,SAAS,QAAQ,uBAAuB;AAC1E;AACF,SAAK,MAAM,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,IAAI;AAC9E,SAAG,eAAe;AACpB,SAAK,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAC5E,SAAG,gBAAgB;AACrB,UAAM,KAAK,GAAG,IAAI,SAAS;AAC3B,UAAM,KAAK,GAAG,IAAI,SAAS;AAC3B,UAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC5C,QAAI,cAAc,KAAK,WAAW,OAAO,SAAS,QAAQ,sBAAsB,OAAO,KAAK;AAC1F,YAAM;AAAA,EACV;AACA,QAAM,kBAAkB;AAAA,IACtB,UAAU,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAAA,IACnF,OAAO,KAAK,WAAW,OAAO,SAAS,QAAQ,cAAc,OAAO,SAAS,GAAG;AAAA,EAClF;AACA,QAAM,UAAU;AAAA,IACd,iBAAiB,YAAY,eAAe,QAAQ,eAAe;AAAA,IACnE,iBAAiB,YAAY,eAAe,QAAQ,eAAe;AAAA,IACnE,iBAAiB,YAAY,CAAC,aAAa,cAAc,GAAG,WAAW,eAAe;AAAA,EACxF;AACA,QAAM,OAAO,MAAM,QAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC/C,SAAO;AACT;AAEA,IAAM,cAA8C,gBAAgB;AAAA,EAClE,MAAM;AAAA,EACN,OAAO,CAAC,MAAM,SAAS;AAAA,EACvB,OAAO,CAAC,SAAS;AAAA,EACjB,MAAM,OAAO,EAAE,OAAO,KAAK,GAAG;AAC5B,UAAM,SAAS,IAAI;AACnB;AAAA,MACE;AAAA,MACA,CAAC,MAAM;AACL,aAAK,WAAW,CAAC;AAAA,MACnB;AAAA,MACA,MAAM;AAAA,IACR;AACA,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,CAAC;AAAA,IAChE;AAAA,EACF;AACF,CAAC;AAED,IAAM,eAAe;AAAA,EACnB,QAAQ,IAAI,SAAS;AACnB,QAAI,OAAO,QAAQ,UAAU;AAC3B,kBAAY,IAAI,QAAQ,OAAO,EAAE,WAAW,QAAQ,UAAU,CAAC;AAAA;AAE/D,kBAAY,IAAI,GAAG,QAAQ,KAAK;AAAA,EACpC;AACF;AAEA,IAAM,mBAAmD,gBAAgB;AAAA,EACvE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,SAAS,iBAAiB;AAAA,IAC5B,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAA6C,gBAAgB;AAAA,EACjE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,WAAW,KAAK,CAAC;AACvC,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,qBAAqD,gBAAgB;AAAA,EACzE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,mBAAmB,CAAC;AAC1C,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,eAA+C,gBAAgB;AAAA,EACnE,MAAM;AAAA,EACN,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,aAAa,KAAK,CAAC;AACzC,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,YAAY,OAAO,SAAS,GAAG,KAAK,OAAO,IAAI;AAAA,IACpE;AAAA,EACF;AACF,CAAC;AAED,IAAM,UAAU,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AACzL,IAAM,YAAY;AAClB,IAAM,WAA2B,YAAY;AAC7C,SAAS,cAAc;AACrB,MAAI,EAAE,aAAa;AACjB,YAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC9C,SAAO,QAAQ,SAAS;AAC1B;AACA,SAAS,cAAc,KAAK,UAAU;AACpC,SAAO,SAAS,GAAG,KAAK;AAC1B;AAEA,SAAS,aAAa;AACpB,QAAM,YAAY,IAAI,KAAK;AAC3B,QAAM,WAAW,mBAAmB;AACpC,MAAI,UAAU;AACZ,cAAU,MAAM;AACd,gBAAU,QAAQ;AAAA,IACpB,GAAG,QAAQ;AAAA,EACb;AACA,SAAO;AACT;AAEA,SAAS,aAAa,UAAU;AAC9B,QAAM,YAAY,WAAW;AAC7B,SAAO,SAAS,MAAM;AACpB,cAAU;AACV,WAAO,QAAQ,SAAS,CAAC;AAAA,EAC3B,CAAC;AACH;AAEA,SAAS,cAAc,OAAO,UAAU,CAAC,GAAG;AAC1C,QAAM,EAAE,QAAAC,UAAS,cAAc,IAAI;AACnC,QAAM,cAAc,aAAa,MAAMA,WAAU,gBAAgBA,WAAU,OAAOA,QAAO,eAAe,UAAU;AAClH,MAAI;AACJ,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,UAAU,CAAC,UAAU;AACzB,YAAQ,QAAQ,MAAM;AAAA,EACxB;AACA,QAAM,UAAU,MAAM;AACpB,QAAI,CAAC;AACH;AACF,QAAI,yBAAyB;AAC3B,iBAAW,oBAAoB,UAAU,OAAO;AAAA;AAEhD,iBAAW,eAAe,OAAO;AAAA,EACrC;AACA,QAAM,YAAY,YAAY,MAAM;AAClC,QAAI,CAAC,YAAY;AACf;AACF,YAAQ;AACR,iBAAaA,QAAO,WAAW,QAAQ,KAAK,CAAC;AAC7C,QAAI,sBAAsB;AACxB,iBAAW,iBAAiB,UAAU,OAAO;AAAA;AAE7C,iBAAW,YAAY,OAAO;AAChC,YAAQ,QAAQ,WAAW;AAAA,EAC7B,CAAC;AACD,oBAAkB,MAAM;AACtB,cAAU;AACV,YAAQ;AACR,iBAAa;AAAA,EACf,CAAC;AACD,SAAO;AACT;AAEA,SAASC,kBAAiB,SAAS;AACjC,SAAO,cAAc,gCAAgC,OAAO;AAC9D;AAEA,SAAS,oBAAoB,SAAS;AACpC,SAAO,WAAW,OAAO,QAAQ,mBAAmB,MAAM,QAAQ,mBAAmB,MAAM,QAAQ,mBAAmB,OAAO,SAAS,OAAO,YAAY,YAAY,YAAY,OAAO,YAAY,WAAW,WAAW,OAAO,YAAY,WAAW,WAAW,CAAC,OAAO,MAAM,OAAO,IAAI,WAAW;AACzS;AAEA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,IACP,MAAM,CAAC,MAAM,MAAM;AAAA,IACnB,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;AAAA,IACzB,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,EAChC;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM,OAAO,WAAW,CAAC;AAAA,IAChC,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,QAAQ;AAAA,IACN,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,MAAM,OAAO,CAAC;AAAA,EACxB;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,IAClC,OAAO,CAAC,MAAM,KAAK,UAAU,MAAM,KAAK,EAAE,QAAQ,CAAC,CAAC;AAAA,EACtD;AAAA,EACA,KAAK;AAAA,IACH,MAAM,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,IAClC,OAAO,CAAC,MAAM,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC;AAAA,EAC5C;AAAA,EACA,MAAM;AAAA,IACJ,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC;AAAA,IACvB,OAAO,CAAC,MAAM,EAAE,YAAY;AAAA,EAC9B;AACF;AACA,IAAM,yBAAyB;AAC/B,SAASC,YAAW,KAAK,UAAU,SAAS,UAAU,CAAC,GAAG;AACxD,MAAI;AACJ,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,yBAAyB;AAAA,IACzB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB;AAAA,IACA,QAAAF,UAAS;AAAA,IACT;AAAA,IACA,UAAU,CAAC,MAAM;AACf,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,UAAU,aAAa,KAAK,OAAO,aAAa,aAAa,SAAS,IAAI,QAAQ;AAChG,MAAI,CAAC,SAAS;AACZ,QAAI;AACF,gBAAU,cAAc,qBAAqB,MAAM;AACjD,YAAI;AACJ,gBAAQ,MAAM,kBAAkB,OAAO,SAAS,IAAI;AAAA,MACtD,CAAC,EAAE;AAAA,IACL,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,MAAI,CAAC;AACH,WAAO;AACT,QAAM,UAAU,QAAQ,QAAQ;AAChC,QAAM,OAAO,oBAAoB,OAAO;AACxC,QAAM,cAAc,KAAK,QAAQ,eAAe,OAAO,KAAK,mBAAmB,IAAI;AACnF,QAAM,EAAE,OAAO,YAAY,QAAQ,YAAY,IAAI;AAAA,IACjD;AAAA,IACA,MAAM,MAAM,KAAK,KAAK;AAAA,IACtB,EAAE,OAAO,MAAM,YAAY;AAAA,EAC7B;AACA,MAAIA,WAAU,wBAAwB;AACpC,iBAAa,MAAM;AACjB,UAAI,mBAAmB;AACrB,yBAAiBA,SAAQ,WAAW,MAAM;AAAA;AAE1C,yBAAiBA,SAAQ,wBAAwB,qBAAqB;AACxE,UAAI;AACF,eAAO;AAAA,IACX,CAAC;AAAA,EACH;AACA,MAAI,CAAC;AACH,WAAO;AACT,WAAS,mBAAmB,UAAU,UAAU;AAC9C,QAAIA,SAAQ;AACV,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,MACf;AACA,MAAAA,QAAO,cAAc,mBAAmB,UAAU,IAAI,aAAa,WAAW,OAAO,IAAI,IAAI,YAAY,wBAAwB;AAAA,QAC/H,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACA,WAAS,MAAM,GAAG;AAChB,QAAI;AACF,YAAM,WAAW,QAAQ,QAAQ,GAAG;AACpC,UAAI,KAAK,MAAM;AACb,2BAAmB,UAAU,IAAI;AACjC,gBAAQ,WAAW,GAAG;AAAA,MACxB,OAAO;AACL,cAAM,aAAa,WAAW,MAAM,CAAC;AACrC,YAAI,aAAa,YAAY;AAC3B,kBAAQ,QAAQ,KAAK,UAAU;AAC/B,6BAAmB,UAAU,UAAU;AAAA,QACzC;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AACA,WAAS,KAAK,OAAO;AACnB,UAAM,WAAW,QAAQ,MAAM,WAAW,QAAQ,QAAQ,GAAG;AAC7D,QAAI,YAAY,MAAM;AACpB,UAAI,iBAAiB,WAAW;AAC9B,gBAAQ,QAAQ,KAAK,WAAW,MAAM,OAAO,CAAC;AAChD,aAAO;AAAA,IACT,WAAW,CAAC,SAAS,eAAe;AAClC,YAAM,QAAQ,WAAW,KAAK,QAAQ;AACtC,UAAI,OAAO,kBAAkB;AAC3B,eAAO,cAAc,OAAO,OAAO;AAAA,eAC5B,SAAS,YAAY,CAAC,MAAM,QAAQ,KAAK;AAChD,eAAO,EAAE,GAAG,SAAS,GAAG,MAAM;AAChC,aAAO;AAAA,IACT,WAAW,OAAO,aAAa,UAAU;AACvC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,WAAW,KAAK,QAAQ;AAAA,IACjC;AAAA,EACF;AACA,WAAS,OAAO,OAAO;AACrB,QAAI,SAAS,MAAM,gBAAgB;AACjC;AACF,QAAI,SAAS,MAAM,OAAO,MAAM;AAC9B,WAAK,QAAQ;AACb;AAAA,IACF;AACA,QAAI,SAAS,MAAM,QAAQ;AACzB;AACF,eAAW;AACX,QAAI;AACF,WAAK,SAAS,OAAO,SAAS,MAAM,cAAc,WAAW,MAAM,KAAK,KAAK;AAC3E,aAAK,QAAQ,KAAK,KAAK;AAAA,IAC3B,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX,UAAE;AACA,UAAI;AACF,iBAAS,WAAW;AAAA;AAEpB,oBAAY;AAAA,IAChB;AAAA,EACF;AACA,WAAS,sBAAsB,OAAO;AACpC,WAAO,MAAM,MAAM;AAAA,EACrB;AACA,SAAO;AACT;AAEA,IAAM,oBAAoB;AAC1B,SAAS,aAAa,UAAU,CAAC,GAAG;AAClC,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,QAAAA,UAAS;AAAA,IACT;AAAA,IACA,aAAa;AAAA,IACb,yBAAyB;AAAA,IACzB;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,EACtB,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,GAAG,QAAQ,SAAS,CAAC;AAAA,EACvB;AACA,QAAM,gBAAgBC,kBAAiB,EAAE,QAAAD,QAAO,CAAC;AACjD,QAAM,SAAS,SAAS,MAAM,cAAc,QAAQ,SAAS,OAAO;AACpE,QAAM,QAAQ,eAAe,cAAc,OAAO,MAAM,YAAY,IAAIE,YAAW,YAAY,cAAc,SAAS,EAAE,QAAAF,SAAQ,uBAAuB,CAAC;AACxJ,QAAM,QAAQ,SAAS,MAAM,MAAM,UAAU,SAAS,OAAO,QAAQ,MAAM,KAAK;AAChF,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA,CAAC,WAAW,YAAY,UAAU;AAChC,YAAM,KAAK,OAAO,cAAc,WAAWA,WAAU,OAAO,SAASA,QAAO,SAAS,cAAc,SAAS,IAAI,aAAa,SAAS;AACtI,UAAI,CAAC;AACH;AACF,YAAM,eAA+B,oBAAI,IAAI;AAC7C,YAAM,kBAAkC,oBAAI,IAAI;AAChD,UAAI,oBAAoB;AACxB,UAAI,eAAe,SAAS;AAC1B,cAAM,UAAU,MAAM,MAAM,KAAK;AACjC,eAAO,OAAO,KAAK,EAAE,QAAQ,CAAC,OAAO,KAAK,IAAI,MAAM,KAAK,CAAC,EAAE,OAAO,OAAO,EAAE,QAAQ,CAAC,MAAM;AACzF,cAAI,QAAQ,SAAS,CAAC;AACpB,yBAAa,IAAI,CAAC;AAAA;AAElB,4BAAgB,IAAI,CAAC;AAAA,QACzB,CAAC;AAAA,MACH,OAAO;AACL,4BAAoB,EAAE,KAAK,YAAY,MAAM;AAAA,MAC/C;AACA,UAAI,aAAa,SAAS,KAAK,gBAAgB,SAAS,KAAK,sBAAsB;AACjF;AACF,UAAI;AACJ,UAAI,mBAAmB;AACrB,gBAAQA,QAAO,SAAS,cAAc,OAAO;AAC7C,cAAM,YAAY,SAAS,eAAe,iBAAiB,CAAC;AAC5D,QAAAA,QAAO,SAAS,KAAK,YAAY,KAAK;AAAA,MACxC;AACA,iBAAW,KAAK,cAAc;AAC5B,WAAG,UAAU,IAAI,CAAC;AAAA,MACpB;AACA,iBAAW,KAAK,iBAAiB;AAC/B,WAAG,UAAU,OAAO,CAAC;AAAA,MACvB;AACA,UAAI,mBAAmB;AACrB,WAAG,aAAa,kBAAkB,KAAK,kBAAkB,KAAK;AAAA,MAChE;AACA,UAAI,mBAAmB;AACrB,QAAAA,QAAO,iBAAiB,KAAK,EAAE;AAC/B,iBAAS,KAAK,YAAY,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,WAAS,iBAAiB,MAAM;AAC9B,QAAI;AACJ,oBAAgB,UAAU,YAAY,KAAK,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;AAAA,EAC7E;AACA,WAAS,UAAU,MAAM;AACvB,QAAI,QAAQ;AACV,cAAQ,UAAU,MAAM,gBAAgB;AAAA;AAExC,uBAAiB,IAAI;AAAA,EACzB;AACA,QAAM,OAAO,WAAW,EAAE,OAAO,QAAQ,WAAW,KAAK,CAAC;AAC1D,eAAa,MAAM,UAAU,MAAM,KAAK,CAAC;AACzC,QAAM,OAAO,SAAS;AAAA,IACpB,MAAM;AACJ,aAAO,WAAW,MAAM,QAAQ,MAAM;AAAA,IACxC;AAAA,IACA,IAAI,GAAG;AACL,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,MAAM,EAAE,OAAO,QAAQ,MAAM,CAAC;AACrD;AAEA,IAAM,eAA+C,gBAAgB;AAAA,EACnE,MAAM;AAAA,EACN,OAAO,CAAC,YAAY,aAAa,SAAS,aAAa,cAAc,WAAW,UAAU;AAAA,EAC1F,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,aAAa,KAAK;AAC/B,UAAM,OAAO,SAAS;AAAA,MACpB;AAAA,MACA,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,IACd,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,UAA0C,gBAAgB;AAAA,EAC9D,MAAM;AAAA,EACN,OAAO,CAAC,YAAY,aAAa,aAAa,cAAc,aAAa,cAAc,SAAS;AAAA,EAChG,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,QAAQ,KAAK;AAC5B,UAAM,OAAO,SAAS;AAAA,MACpB;AAAA,MACA,YAAY,UAAU,MAAM;AAAA,IAC9B,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,kBAAkD,gBAAgB;AAAA,EACtE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,gBAAgB,CAAC;AACvC,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,uBAAuD,gBAAgB;AAAA,EAC3E,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,qBAAqB,CAAC;AAC5C,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,sBAAsD,gBAAgB;AAAA,EAC1E,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,YAAY,oBAAoB;AAAA,IAClC,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,iBAAiD,gBAAgB;AAAA,EACrE,MAAM;AAAA,EACN,OAAO,CAAC,aAAa,sBAAsB,aAAa;AAAA,EACxD,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,eAAe,KAAK,CAAC;AAC3C,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,wBAAwD,gBAAgB;AAAA,EAC5E,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,YAAY,sBAAsB;AAAA,IACpC,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,eAA+C,gBAAgB;AAAA,EACnE,MAAM;AAAA,EACN,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,IAAI;AACnB,UAAM,SAAS,SAAS,MAAM;AAC5B,UAAI;AACJ,cAAQ,KAAK,MAAM,WAAW,OAAO,KAAK,OAAO;AAAA,IACnD,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACtC,UAAI;AACJ,cAAQ,KAAK,MAAM,qBAAqB,OAAO,KAAK;AAAA,IACtD,CAAC;AACD,UAAM,WAAW,SAAS,MAAM,CAAC,CAAC,MAAM,QAAQ;AAChD,UAAM,eAAe,MAAM,cAAc;AAAA,MACvC,MAAM;AAAA,MACN,QAAQ,MAAM,YAAY,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MAC5C,WAAa,MAAM,gBAAgB,YAAY,iBAAiB,eAAe;AAAA,IACjF;AACA,UAAM,eAAe,gBAAgB,MAAM,gBAAgB,EAAE,GAAG,GAAG,GAAG,EAAE;AACxE,UAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,UAAI;AACJ,OAAC,KAAK,MAAM,UAAU,OAAO,SAAS,GAAG,KAAK,OAAO,UAAU,KAAK;AACpE,UAAI,CAAC;AACH;AACF,mBAAa,MAAM,IAAI,SAAS;AAChC,mBAAa,MAAM,IAAI,SAAS;AAAA,IAClC;AACA,UAAM,OAAO,SAAS,aAAa,QAAQ;AAAA,MACzC,GAAG;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AACF,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,QAAQ,OAAO,qBAAqB,KAAK,KAAK,GAAG,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,IAC9G;AAAA,EACF;AACF,CAAC;AAED,IAAM,qBAAqD,gBAAgB;AAAA,EACzE,MAAM;AAAA,EACN,OAAO,CAAC,OAAO,IAAI;AAAA,EACnB,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,SAAS,mBAAmB,MAAM,CAAC;AAChD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,IACpE;AAAA,EACF;AACF,CAAC;AAED,SAAS,gBAAgB,IAAI,UAAU,CAAC,GAAG;AACzC,QAAM;AAAA,IACJ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,QAAAA,UAAS;AAAA,EACX,IAAI;AACJ,QAAM,YAAY,IAAI,KAAK;AAC3B,MAAI;AACJ,QAAM,SAAS,CAAC,aAAa;AAC3B,UAAM,QAAQ,WAAW,aAAa;AACtC,QAAI,OAAO;AACT,mBAAa,KAAK;AAClB,cAAQ;AAAA,IACV;AACA,QAAI;AACF,cAAQ,WAAW,MAAM,UAAU,QAAQ,UAAU,KAAK;AAAA;AAE1D,gBAAU,QAAQ;AAAA,EACtB;AACA,MAAI,CAACA;AACH,WAAO;AACT,mBAAiB,IAAI,cAAc,MAAM,OAAO,IAAI,GAAG,EAAE,SAAS,KAAK,CAAC;AACxE,mBAAiB,IAAI,cAAc,MAAM,OAAO,KAAK,GAAG,EAAE,SAAS,KAAK,CAAC;AACzE,SAAO;AACT;AAEA,IAAM,gBAAgB;AAAA,EACpB,QAAQ,IAAI,SAAS;AACnB,UAAM,QAAQ,QAAQ;AACtB,QAAI,OAAO,UAAU,YAAY;AAC/B,YAAM,YAAY,gBAAgB,EAAE;AACpC,YAAM,WAAW,CAAC,MAAM,MAAM,CAAC,CAAC;AAAA,IAClC,OAAO;AACL,YAAM,CAAC,SAAS,OAAO,IAAI;AAC3B,YAAM,YAAY,gBAAgB,IAAI,OAAO;AAC7C,YAAM,WAAW,CAAC,MAAM,QAAQ,CAAC,CAAC;AAAA,IACpC;AAAA,EACF;AACF;AAEA,IAAM,iBAAiD,gBAAgB;AAAA,EACrE,MAAM;AAAA,EACN,OAAO,CAAC,SAAS,UAAU,OAAO,IAAI;AAAA,EACtC,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,SAAS,eAAiB,QAAQ,EAAE,OAAO,MAAM,OAAO,QAAQ,MAAM,OAAO,GAAG,EAAE,KAAK,MAAM,IAAI,CAAC,CAAC;AAChH,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,IACpE;AAAA,EACF;AACF,CAAC;AAED,SAAS,kBAAkB,QAAQ,UAAU,UAAU,CAAC,GAAG;AACzD,QAAM,EAAE,QAAAA,UAAS,eAAe,GAAG,gBAAgB,IAAI;AACvD,MAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,oBAAoBA,OAAM;AAC3E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,WAAW,QAAQ,MAAM;AAC/B,WAAO,MAAM,QAAQ,QAAQ,IAAI,SAAS,IAAI,CAAC,OAAO,aAAa,EAAE,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC;AAAA,EACnG,CAAC;AACD,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,CAAC,QAAQ;AACP,cAAQ;AACR,UAAI,YAAY,SAASA,SAAQ;AAC/B,mBAAW,IAAI,eAAe,QAAQ;AACtC,mBAAW,OAAO,KAAK;AACrB,cAAI;AACF,qBAAS,QAAQ,KAAK,eAAe;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,IACA,EAAE,WAAW,MAAM,OAAO,OAAO;AAAA,EACnC;AACA,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AAAA,EACZ;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAASG,gBAAe,QAAQ,cAAc,EAAE,OAAO,GAAG,QAAQ,EAAE,GAAG,UAAU,CAAC,GAAG;AACnF,QAAM,EAAE,QAAAH,UAAS,eAAe,MAAM,cAAc,IAAI;AACxD,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAI,IAAI;AACR,YAAQ,MAAM,KAAK,aAAa,MAAM,MAAM,OAAO,SAAS,GAAG,iBAAiB,OAAO,SAAS,GAAG,SAAS,KAAK;AAAA,EACnH,CAAC;AACD,QAAM,QAAQ,IAAI,YAAY,KAAK;AACnC,QAAM,SAAS,IAAI,YAAY,MAAM;AACrC,QAAM,EAAE,MAAM,MAAM,IAAI;AAAA,IACtB;AAAA,IACA,CAAC,CAAC,KAAK,MAAM;AACX,YAAM,UAAU,QAAQ,eAAe,MAAM,gBAAgB,QAAQ,gBAAgB,MAAM,iBAAiB,MAAM;AAClH,UAAIA,WAAU,MAAM,OAAO;AACzB,cAAM,QAAQ,aAAa,MAAM;AACjC,YAAI,OAAO;AACT,gBAAM,OAAO,MAAM,sBAAsB;AACzC,gBAAM,QAAQ,KAAK;AACnB,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,MACF,OAAO;AACL,YAAI,SAAS;AACX,gBAAM,gBAAgB,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACjE,gBAAM,QAAQ,cAAc,OAAO,CAAC,KAAK,EAAE,WAAW,MAAM,MAAM,YAAY,CAAC;AAC/E,iBAAO,QAAQ,cAAc,OAAO,CAAC,KAAK,EAAE,UAAU,MAAM,MAAM,WAAW,CAAC;AAAA,QAChF,OAAO;AACL,gBAAM,QAAQ,MAAM,YAAY;AAChC,iBAAO,QAAQ,MAAM,YAAY;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,eAAa,MAAM;AACjB,UAAM,MAAM,aAAa,MAAM;AAC/B,QAAI,KAAK;AACP,YAAM,QAAQ,iBAAiB,MAAM,IAAI,cAAc,YAAY;AACnE,aAAO,QAAQ,kBAAkB,MAAM,IAAI,eAAe,YAAY;AAAA,IACxE;AAAA,EACF,CAAC;AACD,QAAM,QAAQ;AAAA,IACZ,MAAM,aAAa,MAAM;AAAA,IACzB,CAAC,QAAQ;AACP,YAAM,QAAQ,MAAM,YAAY,QAAQ;AACxC,aAAO,QAAQ,MAAM,YAAY,SAAS;AAAA,IAC5C;AAAA,EACF;AACA,WAAS,OAAO;AACd,UAAM;AACN,UAAM;AAAA,EACR;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,eAAe;AAAA,EACnB,QAAQ,IAAI,SAAS;AACnB,QAAI;AACJ,UAAM,UAAU,OAAO,QAAQ,UAAU,aAAa,QAAQ,SAAS,KAAK,QAAQ,UAAU,OAAO,SAAS,GAAG,CAAC;AAClH,UAAM,UAAU,OAAO,QAAQ,UAAU,aAAa,CAAC,IAAI,QAAQ,MAAM,MAAM,CAAC;AAChF,UAAM,EAAE,OAAO,OAAO,IAAIG,gBAAe,IAAI,GAAG,OAAO;AACvD,UAAM,CAAC,OAAO,MAAM,GAAG,CAAC,CAAC,QAAQ,OAAO,MAAM,QAAQ,EAAE,OAAO,QAAQ,QAAQ,QAAQ,CAAC,CAAC;AAAA,EAC3F;AACF;AAEA,IAAM,uBAAuD,gBAAgB;AAAA,EAC3E,MAAM;AAAA,EACN,OAAO,CAAC,IAAI;AAAA,EACZ,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,SAAS;AAAA,MACpB,WAAW,qBAAuB,MAAM;AAAA,IAC1C,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,IACpE;AAAA,EACF;AACF,CAAC;AAED,SAAS,wBAAwB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAAH,UAAS;AAAA,IACT,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,0BAA0BA,OAAM;AACjF,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,UAAU,QAAQ,MAAM;AAC9B,YAAQ,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG,IAAI,YAAY,EAAE,OAAO,UAAU;AAAA,EAC3F,CAAC;AACD,MAAI,UAAU;AACd,QAAM,WAAW,IAAI,SAAS;AAC9B,QAAM,YAAY,YAAY,QAAQ;AAAA,IACpC,MAAM,CAAC,QAAQ,OAAO,aAAa,IAAI,GAAG,SAAS,KAAK;AAAA,IACxD,CAAC,CAAC,UAAU,KAAK,MAAM;AACrB,cAAQ;AACR,UAAI,CAAC,SAAS;AACZ;AACF,UAAI,CAAC,SAAS;AACZ;AACF,YAAM,WAAW,IAAI;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM,aAAa,KAAK;AAAA,UACxB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,eAAS,QAAQ,CAAC,OAAO,MAAM,SAAS,QAAQ,EAAE,CAAC;AACnD,gBAAU,MAAM;AACd,iBAAS,WAAW;AACpB,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,EAAE,WAAW,OAAO,OAAO;AAAA,EAC7B,IAAI;AACJ,QAAM,OAAO,MAAM;AACjB,YAAQ;AACR,cAAU;AACV,aAAS,QAAQ;AAAA,EACnB;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,QAAQ;AACN,cAAQ;AACR,eAAS,QAAQ;AAAA,IACnB;AAAA,IACA,SAAS;AACP,eAAS,QAAQ;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAASI,sBAAqB,SAAS,UAAU,CAAC,GAAG;AACnD,QAAM,EAAE,QAAAJ,UAAS,eAAe,cAAc,YAAY,EAAE,IAAI;AAChE,QAAM,mBAAmB,IAAI,KAAK;AAClC;AAAA,IACE;AAAA,IACA,CAAC,gCAAgC;AAC/B,UAAI,iBAAiB,iBAAiB;AACtC,UAAI,aAAa;AACjB,iBAAW,SAAS,6BAA6B;AAC/C,YAAI,MAAM,QAAQ,YAAY;AAC5B,uBAAa,MAAM;AACnB,2BAAiB,MAAM;AAAA,QACzB;AAAA,MACF;AACA,uBAAiB,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAAA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,qBAAqB;AAAA,EACzB,QAAQ,IAAI,SAAS;AACnB,QAAI,OAAO,QAAQ,UAAU,YAAY;AACvC,YAAM,UAAU,QAAQ;AACxB,YAAM,YAAYI,sBAAqB,EAAE;AACzC,YAAM,WAAW,CAAC,MAAM,QAAQ,CAAC,GAAG,EAAE,WAAW,KAAK,CAAC;AAAA,IACzD,OAAO;AACL,YAAM,CAAC,SAAS,OAAO,IAAI,QAAQ;AACnC,YAAM,YAAYA,sBAAqB,IAAI,OAAO;AAClD,YAAM,WAAW,CAAC,MAAM,QAAQ,CAAC,GAAG,EAAE,WAAW,KAAK,CAAC;AAAA,IACzD;AAAA,EACF;AACF;AAEA,IAAM,gBAAgD,gBAAgB;AAAA,EACpE,MAAM;AAAA,EACN,OAAO;AAAA,IACL,SAAS;AAAA,EACX;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,cAAc,CAAC;AACrC,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,gBAAgD,gBAAgB;AAAA,EACpE,MAAM;AAAA,EACN,OAAO,CAAC,IAAI;AAAA,EACZ,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,SAAS,cAAc,MAAM,CAAC;AAC3C,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,IACpE;AAAA,EACF;AACF,CAAC;AAED,IAAM,iBAAiD,gBAAgB;AAAA,EACrE,MAAM;AAAA,EACN,OAAO,CAAC,sBAAsB,cAAc,WAAW,WAAW;AAAA,EAClE,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,eAAe,KAAK,CAAC;AAC3C,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,UAA0C,gBAAgB;AAAA,EAC9D,MAAM;AAAA,EACN,OAAO,CAAC,WAAW,UAAU,6BAA6B,cAAc;AAAA,EACxE,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,QAAQ,MAAM,SAAS,KAAK,CAAC;AACnD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,SAAS,cAAc,SAAS,cAAc,SAAS;AACrD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV;AAAA,EACF,IAAI,WAAW,OAAO,UAAU,CAAC;AACjC,QAAM,QAAQ,UAAU,WAAW,YAAY,IAAI,IAAI,YAAY;AACnE,QAAM,UAAU,IAAI,KAAK;AACzB,QAAM,YAAY,IAAI,KAAK;AAC3B,QAAM,QAAQ,WAAW,MAAM;AAC/B,iBAAe,QAAQ,SAAS,MAAM,MAAM;AAC1C,QAAI;AACF,YAAM,QAAQ;AAChB,UAAM,QAAQ;AACd,YAAQ,QAAQ;AAChB,cAAU,QAAQ;AAClB,QAAI,SAAS;AACX,YAAM,eAAe,MAAM;AAC7B,UAAM,WAAW,OAAO,YAAY,aAAa,QAAQ,GAAG,IAAI,IAAI;AACpE,QAAI;AACF,YAAM,OAAO,MAAM;AACnB,YAAM,QAAQ;AACd,cAAQ,QAAQ;AAChB,gBAAU,IAAI;AAAA,IAChB,SAAS,GAAG;AACV,YAAM,QAAQ;AACd,cAAQ,CAAC;AACT,UAAI;AACF,cAAM;AAAA,IACV,UAAE;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO,MAAM;AAAA,EACf;AACA,MAAI;AACF,YAAQ,KAAK;AACf,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,WAAS,oBAAoB;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,SAAS,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,QAAQ,KAAK,CAAC,EAAE,MAAM,MAAM;AAAA,IACtE,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,KAAK,aAAa,YAAY;AAC5B,aAAO,kBAAkB,EAAE,KAAK,aAAa,UAAU;AAAA,IACzD;AAAA,EACF;AACF;AAEA,eAAe,UAAU,SAAS;AAChC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,MAAM,IAAI,MAAM;AACtB,UAAM,EAAE,KAAK,QAAQ,OAAO,OAAO,OAAO,SAAS,aAAa,eAAe,IAAI;AACnF,QAAI,MAAM;AACV,QAAI;AACF,UAAI,SAAS;AACf,QAAI;AACF,UAAI,QAAQ;AACd,QAAI;AACF,UAAI,YAAY;AAClB,QAAI;AACF,UAAI,UAAU;AAChB,QAAI;AACF,UAAI,cAAc;AACpB,QAAI;AACF,UAAI,iBAAiB;AACvB,QAAI,SAAS,MAAM,QAAQ,GAAG;AAC9B,QAAI,UAAU;AAAA,EAChB,CAAC;AACH;AACA,SAAS,SAAS,SAAS,oBAAoB,CAAC,GAAG;AACjD,QAAM,QAAQ;AAAA,IACZ,MAAM,UAAU,QAAQ,OAAO,CAAC;AAAA,IAChC;AAAA,IACA;AAAA,MACE,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACL;AAAA,EACF;AACA;AAAA,IACE,MAAM,QAAQ,OAAO;AAAA,IACrB,MAAM,MAAM,QAAQ,kBAAkB,KAAK;AAAA,IAC3C,EAAE,MAAM,KAAK;AAAA,EACf;AACA,SAAO;AACT;AAEA,IAAM,WAA2C,gBAAgB;AAAA,EAC/D,MAAM;AAAA,EACN,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,SAAS,KAAK,CAAC;AACrC,WAAO,MAAM;AACX,UAAI,KAAK,aAAa,MAAM;AAC1B,eAAO,MAAM,QAAQ,IAAI;AAAA,eAClB,KAAK,SAAS,MAAM;AAC3B,eAAO,MAAM,MAAM,KAAK,KAAK;AAC/B,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAC3B,aAAO,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,IACnC;AAAA,EACF;AACF,CAAC;AAED,SAAS,eAAe,IAAI;AAC1B,MAAI,OAAO,WAAW,eAAe,cAAc;AACjD,WAAO,GAAG,SAAS;AACrB,MAAI,OAAO,aAAa,eAAe,cAAc;AACnD,WAAO,GAAG;AACZ,SAAO;AACT;AAEA,IAAM,iCAAiC;AACvC,SAAS,UAAU,SAAS,UAAU,CAAC,GAAG;AACxC,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,uBAAuB;AAAA,MACrB,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX,QAAAJ,UAAS;AAAA,IACT,UAAU,CAAC,MAAM;AACf,cAAQ,MAAM,CAAC;AAAA,IACjB;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,IAAI,CAAC;AACvB,QAAM,YAAY,IAAI,CAAC;AACvB,QAAM,IAAI,SAAS;AAAA,IACjB,MAAM;AACJ,aAAO,UAAU;AAAA,IACnB;AAAA,IACA,IAAI,IAAI;AACN,eAAS,IAAI,MAAM;AAAA,IACrB;AAAA,EACF,CAAC;AACD,QAAM,IAAI,SAAS;AAAA,IACjB,MAAM;AACJ,aAAO,UAAU;AAAA,IACnB;AAAA,IACA,IAAI,IAAI;AACN,eAAS,QAAQ,EAAE;AAAA,IACrB;AAAA,EACF,CAAC;AACD,WAAS,SAAS,IAAI,IAAI;AACxB,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,CAACA;AACH;AACF,UAAM,WAAW,QAAQ,OAAO;AAChC,QAAI,CAAC;AACH;AACF,KAAC,KAAK,oBAAoB,WAAWA,QAAO,SAAS,OAAO,aAAa,OAAO,SAAS,GAAG,SAAS;AAAA,MACnG,MAAM,KAAK,QAAQ,EAAE,MAAM,OAAO,KAAK,EAAE;AAAA,MACzC,OAAO,KAAK,QAAQ,EAAE,MAAM,OAAO,KAAK,EAAE;AAAA,MAC1C,UAAU,QAAQ,QAAQ;AAAA,IAC5B,CAAC;AACD,UAAM,oBAAoB,KAAK,YAAY,OAAO,SAAS,SAAS,aAAa,OAAO,SAAS,GAAG,qBAAqB,YAAY,OAAO,SAAS,SAAS,oBAAoB;AAClL,QAAI,KAAK;AACP,gBAAU,QAAQ,gBAAgB;AACpC,QAAI,KAAK;AACP,gBAAU,QAAQ,gBAAgB;AAAA,EACtC;AACA,QAAM,cAAc,IAAI,KAAK;AAC7B,QAAM,eAAe,SAAS;AAAA,IAC5B,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,aAAa,SAAS;AAAA,IAC1B,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,cAAc,CAAC,MAAM;AACzB,QAAI,CAAC,YAAY;AACf;AACF,gBAAY,QAAQ;AACpB,eAAW,OAAO;AAClB,eAAW,QAAQ;AACnB,eAAW,MAAM;AACjB,eAAW,SAAS;AACpB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,uBAAuB,cAAc,aAAa,WAAW,IAAI;AACvE,QAAM,kBAAkB,CAAC,WAAW;AAClC,QAAI;AACJ,QAAI,CAACA;AACH;AACF,UAAM,OAAO,KAAK,UAAU,OAAO,SAAS,OAAO,aAAa,OAAO,SAAS,GAAG,qBAAqB,UAAU,OAAO,SAAS,OAAO,oBAAoB,aAAa,MAAM;AAChL,UAAM,EAAE,SAAS,cAAc,IAAI,iBAAiB,EAAE;AACtD,UAAM,aAAa,GAAG;AACtB,eAAW,OAAO,aAAa,UAAU;AACzC,eAAW,QAAQ,aAAa,UAAU;AAC1C,UAAM,OAAO,KAAK,IAAI,UAAU,MAAM,OAAO,QAAQ;AACrD,UAAM,QAAQ,KAAK,IAAI,UAAU,IAAI,GAAG,eAAe,GAAG,eAAe,OAAO,SAAS,KAAK;AAC9F,QAAI,YAAY,UAAU,kBAAkB,eAAe;AACzD,mBAAa,OAAO;AACpB,mBAAa,QAAQ;AAAA,IACvB,OAAO;AACL,mBAAa,OAAO;AACpB,mBAAa,QAAQ;AAAA,IACvB;AACA,cAAU,QAAQ;AAClB,QAAI,YAAY,GAAG;AACnB,QAAI,WAAWA,QAAO,YAAY,CAAC;AACjC,kBAAYA,QAAO,SAAS,KAAK;AACnC,eAAW,MAAM,YAAY,UAAU;AACvC,eAAW,SAAS,YAAY,UAAU;AAC1C,UAAM,MAAM,KAAK,IAAI,SAAS,MAAM,OAAO,OAAO;AAClD,UAAM,SAAS,KAAK,IAAI,SAAS,IAAI,GAAG,gBAAgB,GAAG,gBAAgB,OAAO,UAAU,KAAK;AACjG,QAAI,YAAY,UAAU,kBAAkB,kBAAkB;AAC5D,mBAAa,MAAM;AACnB,mBAAa,SAAS;AAAA,IACxB,OAAO;AACL,mBAAa,MAAM;AACnB,mBAAa,SAAS;AAAA,IACxB;AACA,cAAU,QAAQ;AAAA,EACpB;AACA,QAAM,kBAAkB,CAAC,MAAM;AAC7B,QAAI;AACJ,QAAI,CAACA;AACH;AACF,UAAM,eAAe,KAAK,EAAE,OAAO,oBAAoB,OAAO,KAAK,EAAE;AACrE,oBAAgB,WAAW;AAC3B,gBAAY,QAAQ;AACpB,yBAAqB,CAAC;AACtB,aAAS,CAAC;AAAA,EACZ;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,WAAW,cAAc,iBAAiB,UAAU,MAAM,KAAK,IAAI;AAAA,IACnE;AAAA,EACF;AACA,eAAa,MAAM;AACjB,QAAI;AACF,YAAM,WAAW,QAAQ,OAAO;AAChC,UAAI,CAAC;AACH;AACF,sBAAgB,QAAQ;AAAA,IAC1B,SAAS,GAAG;AACV,cAAQ,CAAC;AAAA,IACX;AAAA,EACF,CAAC;AACD;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AACR,YAAM,WAAW,QAAQ,OAAO;AAChC,UAAIA,WAAU;AACZ,wBAAgB,QAAQ;AAAA,IAC5B;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,SAAS,YAAY,UAAU,CAAC,GAAG;AAC5D,MAAI;AACJ,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,cAAc,MAAM;AAAA,EACtB,IAAI;AACJ,QAAM,QAAQ,SAAS;AAAA,IACrB;AAAA,IACA;AAAA,MACE,GAAG;AAAA,MACH,QAAQ;AAAA,QACN,CAAC,SAAS,IAAI,KAAK,QAAQ,aAAa,OAAO,KAAK;AAAA,QACpD,GAAG,QAAQ;AAAA,MACb;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,UAAU,IAAI;AACpB,QAAM,YAAY,SAAS,MAAM,CAAC,CAAC,QAAQ,KAAK;AAChD,QAAM,kBAAkB,SAAS,MAAM;AACrC,WAAO,eAAe,QAAQ,OAAO,CAAC;AAAA,EACxC,CAAC;AACD,QAAM,mBAAmBI,sBAAqB,eAAe;AAC7D,WAAS,eAAe;AACtB,UAAM,QAAQ;AACd,QAAI,CAAC,gBAAgB,SAAS,CAAC,iBAAiB,SAAS,CAAC,YAAY,gBAAgB,KAAK;AACzF;AACF,UAAM,EAAE,cAAc,cAAc,aAAa,YAAY,IAAI,gBAAgB;AACjF,UAAM,aAAa,cAAc,YAAY,cAAc,QAAQ,gBAAgB,eAAe,eAAe;AACjH,QAAI,MAAM,aAAa,SAAS,KAAK,YAAY;AAC/C,UAAI,CAAC,QAAQ,OAAO;AAClB,gBAAQ,QAAQ,QAAQ,IAAI;AAAA,UAC1B,WAAW,KAAK;AAAA,UAChB,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,QAAQ,CAAC;AAAA,QACxD,CAAC,EAAE,QAAQ,MAAM;AACf,kBAAQ,QAAQ;AAChB,mBAAS,MAAM,aAAa,CAAC;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,QAAM,OAAO;AAAA,IACX,MAAM,CAAC,MAAM,aAAa,SAAS,GAAG,iBAAiB,KAAK;AAAA,IAC5D;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA,iBAAe,IAAI;AACnB,SAAO;AAAA,IACL;AAAA,IACA,QAAQ;AACN,eAAS,MAAM,aAAa,CAAC;AAAA,IAC/B;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB;AAAA,EACtB,QAAQ,IAAI,SAAS;AACnB,QAAI,OAAO,QAAQ,UAAU;AAC3B,wBAAkB,IAAI,QAAQ,KAAK;AAAA;AAEnC,wBAAkB,IAAI,GAAG,QAAQ,KAAK;AAAA,EAC1C;AACF;AAEA,IAAM,wBAAwB;AAAA,EAC5B,QAAQ,IAAI,SAAS;AACnB,QAAI,OAAO,QAAQ,UAAU;AAC3B,8BAAwB,IAAI,QAAQ,KAAK;AAAA;AAEzC,8BAAwB,IAAI,GAAG,QAAQ,KAAK;AAAA,EAChD;AACF;AAEA,IAAM,WAA2C,gBAAgB;AAAA,EAC/D,MAAM;AAAA,EACN,OAAO,CAAC,SAAS,oBAAoB,cAAc;AAAA,EACnD,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,SAAS,KAAK,CAAC;AACrC,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,oBAAoD,gBAAgB;AAAA,EACxE,MAAM;AAAA,EACN,OAAO,CAAC,iBAAiB,IAAI;AAAA,EAC7B,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,SAAS,kBAAkB,QAAQ,KAAK,CAAC;AACtD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,IACpE;AAAA,EACF;AACF,CAAC;AAED,IAAM,kBAAkD,gBAAgB;AAAA,EACtE,MAAM;AAAA,EACN,OAAO,CAAC,SAAS,gBAAgB,IAAI;AAAA,EACrC,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,SAAS,gBAAgB,EAAE,GAAG,OAAO,OAAO,CAAC,CAAC;AAC3D,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,IACpE;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAA6C,gBAAgB;AAAA,EACjE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,WAAW,CAAC;AAClC,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,SAAyC,gBAAgB;AAAA,EAC7D,MAAM;AAAA,EACN,OAAO,CAAC,UAAU;AAAA,EAClB,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,OAAO,EAAE,GAAG,OAAO,UAAU,KAAK,CAAC,CAAC;AAC1D,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,eAA+C,gBAAgB;AAAA,EACnE,MAAM;AAAA,EACN,OAAO;AAAA,IACL;AAAA,EACF;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,MAAM,OAAO,QAAQ;AACpC,UAAM,MAAM,aAAa,MAAM;AAC/B,WAAO,MAAM;AACX,UAAI,MAAM,WAAW,IAAI;AACvB,eAAO,MAAM,QAAQ,GAAG;AAAA,IAC5B;AAAA,EACF;AACF,CAAC;AAED,IAAM,sBAAsD,gBAAgB;AAAA,EAC1E,MAAM;AAAA,EACN,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,OAAO,EAAE,OAAO,KAAK,GAAG;AAC5B,UAAM,OAAO,SAAS,oBAAoB;AAAA,MACxC,GAAG;AAAA,MACH,gBAAgB,MAAM;AACpB,YAAI;AACJ,SAAC,KAAK,MAAM,iBAAiB,OAAO,SAAS,GAAG,KAAK,OAAO,GAAG,IAAI;AACnE,aAAK,eAAe,GAAG,IAAI;AAAA,MAC7B;AAAA,MACA,oBAAoB,MAAM;AACxB,YAAI;AACJ,SAAC,KAAK,MAAM,qBAAqB,OAAO,SAAS,GAAG,KAAK,OAAO,GAAG,IAAI;AACvE,aAAK,oBAAoB,GAAG,IAAI;AAAA,MAClC;AAAA,MACA,qBAAqB,MAAM;AACzB,YAAI;AACJ,SAAC,KAAK,MAAM,sBAAsB,OAAO,SAAS,GAAG,KAAK,OAAO,GAAG,IAAI;AACxE,aAAK,qBAAqB,GAAG,IAAI;AAAA,MACnC;AAAA,IACF,CAAC,CAAC;AACF,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,YAA4C,gBAAgB;AAAA,EAChE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,UAAU,UAAU;AAAA,IACtB,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,eAA+C,gBAAgB;AAAA,EACnE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,QAAQ,aAAa;AAAA,IACvB,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAA6C,gBAAgB;AAAA,EACjE,MAAM;AAAA,EACN,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,KAAK,IAAI,IAAI;AACnB,UAAM,OAAO,SAAS,WAAW;AAAA,MAC/B,GAAG;AAAA,MACH,QAAQ,MAAM,WAAW,SAAS,KAAK;AAAA,IACzC,CAAC,CAAC;AACF,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,MAAM,EAAE,KAAK,GAAG,CAAC;AAAA,IAC1C;AAAA,EACF;AACF,CAAC;AAED,IAAM,iBAAiC,gBAAgB;AAAA,EACrD,MAAM;AAAA,EACN,OAAO,CAAC,IAAI;AAAA,EACZ,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,SAAS,IAAI;AACnB,UAAM,OAAO,SAAS,eAAe,MAAM,CAAC;AAC5C,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,EAAE,MAAM,MAAM,OAAO,EAAE,KAAK,OAAO,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,IACpE;AAAA,EACF;AACF,CAAC;AAED,IAAM,0BAA0D,gBAAgB;AAAA,EAC9E,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,aAAa,wBAAwB;AAAA,IACvC,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,uBAAuD,gBAAgB;AAAA,EAC3E,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,UAAU,qBAAqB;AAAA,IACjC,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,mBAAmD,gBAAgB;AAAA,EACvE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,aAAa,iBAAmB;AAAA,IAClC,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,wBAAwD,gBAAgB;AAAA,EAC5E,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,WAAW,sBAAsB;AAAA,IACnC,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,4BAA4D,gBAAgB;AAAA,EAChF,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,QAAQ,0BAA0B;AAAA,IACpC,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,kBAAkB;AAAA,EACtB,QAAQ,IAAI,SAAS;AACnB,QAAI,OAAO,QAAQ,UAAU;AAC3B,wBAAkB,IAAI,QAAQ,KAAK;AAAA;AAEnC,wBAAkB,IAAI,GAAG,QAAQ,KAAK;AAAA,EAC1C;AACF;AAEA,SAAS,oBAAoB,QAAQ,UAAU,UAAU,CAAC,GAAG;AAC3D,QAAM,EAAE,QAAAJ,UAAS,eAAe,GAAG,gBAAgB,IAAI;AACvD,MAAI;AACJ,QAAM,cAAc,aAAa,MAAMA,WAAU,sBAAsBA,OAAM;AAC7E,QAAM,UAAU,MAAM;AACpB,QAAI,UAAU;AACZ,eAAS,WAAW;AACpB,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,UAAU,SAAS,MAAM;AAC7B,UAAM,QAAQ,QAAQ,MAAM;AAC5B,UAAM,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,YAAY,EAAE,OAAO,UAAU;AAC1F,WAAO,IAAI,IAAI,KAAK;AAAA,EACtB,CAAC;AACD,QAAM,YAAY;AAAA,IAChB,MAAM,QAAQ;AAAA,IACd,CAAC,aAAa;AACZ,cAAQ;AACR,UAAI,YAAY,SAAS,SAAS,MAAM;AACtC,mBAAW,IAAI,iBAAiB,QAAQ;AACxC,iBAAS,QAAQ,CAAC,OAAO,SAAS,QAAQ,IAAI,eAAe,CAAC;AAAA,MAChE;AAAA,IACF;AAAA,IACA,EAAE,WAAW,MAAM,OAAO,OAAO;AAAA,EACnC;AACA,QAAM,cAAc,MAAM;AACxB,WAAO,YAAY,OAAO,SAAS,SAAS,YAAY;AAAA,EAC1D;AACA,QAAM,OAAO,MAAM;AACjB,cAAU;AACV,YAAQ;AAAA,EACV;AACA,oBAAkB,IAAI;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,UAAU,MAAM,QAAQ,UAAU,CAAC,GAAG;AAC7C,QAAM,EAAE,QAAAA,UAAS,eAAe,cAAc,UAAU,MAAM,IAAI;AAClE,QAAM,WAAW,IAAI,YAAY;AACjC,QAAM,QAAQ,SAAS,MAAM;AAC3B,QAAI;AACJ,WAAO,aAAa,MAAM,OAAO,KAAKA,WAAU,OAAO,SAASA,QAAO,aAAa,OAAO,SAAS,GAAG;AAAA,EACzG,CAAC;AACD,WAAS,eAAe;AACtB,QAAI;AACJ,UAAM,MAAM,QAAQ,IAAI;AACxB,UAAM,KAAK,QAAQ,KAAK;AACxB,QAAI,MAAMA,WAAU,KAAK;AACvB,YAAM,SAAS,KAAKA,QAAO,iBAAiB,EAAE,EAAE,iBAAiB,GAAG,MAAM,OAAO,SAAS,GAAG,KAAK;AAClG,eAAS,QAAQ,SAAS;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,SAAS;AACX,wBAAoB,OAAO,cAAc;AAAA,MACvC,iBAAiB,CAAC,SAAS,OAAO;AAAA,MAClC,QAAAA;AAAA,IACF,CAAC;AAAA,EACH;AACA;AAAA,IACE,CAAC,OAAO,MAAM,QAAQ,IAAI,CAAC;AAAA,IAC3B,CAAC,GAAG,QAAQ;AACV,UAAI,IAAI,CAAC,KAAK,IAAI,CAAC;AACjB,YAAI,CAAC,EAAE,MAAM,eAAe,IAAI,CAAC,CAAC;AACpC,mBAAa;AAAA,IACf;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EACpB;AACA;AAAA,IACE;AAAA,IACA,CAAC,QAAQ;AACP,UAAI;AACJ,YAAM,WAAW,QAAQ,IAAI;AAC7B,YAAM,KAAK,MAAM,UAAU,OAAO,SAAS,GAAG,UAAU,UAAU;AAChE,YAAI,OAAO;AACT,gBAAM,MAAM,MAAM,eAAe,QAAQ;AAAA;AAEzC,gBAAM,MAAM,MAAM,YAAY,UAAU,GAAG;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,SAAS,oBAAoB;AAC3B,QAAM,MAAM,IAAI,EAAE;AAClB,QAAM,QAAQ,IAAI,EAAE;AACpB,QAAM,SAAS,IAAI,EAAE;AACrB,QAAM,OAAO,IAAI,EAAE;AACnB,MAAI,UAAU;AACZ,UAAM,YAAY,UAAU,UAAU;AACtC,UAAM,cAAc,UAAU,YAAY;AAC1C,UAAM,eAAe,UAAU,aAAa;AAC5C,UAAM,aAAa,UAAU,WAAW;AACxC,cAAU,QAAQ;AAClB,gBAAY,QAAQ;AACpB,iBAAa,QAAQ;AACrB,eAAW,QAAQ;AACnB,WAAO;AACP,qBAAiB,UAAU,cAAc,MAAM,CAAC;AAAA,EAClD;AACA,WAAS,SAAS;AAChB,QAAI,QAAQ,SAAS,UAAU;AAC/B,UAAM,QAAQ,SAAS,YAAY;AACnC,WAAO,QAAQ,SAAS,aAAa;AACrC,SAAK,QAAQ,SAAS,WAAW;AAAA,EACnC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO,iBAAiB,SAAS,eAAe,EAAE,iBAAiB,QAAQ;AAC7E;AAEA,IAAM,oBAAoD,gBAAgB;AAAA,EACxE,MAAM;AAAA,EACN,OAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,kBAAkB;AACtB,WAAO,MAAM;AACX,UAAI,MAAM,SAAS;AACjB,eAAO,EAAE,OAAO;AAAA,UACd,OAAO;AAAA,YACL,YAAY,MAAM,MAAM,IAAI,QAAQ;AAAA,YACpC,cAAc,MAAM,QAAQ,MAAM,QAAQ;AAAA,YAC1C,eAAe,MAAM,SAAS,OAAO,QAAQ;AAAA,YAC7C,aAAa,MAAM,OAAO,KAAK,QAAQ;AAAA,YACvC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,UAAU;AAAA,YACV,UAAU;AAAA,UACZ;AAAA,QACF,GAAG,MAAM,QAAQ,CAAC;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,UAAU;AAAA,EACd,QAAQ,IAAI,SAAS;AACnB,QAAI,OAAO,QAAQ,UAAU,YAAY;AACvC,YAAM,UAAU,QAAQ;AACxB,YAAM,QAAQ,UAAU,IAAI;AAAA,QAC1B,WAAW;AACT,kBAAQ,KAAK;AAAA,QACf;AAAA,QACA,SAAS;AACP,kBAAQ,KAAK;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,YAAM,CAAC,SAAS,OAAO,IAAI,QAAQ;AACnC,YAAM,QAAQ,UAAU,IAAI;AAAA,QAC1B,GAAG;AAAA,QACH,SAAS,GAAG;AACV,cAAI;AACJ,WAAC,KAAK,QAAQ,aAAa,OAAO,SAAS,GAAG,KAAK,SAAS,CAAC;AAC7D,kBAAQ,KAAK;AAAA,QACf;AAAA,QACA,OAAO,GAAG;AACR,cAAI;AACJ,WAAC,KAAK,QAAQ,WAAW,OAAO,SAAS,GAAG,KAAK,SAAS,CAAC;AAC3D,kBAAQ,KAAK;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,KAAK;AAChC,QAAM,QAAQ,OAAO,iBAAiB,GAAG;AACzC,MAAI,MAAM,cAAc,YAAY,MAAM,cAAc,YAAY,MAAM,cAAc,UAAU,IAAI,cAAc,IAAI,eAAe,MAAM,cAAc,UAAU,IAAI,eAAe,IAAI,cAAc;AACxM,WAAO;AAAA,EACT,OAAO;AACL,UAAM,SAAS,IAAI;AACnB,QAAI,CAAC,UAAU,OAAO,YAAY;AAChC,aAAO;AACT,WAAO,oBAAoB,MAAM;AAAA,EACnC;AACF;AACA,SAAS,eAAe,UAAU;AAChC,QAAM,IAAI,YAAY,OAAO;AAC7B,QAAM,UAAU,EAAE;AAClB,MAAI,oBAAoB,OAAO;AAC7B,WAAO;AACT,MAAI,EAAE,QAAQ,SAAS;AACrB,WAAO;AACT,MAAI,EAAE;AACJ,MAAE,eAAe;AACnB,SAAO;AACT;AACA,IAAM,oBAAoC,oBAAI,QAAQ;AACtD,SAAS,cAAc,SAAS,eAAe,OAAO;AACpD,QAAM,WAAW,IAAI,YAAY;AACjC,MAAI,wBAAwB;AAC5B,MAAI,kBAAkB;AACtB,QAAM,MAAM,OAAO,GAAG,CAAC,OAAO;AAC5B,UAAM,SAAS,eAAe,QAAQ,EAAE,CAAC;AACzC,QAAI,QAAQ;AACV,YAAM,MAAM;AACZ,UAAI,CAAC,kBAAkB,IAAI,GAAG;AAC5B,0BAAkB,IAAI,KAAK,IAAI,MAAM,QAAQ;AAC/C,UAAI,IAAI,MAAM,aAAa;AACzB,0BAAkB,IAAI,MAAM;AAC9B,UAAI,IAAI,MAAM,aAAa;AACzB,eAAO,SAAS,QAAQ;AAC1B,UAAI,SAAS;AACX,eAAO,IAAI,MAAM,WAAW;AAAA,IAChC;AAAA,EACF,GAAG;AAAA,IACD,WAAW;AAAA,EACb,CAAC;AACD,QAAM,OAAO,MAAM;AACjB,UAAM,KAAK,eAAe,QAAQ,OAAO,CAAC;AAC1C,QAAI,CAAC,MAAM,SAAS;AAClB;AACF,QAAI,OAAO;AACT,8BAAwB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,CAAC,MAAM;AACL,yBAAe,CAAC;AAAA,QAClB;AAAA,QACA,EAAE,SAAS,MAAM;AAAA,MACnB;AAAA,IACF;AACA,OAAG,MAAM,WAAW;AACpB,aAAS,QAAQ;AAAA,EACnB;AACA,QAAM,SAAS,MAAM;AACnB,UAAM,KAAK,eAAe,QAAQ,OAAO,CAAC;AAC1C,QAAI,CAAC,MAAM,CAAC,SAAS;AACnB;AACF,QAAI;AACF,+BAAyB,OAAO,SAAS,sBAAsB;AACjE,OAAG,MAAM,WAAW;AACpB,sBAAkB,OAAO,EAAE;AAC3B,aAAS,QAAQ;AAAA,EACnB;AACA,oBAAkB,MAAM;AACxB,SAAO,SAAS;AAAA,IACd,MAAM;AACJ,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,IAAI,GAAG;AACL,UAAI;AACF,aAAK;AAAA,UACF,QAAO;AAAA,IACd;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eAAe;AACtB,MAAI,YAAY;AAChB,QAAM,QAAQ,IAAI,KAAK;AACvB,SAAO,CAAC,IAAI,YAAY;AACtB,UAAM,QAAQ,QAAQ;AACtB,QAAI;AACF;AACF,gBAAY;AACZ,UAAM,WAAW,cAAc,IAAI,QAAQ,KAAK;AAChD,UAAM,OAAO,CAAC,MAAM,SAAS,QAAQ,CAAC;AAAA,EACxC;AACF;AACA,IAAM,cAAc,aAAa;AAEjC,IAAM,aAA6C,gBAAgB;AAAA,EACjE,MAAM;AAAA,EACN,OAAO,CAAC,QAAQ,kBAAkB,OAAO,qBAAqB,YAAY,YAAY;AAAA,EACtF,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,WAAW,MAAM,MAAM,MAAM,EAAE,GAAG,OAAO,UAAU,KAAK,CAAC,CAAC;AAChF,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,eAA+C,gBAAgB;AAAA,EACnE,MAAM;AAAA,EACN,OAAO,CAAC,aAAa,YAAY,QAAQ;AAAA,EACzC,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,aAAa,EAAE,GAAG,OAAO,UAAU,KAAK,CAAC,CAAC;AAChE,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,iBAAiD,gBAAgB;AAAA,EACrE,MAAM;AAAA,EACN,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,OAAO,EAAE,OAAO,OAAO,GAAG;AAC9B,UAAM,EAAE,MAAM,QAAQ,IAAI,OAAO,KAAK;AACtC,UAAM,EAAE,MAAM,gBAAgB,cAAc,SAAS,IAAI,eAAe,SAAS,MAAM,OAAO;AAC9F,WAAO,EAAE,SAAS,CAAC;AACnB,QAAI,eAAe,SAAS,OAAO,eAAe,UAAU,YAAY,CAAC,MAAM,QAAQ,eAAe,KAAK;AACzG,qBAAe,MAAM,SAAS,MAAM,UAAU;AAChD,WAAO,MAAM,EAAE,OAAO,EAAE,GAAG,eAAe,GAAG;AAAA,MAC3C,EAAE,OAAO,EAAE,GAAG,aAAa,MAAM,GAAG,KAAK,MAAM,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,UAAU,QAAQ,KAAK,OAAO,EAAE,GAAG,MAAM,UAAU,MAAM,QAAQ,IAAI,IAAI,qBAAqB,CAAC,CAAC;AAAA,IAC7L,CAAC;AAAA,EACH;AACF,CAAC;AAED,IAAM,iBAAiD,gBAAgB;AAAA,EACrE,MAAM;AAAA,EACN,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS;AAAA,MACpB,SAAS,eAAe;AAAA,IAC1B,CAAC;AACD,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;AAED,IAAM,gBAAgD,gBAAgB;AAAA,EACpE,MAAM;AAAA,EACN,OAAO,CAAC,gBAAgB,eAAe;AAAA,EACvC,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,OAAO,SAAS,cAAc,KAAK,CAAC;AAC1C,WAAO,MAAM;AACX,UAAI,MAAM;AACR,eAAO,MAAM,QAAQ,IAAI;AAAA,IAC7B;AAAA,EACF;AACF,CAAC;", "names": ["onClickOutside", "window", "usePreferredDark", "useStorage", "useElementSize", "useElementVisibility"]}
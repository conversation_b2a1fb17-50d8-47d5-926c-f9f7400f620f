import json
from fastapi import Depends, Query, Request

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
    insert_operation,
    OperationType,
    PAGINATION,
)
from .schema import CalLogCreateDTO, CalLogDTO
from models.fishery import CalLogModel


router = TortoiseCRUDRouter(
    schema=CalLogDTO,
    create_schema=CalLogCreateDTO,
    db_model=CalLogModel,
    prefix="calibration",
    tags=["校准记录"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.SELF_DEFAULT,
    tenant_data_option=TenantDataOption.TENANT_ONLY,
)

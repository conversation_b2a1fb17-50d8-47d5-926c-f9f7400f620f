
function getMISSMS(epoch) {
    let dt = new Date(epoch);
    let mi = dt.getMinutes();
    let ss = dt.getSeconds();
    let ms = dt.getMilliseconds();
    mi = ('00' + mi).slice(-2);
    ss = ('00' + ss).slice(-2);
    ms = ('000' + ms).slice(-3);
    return mi + ':' + ss + ':' + ms;
}


class WaveChart {
    private x_buf: any[] = [];
    private y_buf: { [key: string]: any[] } = {};

    // Static color palettes
    public static colorPalette_macarons = [
        '#2ec7c9', '#b6a2de', '#5ab1ef', '#ffb980', '#d87a80',
        '#8d98b3', '#e5cf0d', '#97b552', '#95706d', '#dc69aa',
        '#07a2a4', '#9a7fd1', '#588dd5', '#f5994e', '#c05050',
        '#59678c', '#c9ab00', '#7eb00a', '#6f5553', '#c14089',
    ];

    public static colorPalette_dark = [
        '#4992ff', '#7cffb2', '#fddd60', '#ff6e76', '#58d9f9',
        '#05c091', '#ff8a45', '#8d48e3', '#dd79ff'
    ];

    public static colorPalette_default = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
    ];

    constructor() {
        // Initialize any required properties
    }

    public __appendData(chart: any, data: object, max_len: number = 100000) {
        for (const it of data.x) {
            this.x_buf.push(it);
            // x_buf.push(getMISSMS(parseInt(it * 1000)));
        }

        if (this.x_buf.length > max_len) {
            this.x_buf = this.x_buf.slice(-max_len);
        }

        var legend_data = [];
        var series_data = [];

        // 清理多余的buf
        // for (var k of Object.keys(y_buf)) {
        //     if (!data.y.find(item => item.name == k))
        //         delete y_buf[k];
        // }



        for (const it of data.y) {
            legend_data.push(it.name);
            if (!this.y_buf[it.name]) {
                this.y_buf[it.name] = [];
            }

            this.y_buf[it.name].push(...it.value);
            if (this.y_buf[it.name].length > max_len) {
                this.y_buf[it.name] = this.y_buf[it.name].slice(-max_len);
            }

            series_data.push({
                name: it.name,
                type: 'line',
                data: this.y_buf[it.name],
                // sampling: 'lttb',
                smooth: false,
                symbol: 'none',
                // showSymbol: false,
                large: true,
                largeThreshold: 1000,
                itemStyle: {
                    lineStyle: {
                        width: 1, //折线宽度
                    },
                },
            });
        }

        // 清理多余的series
        var series_del = false;

        var chart_option = chart.getOption();
        for (var i = chart_option.series.length - 1; i >= 0; i--) {
            // if (!data.y.find(item => item.name == chart_option.series[i].name)) {
            if (!legend_data.includes(chart_option.series[i].name)) {
                series_del = true;
                // chart_series.splice(i, 1);
            }
        }

        if (series_del) {
            chart_option.xAxis.data = this.x_buf;
            chart_option.series = series_data;
            chart.setOption(chart_option, true)
        }
        else {
            chart.setOption({
                xAxis: {
                    data: this.x_buf,
                },
                legend: {
                    data: legend_data,
                },
                series: series_data,
            });
        }

        return legend_data;
    };

    public __setData(chart, data: object) {
        var legend_data = [];
        var series_data = [];
        for (const it of data.y) {
            legend_data.push(it.name);
            series_data.push({
                name: it.name,
                type: 'line',
                data: it.value,
                smooth: false,
                symbol: 'none',
                large: true,
                largeThreshold: 1000,
                itemStyle: {
                    lineStyle: {
                        width: 1, //折线宽度
                    },
                },
            });
        }

        // 清理多余的series
        var series_del = false;

        var chart_option = chart.getOption();
        for (var i = chart_option.series.length - 1; i >= 0; i--) {
            // if (!data.y.find(item => item.name == chart_option.series[i].name)) {
            if (!legend_data.includes(chart_option.series[i].name)) {
                series_del = true;
                // chart_series.splice(i, 1);
            }
        }

        if (series_del) {
            chart_option.xAxis.data = data.x;
            chart_option.series = series_data;
            chart.setOption(chart_option, true)
        }
        else {
            chart.setOption({
                xAxis: {
                    data: data.x,
                },
                legend: {
                    data: legend_data,
                },
                series: series_data,
            });
        }

        return legend_data;
    };

    public __clear(chart) {
        this.x_buf = [];
        this.y_buf = {};

        return this.__setData(chart, { x: [], y: [] })
    };

    public __getChartData(chart) {
        var option = chart.getOption();
        var xAxis = option.xAxis[0]; // x 轴

        var header;
        var seriesData;
        var dataZoomComponent = option.dataZoom[0]; // dataZoom 组件
        if (dataZoomComponent) {
            var startPercent = dataZoomComponent.start;
            var endPercent = dataZoomComponent.end;
            var dataLength = xAxis.data.length;
            var start = Math.floor((startPercent / 100) * dataLength);
            var end = Math.ceil((endPercent / 100) * dataLength);

            var arr = ['time'];
            header = arr.concat(xAxis.data.slice(start, end));

            seriesData = option.series.map(function (serie) {
                var arr = [serie.name];
                return arr.concat(serie.data.slice(start, end));
            });
        } else {
            var arr = ['time'];
            header = arr.concat(xAxis.data);

            seriesData = option.series.map(function (serie) {
                var arr = [serie.name];
                return arr.concat(serie.data);
            });
        }

        var csvContent = header.join(',');

        seriesData.forEach(function (dataArray) {
            var dataString = dataArray.join(',');
            csvContent += '\r\n' + dataString;
        });

        return csvContent;
    };

    public __exportCSV(chart, filename) {
        // 获取导出的数据
        var data = this.__getChartData(chart);
        // 创建 Blob 对象
        var blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
        // 创建下载链接
        var downloadLink = document.createElement('a');
        downloadLink.setAttribute('download', filename);
        downloadLink.setAttribute('href', URL.createObjectURL(blob));
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        // 触发下载链接
        downloadLink.click();
        // 移除下载链接
        document.body.removeChild(downloadLink);
    };

}

export { WaveChart };

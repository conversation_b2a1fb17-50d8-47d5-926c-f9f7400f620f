@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\git\tvs\tvs_site\web\node_modules\.pnpm\bumpp@9.9.1\node_modules\bumpp\bin\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\bumpp@9.9.1\node_modules\bumpp\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\bumpp@9.9.1\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\git\tvs\tvs_site\web\node_modules\.pnpm\bumpp@9.9.1\node_modules\bumpp\bin\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\bumpp@9.9.1\node_modules\bumpp\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\bumpp@9.9.1\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\bumpp\bin\bumpp.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\bumpp\bin\bumpp.js" %*
)

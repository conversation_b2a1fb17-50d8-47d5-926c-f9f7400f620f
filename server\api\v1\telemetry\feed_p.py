from datetime import datetime
from api.v1.fishery.device.schema import DeviceParamsDTO, Task
from models.fishery.device import DeviceModel


class FeedP_CloudCmd:
    @classmethod
    async def parse_mqtt_cmd(cls, sn_list: list[str], payload_dict: dict):
        func_map = {
            "system": cls._on_cmd_system,
            "lead": cls._on_cmd_lead,
            "set_feed": cls._on_cmd_set_feed,
        }

        if "type" in payload_dict:
            func = func_map.get(payload_dict["type"])
            if func:
                await func(sn_list, payload_dict)

    @classmethod
    async def _update_device_params(cls, sn: str, data: dict):
        device = await DeviceModel.get_or_none(sn=sn)
        if device:
            # 更新 params 和 params_time
            if device.params is None:
                device.params = data
            else:
                device.params = cls.deep_merge(device.params.copy(), data)
            device.params_time = datetime.now()
            await device.save()

    @classmethod
    async def _on_cmd_system(cls, sn_list: list[str], payload_dict: dict):
        data = DeviceParamsDTO(system=payload_dict["data"]).model_dump()
        for sn in sn_list:
            await cls._update_device_params(sn, data)

    @classmethod
    async def _on_cmd_lead(cls, sn_list: list[str], payload_dict: dict):
        data = DeviceParamsDTO(lead=payload_dict["data"]).model_dump()
        for sn in sn_list:
            await cls._update_device_params(sn, data)

    @classmethod
    def _parse_task(cls, data: dict) -> Task:
        params = {"mode": data["mode"]}
    
        if (data["mode"] == "auto") and ("task" in data):
            params[data["mode"]] = data["task"]
            
        elif data["mode"] == "manual":
            params[data["mode"]] = {
                "total_time": data["total_time"],
                "duration": data["duration"],
                "gap_duration": data["gap_duration"],
            }
       
        return Task(**params)

    @classmethod
    async def _on_cmd_set_feed(cls, sn_list: list[str], payload_dict: dict):
        data = DeviceParamsDTO(task=cls._parse_task(payload_dict["data"])).model_dump()
        for sn in sn_list:
            await cls._update_device_params(sn, data)

    @classmethod
    def deep_merge(cls, left: dict, right: dict) -> dict:
        """递归合并字典，right覆盖left"""
        for key in right:
            if right[key] is None:
                continue

            if key in left:
                # 双方都是字典则递归合并
                if isinstance(left[key], dict) and isinstance(right[key], dict):
                    cls.deep_merge(left[key], right[key])
                else:
                    left[key] = right[key]  # 直接覆盖
            else:
                left[key] = right[key]
        return left

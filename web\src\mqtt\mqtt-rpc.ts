
import { useMQTT } from 'mqtt-vue-hook';
import { makeid } from '@/utils/makeid';

const mqttHook = useMQTT();


export class MqttRpc {
    private static _instance: MqttRpc | null = null;
    private callbackMap: Map<string, Function>;

    private constructor() {
        this.callbackMap = new Map();

        mqttHook.subscribe(['rpc/response/#'], 1, {}, () => {
            console.log('rpc subscribed!');
        });

        mqttHook.registerEvent(
            'rpc/response/#',
            (topic, message) => {
                const match = topic.match(/rpc\/response\/(.*)/);
                const rpc_id = match ? match[1] : null;
                if (rpc_id) {
                    const data = JSON.parse(message.toString());
                    const callback = this.callbackMap.get(rpc_id);

                    if (callback) {
                        callback(data.result);
                        this.callbackMap.delete(data.id);
                    }
                }
            },
            'string_key'
        );
    }

    public static get Instance(): MqttRpc {
        return this._instance || (this._instance = new this());
    }

    public publish(topic: string, data: JSON | string, pub_callback: Function, ret_callback: Function) {
        const rpc_id = makeid(10);
        const rpc_data = {
            id: rpc_id,
            data: data
        }

        mqttHook.publish(topic, JSON.stringify(rpc_data), 1, {}, () => {
            console.log('rpc published!');
            if (pub_callback) {
                pub_callback()
            }
            if (ret_callback) {
                this.callbackMap.set(rpc_id, ret_callback);
            }
        });
    }
}



// // 调用 RPC 方法的示例

// MqttRpc.Instance.publish('rpc/call/test', "hello",
//     () => {
//     },
//     (result: any) => {
//         console.log('RPC Result:', result);
//     })



// // 服务端示例
// const rpcServer = mqtt.connect('mqtt://mqtt.eclipse.org');

// rpcServer.subscribe('rpc/request');

// rpcServer.on('message', (topic, message) => {
//     const data = JSON.parse(message.toString());

//     // 模拟服务器端执行 RPC 方法
//     const result = executeRPCMethod(data.method, data.params);

//     // 发送响应
//     const responseData = {
//         id: data.id,
//         result,
//     };

//     rpcServer.publish(`rpc/response/${data.id}`, JSON.stringify(responseData));
// });

// function executeRPCMethod(method: string, params: any[]): any {
//     // 此处可根据实际需求执行对应的 RPC 方法
//     if (method === 'add') {
//         return params.reduce((sum, num) => sum + num, 0);
//     }

//     // 更多方法可根据需要添加

//     return null;
// }





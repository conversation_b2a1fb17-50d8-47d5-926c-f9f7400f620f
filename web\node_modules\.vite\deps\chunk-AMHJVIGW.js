import {
  require_eventemitter3
} from "./chunk-6ODCBTCC.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS,
  __toESM
} from "./chunk-ULBN3QDT.js";

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/reverse.js
function reverse_default(array3, n2) {
  var t2, j2 = array3.length, i2 = j2 - n2;
  while (i2 < --j2) t2 = array3[i2], array3[i2++] = array3[j2], array3[j2] = t2;
}
var init_reverse = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/reverse.js"() {
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/identity.js
function identity_default4(x3) {
  return x3;
}
var init_identity = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/identity.js"() {
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/transform.js
function transform_default2(transform) {
  if (transform == null) return identity_default4;
  var x05, y05, kx = transform.scale[0], ky = transform.scale[1], dx = transform.translate[0], dy = transform.translate[1];
  return function(input, i2) {
    if (!i2) x05 = y05 = 0;
    var j2 = 2, n2 = input.length, output = new Array(n2);
    output[0] = (x05 += input[0]) * kx + dx;
    output[1] = (y05 += input[1]) * ky + dy;
    while (j2 < n2) output[j2] = input[j2], ++j2;
    return output;
  };
}
var init_transform = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/transform.js"() {
    init_identity();
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/feature.js
function feature_default(topology, o2) {
  if (typeof o2 === "string") o2 = topology.objects[o2];
  return o2.type === "GeometryCollection" ? { type: "FeatureCollection", features: o2.geometries.map(function(o3) {
    return feature2(topology, o3);
  }) } : feature2(topology, o2);
}
function feature2(topology, o2) {
  var id = o2.id, bbox = o2.bbox, properties = o2.properties == null ? {} : o2.properties, geometry = object(topology, o2);
  return id == null && bbox == null ? { type: "Feature", properties, geometry } : bbox == null ? { type: "Feature", id, properties, geometry } : { type: "Feature", id, bbox, properties, geometry };
}
function object(topology, o2) {
  var transformPoint = transform_default2(topology.transform), arcs = topology.arcs;
  function arc(i2, points) {
    if (points.length) points.pop();
    for (var a2 = arcs[i2 < 0 ? ~i2 : i2], k2 = 0, n2 = a2.length; k2 < n2; ++k2) {
      points.push(transformPoint(a2[k2], k2));
    }
    if (i2 < 0) reverse_default(points, n2);
  }
  function point2(p2) {
    return transformPoint(p2);
  }
  function line(arcs2) {
    var points = [];
    for (var i2 = 0, n2 = arcs2.length; i2 < n2; ++i2) arc(arcs2[i2], points);
    if (points.length < 2) points.push(points[0]);
    return points;
  }
  function ring(arcs2) {
    var points = line(arcs2);
    while (points.length < 4) points.push(points[0]);
    return points;
  }
  function polygon(arcs2) {
    return arcs2.map(ring);
  }
  function geometry(o3) {
    var type = o3.type, coordinates;
    switch (type) {
      case "GeometryCollection":
        return { type, geometries: o3.geometries.map(geometry) };
      case "Point":
        coordinates = point2(o3.coordinates);
        break;
      case "MultiPoint":
        coordinates = o3.coordinates.map(point2);
        break;
      case "LineString":
        coordinates = line(o3.arcs);
        break;
      case "MultiLineString":
        coordinates = o3.arcs.map(line);
        break;
      case "Polygon":
        coordinates = polygon(o3.arcs);
        break;
      case "MultiPolygon":
        coordinates = o3.arcs.map(polygon);
        break;
      default:
        return null;
    }
    return { type, coordinates };
  }
  return geometry(o2);
}
var init_feature = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/feature.js"() {
    init_reverse();
    init_transform();
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/bbox.js
function bbox_default(topology) {
  var t2 = transform_default2(topology.transform), key, x05 = Infinity, y05 = x05, x13 = -x05, y13 = -x05;
  function bboxPoint(p2) {
    p2 = t2(p2);
    if (p2[0] < x05) x05 = p2[0];
    if (p2[0] > x13) x13 = p2[0];
    if (p2[1] < y05) y05 = p2[1];
    if (p2[1] > y13) y13 = p2[1];
  }
  function bboxGeometry(o2) {
    switch (o2.type) {
      case "GeometryCollection":
        o2.geometries.forEach(bboxGeometry);
        break;
      case "Point":
        bboxPoint(o2.coordinates);
        break;
      case "MultiPoint":
        o2.coordinates.forEach(bboxPoint);
        break;
    }
  }
  topology.arcs.forEach(function(arc) {
    var i2 = -1, n2 = arc.length, p2;
    while (++i2 < n2) {
      p2 = t2(arc[i2], i2);
      if (p2[0] < x05) x05 = p2[0];
      if (p2[0] > x13) x13 = p2[0];
      if (p2[1] < y05) y05 = p2[1];
      if (p2[1] > y13) y13 = p2[1];
    }
  });
  for (key in topology.objects) {
    bboxGeometry(topology.objects[key]);
  }
  return [x05, y05, x13, y13];
}
var init_bbox = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/bbox.js"() {
    init_transform();
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/stitch.js
function stitch_default(topology, arcs) {
  var stitchedArcs = {}, fragmentByStart = {}, fragmentByEnd = {}, fragments = [], emptyIndex = -1;
  arcs.forEach(function(i2, j2) {
    var arc = topology.arcs[i2 < 0 ? ~i2 : i2], t2;
    if (arc.length < 3 && !arc[1][0] && !arc[1][1]) {
      t2 = arcs[++emptyIndex], arcs[emptyIndex] = i2, arcs[j2] = t2;
    }
  });
  arcs.forEach(function(i2) {
    var e3 = ends(i2), start = e3[0], end = e3[1], f2, g3;
    if (f2 = fragmentByEnd[start]) {
      delete fragmentByEnd[f2.end];
      f2.push(i2);
      f2.end = end;
      if (g3 = fragmentByStart[end]) {
        delete fragmentByStart[g3.start];
        var fg = g3 === f2 ? f2 : f2.concat(g3);
        fragmentByStart[fg.start = f2.start] = fragmentByEnd[fg.end = g3.end] = fg;
      } else {
        fragmentByStart[f2.start] = fragmentByEnd[f2.end] = f2;
      }
    } else if (f2 = fragmentByStart[end]) {
      delete fragmentByStart[f2.start];
      f2.unshift(i2);
      f2.start = start;
      if (g3 = fragmentByEnd[start]) {
        delete fragmentByEnd[g3.end];
        var gf = g3 === f2 ? f2 : g3.concat(f2);
        fragmentByStart[gf.start = g3.start] = fragmentByEnd[gf.end = f2.end] = gf;
      } else {
        fragmentByStart[f2.start] = fragmentByEnd[f2.end] = f2;
      }
    } else {
      f2 = [i2];
      fragmentByStart[f2.start = start] = fragmentByEnd[f2.end = end] = f2;
    }
  });
  function ends(i2) {
    var arc = topology.arcs[i2 < 0 ? ~i2 : i2], p0 = arc[0], p1;
    if (topology.transform) p1 = [0, 0], arc.forEach(function(dp) {
      p1[0] += dp[0], p1[1] += dp[1];
    });
    else p1 = arc[arc.length - 1];
    return i2 < 0 ? [p1, p0] : [p0, p1];
  }
  function flush(fragmentByEnd2, fragmentByStart2) {
    for (var k2 in fragmentByEnd2) {
      var f2 = fragmentByEnd2[k2];
      delete fragmentByStart2[f2.start];
      delete f2.start;
      delete f2.end;
      f2.forEach(function(i2) {
        stitchedArcs[i2 < 0 ? ~i2 : i2] = 1;
      });
      fragments.push(f2);
    }
  }
  flush(fragmentByEnd, fragmentByStart);
  flush(fragmentByStart, fragmentByEnd);
  arcs.forEach(function(i2) {
    if (!stitchedArcs[i2 < 0 ? ~i2 : i2]) fragments.push([i2]);
  });
  return fragments;
}
var init_stitch = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/stitch.js"() {
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/mesh.js
function mesh_default(topology) {
  return object(topology, meshArcs.apply(this, arguments));
}
function meshArcs(topology, object2, filter) {
  var arcs, i2, n2;
  if (arguments.length > 1) arcs = extractArcs(topology, object2, filter);
  else for (i2 = 0, arcs = new Array(n2 = topology.arcs.length); i2 < n2; ++i2) arcs[i2] = i2;
  return { type: "MultiLineString", arcs: stitch_default(topology, arcs) };
}
function extractArcs(topology, object2, filter) {
  var arcs = [], geomsByArc = [], geom;
  function extract0(i2) {
    var j2 = i2 < 0 ? ~i2 : i2;
    (geomsByArc[j2] || (geomsByArc[j2] = [])).push({ i: i2, g: geom });
  }
  function extract1(arcs2) {
    arcs2.forEach(extract0);
  }
  function extract2(arcs2) {
    arcs2.forEach(extract1);
  }
  function extract3(arcs2) {
    arcs2.forEach(extract2);
  }
  function geometry(o2) {
    switch (geom = o2, o2.type) {
      case "GeometryCollection":
        o2.geometries.forEach(geometry);
        break;
      case "LineString":
        extract1(o2.arcs);
        break;
      case "MultiLineString":
      case "Polygon":
        extract2(o2.arcs);
        break;
      case "MultiPolygon":
        extract3(o2.arcs);
        break;
    }
  }
  geometry(object2);
  geomsByArc.forEach(filter == null ? function(geoms) {
    arcs.push(geoms[0].i);
  } : function(geoms) {
    if (filter(geoms[0].g, geoms[geoms.length - 1].g)) arcs.push(geoms[0].i);
  });
  return arcs;
}
var init_mesh = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/mesh.js"() {
    init_feature();
    init_stitch();
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/merge.js
function planarRingArea(ring) {
  var i2 = -1, n2 = ring.length, a2, b2 = ring[n2 - 1], area = 0;
  while (++i2 < n2) a2 = b2, b2 = ring[i2], area += a2[0] * b2[1] - a2[1] * b2[0];
  return Math.abs(area);
}
function merge_default2(topology) {
  return object(topology, mergeArcs.apply(this, arguments));
}
function mergeArcs(topology, objects) {
  var polygonsByArc = {}, polygons = [], groups = [];
  objects.forEach(geometry);
  function geometry(o2) {
    switch (o2.type) {
      case "GeometryCollection":
        o2.geometries.forEach(geometry);
        break;
      case "Polygon":
        extract(o2.arcs);
        break;
      case "MultiPolygon":
        o2.arcs.forEach(extract);
        break;
    }
  }
  function extract(polygon) {
    polygon.forEach(function(ring) {
      ring.forEach(function(arc) {
        (polygonsByArc[arc = arc < 0 ? ~arc : arc] || (polygonsByArc[arc] = [])).push(polygon);
      });
    });
    polygons.push(polygon);
  }
  function area(ring) {
    return planarRingArea(object(topology, { type: "Polygon", arcs: [ring] }).coordinates[0]);
  }
  polygons.forEach(function(polygon) {
    if (!polygon._) {
      var group = [], neighbors = [polygon];
      polygon._ = 1;
      groups.push(group);
      while (polygon = neighbors.pop()) {
        group.push(polygon);
        polygon.forEach(function(ring) {
          ring.forEach(function(arc) {
            polygonsByArc[arc < 0 ? ~arc : arc].forEach(function(polygon2) {
              if (!polygon2._) {
                polygon2._ = 1;
                neighbors.push(polygon2);
              }
            });
          });
        });
      }
    }
  });
  polygons.forEach(function(polygon) {
    delete polygon._;
  });
  return {
    type: "MultiPolygon",
    arcs: groups.map(function(polygons2) {
      var arcs = [], n2;
      polygons2.forEach(function(polygon) {
        polygon.forEach(function(ring) {
          ring.forEach(function(arc) {
            if (polygonsByArc[arc < 0 ? ~arc : arc].length < 2) {
              arcs.push(arc);
            }
          });
        });
      });
      arcs = stitch_default(topology, arcs);
      if ((n2 = arcs.length) > 1) {
        for (var i2 = 1, k2 = area(arcs[0]), ki, t2; i2 < n2; ++i2) {
          if ((ki = area(arcs[i2])) > k2) {
            t2 = arcs[0], arcs[0] = arcs[i2], arcs[i2] = t2, k2 = ki;
          }
        }
      }
      return arcs;
    }).filter(function(arcs) {
      return arcs.length > 0;
    })
  };
}
var init_merge = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/merge.js"() {
    init_feature();
    init_stitch();
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/bisect.js
function bisect_default2(a2, x3) {
  var lo = 0, hi = a2.length;
  while (lo < hi) {
    var mid = lo + hi >>> 1;
    if (a2[mid] < x3) lo = mid + 1;
    else hi = mid;
  }
  return lo;
}
var init_bisect = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/bisect.js"() {
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/neighbors.js
function neighbors_default(objects) {
  var indexesByArc = {}, neighbors = objects.map(function() {
    return [];
  });
  function line(arcs, i3) {
    arcs.forEach(function(a2) {
      if (a2 < 0) a2 = ~a2;
      var o2 = indexesByArc[a2];
      if (o2) o2.push(i3);
      else indexesByArc[a2] = [i3];
    });
  }
  function polygon(arcs, i3) {
    arcs.forEach(function(arc) {
      line(arc, i3);
    });
  }
  function geometry(o2, i3) {
    if (o2.type === "GeometryCollection") o2.geometries.forEach(function(o3) {
      geometry(o3, i3);
    });
    else if (o2.type in geometryType) geometryType[o2.type](o2.arcs, i3);
  }
  var geometryType = {
    LineString: line,
    MultiLineString: polygon,
    Polygon: polygon,
    MultiPolygon: function(arcs, i3) {
      arcs.forEach(function(arc) {
        polygon(arc, i3);
      });
    }
  };
  objects.forEach(geometry);
  for (var i2 in indexesByArc) {
    for (var indexes = indexesByArc[i2], m2 = indexes.length, j2 = 0; j2 < m2; ++j2) {
      for (var k2 = j2 + 1; k2 < m2; ++k2) {
        var ij = indexes[j2], ik = indexes[k2], n2;
        if ((n2 = neighbors[ij])[i2 = bisect_default2(n2, ik)] !== ik) n2.splice(i2, 0, ik);
        if ((n2 = neighbors[ik])[i2 = bisect_default2(n2, ij)] !== ij) n2.splice(i2, 0, ij);
      }
    }
  }
  return neighbors;
}
var init_neighbors = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/neighbors.js"() {
    init_bisect();
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/untransform.js
function untransform_default(transform) {
  if (transform == null) return identity_default4;
  var x05, y05, kx = transform.scale[0], ky = transform.scale[1], dx = transform.translate[0], dy = transform.translate[1];
  return function(input, i2) {
    if (!i2) x05 = y05 = 0;
    var j2 = 2, n2 = input.length, output = new Array(n2), x13 = Math.round((input[0] - dx) / kx), y13 = Math.round((input[1] - dy) / ky);
    output[0] = x13 - x05, x05 = x13;
    output[1] = y13 - y05, y05 = y13;
    while (j2 < n2) output[j2] = input[j2], ++j2;
    return output;
  };
}
var init_untransform = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/untransform.js"() {
    init_identity();
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/quantize.js
function quantize_default(topology, transform) {
  if (topology.transform) throw new Error("already quantized");
  if (!transform || !transform.scale) {
    if (!((n2 = Math.floor(transform)) >= 2)) throw new Error("n must be ≥2");
    box = topology.bbox || bbox_default(topology);
    var x05 = box[0], y05 = box[1], x13 = box[2], y13 = box[3], n2;
    transform = { scale: [x13 - x05 ? (x13 - x05) / (n2 - 1) : 1, y13 - y05 ? (y13 - y05) / (n2 - 1) : 1], translate: [x05, y05] };
  } else {
    box = topology.bbox;
  }
  var t2 = untransform_default(transform), box, key, inputs = topology.objects, outputs = {};
  function quantizePoint(point2) {
    return t2(point2);
  }
  function quantizeGeometry(input) {
    var output;
    switch (input.type) {
      case "GeometryCollection":
        output = { type: "GeometryCollection", geometries: input.geometries.map(quantizeGeometry) };
        break;
      case "Point":
        output = { type: "Point", coordinates: quantizePoint(input.coordinates) };
        break;
      case "MultiPoint":
        output = { type: "MultiPoint", coordinates: input.coordinates.map(quantizePoint) };
        break;
      default:
        return input;
    }
    if (input.id != null) output.id = input.id;
    if (input.bbox != null) output.bbox = input.bbox;
    if (input.properties != null) output.properties = input.properties;
    return output;
  }
  function quantizeArc(input) {
    var i2 = 0, j2 = 1, n3 = input.length, p2, output = new Array(n3);
    output[0] = t2(input[0], 0);
    while (++i2 < n3) if ((p2 = t2(input[i2], i2))[0] || p2[1]) output[j2++] = p2;
    if (j2 === 1) output[j2++] = [0, 0];
    output.length = j2;
    return output;
  }
  for (key in inputs) outputs[key] = quantizeGeometry(inputs[key]);
  return {
    type: "Topology",
    bbox: box,
    transform,
    objects: outputs,
    arcs: topology.arcs.map(quantizeArc)
  };
}
var init_quantize = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/quantize.js"() {
    init_bbox();
    init_untransform();
  }
});

// node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/index.js
var src_exports = {};
__export(src_exports, {
  bbox: () => bbox_default,
  feature: () => feature_default,
  merge: () => merge_default2,
  mergeArcs: () => mergeArcs,
  mesh: () => mesh_default,
  meshArcs: () => meshArcs,
  neighbors: () => neighbors_default,
  quantize: () => quantize_default,
  transform: () => transform_default2,
  untransform: () => untransform_default
});
var init_src = __esm({
  "node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/index.js"() {
    init_bbox();
    init_feature();
    init_mesh();
    init_merge();
    init_neighbors();
    init_quantize();
    init_transform();
    init_untransform();
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/object.js
var hasOwnProperty3;
var init_object = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/object.js"() {
    hasOwnProperty3 = Object.prototype.hasOwnProperty;
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/bounds.js
function bounds_default3(objects) {
  var x05 = Infinity, y05 = Infinity, x13 = -Infinity, y13 = -Infinity;
  function boundGeometry(geometry) {
    if (geometry != null && hasOwnProperty3.call(boundGeometryType, geometry.type)) boundGeometryType[geometry.type](geometry);
  }
  var boundGeometryType = {
    GeometryCollection: function(o2) {
      o2.geometries.forEach(boundGeometry);
    },
    Point: function(o2) {
      boundPoint(o2.coordinates);
    },
    MultiPoint: function(o2) {
      o2.coordinates.forEach(boundPoint);
    },
    LineString: function(o2) {
      boundLine(o2.arcs);
    },
    MultiLineString: function(o2) {
      o2.arcs.forEach(boundLine);
    },
    Polygon: function(o2) {
      o2.arcs.forEach(boundLine);
    },
    MultiPolygon: function(o2) {
      o2.arcs.forEach(boundMultiLine);
    }
  };
  function boundPoint(coordinates) {
    var x3 = coordinates[0], y3 = coordinates[1];
    if (x3 < x05) x05 = x3;
    if (x3 > x13) x13 = x3;
    if (y3 < y05) y05 = y3;
    if (y3 > y13) y13 = y3;
  }
  function boundLine(coordinates) {
    coordinates.forEach(boundPoint);
  }
  function boundMultiLine(coordinates) {
    coordinates.forEach(boundLine);
  }
  for (var key in objects) {
    boundGeometry(objects[key]);
  }
  return x13 >= x05 && y13 >= y05 ? [x05, y05, x13, y13] : void 0;
}
var init_bounds = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/bounds.js"() {
    init_object();
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/hash/hashset.js
function hashset_default(size, hash, equal, type, empty) {
  if (arguments.length === 3) {
    type = Array;
    empty = null;
  }
  var store = new type(size = 1 << Math.max(4, Math.ceil(Math.log(size) / Math.LN2))), mask = size - 1;
  for (var i2 = 0; i2 < size; ++i2) {
    store[i2] = empty;
  }
  function add2(value) {
    var index = hash(value) & mask, match = store[index], collisions = 0;
    while (match != empty) {
      if (equal(match, value)) return true;
      if (++collisions >= size) throw new Error("full hashset");
      match = store[index = index + 1 & mask];
    }
    store[index] = value;
    return true;
  }
  function has2(value) {
    var index = hash(value) & mask, match = store[index], collisions = 0;
    while (match != empty) {
      if (equal(match, value)) return true;
      if (++collisions >= size) break;
      match = store[index = index + 1 & mask];
    }
    return false;
  }
  function values() {
    var values2 = [];
    for (var i3 = 0, n2 = store.length; i3 < n2; ++i3) {
      var match = store[i3];
      if (match != empty) values2.push(match);
    }
    return values2;
  }
  return {
    add: add2,
    has: has2,
    values
  };
}
var init_hashset = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/hash/hashset.js"() {
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/hash/hashmap.js
function hashmap_default(size, hash, equal, keyType, keyEmpty, valueType) {
  if (arguments.length === 3) {
    keyType = valueType = Array;
    keyEmpty = null;
  }
  var keystore = new keyType(size = 1 << Math.max(4, Math.ceil(Math.log(size) / Math.LN2))), valstore = new valueType(size), mask = size - 1;
  for (var i2 = 0; i2 < size; ++i2) {
    keystore[i2] = keyEmpty;
  }
  function set(key, value) {
    var index = hash(key) & mask, matchKey = keystore[index], collisions = 0;
    while (matchKey != keyEmpty) {
      if (equal(matchKey, key)) return valstore[index] = value;
      if (++collisions >= size) throw new Error("full hashmap");
      matchKey = keystore[index = index + 1 & mask];
    }
    keystore[index] = key;
    valstore[index] = value;
    return value;
  }
  function maybeSet(key, value) {
    var index = hash(key) & mask, matchKey = keystore[index], collisions = 0;
    while (matchKey != keyEmpty) {
      if (equal(matchKey, key)) return valstore[index];
      if (++collisions >= size) throw new Error("full hashmap");
      matchKey = keystore[index = index + 1 & mask];
    }
    keystore[index] = key;
    valstore[index] = value;
    return value;
  }
  function get2(key, missingValue) {
    var index = hash(key) & mask, matchKey = keystore[index], collisions = 0;
    while (matchKey != keyEmpty) {
      if (equal(matchKey, key)) return valstore[index];
      if (++collisions >= size) break;
      matchKey = keystore[index = index + 1 & mask];
    }
    return missingValue;
  }
  function keys2() {
    var keys3 = [];
    for (var i3 = 0, n2 = keystore.length; i3 < n2; ++i3) {
      var matchKey = keystore[i3];
      if (matchKey != keyEmpty) keys3.push(matchKey);
    }
    return keys3;
  }
  return {
    set,
    maybeSet,
    // set if unset
    get: get2,
    keys: keys2
  };
}
var init_hashmap = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/hash/hashmap.js"() {
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/hash/point-equal.js
function point_equal_default(pointA, pointB) {
  return pointA[0] === pointB[0] && pointA[1] === pointB[1];
}
var init_point_equal = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/hash/point-equal.js"() {
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/hash/point-hash.js
function point_hash_default(point2) {
  floats[0] = point2[0];
  floats[1] = point2[1];
  var hash = uints[0] ^ uints[1];
  hash = hash << 5 ^ hash >> 7 ^ uints[2] ^ uints[3];
  return hash & 2147483647;
}
var buffer, floats, uints;
var init_point_hash = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/hash/point-hash.js"() {
    buffer = new ArrayBuffer(16);
    floats = new Float64Array(buffer);
    uints = new Uint32Array(buffer);
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/join.js
function join_default(topology) {
  var coordinates = topology.coordinates, lines = topology.lines, rings = topology.rings, indexes = index(), visitedByIndex = new Int32Array(coordinates.length), leftByIndex = new Int32Array(coordinates.length), rightByIndex = new Int32Array(coordinates.length), junctionByIndex = new Int8Array(coordinates.length), junctionCount = 0, i2, n2, previousIndex, currentIndex, nextIndex;
  for (i2 = 0, n2 = coordinates.length; i2 < n2; ++i2) {
    visitedByIndex[i2] = leftByIndex[i2] = rightByIndex[i2] = -1;
  }
  for (i2 = 0, n2 = lines.length; i2 < n2; ++i2) {
    var line = lines[i2], lineStart = line[0], lineEnd = line[1];
    currentIndex = indexes[lineStart];
    nextIndex = indexes[++lineStart];
    ++junctionCount, junctionByIndex[currentIndex] = 1;
    while (++lineStart <= lineEnd) {
      sequence(i2, previousIndex = currentIndex, currentIndex = nextIndex, nextIndex = indexes[lineStart]);
    }
    ++junctionCount, junctionByIndex[nextIndex] = 1;
  }
  for (i2 = 0, n2 = coordinates.length; i2 < n2; ++i2) {
    visitedByIndex[i2] = -1;
  }
  for (i2 = 0, n2 = rings.length; i2 < n2; ++i2) {
    var ring = rings[i2], ringStart = ring[0] + 1, ringEnd = ring[1];
    previousIndex = indexes[ringEnd - 1];
    currentIndex = indexes[ringStart - 1];
    nextIndex = indexes[ringStart];
    sequence(i2, previousIndex, currentIndex, nextIndex);
    while (++ringStart <= ringEnd) {
      sequence(i2, previousIndex = currentIndex, currentIndex = nextIndex, nextIndex = indexes[ringStart]);
    }
  }
  function sequence(i3, previousIndex2, currentIndex2, nextIndex2) {
    if (visitedByIndex[currentIndex2] === i3) return;
    visitedByIndex[currentIndex2] = i3;
    var leftIndex = leftByIndex[currentIndex2];
    if (leftIndex >= 0) {
      var rightIndex = rightByIndex[currentIndex2];
      if ((leftIndex !== previousIndex2 || rightIndex !== nextIndex2) && (leftIndex !== nextIndex2 || rightIndex !== previousIndex2)) {
        ++junctionCount, junctionByIndex[currentIndex2] = 1;
      }
    } else {
      leftByIndex[currentIndex2] = previousIndex2;
      rightByIndex[currentIndex2] = nextIndex2;
    }
  }
  function index() {
    var indexByPoint = hashmap_default(coordinates.length * 1.4, hashIndex, equalIndex, Int32Array, -1, Int32Array), indexes2 = new Int32Array(coordinates.length);
    for (var i3 = 0, n3 = coordinates.length; i3 < n3; ++i3) {
      indexes2[i3] = indexByPoint.maybeSet(i3, i3);
    }
    return indexes2;
  }
  function hashIndex(i3) {
    return point_hash_default(coordinates[i3]);
  }
  function equalIndex(i3, j3) {
    return point_equal_default(coordinates[i3], coordinates[j3]);
  }
  visitedByIndex = leftByIndex = rightByIndex = null;
  var junctionByPoint = hashset_default(junctionCount * 1.4, point_hash_default, point_equal_default), j2;
  for (i2 = 0, n2 = coordinates.length; i2 < n2; ++i2) {
    if (junctionByIndex[j2 = indexes[i2]]) {
      junctionByPoint.add(coordinates[j2]);
    }
  }
  return junctionByPoint;
}
var init_join = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/join.js"() {
    init_hashset();
    init_hashmap();
    init_point_equal();
    init_point_hash();
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/cut.js
function cut_default(topology) {
  var junctions = join_default(topology), coordinates = topology.coordinates, lines = topology.lines, rings = topology.rings, next, i2, n2;
  for (i2 = 0, n2 = lines.length; i2 < n2; ++i2) {
    var line = lines[i2], lineMid = line[0], lineEnd = line[1];
    while (++lineMid < lineEnd) {
      if (junctions.has(coordinates[lineMid])) {
        next = { 0: lineMid, 1: line[1] };
        line[1] = lineMid;
        line = line.next = next;
      }
    }
  }
  for (i2 = 0, n2 = rings.length; i2 < n2; ++i2) {
    var ring = rings[i2], ringStart = ring[0], ringMid = ringStart, ringEnd = ring[1], ringFixed = junctions.has(coordinates[ringStart]);
    while (++ringMid < ringEnd) {
      if (junctions.has(coordinates[ringMid])) {
        if (ringFixed) {
          next = { 0: ringMid, 1: ring[1] };
          ring[1] = ringMid;
          ring = ring.next = next;
        } else {
          rotateArray(coordinates, ringStart, ringEnd, ringEnd - ringMid);
          coordinates[ringEnd] = coordinates[ringStart];
          ringFixed = true;
          ringMid = ringStart;
        }
      }
    }
  }
  return topology;
}
function rotateArray(array3, start, end, offset) {
  reverse(array3, start, end);
  reverse(array3, start, start + offset);
  reverse(array3, start + offset, end);
}
function reverse(array3, start, end) {
  for (var mid = start + (end-- - start >> 1), t2; start < mid; ++start, --end) {
    t2 = array3[start], array3[start] = array3[end], array3[end] = t2;
  }
}
var init_cut = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/cut.js"() {
    init_join();
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/dedup.js
function dedup_default(topology) {
  var coordinates = topology.coordinates, lines = topology.lines, line, rings = topology.rings, ring, arcCount = lines.length + rings.length, i2, n2;
  delete topology.lines;
  delete topology.rings;
  for (i2 = 0, n2 = lines.length; i2 < n2; ++i2) {
    line = lines[i2];
    while (line = line.next) ++arcCount;
  }
  for (i2 = 0, n2 = rings.length; i2 < n2; ++i2) {
    ring = rings[i2];
    while (ring = ring.next) ++arcCount;
  }
  var arcsByEnd = hashmap_default(arcCount * 2 * 1.4, point_hash_default, point_equal_default), arcs = topology.arcs = [];
  for (i2 = 0, n2 = lines.length; i2 < n2; ++i2) {
    line = lines[i2];
    do {
      dedupLine(line);
    } while (line = line.next);
  }
  for (i2 = 0, n2 = rings.length; i2 < n2; ++i2) {
    ring = rings[i2];
    if (ring.next) {
      do {
        dedupLine(ring);
      } while (ring = ring.next);
    } else {
      dedupRing(ring);
    }
  }
  function dedupLine(arc) {
    var startPoint, endPoint, startArcs, startArc, endArcs, endArc, i3, n3;
    if (startArcs = arcsByEnd.get(startPoint = coordinates[arc[0]])) {
      for (i3 = 0, n3 = startArcs.length; i3 < n3; ++i3) {
        startArc = startArcs[i3];
        if (equalLine(startArc, arc)) {
          arc[0] = startArc[0];
          arc[1] = startArc[1];
          return;
        }
      }
    }
    if (endArcs = arcsByEnd.get(endPoint = coordinates[arc[1]])) {
      for (i3 = 0, n3 = endArcs.length; i3 < n3; ++i3) {
        endArc = endArcs[i3];
        if (reverseEqualLine(endArc, arc)) {
          arc[1] = endArc[0];
          arc[0] = endArc[1];
          return;
        }
      }
    }
    if (startArcs) startArcs.push(arc);
    else arcsByEnd.set(startPoint, [arc]);
    if (endArcs) endArcs.push(arc);
    else arcsByEnd.set(endPoint, [arc]);
    arcs.push(arc);
  }
  function dedupRing(arc) {
    var endPoint, endArcs, endArc, i3, n3;
    if (endArcs = arcsByEnd.get(endPoint = coordinates[arc[0]])) {
      for (i3 = 0, n3 = endArcs.length; i3 < n3; ++i3) {
        endArc = endArcs[i3];
        if (equalRing(endArc, arc)) {
          arc[0] = endArc[0];
          arc[1] = endArc[1];
          return;
        }
        if (reverseEqualRing(endArc, arc)) {
          arc[0] = endArc[1];
          arc[1] = endArc[0];
          return;
        }
      }
    }
    if (endArcs = arcsByEnd.get(endPoint = coordinates[arc[0] + findMinimumOffset(arc)])) {
      for (i3 = 0, n3 = endArcs.length; i3 < n3; ++i3) {
        endArc = endArcs[i3];
        if (equalRing(endArc, arc)) {
          arc[0] = endArc[0];
          arc[1] = endArc[1];
          return;
        }
        if (reverseEqualRing(endArc, arc)) {
          arc[0] = endArc[1];
          arc[1] = endArc[0];
          return;
        }
      }
    }
    if (endArcs) endArcs.push(arc);
    else arcsByEnd.set(endPoint, [arc]);
    arcs.push(arc);
  }
  function equalLine(arcA, arcB) {
    var ia = arcA[0], ib = arcB[0], ja = arcA[1], jb = arcB[1];
    if (ia - ja !== ib - jb) return false;
    for (; ia <= ja; ++ia, ++ib) if (!point_equal_default(coordinates[ia], coordinates[ib])) return false;
    return true;
  }
  function reverseEqualLine(arcA, arcB) {
    var ia = arcA[0], ib = arcB[0], ja = arcA[1], jb = arcB[1];
    if (ia - ja !== ib - jb) return false;
    for (; ia <= ja; ++ia, --jb) if (!point_equal_default(coordinates[ia], coordinates[jb])) return false;
    return true;
  }
  function equalRing(arcA, arcB) {
    var ia = arcA[0], ib = arcB[0], ja = arcA[1], jb = arcB[1], n3 = ja - ia;
    if (n3 !== jb - ib) return false;
    var ka = findMinimumOffset(arcA), kb = findMinimumOffset(arcB);
    for (var i3 = 0; i3 < n3; ++i3) {
      if (!point_equal_default(coordinates[ia + (i3 + ka) % n3], coordinates[ib + (i3 + kb) % n3])) return false;
    }
    return true;
  }
  function reverseEqualRing(arcA, arcB) {
    var ia = arcA[0], ib = arcB[0], ja = arcA[1], jb = arcB[1], n3 = ja - ia;
    if (n3 !== jb - ib) return false;
    var ka = findMinimumOffset(arcA), kb = n3 - findMinimumOffset(arcB);
    for (var i3 = 0; i3 < n3; ++i3) {
      if (!point_equal_default(coordinates[ia + (i3 + ka) % n3], coordinates[jb - (i3 + kb) % n3])) return false;
    }
    return true;
  }
  function findMinimumOffset(arc) {
    var start = arc[0], end = arc[1], mid = start, minimum = mid, minimumPoint = coordinates[mid];
    while (++mid < end) {
      var point2 = coordinates[mid];
      if (point2[0] < minimumPoint[0] || point2[0] === minimumPoint[0] && point2[1] < minimumPoint[1]) {
        minimum = mid;
        minimumPoint = point2;
      }
    }
    return minimum - start;
  }
  return topology;
}
var init_dedup = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/dedup.js"() {
    init_hashmap();
    init_point_equal();
    init_point_hash();
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/delta.js
function delta_default(arcs) {
  var i2 = -1, n2 = arcs.length;
  while (++i2 < n2) {
    var arc = arcs[i2], j2 = 0, k2 = 1, m2 = arc.length, point2 = arc[0], x05 = point2[0], y05 = point2[1], x13, y13;
    while (++j2 < m2) {
      point2 = arc[j2], x13 = point2[0], y13 = point2[1];
      if (x13 !== x05 || y13 !== y05) arc[k2++] = [x13 - x05, y13 - y05], x05 = x13, y05 = y13;
    }
    if (k2 === 1) arc[k2++] = [0, 0];
    arc.length = k2;
  }
  return arcs;
}
var init_delta = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/delta.js"() {
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/extract.js
function extract_default(objects) {
  var index = -1, lines = [], rings = [], coordinates = [];
  function extractGeometry(geometry) {
    if (geometry && hasOwnProperty3.call(extractGeometryType, geometry.type)) extractGeometryType[geometry.type](geometry);
  }
  var extractGeometryType = {
    GeometryCollection: function(o2) {
      o2.geometries.forEach(extractGeometry);
    },
    LineString: function(o2) {
      o2.arcs = extractLine(o2.arcs);
    },
    MultiLineString: function(o2) {
      o2.arcs = o2.arcs.map(extractLine);
    },
    Polygon: function(o2) {
      o2.arcs = o2.arcs.map(extractRing);
    },
    MultiPolygon: function(o2) {
      o2.arcs = o2.arcs.map(extractMultiRing);
    }
  };
  function extractLine(line) {
    for (var i2 = 0, n2 = line.length; i2 < n2; ++i2) coordinates[++index] = line[i2];
    var arc = { 0: index - n2 + 1, 1: index };
    lines.push(arc);
    return arc;
  }
  function extractRing(ring) {
    for (var i2 = 0, n2 = ring.length; i2 < n2; ++i2) coordinates[++index] = ring[i2];
    var arc = { 0: index - n2 + 1, 1: index };
    rings.push(arc);
    return arc;
  }
  function extractMultiRing(rings2) {
    return rings2.map(extractRing);
  }
  for (var key in objects) {
    extractGeometry(objects[key]);
  }
  return {
    type: "Topology",
    coordinates,
    lines,
    rings,
    objects
  };
}
var init_extract = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/extract.js"() {
    init_object();
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/geometry.js
function geometry_default(inputs) {
  var outputs = {}, key;
  for (key in inputs) outputs[key] = geomifyObject(inputs[key]);
  return outputs;
}
function geomifyObject(input) {
  return input == null ? { type: null } : (input.type === "FeatureCollection" ? geomifyFeatureCollection : input.type === "Feature" ? geomifyFeature : geomifyGeometry)(input);
}
function geomifyFeatureCollection(input) {
  var output = { type: "GeometryCollection", geometries: input.features.map(geomifyFeature) };
  if (input.bbox != null) output.bbox = input.bbox;
  return output;
}
function geomifyFeature(input) {
  var output = geomifyGeometry(input.geometry), key;
  if (input.id != null) output.id = input.id;
  if (input.bbox != null) output.bbox = input.bbox;
  for (key in input.properties) {
    output.properties = input.properties;
    break;
  }
  return output;
}
function geomifyGeometry(input) {
  if (input == null) return { type: null };
  var output = input.type === "GeometryCollection" ? { type: "GeometryCollection", geometries: input.geometries.map(geomifyGeometry) } : input.type === "Point" || input.type === "MultiPoint" ? { type: input.type, coordinates: input.coordinates } : { type: input.type, arcs: input.coordinates };
  if (input.bbox != null) output.bbox = input.bbox;
  return output;
}
var init_geometry = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/geometry.js"() {
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/prequantize.js
function prequantize_default(objects, bbox, n2) {
  var x05 = bbox[0], y05 = bbox[1], x13 = bbox[2], y13 = bbox[3], kx = x13 - x05 ? (n2 - 1) / (x13 - x05) : 1, ky = y13 - y05 ? (n2 - 1) / (y13 - y05) : 1;
  function quantizePoint(input) {
    return [Math.round((input[0] - x05) * kx), Math.round((input[1] - y05) * ky)];
  }
  function quantizePoints(input, m2) {
    var i2 = -1, j2 = 0, n3 = input.length, output = new Array(n3), pi4, px, py, x3, y3;
    while (++i2 < n3) {
      pi4 = input[i2];
      x3 = Math.round((pi4[0] - x05) * kx);
      y3 = Math.round((pi4[1] - y05) * ky);
      if (x3 !== px || y3 !== py) output[j2++] = [px = x3, py = y3];
    }
    output.length = j2;
    while (j2 < m2) j2 = output.push([output[0][0], output[0][1]]);
    return output;
  }
  function quantizeLine(input) {
    return quantizePoints(input, 2);
  }
  function quantizeRing(input) {
    return quantizePoints(input, 4);
  }
  function quantizePolygon(input) {
    return input.map(quantizeRing);
  }
  function quantizeGeometry(o2) {
    if (o2 != null && hasOwnProperty3.call(quantizeGeometryType, o2.type)) quantizeGeometryType[o2.type](o2);
  }
  var quantizeGeometryType = {
    GeometryCollection: function(o2) {
      o2.geometries.forEach(quantizeGeometry);
    },
    Point: function(o2) {
      o2.coordinates = quantizePoint(o2.coordinates);
    },
    MultiPoint: function(o2) {
      o2.coordinates = o2.coordinates.map(quantizePoint);
    },
    LineString: function(o2) {
      o2.arcs = quantizeLine(o2.arcs);
    },
    MultiLineString: function(o2) {
      o2.arcs = o2.arcs.map(quantizeLine);
    },
    Polygon: function(o2) {
      o2.arcs = quantizePolygon(o2.arcs);
    },
    MultiPolygon: function(o2) {
      o2.arcs = o2.arcs.map(quantizePolygon);
    }
  };
  for (var key in objects) {
    quantizeGeometry(objects[key]);
  }
  return {
    scale: [1 / kx, 1 / ky],
    translate: [x05, y05]
  };
}
var init_prequantize = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/prequantize.js"() {
    init_object();
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/topology.js
function topology_default(objects, quantization) {
  var bbox = bounds_default3(objects = geometry_default(objects)), transform = quantization > 0 && bbox && prequantize_default(objects, bbox, quantization), topology = dedup_default(cut_default(extract_default(objects))), coordinates = topology.coordinates, indexByArc = hashmap_default(topology.arcs.length * 1.4, hashArc, equalArc);
  objects = topology.objects;
  topology.bbox = bbox;
  topology.arcs = topology.arcs.map(function(arc, i2) {
    indexByArc.set(arc, i2);
    return coordinates.slice(arc[0], arc[1] + 1);
  });
  delete topology.coordinates;
  coordinates = null;
  function indexGeometry(geometry) {
    if (geometry && hasOwnProperty3.call(indexGeometryType, geometry.type)) indexGeometryType[geometry.type](geometry);
  }
  var indexGeometryType = {
    GeometryCollection: function(o2) {
      o2.geometries.forEach(indexGeometry);
    },
    LineString: function(o2) {
      o2.arcs = indexArcs(o2.arcs);
    },
    MultiLineString: function(o2) {
      o2.arcs = o2.arcs.map(indexArcs);
    },
    Polygon: function(o2) {
      o2.arcs = o2.arcs.map(indexArcs);
    },
    MultiPolygon: function(o2) {
      o2.arcs = o2.arcs.map(indexMultiArcs);
    }
  };
  function indexArcs(arc) {
    var indexes = [];
    do {
      var index = indexByArc.get(arc);
      indexes.push(arc[0] < arc[1] ? index : ~index);
    } while (arc = arc.next);
    return indexes;
  }
  function indexMultiArcs(arcs) {
    return arcs.map(indexArcs);
  }
  for (var key in objects) {
    indexGeometry(objects[key]);
  }
  if (transform) {
    topology.transform = transform;
    topology.arcs = delta_default(topology.arcs);
  }
  return topology;
}
function hashArc(arc) {
  var i2 = arc[0], j2 = arc[1], t2;
  if (j2 < i2) t2 = i2, i2 = j2, j2 = t2;
  return i2 + 31 * j2;
}
function equalArc(arcA, arcB) {
  var ia = arcA[0], ja = arcA[1], ib = arcB[0], jb = arcB[1], t2;
  if (ja < ia) t2 = ia, ia = ja, ja = t2;
  if (jb < ib) t2 = ib, ib = jb, jb = t2;
  return ia === ib && ja === jb;
}
var init_topology = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/topology.js"() {
    init_bounds();
    init_cut();
    init_dedup();
    init_delta();
    init_extract();
    init_geometry();
    init_hashmap();
    init_object();
    init_prequantize();
  }
});

// node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/index.js
var src_exports2 = {};
__export(src_exports2, {
  topology: () => topology_default
});
var init_src2 = __esm({
  "node_modules/.pnpm/topojson-server@3.0.1/node_modules/topojson-server/src/index.js"() {
    init_topology();
  }
});

// node_modules/.pnpm/geojson-linestring-dissolve@0.0.1/node_modules/geojson-linestring-dissolve/index.js
var require_geojson_linestring_dissolve = __commonJS({
  "node_modules/.pnpm/geojson-linestring-dissolve@0.0.1/node_modules/geojson-linestring-dissolve/index.js"(exports, module) {
    module.exports = mergeViableLineStrings;
    function coordId(coord) {
      return coord[0].toString() + "," + coord[1].toString();
    }
    function mergeLineStrings(a2, b2) {
      var s1 = coordId(a2.coordinates[0]);
      var e1 = coordId(a2.coordinates[a2.coordinates.length - 1]);
      var s2 = coordId(b2.coordinates[0]);
      var e24 = coordId(b2.coordinates[b2.coordinates.length - 1]);
      var coords;
      if (s1 === e24) {
        coords = b2.coordinates.concat(a2.coordinates.slice(1));
      } else if (s2 === e1) {
        coords = a2.coordinates.concat(b2.coordinates.slice(1));
      } else if (s1 === s2) {
        coords = a2.coordinates.slice(1).reverse().concat(b2.coordinates);
      } else if (e1 === e24) {
        coords = a2.coordinates.concat(b2.coordinates.reverse().slice(1));
      } else {
        return null;
      }
      return {
        type: "LineString",
        coordinates: coords
      };
    }
    function mergeViableLineStrings(geoms) {
      var lineStrings = geoms.slice();
      var result = [];
      while (lineStrings.length > 0) {
        var ls = lineStrings.shift();
        lineStrings = lineStrings.reduce(function(accum, cur) {
          var merged = mergeLineStrings(ls, cur);
          if (merged) {
            ls = merged;
          } else {
            accum.push(cur);
          }
          return accum;
        }, []);
        result.push(ls);
      }
      if (result.length === 1) {
        result = result[0];
      } else {
        result = {
          type: "MultiLineString",
          coordinates: result.map(function(ls2) {
            return ls2.coordinates;
          })
        };
      }
      return result;
    }
  }
});

// node_modules/.pnpm/@turf+meta@3.14.0/node_modules/@turf/meta/index.js
var require_meta = __commonJS({
  "node_modules/.pnpm/@turf+meta@3.14.0/node_modules/@turf/meta/index.js"(exports, module) {
    function coordEach(layer, callback, excludeWrapCoord) {
      var i2, j2, k2, g3, l2, geometry, stopG, coords, geometryMaybeCollection, wrapShrink = 0, currentIndex = 0, isGeometryCollection, isFeatureCollection = layer.type === "FeatureCollection", isFeature = layer.type === "Feature", stop = isFeatureCollection ? layer.features.length : 1;
      for (i2 = 0; i2 < stop; i2++) {
        geometryMaybeCollection = isFeatureCollection ? layer.features[i2].geometry : isFeature ? layer.geometry : layer;
        isGeometryCollection = geometryMaybeCollection.type === "GeometryCollection";
        stopG = isGeometryCollection ? geometryMaybeCollection.geometries.length : 1;
        for (g3 = 0; g3 < stopG; g3++) {
          geometry = isGeometryCollection ? geometryMaybeCollection.geometries[g3] : geometryMaybeCollection;
          coords = geometry.coordinates;
          wrapShrink = excludeWrapCoord && (geometry.type === "Polygon" || geometry.type === "MultiPolygon") ? 1 : 0;
          if (geometry.type === "Point") {
            callback(coords, currentIndex);
            currentIndex++;
          } else if (geometry.type === "LineString" || geometry.type === "MultiPoint") {
            for (j2 = 0; j2 < coords.length; j2++) {
              callback(coords[j2], currentIndex);
              currentIndex++;
            }
          } else if (geometry.type === "Polygon" || geometry.type === "MultiLineString") {
            for (j2 = 0; j2 < coords.length; j2++)
              for (k2 = 0; k2 < coords[j2].length - wrapShrink; k2++) {
                callback(coords[j2][k2], currentIndex);
                currentIndex++;
              }
          } else if (geometry.type === "MultiPolygon") {
            for (j2 = 0; j2 < coords.length; j2++)
              for (k2 = 0; k2 < coords[j2].length; k2++)
                for (l2 = 0; l2 < coords[j2][k2].length - wrapShrink; l2++) {
                  callback(coords[j2][k2][l2], currentIndex);
                  currentIndex++;
                }
          } else if (geometry.type === "GeometryCollection") {
            for (j2 = 0; j2 < geometry.geometries.length; j2++)
              coordEach(geometry.geometries[j2], callback, excludeWrapCoord);
          } else {
            throw new Error("Unknown Geometry Type");
          }
        }
      }
    }
    module.exports.coordEach = coordEach;
    function coordReduce(layer, callback, initialValue, excludeWrapCoord) {
      var previousValue = initialValue;
      coordEach(layer, function(currentCoords, currentIndex) {
        if (currentIndex === 0 && initialValue === void 0) {
          previousValue = currentCoords;
        } else {
          previousValue = callback(previousValue, currentCoords, currentIndex);
        }
      }, excludeWrapCoord);
      return previousValue;
    }
    module.exports.coordReduce = coordReduce;
    function propEach(layer, callback) {
      var i2;
      switch (layer.type) {
        case "FeatureCollection":
          for (i2 = 0; i2 < layer.features.length; i2++) {
            callback(layer.features[i2].properties, i2);
          }
          break;
        case "Feature":
          callback(layer.properties, 0);
          break;
      }
    }
    module.exports.propEach = propEach;
    function propReduce(layer, callback, initialValue) {
      var previousValue = initialValue;
      propEach(layer, function(currentProperties, currentIndex) {
        if (currentIndex === 0 && initialValue === void 0) {
          previousValue = currentProperties;
        } else {
          previousValue = callback(previousValue, currentProperties, currentIndex);
        }
      });
      return previousValue;
    }
    module.exports.propReduce = propReduce;
    function featureEach2(layer, callback) {
      if (layer.type === "Feature") {
        callback(layer, 0);
      } else if (layer.type === "FeatureCollection") {
        for (var i2 = 0; i2 < layer.features.length; i2++) {
          callback(layer.features[i2], i2);
        }
      }
    }
    module.exports.featureEach = featureEach2;
    function featureReduce(layer, callback, initialValue) {
      var previousValue = initialValue;
      featureEach2(layer, function(currentFeature, currentIndex) {
        if (currentIndex === 0 && initialValue === void 0) {
          previousValue = currentFeature;
        } else {
          previousValue = callback(previousValue, currentFeature, currentIndex);
        }
      });
      return previousValue;
    }
    module.exports.featureReduce = featureReduce;
    function coordAll(layer) {
      var coords = [];
      coordEach(layer, function(coord) {
        coords.push(coord);
      });
      return coords;
    }
    module.exports.coordAll = coordAll;
    function geomEach2(layer, callback) {
      var i2, j2, g3, geometry, stopG, geometryMaybeCollection, isGeometryCollection, currentIndex = 0, isFeatureCollection = layer.type === "FeatureCollection", isFeature = layer.type === "Feature", stop = isFeatureCollection ? layer.features.length : 1;
      for (i2 = 0; i2 < stop; i2++) {
        geometryMaybeCollection = isFeatureCollection ? layer.features[i2].geometry : isFeature ? layer.geometry : layer;
        isGeometryCollection = geometryMaybeCollection.type === "GeometryCollection";
        stopG = isGeometryCollection ? geometryMaybeCollection.geometries.length : 1;
        for (g3 = 0; g3 < stopG; g3++) {
          geometry = isGeometryCollection ? geometryMaybeCollection.geometries[g3] : geometryMaybeCollection;
          if (geometry.type === "Point" || geometry.type === "LineString" || geometry.type === "MultiPoint" || geometry.type === "Polygon" || geometry.type === "MultiLineString" || geometry.type === "MultiPolygon") {
            callback(geometry, currentIndex);
            currentIndex++;
          } else if (geometry.type === "GeometryCollection") {
            for (j2 = 0; j2 < geometry.geometries.length; j2++) {
              callback(geometry.geometries[j2], currentIndex);
              currentIndex++;
            }
          } else {
            throw new Error("Unknown Geometry Type");
          }
        }
      }
    }
    module.exports.geomEach = geomEach2;
    function geomReduce(layer, callback, initialValue) {
      var previousValue = initialValue;
      geomEach2(layer, function(currentGeometry, currentIndex) {
        if (currentIndex === 0 && initialValue === void 0) {
          previousValue = currentGeometry;
        } else {
          previousValue = callback(previousValue, currentGeometry, currentIndex);
        }
      });
      return previousValue;
    }
    module.exports.geomReduce = geomReduce;
  }
});

// node_modules/.pnpm/geojson-flatten@0.2.4/node_modules/geojson-flatten/dist/index.js
var require_dist = __commonJS({
  "node_modules/.pnpm/geojson-flatten@0.2.4/node_modules/geojson-flatten/dist/index.js"(exports, module) {
    module.exports = function e3(t2) {
      switch (t2 && t2.type || null) {
        case "FeatureCollection":
          return t2.features = t2.features.reduce(function(t3, r2) {
            return t3.concat(e3(r2));
          }, []), t2;
        case "Feature":
          return t2.geometry ? e3(t2.geometry).map(function(e4) {
            var r2 = { type: "Feature", properties: JSON.parse(JSON.stringify(t2.properties)), geometry: e4 };
            return void 0 !== t2.id && (r2.id = t2.id), r2;
          }) : t2;
        case "MultiPoint":
          return t2.coordinates.map(function(e4) {
            return { type: "Point", coordinates: e4 };
          });
        case "MultiPolygon":
          return t2.coordinates.map(function(e4) {
            return { type: "Polygon", coordinates: e4 };
          });
        case "MultiLineString":
          return t2.coordinates.map(function(e4) {
            return { type: "LineString", coordinates: e4 };
          });
        case "GeometryCollection":
          return t2.geometries.map(e3).reduce(function(e4, t3) {
            return e4.concat(t3);
          }, []);
        case "Point":
        case "Polygon":
        case "LineString":
          return [t2];
      }
    };
  }
});

// node_modules/.pnpm/geojson-dissolve@3.1.0/node_modules/geojson-dissolve/index.js
var require_geojson_dissolve = __commonJS({
  "node_modules/.pnpm/geojson-dissolve@3.1.0/node_modules/geojson-dissolve/index.js"(exports, module) {
    var createTopology = (init_src2(), __toCommonJS(src_exports2)).topology;
    var mergeTopology = (init_src(), __toCommonJS(src_exports)).merge;
    var dissolveLineStrings = require_geojson_linestring_dissolve();
    var geomEach2 = require_meta().geomEach;
    var flatten2 = require_dist();
    module.exports = dissolve;
    function toArray(args) {
      if (!args.length) return [];
      return Array.isArray(args[0]) ? args[0] : Array.prototype.slice.call(args);
    }
    function dissolvePolygons(geoms) {
      var objects = {
        geoms: {
          type: "GeometryCollection",
          geometries: JSON.parse(JSON.stringify(geoms))
        }
      };
      var topo = createTopology(objects);
      return mergeTopology(topo, topo.objects.geoms.geometries);
    }
    function getHomogenousType(geoms) {
      var type = null;
      for (var i2 = 0; i2 < geoms.length; i2++) {
        if (!type) {
          type = geoms[i2].type;
        } else if (type !== geoms[i2].type) {
          return null;
        }
      }
      return type;
    }
    function dissolve() {
      var objects = toArray(arguments);
      var geoms = objects.reduce(function(acc, o2) {
        var flat = flatten2(o2);
        if (!Array.isArray(flat)) flat = [flat];
        for (var i2 = 0; i2 < flat.length; i2++) {
          geomEach2(flat[i2], function(geom) {
            acc.push(geom);
          });
        }
        return acc;
      }, []);
      var type = getHomogenousType(geoms);
      if (!type) {
        throw new Error("List does not contain only homoegenous GeoJSON");
      }
      switch (type) {
        case "LineString":
          return dissolveLineStrings(geoms);
        case "Polygon":
          return dissolvePolygons(geoms);
        default:
          return geoms;
      }
    }
  }
});

// node_modules/.pnpm/simplify-geometry@0.0.2/node_modules/simplify-geometry/lib/line.js
var require_line = __commonJS({
  "node_modules/.pnpm/simplify-geometry@0.0.2/node_modules/simplify-geometry/lib/line.js"(exports, module) {
    var Line = function(p1, p2) {
      this.p1 = p1;
      this.p2 = p2;
    };
    Line.prototype.rise = function() {
      return this.p2[1] - this.p1[1];
    };
    Line.prototype.run = function() {
      return this.p2[0] - this.p1[0];
    };
    Line.prototype.slope = function() {
      return this.rise() / this.run();
    };
    Line.prototype.yIntercept = function() {
      return this.p1[1] - this.p1[0] * this.slope(this.p1, this.p2);
    };
    Line.prototype.isVertical = function() {
      return !isFinite(this.slope());
    };
    Line.prototype.isHorizontal = function() {
      return this.p1[1] == this.p2[1];
    };
    Line.prototype._perpendicularDistanceHorizontal = function(point2) {
      return Math.abs(this.p1[1] - point2[1]);
    };
    Line.prototype._perpendicularDistanceVertical = function(point2) {
      return Math.abs(this.p1[0] - point2[0]);
    };
    Line.prototype._perpendicularDistanceHasSlope = function(point2) {
      var slope = this.slope();
      var y_intercept = this.yIntercept();
      return Math.abs(slope * point2[0] - point2[1] + y_intercept) / Math.sqrt(Math.pow(slope, 2) + 1);
    };
    Line.prototype.perpendicularDistance = function(point2) {
      if (this.isVertical()) {
        return this._perpendicularDistanceVertical(point2);
      } else if (this.isHorizontal()) {
        return this._perpendicularDistanceHorizontal(point2);
      } else {
        return this._perpendicularDistanceHasSlope(point2);
      }
    };
    module.exports = Line;
  }
});

// node_modules/.pnpm/simplify-geometry@0.0.2/node_modules/simplify-geometry/lib/index.js
var require_lib = __commonJS({
  "node_modules/.pnpm/simplify-geometry@0.0.2/node_modules/simplify-geometry/lib/index.js"(exports, module) {
    var Line = require_line();
    var simplifyGeometry = function(points, tolerance) {
      var dmax = 0;
      var index = 0;
      for (var i2 = 1; i2 <= points.length - 2; i2++) {
        var d2 = new Line(points[0], points[points.length - 1]).perpendicularDistance(points[i2]);
        if (d2 > dmax) {
          index = i2;
          dmax = d2;
        }
      }
      if (dmax > tolerance) {
        var results_one = simplifyGeometry(points.slice(0, index), tolerance);
        var results_two = simplifyGeometry(points.slice(index, points.length), tolerance);
        var results = results_one.concat(results_two);
      } else if (points.length > 1) {
        results = [points[0], points[points.length - 1]];
      } else {
        results = [points[0]];
      }
      return results;
    };
    module.exports = simplifyGeometry;
  }
});

// node_modules/.pnpm/simplify-geojson@1.0.5/node_modules/simplify-geojson/index.js
var require_simplify_geojson = __commonJS({
  "node_modules/.pnpm/simplify-geojson@1.0.5/node_modules/simplify-geojson/index.js"(exports, module) {
    var simplify = require_lib();
    module.exports = function(geojson, tolerance, dontClone) {
      if (!dontClone) geojson = JSON.parse(JSON.stringify(geojson));
      if (geojson.features) return simplifyFeatureCollection(geojson, tolerance);
      else if (geojson.type && geojson.type === "Feature") return simplifyFeature(geojson, tolerance);
      else return new Error("FeatureCollection or individual Feature required");
    };
    module.exports.simplify = function(coordinates, tolerance) {
      return simplify(coordinates, tolerance);
    };
    function simplifyFeature(feat, tolerance) {
      var geom = feat.geometry;
      var type = geom.type;
      if (type === "LineString") {
        geom.coordinates = module.exports.simplify(geom.coordinates, tolerance);
      } else if (type === "Polygon" || type === "MultiLineString") {
        for (var j2 = 0; j2 < geom.coordinates.length; j2++) {
          geom.coordinates[j2] = module.exports.simplify(geom.coordinates[j2], tolerance);
        }
      } else if (type === "MultiPolygon") {
        for (var k2 = 0; k2 < geom.coordinates.length; k2++) {
          for (var l2 = 0; l2 < geom.coordinates[k2].length; l2++) {
            geom.coordinates[k2][l2] = module.exports.simplify(geom.coordinates[k2][l2], tolerance);
          }
        }
      }
      return feat;
    }
    function simplifyFeatureCollection(fc, tolerance) {
      for (var i2 = 0; i2 < fc.features.length; i2++) {
        fc.features[i2] = simplifyFeature(fc.features[i2], tolerance);
      }
      return fc;
    }
  }
});

// node_modules/.pnpm/geobuf@3.0.2/node_modules/geobuf/encode.js
var require_encode = __commonJS({
  "node_modules/.pnpm/geobuf@3.0.2/node_modules/geobuf/encode.js"(exports, module) {
    "use strict";
    module.exports = encode;
    var keys2;
    var keysNum;
    var keysArr;
    var dim;
    var e3;
    var maxPrecision = 1e6;
    var geometryTypes = {
      "Point": 0,
      "MultiPoint": 1,
      "LineString": 2,
      "MultiLineString": 3,
      "Polygon": 4,
      "MultiPolygon": 5,
      "GeometryCollection": 6
    };
    function encode(obj, pbf) {
      keys2 = {};
      keysArr = [];
      keysNum = 0;
      dim = 0;
      e3 = 1;
      analyze(obj);
      e3 = Math.min(e3, maxPrecision);
      var precision = Math.ceil(Math.log(e3) / Math.LN10);
      for (var i2 = 0; i2 < keysArr.length; i2++) pbf.writeStringField(1, keysArr[i2]);
      if (dim !== 2) pbf.writeVarintField(2, dim);
      if (precision !== 6) pbf.writeVarintField(3, precision);
      if (obj.type === "FeatureCollection") pbf.writeMessage(4, writeFeatureCollection, obj);
      else if (obj.type === "Feature") pbf.writeMessage(5, writeFeature, obj);
      else pbf.writeMessage(6, writeGeometry, obj);
      keys2 = null;
      return pbf.finish();
    }
    function analyze(obj) {
      var i2, key;
      if (obj.type === "FeatureCollection") {
        for (i2 = 0; i2 < obj.features.length; i2++) analyze(obj.features[i2]);
      } else if (obj.type === "Feature") {
        if (obj.geometry !== null) analyze(obj.geometry);
        for (key in obj.properties) saveKey(key);
      } else if (obj.type === "Point") analyzePoint(obj.coordinates);
      else if (obj.type === "MultiPoint") analyzePoints(obj.coordinates);
      else if (obj.type === "GeometryCollection") {
        for (i2 = 0; i2 < obj.geometries.length; i2++) analyze(obj.geometries[i2]);
      } else if (obj.type === "LineString") analyzePoints(obj.coordinates);
      else if (obj.type === "Polygon" || obj.type === "MultiLineString") analyzeMultiLine(obj.coordinates);
      else if (obj.type === "MultiPolygon") {
        for (i2 = 0; i2 < obj.coordinates.length; i2++) analyzeMultiLine(obj.coordinates[i2]);
      }
      for (key in obj) {
        if (!isSpecialKey(key, obj.type)) saveKey(key);
      }
    }
    function analyzeMultiLine(coords) {
      for (var i2 = 0; i2 < coords.length; i2++) analyzePoints(coords[i2]);
    }
    function analyzePoints(coords) {
      for (var i2 = 0; i2 < coords.length; i2++) analyzePoint(coords[i2]);
    }
    function analyzePoint(point2) {
      dim = Math.max(dim, point2.length);
      for (var i2 = 0; i2 < point2.length; i2++) {
        while (Math.round(point2[i2] * e3) / e3 !== point2[i2] && e3 < maxPrecision) e3 *= 10;
      }
    }
    function saveKey(key) {
      if (keys2[key] === void 0) {
        keysArr.push(key);
        keys2[key] = keysNum++;
      }
    }
    function writeFeatureCollection(obj, pbf) {
      for (var i2 = 0; i2 < obj.features.length; i2++) {
        pbf.writeMessage(1, writeFeature, obj.features[i2]);
      }
      writeProps(obj, pbf, true);
    }
    function writeFeature(feature3, pbf) {
      if (feature3.geometry !== null) pbf.writeMessage(1, writeGeometry, feature3.geometry);
      if (feature3.id !== void 0) {
        if (typeof feature3.id === "number" && feature3.id % 1 === 0) pbf.writeSVarintField(12, feature3.id);
        else pbf.writeStringField(11, feature3.id);
      }
      if (feature3.properties) writeProps(feature3.properties, pbf);
      writeProps(feature3, pbf, true);
    }
    function writeGeometry(geom, pbf) {
      pbf.writeVarintField(1, geometryTypes[geom.type]);
      var coords = geom.coordinates;
      if (geom.type === "Point") writePoint(coords, pbf);
      else if (geom.type === "MultiPoint") writeLine(coords, pbf, true);
      else if (geom.type === "LineString") writeLine(coords, pbf);
      else if (geom.type === "MultiLineString") writeMultiLine(coords, pbf);
      else if (geom.type === "Polygon") writeMultiLine(coords, pbf, true);
      else if (geom.type === "MultiPolygon") writeMultiPolygon(coords, pbf);
      else if (geom.type === "GeometryCollection") {
        for (var i2 = 0; i2 < geom.geometries.length; i2++) pbf.writeMessage(4, writeGeometry, geom.geometries[i2]);
      }
      writeProps(geom, pbf, true);
    }
    function writeProps(props, pbf, isCustom) {
      var indexes = [], valueIndex = 0;
      for (var key in props) {
        if (isCustom && isSpecialKey(key, props.type)) {
          continue;
        }
        pbf.writeMessage(13, writeValue, props[key]);
        indexes.push(keys2[key]);
        indexes.push(valueIndex++);
      }
      pbf.writePackedVarint(isCustom ? 15 : 14, indexes);
    }
    function writeValue(value, pbf) {
      if (value === null) return;
      var type = typeof value;
      if (type === "string") pbf.writeStringField(1, value);
      else if (type === "boolean") pbf.writeBooleanField(5, value);
      else if (type === "object") pbf.writeStringField(6, JSON.stringify(value));
      else if (type === "number") {
        if (value % 1 !== 0) pbf.writeDoubleField(2, value);
        else if (value >= 0) pbf.writeVarintField(3, value);
        else pbf.writeVarintField(4, -value);
      }
    }
    function writePoint(point2, pbf) {
      var coords = [];
      for (var i2 = 0; i2 < dim; i2++) coords.push(Math.round(point2[i2] * e3));
      pbf.writePackedSVarint(3, coords);
    }
    function writeLine(line, pbf) {
      var coords = [];
      populateLine(coords, line);
      pbf.writePackedSVarint(3, coords);
    }
    function writeMultiLine(lines, pbf, closed) {
      var len = lines.length, i2;
      if (len !== 1) {
        var lengths = [];
        for (i2 = 0; i2 < len; i2++) lengths.push(lines[i2].length - (closed ? 1 : 0));
        pbf.writePackedVarint(2, lengths);
      }
      var coords = [];
      for (i2 = 0; i2 < len; i2++) populateLine(coords, lines[i2], closed);
      pbf.writePackedSVarint(3, coords);
    }
    function writeMultiPolygon(polygons, pbf) {
      var len = polygons.length, i2, j2;
      if (len !== 1 || polygons[0].length !== 1) {
        var lengths = [len];
        for (i2 = 0; i2 < len; i2++) {
          lengths.push(polygons[i2].length);
          for (j2 = 0; j2 < polygons[i2].length; j2++) lengths.push(polygons[i2][j2].length - 1);
        }
        pbf.writePackedVarint(2, lengths);
      }
      var coords = [];
      for (i2 = 0; i2 < len; i2++) {
        for (j2 = 0; j2 < polygons[i2].length; j2++) populateLine(coords, polygons[i2][j2], true);
      }
      pbf.writePackedSVarint(3, coords);
    }
    function populateLine(coords, line, closed) {
      var i2, j2, len = line.length - (closed ? 1 : 0), sum3 = new Array(dim);
      for (j2 = 0; j2 < dim; j2++) sum3[j2] = 0;
      for (i2 = 0; i2 < len; i2++) {
        for (j2 = 0; j2 < dim; j2++) {
          var n2 = Math.round(line[i2][j2] * e3) - sum3[j2];
          coords.push(n2);
          sum3[j2] += n2;
        }
      }
    }
    function isSpecialKey(key, type) {
      if (key === "type") return true;
      else if (type === "FeatureCollection") {
        if (key === "features") return true;
      } else if (type === "Feature") {
        if (key === "id" || key === "properties" || key === "geometry") return true;
      } else if (type === "GeometryCollection") {
        if (key === "geometries") return true;
      } else if (key === "coordinates") return true;
      return false;
    }
  }
});

// node_modules/.pnpm/geobuf@3.0.2/node_modules/geobuf/decode.js
var require_decode = __commonJS({
  "node_modules/.pnpm/geobuf@3.0.2/node_modules/geobuf/decode.js"(exports, module) {
    "use strict";
    module.exports = decode;
    var keys2;
    var values;
    var lengths;
    var dim;
    var e3;
    var geometryTypes = [
      "Point",
      "MultiPoint",
      "LineString",
      "MultiLineString",
      "Polygon",
      "MultiPolygon",
      "GeometryCollection"
    ];
    function decode(pbf) {
      dim = 2;
      e3 = Math.pow(10, 6);
      lengths = null;
      keys2 = [];
      values = [];
      var obj = pbf.readFields(readDataField, {});
      keys2 = null;
      return obj;
    }
    function readDataField(tag, obj, pbf) {
      if (tag === 1) keys2.push(pbf.readString());
      else if (tag === 2) dim = pbf.readVarint();
      else if (tag === 3) e3 = Math.pow(10, pbf.readVarint());
      else if (tag === 4) readFeatureCollection(pbf, obj);
      else if (tag === 5) readFeature(pbf, obj);
      else if (tag === 6) readGeometry(pbf, obj);
    }
    function readFeatureCollection(pbf, obj) {
      obj.type = "FeatureCollection";
      obj.features = [];
      return pbf.readMessage(readFeatureCollectionField, obj);
    }
    function readFeature(pbf, feature3) {
      feature3.type = "Feature";
      var f2 = pbf.readMessage(readFeatureField, feature3);
      if (!("geometry" in f2)) f2.geometry = null;
      return f2;
    }
    function readGeometry(pbf, geom) {
      geom.type = "Point";
      return pbf.readMessage(readGeometryField, geom);
    }
    function readFeatureCollectionField(tag, obj, pbf) {
      if (tag === 1) obj.features.push(readFeature(pbf, {}));
      else if (tag === 13) values.push(readValue(pbf));
      else if (tag === 15) readProps(pbf, obj);
    }
    function readFeatureField(tag, feature3, pbf) {
      if (tag === 1) feature3.geometry = readGeometry(pbf, {});
      else if (tag === 11) feature3.id = pbf.readString();
      else if (tag === 12) feature3.id = pbf.readSVarint();
      else if (tag === 13) values.push(readValue(pbf));
      else if (tag === 14) feature3.properties = readProps(pbf, {});
      else if (tag === 15) readProps(pbf, feature3);
    }
    function readGeometryField(tag, geom, pbf) {
      if (tag === 1) geom.type = geometryTypes[pbf.readVarint()];
      else if (tag === 2) lengths = pbf.readPackedVarint();
      else if (tag === 3) readCoords(geom, pbf, geom.type);
      else if (tag === 4) {
        geom.geometries = geom.geometries || [];
        geom.geometries.push(readGeometry(pbf, {}));
      } else if (tag === 13) values.push(readValue(pbf));
      else if (tag === 15) readProps(pbf, geom);
    }
    function readCoords(geom, pbf, type) {
      if (type === "Point") geom.coordinates = readPoint(pbf);
      else if (type === "MultiPoint") geom.coordinates = readLine(pbf, true);
      else if (type === "LineString") geom.coordinates = readLine(pbf);
      else if (type === "MultiLineString") geom.coordinates = readMultiLine(pbf);
      else if (type === "Polygon") geom.coordinates = readMultiLine(pbf, true);
      else if (type === "MultiPolygon") geom.coordinates = readMultiPolygon(pbf);
    }
    function readValue(pbf) {
      var end = pbf.readVarint() + pbf.pos, value = null;
      while (pbf.pos < end) {
        var val = pbf.readVarint(), tag = val >> 3;
        if (tag === 1) value = pbf.readString();
        else if (tag === 2) value = pbf.readDouble();
        else if (tag === 3) value = pbf.readVarint();
        else if (tag === 4) value = -pbf.readVarint();
        else if (tag === 5) value = pbf.readBoolean();
        else if (tag === 6) value = JSON.parse(pbf.readString());
      }
      return value;
    }
    function readProps(pbf, props) {
      var end = pbf.readVarint() + pbf.pos;
      while (pbf.pos < end) props[keys2[pbf.readVarint()]] = values[pbf.readVarint()];
      values = [];
      return props;
    }
    function readPoint(pbf) {
      var end = pbf.readVarint() + pbf.pos, coords = [];
      while (pbf.pos < end) coords.push(pbf.readSVarint() / e3);
      return coords;
    }
    function readLinePart(pbf, end, len, closed) {
      var i2 = 0, coords = [], p2, d2;
      var prevP = [];
      for (d2 = 0; d2 < dim; d2++) prevP[d2] = 0;
      while (len ? i2 < len : pbf.pos < end) {
        p2 = [];
        for (d2 = 0; d2 < dim; d2++) {
          prevP[d2] += pbf.readSVarint();
          p2[d2] = prevP[d2] / e3;
        }
        coords.push(p2);
        i2++;
      }
      if (closed) coords.push(coords[0]);
      return coords;
    }
    function readLine(pbf) {
      return readLinePart(pbf, pbf.readVarint() + pbf.pos);
    }
    function readMultiLine(pbf, closed) {
      var end = pbf.readVarint() + pbf.pos;
      if (!lengths) return [readLinePart(pbf, end, null, closed)];
      var coords = [];
      for (var i2 = 0; i2 < lengths.length; i2++) coords.push(readLinePart(pbf, end, lengths[i2], closed));
      lengths = null;
      return coords;
    }
    function readMultiPolygon(pbf) {
      var end = pbf.readVarint() + pbf.pos;
      if (!lengths) return [[readLinePart(pbf, end, null, true)]];
      var coords = [];
      var j2 = 1;
      for (var i2 = 0; i2 < lengths[0]; i2++) {
        var rings = [];
        for (var k2 = 0; k2 < lengths[j2]; k2++) rings.push(readLinePart(pbf, end, lengths[j2 + 1 + k2], true));
        j2 += lengths[j2] + 1;
        coords.push(rings);
      }
      lengths = null;
      return coords;
    }
  }
});

// node_modules/.pnpm/geobuf@3.0.2/node_modules/geobuf/index.js
var require_geobuf = __commonJS({
  "node_modules/.pnpm/geobuf@3.0.2/node_modules/geobuf/index.js"(exports) {
    "use strict";
    exports.encode = require_encode();
    exports.decode = require_decode();
  }
});

// node_modules/.pnpm/ieee754@1.2.1/node_modules/ieee754/index.js
var require_ieee754 = __commonJS({
  "node_modules/.pnpm/ieee754@1.2.1/node_modules/ieee754/index.js"(exports) {
    exports.read = function(buffer2, offset, isLE, mLen, nBytes) {
      var e3, m2;
      var eLen = nBytes * 8 - mLen - 1;
      var eMax = (1 << eLen) - 1;
      var eBias = eMax >> 1;
      var nBits = -7;
      var i2 = isLE ? nBytes - 1 : 0;
      var d2 = isLE ? -1 : 1;
      var s2 = buffer2[offset + i2];
      i2 += d2;
      e3 = s2 & (1 << -nBits) - 1;
      s2 >>= -nBits;
      nBits += eLen;
      for (; nBits > 0; e3 = e3 * 256 + buffer2[offset + i2], i2 += d2, nBits -= 8) {
      }
      m2 = e3 & (1 << -nBits) - 1;
      e3 >>= -nBits;
      nBits += mLen;
      for (; nBits > 0; m2 = m2 * 256 + buffer2[offset + i2], i2 += d2, nBits -= 8) {
      }
      if (e3 === 0) {
        e3 = 1 - eBias;
      } else if (e3 === eMax) {
        return m2 ? NaN : (s2 ? -1 : 1) * Infinity;
      } else {
        m2 = m2 + Math.pow(2, mLen);
        e3 = e3 - eBias;
      }
      return (s2 ? -1 : 1) * m2 * Math.pow(2, e3 - mLen);
    };
    exports.write = function(buffer2, value, offset, isLE, mLen, nBytes) {
      var e3, m2, c2;
      var eLen = nBytes * 8 - mLen - 1;
      var eMax = (1 << eLen) - 1;
      var eBias = eMax >> 1;
      var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;
      var i2 = isLE ? 0 : nBytes - 1;
      var d2 = isLE ? 1 : -1;
      var s2 = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;
      value = Math.abs(value);
      if (isNaN(value) || value === Infinity) {
        m2 = isNaN(value) ? 1 : 0;
        e3 = eMax;
      } else {
        e3 = Math.floor(Math.log(value) / Math.LN2);
        if (value * (c2 = Math.pow(2, -e3)) < 1) {
          e3--;
          c2 *= 2;
        }
        if (e3 + eBias >= 1) {
          value += rt / c2;
        } else {
          value += rt * Math.pow(2, 1 - eBias);
        }
        if (value * c2 >= 2) {
          e3++;
          c2 /= 2;
        }
        if (e3 + eBias >= eMax) {
          m2 = 0;
          e3 = eMax;
        } else if (e3 + eBias >= 1) {
          m2 = (value * c2 - 1) * Math.pow(2, mLen);
          e3 = e3 + eBias;
        } else {
          m2 = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);
          e3 = 0;
        }
      }
      for (; mLen >= 8; buffer2[offset + i2] = m2 & 255, i2 += d2, m2 /= 256, mLen -= 8) {
      }
      e3 = e3 << mLen | m2;
      eLen += mLen;
      for (; eLen > 0; buffer2[offset + i2] = e3 & 255, i2 += d2, e3 /= 256, eLen -= 8) {
      }
      buffer2[offset + i2 - d2] |= s2 * 128;
    };
  }
});

// node_modules/.pnpm/pbf@3.3.0/node_modules/pbf/index.js
var require_pbf = __commonJS({
  "node_modules/.pnpm/pbf@3.3.0/node_modules/pbf/index.js"(exports, module) {
    "use strict";
    module.exports = Pbf;
    var ieee754 = require_ieee754();
    function Pbf(buf) {
      this.buf = ArrayBuffer.isView && ArrayBuffer.isView(buf) ? buf : new Uint8Array(buf || 0);
      this.pos = 0;
      this.type = 0;
      this.length = this.buf.length;
    }
    Pbf.Varint = 0;
    Pbf.Fixed64 = 1;
    Pbf.Bytes = 2;
    Pbf.Fixed32 = 5;
    var SHIFT_LEFT_32 = (1 << 16) * (1 << 16);
    var SHIFT_RIGHT_32 = 1 / SHIFT_LEFT_32;
    var TEXT_DECODER_MIN_LENGTH = 12;
    var utf8TextDecoder = typeof TextDecoder === "undefined" ? null : new TextDecoder("utf-8");
    Pbf.prototype = {
      destroy: function() {
        this.buf = null;
      },
      // === READING =================================================================
      readFields: function(readField, result, end) {
        end = end || this.length;
        while (this.pos < end) {
          var val = this.readVarint(), tag = val >> 3, startPos = this.pos;
          this.type = val & 7;
          readField(tag, result, this);
          if (this.pos === startPos) this.skip(val);
        }
        return result;
      },
      readMessage: function(readField, result) {
        return this.readFields(readField, result, this.readVarint() + this.pos);
      },
      readFixed32: function() {
        var val = readUInt32(this.buf, this.pos);
        this.pos += 4;
        return val;
      },
      readSFixed32: function() {
        var val = readInt32(this.buf, this.pos);
        this.pos += 4;
        return val;
      },
      // 64-bit int handling is based on github.com/dpw/node-buffer-more-ints (MIT-licensed)
      readFixed64: function() {
        var val = readUInt32(this.buf, this.pos) + readUInt32(this.buf, this.pos + 4) * SHIFT_LEFT_32;
        this.pos += 8;
        return val;
      },
      readSFixed64: function() {
        var val = readUInt32(this.buf, this.pos) + readInt32(this.buf, this.pos + 4) * SHIFT_LEFT_32;
        this.pos += 8;
        return val;
      },
      readFloat: function() {
        var val = ieee754.read(this.buf, this.pos, true, 23, 4);
        this.pos += 4;
        return val;
      },
      readDouble: function() {
        var val = ieee754.read(this.buf, this.pos, true, 52, 8);
        this.pos += 8;
        return val;
      },
      readVarint: function(isSigned) {
        var buf = this.buf, val, b2;
        b2 = buf[this.pos++];
        val = b2 & 127;
        if (b2 < 128) return val;
        b2 = buf[this.pos++];
        val |= (b2 & 127) << 7;
        if (b2 < 128) return val;
        b2 = buf[this.pos++];
        val |= (b2 & 127) << 14;
        if (b2 < 128) return val;
        b2 = buf[this.pos++];
        val |= (b2 & 127) << 21;
        if (b2 < 128) return val;
        b2 = buf[this.pos];
        val |= (b2 & 15) << 28;
        return readVarintRemainder(val, isSigned, this);
      },
      readVarint64: function() {
        return this.readVarint(true);
      },
      readSVarint: function() {
        var num = this.readVarint();
        return num % 2 === 1 ? (num + 1) / -2 : num / 2;
      },
      readBoolean: function() {
        return Boolean(this.readVarint());
      },
      readString: function() {
        var end = this.readVarint() + this.pos;
        var pos = this.pos;
        this.pos = end;
        if (end - pos >= TEXT_DECODER_MIN_LENGTH && utf8TextDecoder) {
          return readUtf8TextDecoder(this.buf, pos, end);
        }
        return readUtf8(this.buf, pos, end);
      },
      readBytes: function() {
        var end = this.readVarint() + this.pos, buffer2 = this.buf.subarray(this.pos, end);
        this.pos = end;
        return buffer2;
      },
      // verbose for performance reasons; doesn't affect gzipped size
      readPackedVarint: function(arr, isSigned) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readVarint(isSigned));
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readVarint(isSigned));
        return arr;
      },
      readPackedSVarint: function(arr) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readSVarint());
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readSVarint());
        return arr;
      },
      readPackedBoolean: function(arr) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readBoolean());
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readBoolean());
        return arr;
      },
      readPackedFloat: function(arr) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readFloat());
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readFloat());
        return arr;
      },
      readPackedDouble: function(arr) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readDouble());
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readDouble());
        return arr;
      },
      readPackedFixed32: function(arr) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readFixed32());
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readFixed32());
        return arr;
      },
      readPackedSFixed32: function(arr) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readSFixed32());
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readSFixed32());
        return arr;
      },
      readPackedFixed64: function(arr) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readFixed64());
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readFixed64());
        return arr;
      },
      readPackedSFixed64: function(arr) {
        if (this.type !== Pbf.Bytes) return arr.push(this.readSFixed64());
        var end = readPackedEnd(this);
        arr = arr || [];
        while (this.pos < end) arr.push(this.readSFixed64());
        return arr;
      },
      skip: function(val) {
        var type = val & 7;
        if (type === Pbf.Varint) while (this.buf[this.pos++] > 127) {
        }
        else if (type === Pbf.Bytes) this.pos = this.readVarint() + this.pos;
        else if (type === Pbf.Fixed32) this.pos += 4;
        else if (type === Pbf.Fixed64) this.pos += 8;
        else throw new Error("Unimplemented type: " + type);
      },
      // === WRITING =================================================================
      writeTag: function(tag, type) {
        this.writeVarint(tag << 3 | type);
      },
      realloc: function(min3) {
        var length = this.length || 16;
        while (length < this.pos + min3) length *= 2;
        if (length !== this.length) {
          var buf = new Uint8Array(length);
          buf.set(this.buf);
          this.buf = buf;
          this.length = length;
        }
      },
      finish: function() {
        this.length = this.pos;
        this.pos = 0;
        return this.buf.subarray(0, this.length);
      },
      writeFixed32: function(val) {
        this.realloc(4);
        writeInt32(this.buf, val, this.pos);
        this.pos += 4;
      },
      writeSFixed32: function(val) {
        this.realloc(4);
        writeInt32(this.buf, val, this.pos);
        this.pos += 4;
      },
      writeFixed64: function(val) {
        this.realloc(8);
        writeInt32(this.buf, val & -1, this.pos);
        writeInt32(this.buf, Math.floor(val * SHIFT_RIGHT_32), this.pos + 4);
        this.pos += 8;
      },
      writeSFixed64: function(val) {
        this.realloc(8);
        writeInt32(this.buf, val & -1, this.pos);
        writeInt32(this.buf, Math.floor(val * SHIFT_RIGHT_32), this.pos + 4);
        this.pos += 8;
      },
      writeVarint: function(val) {
        val = +val || 0;
        if (val > 268435455 || val < 0) {
          writeBigVarint(val, this);
          return;
        }
        this.realloc(4);
        this.buf[this.pos++] = val & 127 | (val > 127 ? 128 : 0);
        if (val <= 127) return;
        this.buf[this.pos++] = (val >>>= 7) & 127 | (val > 127 ? 128 : 0);
        if (val <= 127) return;
        this.buf[this.pos++] = (val >>>= 7) & 127 | (val > 127 ? 128 : 0);
        if (val <= 127) return;
        this.buf[this.pos++] = val >>> 7 & 127;
      },
      writeSVarint: function(val) {
        this.writeVarint(val < 0 ? -val * 2 - 1 : val * 2);
      },
      writeBoolean: function(val) {
        this.writeVarint(Boolean(val));
      },
      writeString: function(str) {
        str = String(str);
        this.realloc(str.length * 4);
        this.pos++;
        var startPos = this.pos;
        this.pos = writeUtf8(this.buf, str, this.pos);
        var len = this.pos - startPos;
        if (len >= 128) makeRoomForExtraLength(startPos, len, this);
        this.pos = startPos - 1;
        this.writeVarint(len);
        this.pos += len;
      },
      writeFloat: function(val) {
        this.realloc(4);
        ieee754.write(this.buf, val, this.pos, true, 23, 4);
        this.pos += 4;
      },
      writeDouble: function(val) {
        this.realloc(8);
        ieee754.write(this.buf, val, this.pos, true, 52, 8);
        this.pos += 8;
      },
      writeBytes: function(buffer2) {
        var len = buffer2.length;
        this.writeVarint(len);
        this.realloc(len);
        for (var i2 = 0; i2 < len; i2++) this.buf[this.pos++] = buffer2[i2];
      },
      writeRawMessage: function(fn, obj) {
        this.pos++;
        var startPos = this.pos;
        fn(obj, this);
        var len = this.pos - startPos;
        if (len >= 128) makeRoomForExtraLength(startPos, len, this);
        this.pos = startPos - 1;
        this.writeVarint(len);
        this.pos += len;
      },
      writeMessage: function(tag, fn, obj) {
        this.writeTag(tag, Pbf.Bytes);
        this.writeRawMessage(fn, obj);
      },
      writePackedVarint: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedVarint, arr);
      },
      writePackedSVarint: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedSVarint, arr);
      },
      writePackedBoolean: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedBoolean, arr);
      },
      writePackedFloat: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedFloat, arr);
      },
      writePackedDouble: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedDouble, arr);
      },
      writePackedFixed32: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedFixed32, arr);
      },
      writePackedSFixed32: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedSFixed32, arr);
      },
      writePackedFixed64: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedFixed64, arr);
      },
      writePackedSFixed64: function(tag, arr) {
        if (arr.length) this.writeMessage(tag, writePackedSFixed64, arr);
      },
      writeBytesField: function(tag, buffer2) {
        this.writeTag(tag, Pbf.Bytes);
        this.writeBytes(buffer2);
      },
      writeFixed32Field: function(tag, val) {
        this.writeTag(tag, Pbf.Fixed32);
        this.writeFixed32(val);
      },
      writeSFixed32Field: function(tag, val) {
        this.writeTag(tag, Pbf.Fixed32);
        this.writeSFixed32(val);
      },
      writeFixed64Field: function(tag, val) {
        this.writeTag(tag, Pbf.Fixed64);
        this.writeFixed64(val);
      },
      writeSFixed64Field: function(tag, val) {
        this.writeTag(tag, Pbf.Fixed64);
        this.writeSFixed64(val);
      },
      writeVarintField: function(tag, val) {
        this.writeTag(tag, Pbf.Varint);
        this.writeVarint(val);
      },
      writeSVarintField: function(tag, val) {
        this.writeTag(tag, Pbf.Varint);
        this.writeSVarint(val);
      },
      writeStringField: function(tag, str) {
        this.writeTag(tag, Pbf.Bytes);
        this.writeString(str);
      },
      writeFloatField: function(tag, val) {
        this.writeTag(tag, Pbf.Fixed32);
        this.writeFloat(val);
      },
      writeDoubleField: function(tag, val) {
        this.writeTag(tag, Pbf.Fixed64);
        this.writeDouble(val);
      },
      writeBooleanField: function(tag, val) {
        this.writeVarintField(tag, Boolean(val));
      }
    };
    function readVarintRemainder(l2, s2, p2) {
      var buf = p2.buf, h2, b2;
      b2 = buf[p2.pos++];
      h2 = (b2 & 112) >> 4;
      if (b2 < 128) return toNum(l2, h2, s2);
      b2 = buf[p2.pos++];
      h2 |= (b2 & 127) << 3;
      if (b2 < 128) return toNum(l2, h2, s2);
      b2 = buf[p2.pos++];
      h2 |= (b2 & 127) << 10;
      if (b2 < 128) return toNum(l2, h2, s2);
      b2 = buf[p2.pos++];
      h2 |= (b2 & 127) << 17;
      if (b2 < 128) return toNum(l2, h2, s2);
      b2 = buf[p2.pos++];
      h2 |= (b2 & 127) << 24;
      if (b2 < 128) return toNum(l2, h2, s2);
      b2 = buf[p2.pos++];
      h2 |= (b2 & 1) << 31;
      if (b2 < 128) return toNum(l2, h2, s2);
      throw new Error("Expected varint not more than 10 bytes");
    }
    function readPackedEnd(pbf) {
      return pbf.type === Pbf.Bytes ? pbf.readVarint() + pbf.pos : pbf.pos + 1;
    }
    function toNum(low, high, isSigned) {
      if (isSigned) {
        return high * 4294967296 + (low >>> 0);
      }
      return (high >>> 0) * 4294967296 + (low >>> 0);
    }
    function writeBigVarint(val, pbf) {
      var low, high;
      if (val >= 0) {
        low = val % 4294967296 | 0;
        high = val / 4294967296 | 0;
      } else {
        low = ~(-val % 4294967296);
        high = ~(-val / 4294967296);
        if (low ^ 4294967295) {
          low = low + 1 | 0;
        } else {
          low = 0;
          high = high + 1 | 0;
        }
      }
      if (val >= 18446744073709552e3 || val < -18446744073709552e3) {
        throw new Error("Given varint doesn't fit into 10 bytes");
      }
      pbf.realloc(10);
      writeBigVarintLow(low, high, pbf);
      writeBigVarintHigh(high, pbf);
    }
    function writeBigVarintLow(low, high, pbf) {
      pbf.buf[pbf.pos++] = low & 127 | 128;
      low >>>= 7;
      pbf.buf[pbf.pos++] = low & 127 | 128;
      low >>>= 7;
      pbf.buf[pbf.pos++] = low & 127 | 128;
      low >>>= 7;
      pbf.buf[pbf.pos++] = low & 127 | 128;
      low >>>= 7;
      pbf.buf[pbf.pos] = low & 127;
    }
    function writeBigVarintHigh(high, pbf) {
      var lsb = (high & 7) << 4;
      pbf.buf[pbf.pos++] |= lsb | ((high >>>= 3) ? 128 : 0);
      if (!high) return;
      pbf.buf[pbf.pos++] = high & 127 | ((high >>>= 7) ? 128 : 0);
      if (!high) return;
      pbf.buf[pbf.pos++] = high & 127 | ((high >>>= 7) ? 128 : 0);
      if (!high) return;
      pbf.buf[pbf.pos++] = high & 127 | ((high >>>= 7) ? 128 : 0);
      if (!high) return;
      pbf.buf[pbf.pos++] = high & 127 | ((high >>>= 7) ? 128 : 0);
      if (!high) return;
      pbf.buf[pbf.pos++] = high & 127;
    }
    function makeRoomForExtraLength(startPos, len, pbf) {
      var extraLen = len <= 16383 ? 1 : len <= 2097151 ? 2 : len <= 268435455 ? 3 : Math.floor(Math.log(len) / (Math.LN2 * 7));
      pbf.realloc(extraLen);
      for (var i2 = pbf.pos - 1; i2 >= startPos; i2--) pbf.buf[i2 + extraLen] = pbf.buf[i2];
    }
    function writePackedVarint(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeVarint(arr[i2]);
    }
    function writePackedSVarint(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeSVarint(arr[i2]);
    }
    function writePackedFloat(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeFloat(arr[i2]);
    }
    function writePackedDouble(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeDouble(arr[i2]);
    }
    function writePackedBoolean(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeBoolean(arr[i2]);
    }
    function writePackedFixed32(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeFixed32(arr[i2]);
    }
    function writePackedSFixed32(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeSFixed32(arr[i2]);
    }
    function writePackedFixed64(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeFixed64(arr[i2]);
    }
    function writePackedSFixed64(arr, pbf) {
      for (var i2 = 0; i2 < arr.length; i2++) pbf.writeSFixed64(arr[i2]);
    }
    function readUInt32(buf, pos) {
      return (buf[pos] | buf[pos + 1] << 8 | buf[pos + 2] << 16) + buf[pos + 3] * 16777216;
    }
    function writeInt32(buf, val, pos) {
      buf[pos] = val;
      buf[pos + 1] = val >>> 8;
      buf[pos + 2] = val >>> 16;
      buf[pos + 3] = val >>> 24;
    }
    function readInt32(buf, pos) {
      return (buf[pos] | buf[pos + 1] << 8 | buf[pos + 2] << 16) + (buf[pos + 3] << 24);
    }
    function readUtf8(buf, pos, end) {
      var str = "";
      var i2 = pos;
      while (i2 < end) {
        var b0 = buf[i2];
        var c2 = null;
        var bytesPerSequence = b0 > 239 ? 4 : b0 > 223 ? 3 : b0 > 191 ? 2 : 1;
        if (i2 + bytesPerSequence > end) break;
        var b1, b2, b3;
        if (bytesPerSequence === 1) {
          if (b0 < 128) {
            c2 = b0;
          }
        } else if (bytesPerSequence === 2) {
          b1 = buf[i2 + 1];
          if ((b1 & 192) === 128) {
            c2 = (b0 & 31) << 6 | b1 & 63;
            if (c2 <= 127) {
              c2 = null;
            }
          }
        } else if (bytesPerSequence === 3) {
          b1 = buf[i2 + 1];
          b2 = buf[i2 + 2];
          if ((b1 & 192) === 128 && (b2 & 192) === 128) {
            c2 = (b0 & 15) << 12 | (b1 & 63) << 6 | b2 & 63;
            if (c2 <= 2047 || c2 >= 55296 && c2 <= 57343) {
              c2 = null;
            }
          }
        } else if (bytesPerSequence === 4) {
          b1 = buf[i2 + 1];
          b2 = buf[i2 + 2];
          b3 = buf[i2 + 3];
          if ((b1 & 192) === 128 && (b2 & 192) === 128 && (b3 & 192) === 128) {
            c2 = (b0 & 15) << 18 | (b1 & 63) << 12 | (b2 & 63) << 6 | b3 & 63;
            if (c2 <= 65535 || c2 >= 1114112) {
              c2 = null;
            }
          }
        }
        if (c2 === null) {
          c2 = 65533;
          bytesPerSequence = 1;
        } else if (c2 > 65535) {
          c2 -= 65536;
          str += String.fromCharCode(c2 >>> 10 & 1023 | 55296);
          c2 = 56320 | c2 & 1023;
        }
        str += String.fromCharCode(c2);
        i2 += bytesPerSequence;
      }
      return str;
    }
    function readUtf8TextDecoder(buf, pos, end) {
      return utf8TextDecoder.decode(buf.subarray(pos, end));
    }
    function writeUtf8(buf, str, pos) {
      for (var i2 = 0, c2, lead; i2 < str.length; i2++) {
        c2 = str.charCodeAt(i2);
        if (c2 > 55295 && c2 < 57344) {
          if (lead) {
            if (c2 < 56320) {
              buf[pos++] = 239;
              buf[pos++] = 191;
              buf[pos++] = 189;
              lead = c2;
              continue;
            } else {
              c2 = lead - 55296 << 10 | c2 - 56320 | 65536;
              lead = null;
            }
          } else {
            if (c2 > 56319 || i2 + 1 === str.length) {
              buf[pos++] = 239;
              buf[pos++] = 191;
              buf[pos++] = 189;
            } else {
              lead = c2;
            }
            continue;
          }
        } else if (lead) {
          buf[pos++] = 239;
          buf[pos++] = 191;
          buf[pos++] = 189;
          lead = null;
        }
        if (c2 < 128) {
          buf[pos++] = c2;
        } else {
          if (c2 < 2048) {
            buf[pos++] = c2 >> 6 | 192;
          } else {
            if (c2 < 65536) {
              buf[pos++] = c2 >> 12 | 224;
            } else {
              buf[pos++] = c2 >> 18 | 240;
              buf[pos++] = c2 >> 12 & 63 | 128;
            }
            buf[pos++] = c2 >> 6 & 63 | 128;
          }
          buf[pos++] = c2 & 63 | 128;
        }
      }
      return pos;
    }
  }
});

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isType.js
var isType = (value, type) => Object.prototype.toString.call(value) === `[object ${type}]`;
var isType_default = isType;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isBoolean.js
var isBoolean = (value, fuzzy = false) => fuzzy ? "boolean" == typeof value : true === value || false === value || isType_default(value, "Boolean");
var isBoolean_default = isBoolean;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isFunction.js
var isFunction = (value) => "function" == typeof value;
var isFunction_default = isFunction;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isNil.js
var isNil = (value) => null == value;
var isNil_default = isNil;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isValid.js
var isValid = (value) => null != value;
var isValid_default = isValid;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isObject.js
var isObject = (value) => {
  const type = typeof value;
  return null !== value && "object" === type || "function" === type;
};
var isObject_default = isObject;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isUndefined.js
var isUndefined = (value) => void 0 === value;
var isUndefined_default = isUndefined;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isString.js
var isString = (value, fuzzy = false) => {
  const type = typeof value;
  return fuzzy ? "string" === type : "string" === type || isType_default(value, "String");
};
var isString_default = isString;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isArray.js
var isArray = (value) => Array.isArray ? Array.isArray(value) : isType_default(value, "Array");
var isArray_default = isArray;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isNumber.js
var isNumber = (value, fuzzy = false) => {
  const type = typeof value;
  return fuzzy ? "number" === type : "number" === type || isType_default(value, "Number");
};
var isNumber_default = isNumber;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isNumeric.js
var isNumeric = (value) => "string" == typeof value && (!isNaN(Number(value)) && !isNaN(parseFloat(value)));
var isNumeric_default = isNumeric;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isValidNumber.js
var isValidNumber = (value) => isNumber_default(value) && Number.isFinite(value);
var isValidNumber_default = isValidNumber;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isArrayLike.js
var isArrayLike = function(value) {
  return null !== value && "function" != typeof value && Number.isFinite(value.length);
};
var isArrayLike_default = isArrayLike;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/getType.js
var getType = (value) => ({}).toString.call(value).replace(/^\[object /, "").replace(/]$/, "");
var getType_default = getType;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isPrototype.js
var objectProto = Object.prototype;
var isPrototype = function(value) {
  const Ctor = value && value.constructor;
  return value === ("function" == typeof Ctor && Ctor.prototype || objectProto);
};
var isPrototype_default = isPrototype;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isEmpty.js
var hasOwnProperty = Object.prototype.hasOwnProperty;
function isEmpty(value) {
  if (isNil_default(value)) return true;
  if (isArrayLike_default(value)) return !value.length;
  const type = getType_default(value);
  if ("Map" === type || "Set" === type) return !value.size;
  if (isPrototype_default(value)) return !Object.keys(value).length;
  for (const key in value) if (hasOwnProperty.call(value, key)) return false;
  return true;
}
var isEmpty_default = isEmpty;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/get.js
var get = (obj, path, defaultValue) => {
  const paths = isString_default(path) ? path.split(".") : path;
  for (let p2 = 0; p2 < paths.length; p2++) obj = obj ? obj[paths[p2]] : void 0;
  return void 0 === obj ? defaultValue : obj;
};
var get_default = get;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isDate.js
var isDate = (value) => isType_default(value, "Date");
var isDate_default = isDate;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/cloneDeep.js
function cloneDeep(value, ignoreWhen, excludeKeys) {
  let result;
  if (!isValid_default(value) || "object" != typeof value || ignoreWhen && ignoreWhen(value)) return value;
  const isArr = isArray_default(value), length = value.length;
  result = isArr ? new Array(length) : "object" == typeof value ? {} : isBoolean_default(value) || isNumber_default(value) || isString_default(value) ? value : isDate_default(value) ? /* @__PURE__ */ new Date(+value) : void 0;
  const props = isArr ? void 0 : Object.keys(Object(value));
  let index = -1;
  if (result) for (; ++index < (props || value).length; ) {
    const key = props ? props[index] : index, subValue = value[key];
    excludeKeys && excludeKeys.includes(key.toString()) ? result[key] = subValue : result[key] = cloneDeep(subValue, ignoreWhen, excludeKeys);
  }
  return result;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isObjectLike.js
var isObjectLike = (value) => "object" == typeof value && null !== value;
var isObjectLike_default = isObjectLike;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isPlainObject.js
var isPlainObject = function(value) {
  if (!isObjectLike_default(value) || !isType_default(value, "Object")) return false;
  if (null === Object.getPrototypeOf(value)) return true;
  let proto = value;
  for (; null !== Object.getPrototypeOf(proto); ) proto = Object.getPrototypeOf(proto);
  return Object.getPrototypeOf(value) === proto;
};
var isPlainObject_default = isPlainObject;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/merge.js
function baseMerge(target, source, shallowArray = false, skipTargetArray = false) {
  if (source) {
    if (target === source) return;
    if (isValid_default(source) && "object" == typeof source) {
      const iterable = Object(source), props = [];
      for (const key in iterable) props.push(key);
      let { length } = props, propIndex = -1;
      for (; length--; ) {
        const key = props[++propIndex];
        !isValid_default(iterable[key]) || "object" != typeof iterable[key] || skipTargetArray && isArray_default(target[key]) ? assignMergeValue(target, key, iterable[key]) : baseMergeDeep(target, source, key, shallowArray, skipTargetArray);
      }
    }
  }
}
function baseMergeDeep(target, source, key, shallowArray = false, skipTargetArray = false) {
  const objValue = target[key], srcValue = source[key];
  let newValue = source[key], isCommon = true;
  if (isArray_default(srcValue)) {
    if (shallowArray) newValue = [];
    else if (isArray_default(objValue)) newValue = objValue;
    else if (isArrayLike_default(objValue)) {
      newValue = new Array(objValue.length);
      let index = -1;
      const length = objValue.length;
      for (; ++index < length; ) newValue[index] = objValue[index];
    }
  } else isPlainObject_default(srcValue) ? (newValue = null != objValue ? objValue : {}, "function" != typeof objValue && "object" == typeof objValue || (newValue = {})) : isCommon = false;
  isCommon && baseMerge(newValue, srcValue, shallowArray, skipTargetArray), assignMergeValue(target, key, newValue);
}
function assignMergeValue(target, key, value) {
  (void 0 !== value && !eq(target[key], value) || void 0 === value && !(key in target)) && (target[key] = value);
}
function eq(value, other) {
  return value === other || Number.isNaN(value) && Number.isNaN(other);
}
function merge(target, ...sources) {
  let sourceIndex = -1;
  const length = sources.length;
  for (; ++sourceIndex < length; ) {
    baseMerge(target, sources[sourceIndex], true);
  }
  return target;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/pickWithout.js
function pickWithout(obj, keys2) {
  if (!obj || !isPlainObject_default(obj)) return obj;
  const result = {};
  return Object.keys(obj).forEach((k2) => {
    const v2 = obj[k2];
    let match = false;
    keys2.forEach((itKey) => {
      (isString_default(itKey) && itKey === k2 || itKey instanceof RegExp && k2.match(itKey)) && (match = true);
    }), match || (result[k2] = v2);
  }), result;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isEqual.js
function objToString(obj) {
  return Object.prototype.toString.call(obj);
}
function objectKeys(obj) {
  return Object.keys(obj);
}
function isEqual(a2, b2, options) {
  if (a2 === b2) return true;
  if (typeof a2 != typeof b2) return false;
  if (null == a2 || null == b2) return false;
  if (Number.isNaN(a2) && Number.isNaN(b2)) return true;
  if (objToString(a2) !== objToString(b2)) return false;
  if (isFunction_default(a2)) return !!(null == options ? void 0 : options.skipFunction);
  if ("object" != typeof a2) return false;
  if (isArray_default(a2)) {
    if (a2.length !== b2.length) return false;
    for (let i2 = a2.length - 1; i2 >= 0; i2--) if (!isEqual(a2[i2], b2[i2], options)) return false;
    return true;
  }
  if (!isPlainObject_default(a2)) return false;
  const ka = objectKeys(a2), kb = objectKeys(b2);
  if (ka.length !== kb.length) return false;
  ka.sort(), kb.sort();
  for (let i2 = ka.length - 1; i2 >= 0; i2--) if (ka[i2] != kb[i2]) return false;
  for (let i2 = ka.length - 1; i2 >= 0; i2--) {
    const key = ka[i2];
    if (!isEqual(a2[key], b2[key], options)) return false;
  }
  return true;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/mixin.js
function keys(obj) {
  if (!obj) return [];
  if (Object.keys) return Object.keys(obj);
  const keyList = [];
  for (const key in obj) obj.hasOwnProperty(key) && keyList.push(key);
  return keyList;
}
function defaults(target, source, overlay) {
  const keysArr = keys(source);
  for (let i2 = 0; i2 < keysArr.length; i2++) {
    const key = keysArr[i2];
    (overlay ? null != source[key] : null == target[key]) && (target[key] = source[key]);
  }
  return target;
}
function mixin(target, source, override = true) {
  if (target = "prototype" in target ? target.prototype : target, source = "prototype" in source ? source.prototype : source, Object.getOwnPropertyNames) {
    const keyList = Object.getOwnPropertyNames(source);
    for (let i2 = 0; i2 < keyList.length; i2++) {
      const key = keyList[i2];
      "constructor" !== key && (override ? null != source[key] : null == target[key]) && (target[key] = source[key]);
    }
  } else defaults(target, source, override);
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/array.js
function array(arr) {
  return isValid_default(arr) ? isArray_default(arr) ? arr : [arr] : [];
}
function last(val) {
  if (isArrayLike_default(val)) {
    return val[val.length - 1];
  }
}
var maxInArray = (arr, compareFn) => {
  var _a;
  if (0 === arr.length) return;
  let max3 = arr[0];
  for (let i2 = 1; i2 < arr.length; i2++) {
    const value = arr[i2];
    (null !== (_a = null == compareFn ? void 0 : compareFn(value, max3)) && void 0 !== _a ? _a : value - max3) > 0 && (max3 = value);
  }
  return max3;
};
var minInArray = (arr, compareFn) => {
  var _a;
  if (0 === arr.length) return;
  let min3 = arr[0];
  for (let i2 = 1; i2 < arr.length; i2++) {
    const value = arr[i2];
    (null !== (_a = null == compareFn ? void 0 : compareFn(value, min3)) && void 0 !== _a ? _a : value - min3) < 0 && (min3 = value);
  }
  return min3;
};
function arrayEqual(a2, b2) {
  if (!isArray_default(a2) || !isArray_default(b2)) return false;
  if (a2.length !== b2.length) return false;
  for (let i2 = 0; i2 < a2.length; i2++) if (a2[i2] !== b2[i2]) return false;
  return true;
}
function uniqArray(arr) {
  return arr && isArray_default(arr) ? Array.from(new Set(array(arr))) : arr;
}
function shuffleArray(arr, random = Math.random) {
  let j2, x3, i2 = arr.length;
  for (; i2; ) j2 = Math.floor(random() * i2), x3 = arr[--i2], arr[i2] = arr[j2], arr[j2] = x3;
  return arr;
}
function flattenArray(arr) {
  if (!isArray_default(arr)) return [arr];
  const result = [];
  for (const value of arr) result.push(...flattenArray(value));
  return result;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/logger.js
var hasConsole = "undefined" != typeof console;
function log(method, level, input) {
  const args = [level].concat([].slice.call(input));
  hasConsole && console[method].apply(console, args);
}
var LoggerLevel;
!function(LoggerLevel2) {
  LoggerLevel2[LoggerLevel2.None = 0] = "None", LoggerLevel2[LoggerLevel2.Error = 1] = "Error", LoggerLevel2[LoggerLevel2.Warn = 2] = "Warn", LoggerLevel2[LoggerLevel2.Info = 3] = "Info", LoggerLevel2[LoggerLevel2.Debug = 4] = "Debug";
}(LoggerLevel || (LoggerLevel = {}));
var Logger = class _Logger {
  static getInstance(level, method) {
    return _Logger._instance && isNumber_default(level) ? _Logger._instance.level(level) : _Logger._instance || (_Logger._instance = new _Logger(level, method)), _Logger._instance;
  }
  static setInstance(logger) {
    return _Logger._instance = logger;
  }
  static setInstanceLevel(level) {
    _Logger._instance ? _Logger._instance.level(level) : _Logger._instance = new _Logger(level);
  }
  static clearInstance() {
    _Logger._instance = null;
  }
  constructor(level = LoggerLevel.None, method) {
    this._onErrorHandler = [], this._level = level, this._method = method;
  }
  addErrorHandler(handler) {
    this._onErrorHandler.find((h2) => h2 === handler) || this._onErrorHandler.push(handler);
  }
  removeErrorHandler(handler) {
    const index = this._onErrorHandler.findIndex((h2) => h2 === handler);
    index < 0 || this._onErrorHandler.splice(index, 1);
  }
  callErrorHandler(...args) {
    this._onErrorHandler.forEach((h2) => h2(...args));
  }
  canLogInfo() {
    return this._level >= LoggerLevel.Info;
  }
  canLogDebug() {
    return this._level >= LoggerLevel.Debug;
  }
  canLogError() {
    return this._level >= LoggerLevel.Error;
  }
  canLogWarn() {
    return this._level >= LoggerLevel.Warn;
  }
  level(levelValue) {
    return arguments.length ? (this._level = +levelValue, this) : this._level;
  }
  error(...args) {
    var _a;
    return this._level >= LoggerLevel.Error && (this._onErrorHandler.length ? this.callErrorHandler(...args) : log(null !== (_a = this._method) && void 0 !== _a ? _a : "error", "ERROR", args)), this;
  }
  warn(...args) {
    return this._level >= LoggerLevel.Warn && log(this._method || "warn", "WARN", args), this;
  }
  info(...args) {
    return this._level >= LoggerLevel.Info && log(this._method || "log", "INFO", args), this;
  }
  debug(...args) {
    return this._level >= LoggerLevel.Debug && log(this._method || "log", "DEBUG", args), this;
  }
};
Logger._instance = null;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/ascending.js
function ascending(a2, b2) {
  return a2 < b2 ? -1 : a2 > b2 ? 1 : a2 >= b2 ? 0 : NaN;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/bisect.js
function bisect(a2, x3, lo = 0, hi) {
  for (isNil_default(hi) && (hi = a2.length); lo < hi; ) {
    const mid = lo + hi >>> 1;
    ascending(a2[mid], x3) > 0 ? hi = mid : lo = mid + 1;
  }
  return lo;
}
function findZeroOfFunction(f2, a2, b2, parameters) {
  var _a, _b;
  const maxIterations = null !== (_a = null == parameters ? void 0 : parameters.maxIterations) && void 0 !== _a ? _a : 100, tolerance = null !== (_b = null == parameters ? void 0 : parameters.tolerance) && void 0 !== _b ? _b : 1e-10, fA = f2(a2), fB = f2(b2);
  let delta = b2 - a2;
  if (fA * fB > 0) {
    return Logger.getInstance().error("Initial bisect points must have opposite signs"), NaN;
  }
  if (0 === fA) return a2;
  if (0 === fB) return b2;
  for (let i2 = 0; i2 < maxIterations; ++i2) {
    delta /= 2;
    const mid = a2 + delta, fMid = f2(mid);
    if (fMid * fA >= 0 && (a2 = mid), Math.abs(delta) < tolerance || 0 === fMid) return mid;
  }
  return a2 + delta;
}
var binaryFuzzySearch = (arr, compareFn) => binaryFuzzySearchInNumberRange(0, arr.length, (value) => compareFn(arr[value]));
var binaryFuzzySearchInNumberRange = (x13, x22, compareFn) => {
  let left = x13, right = x22;
  for (; left < right; ) {
    const mid = Math.floor((left + right) / 2);
    compareFn(mid) >= 0 ? right = mid : left = mid + 1;
  }
  return left;
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/clamp.js
var clamp = function(input, min3, max3) {
  return input < min3 ? min3 : input > max3 ? max3 : input;
};
var clamp_default = clamp;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/clamper.js
function clamper(a2, b2) {
  let t2;
  return a2 > b2 && (t2 = a2, a2 = b2, b2 = t2), (x3) => Math.max(a2, Math.min(b2, x3));
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/debounce.js
var hasRaf = false;
try {
  hasRaf = "function" == typeof requestAnimationFrame && "function" == typeof cancelAnimationFrame;
} catch (err) {
  hasRaf = false;
}
function debounce(func, wait, options) {
  let lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;
  const useRAF = !wait && 0 !== wait && hasRaf;
  if ("function" != typeof func) throw new TypeError("Expected a function");
  function invokeFunc(time) {
    const args = lastArgs, thisArg = lastThis;
    return lastArgs = lastThis = void 0, lastInvokeTime = time, result = func.apply(thisArg, args), result;
  }
  function startTimer(pendingFunc, wait2) {
    return useRAF ? (cancelAnimationFrame(timerId), requestAnimationFrame(pendingFunc)) : setTimeout(pendingFunc, wait2);
  }
  function shouldInvoke(time) {
    const timeSinceLastCall = time - lastCallTime;
    return void 0 === lastCallTime || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && time - lastInvokeTime >= maxWait;
  }
  function timerExpired() {
    const time = Date.now();
    if (shouldInvoke(time)) return trailingEdge(time);
    timerId = startTimer(timerExpired, function(time2) {
      const timeSinceLastInvoke = time2 - lastInvokeTime, timeWaiting = wait - (time2 - lastCallTime);
      return maxing ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;
    }(time));
  }
  function trailingEdge(time) {
    return timerId = void 0, trailing && lastArgs ? invokeFunc(time) : (lastArgs = lastThis = void 0, result);
  }
  function debounced(...args) {
    const time = Date.now(), isInvoking = shouldInvoke(time);
    if (lastArgs = args, lastThis = this, lastCallTime = time, isInvoking) {
      if (void 0 === timerId) return function(time2) {
        return lastInvokeTime = time2, timerId = startTimer(timerExpired, wait), leading ? invokeFunc(time2) : result;
      }(lastCallTime);
      if (maxing) return timerId = startTimer(timerExpired, wait), invokeFunc(lastCallTime);
    }
    return void 0 === timerId && (timerId = startTimer(timerExpired, wait)), result;
  }
  return wait = +wait || 0, isObject_default(options) && (leading = !!options.leading, maxing = "maxWait" in options, maxing && (maxWait = Math.max(isValidNumber_default(options.maxWait) ? options.maxWait : 0, wait)), trailing = "trailing" in options ? !!options.trailing : trailing), debounced.cancel = function() {
    void 0 !== timerId && function(id) {
      if (useRAF) return cancelAnimationFrame(id);
      clearTimeout(id);
    }(timerId), lastInvokeTime = 0, lastArgs = lastCallTime = lastThis = timerId = void 0;
  }, debounced.flush = function() {
    return void 0 === timerId ? result : trailingEdge(Date.now());
  }, debounced.pending = function() {
    return void 0 !== timerId;
  }, debounced;
}
hasRaf = false;
var debounce_default = debounce;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/throttle.js
function throttle(func, wait, options) {
  let leading = true, trailing = true;
  if ("function" != typeof func) throw new TypeError("Expected a function");
  return isObject_default(options) && (leading = "leading" in options ? !!options.leading : leading, trailing = "trailing" in options ? !!options.trailing : trailing), debounce_default(func, wait, {
    leading,
    trailing,
    maxWait: wait
  });
}
var throttle_default = throttle;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isValidUrl.js
var isValidUrl = (value) => new RegExp(/^(http(s)?:\/\/)\w+[^\s]+(\.[^\s]+){1,}$/).test(value);
var isValidUrl_default = isValidUrl;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/isBase64.js
var isBase64 = (value) => new RegExp(/^data:image\/(?:gif|png|jpeg|bmp|webp|svg\+xml)(?:;charset=utf-8)?;base64,(?:[A-Za-z0-9]|[+/])+={0,2}/g).test(value);
var isBase64_default = isBase64;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/has.js
var hasOwnProperty2 = Object.prototype.hasOwnProperty;
var has = (object2, key) => null != object2 && hasOwnProperty2.call(object2, key);
var has_default = has;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/range.js
function range(start, stop, step) {
  isValid_default(stop) || (stop = start, start = 0), isValid_default(step) || (step = 1);
  let i2 = -1;
  const n2 = 0 | Math.max(0, Math.ceil((stop - start) / step)), range2 = new Array(n2);
  for (; ++i2 < n2; ) range2[i2] = start + i2 * step;
  return range2;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/toNumber.js
function toNumber(a2) {
  return Number(a2);
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/quantileSorted.js
function quantileSorted(values, percent, valueof = toNumber) {
  const n2 = values.length;
  if (!n2) return;
  if (percent <= 0 || n2 < 2) return valueof(values[0], 0, values);
  if (percent >= 1) return valueof(values[n2 - 1], n2 - 1, values);
  const i2 = (n2 - 1) * percent, i0 = Math.floor(i2), value0 = valueof(values[i0], i0, values);
  return value0 + (valueof(values[i0 + 1], i0 + 1, values) - value0) * (i2 - i0);
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/median.js
var median = (values, isSorted) => {
  let sorted = values;
  return true !== isSorted && (sorted = values.sort(ascending)), quantileSorted(sorted, 0.5);
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/tickStep.js
var e10 = Math.sqrt(50);
var e5 = Math.sqrt(10);
var e2 = Math.sqrt(2);

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/number.js
var DEFAULT_ABSOLUTE_TOLERATE = 1e-10;
var DEFAULT_RELATIVE_TOLERATE = 1e-10;
function isNumberClose(a2, b2, relTol = DEFAULT_RELATIVE_TOLERATE, absTol = DEFAULT_ABSOLUTE_TOLERATE) {
  const abs3 = absTol, rel = relTol * Math.max(a2, b2);
  return Math.abs(a2 - b2) <= Math.max(abs3, rel);
}
function isGreater(a2, b2, relTol, absTol) {
  return a2 > b2 && !isNumberClose(a2, b2, relTol, absTol);
}
function isLess(a2, b2, relTol, absTol) {
  return a2 < b2 && !isNumberClose(a2, b2, relTol, absTol);
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/memoize.js
var memoize = (func) => {
  let lastArgs = null, lastResult = null;
  return (...args) => (lastArgs && args.every((val, i2) => val === lastArgs[i2]) || (lastArgs = args, lastResult = func(...args)), lastResult);
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/clampRange.js
var clampRange = (range2, min3, max3) => {
  let [lowValue, highValue] = range2;
  highValue < lowValue && (lowValue = range2[1], highValue = range2[0]);
  const span = highValue - lowValue;
  return span >= max3 - min3 ? [min3, max3] : (lowValue = Math.min(Math.max(lowValue, min3), max3 - span), [lowValue, lowValue + span]);
};
var clampRange_default = clampRange;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/interpolate.js
function interpolateNumber(a2, b2) {
  return (t2) => a2 * (1 - t2) + b2 * t2;
}
function interpolateNumberRound(a2, b2) {
  return function(t2) {
    return Math.round(a2 * (1 - t2) + b2 * t2);
  };
}
function interpolateDate(a2, b2) {
  const aVal = a2.valueOf(), bVal = b2.valueOf(), d2 = /* @__PURE__ */ new Date();
  return (t2) => (d2.setTime(aVal * (1 - t2) + bVal * t2), d2);
}
var reA = /[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g;
var reB = new RegExp(reA.source, "g");
function zero(b2) {
  return function() {
    return b2;
  };
}
function one(b2) {
  return function(t2) {
    return b2(t2) + "";
  };
}
function interpolateString(a2, b2) {
  let am, bm, bs, bi = reA.lastIndex = reB.lastIndex = 0, i2 = -1;
  const s2 = [], q2 = [];
  for (a2 += "", b2 += ""; (am = reA.exec(a2)) && (bm = reB.exec(b2)); ) (bs = bm.index) > bi && (bs = b2.slice(bi, bs), s2[i2] ? s2[i2] += bs : s2[++i2] = bs), (am = am[0]) === (bm = bm[0]) ? s2[i2] ? s2[i2] += bm : s2[++i2] = bm : (s2[++i2] = null, q2.push({
    i: i2,
    x: interpolateNumber(am, bm)
  })), bi = reB.lastIndex;
  return bi < b2.length && (bs = b2.slice(bi), s2[i2] ? s2[i2] += bs : s2[++i2] = bs), s2.length < 2 ? q2[0] ? one(q2[0].x) : zero(b2) : (b2 = q2.length, function(t2) {
    for (let o2, i3 = 0; i3 < b2; ++i3) s2[(o2 = q2[i3]).i] = o2.x(t2);
    return s2.join("");
  });
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/toValidNumber.js
function toValidNumber(v2) {
  if (isValidNumber_default(v2)) return v2;
  const value = +v2;
  return isValidNumber_default(value) ? value : 0;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/common/random.js
function seedRandom(seed) {
  return parseFloat("0." + Math.sin(seed).toString().substring(6));
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/math.js
var epsilon = 1e-12;
var pi = Math.PI;
var halfPi = pi / 2;
var tau = 2 * pi;
var pi2 = 2 * Math.PI;
var abs = Math.abs;
var atan2 = Math.atan2;
var cos = Math.cos;
var max = Math.max;
var min = Math.min;
var sin = Math.sin;
var sqrt = Math.sqrt;
var pow = Math.pow;
function acos(x3) {
  return x3 > 1 ? 0 : x3 < -1 ? pi : Math.acos(x3);
}
function asin(x3) {
  return x3 >= 1 ? halfPi : x3 <= -1 ? -halfPi : Math.asin(x3);
}
function pointAt(x13, y13, x22, y22, t2) {
  let x3 = x22, y3 = y22;
  return "number" == typeof x13 && "number" == typeof x22 && (x3 = (1 - t2) * x13 + t2 * x22), "number" == typeof y13 && "number" == typeof y22 && (y3 = (1 - t2) * y13 + t2 * y22), {
    x: x3,
    y: y3
  };
}
function crossProduct(dir1, dir2) {
  return dir1[0] * dir2[1] - dir1[1] * dir2[0];
}
function dotProduct(a2, b2) {
  let ret = 0;
  for (let i2 = 0; i2 < a2.length; ++i2) ret += a2[i2] * b2[i2];
  return ret;
}
function fuzzyEqualVec(a2, b2) {
  return abs(a2[0] - b2[0]) + abs(a2[1] - b2[1]) < 1e-12;
}
function fixPrecision(num, precision = 10) {
  return Math.round(num * precision) / precision;
}
function getDecimalPlaces(n2) {
  const dStr = n2.toString().split(/[eE]/), s2 = (dStr[0].split(".")[1] || "").length - (+dStr[1] || 0);
  return s2 > 0 ? s2 : 0;
}
function precisionAdd(a2, b2) {
  return fixPrecision(a2 + b2, 10 ** Math.max(getDecimalPlaces(a2), getDecimalPlaces(b2)));
}
function precisionSub(a2, b2) {
  return fixPrecision(a2 - b2, 10 ** Math.max(getDecimalPlaces(a2), getDecimalPlaces(b2)));
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/data-structure/point.js
var Point = class _Point {
  constructor(x3 = 0, y3 = 0, x13, y13) {
    this.x = 0, this.y = 0, this.x = x3, this.y = y3, this.x1 = x13, this.y1 = y13;
  }
  clone() {
    return new _Point(this.x, this.y);
  }
  copyFrom(p2) {
    return this.x = p2.x, this.y = p2.y, this.x1 = p2.x1, this.y1 = p2.y1, this.defined = p2.defined, this.context = p2.context, this;
  }
  set(x3, y3) {
    return this.x = x3, this.y = y3, this;
  }
  add(point2) {
    return isNumber_default(point2) ? (this.x += point2, void (this.y += point2)) : (this.x += point2.x, this.y += point2.y, this);
  }
  sub(point2) {
    return isNumber_default(point2) ? (this.x -= point2, void (this.y -= point2)) : (this.x -= point2.x, this.y -= point2.y, this);
  }
  multi(point2) {
    throw new Error("暂不支持");
  }
  div(point2) {
    throw new Error("暂不支持");
  }
};
var PointService = class {
  static distancePP(p1, p2) {
    return sqrt(pow(p1.x - p2.x, 2) + pow(p1.y - p2.y, 2));
  }
  static distanceNN(x3, y3, x13, y13) {
    return sqrt(pow(x3 - x13, 2) + pow(y3 - y13, 2));
  }
  static distancePN(point2, x3, y3) {
    return sqrt(pow(x3 - point2.x, 2) + pow(y3 - point2.y, 2));
  }
  static pointAtPP(p1, p2, t2) {
    return new Point((p2.x - p1.x) * t2 + p1.x, (p2.y - p1.y) * t2 + p1.y);
  }
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/angle.js
function degreeToRadian(degree) {
  return degree * (Math.PI / 180);
}
function radianToDegree(radian) {
  return 180 * radian / Math.PI;
}
var clampRadian = (angle = 0) => {
  if (angle < 0) for (; angle < -tau; ) angle += tau;
  else if (angle > 0) for (; angle > tau; ) angle -= tau;
  return angle;
};
var clampAngleByRadian = clampRadian;
function polarToCartesian(center, radius, angleInRadian) {
  return radius ? {
    x: center.x + radius * Math.cos(angleInRadian),
    y: center.y + radius * Math.sin(angleInRadian)
  } : {
    x: center.x,
    y: center.y
  };
}
function cartesianToPolar(point2, center = {
  x: 0,
  y: 0
}, startAngle = 0, endAngle = 2 * Math.PI) {
  const { x: x3, y: y3 } = point2, { x: centerX, y: centerY } = center;
  let dx = x3 - centerX, dy = y3 - centerY;
  const radius = Math.sqrt(dx * dx + dy * dy);
  if (0 === radius) return {
    radius: 0,
    angle: 0
  };
  dx /= radius, dy /= radius;
  let radian = Math.atan2(dy, dx);
  if (radian < startAngle) for (; radian <= startAngle; ) radian += 2 * Math.PI;
  if (radian > endAngle) for (; radian >= endAngle; ) radian -= 2 * Math.PI;
  return {
    radius,
    angle: radian
  };
}
function getAngleByPoint(center, point2) {
  return Math.atan2(point2.y - center.y, point2.x - center.x);
}
function normalizeAngle(angle) {
  for (; angle < 0; ) angle += 2 * Math.PI;
  for (; angle >= 2 * Math.PI; ) angle -= 2 * Math.PI;
  return angle;
}
function findBoundaryAngles(startAngle, endAngle) {
  const deltaAngle = Math.abs(endAngle - startAngle);
  if (deltaAngle >= 2 * Math.PI || 2 * Math.PI - deltaAngle < 1e-6) return [0, Math.PI / 2, Math.PI, 1.5 * Math.PI];
  const normalMin = normalizeAngle(Math.min(startAngle, endAngle)), normalMax = normalMin + deltaAngle, steps = [normalMin, normalMax];
  let directionAngle = Math.floor(normalMin / Math.PI) * Math.PI / 2;
  for (; directionAngle < normalMax; ) directionAngle > normalMin && steps.push(directionAngle), directionAngle += Math.PI / 2;
  return steps;
}
function calculateMaxRadius(rect, center, startAngle, endAngle) {
  const { x: x3, y: y3 } = center, steps = findBoundaryAngles(startAngle, endAngle), { width, height } = rect, radiusList = [];
  return steps.forEach((step) => {
    const sin3 = Math.sin(step), cos3 = Math.cos(step);
    1 === sin3 ? radiusList.push(height - y3) : -1 === sin3 ? radiusList.push(y3) : 1 === cos3 ? radiusList.push(width - x3) : -1 === cos3 ? radiusList.push(x3) : (sin3 > 0 ? radiusList.push(Math.abs((height - y3) / sin3)) : radiusList.push(Math.abs(y3 / sin3)), cos3 > 0 ? radiusList.push(Math.abs((width - x3) / cos3)) : radiusList.push(Math.abs(x3 / cos3)));
  }), Math.min.apply(null, radiusList);
}
function computeQuadrant(angle) {
  return (angle = normalizeAngle(angle)) > 0 && angle <= Math.PI / 2 ? 2 : angle > Math.PI / 2 && angle <= Math.PI ? 3 : angle > Math.PI && angle <= 3 * Math.PI / 2 ? 4 : 1;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/graphics/algorithm/intersect.js
function sub(out, v1, v2) {
  out[0] = v1[0] - v2[0], out[1] = v1[1] - v2[1];
}
function isIntersect(left1, right1, left2, right2) {
  let min1 = left1[0], max1 = right1[0], min22 = left2[0], max22 = right2[0];
  return max1 < min1 && ([min1, max1] = [max1, min1]), max22 < min22 && ([max22, min22] = [min22, max22]), !(max1 < min22 || max22 < min1) && (min1 = left1[1], max1 = right1[1], min22 = left2[1], max22 = right2[1], max1 < min1 && ([min1, max1] = [max1, min1]), max22 < min22 && ([max22, min22] = [min22, max22]), !(max1 < min22 || max22 < min1));
}
function getIntersectPoint(left1, right1, left2, right2) {
  if (!isIntersect(left1, right1, left2, right2)) return false;
  const dir1 = [0, 0], dir2 = [0, 0], tempVec = [0, 0];
  if (sub(dir1, right1, left1), sub(dir2, right2, left2), fuzzyEqualVec(dir1, dir2)) return true;
  sub(tempVec, left2, left1);
  const t2 = crossProduct(tempVec, dir2) / crossProduct(dir1, dir2);
  return t2 >= 0 && t2 <= 1 && [left1[0] + dir1[0] * t2, left1[1] + dir1[1] * t2];
}
function getRectIntersect(bbox1, bbox2, format) {
  if (null === bbox1) return bbox2;
  if (null === bbox2) return bbox1;
  const { x11, x12: x122, y11, y12: y122, x21, x22, y21, y22 } = formatTwoBBox(bbox1, bbox2, format);
  return x11 >= x22 || x122 <= x21 || y11 >= y22 || y122 <= y21 ? {
    x1: 0,
    y1: 0,
    x2: 0,
    y2: 0
  } : {
    x1: Math.max(x11, x21),
    y1: Math.max(y11, y21),
    x2: Math.min(x122, x22),
    y2: Math.min(y122, y22)
  };
}
var InnerBBox;
!function(InnerBBox2) {
  InnerBBox2[InnerBBox2.NONE = 0] = "NONE", InnerBBox2[InnerBBox2.BBOX1 = 1] = "BBOX1", InnerBBox2[InnerBBox2.BBOX2 = 2] = "BBOX2";
}(InnerBBox || (InnerBBox = {}));
var formatTwoBBox = (bbox1, bbox2, format) => {
  let x11 = bbox1.x1, x122 = bbox1.x2, y11 = bbox1.y1, y122 = bbox1.y2, x21 = bbox2.x1, x22 = bbox2.x2, y21 = bbox2.y1, y22 = bbox2.y2;
  return format && (x11 > x122 && ([x11, x122] = [x122, x11]), y11 > y122 && ([y11, y122] = [y122, y11]), x21 > x22 && ([x21, x22] = [x22, x21]), y21 > y22 && ([y21, y22] = [y22, y21])), {
    x11,
    x12: x122,
    y11,
    y12: y122,
    x21,
    x22,
    y21,
    y22
  };
};
function rectInsideAnotherRect(bbox1, bbox2, format) {
  if (!bbox1 || !bbox2) return InnerBBox.NONE;
  const { x11, x12: x122, y11, y12: y122, x21, x22, y21, y22 } = formatTwoBBox(bbox1, bbox2, format);
  return x11 > x21 && x122 < x22 && y11 > y21 && y122 < y22 ? InnerBBox.BBOX1 : x21 > x11 && x22 < x122 && y21 > y11 && y22 < y122 ? InnerBBox.BBOX2 : InnerBBox.NONE;
}
function isRectIntersect(bbox1, bbox2, format) {
  if (bbox1 && bbox2) {
    if (!format) return !(bbox1.x1 > bbox2.x2 || bbox1.x2 < bbox2.x1 || bbox1.y1 > bbox2.y2 || bbox1.y2 < bbox2.y1);
    const { x11, x12: x122, y11, y12: y122, x21, x22, y21, y22 } = formatTwoBBox(bbox1, bbox2, true);
    return !(x11 > x22 || x122 < x21 || y11 > y22 || y122 < y21);
  }
  return true;
}
function pointInRect(point2, bbox, format) {
  if (!bbox) return true;
  if (!format) return point2.x >= bbox.x1 && point2.x <= bbox.x2 && point2.y >= bbox.y1 && point2.y <= bbox.y2;
  let x11 = bbox.x1, x122 = bbox.x2, y11 = bbox.y1, y122 = bbox.y2;
  return x11 > x122 && ([x11, x122] = [x122, x11]), y11 > y122 && ([y11, y122] = [y122, y11]), point2.x >= x11 && point2.x <= x122 && point2.y >= y11 && point2.y <= y122;
}
function getProjectionRadius(checkAxis, axis) {
  return Math.abs(axis[0] * checkAxis[0] + axis[1] * checkAxis[1]);
}
function rotatePoint({ x: x3, y: y3 }, rad, origin = {
  x: 0,
  y: 0
}) {
  return {
    x: (x3 - origin.x) * Math.cos(rad) - (y3 - origin.y) * Math.sin(rad) + origin.x,
    y: (x3 - origin.x) * Math.sin(rad) + (y3 - origin.y) * Math.cos(rad) + origin.y
  };
}
function getCenterPoint(box) {
  return {
    x: (box.x1 + box.x2) / 2,
    y: (box.y1 + box.y2) / 2
  };
}
function toRect(box, isDeg) {
  const deg = isDeg ? degreeToRadian(box.angle) : box.angle, cp = getCenterPoint(box);
  return [rotatePoint({
    x: box.x1,
    y: box.y1
  }, deg, cp), rotatePoint({
    x: box.x2,
    y: box.y1
  }, deg, cp), rotatePoint({
    x: box.x2,
    y: box.y2
  }, deg, cp), rotatePoint({
    x: box.x1,
    y: box.y2
  }, deg, cp)];
}
function isRotateAABBIntersect(box1, box2, isDeg = false) {
  const rect1 = toRect(box1, isDeg), rect2 = toRect(box2, isDeg), vector = (start, end) => [end.x - start.x, end.y - start.y], vp1p2 = vector(getCenterPoint(box1), getCenterPoint(box2)), AB = vector(rect1[0], rect1[1]), BC = vector(rect1[1], rect1[2]), A1B1 = vector(rect2[0], rect2[1]), B1C1 = vector(rect2[1], rect2[2]), deg11 = isDeg ? degreeToRadian(box1.angle) : box1.angle;
  let deg12 = isDeg ? degreeToRadian(90 - box1.angle) : box1.angle + halfPi;
  const deg21 = isDeg ? degreeToRadian(box2.angle) : box2.angle;
  let deg22 = isDeg ? degreeToRadian(90 - box2.angle) : box2.angle + halfPi;
  deg12 > pi2 && (deg12 -= pi2), deg22 > pi2 && (deg22 -= pi2);
  const isCover = (checkAxisRadius, deg, targetAxis1, targetAxis2) => {
    const checkAxis = [Math.cos(deg), Math.sin(deg)];
    return checkAxisRadius + (getProjectionRadius(checkAxis, targetAxis1) + getProjectionRadius(checkAxis, targetAxis2)) / 2 > getProjectionRadius(checkAxis, vp1p2);
  };
  return isCover((box1.x2 - box1.x1) / 2, deg11, A1B1, B1C1) && isCover((box1.y2 - box1.y1) / 2, deg12, A1B1, B1C1) && isCover((box2.x2 - box2.x1) / 2, deg21, AB, BC) && isCover((box2.y2 - box2.y1) / 2, deg22, AB, BC);
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/graphics/algorithm/aabb.js
var x1;
var y1;
var x2;
var y2;
function getAABBFromPoints(points) {
  return x1 = 1 / 0, y1 = 1 / 0, x2 = -1 / 0, y2 = -1 / 0, points.forEach((point2) => {
    x1 > point2.x && (x1 = point2.x), x2 < point2.x && (x2 = point2.x), y1 > point2.y && (y1 = point2.y), y2 < point2.y && (y2 = point2.y);
  }), {
    x1,
    y1,
    x2,
    y2
  };
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/graphics/polygon.js
var EPSILON = 1e-8;
function lineIntersectPolygon(a1x, a1y, a2x, a2y, points) {
  for (let i2 = 0, p2 = points[points.length - 1]; i2 < points.length; i2++) {
    const p3 = points[i2];
    if (isIntersect([a1x, a1y], [a2x, a2y], [p3.x, p3.y], [p2.x, p2.y])) return true;
    p2 = p3;
  }
  return false;
}
function polygonContainPoint(points, x3, y3) {
  let w2 = 0, p2 = points[0];
  if (!p2) return false;
  for (let i2 = 1; i2 < points.length; i2++) {
    const p22 = points[i2];
    w2 += isPointInLine(p2.x, p2.y, p22.x, p22.y, x3, y3), p2 = p22;
  }
  const p0 = points[0];
  return isAroundEqual(p2.x, p0.x) && isAroundEqual(p2.y, p0.y) || (w2 += isPointInLine(p2.x, p2.y, p0.x, p0.y, x3, y3)), 0 !== w2;
}
function isPointInLine(x05, y05, x13, y13, x3, y3) {
  if (y3 > y05 && y3 > y13 || y3 < y05 && y3 < y13) return 0;
  if (y13 === y05) return 0;
  const t2 = (y3 - y05) / (y13 - y05);
  let dir = y13 < y05 ? 1 : -1;
  1 !== t2 && 0 !== t2 || (dir = y13 < y05 ? 0.5 : -0.5);
  const x_ = t2 * (x13 - x05) + x05;
  return x_ === x3 ? 1 / 0 : x_ > x3 ? dir : 0;
}
function isAroundEqual(a2, b2) {
  return Math.abs(a2 - b2) < EPSILON;
}
function polygonIntersectPolygon(pointsA, pointsB) {
  for (let i2 = 0; i2 < pointsB.length; i2++) {
    if (polygonContainPoint(pointsA, pointsB[i2].x, pointsB[i2].y)) return true;
    if (i2 > 0 && lineIntersectPolygon(pointsB[i2 - 1].x, pointsB[i2 - 1].y, pointsB[i2].x, pointsB[i2].y, pointsA)) return true;
  }
  return false;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/graphics/bounds-util.js
var calculateAnchorOfBounds = (bounds, anchorType) => {
  const { x1: x13, x2: x22, y1: y13, y2: y22 } = bounds, rectWidth = Math.abs(x22 - x13), rectHeight = Math.abs(y22 - y13);
  let anchorX = (x13 + x22) / 2, anchorY = (y13 + y22) / 2, sx = 0, sy = 0;
  switch (anchorType) {
    case "top":
    case "inside-top":
      sy = -0.5;
      break;
    case "bottom":
    case "inside-bottom":
      sy = 0.5;
      break;
    case "left":
    case "inside-left":
      sx = -0.5;
      break;
    case "right":
    case "inside-right":
      sx = 0.5;
      break;
    case "top-right":
      sx = 0.5, sy = -0.5;
      break;
    case "top-left":
      sx = -0.5, sy = -0.5;
      break;
    case "bottom-right":
      sx = 0.5, sy = 0.5;
      break;
    case "bottom-left":
      sx = -0.5, sy = 0.5;
  }
  return anchorX += sx * rectWidth, anchorY += sy * rectHeight, {
    x: anchorX,
    y: anchorY
  };
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/graphics/text/stringWidth.js
var eastAsianCharacterInfo = (character) => {
  let x3 = character.charCodeAt(0), y3 = 2 === character.length ? character.charCodeAt(1) : 0, codePoint = x3;
  return 55296 <= x3 && x3 <= 56319 && 56320 <= y3 && y3 <= 57343 && (x3 &= 1023, y3 &= 1023, codePoint = x3 << 10 | y3, codePoint += 65536), 12288 === codePoint || 65281 <= codePoint && codePoint <= 65376 || 65504 <= codePoint && codePoint <= 65510 ? "F" : 8361 === codePoint || 65377 <= codePoint && codePoint <= 65470 || 65474 <= codePoint && codePoint <= 65479 || 65482 <= codePoint && codePoint <= 65487 || 65490 <= codePoint && codePoint <= 65495 || 65498 <= codePoint && codePoint <= 65500 || 65512 <= codePoint && codePoint <= 65518 ? "H" : 4352 <= codePoint && codePoint <= 4447 || 4515 <= codePoint && codePoint <= 4519 || 4602 <= codePoint && codePoint <= 4607 || 9001 <= codePoint && codePoint <= 9002 || 11904 <= codePoint && codePoint <= 11929 || 11931 <= codePoint && codePoint <= 12019 || 12032 <= codePoint && codePoint <= 12245 || 12272 <= codePoint && codePoint <= 12283 || 12289 <= codePoint && codePoint <= 12350 || 12353 <= codePoint && codePoint <= 12438 || 12441 <= codePoint && codePoint <= 12543 || 12549 <= codePoint && codePoint <= 12589 || 12593 <= codePoint && codePoint <= 12686 || 12688 <= codePoint && codePoint <= 12730 || 12736 <= codePoint && codePoint <= 12771 || 12784 <= codePoint && codePoint <= 12830 || 12832 <= codePoint && codePoint <= 12871 || 12880 <= codePoint && codePoint <= 13054 || 13056 <= codePoint && codePoint <= 19903 || 19968 <= codePoint && codePoint <= 42124 || 42128 <= codePoint && codePoint <= 42182 || 43360 <= codePoint && codePoint <= 43388 || 44032 <= codePoint && codePoint <= 55203 || 55216 <= codePoint && codePoint <= 55238 || 55243 <= codePoint && codePoint <= 55291 || 63744 <= codePoint && codePoint <= 64255 || 65040 <= codePoint && codePoint <= 65049 || 65072 <= codePoint && codePoint <= 65106 || 65108 <= codePoint && codePoint <= 65126 || 65128 <= codePoint && codePoint <= 65131 || 110592 <= codePoint && codePoint <= 110593 || 127488 <= codePoint && codePoint <= 127490 || 127504 <= codePoint && codePoint <= 127546 || 127552 <= codePoint && codePoint <= 127560 || 127568 <= codePoint && codePoint <= 127569 || 131072 <= codePoint && codePoint <= 194367 || 177984 <= codePoint && codePoint <= 196605 || 196608 <= codePoint && codePoint <= 262141 ? "W" : 32 <= codePoint && codePoint <= 126 || 162 <= codePoint && codePoint <= 163 || 165 <= codePoint && codePoint <= 166 || 172 === codePoint || 175 === codePoint || 10214 <= codePoint && codePoint <= 10221 || 10629 <= codePoint && codePoint <= 10630 ? "Na" : 161 === codePoint || 164 === codePoint || 167 <= codePoint && codePoint <= 168 || 170 === codePoint || 173 <= codePoint && codePoint <= 174 || 176 <= codePoint && codePoint <= 180 || 182 <= codePoint && codePoint <= 186 || 188 <= codePoint && codePoint <= 191 || 198 === codePoint || 208 === codePoint || 215 <= codePoint && codePoint <= 216 || 222 <= codePoint && codePoint <= 225 || 230 === codePoint || 232 <= codePoint && codePoint <= 234 || 236 <= codePoint && codePoint <= 237 || 240 === codePoint || 242 <= codePoint && codePoint <= 243 || 247 <= codePoint && codePoint <= 250 || 252 === codePoint || 254 === codePoint || 257 === codePoint || 273 === codePoint || 275 === codePoint || 283 === codePoint || 294 <= codePoint && codePoint <= 295 || 299 === codePoint || 305 <= codePoint && codePoint <= 307 || 312 === codePoint || 319 <= codePoint && codePoint <= 322 || 324 === codePoint || 328 <= codePoint && codePoint <= 331 || 333 === codePoint || 338 <= codePoint && codePoint <= 339 || 358 <= codePoint && codePoint <= 359 || 363 === codePoint || 462 === codePoint || 464 === codePoint || 466 === codePoint || 468 === codePoint || 470 === codePoint || 472 === codePoint || 474 === codePoint || 476 === codePoint || 593 === codePoint || 609 === codePoint || 708 === codePoint || 711 === codePoint || 713 <= codePoint && codePoint <= 715 || 717 === codePoint || 720 === codePoint || 728 <= codePoint && codePoint <= 731 || 733 === codePoint || 735 === codePoint || 768 <= codePoint && codePoint <= 879 || 913 <= codePoint && codePoint <= 929 || 931 <= codePoint && codePoint <= 937 || 945 <= codePoint && codePoint <= 961 || 963 <= codePoint && codePoint <= 969 || 1025 === codePoint || 1040 <= codePoint && codePoint <= 1103 || 1105 === codePoint || 8208 === codePoint || 8211 <= codePoint && codePoint <= 8214 || 8216 <= codePoint && codePoint <= 8217 || 8220 <= codePoint && codePoint <= 8221 || 8224 <= codePoint && codePoint <= 8226 || 8228 <= codePoint && codePoint <= 8231 || 8240 === codePoint || 8242 <= codePoint && codePoint <= 8243 || 8245 === codePoint || 8251 === codePoint || 8254 === codePoint || 8308 === codePoint || 8319 === codePoint || 8321 <= codePoint && codePoint <= 8324 || 8364 === codePoint || 8451 === codePoint || 8453 === codePoint || 8457 === codePoint || 8467 === codePoint || 8470 === codePoint || 8481 <= codePoint && codePoint <= 8482 || 8486 === codePoint || 8491 === codePoint || 8531 <= codePoint && codePoint <= 8532 || 8539 <= codePoint && codePoint <= 8542 || 8544 <= codePoint && codePoint <= 8555 || 8560 <= codePoint && codePoint <= 8569 || 8585 === codePoint || 8592 <= codePoint && codePoint <= 8601 || 8632 <= codePoint && codePoint <= 8633 || 8658 === codePoint || 8660 === codePoint || 8679 === codePoint || 8704 === codePoint || 8706 <= codePoint && codePoint <= 8707 || 8711 <= codePoint && codePoint <= 8712 || 8715 === codePoint || 8719 === codePoint || 8721 === codePoint || 8725 === codePoint || 8730 === codePoint || 8733 <= codePoint && codePoint <= 8736 || 8739 === codePoint || 8741 === codePoint || 8743 <= codePoint && codePoint <= 8748 || 8750 === codePoint || 8756 <= codePoint && codePoint <= 8759 || 8764 <= codePoint && codePoint <= 8765 || 8776 === codePoint || 8780 === codePoint || 8786 === codePoint || 8800 <= codePoint && codePoint <= 8801 || 8804 <= codePoint && codePoint <= 8807 || 8810 <= codePoint && codePoint <= 8811 || 8814 <= codePoint && codePoint <= 8815 || 8834 <= codePoint && codePoint <= 8835 || 8838 <= codePoint && codePoint <= 8839 || 8853 === codePoint || 8857 === codePoint || 8869 === codePoint || 8895 === codePoint || 8978 === codePoint || 9312 <= codePoint && codePoint <= 9449 || 9451 <= codePoint && codePoint <= 9547 || 9552 <= codePoint && codePoint <= 9587 || 9600 <= codePoint && codePoint <= 9615 || 9618 <= codePoint && codePoint <= 9621 || 9632 <= codePoint && codePoint <= 9633 || 9635 <= codePoint && codePoint <= 9641 || 9650 <= codePoint && codePoint <= 9651 || 9654 <= codePoint && codePoint <= 9655 || 9660 <= codePoint && codePoint <= 9661 || 9664 <= codePoint && codePoint <= 9665 || 9670 <= codePoint && codePoint <= 9672 || 9675 === codePoint || 9678 <= codePoint && codePoint <= 9681 || 9698 <= codePoint && codePoint <= 9701 || 9711 === codePoint || 9733 <= codePoint && codePoint <= 9734 || 9737 === codePoint || 9742 <= codePoint && codePoint <= 9743 || 9748 <= codePoint && codePoint <= 9749 || 9756 === codePoint || 9758 === codePoint || 9792 === codePoint || 9794 === codePoint || 9824 <= codePoint && codePoint <= 9825 || 9827 <= codePoint && codePoint <= 9829 || 9831 <= codePoint && codePoint <= 9834 || 9836 <= codePoint && codePoint <= 9837 || 9839 === codePoint || 9886 <= codePoint && codePoint <= 9887 || 9918 <= codePoint && codePoint <= 9919 || 9924 <= codePoint && codePoint <= 9933 || 9935 <= codePoint && codePoint <= 9953 || 9955 === codePoint || 9960 <= codePoint && codePoint <= 9983 || 10045 === codePoint || 10071 === codePoint || 10102 <= codePoint && codePoint <= 10111 || 11093 <= codePoint && codePoint <= 11097 || 12872 <= codePoint && codePoint <= 12879 || 57344 <= codePoint && codePoint <= 63743 || 65024 <= codePoint && codePoint <= 65039 || 65533 === codePoint || 127232 <= codePoint && codePoint <= 127242 || 127248 <= codePoint && codePoint <= 127277 || 127280 <= codePoint && codePoint <= 127337 || 127344 <= codePoint && codePoint <= 127386 || 917760 <= codePoint && codePoint <= 917999 || 983040 <= codePoint && codePoint <= 1048573 || 1048576 <= codePoint && codePoint <= 1114109 ? "A" : "N";
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/graphics/text/measure/util.js
function getContextFont(text, defaultAttr = {}, fontSizeScale) {
  fontSizeScale || (fontSizeScale = 1);
  const { fontStyle = defaultAttr.fontStyle, fontVariant = defaultAttr.fontVariant, fontWeight = defaultAttr.fontWeight, fontSize = defaultAttr.fontSize, fontFamily = defaultAttr.fontFamily } = text;
  return (fontStyle ? fontStyle + " " : "") + (fontVariant ? fontVariant + " " : "") + (fontWeight ? fontWeight + " " : "") + fontSize * fontSizeScale + "px " + (fontFamily || "sans-serif");
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/graphics/text/measure/textMeasure.js
var TextMeasure = class _TextMeasure {
  constructor(option, textSpec) {
    this._numberCharSize = null, this._fullCharSize = null, this._letterCharSize = null, this._specialCharSizeMap = {}, this._canvas = null, this._context = null, this._contextSaved = false, this._notSupportCanvas = false, this._notSupportVRender = false, this._userSpec = {}, this.specialCharSet = `-/: .,@%'"~`, this._option = option, this._userSpec = null != textSpec ? textSpec : {}, this.textSpec = this._initSpec(), isValid_default(option.specialCharSet) && (this.specialCharSet = option.specialCharSet), this._standardMethod = isValid_default(option.getTextBounds) ? this.fullMeasure.bind(this) : this.measureWithNaiveCanvas.bind(this);
  }
  initContext() {
    if (this._notSupportCanvas) return false;
    if (isNil_default(this._canvas) && (isValid_default(this._option.getCanvasForMeasure) && (this._canvas = this._option.getCanvasForMeasure()), isNil_default(this._canvas) && "undefined" != typeof window && void 0 !== window.document && globalThis && isValid_default(globalThis.document) && (this._canvas = globalThis.document.createElement("canvas"))), isNil_default(this._context) && isValid_default(this._canvas)) {
      const context = this._canvas.getContext("2d");
      isValid_default(context) && (context.save(), context.font = getContextFont(this.textSpec), this._contextSaved = true, this._context = context);
    }
    return !isNil_default(this._context) || (this._notSupportCanvas = true, false);
  }
  _initSpec() {
    var _a, _b, _c;
    const { defaultFontParams = {} } = this._option, { fontStyle = defaultFontParams.fontStyle, fontVariant = defaultFontParams.fontVariant, fontWeight = null !== (_a = defaultFontParams.fontWeight) && void 0 !== _a ? _a : "normal", fontSize = null !== (_b = defaultFontParams.fontSize) && void 0 !== _b ? _b : 12, fontFamily = null !== (_c = defaultFontParams.fontFamily) && void 0 !== _c ? _c : "sans-serif", align, textAlign = null != align ? align : "center", baseline, textBaseline = null != baseline ? baseline : "middle", ellipsis, limit } = this._userSpec;
    let { lineHeight = fontSize } = this._userSpec;
    if (isString_default(lineHeight) && "%" === lineHeight[lineHeight.length - 1]) {
      const scale2 = Number.parseFloat(lineHeight.substring(0, lineHeight.length - 1)) / 100;
      lineHeight = fontSize * scale2;
    }
    return {
      fontStyle,
      fontVariant,
      fontFamily,
      fontSize,
      fontWeight,
      textAlign,
      textBaseline,
      ellipsis,
      limit,
      lineHeight
    };
  }
  measure(text, method) {
    switch (method) {
      case "vrender":
      case "canopus":
        return this.fullMeasure(text);
      case "canvas":
        return this.measureWithNaiveCanvas(text);
      case "simple":
        return this.quickMeasureWithoutCanvas(text);
      default:
        return this.quickMeasure(text);
    }
  }
  fullMeasure(text) {
    if (isNil_default(text)) return {
      width: 0,
      height: 0
    };
    if (isNil_default(this._option.getTextBounds) || !this._notSupportVRender) return this.measureWithNaiveCanvas(text);
    const { fontFamily, fontSize, fontWeight, textAlign, textBaseline, ellipsis, limit, lineHeight } = this.textSpec;
    let size;
    try {
      const bounds = this._option.getTextBounds({
        text,
        fontFamily,
        fontSize,
        fontWeight,
        textAlign,
        textBaseline,
        ellipsis: !!ellipsis,
        maxLineWidth: limit || 1 / 0,
        lineHeight
      });
      size = {
        width: bounds.width(),
        height: bounds.height()
      };
    } catch (e3) {
      this._notSupportVRender = true, size = this.measureWithNaiveCanvas(text);
    }
    return size;
  }
  measureWithNaiveCanvas(text) {
    return this._measureReduce(text, this._measureWithNaiveCanvas.bind(this));
  }
  _measureWithNaiveCanvas(text) {
    var _a;
    if (!this.initContext()) return this._quickMeasureWithoutCanvas(text);
    const metrics = this._context.measureText(text), { fontSize, lineHeight } = this.textSpec;
    return {
      width: metrics.width,
      height: null !== (_a = lineHeight) && void 0 !== _a ? _a : fontSize,
      fontBoundingBoxAscent: metrics.fontBoundingBoxAscent,
      fontBoundingBoxDescent: metrics.fontBoundingBoxDescent
    };
  }
  quickMeasure(text) {
    return this._measureReduce(text, this._quickMeasure.bind(this));
  }
  _quickMeasure(text) {
    const totalSize = {
      width: 0,
      height: 0
    };
    for (let i2 = 0; i2 < text.length; i2++) {
      const char = text[i2];
      let size = this._measureSpecialChar(char);
      isNil_default(size) && _TextMeasure.NUMBERS_CHAR_SET.includes(char) && (size = this._measureNumberChar()), isNil_default(size) && ["F", "W"].includes(eastAsianCharacterInfo(char)) && (size = this._measureFullSizeChar()), isNil_default(size) && (size = this._measureLetterChar()), totalSize.width += size.width, totalSize.height = Math.max(totalSize.height, size.height), !isNil_default(size.fontBoundingBoxAscent) && (totalSize.fontBoundingBoxAscent = size.fontBoundingBoxAscent), !isNil_default(size.fontBoundingBoxDescent) && (totalSize.fontBoundingBoxDescent = size.fontBoundingBoxDescent);
    }
    return totalSize;
  }
  quickMeasureWithoutCanvas(text) {
    return this._measureReduce(text, this._quickMeasureWithoutCanvas.bind(this));
  }
  _quickMeasureWithoutCanvas(text) {
    var _a;
    const totalSize = {
      width: 0,
      height: 0
    }, { fontSize, lineHeight } = this.textSpec;
    for (let i2 = 0; i2 < text.length; i2++) {
      const char = text[i2], size = ["F", "W"].includes(eastAsianCharacterInfo(char)) ? 1 : 0.53;
      totalSize.width += size * fontSize;
    }
    return totalSize.height = null !== (_a = lineHeight) && void 0 !== _a ? _a : fontSize, totalSize;
  }
  _measureReduce(text, processor) {
    var _a;
    const { fontSize, lineHeight } = this.textSpec, defaultResult = {
      width: 0,
      height: 0
    };
    if (isNil_default(text)) return defaultResult;
    if (isArray_default(text)) {
      const textArr = text.filter(isValid_default).map((s2) => s2.toString());
      return 0 === textArr.length ? defaultResult : 1 === textArr.length ? processor(textArr[0]) : {
        width: textArr.reduce((maxWidth, cur) => Math.max(maxWidth, processor(cur).width), 0),
        height: textArr.length * ((null !== (_a = lineHeight) && void 0 !== _a ? _a : fontSize) + 1) + 1
      };
    }
    return processor(text.toString());
  }
  _measureNumberChar() {
    if (isNil_default(this._numberCharSize)) {
      const numberBounds = this._standardMethod(_TextMeasure.NUMBERS_CHAR_SET);
      this._numberCharSize = {
        width: numberBounds.width / _TextMeasure.NUMBERS_CHAR_SET.length,
        height: numberBounds.height,
        fontBoundingBoxAscent: numberBounds.fontBoundingBoxAscent,
        fontBoundingBoxDescent: numberBounds.fontBoundingBoxDescent
      };
    }
    return this._numberCharSize;
  }
  _measureFullSizeChar() {
    return isNil_default(this._fullCharSize) && (this._fullCharSize = this._standardMethod(_TextMeasure.FULL_SIZE_CHAR)), this._fullCharSize;
  }
  _measureLetterChar() {
    if (isNil_default(this._letterCharSize)) {
      const alphabetBounds = this._standardMethod(_TextMeasure.ALPHABET_CHAR_SET);
      this._letterCharSize = {
        width: alphabetBounds.width / _TextMeasure.ALPHABET_CHAR_SET.length,
        height: alphabetBounds.height,
        fontBoundingBoxAscent: alphabetBounds.fontBoundingBoxAscent,
        fontBoundingBoxDescent: alphabetBounds.fontBoundingBoxDescent
      };
    }
    return this._letterCharSize;
  }
  _measureSpecialChar(char) {
    return isValid_default(this._specialCharSizeMap[char]) ? this._specialCharSizeMap[char] : this.specialCharSet.includes(char) ? (this._specialCharSizeMap[char] = this._standardMethod(char), this._specialCharSizeMap[char]) : null;
  }
  release() {
    isValid_default(this._canvas) && (this._canvas = null), isValid_default(this._context) && (this._contextSaved && (this._context.restore(), this._contextSaved = false), this._context = null);
  }
};
TextMeasure.ALPHABET_CHAR_SET = "abcdefghijklmnopqrstuvwxyz", TextMeasure.NUMBERS_CHAR_SET = "0123456789", TextMeasure.FULL_SIZE_CHAR = "字";

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/data-structure/bounds.js
function transformBoundsWithMatrix(out, bounds, matrix) {
  const { x1: x13, y1: y13, x2: x22, y2: y22 } = bounds;
  return matrix.onlyTranslate() ? (out !== bounds && out.setValue(bounds.x1, bounds.y1, bounds.x2, bounds.y2), out.translate(matrix.e, matrix.f), bounds) : (out.clear(), out.add(matrix.a * x13 + matrix.c * y13 + matrix.e, matrix.b * x13 + matrix.d * y13 + matrix.f), out.add(matrix.a * x22 + matrix.c * y13 + matrix.e, matrix.b * x22 + matrix.d * y13 + matrix.f), out.add(matrix.a * x22 + matrix.c * y22 + matrix.e, matrix.b * x22 + matrix.d * y22 + matrix.f), out.add(matrix.a * x13 + matrix.c * y22 + matrix.e, matrix.b * x13 + matrix.d * y22 + matrix.f), bounds);
}
var Bounds = class _Bounds {
  constructor(bounds) {
    bounds ? this.setValue(bounds.x1, bounds.y1, bounds.x2, bounds.y2) : this.clear();
  }
  clone() {
    return new _Bounds(this);
  }
  clear() {
    return this.x1 = +Number.MAX_VALUE, this.y1 = +Number.MAX_VALUE, this.x2 = -Number.MAX_VALUE, this.y2 = -Number.MAX_VALUE, this;
  }
  empty() {
    return this.x1 === +Number.MAX_VALUE && this.y1 === +Number.MAX_VALUE && this.x2 === -Number.MAX_VALUE && this.y2 === -Number.MAX_VALUE;
  }
  equals(b2) {
    return this.x1 === b2.x1 && this.y1 === b2.y1 && this.x2 === b2.x2 && this.y2 === b2.y2;
  }
  setValue(x13 = 0, y13 = 0, x22 = 0, y22 = 0) {
    return this.x1 = x13, this.y1 = y13, this.x2 = x22, this.y2 = y22, this;
  }
  set(x13 = 0, y13 = 0, x22 = 0, y22 = 0) {
    return x22 < x13 ? (this.x2 = x13, this.x1 = x22) : (this.x1 = x13, this.x2 = x22), y22 < y13 ? (this.y2 = y13, this.y1 = y22) : (this.y1 = y13, this.y2 = y22), this;
  }
  add(x3 = 0, y3 = 0) {
    return x3 < this.x1 && (this.x1 = x3), y3 < this.y1 && (this.y1 = y3), x3 > this.x2 && (this.x2 = x3), y3 > this.y2 && (this.y2 = y3), this;
  }
  expand(d2 = 0) {
    return isArray_default(d2) ? (this.y1 -= d2[0], this.x2 += d2[1], this.y2 += d2[2], this.x1 -= d2[3]) : (this.x1 -= d2, this.y1 -= d2, this.x2 += d2, this.y2 += d2), this;
  }
  round() {
    return this.x1 = Math.floor(this.x1), this.y1 = Math.floor(this.y1), this.x2 = Math.ceil(this.x2), this.y2 = Math.ceil(this.y2), this;
  }
  translate(dx = 0, dy = 0) {
    return this.x1 += dx, this.x2 += dx, this.y1 += dy, this.y2 += dy, this;
  }
  rotate(angle = 0, x3 = 0, y3 = 0) {
    const p2 = this.rotatedPoints(angle, x3, y3);
    return this.clear().add(p2[0], p2[1]).add(p2[2], p2[3]).add(p2[4], p2[5]).add(p2[6], p2[7]);
  }
  scale(sx = 0, sy = 0, x3 = 0, y3 = 0) {
    const p2 = this.scalePoints(sx, sy, x3, y3);
    return this.clear().add(p2[0], p2[1]).add(p2[2], p2[3]);
  }
  union(b2) {
    return b2.x1 < this.x1 && (this.x1 = b2.x1), b2.y1 < this.y1 && (this.y1 = b2.y1), b2.x2 > this.x2 && (this.x2 = b2.x2), b2.y2 > this.y2 && (this.y2 = b2.y2), this;
  }
  intersect(b2) {
    return b2.x1 > this.x1 && (this.x1 = b2.x1), b2.y1 > this.y1 && (this.y1 = b2.y1), b2.x2 < this.x2 && (this.x2 = b2.x2), b2.y2 < this.y2 && (this.y2 = b2.y2), this;
  }
  encloses(b2) {
    return b2 && this.x1 <= b2.x1 && this.x2 >= b2.x2 && this.y1 <= b2.y1 && this.y2 >= b2.y2;
  }
  alignsWith(b2) {
    return b2 && (this.x1 === b2.x1 || this.x2 === b2.x2 || this.y1 === b2.y1 || this.y2 === b2.y2);
  }
  intersects(b2) {
    return b2 && !(this.x2 < b2.x1 || this.x1 > b2.x2 || this.y2 < b2.y1 || this.y1 > b2.y2);
  }
  contains(x3 = 0, y3 = 0) {
    return !(x3 < this.x1 || x3 > this.x2 || y3 < this.y1 || y3 > this.y2);
  }
  containsPoint(p2) {
    return !(p2.x < this.x1 || p2.x > this.x2 || p2.y < this.y1 || p2.y > this.y2);
  }
  width() {
    return this.empty() ? 0 : this.x2 - this.x1;
  }
  height() {
    return this.empty() ? 0 : this.y2 - this.y1;
  }
  scaleX(s2 = 0) {
    return this.x1 *= s2, this.x2 *= s2, this;
  }
  scaleY(s2 = 0) {
    return this.y1 *= s2, this.y2 *= s2, this;
  }
  transformWithMatrix(matrix) {
    return transformBoundsWithMatrix(this, this, matrix), this;
  }
  copy(b2) {
    return this.x1 = b2.x1, this.y1 = b2.y1, this.x2 = b2.x2, this.y2 = b2.y2, this;
  }
  rotatedPoints(angle, x3, y3) {
    const { x1: x13, y1: y13, x2: x22, y2: y22 } = this, cos3 = Math.cos(angle), sin3 = Math.sin(angle), cx = x3 - x3 * cos3 + y3 * sin3, cy = y3 - x3 * sin3 - y3 * cos3;
    return [cos3 * x13 - sin3 * y13 + cx, sin3 * x13 + cos3 * y13 + cy, cos3 * x13 - sin3 * y22 + cx, sin3 * x13 + cos3 * y22 + cy, cos3 * x22 - sin3 * y13 + cx, sin3 * x22 + cos3 * y13 + cy, cos3 * x22 - sin3 * y22 + cx, sin3 * x22 + cos3 * y22 + cy];
  }
  scalePoints(sx, sy, x3, y3) {
    const { x1: x13, y1: y13, x2: x22, y2: y22 } = this;
    return [sx * x13 + (1 - sx) * x3, sy * y13 + (1 - sy) * y3, sx * x22 + (1 - sx) * x3, sy * y22 + (1 - sy) * y3];
  }
};
var AABBBounds = class extends Bounds {
};
var OBBBounds = class _OBBBounds extends Bounds {
  constructor(bounds, angle = 0) {
    var _a;
    super(bounds), bounds && (this.angle = null !== (_a = bounds.angle) && void 0 !== _a ? _a : angle);
  }
  intersects(b2) {
    return isRotateAABBIntersect(this, b2);
  }
  setValue(x13 = 0, y13 = 0, x22 = 0, y22 = 0, angle = 0) {
    return super.setValue(x13, y13, x22, y22), this.angle = angle, this;
  }
  clone() {
    return new _OBBBounds(this);
  }
  getRotatedCorners() {
    const originPoint = {
      x: (this.x1 + this.x2) / 2,
      y: (this.y1 + this.y2) / 2
    };
    return [rotatePoint({
      x: this.x1,
      y: this.y1
    }, this.angle, originPoint), rotatePoint({
      x: this.x2,
      y: this.y1
    }, this.angle, originPoint), rotatePoint({
      x: this.x1,
      y: this.y2
    }, this.angle, originPoint), rotatePoint({
      x: this.x2,
      y: this.y2
    }, this.angle, originPoint)];
  }
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/data-structure/matrix.js
var Matrix = class _Matrix {
  constructor(a2 = 1, b2 = 0, c2 = 0, d2 = 1, e3 = 0, f2 = 0) {
    this.a = a2, this.b = b2, this.c = c2, this.d = d2, this.e = e3, this.f = f2;
  }
  equalToMatrix(m2) {
    return !(this.e !== m2.e || this.f !== m2.f || this.a !== m2.a || this.d !== m2.d || this.b !== m2.b || this.c !== m2.c);
  }
  equalTo(a2, b2, c2, d2, e3, f2) {
    return !(this.e !== e3 || this.f !== f2 || this.a !== a2 || this.d !== d2 || this.b !== b2 || this.c !== c2);
  }
  setValue(a2, b2, c2, d2, e3, f2) {
    return this.a = a2, this.b = b2, this.c = c2, this.d = d2, this.e = e3, this.f = f2, this;
  }
  reset() {
    return this.a = 1, this.b = 0, this.c = 0, this.d = 1, this.e = 0, this.f = 0, this;
  }
  getInverse() {
    const a2 = this.a, b2 = this.b, c2 = this.c, d2 = this.d, e3 = this.e, f2 = this.f, m2 = new _Matrix(), dt = a2 * d2 - b2 * c2;
    return m2.a = d2 / dt, m2.b = -b2 / dt, m2.c = -c2 / dt, m2.d = a2 / dt, m2.e = (c2 * f2 - d2 * e3) / dt, m2.f = -(a2 * f2 - b2 * e3) / dt, m2;
  }
  rotate(rad) {
    const c2 = Math.cos(rad), s2 = Math.sin(rad), m11 = this.a * c2 + this.c * s2, m12 = this.b * c2 + this.d * s2, m21 = this.a * -s2 + this.c * c2, m22 = this.b * -s2 + this.d * c2;
    return this.a = m11, this.b = m12, this.c = m21, this.d = m22, this;
  }
  rotateByCenter(rad, cx, cy) {
    const cos3 = Math.cos(rad), sin3 = Math.sin(rad), rotateM13 = (1 - cos3) * cx + sin3 * cy, rotateM23 = (1 - cos3) * cy - sin3 * cx, m11 = cos3 * this.a - sin3 * this.b, m21 = sin3 * this.a + cos3 * this.b, m12 = cos3 * this.c - sin3 * this.d, m22 = sin3 * this.c + cos3 * this.d, m13 = cos3 * this.e - sin3 * this.f + rotateM13, m23 = sin3 * this.e + cos3 * this.f + rotateM23;
    return this.a = m11, this.b = m21, this.c = m12, this.d = m22, this.e = m13, this.f = m23, this;
  }
  scale(sx, sy) {
    return this.a *= sx, this.b *= sx, this.c *= sy, this.d *= sy, this;
  }
  setScale(sx, sy) {
    return this.b = this.b / this.a * sx, this.c = this.c / this.d * sy, this.a = sx, this.d = sy, this;
  }
  transform(a2, b2, c2, d2, e3, f2) {
    return this.multiply(a2, b2, c2, d2, e3, f2), this;
  }
  translate(x3, y3) {
    return this.e += this.a * x3 + this.c * y3, this.f += this.b * x3 + this.d * y3, this;
  }
  transpose() {
    const { a: a2, b: b2, c: c2, d: d2, e: e3, f: f2 } = this;
    return this.a = b2, this.b = a2, this.c = d2, this.d = c2, this.e = f2, this.f = e3, this;
  }
  multiply(a2, b2, c2, d2, e24, f2) {
    const a1 = this.a, b1 = this.b, c1 = this.c, d1 = this.d, m11 = a1 * a2 + c1 * b2, m12 = b1 * a2 + d1 * b2, m21 = a1 * c2 + c1 * d2, m22 = b1 * c2 + d1 * d2, dx = a1 * e24 + c1 * f2 + this.e, dy = b1 * e24 + d1 * f2 + this.f;
    return this.a = m11, this.b = m12, this.c = m21, this.d = m22, this.e = dx, this.f = dy, this;
  }
  interpolate(m2, t2) {
    const m3 = new _Matrix();
    return m3.a = this.a + (m2.a - this.a) * t2, m3.b = this.b + (m2.b - this.b) * t2, m3.c = this.c + (m2.c - this.c) * t2, m3.d = this.d + (m2.d - this.d) * t2, m3.e = this.e + (m2.e - this.e) * t2, m3.f = this.f + (m2.f - this.f) * t2, m3;
  }
  transformPoint(source, target) {
    const { a: a2, b: b2, c: c2, d: d2, e: e3, f: f2 } = this, dt = a2 * d2 - b2 * c2, nextA = d2 / dt, nextB = -b2 / dt, nextC = -c2 / dt, nextD = a2 / dt, nextE = (c2 * f2 - d2 * e3) / dt, nextF = -(a2 * f2 - b2 * e3) / dt, { x: x3, y: y3 } = source;
    target.x = x3 * nextA + y3 * nextC + nextE, target.y = x3 * nextB + y3 * nextD + nextF;
  }
  onlyTranslate(scale2 = 1) {
    return this.a === scale2 && 0 === this.b && 0 === this.c && this.d === scale2;
  }
  clone() {
    return new _Matrix(this.a, this.b, this.c, this.d, this.e, this.f);
  }
  toTransformAttrs() {
    const a2 = this.a, b2 = this.b, c2 = this.c, d2 = this.d, delta = a2 * d2 - b2 * c2, result = {
      x: this.e,
      y: this.f,
      rotateDeg: 0,
      scaleX: 0,
      scaleY: 0,
      skewX: 0,
      skewY: 0
    };
    if (0 !== a2 || 0 !== b2) {
      const r2 = Math.sqrt(a2 * a2 + b2 * b2);
      result.rotateDeg = b2 > 0 ? Math.acos(a2 / r2) : -Math.acos(a2 / r2), result.scaleX = r2, result.scaleY = delta / r2, result.skewX = (a2 * c2 + b2 * d2) / delta, result.skewY = 0;
    } else if (0 !== c2 || 0 !== d2) {
      const s2 = Math.sqrt(c2 * c2 + d2 * d2);
      result.rotateDeg = Math.PI / 2 - (d2 > 0 ? Math.acos(-c2 / s2) : -Math.acos(c2 / s2)), result.scaleX = delta / s2, result.scaleY = s2, result.skewX = 0, result.skewY = (a2 * c2 + b2 * d2) / delta;
    }
    return result.rotateDeg = radianToDegree(result.rotateDeg), result;
  }
};
function normalTransform(out, origin, x3, y3, scaleX, scaleY, angle, rotateCenter) {
  const oa = origin.a, ob = origin.b, oc = origin.c, od = origin.d, oe = origin.e, of = origin.f, cosTheta = cos(angle), sinTheta = sin(angle);
  let rotateCenterX, rotateCenterY;
  rotateCenter ? (rotateCenterX = rotateCenter[0], rotateCenterY = rotateCenter[1]) : (rotateCenterX = x3, rotateCenterY = y3);
  const offsetX = rotateCenterX - x3, offsetY = rotateCenterY - y3, a1 = oa * cosTheta + oc * sinTheta, b1 = ob * cosTheta + od * sinTheta, c1 = oc * cosTheta - oa * sinTheta, d1 = od * cosTheta - ob * sinTheta;
  out.a = scaleX * a1, out.b = scaleX * b1, out.c = scaleY * c1, out.d = scaleY * d1, out.e = oe + oa * rotateCenterX + oc * rotateCenterY - a1 * offsetX - c1 * offsetY, out.f = of + ob * rotateCenterX + od * rotateCenterY - b1 * offsetX - d1 * offsetY;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/color/hslToRgb.js
function hslToRgb(h2, s2, l2) {
  s2 /= 100, l2 /= 100;
  const c2 = (1 - Math.abs(2 * l2 - 1)) * s2, x3 = c2 * (1 - Math.abs(h2 / 60 % 2 - 1)), m2 = l2 - c2 / 2;
  let r2 = 0, g3 = 0, b2 = 0;
  return 0 <= h2 && h2 < 60 ? (r2 = c2, g3 = x3, b2 = 0) : 60 <= h2 && h2 < 120 ? (r2 = x3, g3 = c2, b2 = 0) : 120 <= h2 && h2 < 180 ? (r2 = 0, g3 = c2, b2 = x3) : 180 <= h2 && h2 < 240 ? (r2 = 0, g3 = x3, b2 = c2) : 240 <= h2 && h2 < 300 ? (r2 = x3, g3 = 0, b2 = c2) : 300 <= h2 && h2 < 360 && (r2 = c2, g3 = 0, b2 = x3), r2 = Math.round(255 * (r2 + m2)), g3 = Math.round(255 * (g3 + m2)), b2 = Math.round(255 * (b2 + m2)), {
    r: r2,
    g: g3,
    b: b2
  };
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/color/rgbToHsl.js
function rgbToHsl(r2, g3, b2) {
  r2 /= 255, g3 /= 255, b2 /= 255;
  const cMin = Math.min(r2, g3, b2), cMax = Math.max(r2, g3, b2), delta = cMax - cMin;
  let h2 = 0, s2 = 0, l2 = 0;
  return h2 = 0 === delta ? 0 : cMax === r2 ? (g3 - b2) / delta % 6 : cMax === g3 ? (b2 - r2) / delta + 2 : (r2 - g3) / delta + 4, h2 = Math.round(60 * h2), h2 < 0 && (h2 += 360), l2 = (cMax + cMin) / 2, s2 = 0 === delta ? 0 : delta / (1 - Math.abs(2 * l2 - 1)), s2 = +(100 * s2).toFixed(1), l2 = +(100 * l2).toFixed(1), {
    h: h2,
    s: s2,
    l: l2
  };
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/color/Color.js
var REG_HEX = /^#([0-9a-f]{3,8})$/;
var DEFAULT_COLORS_OPACITY = {
  transparent: 4294967040
};
var DEFAULT_COLORS = {
  aliceblue: 15792383,
  antiquewhite: 16444375,
  aqua: 65535,
  aquamarine: 8388564,
  azure: 15794175,
  beige: 16119260,
  bisque: 16770244,
  black: 0,
  blanchedalmond: 16772045,
  blue: 255,
  blueviolet: 9055202,
  brown: 10824234,
  burlywood: 14596231,
  cadetblue: 6266528,
  chartreuse: 8388352,
  chocolate: 13789470,
  coral: 16744272,
  cornflowerblue: 6591981,
  cornsilk: 16775388,
  crimson: 14423100,
  cyan: 65535,
  darkblue: 139,
  darkcyan: 35723,
  darkgoldenrod: 12092939,
  darkgray: 11119017,
  darkgreen: 25600,
  darkgrey: 11119017,
  darkkhaki: 12433259,
  darkmagenta: 9109643,
  darkolivegreen: 5597999,
  darkorange: 16747520,
  darkorchid: 10040012,
  darkred: 9109504,
  darksalmon: 15308410,
  darkseagreen: 9419919,
  darkslateblue: 4734347,
  darkslategray: 3100495,
  darkslategrey: 3100495,
  darkturquoise: 52945,
  darkviolet: 9699539,
  deeppink: 16716947,
  deepskyblue: 49151,
  dimgray: 6908265,
  dimgrey: 6908265,
  dodgerblue: 2003199,
  firebrick: 11674146,
  floralwhite: 16775920,
  forestgreen: 2263842,
  fuchsia: 16711935,
  gainsboro: 14474460,
  ghostwhite: 16316671,
  gold: 16766720,
  goldenrod: 14329120,
  gray: 8421504,
  green: 32768,
  greenyellow: 11403055,
  grey: 8421504,
  honeydew: 15794160,
  hotpink: 16738740,
  indianred: 13458524,
  indigo: 4915330,
  ivory: 16777200,
  khaki: 15787660,
  lavender: 15132410,
  lavenderblush: 16773365,
  lawngreen: 8190976,
  lemonchiffon: 16775885,
  lightblue: 11393254,
  lightcoral: 15761536,
  lightcyan: 14745599,
  lightgoldenrodyellow: 16448210,
  lightgray: 13882323,
  lightgreen: 9498256,
  lightgrey: 13882323,
  lightpink: 16758465,
  lightsalmon: 16752762,
  lightseagreen: 2142890,
  lightskyblue: 8900346,
  lightslategray: 7833753,
  lightslategrey: 7833753,
  lightsteelblue: 11584734,
  lightyellow: 16777184,
  lime: 65280,
  limegreen: 3329330,
  linen: 16445670,
  magenta: 16711935,
  maroon: 8388608,
  mediumaquamarine: 6737322,
  mediumblue: 205,
  mediumorchid: 12211667,
  mediumpurple: 9662683,
  mediumseagreen: 3978097,
  mediumslateblue: 8087790,
  mediumspringgreen: 64154,
  mediumturquoise: 4772300,
  mediumvioletred: 13047173,
  midnightblue: 1644912,
  mintcream: 16121850,
  mistyrose: 16770273,
  moccasin: 16770229,
  navajowhite: 16768685,
  navy: 128,
  oldlace: 16643558,
  olive: 8421376,
  olivedrab: 7048739,
  orange: 16753920,
  orangered: 16729344,
  orchid: 14315734,
  palegoldenrod: 15657130,
  palegreen: 10025880,
  paleturquoise: 11529966,
  palevioletred: 14381203,
  papayawhip: 16773077,
  peachpuff: 16767673,
  peru: 13468991,
  pink: 16761035,
  plum: 14524637,
  powderblue: 11591910,
  purple: 8388736,
  rebeccapurple: 6697881,
  red: 16711680,
  rosybrown: 12357519,
  royalblue: 4286945,
  saddlebrown: 9127187,
  salmon: 16416882,
  sandybrown: 16032864,
  seagreen: 3050327,
  seashell: 16774638,
  sienna: 10506797,
  silver: 12632256,
  skyblue: 8900331,
  slateblue: 6970061,
  slategray: 7372944,
  slategrey: 7372944,
  snow: 16775930,
  springgreen: 65407,
  steelblue: 4620980,
  tan: 13808780,
  teal: 32896,
  thistle: 14204888,
  tomato: 16737095,
  turquoise: 4251856,
  violet: 15631086,
  wheat: 16113331,
  white: 16777215,
  whitesmoke: 16119285,
  yellow: 16776960,
  yellowgreen: 10145074
};
function hex(value) {
  return ((value = Math.max(0, Math.min(255, Math.round(value) || 0))) < 16 ? "0" : "") + value.toString(16);
}
function rgb(value) {
  return isNumber_default(value) ? new RGB(value >> 16, value >> 8 & 255, 255 & value, 1) : isArray_default(value) ? new RGB(value[0], value[1], value[2]) : new RGB(255, 255, 255);
}
function rgba(value) {
  return isNumber_default(value) ? new RGB(value >>> 24, value >>> 16 & 255, value >>> 8 & 255, 255 & value) : isArray_default(value) ? new RGB(value[0], value[1], value[2], value[3]) : new RGB(255, 255, 255, 1);
}
function SRGBToLinear(c2) {
  return c2 < 0.04045 ? 0.0773993808 * c2 : Math.pow(0.9478672986 * c2 + 0.0521327014, 2.4);
}
function LinearToSRGB(c2) {
  return c2 < 31308e-7 ? 12.92 * c2 : 1.055 * Math.pow(c2, 0.41666) - 0.055;
}
var setHex = (formatValue, forceHex) => {
  const isHex = REG_HEX.exec(formatValue);
  if (forceHex || isHex) {
    const hex2 = parseInt(isHex[1], 16), hexLength = isHex[1].length;
    return 3 === hexLength ? new RGB((hex2 >> 8 & 15) + ((hex2 >> 8 & 15) << 4), (hex2 >> 4 & 15) + ((hex2 >> 4 & 15) << 4), (15 & hex2) + ((15 & hex2) << 4), 1) : 6 === hexLength ? rgb(hex2) : 8 === hexLength ? new RGB(hex2 >> 24 & 255, hex2 >> 16 & 255, hex2 >> 8 & 255, (255 & hex2) / 255) : null;
  }
};
var Color = class _Color {
  static Brighter(source, b2 = 1) {
    return 1 === b2 ? source : new _Color(source).brighter(b2).toRGBA();
  }
  static SetOpacity(source, o2 = 1) {
    return 1 === o2 ? source : new _Color(source).setOpacity(o2).toRGBA();
  }
  static getColorBrightness(source, model = "hsl") {
    const color = source instanceof _Color ? source : new _Color(source);
    switch (model) {
      case "hsv":
      default:
        return color.getHSVBrightness();
      case "hsl":
        return color.getHSLBrightness();
      case "lum":
        return color.getLuminance();
      case "lum2":
        return color.getLuminance2();
      case "lum3":
        return color.getLuminance3();
      case "wcag":
        return color.getLuminanceWCAG();
    }
  }
  static parseColorString(value) {
    if (isValid_default(DEFAULT_COLORS_OPACITY[value])) return rgba(DEFAULT_COLORS_OPACITY[value]);
    if (isValid_default(DEFAULT_COLORS[value])) return rgb(DEFAULT_COLORS[value]);
    const formatValue = `${value}`.trim().toLowerCase(), hexRes = setHex(formatValue);
    if (void 0 !== hexRes) return hexRes;
    if (/^(rgb|RGB|rgba|RGBA)/.test(formatValue)) {
      const aColor = formatValue.replace(/(?:\(|\)|rgba|RGBA|rgb|RGB)*/g, "").split(",");
      return new RGB(parseInt(aColor[0], 10), parseInt(aColor[1], 10), parseInt(aColor[2], 10), parseFloat(aColor[3]));
    }
    if (/^(hsl|HSL|hsla|HSLA)/.test(formatValue)) {
      const aColor = formatValue.replace(/(?:\(|\)|hsla|HSLA|hsl|HSL)*/g, "").split(","), rgb2 = hslToRgb(parseInt(aColor[0], 10), parseInt(aColor[1], 10), parseInt(aColor[2], 10));
      return new RGB(rgb2.r, rgb2.g, rgb2.b, parseFloat(aColor[3]));
    }
  }
  constructor(value) {
    const color = _Color.parseColorString(value);
    color ? this.color = color : (console.warn(`Warn: 传入${value}无法解析为Color`), this.color = new RGB(255, 255, 255));
  }
  toRGBA() {
    return this.color.formatRgb();
  }
  toString() {
    return this.color.formatRgb();
  }
  toHex() {
    return this.color.formatHex();
  }
  toHsl() {
    return this.color.formatHsl();
  }
  brighter(k2) {
    const { r: r2, g: g3, b: b2 } = this.color;
    return this.color.r = Math.max(0, Math.min(255, Math.floor(r2 * k2))), this.color.g = Math.max(0, Math.min(255, Math.floor(g3 * k2))), this.color.b = Math.max(0, Math.min(255, Math.floor(b2 * k2))), this;
  }
  add(color) {
    const { r: r2, g: g3, b: b2 } = this.color;
    return this.color.r += Math.min(255, r2 + color.color.r), this.color.g += Math.min(255, g3 + color.color.g), this.color.b += Math.min(255, b2 + color.color.b), this;
  }
  sub(color) {
    return this.color.r = Math.max(0, this.color.r - color.color.r), this.color.g = Math.max(0, this.color.g - color.color.g), this.color.b = Math.max(0, this.color.b - color.color.b), this;
  }
  multiply(color) {
    const { r: r2, g: g3, b: b2 } = this.color;
    return this.color.r = Math.max(0, Math.min(255, Math.floor(r2 * color.color.r))), this.color.g = Math.max(0, Math.min(255, Math.floor(g3 * color.color.g))), this.color.b = Math.max(0, Math.min(255, Math.floor(b2 * color.color.b))), this;
  }
  getHSVBrightness() {
    return Math.max(this.color.r, this.color.g, this.color.b) / 255;
  }
  getHSLBrightness() {
    return 0.5 * (Math.max(this.color.r, this.color.g, this.color.b) / 255 + Math.min(this.color.r, this.color.g, this.color.b) / 255);
  }
  setHsl(h2, s2, l2) {
    const opacity = this.color.opacity, hsl = rgbToHsl(this.color.r, this.color.g, this.color.b), rgb2 = hslToRgb(isNil_default(h2) ? hsl.h : clamp_default(h2, 0, 360), isNil_default(s2) ? hsl.s : s2 >= 0 && s2 <= 1 ? 100 * s2 : s2, isNil_default(l2) ? hsl.l : l2 <= 1 && l2 >= 0 ? 100 * l2 : l2);
    return this.color = new RGB(rgb2.r, rgb2.g, rgb2.b, opacity), this;
  }
  setRGB(r2, g3, b2) {
    return !isNil_default(r2) && (this.color.r = r2), !isNil_default(g3) && (this.color.g = g3), !isNil_default(b2) && (this.color.b = b2), this;
  }
  setHex(value) {
    const formatValue = `${value}`.trim().toLowerCase(), res = setHex(formatValue, true);
    return null != res ? res : this;
  }
  setColorName(name) {
    const hex2 = DEFAULT_COLORS[name.toLowerCase()];
    return void 0 !== hex2 ? this.setHex(hex2) : console.warn("THREE.Color: Unknown color " + name), this;
  }
  setScalar(scalar) {
    return this.color.r = scalar, this.color.g = scalar, this.color.b = scalar, this;
  }
  setOpacity(o2 = 1) {
    return this.color.opacity = o2, this;
  }
  getLuminance() {
    return (0.2126 * this.color.r + 0.7152 * this.color.g + 0.0722 * this.color.b) / 255;
  }
  getLuminance2() {
    return (0.2627 * this.color.r + 0.678 * this.color.g + 0.0593 * this.color.b) / 255;
  }
  getLuminance3() {
    return (0.299 * this.color.r + 0.587 * this.color.g + 0.114 * this.color.b) / 255;
  }
  getLuminanceWCAG() {
    const RsRGB = this.color.r / 255, GsRGB = this.color.g / 255, BsRGB = this.color.b / 255;
    let R2, G2, B2;
    R2 = RsRGB <= 0.03928 ? RsRGB / 12.92 : Math.pow((RsRGB + 0.055) / 1.055, 2.4), G2 = GsRGB <= 0.03928 ? GsRGB / 12.92 : Math.pow((GsRGB + 0.055) / 1.055, 2.4), B2 = BsRGB <= 0.03928 ? BsRGB / 12.92 : Math.pow((BsRGB + 0.055) / 1.055, 2.4);
    return 0.2126 * R2 + 0.7152 * G2 + 0.0722 * B2;
  }
  clone() {
    return new _Color(this.color.toString());
  }
  copyGammaToLinear(color, gammaFactor = 2) {
    return this.color.r = Math.pow(color.color.r, gammaFactor), this.color.g = Math.pow(color.color.g, gammaFactor), this.color.b = Math.pow(color.color.b, gammaFactor), this;
  }
  copyLinearToGamma(color, gammaFactor = 2) {
    const safeInverse = gammaFactor > 0 ? 1 / gammaFactor : 1;
    return this.color.r = Math.pow(color.color.r, safeInverse), this.color.g = Math.pow(color.color.g, safeInverse), this.color.b = Math.pow(color.color.b, safeInverse), this;
  }
  convertGammaToLinear(gammaFactor) {
    return this.copyGammaToLinear(this, gammaFactor), this;
  }
  convertLinearToGamma(gammaFactor) {
    return this.copyLinearToGamma(this, gammaFactor), this;
  }
  copySRGBToLinear(color) {
    return this.color.r = SRGBToLinear(color.color.r), this.color.g = SRGBToLinear(color.color.g), this.color.b = SRGBToLinear(color.color.b), this;
  }
  copyLinearToSRGB(color) {
    return this.color.r = LinearToSRGB(color.color.r), this.color.g = LinearToSRGB(color.color.g), this.color.b = LinearToSRGB(color.color.b), this;
  }
  convertSRGBToLinear() {
    return this.copySRGBToLinear(this), this;
  }
  convertLinearToSRGB() {
    return this.copyLinearToSRGB(this), this;
  }
};
var RGB = class {
  constructor(r2, g3, b2, opacity) {
    this.r = isNaN(+r2) ? 255 : Math.max(0, Math.min(255, +r2)), this.g = isNaN(+g3) ? 255 : Math.max(0, Math.min(255, +g3)), this.b = isNaN(+b2) ? 255 : Math.max(0, Math.min(255, +b2)), isValid_default(opacity) ? this.opacity = isNaN(+opacity) ? 1 : Math.max(0, Math.min(1, +opacity)) : this.opacity = 1;
  }
  formatHex() {
    return `#${hex(this.r) + hex(this.g) + hex(this.b) + (1 === this.opacity ? "" : hex(255 * this.opacity))}`;
  }
  formatRgb() {
    const opacity = this.opacity;
    return `${1 === opacity ? "rgb(" : "rgba("}${this.r},${this.g},${this.b}${1 === opacity ? ")" : `,${opacity})`}`;
  }
  formatHsl() {
    const opacity = this.opacity, { h: h2, s: s2, l: l2 } = rgbToHsl(this.r, this.g, this.b);
    return `${1 === opacity ? "hsl(" : "hsla("}${h2},${s2}%,${l2}%${1 === opacity ? ")" : `,${opacity})`}`;
  }
  toString() {
    return this.formatHex();
  }
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/color/index.js
var color_exports = {};
__export(color_exports, {
  Color: () => Color,
  DEFAULT_COLORS: () => DEFAULT_COLORS,
  RGB: () => RGB,
  hexToRgb: () => hexToRgb,
  hslToRgb: () => hslToRgb,
  interpolateRgb: () => interpolateRgb,
  rgbToHex: () => rgbToHex,
  rgbToHsl: () => rgbToHsl
});

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/color/hexToRgb.js
function hexToRgb(str) {
  let r2 = "", g3 = "", b2 = "";
  const strtIndex = "#" === str[0] ? 1 : 0;
  for (let i2 = strtIndex; i2 < str.length; i2++) "#" !== str[i2] && (i2 < strtIndex + 2 ? r2 += str[i2] : i2 < strtIndex + 4 ? g3 += str[i2] : i2 < strtIndex + 6 && (b2 += str[i2]));
  return [parseInt(r2, 16), parseInt(g3, 16), parseInt(b2, 16)];
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/color/rgbToHex.js
function rgbToHex(r2, g3, b2) {
  return Number((1 << 24) + (r2 << 16) + (g3 << 8) + b2).toString(16).slice(1);
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/color/interpolate.js
function interpolateRgb(colorA, colorB) {
  const redA = colorA.r, redB = colorB.r, greenA = colorA.g, greenB = colorB.g, blueA = colorA.b, blueB = colorB.b, opacityA = colorA.opacity, opacityB = colorB.opacity;
  return (t2) => {
    const r2 = Math.round(redA * (1 - t2) + redB * t2), g3 = Math.round(greenA * (1 - t2) + greenB * t2), b2 = Math.round(blueA * (1 - t2) + blueB * t2);
    return new RGB(r2, g3, b2, opacityA * (1 - t2) + opacityB * t2);
  };
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/padding.js
function normalizePadding(padding) {
  if (isValidNumber_default(padding)) return [padding, padding, padding, padding];
  if (isArray_default(padding)) {
    const length = padding.length;
    if (1 === length) {
      const paddingValue = padding[0];
      return [paddingValue, paddingValue, paddingValue, paddingValue];
    }
    if (2 === length) {
      const [vertical, horizontal] = padding;
      return [vertical, horizontal, vertical, horizontal];
    }
    if (3 === length) {
      const [top, horizontal, bottom] = padding;
      return [top, horizontal, bottom, horizontal];
    }
    if (4 === length) return padding;
  }
  if (isObject_default(padding)) {
    const { top = 0, right = 0, bottom = 0, left = 0 } = padding;
    return [top, right, bottom, left];
  }
  return [0, 0, 0, 0];
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/dom.js
function getContainerSize(el, defaultWidth = 0, defaultHeight = 0) {
  if (!el) return {
    width: defaultWidth,
    height: defaultHeight
  };
  let getComputedStyle;
  try {
    getComputedStyle = null === window || void 0 === window ? void 0 : window.getComputedStyle;
  } catch (e3) {
    getComputedStyle = () => ({});
  }
  const style = getComputedStyle(el);
  if (/^(\d*\.?\d+)(px)$/.exec(style.width)) {
    const computedWidth = parseFloat(style.width) - parseFloat(style.paddingLeft) - parseFloat(style.paddingRight) || el.clientWidth - 1, computedHeight = parseFloat(style.height) - parseFloat(style.paddingTop) - parseFloat(style.paddingBottom) || el.clientHeight - 1;
    return {
      width: computedWidth <= 0 ? defaultWidth : computedWidth,
      height: computedHeight <= 0 ? defaultHeight : computedHeight
    };
  }
  return {
    width: defaultWidth,
    height: defaultHeight
  };
}
function getElementAbsolutePosition(element) {
  const { x: x3, y: y3 } = element.getBoundingClientRect();
  return {
    x: x3,
    y: y3
  };
}
var styleStringToObject = (styleStr = "") => {
  const res = {};
  return styleStr.split(";").forEach((item) => {
    if (item) {
      const arr = item.split(":");
      if (2 === arr.length) {
        const key = arr[0].trim(), value = arr[1].trim();
        key && value && (res[key] = value);
      }
    }
  }), res;
};
var lowerCamelCaseToMiddle = (str) => str.replace(/([A-Z])/g, "-$1").toLowerCase();
function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (_2, letter) => letter.toUpperCase());
}
function isHTMLElement(obj) {
  try {
    return obj instanceof Element;
  } catch (_a) {
    const htmlElementKeys = ["children", "innerHTML", "classList", "setAttribute", "tagName", "getBoundingClientRect"], keys2 = Object.keys(obj);
    return htmlElementKeys.every((key) => keys2.includes(key));
  }
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/format/time.js
var TimeUtil = class _TimeUtil {
  static getInstance() {
    return _TimeUtil.instance || (_TimeUtil.instance = new _TimeUtil()), _TimeUtil.instance;
  }
  constructor() {
    this.locale_shortWeekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"], this.locale_periods = ["AM", "PM"], this.locale_weekdays = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], this.locale_shortMonths = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], this.numberRe = /^\s*\d+/, this.pads = {
      "-": "",
      _: " ",
      0: "0"
    }, this.requoteRe = /[\\^$*+?|[\]().{}]/g, this.locale_months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], this.formatShortWeekday = (d2) => this.locale_shortWeekdays[d2.getDay()], this.formatWeekday = (d2) => this.locale_weekdays[d2.getDay()], this.formatShortMonth = (d2) => this.locale_shortMonths[d2.getMonth()], this.formatMonth = (d2) => this.locale_months[d2.getMonth()], this.formatDayOfMonth = (d2, p2) => this.pad(d2.getDate(), p2, 2), this.formatHour24 = (d2, p2) => this.pad(d2.getHours(), p2, 2), this.formatHour12 = (d2, p2) => this.pad(d2.getHours() % 12 || 12, p2, 2), this.formatMilliseconds = (d2, p2) => this.pad(d2.getMilliseconds(), p2, 3), this.formatMonthNumber = (d2, p2) => this.pad(d2.getMonth() + 1, p2, 2), this.formatMinutes = (d2, p2) => this.pad(d2.getMinutes(), p2, 2), this.formatPeriod = (d2) => this.locale_periods[+(d2.getHours() >= 12)], this.formatSeconds = (d2, p2) => this.pad(d2.getSeconds(), p2, 2), this.formatFullYear = (d2, p2) => this.pad(d2.getFullYear() % 1e4, p2, 4), this.formatUTCShortWeekday = (d2) => this.locale_shortWeekdays[d2.getUTCDay()], this.formatUTCWeekday = (d2) => this.locale_weekdays[d2.getUTCDay()], this.formatUTCShortMonth = (d2) => this.locale_shortMonths[d2.getUTCMonth()], this.formatUTCMonth = (d2) => this.locale_months[d2.getUTCMonth()], this.formatUTCDayOfMonth = (d2, p2) => this.pad(d2.getUTCDate(), p2, 2), this.formatUTCHour24 = (d2, p2) => this.pad(d2.getUTCHours(), p2, 2), this.formatUTCHour12 = (d2, p2) => this.pad(d2.getUTCHours() % 12 || 12, p2, 2), this.formatUTCMilliseconds = (d2, p2) => this.pad(d2.getUTCMilliseconds(), p2, 3), this.formatUTCMonthNumber = (d2, p2) => this.pad(d2.getUTCMonth() + 1, p2, 2), this.formatUTCMinutes = (d2, p2) => this.pad(d2.getUTCMinutes(), p2, 2), this.formatUTCPeriod = (d2) => this.locale_periods[+(d2.getUTCHours() >= 12)], this.formatUTCSeconds = (d2, p2) => this.pad(d2.getUTCSeconds(), p2, 2), this.formatUTCFullYear = (d2, p2) => this.pad(d2.getUTCFullYear() % 1e4, p2, 4), this.formats = {
      a: this.formatShortWeekday,
      A: this.formatWeekday,
      b: this.formatShortMonth,
      B: this.formatMonth,
      d: this.formatDayOfMonth,
      e: this.formatDayOfMonth,
      H: this.formatHour24,
      I: this.formatHour12,
      L: this.formatMilliseconds,
      m: this.formatMonthNumber,
      M: this.formatMinutes,
      p: this.formatPeriod,
      S: this.formatSeconds,
      Y: this.formatFullYear
    }, this.utcFormats = {
      a: this.formatUTCShortWeekday,
      A: this.formatUTCWeekday,
      b: this.formatUTCShortMonth,
      B: this.formatUTCMonth,
      d: this.formatUTCDayOfMonth,
      e: this.formatUTCDayOfMonth,
      H: this.formatUTCHour24,
      I: this.formatUTCHour12,
      L: this.formatUTCMilliseconds,
      m: this.formatUTCMonthNumber,
      M: this.formatUTCMinutes,
      p: this.formatUTCPeriod,
      S: this.formatUTCSeconds,
      Y: this.formatUTCFullYear
    }, this.parseShortWeekday = (d2, string, i2) => {
      const n2 = this.shortWeekdayRe.exec(string.slice(i2));
      return n2 ? (d2.w = this.shortWeekdayLookup.get(n2[0].toLowerCase()), i2 + n2[0].length) : -1;
    }, this.parseWeekday = (d2, string, i2) => {
      const n2 = this.weekdayRe.exec(string.slice(i2));
      return n2 ? (d2.w = this.weekdayLookup.get(n2[0].toLowerCase()), i2 + n2[0].length) : -1;
    }, this.parseShortMonth = (d2, string, i2) => {
      const n2 = this.shortMonthRe.exec(string.slice(i2));
      return n2 ? (d2.m = this.shortMonthLookup.get(n2[0].toLowerCase()), i2 + n2[0].length) : -1;
    }, this.parseMonth = (d2, string, i2) => {
      const n2 = this.monthRe.exec(string.slice(i2));
      return n2 ? (d2.m = this.monthLookup.get(n2[0].toLowerCase()), i2 + n2[0].length) : -1;
    }, this.parseDayOfMonth = (d2, string, i2) => {
      const n2 = this.numberRe.exec(string.slice(i2, i2 + 2));
      return n2 ? (d2.d = +n2[0], i2 + n2[0].length) : -1;
    }, this.parseHour24 = (d2, string, i2) => {
      const n2 = this.numberRe.exec(string.slice(i2, i2 + 2));
      return n2 ? (d2.H = +n2[0], i2 + n2[0].length) : -1;
    }, this.parseMilliseconds = (d2, string, i2) => {
      const n2 = this.numberRe.exec(string.slice(i2, i2 + 3));
      return n2 ? (d2.L = +n2[0], i2 + n2[0].length) : -1;
    }, this.parseMonthNumber = (d2, string, i2) => {
      const n2 = this.numberRe.exec(string.slice(i2, i2 + 2));
      return n2 ? (d2.m = n2 - 1, i2 + n2[0].length) : -1;
    }, this.parseMinutes = (d2, string, i2) => {
      const n2 = this.numberRe.exec(string.slice(i2, i2 + 2));
      return n2 ? (d2.M = +n2[0], i2 + n2[0].length) : -1;
    }, this.parsePeriod = (d2, string, i2) => {
      const n2 = this.periodRe.exec(string.slice(i2));
      return n2 ? (d2.p = this.periodLookup.get(n2[0].toLowerCase()), i2 + n2[0].length) : -1;
    }, this.parseSeconds = (d2, string, i2) => {
      const n2 = this.numberRe.exec(string.slice(i2, i2 + 2));
      return n2 ? (d2.S = +n2[0], i2 + n2[0].length) : -1;
    }, this.parseFullYear = (d2, string, i2) => {
      const n2 = this.numberRe.exec(string.slice(i2, i2 + 4));
      return n2 ? (d2.y = +n2[0], i2 + n2[0].length) : -1;
    }, this.parses = {
      a: this.parseShortWeekday,
      A: this.parseWeekday,
      b: this.parseShortMonth,
      B: this.parseMonth,
      d: this.parseDayOfMonth,
      e: this.parseDayOfMonth,
      H: this.parseHour24,
      I: this.parseHour24,
      L: this.parseMilliseconds,
      m: this.parseMonthNumber,
      M: this.parseMinutes,
      p: this.parsePeriod,
      S: this.parseSeconds,
      Y: this.parseFullYear
    }, this.timeFormat = (specifier, timeText) => this.newFormat(specifier, this.formats)(new Date(this.getFullTimeStamp(timeText))), this.timeUTCFormat = (specifier, timeText) => this.newFormat(specifier, this.utcFormats)(new Date(this.getFullTimeStamp(timeText))), this.timeParse = (specifier, timeText) => this.newParse(specifier, false)(timeText + ""), this.requoteF = this.requote.bind(this), this.periodRe = this.formatRe(this.locale_periods), this.periodLookup = this.formatLookup(this.locale_periods), this.weekdayRe = this.formatRe(this.locale_weekdays), this.weekdayLookup = this.formatLookup(this.locale_weekdays), this.shortWeekdayRe = this.formatRe(this.locale_shortWeekdays), this.shortWeekdayLookup = this.formatLookup(this.locale_shortWeekdays), this.monthRe = this.formatRe(this.locale_months), this.monthLookup = this.formatLookup(this.locale_months), this.shortMonthRe = this.formatRe(this.locale_shortMonths), this.shortMonthLookup = this.formatLookup(this.locale_shortMonths);
  }
  requote(s2) {
    return s2.replace(this.requoteRe, "\\$&");
  }
  localDate(d2) {
    if (0 <= d2.y && d2.y < 100) {
      const date = new Date(-1, d2.m, d2.d, d2.H, d2.M, d2.S, d2.L);
      return date.setFullYear(d2.y), date;
    }
    return new Date(d2.y, d2.m, d2.d, d2.H, d2.M, d2.S, d2.L);
  }
  utcDate(d2) {
    if (0 <= d2.y && d2.y < 100) {
      const date = new Date(Date.UTC(-1, d2.m, d2.d, d2.H, d2.M, d2.S, d2.L));
      return date.setUTCFullYear(d2.y), date;
    }
    return new Date(Date.UTC(d2.y, d2.m, d2.d, d2.H, d2.M, d2.S, d2.L));
  }
  newDate(y3, m2, d2) {
    return {
      y: y3,
      m: m2,
      d: d2,
      H: 0,
      M: 0,
      S: 0,
      L: 0
    };
  }
  formatRe(names) {
    return new RegExp("^(?:" + names.map(this.requoteF).join("|") + ")", "i");
  }
  formatLookup(names) {
    return new Map(names.map((name, i2) => [name.toLowerCase(), i2]));
  }
  pad(value, fill, width) {
    const sign3 = value < 0 ? "-" : "", string = (sign3 ? -value : value) + "", length = string.length;
    return sign3 + (length < width ? new Array(width - length + 1).join(fill) + string : string);
  }
  parseSpecifier(d2, specifier, string, j2) {
    let i2 = 0;
    const n2 = specifier.length, m2 = string.length;
    let c2, parse;
    for (; i2 < n2; ) {
      if (j2 >= m2) return -1;
      if (c2 = specifier.charCodeAt(i2++), 37 === c2) {
        if (c2 = specifier.charAt(i2++), parse = this.parses[c2 in this.pads ? specifier.charAt(i2++) : c2], !parse || (j2 = parse(d2, string, j2)) < 0) return -1;
      } else if (c2 !== string.charCodeAt(j2++)) return -1;
    }
    return j2;
  }
  newParse(specifier, Z3) {
    const that = this;
    return function(string) {
      const d2 = that.newDate(1900, void 0, 1);
      return that.parseSpecifier(d2, specifier, string += "", 0) !== string.length ? null : "Q" in d2 ? new Date(d2.Q) : "s" in d2 ? new Date(1e3 * d2.s + ("L" in d2 ? d2.L : 0)) : (Z3 && !("Z" in d2) && (d2.Z = 0), "p" in d2 && (d2.H = d2.H % 12 + 12 * d2.p), void 0 === d2.m && (d2.m = "q" in d2 ? d2.q : 0), "Z" in d2 ? (d2.H += d2.Z / 100 | 0, d2.M += d2.Z % 100, that.utcDate(d2)) : that.localDate(d2));
    };
  }
  newFormat(specifier, formats) {
    const that = this;
    return function(date) {
      const string = [];
      let i2 = -1, j2 = 0;
      const n2 = specifier.length;
      let c2, pad2, format;
      for (date instanceof Date || (date = /* @__PURE__ */ new Date(+date)); ++i2 < n2; ) 37 === specifier.charCodeAt(i2) && (string.push(specifier.slice(j2, i2)), (pad2 = that.pads[c2 = specifier.charAt(++i2)]) ? c2 = specifier.charAt(++i2) : pad2 = "e" === c2 ? " " : "0", format = formats[c2], c2 = format(date, pad2), string.push(c2), j2 = i2 + 1);
      return string.push(specifier.slice(j2, i2)), string.join("");
    };
  }
  getFullTimeStamp(timeText) {
    const timeOriStamp = parseInt(timeText + "", 10);
    return 10 === String(timeOriStamp).length ? 1e3 * timeOriStamp : timeOriStamp;
  }
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/format/number/specifier.js
var FormatSpecifier = class {
  constructor(specifier = {}) {
    this.fill = void 0 === specifier.fill ? " " : specifier.fill + "", this.align = void 0 === specifier.align ? ">" : specifier.align + "", this.sign = void 0 === specifier.sign ? "-" : specifier.sign + "", this.symbol = void 0 === specifier.symbol ? "" : specifier.symbol + "", this.zero = !!specifier.zero, this.width = void 0 === specifier.width ? void 0 : +specifier.width, this.comma = !!specifier.comma, this.precision = void 0 === specifier.precision ? void 0 : +specifier.precision, this.trim = !!specifier.trim, this.type = void 0 === specifier.type ? "" : specifier.type + "";
  }
  toString() {
    return this.fill + this.align + this.sign + this.symbol + (this.zero ? "0" : "") + (void 0 === this.width ? "" : Math.max(1, 0 | this.width)) + (this.comma ? "," : "") + (void 0 === this.precision ? "" : "." + Math.max(0, 0 | this.precision)) + (this.trim ? "~" : "") + this.type;
  }
};
var numberSpecifierReg = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;
function formatSpecifier(specifier) {
  let match;
  if (match = numberSpecifierReg.exec(specifier)) return new FormatSpecifier({
    fill: match[1],
    align: match[2],
    sign: match[3],
    symbol: match[4],
    zero: match[5],
    width: match[6],
    comma: match[7],
    precision: match[8] && match[8].slice(1),
    trim: match[9],
    type: match[10]
  });
  Logger.getInstance().error("invalid format: " + specifier);
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/format/number/formatDecimal.js
function formatDecimal(x3) {
  return Math.abs(x3 = Math.round(x3)) >= 1e21 ? x3.toLocaleString("en").replace(/,/g, "") : x3.toString(10);
}
function formatDecimalParts(x3, p2) {
  const _x = p2 ? x3.toExponential(p2 - 1) : x3.toExponential(), i2 = _x.indexOf("e");
  if (i2 < 0) return null;
  const coefficient = _x.slice(0, i2);
  return [coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient, +_x.slice(i2 + 1)];
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/format/number/formatGroup.js
function formatGroup(grouping, thousands) {
  return function(value, width) {
    let i2 = value.length;
    const t2 = [];
    let j2 = 0, g3 = grouping[0], length = 0;
    for (; i2 > 0 && g3 > 0 && (length + g3 + 1 > width && (g3 = Math.max(1, width - length)), t2.push(value.substring(i2 -= g3, i2 + g3)), !((length += g3 + 1) > width)); ) g3 = grouping[j2 = (j2 + 1) % grouping.length];
    return t2.reverse().join(thousands);
  };
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/format/number/formatPrefixAuto.js
var prefixExponent;
function formatPrefixAuto(x3, p2) {
  const d2 = formatDecimalParts(x3, p2);
  if (!d2) return x3 + "";
  const coefficient = d2[0], exponent2 = d2[1], i2 = exponent2 - (prefixExponent = 3 * Math.max(-8, Math.min(8, Math.floor(exponent2 / 3)))) + 1, n2 = coefficient.length;
  return i2 === n2 ? coefficient : i2 > n2 ? coefficient + new Array(i2 - n2 + 1).join("0") : i2 > 0 ? coefficient.slice(0, i2) + "." + coefficient.slice(i2) : "0." + new Array(1 - i2).join("0") + formatDecimalParts(x3, Math.max(0, p2 + i2 - 1))[0];
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/format/number/formatRounded.js
function formatRounded(x3, p2) {
  const d2 = formatDecimalParts(x3, p2);
  if (!d2) return x3 + "";
  const coefficient = d2[0], exponent2 = d2[1];
  return exponent2 < 0 ? "0." + new Array(-exponent2).join("0") + coefficient : coefficient.length > exponent2 + 1 ? coefficient.slice(0, exponent2 + 1) + "." + coefficient.slice(exponent2 + 1) : coefficient + new Array(exponent2 - coefficient.length + 2).join("0");
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/format/number/formatTrim.js
function formatTrim(s2) {
  const n2 = s2.length;
  let i1, i0 = -1;
  out: for (let i2 = 1; i2 < n2; ++i2) switch (s2[i2]) {
    case ".":
      i0 = i1 = i2;
      break;
    case "0":
      0 === i0 && (i0 = i2), i1 = i2;
      break;
    default:
      if (!+s2[i2]) break out;
      i0 > 0 && (i0 = 0);
  }
  return i0 > 0 ? s2.slice(0, i0) + s2.slice(i1 + 1) : s2;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/format/number/number.js
var prefixes = ["y", "z", "a", "f", "p", "n", "µ", "m", "", "k", "M", "G", "T", "P", "E", "Z", "Y"];
var NumberUtil = class _NumberUtil {
  constructor() {
    this.locale = {
      thousands: ",",
      grouping: [3],
      currency: ["$", ""]
    }, this.group = void 0 === this.locale.grouping || void 0 === this.locale.thousands ? (group) => group : formatGroup([...this.locale.grouping].map(Number), `${this.locale.thousands}`), this.currencyPrefix = void 0 === this.locale.currency ? "" : this.locale.currency[0] + "", this.currencySuffix = void 0 === this.locale.currency ? "" : this.locale.currency[1] + "", this.decimal = void 0 === this.locale.decimal ? "." : this.locale.decimal + "", this.numerals = void 0 === this.locale.numerals ? (numerals) => numerals : formatNumerals([...this.locale.numerals].map(String)), this.percent = void 0 === this.locale.percent ? "%" : this.locale.percent + "", this.minus = void 0 === this.locale.minus ? "−" : this.locale.minus + "", this.nan = void 0 === this.locale.nan ? "NaN" : this.locale.nan + "", this.formatter = (specifier) => this.newFormat(specifier), this.format = (specifier, value) => this.formatter(specifier)(value), this.formatPrefix = (specifier, value) => this._formatPrefix(specifier, value);
  }
  static getInstance() {
    return _NumberUtil.instance || (_NumberUtil.instance = new _NumberUtil()), _NumberUtil.instance;
  }
  newFormat(specifier) {
    const specifierIns = formatSpecifier(specifier);
    let fill = specifierIns.fill, align = specifierIns.align;
    const sign3 = specifierIns.sign, symbol = specifierIns.symbol;
    let zero2 = specifierIns.zero;
    const width = specifierIns.width;
    let comma = specifierIns.comma, precision = specifierIns.precision, trim = specifierIns.trim, type = specifierIns.type;
    "n" === type ? (comma = true, type = "g") : formatTypes[type] || (void 0 === precision && (precision = 12), trim = true, type = "g"), (zero2 || "0" === fill && "=" === align) && (zero2 = true, fill = "0", align = "=");
    const prefix = "$" === symbol ? this.currencyPrefix : "#" === symbol && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "", suffix = "$" === symbol ? this.currencySuffix : /[%p]/.test(type) ? this.percent : "", formatType = formatTypes[type], maybeSuffix = /[defgprstz%]/.test(type);
    precision = void 0 === precision ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));
    const { nan, minus, decimal, group, numerals } = this;
    function format(value) {
      let i2, n2, c2, valuePrefix = prefix, valueSuffix = suffix, _value = value;
      if ("c" === type) valueSuffix = formatType(_value) + valueSuffix, _value = "";
      else {
        _value = +_value;
        let valueNegative = _value < 0 || 1 / _value < 0;
        if (_value = isNaN(_value) ? nan : formatType(Math.abs(_value), precision), trim && (_value = formatTrim(_value)), valueNegative && 0 == +_value && "+" !== sign3 && (valueNegative = false), valuePrefix = (valueNegative ? "(" === sign3 ? sign3 : minus : "-" === sign3 || "(" === sign3 ? "" : sign3) + valuePrefix, valueSuffix = ("s" === type ? prefixes[8 + prefixExponent / 3] : "") + valueSuffix + (valueNegative && "(" === sign3 ? ")" : ""), maybeSuffix) {
          for (i2 = -1, n2 = _value.length; ++i2 < n2; ) if (c2 = _value.charCodeAt(i2), 48 > c2 || c2 > 57) {
            valueSuffix = (46 === c2 ? decimal + _value.slice(i2 + 1) : _value.slice(i2)) + valueSuffix, _value = _value.slice(0, i2);
            break;
          }
        }
      }
      comma && !zero2 && (_value = group(_value, 1 / 0));
      let length = valuePrefix.length + _value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : "";
      switch (comma && zero2 && (_value = group(padding + _value, padding.length ? width - valueSuffix.length : 1 / 0), padding = ""), align) {
        case "<":
          _value = valuePrefix + _value + valueSuffix + padding;
          break;
        case "=":
          _value = valuePrefix + padding + _value + valueSuffix;
          break;
        case "^":
          _value = padding.slice(0, length = padding.length >> 1) + valuePrefix + _value + valueSuffix + padding.slice(length);
          break;
        default:
          _value = padding + valuePrefix + _value + valueSuffix;
      }
      return numerals(_value);
    }
    return format.toString = function() {
      return specifier + "";
    }, format;
  }
  _formatPrefix(specifier, value) {
    const _specifier = formatSpecifier(specifier);
    _specifier.type = "f";
    const f2 = this.newFormat(_specifier.toString()), e3 = 3 * Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))), k2 = Math.pow(10, -e3), prefix = prefixes[8 + e3 / 3];
    return function(value2) {
      return f2(k2 * value2) + prefix;
    };
  }
};
var formatTypes = {
  "%": (x3, p2) => (100 * x3).toFixed(p2),
  b: (x3) => Math.round(x3).toString(2),
  c: (x3) => x3 + "",
  d: formatDecimal,
  f: (x3, p2) => x3.toFixed(p2),
  e: (x3, p2) => x3.toExponential(p2),
  g: (x3, p2) => x3.toPrecision(p2),
  o: (x3) => Math.round(x3).toString(8),
  p: (x3, p2) => formatRounded(100 * x3, p2),
  r: formatRounded,
  s: formatPrefixAuto,
  X: (x3) => Math.round(x3).toString(16).toUpperCase(),
  x: (x3) => Math.round(x3).toString(16),
  t: (x3, p2) => Number.isInteger(x3) ? x3.toFixed(2) : Math.floor(x3 * Math.pow(10, p2)) / Math.pow(10, p2) + "",
  z: (x3, p2) => x3 % 1 == 0 ? x3 + "" : x3.toFixed(p2)
};
function exponent(x3) {
  const _x = formatDecimalParts(Math.abs(x3));
  return _x ? _x[1] : NaN;
}
function formatNumerals(numerals) {
  return function(value) {
    return value.replace(/[0-9]/g, (i2) => numerals[+i2]);
  };
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/index.js
var import_eventemitter3 = __toESM(require_eventemitter3());

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/lru.js
var LRU = class {
  constructor() {
    this.CLEAN_THRESHOLD = 1e3, this.L_TIME = 1e3, this.R_COUNT = 1, this.R_TIMESTAMP_MAX_SIZE = 20;
  }
  clearCache(cache, params) {
    const { CLEAN_THRESHOLD = this.CLEAN_THRESHOLD, L_TIME = this.L_TIME, R_COUNT = this.R_COUNT } = params;
    if (cache.size < CLEAN_THRESHOLD) return 0;
    let clearNum = 0;
    const clear = (key) => {
      clearNum++, cache.delete(key);
    }, now = Date.now();
    return cache.forEach((item, key) => {
      if (item.timestamp.length < R_COUNT) return clear(key);
      let useCount = 0;
      for (; now - item.timestamp[item.timestamp.length - 1 - useCount] < L_TIME && (useCount++, !(useCount >= R_COUNT)); ) ;
      if (useCount < R_COUNT) return clear(key);
      for (; now - item.timestamp[0] > L_TIME; ) item.timestamp.shift();
    }), clearNum;
  }
  addLimitedTimestamp(cacheItem, t2, params) {
    const { R_TIMESTAMP_MAX_SIZE = this.R_TIMESTAMP_MAX_SIZE } = params;
    cacheItem.timestamp.length > R_TIMESTAMP_MAX_SIZE && cacheItem.timestamp.shift(), cacheItem.timestamp.push(t2);
  }
  clearTimeStamp(cache, params) {
    const { L_TIME = this.L_TIME } = params, now = Date.now();
    cache.forEach((item) => {
      for (; now - item.timestamp[0] > L_TIME; ) item.timestamp.shift();
    });
  }
  clearItemTimestamp(cacheItem, params) {
    const { L_TIME = this.L_TIME } = params, now = Date.now();
    for (; now - cacheItem.timestamp[0] > L_TIME; ) cacheItem.timestamp.shift();
  }
};

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/time/interval.js
var SECOND = 1e3;
var MINUTE = 6e4;
var HOUR = 36e5;
var DAY = 24 * HOUR;
var MONTH = 31 * DAY;
var YEAR = 365 * DAY;

// node_modules/.pnpm/@turf+helpers@6.5.0/node_modules/@turf/helpers/dist/es/index.js
var earthRadius = 63710088e-1;
var factors = {
  centimeters: earthRadius * 100,
  centimetres: earthRadius * 100,
  degrees: earthRadius / 111325,
  feet: earthRadius * 3.28084,
  inches: earthRadius * 39.37,
  kilometers: earthRadius / 1e3,
  kilometres: earthRadius / 1e3,
  meters: earthRadius,
  metres: earthRadius,
  miles: earthRadius / 1609.344,
  millimeters: earthRadius * 1e3,
  millimetres: earthRadius * 1e3,
  nauticalmiles: earthRadius / 1852,
  radians: 1,
  yards: earthRadius * 1.0936
};
var unitsFactors = {
  centimeters: 100,
  centimetres: 100,
  degrees: 1 / 111325,
  feet: 3.28084,
  inches: 39.37,
  kilometers: 1 / 1e3,
  kilometres: 1 / 1e3,
  meters: 1,
  metres: 1,
  miles: 1 / 1609.344,
  millimeters: 1e3,
  millimetres: 1e3,
  nauticalmiles: 1 / 1852,
  radians: 1 / earthRadius,
  yards: 1.0936133
};
function feature(geom, properties, options) {
  if (options === void 0) {
    options = {};
  }
  var feat = { type: "Feature" };
  if (options.id === 0 || options.id) {
    feat.id = options.id;
  }
  if (options.bbox) {
    feat.bbox = options.bbox;
  }
  feat.properties = properties || {};
  feat.geometry = geom;
  return feat;
}
function featureCollection(features, options) {
  if (options === void 0) {
    options = {};
  }
  var fc = { type: "FeatureCollection" };
  if (options.id) {
    fc.id = options.id;
  }
  if (options.bbox) {
    fc.bbox = options.bbox;
  }
  fc.features = features;
  return fc;
}
function lengthToRadians(distance, units) {
  if (units === void 0) {
    units = "kilometers";
  }
  var factor = factors[units];
  if (!factor) {
    throw new Error(units + " units is invalid");
  }
  return distance / factor;
}
function isObject2(input) {
  return !!input && input.constructor === Object;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/geo/invariant.js
function getGeom(geojson) {
  return "Feature" === geojson.type ? geojson.geometry : geojson;
}
function isPointInPolygon(point2, polygon) {
  if (!point2) return false;
  if (!polygon) return false;
  const geom = getGeom(polygon), type = geom.type, bbox = polygon.bbox;
  let polys = geom.coordinates;
  if (bbox && true === pointInRect(point2, {
    x1: bbox[0],
    x2: bbox[1],
    y1: bbox[1],
    y2: bbox[3]
  }, true)) return false;
  "Polygon" === type && (polys = [polys]);
  let result = false;
  for (let i2 = 0; i2 < polys.length; ++i2) for (let j2 = 0; j2 < polys[i2].length; ++j2) {
    if (polygonContainPoint(polys[i2][j2].map((p2) => ({
      x: p2[0],
      y: p2[1]
    })), point2.x, point2.y)) return result = true, result;
  }
  return result;
}
function destination(point2, distance, bearing, options = {}) {
  const longitude1 = degreeToRadian(point2[0]), latitude1 = degreeToRadian(point2[1]), bearingRad = degreeToRadian(bearing), radians2 = lengthToRadians(distance, options.units), latitude2 = Math.asin(Math.sin(latitude1) * Math.cos(radians2) + Math.cos(latitude1) * Math.sin(radians2) * Math.cos(bearingRad)), longitude2 = longitude1 + Math.atan2(Math.sin(bearingRad) * Math.sin(radians2) * Math.cos(latitude1), Math.cos(radians2) - Math.sin(latitude1) * Math.sin(latitude2));
  return {
    x: radianToDegree(longitude2),
    y: radianToDegree(latitude2)
  };
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/geo/constant.js
var SMALL = 1e-10;

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/geo/circle-intersection.js
function intersectionArea(circles, stats) {
  const intersectionPoints = getIntersectionPoints(circles), innerPoints = intersectionPoints.filter(function(p2) {
    return containedInCircles(p2, circles);
  });
  let arcArea = 0, polygonArea = 0;
  const arcs = [];
  if (innerPoints.length > 1) {
    const center = getCenter(innerPoints);
    for (let i2 = 0; i2 < innerPoints.length; ++i2) {
      const p3 = innerPoints[i2];
      p3.angle = Math.atan2(p3.x - center.x, p3.y - center.y);
    }
    innerPoints.sort(function(a2, b2) {
      return b2.angle - a2.angle;
    });
    let p2 = innerPoints[innerPoints.length - 1];
    for (let i2 = 0; i2 < innerPoints.length; ++i2) {
      const p1 = innerPoints[i2];
      polygonArea += (p2.x + p1.x) * (p1.y - p2.y);
      const midPoint = {
        x: (p1.x + p2.x) / 2,
        y: (p1.y + p2.y) / 2
      };
      let arc = null;
      for (let j2 = 0; j2 < p1.parentIndex.length; ++j2) if (p2.parentIndex.indexOf(p1.parentIndex[j2]) > -1) {
        const circle2 = circles[p1.parentIndex[j2]], a1 = Math.atan2(p1.x - circle2.x, p1.y - circle2.y), a2 = Math.atan2(p2.x - circle2.x, p2.y - circle2.y);
        let angleDiff = a2 - a1;
        angleDiff < 0 && (angleDiff += 2 * Math.PI);
        const a3 = a2 - angleDiff / 2;
        let width = PointService.distancePP(midPoint, {
          x: circle2.x + circle2.radius * Math.sin(a3),
          y: circle2.y + circle2.radius * Math.cos(a3)
        });
        width > 2 * circle2.radius && (width = 2 * circle2.radius), (null === arc || arc.width > width) && (arc = {
          circle: circle2,
          width,
          p1,
          p2
        });
      }
      null !== arc && (arcs.push(arc), arcArea += circleArea(arc.circle.radius, arc.width), p2 = p1);
    }
  } else {
    let smallest = circles[0];
    for (let i2 = 1; i2 < circles.length; ++i2) circles[i2].radius < smallest.radius && (smallest = circles[i2]);
    let disjoint = false;
    for (let i2 = 0; i2 < circles.length; ++i2) if (PointService.distancePP(circles[i2], smallest) > Math.abs(smallest.radius - circles[i2].radius)) {
      disjoint = true;
      break;
    }
    disjoint ? arcArea = polygonArea = 0 : (arcArea = smallest.radius * smallest.radius * Math.PI, arcs.push({
      circle: smallest,
      p1: {
        x: smallest.x,
        y: smallest.y + smallest.radius
      },
      p2: {
        x: smallest.x - SMALL,
        y: smallest.y + smallest.radius
      },
      width: 2 * smallest.radius
    }));
  }
  return polygonArea /= 2, stats && (stats.area = arcArea + polygonArea, stats.arcArea = arcArea, stats.polygonArea = polygonArea, stats.arcs = arcs, stats.innerPoints = innerPoints, stats.intersectionPoints = intersectionPoints), arcArea + polygonArea;
}
function containedInCircles(point2, circles) {
  for (let i2 = 0; i2 < circles.length; ++i2) if (PointService.distancePP(point2, circles[i2]) > circles[i2].radius + SMALL) return false;
  return true;
}
function getIntersectionPoints(circles) {
  const ret = [];
  for (let i2 = 0; i2 < circles.length; ++i2) for (let j2 = i2 + 1; j2 < circles.length; ++j2) {
    const intersect = circleCircleIntersection(circles[i2], circles[j2]);
    for (let k2 = 0; k2 < intersect.length; ++k2) {
      const p2 = intersect[k2];
      p2.parentIndex = [i2, j2], ret.push(p2);
    }
  }
  return ret;
}
function circleArea(r2, width) {
  return r2 * r2 * Math.acos(1 - width / r2) - (r2 - width) * Math.sqrt(width * (2 * r2 - width));
}
function circleOverlap(r1, r2, d2) {
  if (d2 >= r1 + r2) return 0;
  if (d2 <= Math.abs(r1 - r2)) return Math.PI * Math.min(r1, r2) * Math.min(r1, r2);
  const w2 = r2 - (d2 * d2 - r1 * r1 + r2 * r2) / (2 * d2);
  return circleArea(r1, r1 - (d2 * d2 - r2 * r2 + r1 * r1) / (2 * d2)) + circleArea(r2, w2);
}
function circleCircleIntersection(p1, p2) {
  const d2 = PointService.distancePP(p1, p2), r1 = p1.radius, r2 = p2.radius;
  if (d2 >= r1 + r2 || d2 <= Math.abs(r1 - r2)) return [];
  const a2 = (r1 * r1 - r2 * r2 + d2 * d2) / (2 * d2), h2 = Math.sqrt(r1 * r1 - a2 * a2), x05 = p1.x + a2 * (p2.x - p1.x) / d2, y05 = p1.y + a2 * (p2.y - p1.y) / d2, rx = -(p2.y - p1.y) * (h2 / d2), ry = -(p2.x - p1.x) * (h2 / d2);
  return [{
    x: x05 + rx,
    y: y05 - ry
  }, {
    x: x05 - rx,
    y: y05 + ry
  }];
}
function getCenter(points) {
  const center = {
    x: 0,
    y: 0
  };
  for (let i2 = 0; i2 < points.length; ++i2) center.x += points[i2].x, center.y += points[i2].y;
  return center.x /= points.length, center.y /= points.length, center;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/fmin/blas1.js
function zeros(x3) {
  const r2 = new Array(x3);
  for (let i2 = 0; i2 < x3; ++i2) r2[i2] = 0;
  return r2;
}
function zerosM(x3, y3) {
  return zeros(x3).map(function() {
    return zeros(y3);
  });
}
function norm2(a2) {
  return Math.sqrt(dotProduct(a2, a2));
}
function scale(ret, value, c2) {
  for (let i2 = 0; i2 < value.length; ++i2) ret[i2] = value[i2] * c2;
}
function weightedSum(ret, w1, v1, w2, v2) {
  for (let j2 = 0; j2 < ret.length; ++j2) ret[j2] = w1 * v1[j2] + w2 * v2[j2];
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/fmin/nelder-mead.js
function nelderMead(f2, x05, parameters) {
  const maxIterations = (parameters = parameters || {}).maxIterations || 200 * x05.length, nonZeroDelta = parameters.nonZeroDelta || 1.05, zeroDelta = parameters.zeroDelta || 1e-3, minErrorDelta = parameters.minErrorDelta || 1e-6, minTolerance = parameters.minErrorDelta || 1e-5, rho = void 0 !== parameters.rho ? parameters.rho : 1, chi = void 0 !== parameters.chi ? parameters.chi : 2, psi = void 0 !== parameters.psi ? parameters.psi : -0.5, sigma = void 0 !== parameters.sigma ? parameters.sigma : 0.5;
  let maxDiff;
  const N2 = x05.length, simplex = new Array(N2 + 1);
  simplex[0] = x05, simplex[0].fx = f2(x05), simplex[0].id = 0;
  for (let i2 = 0; i2 < N2; ++i2) {
    const point2 = x05.slice();
    point2[i2] = point2[i2] ? point2[i2] * nonZeroDelta : zeroDelta, simplex[i2 + 1] = point2, simplex[i2 + 1].fx = f2(point2), simplex[i2 + 1].id = i2 + 1;
  }
  function updateSimplex(value) {
    for (let i2 = 0; i2 < value.length; i2++) simplex[N2][i2] = value[i2];
    simplex[N2].fx = value.fx;
  }
  const sortOrder = function(a2, b2) {
    return a2.fx - b2.fx;
  }, centroid = x05.slice(), reflected = x05.slice(), contracted = x05.slice(), expanded = x05.slice();
  for (let iteration = 0; iteration < maxIterations; ++iteration) {
    if (simplex.sort(sortOrder), parameters.history) {
      const sortedSimplex = simplex.map(function(x3) {
        const state = x3.slice();
        return state.fx = x3.fx, state.id = x3.id, state;
      });
      sortedSimplex.sort(function(a2, b2) {
        return a2.id - b2.id;
      }), parameters.history.push({
        x: simplex[0].slice(),
        fx: simplex[0].fx,
        simplex: sortedSimplex
      });
    }
    maxDiff = 0;
    for (let i2 = 0; i2 < N2; ++i2) maxDiff = Math.max(maxDiff, Math.abs(simplex[0][i2] - simplex[1][i2]));
    if (Math.abs(simplex[0].fx - simplex[N2].fx) < minErrorDelta && maxDiff < minTolerance) break;
    for (let i2 = 0; i2 < N2; ++i2) {
      centroid[i2] = 0;
      for (let j2 = 0; j2 < N2; ++j2) centroid[i2] += simplex[j2][i2];
      centroid[i2] /= N2;
    }
    const worst = simplex[N2];
    if (weightedSum(reflected, 1 + rho, centroid, -rho, worst), reflected.fx = f2(reflected), reflected.fx < simplex[0].fx) weightedSum(expanded, 1 + chi, centroid, -chi, worst), expanded.fx = f2(expanded), expanded.fx < reflected.fx ? updateSimplex(expanded) : updateSimplex(reflected);
    else if (reflected.fx >= simplex[N2 - 1].fx) {
      let shouldReduce = false;
      if (reflected.fx > worst.fx ? (weightedSum(contracted, 1 + psi, centroid, -psi, worst), contracted.fx = f2(contracted), contracted.fx < worst.fx ? updateSimplex(contracted) : shouldReduce = true) : (weightedSum(contracted, 1 - psi * rho, centroid, psi * rho, worst), contracted.fx = f2(contracted), contracted.fx < reflected.fx ? updateSimplex(contracted) : shouldReduce = true), shouldReduce) {
        if (sigma >= 1) break;
        for (let i2 = 1; i2 < simplex.length; ++i2) weightedSum(simplex[i2], 1 - sigma, simplex[0], sigma, simplex[i2]), simplex[i2].fx = f2(simplex[i2]);
      }
    } else updateSimplex(reflected);
  }
  return simplex.sort(sortOrder), {
    fx: simplex[0].fx,
    x: simplex[0]
  };
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/fmin/linesearch.js
function wolfeLineSearch(f2, pk, current, next, a2, c1, c2) {
  const phi0 = current.fx, phiPrime0 = dotProduct(current.fxprime, pk);
  let phi2 = phi0, phi_old = phi0, phiPrime = phiPrime0, a0 = 0;
  function zoom(a_lo, a_high, phi_lo) {
    for (let iteration = 0; iteration < 16; ++iteration) if (a2 = (a_lo + a_high) / 2, weightedSum(next.x, 1, current.x, a2, pk), phi2 = next.fx = f2(next.x, next.fxprime), phiPrime = dotProduct(next.fxprime, pk), phi2 > phi0 + c1 * a2 * phiPrime0 || phi2 >= phi_lo) a_high = a2;
    else {
      if (Math.abs(phiPrime) <= -c2 * phiPrime0) return a2;
      phiPrime * (a_high - a_lo) >= 0 && (a_high = a_lo), a_lo = a2, phi_lo = phi2;
    }
    return 0;
  }
  a2 = a2 || 1, c1 = c1 || 1e-6, c2 = c2 || 0.1;
  for (let iteration = 0; iteration < 10; ++iteration) {
    if (weightedSum(next.x, 1, current.x, a2, pk), phi2 = next.fx = f2(next.x, next.fxprime), phiPrime = dotProduct(next.fxprime, pk), phi2 > phi0 + c1 * a2 * phiPrime0 || iteration && phi2 >= phi_old) return zoom(a0, a2, phi_old);
    if (Math.abs(phiPrime) <= -c2 * phiPrime0) return a2;
    if (phiPrime >= 0) return zoom(a2, a0, phi2);
    phi_old = phi2, a0 = a2, a2 *= 2;
  }
  return a2;
}

// node_modules/.pnpm/@visactor+vutils@0.19.7/node_modules/@visactor/vutils/es/fmin/conjugate-gradient.js
function conjugateGradient(f2, initial, params) {
  let current = {
    x: initial.slice(),
    fx: 0,
    fxprime: initial.slice()
  }, next = {
    x: initial.slice(),
    fx: 0,
    fxprime: initial.slice()
  };
  const yk = initial.slice();
  let temp2, a2 = 1;
  const maxIterations = (params = params || {}).maxIterations || 20 * initial.length;
  current.fx = f2(current.x, current.fxprime);
  const pk = current.fxprime.slice();
  scale(pk, current.fxprime, -1);
  for (let i2 = 0; i2 < maxIterations; ++i2) {
    if (a2 = wolfeLineSearch(f2, pk, current, next, a2), params.history && params.history.push({
      x: current.x.slice(),
      fx: current.fx,
      fxprime: current.fxprime.slice(),
      alpha: a2
    }), a2) {
      weightedSum(yk, 1, next.fxprime, -1, current.fxprime);
      const delta_k = dotProduct(current.fxprime, current.fxprime), beta_k = Math.max(0, dotProduct(yk, next.fxprime) / delta_k);
      weightedSum(pk, beta_k, pk, -1, next.fxprime), temp2 = current, current = next, next = temp2;
    } else scale(pk, current.fxprime, -1);
    if (norm2(current.fxprime) <= 1e-5) break;
  }
  return params.history && params.history.push({
    x: current.x.slice(),
    fx: current.fx,
    fxprime: current.fxprime.slice(),
    alpha: a2
  }), current;
}

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/type.js
var ScaleEnum;
!function(ScaleEnum2) {
  ScaleEnum2.Identity = "identity", ScaleEnum2.Linear = "linear", ScaleEnum2.Log = "log", ScaleEnum2.Pow = "pow", ScaleEnum2.Sqrt = "sqrt", ScaleEnum2.Symlog = "symlog", ScaleEnum2.Time = "time", ScaleEnum2.Quantile = "quantile", ScaleEnum2.Quantize = "quantize", ScaleEnum2.Threshold = "threshold", ScaleEnum2.Ordinal = "ordinal", ScaleEnum2.Point = "point", ScaleEnum2.Band = "band";
}(ScaleEnum || (ScaleEnum = {}));
var EnableScaleMap = {};
Object.values(ScaleEnum).forEach((v2) => {
  EnableScaleMap[v2] = true;
});
function isContinuous(type) {
  switch (type) {
    case ScaleEnum.Linear:
    case ScaleEnum.Log:
    case ScaleEnum.Pow:
    case ScaleEnum.Sqrt:
    case ScaleEnum.Symlog:
    case ScaleEnum.Time:
      return true;
    default:
      return false;
  }
}
function isValidScaleType(type) {
  return !!EnableScaleMap[type];
}
function isDiscrete(type) {
  switch (type) {
    case ScaleEnum.Ordinal:
    case ScaleEnum.Point:
    case ScaleEnum.Band:
      return true;
    default:
      return false;
  }
}

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/utils/utils.js
function identity(x3) {
  return x3;
}
var sqrt2 = (x3) => x3 < 0 ? -Math.sqrt(-x3) : Math.sqrt(x3);
var square = (x3) => x3 < 0 ? -x3 * x3 : x3 * x3;
var logNegative = (x3) => -Math.log(-x3);
var expNegative = (x3) => -Math.exp(-x3);
var pow10 = (x3) => isFinite(x3) ? Math.pow(10, x3) : x3 < 0 ? 0 : x3;
var powp = (base) => 10 === base ? pow10 : base === Math.E ? Math.exp : (x3) => Math.pow(base, x3);
var logp = (base) => base === Math.E ? Math.log : 10 === base ? Math.log10 : 2 === base ? Math.log2 : (base = Math.log(base), (x3) => Math.log(x3) / base);
var symlog = (c2) => (x3) => Math.sign(x3) * Math.log1p(Math.abs(x3 / c2));
var symexp = (c2) => (x3) => Math.sign(x3) * Math.expm1(Math.abs(x3)) * c2;
function normalize(a2, b2) {
  if (a2 = Number(a2), b2 = Number(b2), b2 -= a2) return (x3) => (x3 - a2) / b2;
  const result = Number.isNaN(b2) ? NaN : 0.5;
  return () => result;
}
function bimap(domain, range2, interpolate2) {
  const d0 = domain[0], d1 = domain[1], r0 = range2[0], r1 = range2[1];
  let d0Fuc, r0Fuc;
  return d1 < d0 ? (d0Fuc = normalize(d1, d0), r0Fuc = interpolate2(r1, r0)) : (d0Fuc = normalize(d0, d1), r0Fuc = interpolate2(r0, r1)), (x3) => r0Fuc(d0Fuc(x3));
}
function bandSpace(count2, paddingInner, paddingOuter) {
  let space;
  return space = 1 === count2 ? count2 + 2 * paddingOuter : count2 - paddingInner + 2 * paddingOuter, count2 ? space > 0 ? space : 1 : 0;
}
function scaleWholeRangeSize(count2, bandwidth, paddingInner, paddingOuter) {
  1 === paddingInner && (paddingInner = 0);
  return bandSpace(count2, paddingInner, paddingOuter) * (bandwidth / (1 - paddingInner));
}
function calculateBandwidthFromWholeRangeSize(count2, wholeSize, paddingInner, paddingOuter, round) {
  const space = bandSpace(count2, paddingInner, paddingOuter);
  let step = wholeSize / Math.max(1, space || 1);
  round && (step = Math.floor(step));
  let bandwidth = step * (1 - paddingInner);
  return round && (bandwidth = Math.round(bandwidth)), bandwidth;
}
function calculateWholeRangeFromRangeFactor(range2, rangeFactor) {
  const k2 = (range2[1] - range2[0]) / (rangeFactor[1] - rangeFactor[0]), b2 = range2[0] - k2 * rangeFactor[0];
  return [b2, k2 + b2];
}
function polymap(domain, range2, interpolate2) {
  const j2 = Math.min(domain.length, range2.length) - 1, d2 = new Array(j2), r2 = new Array(j2);
  let i2 = -1;
  for (domain[j2] < domain[0] && (domain = domain.slice().reverse(), range2 = range2.slice().reverse()); ++i2 < j2; ) d2[i2] = normalize(domain[i2], domain[i2 + 1]), r2[i2] = interpolate2(range2[i2], range2[i2 + 1]);
  return function(x3) {
    const i3 = bisect(domain, x3, 1, j2) - 1;
    return r2[i3](d2[i3](x3));
  };
}
var nice = (domain, options) => {
  const newDomain = domain.slice();
  let startIndex = 0, endIndex = newDomain.length - 1, x05 = newDomain[startIndex], x13 = newDomain[endIndex];
  return x13 < x05 && ([startIndex, endIndex] = [endIndex, startIndex], [x05, x13] = [x13, x05]), newDomain[startIndex] = options.floor(x05), newDomain[endIndex] = options.ceil(x13), newDomain;
};
var niceNumber = (value, round = false) => {
  const exponent2 = Math.floor(Math.log10(value)), fraction = value / Math.pow(10, exponent2);
  let niceFraction;
  return niceFraction = round ? fraction < 1.5 ? 1 : fraction < 3 ? 2 : fraction < 7 ? 5 : 10 : fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10, niceFraction * Math.pow(10, exponent2);
};
var restrictNumber = (value, domain) => {
  let min3, max3;
  return domain[0] < domain[1] ? (min3 = domain[0], max3 = domain[1]) : (min3 = domain[1], max3 = domain[0]), Math.min(Math.max(value, min3), max3);
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/base-scale.js
var BaseScale = class {
  constructor() {
    this._rangeFactorStart = null, this._rangeFactorEnd = null;
  }
  _calculateWholeRange(range2) {
    return this._wholeRange ? this._wholeRange : isValid_default(this._rangeFactorStart) && isValid_default(this._rangeFactorEnd) && 2 === range2.length ? (this._wholeRange = calculateWholeRangeFromRangeFactor(range2, [this._rangeFactorStart, this._rangeFactorEnd]), this._wholeRange) : range2;
  }
  rangeFactor(_2, slience, clear) {
    return _2 ? (2 === _2.length && _2.every((r2) => r2 >= 0 && r2 <= 1) && (this._wholeRange = null, 0 === _2[0] && 1 === _2[1] ? (this._rangeFactorStart = null, this._rangeFactorEnd = null) : (this._rangeFactorStart = _2[0], this._rangeFactorEnd = _2[1])), this) : clear ? (this._wholeRange = null, this._rangeFactorStart = null, this._rangeFactorEnd = null, this) : isValid_default(this._rangeFactorStart) && isValid_default(this._rangeFactorEnd) ? [this._rangeFactorStart, this._rangeFactorEnd] : null;
  }
  rangeFactorStart(_2, slience) {
    var _a;
    return isNil_default(_2) ? this._rangeFactorStart : (_2 >= 0 && _2 <= 1 && (this._wholeRange = null, 0 !== _2 || !isNil_default(this._rangeFactorEnd) && 1 !== this._rangeFactorEnd ? (this._rangeFactorStart = _2, this._rangeFactorEnd = null !== (_a = this._rangeFactorEnd) && void 0 !== _a ? _a : 1) : (this._rangeFactorStart = null, this._rangeFactorEnd = null)), this);
  }
  rangeFactorEnd(_2, slience) {
    var _a;
    return isNil_default(_2) ? this._rangeFactorEnd : (_2 >= 0 && _2 <= 1 && (this._wholeRange = null, 0 !== _2 || !isNil_default(this._rangeFactorStart) && 0 !== this._rangeFactorStart ? (this._rangeFactorEnd = _2, this._rangeFactorStart = null !== (_a = this._rangeFactorStart) && void 0 !== _a ? _a : 0) : (this._rangeFactorStart = null, this._rangeFactorEnd = null)), this);
  }
  generateFishEyeTransform() {
    var _a;
    if (!this._fishEyeOptions) return void (this._fishEyeTransform = null);
    const { distortion = 2, radiusRatio = 0.1, radius } = this._fishEyeOptions, range2 = this.range(), first = range2[0], last2 = range2[range2.length - 1], min3 = Math.min(first, last2), max3 = Math.max(first, last2), focus = clamp_default(null !== (_a = this._fishEyeOptions.focus) && void 0 !== _a ? _a : 0, min3, max3), rangeRadius = isNil_default(radius) ? (max3 - min3) * radiusRatio : radius;
    let k0 = Math.exp(distortion);
    k0 = k0 / (k0 - 1) * rangeRadius;
    const k1 = distortion / rangeRadius;
    this._fishEyeTransform = (output) => {
      const delta = Math.abs(output - focus);
      if (delta >= rangeRadius) return output;
      if (delta <= 1e-6) return focus;
      const k2 = k0 * (1 - Math.exp(-delta * k1)) / delta * 0.75 + 0.25;
      return focus + (output - focus) * k2;
    };
  }
  unknown(_2) {
    return arguments.length ? (this._unknown = _2, this) : this._unknown;
  }
  get(key, defaultValue) {
    var _a;
    return null !== (_a = null == this ? void 0 : this[key]) && void 0 !== _a ? _a : defaultValue;
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/ordinal-scale.js
var implicit = Symbol("implicit");
var OrdinalScale = class _OrdinalScale extends BaseScale {
  specified(_2) {
    var _a;
    return _2 ? (this._specified = Object.assign(null !== (_a = this._specified) && void 0 !== _a ? _a : {}, _2), this) : Object.assign({}, this._specified);
  }
  _getSpecifiedValue(input) {
    if (this._specified) return this._specified[input];
  }
  constructor() {
    super(), this.type = ScaleEnum.Ordinal, this._index = /* @__PURE__ */ new Map(), this._domain = [], this._ordinalRange = [], this._unknown = implicit;
  }
  clone() {
    const s2 = new _OrdinalScale().domain(this._domain).range(this._ordinalRange).unknown(this._unknown);
    return this._specified && s2.specified(this._specified), s2;
  }
  calculateVisibleDomain(range2) {
    if (isValid_default(this._rangeFactorStart) && isValid_default(this._rangeFactorEnd) && 2 === range2.length) {
      return [this.invert(range2[0]), this.invert(range2[1])];
    }
    return this._domain;
  }
  scale(d2) {
    const key = `${d2}`, special = this._getSpecifiedValue(key);
    if (void 0 !== special) return special;
    let i2 = this._index.get(key);
    if (!i2) {
      if (this._unknown !== implicit) return this._unknown;
      i2 = this._domain.push(d2), this._index.set(key, i2);
    }
    const output = this._ordinalRange[(i2 - 1) % this._ordinalRange.length];
    return this._fishEyeTransform ? this._fishEyeTransform(output) : output;
  }
  invert(d2) {
    let i2 = 0;
    for (; i2 < this._ordinalRange.length && this._ordinalRange[i2] !== d2; ) i2++;
    return this._domain[(i2 - 1) % this._domain.length];
  }
  domain(_2) {
    if (!_2) return this._domain.slice();
    this._domain = [], this._index = /* @__PURE__ */ new Map();
    for (const value of _2) {
      const key = `${value}`;
      this._index.has(key) || this._index.set(key, this._domain.push(value));
    }
    return this;
  }
  range(_2) {
    if (!_2) return this._ordinalRange.slice();
    const nextRange = Array.from(_2);
    return this._ordinalRange = nextRange, this;
  }
  index(x3) {
    var _a;
    return this._index && null !== (_a = this._index.get(`${x3}`)) && void 0 !== _a ? _a : -1;
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/utils/tick-sample-int.js
function generateTicks(start, stop, step, reverse2) {
  const ticks3 = [];
  let ptr = start;
  for (; ptr <= stop; ) ticks3.push(ptr), ptr += step;
  return reverse2 && ticks3.reverse(), ticks3;
}
function ticks(start, stop, count2, allowExcessive) {
  let reverse2, step;
  if (stop = Math.floor(+stop), start = Math.floor(+start), !(count2 = Math.floor(+count2))) return [];
  if (start === stop) return [start];
  if (reverse2 = stop < start) {
    const n2 = start;
    start = stop, stop = n2;
  }
  let expectedCount = clamper(1, stop - start + 1)(count2);
  if (step = Math.floor((stop - start + 1) / expectedCount), !allowExcessive) for (; Math.ceil((stop - start + 1) / step) > count2 && expectedCount > 1; ) expectedCount -= 1, step = Math.floor((stop - start) / expectedCount);
  return generateTicks(start, stop, step, reverse2);
}
function stepTicks(start, stop, step) {
  let reverse2;
  if (stop = Math.floor(+stop), start = Math.floor(+start), step = clamper(1, stop - start + 1)(Math.floor(+step)), reverse2 = stop < start) {
    const n2 = start;
    start = stop, stop = n2;
  }
  return generateTicks(start, stop, step, reverse2);
}

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/band-scale.js
var BandScale = class _BandScale extends OrdinalScale {
  constructor(slience) {
    super(), this.type = ScaleEnum.Band, this._range = [0, 1], this._step = void 0, this._bandwidth = void 0, this._isFixed = false, this._round = false, this._paddingInner = 0, this._paddingOuter = 0, this._align = 0.5, this._unknown = void 0, delete this.unknown, this.rescale(slience);
  }
  rescale(slience, changeProperty) {
    if (slience) return this;
    this._wholeRange = null;
    const wholeRange = this._calculateWholeRange(this._range, changeProperty), n2 = super.domain().length, reverse2 = wholeRange[1] < wholeRange[0];
    let start = wholeRange[Number(reverse2) - 0];
    const stop = wholeRange[1 - Number(reverse2)], space = bandSpace(n2, this._paddingInner, this._paddingOuter);
    return this._step = (stop - start) / Math.max(1, space || 1), this._round && (this._step = Math.floor(this._step)), start += (stop - start - this._step * (n2 - this._paddingInner)) * this._align, this.isBandwidthFixed() || (this._bandwidth = this._step * (1 - this._paddingInner)), this._round && (start = Math.round(start), this.isBandwidthFixed() || (this._bandwidth = Math.round(this._bandwidth))), this._bandRangeState = {
      reverse: reverse2,
      start: reverse2 ? clamp_default(start + this._step * (n2 - 1), wholeRange[1], wholeRange[0]) : clamp_default(start, wholeRange[0], wholeRange[1]),
      min: reverse2 ? wholeRange[1] : wholeRange[0],
      max: stop,
      count: n2
    }, this.generateFishEyeTransform(), this;
  }
  scale(d2) {
    if (!this._bandRangeState) return;
    const key = `${d2}`, special = this._getSpecifiedValue(key);
    if (void 0 !== special) return special;
    let i2 = this._index.get(key);
    if (!i2) {
      if (this._unknown !== implicit) return this._unknown;
      i2 = this._domain.push(d2), this._index.set(key, i2);
    }
    const { count: count2, start, reverse: reverse2, min: min3, max: max3 } = this._bandRangeState, output = start + (reverse2 ? -1 : 1) * ((i2 - 1) % count2) * this._step;
    return clamp_default(this._fishEyeTransform ? this._fishEyeTransform(output) : output, min3, max3);
  }
  _calculateWholeRange(range2, changeProperty) {
    if (this._wholeRange) return this._wholeRange;
    if ((this._minBandwidth || this._maxBandwidth) && !this._isBandwidthFixedByUser()) {
      let wholeSize;
      if (isValid_default(this._rangeFactorStart) && isValid_default(this._rangeFactorEnd) && 2 === range2.length) {
        const wholeRange = calculateWholeRangeFromRangeFactor(range2, [this._rangeFactorStart, this._rangeFactorEnd]);
        wholeSize = Math.abs(wholeRange[1] - wholeRange[0]);
      } else wholeSize = Math.abs(range2[1] - range2[0]);
      const autoBandwidth = calculateBandwidthFromWholeRangeSize(super.domain().length, wholeSize, this._paddingInner, this._paddingOuter, this._round);
      autoBandwidth < this._minBandwidth ? (this._bandwidth = this._minBandwidth, this._isFixed = true) : autoBandwidth > this._maxBandwidth ? (this._bandwidth = this._maxBandwidth, this._isFixed = true) : (this._bandwidth = autoBandwidth, this._isFixed = false);
    }
    if (this.isBandwidthFixed()) {
      const wholeLength = scaleWholeRangeSize(super.domain().length, this._bandwidth, this._paddingInner, this._paddingOuter) * Math.sign(range2[1] - range2[0]), rangeFactorSize = Math.min((range2[1] - range2[0]) / wholeLength, 1);
      if (isValid_default(this._rangeFactorStart) && isValid_default(this._rangeFactorEnd)) {
        const canAlignStart = this._rangeFactorStart + rangeFactorSize <= 1, canAlignEnd = this._rangeFactorEnd - rangeFactorSize >= 0;
        if ("rangeFactorStart" === changeProperty && canAlignStart ? this._rangeFactorEnd = this._rangeFactorStart + rangeFactorSize : "rangeFactorEnd" === changeProperty && canAlignEnd ? this._rangeFactorStart = this._rangeFactorEnd - rangeFactorSize : range2[0] <= range2[1] ? canAlignStart ? this._rangeFactorEnd = this._rangeFactorStart + rangeFactorSize : canAlignEnd ? this._rangeFactorStart = this._rangeFactorEnd - rangeFactorSize : (this._rangeFactorStart = 0, this._rangeFactorEnd = rangeFactorSize) : canAlignEnd ? this._rangeFactorStart = this._rangeFactorEnd - rangeFactorSize : canAlignStart ? this._rangeFactorEnd = this._rangeFactorStart + rangeFactorSize : (this._rangeFactorStart = 1 - rangeFactorSize, this._rangeFactorEnd = 1), wholeLength > 0) {
          const r0 = range2[0] - wholeLength * this._rangeFactorStart, r1 = r0 + wholeLength;
          this._wholeRange = [r0, r1];
        } else {
          const r1 = range2[1] + wholeLength * (1 - this._rangeFactorEnd), r0 = r1 - wholeLength;
          this._wholeRange = [r0, r1];
        }
      } else this._rangeFactorStart = 0, this._rangeFactorEnd = rangeFactorSize, this._wholeRange = [range2[0], range2[0] + wholeLength];
      return this._wholeRange;
    }
    return super._calculateWholeRange(range2);
  }
  calculateWholeRangeSize() {
    const wholeRange = this._calculateWholeRange(this._range);
    return Math.abs(wholeRange[1] - wholeRange[0]);
  }
  calculateVisibleDomain(range2) {
    const domain = this._domain;
    if (isValid_default(this._rangeFactorStart) && isValid_default(this._rangeFactorEnd) && domain.length) {
      const d0 = this._getInvertIndex(range2[0]), d1 = this._getInvertIndex(range2[1]);
      return domain.slice(Math.min(d0, d1), Math.max(d0, d1) + 1);
    }
    return domain;
  }
  domain(_2, slience) {
    return _2 ? (super.domain(_2), this.rescale(slience)) : super.domain();
  }
  range(_2, slience) {
    return _2 ? (this._range = [toNumber(_2[0]), toNumber(_2[1])], this.rescale(slience)) : this._range;
  }
  rangeRound(_2, slience) {
    return this._range = [toNumber(_2[0]), toNumber(_2[1])], this._round = true, this.rescale(slience);
  }
  ticks(count2 = 10) {
    const d2 = this.calculateVisibleDomain(this._range);
    if (-1 === count2) return d2;
    return ticks(0, d2.length - 1, count2, false).map((i2) => d2[i2]);
  }
  tickData(count2 = 10) {
    return this.ticks(count2).map((tick, index) => ({
      index,
      tick,
      value: (this.scale(tick) - this._range[0] + this._bandwidth / 2) / (this._range[1] - this._range[0])
    }));
  }
  forceTicks(count2 = 10) {
    const d2 = this.calculateVisibleDomain(this._range);
    return ticks(0, d2.length - 1, count2, true).filter((i2) => i2 < d2.length).map((i2) => d2[i2]);
  }
  stepTicks(step) {
    const d2 = this.calculateVisibleDomain(this._range);
    return stepTicks(0, d2.length - 1, step).map((i2) => d2[i2]);
  }
  _getInvertIndex(d2) {
    let i2 = 0;
    const halfStep = this.step() / 2, halfBandwidth = this.bandwidth() / 2, len = this._domain.length, range2 = this.range(), reverse2 = range2[0] > range2[range2.length - 1];
    for (i2 = 0; i2 < len; i2++) {
      const r2 = this.scale(this._domain[i2]) + halfBandwidth;
      if (0 === i2 && (!reverse2 && !isGreater(d2, r2 + halfStep) || reverse2 && !isLess(d2, r2 - halfStep))) break;
      if (i2 === len - 1) break;
      if (!isLess(d2, r2 - halfStep) && !isGreater(d2, r2 + halfStep)) break;
    }
    return i2 >= 0 && i2 <= len - 1 ? i2 : len - 1;
  }
  invert(d2) {
    return this._domain[this._getInvertIndex(d2)];
  }
  padding(p2, slience) {
    return void 0 !== p2 ? (this._paddingOuter = Math.max(0, Math.min(Array.isArray(p2) ? Math.min.apply(null, p2) : p2)), this._paddingInner = this._paddingOuter, this.rescale(slience)) : this._paddingInner;
  }
  paddingInner(_2, slience) {
    return void 0 !== _2 ? (this._paddingInner = Math.max(0, Math.min(1, _2)), this.rescale(slience)) : this._paddingInner;
  }
  paddingOuter(_2, slience) {
    return void 0 !== _2 ? (this._paddingOuter = Math.max(0, Math.min(1, _2)), this.rescale(slience)) : this._paddingOuter;
  }
  step() {
    return this._step;
  }
  round(_2, slience) {
    return void 0 !== _2 ? (this._round = _2, this.rescale(slience)) : this._round;
  }
  align(_2, slience) {
    return void 0 !== _2 ? (this._align = Math.max(0, Math.min(1, _2)), this.rescale(slience)) : this._align;
  }
  rangeFactor(_2, slience) {
    return _2 ? (super.rangeFactor(_2), this.rescale(slience)) : super.rangeFactor();
  }
  rangeFactorStart(_2, slience) {
    return isNil_default(_2) ? super.rangeFactorStart() : (super.rangeFactorStart(_2), this.rescale(slience, "rangeFactorStart"));
  }
  rangeFactorEnd(_2, slience) {
    return isNil_default(_2) ? super.rangeFactorEnd() : (super.rangeFactorEnd(_2), this.rescale(slience, "rangeFactorEnd"));
  }
  bandwidth(_2, slience) {
    return _2 ? ("auto" === _2 ? (this._bandwidth = void 0, this._isFixed = false) : (this._bandwidth = _2, this._isFixed = true), this._userBandwidth = _2, this.rescale(slience)) : this._bandwidth;
  }
  maxBandwidth(_2, slience) {
    return _2 ? (this._maxBandwidth = "auto" === _2 ? void 0 : _2, this.rescale(slience)) : this._maxBandwidth;
  }
  minBandwidth(_2, slience) {
    return _2 ? (this._minBandwidth = "auto" === _2 ? void 0 : _2, this.rescale(slience)) : this._minBandwidth;
  }
  fishEye(options, slience, clear) {
    return options || clear ? (this._fishEyeOptions = options, this._fishEyeTransform = null, this.rescale(slience)) : this._fishEyeOptions;
  }
  isBandwidthFixed() {
    return this._isFixed && !!this._bandwidth;
  }
  _isBandwidthFixedByUser() {
    return this._isFixed && this._userBandwidth && isNumber_default(this._userBandwidth);
  }
  clone() {
    var _a, _b, _c;
    return new _BandScale(true).domain(this._domain, true).range(this._range, true).round(this._round, true).paddingInner(this._paddingInner, true).paddingOuter(this._paddingOuter, true).align(this._align, true).bandwidth(null !== (_a = this._userBandwidth) && void 0 !== _a ? _a : "auto", true).maxBandwidth(null !== (_b = this._maxBandwidth) && void 0 !== _b ? _b : "auto", true).minBandwidth(null !== (_c = this._maxBandwidth) && void 0 !== _c ? _c : "auto");
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/utils/tick-sample.js
var e102 = Math.sqrt(50);
var e52 = Math.sqrt(10);
var e22 = Math.sqrt(2);
var niceNumbers = [1, 2, 5, 10];
var calculateTicksOfSingleValue = (value, tickCount, noDecimals) => {
  let step = 1, start = value;
  const middleIndex = Math.floor((tickCount - 1) / 2), absVal = Math.abs(value);
  return value >= 0 && value <= Number.MIN_VALUE ? start = 0 : value < 0 && value >= -Number.MIN_VALUE ? start = -(tickCount - 1) : !noDecimals && absVal < 1 ? step = getNickStep(absVal).step : (noDecimals || absVal > 1) && (start = Math.floor(value) - middleIndex * step), step > 0 ? (value > 0 ? start = Math.max(start, 0) : value < 0 && (start = Math.min(start, -(tickCount - 1) * step)), range(0, tickCount).map((index) => start + index * step)) : value > 0 ? calculateTicksByStep(0, -(tickCount - 1) / step, step) : calculateTicksByStep((tickCount - 1) / step, 0, step);
};
var d3Ticks = memoize((start, stop, count2, options) => {
  let reverse2, n2, ticks3, step, i2 = -1;
  if (count2 = +count2, (start = +start) === (stop = +stop)) return [start];
  if (Math.abs(start - stop) <= Number.MIN_VALUE && count2 > 0) return [start];
  if ((reverse2 = stop < start) && (n2 = start, start = stop, stop = n2), step = tickIncrement(start, stop, count2).step, !isFinite(step)) return [];
  if (step > 0) {
    let r0 = Math.round(start / step), r1 = Math.round(stop / step);
    for (r0 * step < start && ++r0, r1 * step > stop && --r1, ticks3 = new Array(n2 = r1 - r0 + 1); ++i2 < n2; ) ticks3[i2] = (r0 + i2) * step;
  } else if (step < 0 && (null == options ? void 0 : options.noDecimals)) {
    step = 1;
    const r0 = Math.ceil(start), r1 = Math.floor(stop);
    if (!(r0 <= r1)) return [];
    for (ticks3 = new Array(n2 = r1 - r0 + 1); ++i2 < n2; ) ticks3[i2] = r0 + i2;
  } else {
    step = -step;
    let r0 = Math.round(start * step), r1 = Math.round(stop * step);
    for (r0 / step < start && ++r0, r1 / step > stop && --r1, ticks3 = new Array(n2 = r1 - r0 + 1); ++i2 < n2; ) ticks3[i2] = (r0 + i2) / step;
  }
  return reverse2 && ticks3.reverse(), ticks3;
});
var calculateTicksByStep = (start, stop, step) => {
  let n2, ticks3, i2 = -1;
  if (step > 0) {
    let r0 = Math.floor(start / step), r1 = Math.ceil(stop / step);
    for ((r0 + 1) * step < start && ++r0, (r1 - 1) * step > stop && --r1, ticks3 = new Array(n2 = r1 - r0 + 1); ++i2 < n2; ) ticks3[i2] = (r0 + i2) * step;
  } else {
    step = -step;
    let r0 = Math.floor(start * step), r1 = Math.ceil(stop * step);
    for ((r0 + 1) / step < start && ++r0, (r1 - 1) / step > stop && --r1, ticks3 = new Array(n2 = r1 - r0 + 1); ++i2 < n2; ) ticks3[i2] = (r0 + i2) / step;
  }
  return ticks3;
};
var appendTicksToCount = (ticks3, count2, step) => {
  let n2;
  const firstTick = ticks3[0], lastTick = ticks3[ticks3.length - 1], appendCount = count2 - ticks3.length;
  if (lastTick <= 0) {
    const headTicks2 = [];
    for (n2 = appendCount; n2 >= 1; n2--) headTicks2.push(firstTick - n2 * step);
    return headTicks2.concat(ticks3);
  }
  if (firstTick >= 0) {
    for (n2 = 1; n2 <= appendCount; n2++) ticks3.push(lastTick + n2 * step);
    return ticks3;
  }
  let headTicks = [];
  const tailTicks = [];
  for (n2 = 1; n2 <= appendCount; n2++) n2 % 2 == 0 ? headTicks = [firstTick - Math.floor(n2 / 2) * step].concat(headTicks) : tailTicks.push(lastTick + Math.ceil(n2 / 2) * step);
  return headTicks.concat(ticks3).concat(tailTicks);
};
var ticks2 = memoize((start, stop, count2, options) => {
  let reverse2, ticks3, n2;
  if (count2 = +count2, (start = +start) === (stop = +stop)) return calculateTicksOfSingleValue(start, count2, null == options ? void 0 : options.noDecimals);
  if (Math.abs(start - stop) <= Number.MIN_VALUE && count2 > 0) return calculateTicksOfSingleValue(start, count2, null == options ? void 0 : options.noDecimals);
  (reverse2 = stop < start) && (n2 = start, start = stop, stop = n2);
  const stepRes = tickIncrement(start, stop, count2);
  let step = stepRes.step;
  if (!isFinite(step)) return [];
  if (step > 0) {
    let cur = 1;
    const { power, gap } = stepRes, delatStep = 10 === gap ? 2 * 10 ** power : 1 * 10 ** power;
    for (; cur <= 5 && (ticks3 = calculateTicksByStep(start, stop, step), ticks3.length > count2 + 1) && count2 > 2; ) step += delatStep, cur += 1;
    count2 > 2 && ticks3.length < count2 - 1 && (ticks3 = appendTicksToCount(ticks3, count2, step));
  } else (null == options ? void 0 : options.noDecimals) && step < 0 && (step = 1), ticks3 = calculateTicksByStep(start, stop, step);
  return reverse2 && ticks3.reverse(), ticks3;
});
var getNickStep = (step) => {
  const power = Math.floor(Math.log(step) / Math.LN10), error = step / 10 ** power;
  let gap = niceNumbers[0];
  return error >= e102 ? gap = niceNumbers[3] : error >= e52 ? gap = niceNumbers[2] : error >= e22 && (gap = niceNumbers[1]), power >= 0 ? {
    step: gap * 10 ** power,
    gap,
    power
  } : {
    step: -(10 ** -power) / gap,
    gap,
    power
  };
};
function tickIncrement(start, stop, count2) {
  const step = (stop - start) / Math.max(0, count2);
  return getNickStep(step);
}
function forceTicks(start, stop, count2) {
  let step;
  if (count2 = +count2, (start = +start) === (stop = +stop) && count2 > 0) return [start];
  if (count2 <= 0 || 0 === (step = forceTickIncrement(start, stop, count2)) || !isFinite(step)) return [];
  const ticks3 = new Array(count2);
  for (let i2 = 0; i2 < count2; i2++) ticks3[i2] = start + i2 * step;
  return ticks3;
}
function forceTickIncrement(start, stop, count2) {
  return (stop - start) / Math.max(1, count2 - 1);
}
function stepTicks2(start, stop, step) {
  let n2, reverse2, i2 = -1;
  if (step = +step, (reverse2 = (stop = +stop) < (start = +start)) && (n2 = start, start = stop, stop = n2), !isFinite(step) || stop - start <= step) return [start];
  const count2 = Math.floor((stop - start) / step + 1), ticks3 = new Array(count2);
  for (; ++i2 < count2; ) ticks3[i2] = start + i2 * step;
  return reverse2 && ticks3.reverse(), ticks3;
}
function niceLinear(d2, count2 = 10) {
  let prestep, step, i0 = 0, i1 = d2.length - 1, start = d2[i0], stop = d2[i1], maxIter = 10;
  for (stop < start && (step = start, start = stop, stop = step, step = i0, i0 = i1, i1 = step); maxIter-- > 0; ) {
    if (step = tickIncrement(start, stop, count2).step, step === prestep) return d2[i0] = start, d2[i1] = stop, d2;
    if (step > 0) start = Math.floor(start / step) * step, stop = Math.ceil(stop / step) * step;
    else {
      if (!(step < 0)) break;
      start = Math.ceil(start * step) / step, stop = Math.floor(stop * step) / step;
    }
    prestep = step;
  }
}
function parseNiceOptions(originalDomain, option) {
  const hasForceMin = isNumber_default(option.forceMin), hasForceMax = isNumber_default(option.forceMax);
  let niceType = null;
  const niceMinMax = [];
  let niceDomain = null;
  const domainValidator = hasForceMin && hasForceMax ? (x3) => x3 >= option.forceMin && x3 <= option.forceMax : hasForceMin ? (x3) => x3 >= option.forceMin : hasForceMax ? (x3) => x3 <= option.forceMax : null;
  return hasForceMin ? niceMinMax[0] = option.forceMin : isNumber_default(option.min) && option.min <= Math.min(originalDomain[0], originalDomain[originalDomain.length - 1]) && (niceMinMax[0] = option.min), hasForceMax ? niceMinMax[1] = option.forceMax : isNumber_default(option.max) && option.max >= Math.max(originalDomain[0], originalDomain[originalDomain.length - 1]) && (niceMinMax[1] = option.max), isNumber_default(niceMinMax[0]) && isNumber_default(niceMinMax[1]) ? (niceDomain = originalDomain.slice(), niceDomain[0] = niceMinMax[0], niceDomain[niceDomain.length - 1] = niceMinMax[1]) : niceType = isNumber_default(niceMinMax[0]) || isNumber_default(niceMinMax[1]) ? isNumber_default(niceMinMax[0]) ? "max" : "min" : "all", {
    niceType,
    niceDomain,
    niceMinMax,
    domainValidator
  };
}
var fixPrecision2 = (start, stop, value) => Math.abs(stop - start) < 1 ? +value.toFixed(1) : Math.round(+value);
var d3TicksForLog = memoize((start, stop, count2, base, transformer2, untransformer, options) => {
  let u2 = start, v2 = stop;
  const r2 = v2 < u2;
  r2 && ([u2, v2] = [v2, u2]);
  let k2, t2, i2 = transformer2(u2), j2 = transformer2(v2), z2 = [];
  if (!(base % 1) && j2 - i2 < count2) {
    if (i2 = Math.floor(i2), j2 = Math.ceil(j2), u2 > 0) {
      for (; i2 <= j2; ++i2) for (k2 = 1; k2 < base; ++k2) if (t2 = i2 < 0 ? k2 / untransformer(-i2) : k2 * untransformer(i2), !(t2 < u2)) {
        if (t2 > v2) break;
        z2.push(t2);
      }
    } else for (; i2 <= j2; ++i2) for (k2 = base - 1; k2 >= 1; --k2) if (t2 = i2 > 0 ? k2 / untransformer(-i2) : k2 * untransformer(i2), !(t2 < u2)) {
      if (t2 > v2) break;
      z2.push(t2);
    }
    2 * z2.length < count2 && (z2 = ticks2(u2, v2, count2));
  } else z2 = ticks2(i2, j2, Math.min(j2 - i2, count2)).map(untransformer);
  return z2 = z2.filter((t3) => 0 !== t3), (null == options ? void 0 : options.noDecimals) && (z2 = Array.from(new Set(z2.map((t3) => Math.floor(t3))))), r2 ? z2.reverse() : z2;
});
var ticksBaseTransform = memoize((start, stop, count2, base, transformer2, untransformer) => {
  const ticksResult = [], ticksMap = {}, startExp = transformer2(start), stopExp = transformer2(stop);
  let ticksExp = [];
  if (Number.isInteger(base)) ticksExp = ticks2(startExp, stopExp, count2);
  else {
    const stepExp = (stopExp - startExp) / (count2 - 1);
    for (let i2 = 0; i2 < count2; i2++) ticksExp.push(startExp + i2 * stepExp);
  }
  return ticksExp.forEach((tl) => {
    const power = untransformer(tl), nicePower = Number.isInteger(base) ? fixPrecision2(start, stop, power) : fixPrecision2(start, stop, niceNumber(power)), scopePower = fixPrecision2(start, stop, restrictNumber(nicePower, [start, stop]));
    !ticksMap[scopePower] && !isNaN(scopePower) && ticksExp.length > 1 && (ticksMap[scopePower] = 1, ticksResult.push(scopePower));
  }), ticksResult;
});
var forceTicksBaseTransform = memoize((start, stop, count2, transformer2, untransformer) => forceTicks(transformer2(start), transformer2(stop), count2).map((te) => niceNumber(untransformer(te))));
var forceStepTicksBaseTransform = memoize((start, stop, step, transformer2, untransformer) => stepTicks2(transformer2(start), transformer2(stop), step).map((te) => niceNumber(untransformer(te))));

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/utils/interpolate.js
var { interpolateRgb: interpolateRgb2 } = color_exports;
function interpolate(a2, b2) {
  const t2 = typeof b2;
  let c2;
  if (isNil_default(b2) || "boolean" === t2) return () => b2;
  if ("number" === t2) return interpolateNumber(a2, b2);
  if ("string" === t2) {
    if (c2 = color_exports.Color.parseColorString(b2)) {
      const rgb2 = interpolateRgb2(color_exports.Color.parseColorString(a2), c2);
      return (t3) => rgb2(t3).formatRgb();
    }
    return interpolateNumber(Number(a2), Number(b2));
  }
  return b2 instanceof color_exports.RGB ? interpolateRgb2(a2, b2) : b2 instanceof color_exports.Color ? interpolateRgb2(a2.color, b2.color) : b2 instanceof Date ? interpolateDate(a2, b2) : interpolateNumber(Number(a2), Number(b2));
}

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/continuous-scale.js
var ContinuousScale = class extends BaseScale {
  constructor(transformer2 = identity, untransformer = identity) {
    super(), this._unknown = void 0, this.transformer = transformer2, this.untransformer = untransformer, this._forceAlign = true, this._domain = [0, 1], this._range = [0, 1], this._clamp = identity, this._piecewise = bimap, this._interpolate = interpolate;
  }
  calculateVisibleDomain(range2) {
    var _a;
    if (isValid_default(this._rangeFactorStart) && isValid_default(this._rangeFactorEnd) && 2 === range2.length) {
      return [this.invert(range2[0]), this.invert(range2[1])];
    }
    return null !== (_a = this._niceDomain) && void 0 !== _a ? _a : this._domain;
  }
  fishEye(options, slience, clear) {
    return options || clear ? (this._fishEyeOptions = options, this._fishEyeTransform = null, this.rescale(slience)) : this._fishEyeOptions;
  }
  scale(x3) {
    var _a;
    if (x3 = Number(x3), Number.isNaN(x3) || this._domainValidator && !this._domainValidator(x3)) return this._unknown;
    this._output || (this._output = this._piecewise((null !== (_a = this._niceDomain) && void 0 !== _a ? _a : this._domain).map(this.transformer), this._calculateWholeRange(this._range), this._interpolate));
    const output = this._output(this.transformer(this._clamp(x3)));
    return this._fishEyeTransform ? this._fishEyeTransform(output) : output;
  }
  invert(y3) {
    var _a;
    return this._input || (this._input = this._piecewise(this._calculateWholeRange(this._range), (null !== (_a = this._niceDomain) && void 0 !== _a ? _a : this._domain).map(this.transformer), interpolateNumber)), this._clamp(this.untransformer(this._input(y3)));
  }
  domain(_2, slience) {
    var _a;
    if (!_2) return (null !== (_a = this._niceDomain) && void 0 !== _a ? _a : this._domain).slice();
    this._domainValidator = null, this._niceType = null, this._niceDomain = null;
    const nextDomain = Array.from(_2, toNumber);
    return this._domain = nextDomain, this.rescale(slience);
  }
  range(_2, slience) {
    if (!_2) return this._range.slice();
    const nextRange = Array.from(_2);
    return this._range = nextRange, this.rescale(slience);
  }
  rangeRound(_2, slience) {
    const nextRange = Array.from(_2);
    return this._range = nextRange, this._interpolate = interpolateNumberRound, this.rescale(slience);
  }
  rescale(slience) {
    var _a;
    if (slience) return this;
    const domain = null !== (_a = this._niceDomain) && void 0 !== _a ? _a : this._domain, domainLength = domain.length, rangeLength = this._range.length;
    let n2 = Math.min(domainLength, rangeLength);
    if (domainLength && domainLength < rangeLength && this._forceAlign) {
      const deltaStep = rangeLength - domainLength + 1, last2 = domain[domainLength - 1], delta = domainLength >= 2 ? (last2 - domain[domainLength - 2]) / deltaStep : 0;
      for (let i2 = 1; i2 <= deltaStep; i2++) domain[domainLength - 2 + i2] = last2 - delta * (deltaStep - i2);
      n2 = rangeLength;
    }
    return this._autoClamp && (this._clamp = clamper(domain[0], domain[n2 - 1])), this._piecewise = n2 > 2 ? polymap : bimap, this._output = this._input = null, this._wholeRange = null, this.generateFishEyeTransform(), this;
  }
  clamp(_2, f2, slience) {
    return arguments.length ? (f2 ? (this._autoClamp = false, this._clamp = f2) : (this._autoClamp = !!_2, this._clamp = _2 ? void 0 : identity), this.rescale(slience)) : this._clamp !== identity;
  }
  interpolate(_2, slience) {
    return arguments.length ? (this._interpolate = _2, this.rescale(slience)) : this._interpolate;
  }
  ticks(count2 = 10) {
    return [];
  }
  tickData(count2 = 10) {
    const ticks3 = this.ticks(count2);
    return (null != ticks3 ? ticks3 : []).map((tick, index) => ({
      index,
      tick,
      value: (this.scale(tick) - this._range[0]) / (this._range[1] - this._range[0])
    }));
  }
  rangeFactor(_2, slience) {
    return _2 ? (super.rangeFactor(_2), this._output = this._input = null, this) : super.rangeFactor();
  }
  rangeFactorStart(_2, slience) {
    return isNil_default(_2) ? super.rangeFactorStart() : (super.rangeFactorStart(_2), this._output = this._input = null, this);
  }
  rangeFactorEnd(_2, slience) {
    return isNil_default(_2) ? super.rangeFactorEnd() : (super.rangeFactorEnd(_2), this._output = this._input = null, this);
  }
  forceAlignDomainRange(forceAlign) {
    return arguments.length ? (this._forceAlign = forceAlign, this) : this._forceAlign;
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/linear-scale.js
var LinearScale = class _LinearScale extends ContinuousScale {
  constructor() {
    super(...arguments), this.type = ScaleEnum.Linear;
  }
  clone() {
    var _a;
    const scale2 = new _LinearScale();
    return scale2.domain(this._domain, true).range(this._range, true).unknown(this._unknown).clamp(this.clamp(), null, true).interpolate(this._interpolate), this._niceType && (scale2._niceType = this._niceType, scale2._domainValidator = this._domainValidator, scale2._niceDomain = null === (_a = this._niceDomain) || void 0 === _a ? void 0 : _a.slice()), scale2;
  }
  tickFormat() {
    return () => {
    };
  }
  d3Ticks(count2 = 10, options) {
    const d2 = this.calculateVisibleDomain(this._range);
    return d3Ticks(d2[0], d2[d2.length - 1], count2, options);
  }
  ticks(count2 = 10, options) {
    var _a;
    if (isFunction_default(null == options ? void 0 : options.customTicks)) return options.customTicks(this, count2);
    if (isValid_default(this._rangeFactorStart) && isValid_default(this._rangeFactorEnd) && (this._rangeFactorStart > 0 || this._rangeFactorEnd < 1) && 2 === this._range.length || !this._niceType) return this.d3Ticks(count2, options);
    const curNiceDomain = null !== (_a = this._niceDomain) && void 0 !== _a ? _a : this._domain, originalDomain = this._domain, start = curNiceDomain[0], stop = curNiceDomain[curNiceDomain.length - 1];
    let ticksResult = ticks2(originalDomain[0], originalDomain[originalDomain.length - 1], count2, options);
    if (!ticksResult.length) return ticksResult;
    if (this._domainValidator) ticksResult = ticksResult.filter(this._domainValidator);
    else if ((ticksResult[0] !== start || ticksResult[ticksResult.length - 1] !== stop) && this._niceType) {
      const newNiceDomain = curNiceDomain.slice();
      if ("all" === this._niceType ? (newNiceDomain[0] = ticksResult[0], newNiceDomain[newNiceDomain.length - 1] = ticksResult[ticksResult.length - 1], this._niceDomain = newNiceDomain, this.rescale()) : "min" === this._niceType && ticksResult[0] !== start ? (newNiceDomain[0] = ticksResult[0], this._niceDomain = newNiceDomain, this.rescale()) : "max" === this._niceType && ticksResult[ticksResult.length - 1] !== stop && (newNiceDomain[newNiceDomain.length - 1] = ticksResult[ticksResult.length - 1], this._niceDomain = newNiceDomain, this.rescale()), "all" !== this._niceType) {
        const min3 = Math.min(newNiceDomain[0], newNiceDomain[newNiceDomain.length - 1]), max3 = Math.max(newNiceDomain[0], newNiceDomain[newNiceDomain.length - 1]);
        ticksResult = ticksResult.filter((entry) => entry >= min3 && entry <= max3);
      }
    }
    return ticksResult;
  }
  forceTicks(count2 = 10) {
    const d2 = this.calculateVisibleDomain(this._range);
    return forceTicks(d2[0], d2[d2.length - 1], count2);
  }
  stepTicks(step) {
    const d2 = this.calculateVisibleDomain(this._range);
    return stepTicks2(d2[0], d2[d2.length - 1], step);
  }
  nice(count2 = 10, option) {
    var _a, _b;
    const originalDomain = this._domain;
    let niceMinMax = [];
    if (option) {
      const res = parseNiceOptions(originalDomain, option);
      if (niceMinMax = res.niceMinMax, this._domainValidator = res.domainValidator, this._niceType = res.niceType, res.niceDomain) return this._niceDomain = res.niceDomain, this.rescale(), this;
    } else this._niceType = "all";
    if (this._niceType) {
      const niceDomain = niceLinear(originalDomain.slice(), count2);
      "min" === this._niceType ? niceDomain[niceDomain.length - 1] = null !== (_a = niceMinMax[1]) && void 0 !== _a ? _a : niceDomain[niceDomain.length - 1] : "max" === this._niceType && (niceDomain[0] = null !== (_b = niceMinMax[0]) && void 0 !== _b ? _b : niceDomain[0]), this._niceDomain = niceDomain, this.rescale();
    }
    return this;
  }
  niceMin(count2 = 10) {
    this._niceType = "min";
    const maxD = this._domain[this._domain.length - 1], niceDomain = niceLinear(this.domain(), count2);
    return niceDomain && (niceDomain[niceDomain.length - 1] = maxD, this._niceDomain = niceDomain, this.rescale()), this;
  }
  niceMax(count2 = 10) {
    this._niceType = "max";
    const minD = this._domain[0], niceDomain = niceLinear(this._domain.slice(), count2);
    return niceDomain && (niceDomain[0] = minD, this._niceDomain = niceDomain, this.rescale()), this;
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/log-nice-mixin.js
var LogNiceMixin = class {
  nice(count2 = 10, option) {
    var _b, _c, _d, _e;
    const originalDomain = this._domain;
    let niceMinMax = [], niceType = null;
    if (option) {
      const res = parseNiceOptions(originalDomain, option);
      if (niceMinMax = res.niceMinMax, this._domainValidator = res.domainValidator, niceType = res.niceType, res.niceDomain) return this._niceDomain = res.niceDomain, this.rescale(), this;
    } else niceType = "all";
    if (niceType) {
      const niceDomain = nice(originalDomain.slice(), null !== (_c = null === (_b = this.getNiceConfig) || void 0 === _b ? void 0 : _b.call(this)) && void 0 !== _c ? _c : {
        floor: (x3) => Math.floor(x3),
        ceil: (x3) => Math.ceil(x3)
      });
      return "min" === niceType ? niceDomain[niceDomain.length - 1] = null !== (_d = niceMinMax[1]) && void 0 !== _d ? _d : niceDomain[niceDomain.length - 1] : "max" === niceType && (niceDomain[0] = null !== (_e = niceMinMax[0]) && void 0 !== _e ? _e : niceDomain[0]), this._niceDomain = niceDomain, this.rescale(), this;
    }
    return this;
  }
  niceMin() {
    const maxD = this._domain[this._domain.length - 1];
    this.nice();
    const niceDomain = this._domain.slice();
    return this._domain && (niceDomain[niceDomain.length - 1] = maxD, this._niceDomain = niceDomain, this.rescale()), this;
  }
  niceMax() {
    const minD = this._domain[0];
    this.nice();
    const niceDomain = this._domain.slice();
    return this._domain && (niceDomain[0] = minD, this._niceDomain = niceDomain, this.rescale()), this;
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/log-scale.js
function reflect(f2) {
  return (x3) => -f2(-x3);
}
function limitPositiveZero(min3 = Number.EPSILON) {
  return (x3) => Math.max(x3, min3);
}
function limitNegativeZero(min3 = Number.EPSILON) {
  return (x3) => Math.min(x3, -min3);
}
var LogScale = class _LogScale extends ContinuousScale {
  constructor() {
    super(logp(10), powp(10)), this.type = ScaleEnum.Log, this._limit = limitPositiveZero(), this._logs = this.transformer, this._pows = this.untransformer, this._domain = [1, 10], this._base = 10;
  }
  clone() {
    return new _LogScale().domain(this._domain, true).range(this._range, true).unknown(this._unknown).clamp(this.clamp(), null, true).interpolate(this._interpolate, true).base(this._base);
  }
  rescale(slience) {
    var _a;
    if (slience) return this;
    super.rescale();
    const logs = logp(this._base), pows = powp(this._base);
    return (null !== (_a = this._niceDomain) && void 0 !== _a ? _a : this._domain)[0] < 0 ? (this._logs = reflect(logs), this._pows = reflect(pows), this._limit = limitNegativeZero(), this.transformer = logNegative, this.untransformer = expNegative) : (this._logs = logs, this._pows = pows, this._limit = limitPositiveZero(), this.transformer = this._logs, this.untransformer = pows), this;
  }
  scale(x3) {
    var _a;
    if (x3 = Number(x3), Number.isNaN(x3) || this._domainValidator && !this._domainValidator(x3)) return this._unknown;
    this._output || (this._output = this._piecewise((null !== (_a = this._niceDomain) && void 0 !== _a ? _a : this._domain).map(this._limit).map(this.transformer), this._calculateWholeRange(this._range), this._interpolate));
    const output = this._output(this.transformer(this._limit(this._clamp(x3))));
    return this._fishEyeTransform ? this._fishEyeTransform(output) : output;
  }
  base(_2, slience) {
    return arguments.length ? (this._base = _2, this.rescale(slience)) : this._base;
  }
  tickFormat() {
    return identity;
  }
  d3Ticks(count2 = 10, options) {
    const d2 = this.domain(), u2 = this._limit(d2[0]), v2 = this._limit(d2[d2.length - 1]);
    return d3TicksForLog(u2, v2, count2, this._base, this.transformer, this.untransformer, options);
  }
  ticks(count2 = 10) {
    const d2 = this.calculateVisibleDomain(this._range);
    return ticksBaseTransform(this._limit(d2[0]), this._limit(d2[d2.length - 1]), count2, this._base, this.transformer, this.untransformer);
  }
  forceTicks(count2 = 10) {
    const d2 = this.calculateVisibleDomain(this._range);
    return forceTicksBaseTransform(d2[0], d2[d2.length - 1], count2, this.transformer, this.untransformer);
  }
  stepTicks(step) {
    const d2 = this.calculateVisibleDomain(this._range);
    return forceTicksBaseTransform(this._limit(d2[0]), this._limit(d2[d2.length - 1]), step, this.transformer, this.untransformer);
  }
  getNiceConfig() {
    return {
      floor: (x3) => this._pows(Math.floor(this._logs(this._limit(x3)))),
      ceil: (x3) => Math.abs(x3) >= 1 ? Math.ceil(x3) : this._pows(Math.ceil(this._logs(this._limit(x3))))
    };
  }
};
mixin(LogScale, LogNiceMixin);

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/symlog-scale.js
var SymlogScale = class _SymlogScale extends LinearScale {
  constructor() {
    super(symlog(1), symexp(1)), this.type = ScaleEnum.Symlog, this._const = 1;
  }
  clone() {
    return new _SymlogScale().domain(this._domain, true).range(this._range, true).unknown(this._unknown).clamp(this.clamp(), null, true).interpolate(this._interpolate, true).constant(this._const);
  }
  constant(_2, slience) {
    return arguments.length ? (this._const = _2, this.transformer = symlog(_2), this.untransformer = symexp(_2), this.rescale(slience)) : this._const;
  }
  d3Ticks(count2 = 10, options) {
    const d2 = this.domain(), u2 = d2[0], v2 = d2[d2.length - 1];
    return d3TicksForLog(u2, v2, count2, this._const, this.transformer, this.untransformer, options);
  }
  ticks(count2 = 10) {
    const d2 = this.calculateVisibleDomain(this._range);
    return ticksBaseTransform(d2[0], d2[d2.length - 1], count2, this._const, this.transformer, this.untransformer);
  }
  forceTicks(count2 = 10) {
    const d2 = this.calculateVisibleDomain(this._range);
    return forceTicksBaseTransform(d2[0], d2[d2.length - 1], count2, this.transformer, this.untransformer);
  }
  stepTicks(step) {
    const d2 = this.calculateVisibleDomain(this._range);
    return forceTicksBaseTransform(d2[0], d2[d2.length - 1], step, this.transformer, this.untransformer);
  }
};
mixin(SymlogScale, LogNiceMixin);

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/utils/tick-wilkinson-extended.js
var eps = 100 * Number.EPSILON;

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/point-scale.js
var PointScale = class extends BandScale {
  constructor(slience) {
    super(false), this.type = ScaleEnum.Point, this._padding = 0, this.paddingInner(1, slience), this.padding = this.paddingOuter, this.paddingInner = void 0, this.paddingOuter = void 0;
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/sqrt-scale.js
var SqrtScale = class _SqrtScale extends LinearScale {
  constructor() {
    super(sqrt2, square), this.type = ScaleEnum.Sqrt;
  }
  clone() {
    return new _SqrtScale().domain(this._domain, true).range(this._range, true).unknown(this._unknown).clamp(this.clamp(), null, true).interpolate(this._interpolate);
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/threshold-scale.js
var ThresholdScale = class _ThresholdScale {
  constructor() {
    this.type = ScaleEnum.Threshold, this._range = [0, 1], this._domain = [0.5], this.n = 1;
  }
  unknown(_2) {
    return arguments.length ? (this._unknown = _2, this) : this._unknown;
  }
  scale(x3) {
    return !isNil_default(x3) && isValidNumber_default(+x3) ? this._range[bisect(this._domain, x3, 0, this.n)] : this._unknown;
  }
  invertExtent(y3) {
    const i2 = this._range.indexOf(y3);
    return [this._domain[i2 - 1], this._domain[i2]];
  }
  domain(_2) {
    return _2 ? (this._domain = Array.from(_2), this.n = Math.min(this._domain.length, this._range.length - 1), this) : this._domain.slice();
  }
  range(_2) {
    return _2 ? (this._range = Array.from(_2), this.n = Math.min(this._domain.length, this._range.length - 1), this) : this._range.slice();
  }
  clone() {
    return new _ThresholdScale().domain(this._domain).range(this._range).unknown(this._unknown);
  }
};

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/utils/time.js
var timeIntervals = [["second", 1, SECOND], ["second", 5, 5 * SECOND], ["second", 10, 10 * SECOND], ["second", 30, 30 * SECOND], ["minute", 1, MINUTE], ["minute", 5, 5 * MINUTE], ["minute", 10, 10 * MINUTE], ["minute", 30, 30 * MINUTE], ["hour", 1, HOUR], ["hour", 3, 3 * HOUR], ["hour", 6, 6 * HOUR], ["hour", 12, 12 * HOUR], ["day", 1, DAY], ["day", 2, 2 * DAY], ["day", 7, 7 * DAY], ["month", 1, MONTH], ["month", 3, 3 * MONTH], ["month", 6, 6 * MONTH], ["year", 1, 365 * DAY]];

// node_modules/.pnpm/@visactor+vscale@0.19.7/node_modules/@visactor/vscale/es/identity-scale.js
var implicit2 = Symbol("implicit");

// node_modules/.pnpm/d3-dsv@2.0.0/node_modules/d3-dsv/src/dsv.js
var EOL = {};
var EOF = {};
var QUOTE = 34;
var NEWLINE = 10;
var RETURN = 13;
function objectConverter(columns) {
  return new Function("d", "return {" + columns.map(function(name, i2) {
    return JSON.stringify(name) + ": d[" + i2 + '] || ""';
  }).join(",") + "}");
}
function customConverter(columns, f2) {
  var object2 = objectConverter(columns);
  return function(row, i2) {
    return f2(object2(row), i2, columns);
  };
}
function inferColumns(rows) {
  var columnSet = /* @__PURE__ */ Object.create(null), columns = [];
  rows.forEach(function(row) {
    for (var column in row) {
      if (!(column in columnSet)) {
        columns.push(columnSet[column] = column);
      }
    }
  });
  return columns;
}
function pad(value, width) {
  var s2 = value + "", length = s2.length;
  return length < width ? new Array(width - length + 1).join(0) + s2 : s2;
}
function formatYear(year) {
  return year < 0 ? "-" + pad(-year, 6) : year > 9999 ? "+" + pad(year, 6) : pad(year, 4);
}
function formatDate(date) {
  var hours = date.getUTCHours(), minutes = date.getUTCMinutes(), seconds = date.getUTCSeconds(), milliseconds = date.getUTCMilliseconds();
  return isNaN(date) ? "Invalid Date" : formatYear(date.getUTCFullYear(), 4) + "-" + pad(date.getUTCMonth() + 1, 2) + "-" + pad(date.getUTCDate(), 2) + (milliseconds ? "T" + pad(hours, 2) + ":" + pad(minutes, 2) + ":" + pad(seconds, 2) + "." + pad(milliseconds, 3) + "Z" : seconds ? "T" + pad(hours, 2) + ":" + pad(minutes, 2) + ":" + pad(seconds, 2) + "Z" : minutes || hours ? "T" + pad(hours, 2) + ":" + pad(minutes, 2) + "Z" : "");
}
function dsv_default(delimiter) {
  var reFormat = new RegExp('["' + delimiter + "\n\r]"), DELIMITER = delimiter.charCodeAt(0);
  function parse(text, f2) {
    var convert, columns, rows = parseRows(text, function(row, i2) {
      if (convert) return convert(row, i2 - 1);
      columns = row, convert = f2 ? customConverter(row, f2) : objectConverter(row);
    });
    rows.columns = columns || [];
    return rows;
  }
  function parseRows(text, f2) {
    var rows = [], N2 = text.length, I2 = 0, n2 = 0, t2, eof = N2 <= 0, eol = false;
    if (text.charCodeAt(N2 - 1) === NEWLINE) --N2;
    if (text.charCodeAt(N2 - 1) === RETURN) --N2;
    function token() {
      if (eof) return EOF;
      if (eol) return eol = false, EOL;
      var i2, j2 = I2, c2;
      if (text.charCodeAt(j2) === QUOTE) {
        while (I2++ < N2 && text.charCodeAt(I2) !== QUOTE || text.charCodeAt(++I2) === QUOTE) ;
        if ((i2 = I2) >= N2) eof = true;
        else if ((c2 = text.charCodeAt(I2++)) === NEWLINE) eol = true;
        else if (c2 === RETURN) {
          eol = true;
          if (text.charCodeAt(I2) === NEWLINE) ++I2;
        }
        return text.slice(j2 + 1, i2 - 1).replace(/""/g, '"');
      }
      while (I2 < N2) {
        if ((c2 = text.charCodeAt(i2 = I2++)) === NEWLINE) eol = true;
        else if (c2 === RETURN) {
          eol = true;
          if (text.charCodeAt(I2) === NEWLINE) ++I2;
        } else if (c2 !== DELIMITER) continue;
        return text.slice(j2, i2);
      }
      return eof = true, text.slice(j2, N2);
    }
    while ((t2 = token()) !== EOF) {
      var row = [];
      while (t2 !== EOL && t2 !== EOF) row.push(t2), t2 = token();
      if (f2 && (row = f2(row, n2++)) == null) continue;
      rows.push(row);
    }
    return rows;
  }
  function preformatBody(rows, columns) {
    return rows.map(function(row) {
      return columns.map(function(column) {
        return formatValue(row[column]);
      }).join(delimiter);
    });
  }
  function format(rows, columns) {
    if (columns == null) columns = inferColumns(rows);
    return [columns.map(formatValue).join(delimiter)].concat(preformatBody(rows, columns)).join("\n");
  }
  function formatBody(rows, columns) {
    if (columns == null) columns = inferColumns(rows);
    return preformatBody(rows, columns).join("\n");
  }
  function formatRows(rows) {
    return rows.map(formatRow).join("\n");
  }
  function formatRow(row) {
    return row.map(formatValue).join(delimiter);
  }
  function formatValue(value) {
    return value == null ? "" : value instanceof Date ? formatDate(value) : reFormat.test(value += "") ? '"' + value.replace(/"/g, '""') + '"' : value;
  }
  return {
    parse,
    parseRows,
    format,
    formatBody,
    formatRows,
    formatRow,
    formatValue
  };
}

// node_modules/.pnpm/d3-dsv@2.0.0/node_modules/d3-dsv/src/csv.js
var csv = dsv_default(",");
var csvParse = csv.parse;
var csvParseRows = csv.parseRows;
var csvFormat = csv.format;
var csvFormatBody = csv.formatBody;
var csvFormatRows = csv.formatRows;
var csvFormatRow = csv.formatRow;
var csvFormatValue = csv.formatValue;

// node_modules/.pnpm/d3-dsv@2.0.0/node_modules/d3-dsv/src/tsv.js
var tsv = dsv_default("	");
var tsvParse = tsv.parse;
var tsvParseRows = tsv.parseRows;
var tsvFormat = tsv.format;
var tsvFormatBody = tsv.formatBody;
var tsvFormatRows = tsv.formatRows;
var tsvFormatRow = tsv.formatRow;
var tsvFormatValue = tsv.formatValue;

// node_modules/.pnpm/d3-dsv@2.0.0/node_modules/d3-dsv/src/autoType.js
var fixtz = (/* @__PURE__ */ new Date("2019-01-01T00:00")).getHours() || (/* @__PURE__ */ new Date("2019-07-01T00:00")).getHours();

// node_modules/.pnpm/d3-hexbin@0.2.2/node_modules/d3-hexbin/src/hexbin.js
var thirdPi = Math.PI / 3;
var angles = [0, thirdPi, 2 * thirdPi, 3 * thirdPi, 4 * thirdPi, 5 * thirdPi];

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/identity.js
function identity_default(x3) {
  return x3;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/stream.js
function streamGeometry(geometry, stream) {
  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {
    streamGeometryType[geometry.type](geometry, stream);
  }
}
var streamObjectType = {
  Feature: function(object2, stream) {
    streamGeometry(object2.geometry, stream);
  },
  FeatureCollection: function(object2, stream) {
    var features = object2.features, i2 = -1, n2 = features.length;
    while (++i2 < n2) streamGeometry(features[i2].geometry, stream);
  }
};
var streamGeometryType = {
  Sphere: function(object2, stream) {
    stream.sphere();
  },
  Point: function(object2, stream) {
    object2 = object2.coordinates;
    stream.point(object2[0], object2[1], object2[2]);
  },
  MultiPoint: function(object2, stream) {
    var coordinates = object2.coordinates, i2 = -1, n2 = coordinates.length;
    while (++i2 < n2) object2 = coordinates[i2], stream.point(object2[0], object2[1], object2[2]);
  },
  LineString: function(object2, stream) {
    streamLine(object2.coordinates, stream, 0);
  },
  MultiLineString: function(object2, stream) {
    var coordinates = object2.coordinates, i2 = -1, n2 = coordinates.length;
    while (++i2 < n2) streamLine(coordinates[i2], stream, 0);
  },
  Polygon: function(object2, stream) {
    streamPolygon(object2.coordinates, stream);
  },
  MultiPolygon: function(object2, stream) {
    var coordinates = object2.coordinates, i2 = -1, n2 = coordinates.length;
    while (++i2 < n2) streamPolygon(coordinates[i2], stream);
  },
  GeometryCollection: function(object2, stream) {
    var geometries = object2.geometries, i2 = -1, n2 = geometries.length;
    while (++i2 < n2) streamGeometry(geometries[i2], stream);
  }
};
function streamLine(coordinates, stream, closed) {
  var i2 = -1, n2 = coordinates.length - closed, coordinate;
  stream.lineStart();
  while (++i2 < n2) coordinate = coordinates[i2], stream.point(coordinate[0], coordinate[1], coordinate[2]);
  stream.lineEnd();
}
function streamPolygon(coordinates, stream) {
  var i2 = -1, n2 = coordinates.length;
  stream.polygonStart();
  while (++i2 < n2) streamLine(coordinates[i2], stream, 1);
  stream.polygonEnd();
}
function stream_default(object2, stream) {
  if (object2 && streamObjectType.hasOwnProperty(object2.type)) {
    streamObjectType[object2.type](object2, stream);
  } else {
    streamGeometry(object2, stream);
  }
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/adder.js
function adder_default() {
  return new Adder();
}
function Adder() {
  this.reset();
}
Adder.prototype = {
  constructor: Adder,
  reset: function() {
    this.s = // rounded value
    this.t = 0;
  },
  add: function(y3) {
    add(temp, y3, this.t);
    add(this, temp.s, this.s);
    if (this.s) this.t += temp.t;
    else this.s = temp.t;
  },
  valueOf: function() {
    return this.s;
  }
};
var temp = new Adder();
function add(adder, a2, b2) {
  var x3 = adder.s = a2 + b2, bv = x3 - a2, av = x3 - bv;
  adder.t = a2 - av + (b2 - bv);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/math.js
var epsilon2 = 1e-6;
var epsilon22 = 1e-12;
var pi3 = Math.PI;
var halfPi2 = pi3 / 2;
var quarterPi = pi3 / 4;
var tau2 = pi3 * 2;
var degrees = 180 / pi3;
var radians = pi3 / 180;
var abs2 = Math.abs;
var atan = Math.atan;
var atan22 = Math.atan2;
var cos2 = Math.cos;
var exp = Math.exp;
var log2 = Math.log;
var pow2 = Math.pow;
var sin2 = Math.sin;
var sign = Math.sign || function(x3) {
  return x3 > 0 ? 1 : x3 < 0 ? -1 : 0;
};
var sqrt3 = Math.sqrt;
var tan = Math.tan;
function acos2(x3) {
  return x3 > 1 ? 0 : x3 < -1 ? pi3 : Math.acos(x3);
}
function asin2(x3) {
  return x3 > 1 ? halfPi2 : x3 < -1 ? -halfPi2 : Math.asin(x3);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/noop.js
function noop() {
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/path/area.js
var areaSum = adder_default();
var areaRingSum = adder_default();
var x00;
var y00;
var x0;
var y0;
var areaStream = {
  point: noop,
  lineStart: noop,
  lineEnd: noop,
  polygonStart: function() {
    areaStream.lineStart = areaRingStart;
    areaStream.lineEnd = areaRingEnd;
  },
  polygonEnd: function() {
    areaStream.lineStart = areaStream.lineEnd = areaStream.point = noop;
    areaSum.add(abs2(areaRingSum));
    areaRingSum.reset();
  },
  result: function() {
    var area = areaSum / 2;
    areaSum.reset();
    return area;
  }
};
function areaRingStart() {
  areaStream.point = areaPointFirst;
}
function areaPointFirst(x3, y3) {
  areaStream.point = areaPoint;
  x00 = x0 = x3, y00 = y0 = y3;
}
function areaPoint(x3, y3) {
  areaRingSum.add(y0 * x3 - x0 * y3);
  x0 = x3, y0 = y3;
}
function areaRingEnd() {
  areaPoint(x00, y00);
}
var area_default = areaStream;

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/path/bounds.js
var x02 = Infinity;
var y02 = x02;
var x12 = -x02;
var y12 = x12;
var boundsStream = {
  point: boundsPoint,
  lineStart: noop,
  lineEnd: noop,
  polygonStart: noop,
  polygonEnd: noop,
  result: function() {
    var bounds = [[x02, y02], [x12, y12]];
    x12 = y12 = -(y02 = x02 = Infinity);
    return bounds;
  }
};
function boundsPoint(x3, y3) {
  if (x3 < x02) x02 = x3;
  if (x3 > x12) x12 = x3;
  if (y3 < y02) y02 = y3;
  if (y3 > y12) y12 = y3;
}
var bounds_default = boundsStream;

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/path/centroid.js
var X0 = 0;
var Y0 = 0;
var Z0 = 0;
var X1 = 0;
var Y1 = 0;
var Z1 = 0;
var X2 = 0;
var Y2 = 0;
var Z2 = 0;
var x002;
var y002;
var x03;
var y03;
var centroidStream = {
  point: centroidPoint,
  lineStart: centroidLineStart,
  lineEnd: centroidLineEnd,
  polygonStart: function() {
    centroidStream.lineStart = centroidRingStart;
    centroidStream.lineEnd = centroidRingEnd;
  },
  polygonEnd: function() {
    centroidStream.point = centroidPoint;
    centroidStream.lineStart = centroidLineStart;
    centroidStream.lineEnd = centroidLineEnd;
  },
  result: function() {
    var centroid = Z2 ? [X2 / Z2, Y2 / Z2] : Z1 ? [X1 / Z1, Y1 / Z1] : Z0 ? [X0 / Z0, Y0 / Z0] : [NaN, NaN];
    X0 = Y0 = Z0 = X1 = Y1 = Z1 = X2 = Y2 = Z2 = 0;
    return centroid;
  }
};
function centroidPoint(x3, y3) {
  X0 += x3;
  Y0 += y3;
  ++Z0;
}
function centroidLineStart() {
  centroidStream.point = centroidPointFirstLine;
}
function centroidPointFirstLine(x3, y3) {
  centroidStream.point = centroidPointLine;
  centroidPoint(x03 = x3, y03 = y3);
}
function centroidPointLine(x3, y3) {
  var dx = x3 - x03, dy = y3 - y03, z2 = sqrt3(dx * dx + dy * dy);
  X1 += z2 * (x03 + x3) / 2;
  Y1 += z2 * (y03 + y3) / 2;
  Z1 += z2;
  centroidPoint(x03 = x3, y03 = y3);
}
function centroidLineEnd() {
  centroidStream.point = centroidPoint;
}
function centroidRingStart() {
  centroidStream.point = centroidPointFirstRing;
}
function centroidRingEnd() {
  centroidPointRing(x002, y002);
}
function centroidPointFirstRing(x3, y3) {
  centroidStream.point = centroidPointRing;
  centroidPoint(x002 = x03 = x3, y002 = y03 = y3);
}
function centroidPointRing(x3, y3) {
  var dx = x3 - x03, dy = y3 - y03, z2 = sqrt3(dx * dx + dy * dy);
  X1 += z2 * (x03 + x3) / 2;
  Y1 += z2 * (y03 + y3) / 2;
  Z1 += z2;
  z2 = y03 * x3 - x03 * y3;
  X2 += z2 * (x03 + x3);
  Y2 += z2 * (y03 + y3);
  Z2 += z2 * 3;
  centroidPoint(x03 = x3, y03 = y3);
}
var centroid_default = centroidStream;

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/path/context.js
function PathContext(context) {
  this._context = context;
}
PathContext.prototype = {
  _radius: 4.5,
  pointRadius: function(_2) {
    return this._radius = _2, this;
  },
  polygonStart: function() {
    this._line = 0;
  },
  polygonEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line === 0) this._context.closePath();
    this._point = NaN;
  },
  point: function(x3, y3) {
    switch (this._point) {
      case 0: {
        this._context.moveTo(x3, y3);
        this._point = 1;
        break;
      }
      case 1: {
        this._context.lineTo(x3, y3);
        break;
      }
      default: {
        this._context.moveTo(x3 + this._radius, y3);
        this._context.arc(x3, y3, this._radius, 0, tau2);
        break;
      }
    }
  },
  result: noop
};

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/path/measure.js
var lengthSum = adder_default();
var lengthRing;
var x003;
var y003;
var x04;
var y04;
var lengthStream = {
  point: noop,
  lineStart: function() {
    lengthStream.point = lengthPointFirst;
  },
  lineEnd: function() {
    if (lengthRing) lengthPoint(x003, y003);
    lengthStream.point = noop;
  },
  polygonStart: function() {
    lengthRing = true;
  },
  polygonEnd: function() {
    lengthRing = null;
  },
  result: function() {
    var length = +lengthSum;
    lengthSum.reset();
    return length;
  }
};
function lengthPointFirst(x3, y3) {
  lengthStream.point = lengthPoint;
  x003 = x04 = x3, y003 = y04 = y3;
}
function lengthPoint(x3, y3) {
  x04 -= x3, y04 -= y3;
  lengthSum.add(sqrt3(x04 * x04 + y04 * y04));
  x04 = x3, y04 = y3;
}
var measure_default = lengthStream;

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/path/string.js
function PathString() {
  this._string = [];
}
PathString.prototype = {
  _radius: 4.5,
  _circle: circle(4.5),
  pointRadius: function(_2) {
    if ((_2 = +_2) !== this._radius) this._radius = _2, this._circle = null;
    return this;
  },
  polygonStart: function() {
    this._line = 0;
  },
  polygonEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line === 0) this._string.push("Z");
    this._point = NaN;
  },
  point: function(x3, y3) {
    switch (this._point) {
      case 0: {
        this._string.push("M", x3, ",", y3);
        this._point = 1;
        break;
      }
      case 1: {
        this._string.push("L", x3, ",", y3);
        break;
      }
      default: {
        if (this._circle == null) this._circle = circle(this._radius);
        this._string.push("M", x3, ",", y3, this._circle);
        break;
      }
    }
  },
  result: function() {
    if (this._string.length) {
      var result = this._string.join("");
      this._string = [];
      return result;
    } else {
      return null;
    }
  }
};
function circle(radius) {
  return "m0," + radius + "a" + radius + "," + radius + " 0 1,1 0," + -2 * radius + "a" + radius + "," + radius + " 0 1,1 0," + 2 * radius + "z";
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/path/index.js
function path_default(projection2, context) {
  var pointRadius = 4.5, projectionStream, contextStream;
  function path(object2) {
    if (object2) {
      if (typeof pointRadius === "function") contextStream.pointRadius(+pointRadius.apply(this, arguments));
      stream_default(object2, projectionStream(contextStream));
    }
    return contextStream.result();
  }
  path.area = function(object2) {
    stream_default(object2, projectionStream(area_default));
    return area_default.result();
  };
  path.measure = function(object2) {
    stream_default(object2, projectionStream(measure_default));
    return measure_default.result();
  };
  path.bounds = function(object2) {
    stream_default(object2, projectionStream(bounds_default));
    return bounds_default.result();
  };
  path.centroid = function(object2) {
    stream_default(object2, projectionStream(centroid_default));
    return centroid_default.result();
  };
  path.projection = function(_2) {
    return arguments.length ? (projectionStream = _2 == null ? (projection2 = null, identity_default) : (projection2 = _2).stream, path) : projection2;
  };
  path.context = function(_2) {
    if (!arguments.length) return context;
    contextStream = _2 == null ? (context = null, new PathString()) : new PathContext(context = _2);
    if (typeof pointRadius !== "function") contextStream.pointRadius(pointRadius);
    return path;
  };
  path.pointRadius = function(_2) {
    if (!arguments.length) return pointRadius;
    pointRadius = typeof _2 === "function" ? _2 : (contextStream.pointRadius(+_2), +_2);
    return path;
  };
  return path.projection(projection2).context(context);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/clip/buffer.js
function buffer_default() {
  var lines = [], line;
  return {
    point: function(x3, y3, m2) {
      line.push([x3, y3, m2]);
    },
    lineStart: function() {
      lines.push(line = []);
    },
    lineEnd: noop,
    rejoin: function() {
      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));
    },
    result: function() {
      var result = lines;
      lines = [];
      line = null;
      return result;
    }
  };
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/pointEqual.js
function pointEqual_default(a2, b2) {
  return abs2(a2[0] - b2[0]) < epsilon2 && abs2(a2[1] - b2[1]) < epsilon2;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/clip/rejoin.js
function Intersection(point2, points, other, entry) {
  this.x = point2;
  this.z = points;
  this.o = other;
  this.e = entry;
  this.v = false;
  this.n = this.p = null;
}
function rejoin_default(segments, compareIntersection2, startInside, interpolate2, stream) {
  var subject = [], clip = [], i2, n2;
  segments.forEach(function(segment) {
    if ((n3 = segment.length - 1) <= 0) return;
    var n3, p0 = segment[0], p1 = segment[n3], x3;
    if (pointEqual_default(p0, p1)) {
      if (!p0[2] && !p1[2]) {
        stream.lineStart();
        for (i2 = 0; i2 < n3; ++i2) stream.point((p0 = segment[i2])[0], p0[1]);
        stream.lineEnd();
        return;
      }
      p1[0] += 2 * epsilon2;
    }
    subject.push(x3 = new Intersection(p0, segment, null, true));
    clip.push(x3.o = new Intersection(p0, null, x3, false));
    subject.push(x3 = new Intersection(p1, segment, null, false));
    clip.push(x3.o = new Intersection(p1, null, x3, true));
  });
  if (!subject.length) return;
  clip.sort(compareIntersection2);
  link(subject);
  link(clip);
  for (i2 = 0, n2 = clip.length; i2 < n2; ++i2) {
    clip[i2].e = startInside = !startInside;
  }
  var start = subject[0], points, point2;
  while (1) {
    var current = start, isSubject = true;
    while (current.v) if ((current = current.n) === start) return;
    points = current.z;
    stream.lineStart();
    do {
      current.v = current.o.v = true;
      if (current.e) {
        if (isSubject) {
          for (i2 = 0, n2 = points.length; i2 < n2; ++i2) stream.point((point2 = points[i2])[0], point2[1]);
        } else {
          interpolate2(current.x, current.n.x, 1, stream);
        }
        current = current.n;
      } else {
        if (isSubject) {
          points = current.p.z;
          for (i2 = points.length - 1; i2 >= 0; --i2) stream.point((point2 = points[i2])[0], point2[1]);
        } else {
          interpolate2(current.x, current.p.x, -1, stream);
        }
        current = current.p;
      }
      current = current.o;
      points = current.z;
      isSubject = !isSubject;
    } while (!current.v);
    stream.lineEnd();
  }
}
function link(array3) {
  if (!(n2 = array3.length)) return;
  var n2, i2 = 0, a2 = array3[0], b2;
  while (++i2 < n2) {
    a2.n = b2 = array3[i2];
    b2.p = a2;
    a2 = b2;
  }
  a2.n = b2 = array3[0];
  b2.p = a2;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/cartesian.js
function spherical(cartesian2) {
  return [atan22(cartesian2[1], cartesian2[0]), asin2(cartesian2[2])];
}
function cartesian(spherical2) {
  var lambda = spherical2[0], phi2 = spherical2[1], cosPhi = cos2(phi2);
  return [cosPhi * cos2(lambda), cosPhi * sin2(lambda), sin2(phi2)];
}
function cartesianDot(a2, b2) {
  return a2[0] * b2[0] + a2[1] * b2[1] + a2[2] * b2[2];
}
function cartesianCross(a2, b2) {
  return [a2[1] * b2[2] - a2[2] * b2[1], a2[2] * b2[0] - a2[0] * b2[2], a2[0] * b2[1] - a2[1] * b2[0]];
}
function cartesianAddInPlace(a2, b2) {
  a2[0] += b2[0], a2[1] += b2[1], a2[2] += b2[2];
}
function cartesianScale(vector, k2) {
  return [vector[0] * k2, vector[1] * k2, vector[2] * k2];
}
function cartesianNormalizeInPlace(d2) {
  var l2 = sqrt3(d2[0] * d2[0] + d2[1] * d2[1] + d2[2] * d2[2]);
  d2[0] /= l2, d2[1] /= l2, d2[2] /= l2;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/polygonContains.js
var sum = adder_default();
function longitude(point2) {
  if (abs2(point2[0]) <= pi3)
    return point2[0];
  else
    return sign(point2[0]) * ((abs2(point2[0]) + pi3) % tau2 - pi3);
}
function polygonContains_default(polygon, point2) {
  var lambda = longitude(point2), phi2 = point2[1], sinPhi = sin2(phi2), normal = [sin2(lambda), -cos2(lambda), 0], angle = 0, winding = 0;
  sum.reset();
  if (sinPhi === 1) phi2 = halfPi2 + epsilon2;
  else if (sinPhi === -1) phi2 = -halfPi2 - epsilon2;
  for (var i2 = 0, n2 = polygon.length; i2 < n2; ++i2) {
    if (!(m2 = (ring = polygon[i2]).length)) continue;
    var ring, m2, point0 = ring[m2 - 1], lambda0 = longitude(point0), phi0 = point0[1] / 2 + quarterPi, sinPhi0 = sin2(phi0), cosPhi0 = cos2(phi0);
    for (var j2 = 0; j2 < m2; ++j2, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {
      var point1 = ring[j2], lambda1 = longitude(point1), phi1 = point1[1] / 2 + quarterPi, sinPhi1 = sin2(phi1), cosPhi1 = cos2(phi1), delta = lambda1 - lambda0, sign3 = delta >= 0 ? 1 : -1, absDelta = sign3 * delta, antimeridian = absDelta > pi3, k2 = sinPhi0 * sinPhi1;
      sum.add(atan22(k2 * sign3 * sin2(absDelta), cosPhi0 * cosPhi1 + k2 * cos2(absDelta)));
      angle += antimeridian ? delta + sign3 * tau2 : delta;
      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {
        var arc = cartesianCross(cartesian(point0), cartesian(point1));
        cartesianNormalizeInPlace(arc);
        var intersection = cartesianCross(normal, arc);
        cartesianNormalizeInPlace(intersection);
        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * asin2(intersection[2]);
        if (phi2 > phiArc || phi2 === phiArc && (arc[0] || arc[1])) {
          winding += antimeridian ^ delta >= 0 ? 1 : -1;
        }
      }
    }
  }
  return (angle < -epsilon2 || angle < epsilon2 && sum < -epsilon2) ^ winding & 1;
}

// node_modules/.pnpm/d3-array@1.2.4/node_modules/d3-array/src/ascending.js
function ascending_default(a2, b2) {
  return a2 < b2 ? -1 : a2 > b2 ? 1 : a2 >= b2 ? 0 : NaN;
}

// node_modules/.pnpm/d3-array@1.2.4/node_modules/d3-array/src/bisector.js
function bisector_default(compare2) {
  if (compare2.length === 1) compare2 = ascendingComparator(compare2);
  return {
    left: function(a2, x3, lo, hi) {
      if (lo == null) lo = 0;
      if (hi == null) hi = a2.length;
      while (lo < hi) {
        var mid = lo + hi >>> 1;
        if (compare2(a2[mid], x3) < 0) lo = mid + 1;
        else hi = mid;
      }
      return lo;
    },
    right: function(a2, x3, lo, hi) {
      if (lo == null) lo = 0;
      if (hi == null) hi = a2.length;
      while (lo < hi) {
        var mid = lo + hi >>> 1;
        if (compare2(a2[mid], x3) > 0) hi = mid;
        else lo = mid + 1;
      }
      return lo;
    }
  };
}
function ascendingComparator(f2) {
  return function(d2, x3) {
    return ascending_default(f2(d2), x3);
  };
}

// node_modules/.pnpm/d3-array@1.2.4/node_modules/d3-array/src/bisect.js
var ascendingBisect = bisector_default(ascending_default);
var bisectRight = ascendingBisect.right;
var bisectLeft = ascendingBisect.left;

// node_modules/.pnpm/d3-array@1.2.4/node_modules/d3-array/src/array.js
var array2 = Array.prototype;
var slice = array2.slice;
var map = array2.map;

// node_modules/.pnpm/d3-array@1.2.4/node_modules/d3-array/src/ticks.js
var e103 = Math.sqrt(50);
var e53 = Math.sqrt(10);
var e23 = Math.sqrt(2);

// node_modules/.pnpm/d3-array@1.2.4/node_modules/d3-array/src/merge.js
function merge_default(arrays) {
  var n2 = arrays.length, m2, i2 = -1, j2 = 0, merged, array3;
  while (++i2 < n2) j2 += arrays[i2].length;
  merged = new Array(j2);
  while (--n2 >= 0) {
    array3 = arrays[n2];
    m2 = array3.length;
    while (--m2 >= 0) {
      merged[--j2] = array3[m2];
    }
  }
  return merged;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/clip/index.js
function clip_default(pointVisible, clipLine, interpolate2, start) {
  return function(sink) {
    var line = clipLine(sink), ringBuffer = buffer_default(), ringSink = clipLine(ringBuffer), polygonStarted = false, polygon, segments, ring;
    var clip = {
      point: point2,
      lineStart,
      lineEnd,
      polygonStart: function() {
        clip.point = pointRing;
        clip.lineStart = ringStart;
        clip.lineEnd = ringEnd;
        segments = [];
        polygon = [];
      },
      polygonEnd: function() {
        clip.point = point2;
        clip.lineStart = lineStart;
        clip.lineEnd = lineEnd;
        segments = merge_default(segments);
        var startInside = polygonContains_default(polygon, start);
        if (segments.length) {
          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;
          rejoin_default(segments, compareIntersection, startInside, interpolate2, sink);
        } else if (startInside) {
          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;
          sink.lineStart();
          interpolate2(null, null, 1, sink);
          sink.lineEnd();
        }
        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;
        segments = polygon = null;
      },
      sphere: function() {
        sink.polygonStart();
        sink.lineStart();
        interpolate2(null, null, 1, sink);
        sink.lineEnd();
        sink.polygonEnd();
      }
    };
    function point2(lambda, phi2) {
      if (pointVisible(lambda, phi2)) sink.point(lambda, phi2);
    }
    function pointLine(lambda, phi2) {
      line.point(lambda, phi2);
    }
    function lineStart() {
      clip.point = pointLine;
      line.lineStart();
    }
    function lineEnd() {
      clip.point = point2;
      line.lineEnd();
    }
    function pointRing(lambda, phi2) {
      ring.push([lambda, phi2]);
      ringSink.point(lambda, phi2);
    }
    function ringStart() {
      ringSink.lineStart();
      ring = [];
    }
    function ringEnd() {
      pointRing(ring[0][0], ring[0][1]);
      ringSink.lineEnd();
      var clean = ringSink.clean(), ringSegments = ringBuffer.result(), i2, n2 = ringSegments.length, m2, segment, point3;
      ring.pop();
      polygon.push(ring);
      ring = null;
      if (!n2) return;
      if (clean & 1) {
        segment = ringSegments[0];
        if ((m2 = segment.length - 1) > 0) {
          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;
          sink.lineStart();
          for (i2 = 0; i2 < m2; ++i2) sink.point((point3 = segment[i2])[0], point3[1]);
          sink.lineEnd();
        }
        return;
      }
      if (n2 > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));
      segments.push(ringSegments.filter(validSegment));
    }
    return clip;
  };
}
function validSegment(segment) {
  return segment.length > 1;
}
function compareIntersection(a2, b2) {
  return ((a2 = a2.x)[0] < 0 ? a2[1] - halfPi2 - epsilon2 : halfPi2 - a2[1]) - ((b2 = b2.x)[0] < 0 ? b2[1] - halfPi2 - epsilon2 : halfPi2 - b2[1]);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/clip/antimeridian.js
var antimeridian_default = clip_default(
  function() {
    return true;
  },
  clipAntimeridianLine,
  clipAntimeridianInterpolate,
  [-pi3, -halfPi2]
);
function clipAntimeridianLine(stream) {
  var lambda0 = NaN, phi0 = NaN, sign0 = NaN, clean;
  return {
    lineStart: function() {
      stream.lineStart();
      clean = 1;
    },
    point: function(lambda1, phi1) {
      var sign1 = lambda1 > 0 ? pi3 : -pi3, delta = abs2(lambda1 - lambda0);
      if (abs2(delta - pi3) < epsilon2) {
        stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? halfPi2 : -halfPi2);
        stream.point(sign0, phi0);
        stream.lineEnd();
        stream.lineStart();
        stream.point(sign1, phi0);
        stream.point(lambda1, phi0);
        clean = 0;
      } else if (sign0 !== sign1 && delta >= pi3) {
        if (abs2(lambda0 - sign0) < epsilon2) lambda0 -= sign0 * epsilon2;
        if (abs2(lambda1 - sign1) < epsilon2) lambda1 -= sign1 * epsilon2;
        phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);
        stream.point(sign0, phi0);
        stream.lineEnd();
        stream.lineStart();
        stream.point(sign1, phi0);
        clean = 0;
      }
      stream.point(lambda0 = lambda1, phi0 = phi1);
      sign0 = sign1;
    },
    lineEnd: function() {
      stream.lineEnd();
      lambda0 = phi0 = NaN;
    },
    clean: function() {
      return 2 - clean;
    }
  };
}
function clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {
  var cosPhi0, cosPhi1, sinLambda0Lambda1 = sin2(lambda0 - lambda1);
  return abs2(sinLambda0Lambda1) > epsilon2 ? atan((sin2(phi0) * (cosPhi1 = cos2(phi1)) * sin2(lambda1) - sin2(phi1) * (cosPhi0 = cos2(phi0)) * sin2(lambda0)) / (cosPhi0 * cosPhi1 * sinLambda0Lambda1)) : (phi0 + phi1) / 2;
}
function clipAntimeridianInterpolate(from, to, direction, stream) {
  var phi2;
  if (from == null) {
    phi2 = direction * halfPi2;
    stream.point(-pi3, phi2);
    stream.point(0, phi2);
    stream.point(pi3, phi2);
    stream.point(pi3, 0);
    stream.point(pi3, -phi2);
    stream.point(0, -phi2);
    stream.point(-pi3, -phi2);
    stream.point(-pi3, 0);
    stream.point(-pi3, phi2);
  } else if (abs2(from[0] - to[0]) > epsilon2) {
    var lambda = from[0] < to[0] ? pi3 : -pi3;
    phi2 = direction * lambda / 2;
    stream.point(-lambda, phi2);
    stream.point(0, phi2);
    stream.point(lambda, phi2);
  } else {
    stream.point(to[0], to[1]);
  }
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/compose.js
function compose_default(a2, b2) {
  function compose(x3, y3) {
    return x3 = a2(x3, y3), b2(x3[0], x3[1]);
  }
  if (a2.invert && b2.invert) compose.invert = function(x3, y3) {
    return x3 = b2.invert(x3, y3), x3 && a2.invert(x3[0], x3[1]);
  };
  return compose;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/rotation.js
function rotationIdentity(lambda, phi2) {
  return [abs2(lambda) > pi3 ? lambda + Math.round(-lambda / tau2) * tau2 : lambda, phi2];
}
rotationIdentity.invert = rotationIdentity;
function rotateRadians(deltaLambda, deltaPhi, deltaGamma) {
  return (deltaLambda %= tau2) ? deltaPhi || deltaGamma ? compose_default(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma)) : rotationLambda(deltaLambda) : deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma) : rotationIdentity;
}
function forwardRotationLambda(deltaLambda) {
  return function(lambda, phi2) {
    return lambda += deltaLambda, [lambda > pi3 ? lambda - tau2 : lambda < -pi3 ? lambda + tau2 : lambda, phi2];
  };
}
function rotationLambda(deltaLambda) {
  var rotation = forwardRotationLambda(deltaLambda);
  rotation.invert = forwardRotationLambda(-deltaLambda);
  return rotation;
}
function rotationPhiGamma(deltaPhi, deltaGamma) {
  var cosDeltaPhi = cos2(deltaPhi), sinDeltaPhi = sin2(deltaPhi), cosDeltaGamma = cos2(deltaGamma), sinDeltaGamma = sin2(deltaGamma);
  function rotation(lambda, phi2) {
    var cosPhi = cos2(phi2), x3 = cos2(lambda) * cosPhi, y3 = sin2(lambda) * cosPhi, z2 = sin2(phi2), k2 = z2 * cosDeltaPhi + x3 * sinDeltaPhi;
    return [
      atan22(y3 * cosDeltaGamma - k2 * sinDeltaGamma, x3 * cosDeltaPhi - z2 * sinDeltaPhi),
      asin2(k2 * cosDeltaGamma + y3 * sinDeltaGamma)
    ];
  }
  rotation.invert = function(lambda, phi2) {
    var cosPhi = cos2(phi2), x3 = cos2(lambda) * cosPhi, y3 = sin2(lambda) * cosPhi, z2 = sin2(phi2), k2 = z2 * cosDeltaGamma - y3 * sinDeltaGamma;
    return [
      atan22(y3 * cosDeltaGamma + z2 * sinDeltaGamma, x3 * cosDeltaPhi + k2 * sinDeltaPhi),
      asin2(k2 * cosDeltaPhi - x3 * sinDeltaPhi)
    ];
  };
  return rotation;
}
function rotation_default(rotate) {
  rotate = rotateRadians(rotate[0] * radians, rotate[1] * radians, rotate.length > 2 ? rotate[2] * radians : 0);
  function forward(coordinates) {
    coordinates = rotate(coordinates[0] * radians, coordinates[1] * radians);
    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;
  }
  forward.invert = function(coordinates) {
    coordinates = rotate.invert(coordinates[0] * radians, coordinates[1] * radians);
    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;
  };
  return forward;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/circle.js
function circleStream(stream, radius, delta, direction, t0, t1) {
  if (!delta) return;
  var cosRadius = cos2(radius), sinRadius = sin2(radius), step = direction * delta;
  if (t0 == null) {
    t0 = radius + direction * tau2;
    t1 = radius - step / 2;
  } else {
    t0 = circleRadius(cosRadius, t0);
    t1 = circleRadius(cosRadius, t1);
    if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * tau2;
  }
  for (var point2, t2 = t0; direction > 0 ? t2 > t1 : t2 < t1; t2 -= step) {
    point2 = spherical([cosRadius, -sinRadius * cos2(t2), -sinRadius * sin2(t2)]);
    stream.point(point2[0], point2[1]);
  }
}
function circleRadius(cosRadius, point2) {
  point2 = cartesian(point2), point2[0] -= cosRadius;
  cartesianNormalizeInPlace(point2);
  var radius = acos2(-point2[1]);
  return ((-point2[2] < 0 ? -radius : radius) + tau2 - epsilon2) % tau2;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/clip/circle.js
function circle_default(radius) {
  var cr = cos2(radius), delta = 6 * radians, smallRadius = cr > 0, notHemisphere = abs2(cr) > epsilon2;
  function interpolate2(from, to, direction, stream) {
    circleStream(stream, radius, delta, direction, from, to);
  }
  function visible(lambda, phi2) {
    return cos2(lambda) * cos2(phi2) > cr;
  }
  function clipLine(stream) {
    var point0, c0, v0, v00, clean;
    return {
      lineStart: function() {
        v00 = v0 = false;
        clean = 1;
      },
      point: function(lambda, phi2) {
        var point1 = [lambda, phi2], point2, v2 = visible(lambda, phi2), c2 = smallRadius ? v2 ? 0 : code(lambda, phi2) : v2 ? code(lambda + (lambda < 0 ? pi3 : -pi3), phi2) : 0;
        if (!point0 && (v00 = v0 = v2)) stream.lineStart();
        if (v2 !== v0) {
          point2 = intersect(point0, point1);
          if (!point2 || pointEqual_default(point0, point2) || pointEqual_default(point1, point2))
            point1[2] = 1;
        }
        if (v2 !== v0) {
          clean = 0;
          if (v2) {
            stream.lineStart();
            point2 = intersect(point1, point0);
            stream.point(point2[0], point2[1]);
          } else {
            point2 = intersect(point0, point1);
            stream.point(point2[0], point2[1], 2);
            stream.lineEnd();
          }
          point0 = point2;
        } else if (notHemisphere && point0 && smallRadius ^ v2) {
          var t2;
          if (!(c2 & c0) && (t2 = intersect(point1, point0, true))) {
            clean = 0;
            if (smallRadius) {
              stream.lineStart();
              stream.point(t2[0][0], t2[0][1]);
              stream.point(t2[1][0], t2[1][1]);
              stream.lineEnd();
            } else {
              stream.point(t2[1][0], t2[1][1]);
              stream.lineEnd();
              stream.lineStart();
              stream.point(t2[0][0], t2[0][1], 3);
            }
          }
        }
        if (v2 && (!point0 || !pointEqual_default(point0, point1))) {
          stream.point(point1[0], point1[1]);
        }
        point0 = point1, v0 = v2, c0 = c2;
      },
      lineEnd: function() {
        if (v0) stream.lineEnd();
        point0 = null;
      },
      // Rejoin first and last segments if there were intersections and the first
      // and last points were visible.
      clean: function() {
        return clean | (v00 && v0) << 1;
      }
    };
  }
  function intersect(a2, b2, two) {
    var pa = cartesian(a2), pb = cartesian(b2);
    var n1 = [1, 0, 0], n2 = cartesianCross(pa, pb), n2n2 = cartesianDot(n2, n2), n1n2 = n2[0], determinant = n2n2 - n1n2 * n1n2;
    if (!determinant) return !two && a2;
    var c1 = cr * n2n2 / determinant, c2 = -cr * n1n2 / determinant, n1xn2 = cartesianCross(n1, n2), A5 = cartesianScale(n1, c1), B2 = cartesianScale(n2, c2);
    cartesianAddInPlace(A5, B2);
    var u2 = n1xn2, w2 = cartesianDot(A5, u2), uu = cartesianDot(u2, u2), t2 = w2 * w2 - uu * (cartesianDot(A5, A5) - 1);
    if (t2 < 0) return;
    var t3 = sqrt3(t2), q2 = cartesianScale(u2, (-w2 - t3) / uu);
    cartesianAddInPlace(q2, A5);
    q2 = spherical(q2);
    if (!two) return q2;
    var lambda0 = a2[0], lambda1 = b2[0], phi0 = a2[1], phi1 = b2[1], z2;
    if (lambda1 < lambda0) z2 = lambda0, lambda0 = lambda1, lambda1 = z2;
    var delta2 = lambda1 - lambda0, polar = abs2(delta2 - pi3) < epsilon2, meridian = polar || delta2 < epsilon2;
    if (!polar && phi1 < phi0) z2 = phi0, phi0 = phi1, phi1 = z2;
    if (meridian ? polar ? phi0 + phi1 > 0 ^ q2[1] < (abs2(q2[0] - lambda0) < epsilon2 ? phi0 : phi1) : phi0 <= q2[1] && q2[1] <= phi1 : delta2 > pi3 ^ (lambda0 <= q2[0] && q2[0] <= lambda1)) {
      var q1 = cartesianScale(u2, (-w2 + t3) / uu);
      cartesianAddInPlace(q1, A5);
      return [q2, spherical(q1)];
    }
  }
  function code(lambda, phi2) {
    var r2 = smallRadius ? radius : pi3 - radius, code2 = 0;
    if (lambda < -r2) code2 |= 1;
    else if (lambda > r2) code2 |= 2;
    if (phi2 < -r2) code2 |= 4;
    else if (phi2 > r2) code2 |= 8;
    return code2;
  }
  return clip_default(visible, clipLine, interpolate2, smallRadius ? [0, -radius] : [-pi3, radius - pi3]);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/clip/line.js
function line_default(a2, b2, x05, y05, x13, y13) {
  var ax = a2[0], ay = a2[1], bx = b2[0], by = b2[1], t0 = 0, t1 = 1, dx = bx - ax, dy = by - ay, r2;
  r2 = x05 - ax;
  if (!dx && r2 > 0) return;
  r2 /= dx;
  if (dx < 0) {
    if (r2 < t0) return;
    if (r2 < t1) t1 = r2;
  } else if (dx > 0) {
    if (r2 > t1) return;
    if (r2 > t0) t0 = r2;
  }
  r2 = x13 - ax;
  if (!dx && r2 < 0) return;
  r2 /= dx;
  if (dx < 0) {
    if (r2 > t1) return;
    if (r2 > t0) t0 = r2;
  } else if (dx > 0) {
    if (r2 < t0) return;
    if (r2 < t1) t1 = r2;
  }
  r2 = y05 - ay;
  if (!dy && r2 > 0) return;
  r2 /= dy;
  if (dy < 0) {
    if (r2 < t0) return;
    if (r2 < t1) t1 = r2;
  } else if (dy > 0) {
    if (r2 > t1) return;
    if (r2 > t0) t0 = r2;
  }
  r2 = y13 - ay;
  if (!dy && r2 < 0) return;
  r2 /= dy;
  if (dy < 0) {
    if (r2 > t1) return;
    if (r2 > t0) t0 = r2;
  } else if (dy > 0) {
    if (r2 < t0) return;
    if (r2 < t1) t1 = r2;
  }
  if (t0 > 0) a2[0] = ax + t0 * dx, a2[1] = ay + t0 * dy;
  if (t1 < 1) b2[0] = ax + t1 * dx, b2[1] = ay + t1 * dy;
  return true;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/clip/rectangle.js
var clipMax = 1e9;
var clipMin = -clipMax;
function clipRectangle(x05, y05, x13, y13) {
  function visible(x3, y3) {
    return x05 <= x3 && x3 <= x13 && y05 <= y3 && y3 <= y13;
  }
  function interpolate2(from, to, direction, stream) {
    var a2 = 0, a1 = 0;
    if (from == null || (a2 = corner(from, direction)) !== (a1 = corner(to, direction)) || comparePoint(from, to) < 0 ^ direction > 0) {
      do
        stream.point(a2 === 0 || a2 === 3 ? x05 : x13, a2 > 1 ? y13 : y05);
      while ((a2 = (a2 + direction + 4) % 4) !== a1);
    } else {
      stream.point(to[0], to[1]);
    }
  }
  function corner(p2, direction) {
    return abs2(p2[0] - x05) < epsilon2 ? direction > 0 ? 0 : 3 : abs2(p2[0] - x13) < epsilon2 ? direction > 0 ? 2 : 1 : abs2(p2[1] - y05) < epsilon2 ? direction > 0 ? 1 : 0 : direction > 0 ? 3 : 2;
  }
  function compareIntersection2(a2, b2) {
    return comparePoint(a2.x, b2.x);
  }
  function comparePoint(a2, b2) {
    var ca = corner(a2, 1), cb = corner(b2, 1);
    return ca !== cb ? ca - cb : ca === 0 ? b2[1] - a2[1] : ca === 1 ? a2[0] - b2[0] : ca === 2 ? a2[1] - b2[1] : b2[0] - a2[0];
  }
  return function(stream) {
    var activeStream = stream, bufferStream = buffer_default(), segments, polygon, ring, x__, y__, v__, x_, y_, v_, first, clean;
    var clipStream = {
      point: point2,
      lineStart,
      lineEnd,
      polygonStart,
      polygonEnd
    };
    function point2(x3, y3) {
      if (visible(x3, y3)) activeStream.point(x3, y3);
    }
    function polygonInside() {
      var winding = 0;
      for (var i2 = 0, n2 = polygon.length; i2 < n2; ++i2) {
        for (var ring2 = polygon[i2], j2 = 1, m2 = ring2.length, point3 = ring2[0], a0, a1, b0 = point3[0], b1 = point3[1]; j2 < m2; ++j2) {
          a0 = b0, a1 = b1, point3 = ring2[j2], b0 = point3[0], b1 = point3[1];
          if (a1 <= y13) {
            if (b1 > y13 && (b0 - a0) * (y13 - a1) > (b1 - a1) * (x05 - a0)) ++winding;
          } else {
            if (b1 <= y13 && (b0 - a0) * (y13 - a1) < (b1 - a1) * (x05 - a0)) --winding;
          }
        }
      }
      return winding;
    }
    function polygonStart() {
      activeStream = bufferStream, segments = [], polygon = [], clean = true;
    }
    function polygonEnd() {
      var startInside = polygonInside(), cleanInside = clean && startInside, visible2 = (segments = merge_default(segments)).length;
      if (cleanInside || visible2) {
        stream.polygonStart();
        if (cleanInside) {
          stream.lineStart();
          interpolate2(null, null, 1, stream);
          stream.lineEnd();
        }
        if (visible2) {
          rejoin_default(segments, compareIntersection2, startInside, interpolate2, stream);
        }
        stream.polygonEnd();
      }
      activeStream = stream, segments = polygon = ring = null;
    }
    function lineStart() {
      clipStream.point = linePoint;
      if (polygon) polygon.push(ring = []);
      first = true;
      v_ = false;
      x_ = y_ = NaN;
    }
    function lineEnd() {
      if (segments) {
        linePoint(x__, y__);
        if (v__ && v_) bufferStream.rejoin();
        segments.push(bufferStream.result());
      }
      clipStream.point = point2;
      if (v_) activeStream.lineEnd();
    }
    function linePoint(x3, y3) {
      var v2 = visible(x3, y3);
      if (polygon) ring.push([x3, y3]);
      if (first) {
        x__ = x3, y__ = y3, v__ = v2;
        first = false;
        if (v2) {
          activeStream.lineStart();
          activeStream.point(x3, y3);
        }
      } else {
        if (v2 && v_) activeStream.point(x3, y3);
        else {
          var a2 = [x_ = Math.max(clipMin, Math.min(clipMax, x_)), y_ = Math.max(clipMin, Math.min(clipMax, y_))], b2 = [x3 = Math.max(clipMin, Math.min(clipMax, x3)), y3 = Math.max(clipMin, Math.min(clipMax, y3))];
          if (line_default(a2, b2, x05, y05, x13, y13)) {
            if (!v_) {
              activeStream.lineStart();
              activeStream.point(a2[0], a2[1]);
            }
            activeStream.point(b2[0], b2[1]);
            if (!v2) activeStream.lineEnd();
            clean = false;
          } else if (v2) {
            activeStream.lineStart();
            activeStream.point(x3, y3);
            clean = false;
          }
        }
      }
      x_ = x3, y_ = y3, v_ = v2;
    }
    return clipStream;
  };
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/transform.js
function transformer(methods) {
  return function(stream) {
    var s2 = new TransformStream();
    for (var key in methods) s2[key] = methods[key];
    s2.stream = stream;
    return s2;
  };
}
function TransformStream() {
}
TransformStream.prototype = {
  constructor: TransformStream,
  point: function(x3, y3) {
    this.stream.point(x3, y3);
  },
  sphere: function() {
    this.stream.sphere();
  },
  lineStart: function() {
    this.stream.lineStart();
  },
  lineEnd: function() {
    this.stream.lineEnd();
  },
  polygonStart: function() {
    this.stream.polygonStart();
  },
  polygonEnd: function() {
    this.stream.polygonEnd();
  }
};

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/fit.js
function fit(projection2, fitBounds, object2) {
  var clip = projection2.clipExtent && projection2.clipExtent();
  projection2.scale(150).translate([0, 0]);
  if (clip != null) projection2.clipExtent(null);
  stream_default(object2, projection2.stream(bounds_default));
  fitBounds(bounds_default.result());
  if (clip != null) projection2.clipExtent(clip);
  return projection2;
}
function fitExtent(projection2, extent2, object2) {
  return fit(projection2, function(b2) {
    var w2 = extent2[1][0] - extent2[0][0], h2 = extent2[1][1] - extent2[0][1], k2 = Math.min(w2 / (b2[1][0] - b2[0][0]), h2 / (b2[1][1] - b2[0][1])), x3 = +extent2[0][0] + (w2 - k2 * (b2[1][0] + b2[0][0])) / 2, y3 = +extent2[0][1] + (h2 - k2 * (b2[1][1] + b2[0][1])) / 2;
    projection2.scale(150 * k2).translate([x3, y3]);
  }, object2);
}
function fitSize(projection2, size, object2) {
  return fitExtent(projection2, [[0, 0], size], object2);
}
function fitWidth(projection2, width, object2) {
  return fit(projection2, function(b2) {
    var w2 = +width, k2 = w2 / (b2[1][0] - b2[0][0]), x3 = (w2 - k2 * (b2[1][0] + b2[0][0])) / 2, y3 = -k2 * b2[0][1];
    projection2.scale(150 * k2).translate([x3, y3]);
  }, object2);
}
function fitHeight(projection2, height, object2) {
  return fit(projection2, function(b2) {
    var h2 = +height, k2 = h2 / (b2[1][1] - b2[0][1]), x3 = -k2 * b2[0][0], y3 = (h2 - k2 * (b2[1][1] + b2[0][1])) / 2;
    projection2.scale(150 * k2).translate([x3, y3]);
  }, object2);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/resample.js
var maxDepth = 16;
var cosMinDistance = cos2(30 * radians);
function resample_default(project, delta2) {
  return +delta2 ? resample(project, delta2) : resampleNone(project);
}
function resampleNone(project) {
  return transformer({
    point: function(x3, y3) {
      x3 = project(x3, y3);
      this.stream.point(x3[0], x3[1]);
    }
  });
}
function resample(project, delta2) {
  function resampleLineTo(x05, y05, lambda0, a0, b0, c0, x13, y13, lambda1, a1, b1, c1, depth, stream) {
    var dx = x13 - x05, dy = y13 - y05, d2 = dx * dx + dy * dy;
    if (d2 > 4 * delta2 && depth--) {
      var a2 = a0 + a1, b2 = b0 + b1, c2 = c0 + c1, m2 = sqrt3(a2 * a2 + b2 * b2 + c2 * c2), phi2 = asin2(c2 /= m2), lambda2 = abs2(abs2(c2) - 1) < epsilon2 || abs2(lambda0 - lambda1) < epsilon2 ? (lambda0 + lambda1) / 2 : atan22(b2, a2), p2 = project(lambda2, phi2), x22 = p2[0], y22 = p2[1], dx2 = x22 - x05, dy2 = y22 - y05, dz = dy * dx2 - dx * dy2;
      if (dz * dz / d2 > delta2 || abs2((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) {
        resampleLineTo(x05, y05, lambda0, a0, b0, c0, x22, y22, lambda2, a2 /= m2, b2 /= m2, c2, depth, stream);
        stream.point(x22, y22);
        resampleLineTo(x22, y22, lambda2, a2, b2, c2, x13, y13, lambda1, a1, b1, c1, depth, stream);
      }
    }
  }
  return function(stream) {
    var lambda00, x004, y004, a00, b00, c00, lambda0, x05, y05, a0, b0, c0;
    var resampleStream = {
      point: point2,
      lineStart,
      lineEnd,
      polygonStart: function() {
        stream.polygonStart();
        resampleStream.lineStart = ringStart;
      },
      polygonEnd: function() {
        stream.polygonEnd();
        resampleStream.lineStart = lineStart;
      }
    };
    function point2(x3, y3) {
      x3 = project(x3, y3);
      stream.point(x3[0], x3[1]);
    }
    function lineStart() {
      x05 = NaN;
      resampleStream.point = linePoint;
      stream.lineStart();
    }
    function linePoint(lambda, phi2) {
      var c2 = cartesian([lambda, phi2]), p2 = project(lambda, phi2);
      resampleLineTo(x05, y05, lambda0, a0, b0, c0, x05 = p2[0], y05 = p2[1], lambda0 = lambda, a0 = c2[0], b0 = c2[1], c0 = c2[2], maxDepth, stream);
      stream.point(x05, y05);
    }
    function lineEnd() {
      resampleStream.point = point2;
      stream.lineEnd();
    }
    function ringStart() {
      lineStart();
      resampleStream.point = ringPoint;
      resampleStream.lineEnd = ringEnd;
    }
    function ringPoint(lambda, phi2) {
      linePoint(lambda00 = lambda, phi2), x004 = x05, y004 = y05, a00 = a0, b00 = b0, c00 = c0;
      resampleStream.point = linePoint;
    }
    function ringEnd() {
      resampleLineTo(x05, y05, lambda0, a0, b0, c0, x004, y004, lambda00, a00, b00, c00, maxDepth, stream);
      resampleStream.lineEnd = lineEnd;
      lineEnd();
    }
    return resampleStream;
  };
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/index.js
var transformRadians = transformer({
  point: function(x3, y3) {
    this.stream.point(x3 * radians, y3 * radians);
  }
});
function transformRotate(rotate) {
  return transformer({
    point: function(x3, y3) {
      var r2 = rotate(x3, y3);
      return this.stream.point(r2[0], r2[1]);
    }
  });
}
function scaleTranslate(k2, dx, dy, sx, sy) {
  function transform(x3, y3) {
    x3 *= sx;
    y3 *= sy;
    return [dx + k2 * x3, dy - k2 * y3];
  }
  transform.invert = function(x3, y3) {
    return [(x3 - dx) / k2 * sx, (dy - y3) / k2 * sy];
  };
  return transform;
}
function scaleTranslateRotate(k2, dx, dy, sx, sy, alpha) {
  var cosAlpha = cos2(alpha), sinAlpha = sin2(alpha), a2 = cosAlpha * k2, b2 = sinAlpha * k2, ai = cosAlpha / k2, bi = sinAlpha / k2, ci = (sinAlpha * dy - cosAlpha * dx) / k2, fi = (sinAlpha * dx + cosAlpha * dy) / k2;
  function transform(x3, y3) {
    x3 *= sx;
    y3 *= sy;
    return [a2 * x3 - b2 * y3 + dx, dy - b2 * x3 - a2 * y3];
  }
  transform.invert = function(x3, y3) {
    return [sx * (ai * x3 - bi * y3 + ci), sy * (fi - bi * x3 - ai * y3)];
  };
  return transform;
}
function projection(project) {
  return projectionMutator(function() {
    return project;
  })();
}
function projectionMutator(projectAt) {
  var project, k2 = 150, x3 = 480, y3 = 250, lambda = 0, phi2 = 0, deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, alpha = 0, sx = 1, sy = 1, theta = null, preclip = antimeridian_default, x05 = null, y05, x13, y13, postclip = identity_default, delta2 = 0.5, projectResample, projectTransform, projectRotateTransform, cache, cacheStream;
  function projection2(point2) {
    return projectRotateTransform(point2[0] * radians, point2[1] * radians);
  }
  function invert(point2) {
    point2 = projectRotateTransform.invert(point2[0], point2[1]);
    return point2 && [point2[0] * degrees, point2[1] * degrees];
  }
  projection2.stream = function(stream) {
    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));
  };
  projection2.preclip = function(_2) {
    return arguments.length ? (preclip = _2, theta = void 0, reset()) : preclip;
  };
  projection2.postclip = function(_2) {
    return arguments.length ? (postclip = _2, x05 = y05 = x13 = y13 = null, reset()) : postclip;
  };
  projection2.clipAngle = function(_2) {
    return arguments.length ? (preclip = +_2 ? circle_default(theta = _2 * radians) : (theta = null, antimeridian_default), reset()) : theta * degrees;
  };
  projection2.clipExtent = function(_2) {
    return arguments.length ? (postclip = _2 == null ? (x05 = y05 = x13 = y13 = null, identity_default) : clipRectangle(x05 = +_2[0][0], y05 = +_2[0][1], x13 = +_2[1][0], y13 = +_2[1][1]), reset()) : x05 == null ? null : [[x05, y05], [x13, y13]];
  };
  projection2.scale = function(_2) {
    return arguments.length ? (k2 = +_2, recenter()) : k2;
  };
  projection2.translate = function(_2) {
    return arguments.length ? (x3 = +_2[0], y3 = +_2[1], recenter()) : [x3, y3];
  };
  projection2.center = function(_2) {
    return arguments.length ? (lambda = _2[0] % 360 * radians, phi2 = _2[1] % 360 * radians, recenter()) : [lambda * degrees, phi2 * degrees];
  };
  projection2.rotate = function(_2) {
    return arguments.length ? (deltaLambda = _2[0] % 360 * radians, deltaPhi = _2[1] % 360 * radians, deltaGamma = _2.length > 2 ? _2[2] % 360 * radians : 0, recenter()) : [deltaLambda * degrees, deltaPhi * degrees, deltaGamma * degrees];
  };
  projection2.angle = function(_2) {
    return arguments.length ? (alpha = _2 % 360 * radians, recenter()) : alpha * degrees;
  };
  projection2.reflectX = function(_2) {
    return arguments.length ? (sx = _2 ? -1 : 1, recenter()) : sx < 0;
  };
  projection2.reflectY = function(_2) {
    return arguments.length ? (sy = _2 ? -1 : 1, recenter()) : sy < 0;
  };
  projection2.precision = function(_2) {
    return arguments.length ? (projectResample = resample_default(projectTransform, delta2 = _2 * _2), reset()) : sqrt3(delta2);
  };
  projection2.fitExtent = function(extent2, object2) {
    return fitExtent(projection2, extent2, object2);
  };
  projection2.fitSize = function(size, object2) {
    return fitSize(projection2, size, object2);
  };
  projection2.fitWidth = function(width, object2) {
    return fitWidth(projection2, width, object2);
  };
  projection2.fitHeight = function(height, object2) {
    return fitHeight(projection2, height, object2);
  };
  function recenter() {
    var center = scaleTranslateRotate(k2, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi2)), transform = (alpha ? scaleTranslateRotate : scaleTranslate)(k2, x3 - center[0], y3 - center[1], sx, sy, alpha);
    rotate = rotateRadians(deltaLambda, deltaPhi, deltaGamma);
    projectTransform = compose_default(project, transform);
    projectRotateTransform = compose_default(rotate, projectTransform);
    projectResample = resample_default(projectTransform, delta2);
    return reset();
  }
  function reset() {
    cache = cacheStream = null;
    return projection2;
  }
  return function() {
    project = projectAt.apply(this, arguments);
    projection2.invert = project.invert && invert;
    return recenter();
  };
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/conic.js
function conicProjection(projectAt) {
  var phi0 = 0, phi1 = pi3 / 3, m2 = projectionMutator(projectAt), p2 = m2(phi0, phi1);
  p2.parallels = function(_2) {
    return arguments.length ? m2(phi0 = _2[0] * radians, phi1 = _2[1] * radians) : [phi0 * degrees, phi1 * degrees];
  };
  return p2;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/cylindricalEqualArea.js
function cylindricalEqualAreaRaw(phi0) {
  var cosPhi0 = cos2(phi0);
  function forward(lambda, phi2) {
    return [lambda * cosPhi0, sin2(phi2) / cosPhi0];
  }
  forward.invert = function(x3, y3) {
    return [x3 / cosPhi0, asin2(y3 * cosPhi0)];
  };
  return forward;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/conicEqualArea.js
function conicEqualAreaRaw(y05, y13) {
  var sy0 = sin2(y05), n2 = (sy0 + sin2(y13)) / 2;
  if (abs2(n2) < epsilon2) return cylindricalEqualAreaRaw(y05);
  var c2 = 1 + sy0 * (2 * n2 - sy0), r0 = sqrt3(c2) / n2;
  function project(x3, y3) {
    var r2 = sqrt3(c2 - 2 * n2 * sin2(y3)) / n2;
    return [r2 * sin2(x3 *= n2), r0 - r2 * cos2(x3)];
  }
  project.invert = function(x3, y3) {
    var r0y = r0 - y3, l2 = atan22(x3, abs2(r0y)) * sign(r0y);
    if (r0y * n2 < 0)
      l2 -= pi3 * sign(x3) * sign(r0y);
    return [l2 / n2, asin2((c2 - (x3 * x3 + r0y * r0y) * n2 * n2) / (2 * n2))];
  };
  return project;
}
function conicEqualArea_default() {
  return conicProjection(conicEqualAreaRaw).scale(155.424).center([0, 33.6442]);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/albers.js
function albers_default() {
  return conicEqualArea_default().parallels([29.5, 45.5]).scale(1070).translate([480, 250]).rotate([96, 0]).center([-0.6, 38.7]);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/albersUsa.js
function multiplex(streams) {
  var n2 = streams.length;
  return {
    point: function(x3, y3) {
      var i2 = -1;
      while (++i2 < n2) streams[i2].point(x3, y3);
    },
    sphere: function() {
      var i2 = -1;
      while (++i2 < n2) streams[i2].sphere();
    },
    lineStart: function() {
      var i2 = -1;
      while (++i2 < n2) streams[i2].lineStart();
    },
    lineEnd: function() {
      var i2 = -1;
      while (++i2 < n2) streams[i2].lineEnd();
    },
    polygonStart: function() {
      var i2 = -1;
      while (++i2 < n2) streams[i2].polygonStart();
    },
    polygonEnd: function() {
      var i2 = -1;
      while (++i2 < n2) streams[i2].polygonEnd();
    }
  };
}
function albersUsa_default() {
  var cache, cacheStream, lower48 = albers_default(), lower48Point, alaska = conicEqualArea_default().rotate([154, 0]).center([-2, 58.5]).parallels([55, 65]), alaskaPoint, hawaii = conicEqualArea_default().rotate([157, 0]).center([-3, 19.9]).parallels([8, 18]), hawaiiPoint, point2, pointStream = { point: function(x3, y3) {
    point2 = [x3, y3];
  } };
  function albersUsa(coordinates) {
    var x3 = coordinates[0], y3 = coordinates[1];
    return point2 = null, (lower48Point.point(x3, y3), point2) || (alaskaPoint.point(x3, y3), point2) || (hawaiiPoint.point(x3, y3), point2);
  }
  albersUsa.invert = function(coordinates) {
    var k2 = lower48.scale(), t2 = lower48.translate(), x3 = (coordinates[0] - t2[0]) / k2, y3 = (coordinates[1] - t2[1]) / k2;
    return (y3 >= 0.12 && y3 < 0.234 && x3 >= -0.425 && x3 < -0.214 ? alaska : y3 >= 0.166 && y3 < 0.234 && x3 >= -0.214 && x3 < -0.115 ? hawaii : lower48).invert(coordinates);
  };
  albersUsa.stream = function(stream) {
    return cache && cacheStream === stream ? cache : cache = multiplex([lower48.stream(cacheStream = stream), alaska.stream(stream), hawaii.stream(stream)]);
  };
  albersUsa.precision = function(_2) {
    if (!arguments.length) return lower48.precision();
    lower48.precision(_2), alaska.precision(_2), hawaii.precision(_2);
    return reset();
  };
  albersUsa.scale = function(_2) {
    if (!arguments.length) return lower48.scale();
    lower48.scale(_2), alaska.scale(_2 * 0.35), hawaii.scale(_2);
    return albersUsa.translate(lower48.translate());
  };
  albersUsa.translate = function(_2) {
    if (!arguments.length) return lower48.translate();
    var k2 = lower48.scale(), x3 = +_2[0], y3 = +_2[1];
    lower48Point = lower48.translate(_2).clipExtent([[x3 - 0.455 * k2, y3 - 0.238 * k2], [x3 + 0.455 * k2, y3 + 0.238 * k2]]).stream(pointStream);
    alaskaPoint = alaska.translate([x3 - 0.307 * k2, y3 + 0.201 * k2]).clipExtent([[x3 - 0.425 * k2 + epsilon2, y3 + 0.12 * k2 + epsilon2], [x3 - 0.214 * k2 - epsilon2, y3 + 0.234 * k2 - epsilon2]]).stream(pointStream);
    hawaiiPoint = hawaii.translate([x3 - 0.205 * k2, y3 + 0.212 * k2]).clipExtent([[x3 - 0.214 * k2 + epsilon2, y3 + 0.166 * k2 + epsilon2], [x3 - 0.115 * k2 - epsilon2, y3 + 0.234 * k2 - epsilon2]]).stream(pointStream);
    return reset();
  };
  albersUsa.fitExtent = function(extent2, object2) {
    return fitExtent(albersUsa, extent2, object2);
  };
  albersUsa.fitSize = function(size, object2) {
    return fitSize(albersUsa, size, object2);
  };
  albersUsa.fitWidth = function(width, object2) {
    return fitWidth(albersUsa, width, object2);
  };
  albersUsa.fitHeight = function(height, object2) {
    return fitHeight(albersUsa, height, object2);
  };
  function reset() {
    cache = cacheStream = null;
    return albersUsa;
  }
  return albersUsa.scale(1070);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/azimuthal.js
function azimuthalRaw(scale2) {
  return function(x3, y3) {
    var cx = cos2(x3), cy = cos2(y3), k2 = scale2(cx * cy);
    return [
      k2 * cy * sin2(x3),
      k2 * sin2(y3)
    ];
  };
}
function azimuthalInvert(angle) {
  return function(x3, y3) {
    var z2 = sqrt3(x3 * x3 + y3 * y3), c2 = angle(z2), sc = sin2(c2), cc = cos2(c2);
    return [
      atan22(x3 * sc, z2 * cc),
      asin2(z2 && y3 * sc / z2)
    ];
  };
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/azimuthalEqualArea.js
var azimuthalEqualAreaRaw = azimuthalRaw(function(cxcy) {
  return sqrt3(2 / (1 + cxcy));
});
azimuthalEqualAreaRaw.invert = azimuthalInvert(function(z2) {
  return 2 * asin2(z2 / 2);
});
function azimuthalEqualArea_default() {
  return projection(azimuthalEqualAreaRaw).scale(124.75).clipAngle(180 - 1e-3);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/azimuthalEquidistant.js
var azimuthalEquidistantRaw = azimuthalRaw(function(c2) {
  return (c2 = acos2(c2)) && c2 / sin2(c2);
});
azimuthalEquidistantRaw.invert = azimuthalInvert(function(z2) {
  return z2;
});
function azimuthalEquidistant_default() {
  return projection(azimuthalEquidistantRaw).scale(79.4188).clipAngle(180 - 1e-3);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/mercator.js
function mercatorRaw(lambda, phi2) {
  return [lambda, log2(tan((halfPi2 + phi2) / 2))];
}
mercatorRaw.invert = function(x3, y3) {
  return [x3, 2 * atan(exp(y3)) - halfPi2];
};
function mercator_default() {
  return mercatorProjection(mercatorRaw).scale(961 / tau2);
}
function mercatorProjection(project) {
  var m2 = projection(project), center = m2.center, scale2 = m2.scale, translate = m2.translate, clipExtent = m2.clipExtent, x05 = null, y05, x13, y13;
  m2.scale = function(_2) {
    return arguments.length ? (scale2(_2), reclip()) : scale2();
  };
  m2.translate = function(_2) {
    return arguments.length ? (translate(_2), reclip()) : translate();
  };
  m2.center = function(_2) {
    return arguments.length ? (center(_2), reclip()) : center();
  };
  m2.clipExtent = function(_2) {
    return arguments.length ? (_2 == null ? x05 = y05 = x13 = y13 = null : (x05 = +_2[0][0], y05 = +_2[0][1], x13 = +_2[1][0], y13 = +_2[1][1]), reclip()) : x05 == null ? null : [[x05, y05], [x13, y13]];
  };
  function reclip() {
    var k2 = pi3 * scale2(), t2 = m2(rotation_default(m2.rotate()).invert([0, 0]));
    return clipExtent(x05 == null ? [[t2[0] - k2, t2[1] - k2], [t2[0] + k2, t2[1] + k2]] : project === mercatorRaw ? [[Math.max(t2[0] - k2, x05), y05], [Math.min(t2[0] + k2, x13), y13]] : [[x05, Math.max(t2[1] - k2, y05)], [x13, Math.min(t2[1] + k2, y13)]]);
  }
  return reclip();
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/conicConformal.js
function tany(y3) {
  return tan((halfPi2 + y3) / 2);
}
function conicConformalRaw(y05, y13) {
  var cy0 = cos2(y05), n2 = y05 === y13 ? sin2(y05) : log2(cy0 / cos2(y13)) / log2(tany(y13) / tany(y05)), f2 = cy0 * pow2(tany(y05), n2) / n2;
  if (!n2) return mercatorRaw;
  function project(x3, y3) {
    if (f2 > 0) {
      if (y3 < -halfPi2 + epsilon2) y3 = -halfPi2 + epsilon2;
    } else {
      if (y3 > halfPi2 - epsilon2) y3 = halfPi2 - epsilon2;
    }
    var r2 = f2 / pow2(tany(y3), n2);
    return [r2 * sin2(n2 * x3), f2 - r2 * cos2(n2 * x3)];
  }
  project.invert = function(x3, y3) {
    var fy = f2 - y3, r2 = sign(n2) * sqrt3(x3 * x3 + fy * fy), l2 = atan22(x3, abs2(fy)) * sign(fy);
    if (fy * n2 < 0)
      l2 -= pi3 * sign(x3) * sign(fy);
    return [l2 / n2, 2 * atan(pow2(f2 / r2, 1 / n2)) - halfPi2];
  };
  return project;
}
function conicConformal_default() {
  return conicProjection(conicConformalRaw).scale(109.5).parallels([30, 30]);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/equirectangular.js
function equirectangularRaw(lambda, phi2) {
  return [lambda, phi2];
}
equirectangularRaw.invert = equirectangularRaw;
function equirectangular_default() {
  return projection(equirectangularRaw).scale(152.63);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/conicEquidistant.js
function conicEquidistantRaw(y05, y13) {
  var cy0 = cos2(y05), n2 = y05 === y13 ? sin2(y05) : (cy0 - cos2(y13)) / (y13 - y05), g3 = cy0 / n2 + y05;
  if (abs2(n2) < epsilon2) return equirectangularRaw;
  function project(x3, y3) {
    var gy = g3 - y3, nx = n2 * x3;
    return [gy * sin2(nx), g3 - gy * cos2(nx)];
  }
  project.invert = function(x3, y3) {
    var gy = g3 - y3, l2 = atan22(x3, abs2(gy)) * sign(gy);
    if (gy * n2 < 0)
      l2 -= pi3 * sign(x3) * sign(gy);
    return [l2 / n2, g3 - sign(n2) * sqrt3(x3 * x3 + gy * gy)];
  };
  return project;
}
function conicEquidistant_default() {
  return conicProjection(conicEquidistantRaw).scale(131.154).center([0, 13.9389]);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/equalEarth.js
var A1 = 1.340264;
var A2 = -0.081106;
var A3 = 893e-6;
var A4 = 3796e-6;
var M = sqrt3(3) / 2;
var iterations = 12;
function equalEarthRaw(lambda, phi2) {
  var l2 = asin2(M * sin2(phi2)), l22 = l2 * l2, l6 = l22 * l22 * l22;
  return [
    lambda * cos2(l2) / (M * (A1 + 3 * A2 * l22 + l6 * (7 * A3 + 9 * A4 * l22))),
    l2 * (A1 + A2 * l22 + l6 * (A3 + A4 * l22))
  ];
}
equalEarthRaw.invert = function(x3, y3) {
  var l2 = y3, l22 = l2 * l2, l6 = l22 * l22 * l22;
  for (var i2 = 0, delta, fy, fpy; i2 < iterations; ++i2) {
    fy = l2 * (A1 + A2 * l22 + l6 * (A3 + A4 * l22)) - y3;
    fpy = A1 + 3 * A2 * l22 + l6 * (7 * A3 + 9 * A4 * l22);
    l2 -= delta = fy / fpy, l22 = l2 * l2, l6 = l22 * l22 * l22;
    if (abs2(delta) < epsilon22) break;
  }
  return [
    M * x3 * (A1 + 3 * A2 * l22 + l6 * (7 * A3 + 9 * A4 * l22)) / cos2(l2),
    asin2(sin2(l2) / M)
  ];
};
function equalEarth_default() {
  return projection(equalEarthRaw).scale(177.158);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/gnomonic.js
function gnomonicRaw(x3, y3) {
  var cy = cos2(y3), k2 = cos2(x3) * cy;
  return [cy * sin2(x3) / k2, sin2(y3) / k2];
}
gnomonicRaw.invert = azimuthalInvert(atan);
function gnomonic_default() {
  return projection(gnomonicRaw).scale(144.049).clipAngle(60);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/identity.js
function identity_default3() {
  var k2 = 1, tx = 0, ty = 0, sx = 1, sy = 1, alpha = 0, ca, sa, x05 = null, y05, x13, y13, kx = 1, ky = 1, transform = transformer({
    point: function(x3, y3) {
      var p2 = projection2([x3, y3]);
      this.stream.point(p2[0], p2[1]);
    }
  }), postclip = identity_default, cache, cacheStream;
  function reset() {
    kx = k2 * sx;
    ky = k2 * sy;
    cache = cacheStream = null;
    return projection2;
  }
  function projection2(p2) {
    var x3 = p2[0] * kx, y3 = p2[1] * ky;
    if (alpha) {
      var t2 = y3 * ca - x3 * sa;
      x3 = x3 * ca + y3 * sa;
      y3 = t2;
    }
    return [x3 + tx, y3 + ty];
  }
  projection2.invert = function(p2) {
    var x3 = p2[0] - tx, y3 = p2[1] - ty;
    if (alpha) {
      var t2 = y3 * ca + x3 * sa;
      x3 = x3 * ca - y3 * sa;
      y3 = t2;
    }
    return [x3 / kx, y3 / ky];
  };
  projection2.stream = function(stream) {
    return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));
  };
  projection2.postclip = function(_2) {
    return arguments.length ? (postclip = _2, x05 = y05 = x13 = y13 = null, reset()) : postclip;
  };
  projection2.clipExtent = function(_2) {
    return arguments.length ? (postclip = _2 == null ? (x05 = y05 = x13 = y13 = null, identity_default) : clipRectangle(x05 = +_2[0][0], y05 = +_2[0][1], x13 = +_2[1][0], y13 = +_2[1][1]), reset()) : x05 == null ? null : [[x05, y05], [x13, y13]];
  };
  projection2.scale = function(_2) {
    return arguments.length ? (k2 = +_2, reset()) : k2;
  };
  projection2.translate = function(_2) {
    return arguments.length ? (tx = +_2[0], ty = +_2[1], reset()) : [tx, ty];
  };
  projection2.angle = function(_2) {
    return arguments.length ? (alpha = _2 % 360 * radians, sa = sin2(alpha), ca = cos2(alpha), reset()) : alpha * degrees;
  };
  projection2.reflectX = function(_2) {
    return arguments.length ? (sx = _2 ? -1 : 1, reset()) : sx < 0;
  };
  projection2.reflectY = function(_2) {
    return arguments.length ? (sy = _2 ? -1 : 1, reset()) : sy < 0;
  };
  projection2.fitExtent = function(extent2, object2) {
    return fitExtent(projection2, extent2, object2);
  };
  projection2.fitSize = function(size, object2) {
    return fitSize(projection2, size, object2);
  };
  projection2.fitWidth = function(width, object2) {
    return fitWidth(projection2, width, object2);
  };
  projection2.fitHeight = function(height, object2) {
    return fitHeight(projection2, height, object2);
  };
  return projection2;
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/naturalEarth1.js
function naturalEarth1Raw(lambda, phi2) {
  var phi22 = phi2 * phi2, phi4 = phi22 * phi22;
  return [
    lambda * (0.8707 - 0.131979 * phi22 + phi4 * (-0.013791 + phi4 * (3971e-6 * phi22 - 1529e-6 * phi4))),
    phi2 * (1.007226 + phi22 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi22 - 5916e-6 * phi4)))
  ];
}
naturalEarth1Raw.invert = function(x3, y3) {
  var phi2 = y3, i2 = 25, delta;
  do {
    var phi22 = phi2 * phi2, phi4 = phi22 * phi22;
    phi2 -= delta = (phi2 * (1.007226 + phi22 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi22 - 5916e-6 * phi4))) - y3) / (1.007226 + phi22 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi22 - 5916e-6 * 11 * phi4)));
  } while (abs2(delta) > epsilon2 && --i2 > 0);
  return [
    x3 / (0.8707 + (phi22 = phi2 * phi2) * (-0.131979 + phi22 * (-0.013791 + phi22 * phi22 * phi22 * (3971e-6 - 1529e-6 * phi22)))),
    phi2
  ];
};
function naturalEarth1_default() {
  return projection(naturalEarth1Raw).scale(175.295);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/orthographic.js
function orthographicRaw(x3, y3) {
  return [cos2(y3) * sin2(x3), sin2(y3)];
}
orthographicRaw.invert = azimuthalInvert(asin2);
function orthographic_default() {
  return projection(orthographicRaw).scale(249.5).clipAngle(90 + epsilon2);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/stereographic.js
function stereographicRaw(x3, y3) {
  var cy = cos2(y3), k2 = 1 + cos2(x3) * cy;
  return [cy * sin2(x3) / k2, sin2(y3) / k2];
}
stereographicRaw.invert = azimuthalInvert(function(z2) {
  return 2 * atan(z2);
});
function stereographic_default() {
  return projection(stereographicRaw).scale(250).clipAngle(142);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/projection/transverseMercator.js
function transverseMercatorRaw(lambda, phi2) {
  return [log2(tan((halfPi2 + phi2) / 2)), -lambda];
}
transverseMercatorRaw.invert = function(x3, y3) {
  return [-y3, 2 * atan(exp(x3)) - halfPi2];
};
function transverseMercator_default() {
  var m2 = mercatorProjection(transverseMercatorRaw), center = m2.center, rotate = m2.rotate;
  m2.center = function(_2) {
    return arguments.length ? center([-_2[1], _2[0]]) : (_2 = center(), [_2[1], -_2[0]]);
  };
  m2.rotate = function(_2) {
    return arguments.length ? rotate([_2[0], _2[1], _2.length > 2 ? _2[2] + 90 : 90]) : (_2 = rotate(), [_2[0], _2[1], _2[2] - 90]);
  };
  return rotate([0, 0, 90]).scale(159.155);
}

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/area.js
var areaRingSum2 = adder_default();
var areaSum2 = adder_default();

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/bounds.js
var deltaSum = adder_default();

// node_modules/.pnpm/d3-geo@1.12.1/node_modules/d3-geo/src/length.js
var lengthSum2 = adder_default();

// node_modules/.pnpm/simple-statistics@7.8.8/node_modules/simple-statistics/dist/simple-statistics.mjs
var simple_statistics_exports = {};
__export(simple_statistics_exports, {
  BayesianClassifier: () => BayesianClassifier,
  PerceptronModel: () => PerceptronModel,
  addToMean: () => addToMean,
  approxEqual: () => approxEqual,
  average: () => mean,
  averageSimple: () => meanSimple,
  bayesian: () => BayesianClassifier,
  bernoulliDistribution: () => bernoulliDistribution,
  binomialDistribution: () => binomialDistribution,
  bisect: () => bisect2,
  chiSquaredDistributionTable: () => chiSquaredDistributionTable,
  chiSquaredGoodnessOfFit: () => chiSquaredGoodnessOfFit,
  chunk: () => chunk,
  ckmeans: () => ckmeans,
  coefficientOfVariation: () => coefficientOfVariation,
  combinations: () => combinations,
  combinationsReplacement: () => combinationsReplacement,
  combineMeans: () => combineMeans,
  combineVariances: () => combineVariances,
  cumulativeStdLogisticProbability: () => cumulativeStdLogisticProbability,
  cumulativeStdNormalProbability: () => cumulativeStdNormalProbability,
  epsilon: () => epsilon3,
  equalIntervalBreaks: () => equalIntervalBreaks,
  erf: () => errorFunction,
  errorFunction: () => errorFunction,
  extent: () => extent,
  extentSorted: () => extentSorted,
  factorial: () => factorial,
  gamma: () => gamma,
  gammaln: () => gammaln,
  geometricMean: () => geometricMean,
  harmonicMean: () => harmonicMean,
  interquartileRange: () => interquartileRange,
  inverseErrorFunction: () => inverseErrorFunction,
  iqr: () => interquartileRange,
  jenks: () => jenks,
  kMeansCluster: () => kMeansCluster,
  kde: () => kernelDensityEstimation,
  kernelDensityEstimation: () => kernelDensityEstimation,
  linearRegression: () => linearRegression,
  linearRegressionLine: () => linearRegressionLine,
  logAverage: () => logAverage,
  logit: () => logit,
  mad: () => medianAbsoluteDeviation,
  max: () => max2,
  maxSorted: () => maxSorted,
  mean: () => mean,
  meanSimple: () => meanSimple,
  median: () => median2,
  medianAbsoluteDeviation: () => medianAbsoluteDeviation,
  medianSorted: () => medianSorted,
  min: () => min2,
  minSorted: () => minSorted,
  mode: () => mode,
  modeFast: () => modeFast,
  modeSorted: () => modeSorted,
  numericSort: () => numericSort,
  perceptron: () => PerceptronModel,
  permutationTest: () => permutationTest,
  permutationsHeap: () => permutationsHeap,
  poissonDistribution: () => poissonDistribution,
  probit: () => probit,
  product: () => product,
  quantile: () => quantile,
  quantileRank: () => quantileRank,
  quantileRankSorted: () => quantileRankSorted,
  quantileSorted: () => quantileSorted2,
  quickselect: () => quickselect,
  rSquared: () => rSquared,
  relativeError: () => relativeError,
  rms: () => rootMeanSquare,
  rootMeanSquare: () => rootMeanSquare,
  sample: () => sample,
  sampleCorrelation: () => sampleCorrelation,
  sampleCovariance: () => sampleCovariance,
  sampleKurtosis: () => sampleKurtosis,
  sampleRankCorrelation: () => sampleRankCorrelation,
  sampleSkewness: () => sampleSkewness,
  sampleStandardDeviation: () => sampleStandardDeviation,
  sampleVariance: () => sampleVariance,
  sampleWithReplacement: () => sampleWithReplacement,
  shuffle: () => shuffle,
  shuffleInPlace: () => shuffleInPlace,
  sign: () => sign2,
  silhouette: () => silhouette,
  silhouetteMetric: () => silhouetteMetric,
  standardDeviation: () => standardDeviation,
  standardNormalTable: () => standardNormalTable,
  subtractFromMean: () => subtractFromMean,
  sum: () => sum2,
  sumNthPowerDeviations: () => sumNthPowerDeviations,
  sumSimple: () => sumSimple,
  tTest: () => tTest,
  tTestTwoSample: () => tTestTwoSample,
  uniqueCountSorted: () => uniqueCountSorted,
  variance: () => variance2,
  wilcoxonRankSum: () => wilcoxonRankSum,
  zScore: () => zScore
});
function linearRegression(data) {
  var m2;
  var b2;
  var dataLength = data.length;
  if (dataLength === 1) {
    m2 = 0;
    b2 = data[0][1];
  } else {
    var sumX = 0;
    var sumY = 0;
    var sumXX = 0;
    var sumXY = 0;
    var point2;
    var x3;
    var y3;
    for (var i2 = 0; i2 < dataLength; i2++) {
      point2 = data[i2];
      x3 = point2[0];
      y3 = point2[1];
      sumX += x3;
      sumY += y3;
      sumXX += x3 * x3;
      sumXY += x3 * y3;
    }
    m2 = (dataLength * sumXY - sumX * sumY) / (dataLength * sumXX - sumX * sumX);
    b2 = sumY / dataLength - m2 * sumX / dataLength;
  }
  return {
    m: m2,
    b: b2
  };
}
function linearRegressionLine(mb) {
  return function(x3) {
    return mb.b + mb.m * x3;
  };
}
function sum2(x3) {
  if (x3.length === 0) {
    return 0;
  }
  var sum3 = x3[0];
  var correction = 0;
  var transition;
  if (typeof sum3 !== "number") {
    return Number.NaN;
  }
  for (var i2 = 1; i2 < x3.length; i2++) {
    if (typeof x3[i2] !== "number") {
      return Number.NaN;
    }
    transition = sum3 + x3[i2];
    if (Math.abs(sum3) >= Math.abs(x3[i2])) {
      correction += sum3 - transition + x3[i2];
    } else {
      correction += x3[i2] - transition + sum3;
    }
    sum3 = transition;
  }
  return sum3 + correction;
}
function mean(x3) {
  if (x3.length === 0) {
    throw new Error("mean requires at least one data point");
  }
  return sum2(x3) / x3.length;
}
function sumNthPowerDeviations(x3, n2) {
  var meanValue = mean(x3);
  var sum3 = 0;
  var tempValue;
  var i2;
  if (n2 === 2) {
    for (i2 = 0; i2 < x3.length; i2++) {
      tempValue = x3[i2] - meanValue;
      sum3 += tempValue * tempValue;
    }
  } else {
    for (i2 = 0; i2 < x3.length; i2++) {
      sum3 += Math.pow(x3[i2] - meanValue, n2);
    }
  }
  return sum3;
}
function variance2(x3) {
  if (x3.length === 0) {
    throw new Error("variance requires at least one data point");
  }
  return sumNthPowerDeviations(x3, 2) / x3.length;
}
function standardDeviation(x3) {
  if (x3.length === 1) {
    return 0;
  }
  var v2 = variance2(x3);
  return Math.sqrt(v2);
}
function rSquared(x3, func) {
  if (x3.length < 2) {
    return 1;
  }
  var sum3 = 0;
  for (var i2 = 0; i2 < x3.length; i2++) {
    sum3 += x3[i2][1];
  }
  var average = sum3 / x3.length;
  var sumOfSquares = 0;
  for (var j2 = 0; j2 < x3.length; j2++) {
    sumOfSquares += Math.pow(average - x3[j2][1], 2);
  }
  var err = 0;
  for (var k2 = 0; k2 < x3.length; k2++) {
    err += Math.pow(x3[k2][1] - func(x3[k2][0]), 2);
  }
  return 1 - err / sumOfSquares;
}
function modeSorted(sorted) {
  if (sorted.length === 0) {
    throw new Error("mode requires at least one data point");
  }
  if (sorted.length === 1) {
    return sorted[0];
  }
  var last2 = sorted[0];
  var value = Number.NaN;
  var maxSeen = 0;
  var seenThis = 1;
  for (var i2 = 1; i2 < sorted.length + 1; i2++) {
    if (sorted[i2] !== last2) {
      if (seenThis > maxSeen) {
        maxSeen = seenThis;
        value = last2;
      }
      seenThis = 1;
      last2 = sorted[i2];
    } else {
      seenThis++;
    }
  }
  return value;
}
function numericSort(x3) {
  return x3.slice().sort(function(a2, b2) {
    return a2 - b2;
  });
}
function mode(x3) {
  return modeSorted(numericSort(x3));
}
function modeFast(x3) {
  var index = /* @__PURE__ */ new Map();
  var mode2;
  var modeCount = 0;
  for (var i2 = 0; i2 < x3.length; i2++) {
    var newCount = index.get(x3[i2]);
    if (newCount === void 0) {
      newCount = 1;
    } else {
      newCount++;
    }
    if (newCount > modeCount) {
      mode2 = x3[i2];
      modeCount = newCount;
    }
    index.set(x3[i2], newCount);
  }
  if (modeCount === 0) {
    throw new Error("mode requires at last one data point");
  }
  return mode2;
}
function min2(x3) {
  if (x3.length === 0) {
    throw new Error("min requires at least one data point");
  }
  var value = x3[0];
  for (var i2 = 1; i2 < x3.length; i2++) {
    if (x3[i2] < value) {
      value = x3[i2];
    }
  }
  return value;
}
function max2(x3) {
  if (x3.length === 0) {
    throw new Error("max requires at least one data point");
  }
  var value = x3[0];
  for (var i2 = 1; i2 < x3.length; i2++) {
    if (x3[i2] > value) {
      value = x3[i2];
    }
  }
  return value;
}
function extent(x3) {
  if (x3.length === 0) {
    throw new Error("extent requires at least one data point");
  }
  var min3 = x3[0];
  var max3 = x3[0];
  for (var i2 = 1; i2 < x3.length; i2++) {
    if (x3[i2] > max3) {
      max3 = x3[i2];
    }
    if (x3[i2] < min3) {
      min3 = x3[i2];
    }
  }
  return [min3, max3];
}
function minSorted(x3) {
  return x3[0];
}
function maxSorted(x3) {
  return x3[x3.length - 1];
}
function extentSorted(x3) {
  return [x3[0], x3[x3.length - 1]];
}
function sumSimple(x3) {
  var value = 0;
  for (var i2 = 0; i2 < x3.length; i2++) {
    if (typeof x3[i2] !== "number") {
      return Number.NaN;
    }
    value += x3[i2];
  }
  return value;
}
function product(x3) {
  var value = 1;
  for (var i2 = 0; i2 < x3.length; i2++) {
    value *= x3[i2];
  }
  return value;
}
function quantileSorted2(x3, p2) {
  var idx = x3.length * p2;
  if (x3.length === 0) {
    throw new Error("quantile requires at least one data point.");
  } else if (p2 < 0 || p2 > 1) {
    throw new Error("quantiles must be between 0 and 1");
  } else if (p2 === 1) {
    return x3[x3.length - 1];
  } else if (p2 === 0) {
    return x3[0];
  } else if (idx % 1 !== 0) {
    return x3[Math.ceil(idx) - 1];
  } else if (x3.length % 2 === 0) {
    return (x3[idx - 1] + x3[idx]) / 2;
  } else {
    return x3[idx];
  }
}
function quickselect(arr, k2, left, right) {
  left = left || 0;
  right = right || arr.length - 1;
  while (right > left) {
    if (right - left > 600) {
      var n2 = right - left + 1;
      var m2 = k2 - left + 1;
      var z2 = Math.log(n2);
      var s2 = 0.5 * Math.exp(2 * z2 / 3);
      var sd = 0.5 * Math.sqrt(z2 * s2 * (n2 - s2) / n2);
      if (m2 - n2 / 2 < 0) {
        sd *= -1;
      }
      var newLeft = Math.max(left, Math.floor(k2 - m2 * s2 / n2 + sd));
      var newRight = Math.min(
        right,
        Math.floor(k2 + (n2 - m2) * s2 / n2 + sd)
      );
      quickselect(arr, k2, newLeft, newRight);
    }
    var t2 = arr[k2];
    var i2 = left;
    var j2 = right;
    swap(arr, left, k2);
    if (arr[right] > t2) {
      swap(arr, left, right);
    }
    while (i2 < j2) {
      swap(arr, i2, j2);
      i2++;
      j2--;
      while (arr[i2] < t2) {
        i2++;
      }
      while (arr[j2] > t2) {
        j2--;
      }
    }
    if (arr[left] === t2) {
      swap(arr, left, j2);
    } else {
      j2++;
      swap(arr, j2, right);
    }
    if (j2 <= k2) {
      left = j2 + 1;
    }
    if (k2 <= j2) {
      right = j2 - 1;
    }
  }
}
function swap(arr, i2, j2) {
  var tmp = arr[i2];
  arr[i2] = arr[j2];
  arr[j2] = tmp;
}
function quantile(x3, p2) {
  var copy = x3.slice();
  if (Array.isArray(p2)) {
    multiQuantileSelect(copy, p2);
    var results = [];
    for (var i2 = 0; i2 < p2.length; i2++) {
      results[i2] = quantileSorted2(copy, p2[i2]);
    }
    return results;
  } else {
    var idx = quantileIndex(copy.length, p2);
    quantileSelect(copy, idx, 0, copy.length - 1);
    return quantileSorted2(copy, p2);
  }
}
function quantileSelect(arr, k2, left, right) {
  if (k2 % 1 === 0) {
    quickselect(arr, k2, left, right);
  } else {
    k2 = Math.floor(k2);
    quickselect(arr, k2, left, right);
    quickselect(arr, k2 + 1, k2 + 1, right);
  }
}
function multiQuantileSelect(arr, p2) {
  var indices = [0];
  for (var i2 = 0; i2 < p2.length; i2++) {
    indices.push(quantileIndex(arr.length, p2[i2]));
  }
  indices.push(arr.length - 1);
  indices.sort(compare);
  var stack = [0, indices.length - 1];
  while (stack.length) {
    var r2 = Math.ceil(stack.pop());
    var l2 = Math.floor(stack.pop());
    if (r2 - l2 <= 1) {
      continue;
    }
    var m2 = Math.floor((l2 + r2) / 2);
    quantileSelect(
      arr,
      indices[m2],
      Math.floor(indices[l2]),
      Math.ceil(indices[r2])
    );
    stack.push(l2, m2, m2, r2);
  }
}
function compare(a2, b2) {
  return a2 - b2;
}
function quantileIndex(len, p2) {
  var idx = len * p2;
  if (p2 === 1) {
    return len - 1;
  } else if (p2 === 0) {
    return 0;
  } else if (idx % 1 !== 0) {
    return Math.ceil(idx) - 1;
  } else if (len % 2 === 0) {
    return idx - 0.5;
  } else {
    return idx;
  }
}
function quantileRankSorted(x3, value) {
  if (value < x3[0]) {
    return 0;
  }
  if (value > x3[x3.length - 1]) {
    return 1;
  }
  var l2 = lowerBound(x3, value);
  if (x3[l2] !== value) {
    return l2 / x3.length;
  }
  l2++;
  var u2 = upperBound(x3, value);
  if (u2 === l2) {
    return l2 / x3.length;
  }
  var r2 = u2 - l2 + 1;
  var sum3 = r2 * (u2 + l2) / 2;
  var mean2 = sum3 / r2;
  return mean2 / x3.length;
}
function lowerBound(x3, value) {
  var mid = 0;
  var lo = 0;
  var hi = x3.length;
  while (lo < hi) {
    mid = lo + hi >>> 1;
    if (value <= x3[mid]) {
      hi = mid;
    } else {
      lo = -~mid;
    }
  }
  return lo;
}
function upperBound(x3, value) {
  var mid = 0;
  var lo = 0;
  var hi = x3.length;
  while (lo < hi) {
    mid = lo + hi >>> 1;
    if (value >= x3[mid]) {
      lo = -~mid;
    } else {
      hi = mid;
    }
  }
  return lo;
}
function quantileRank(x3, value) {
  var sortedCopy = numericSort(x3);
  return quantileRankSorted(sortedCopy, value);
}
function interquartileRange(x3) {
  var q1 = quantile(x3, 0.75);
  var q2 = quantile(x3, 0.25);
  if (typeof q1 === "number" && typeof q2 === "number") {
    return q1 - q2;
  }
}
function median2(x3) {
  return +quantile(x3, 0.5);
}
function medianAbsoluteDeviation(x3) {
  var medianValue = median2(x3);
  var medianAbsoluteDeviations = [];
  for (var i2 = 0; i2 < x3.length; i2++) {
    medianAbsoluteDeviations.push(Math.abs(x3[i2] - medianValue));
  }
  return median2(medianAbsoluteDeviations);
}
function chunk(x3, chunkSize) {
  var output = [];
  if (chunkSize < 1) {
    throw new Error("chunk size must be a positive number");
  }
  if (Math.floor(chunkSize) !== chunkSize) {
    throw new Error("chunk size must be an integer");
  }
  for (var start = 0; start < x3.length; start += chunkSize) {
    output.push(x3.slice(start, start + chunkSize));
  }
  return output;
}
function sampleWithReplacement(x3, n2, randomSource) {
  if (x3.length === 0) {
    return [];
  }
  randomSource = randomSource || Math.random;
  var length = x3.length;
  var sample2 = [];
  for (var i2 = 0; i2 < n2; i2++) {
    var index = Math.floor(randomSource() * length);
    sample2.push(x3[index]);
  }
  return sample2;
}
function shuffleInPlace(x3, randomSource) {
  randomSource = randomSource || Math.random;
  var length = x3.length;
  var temporary;
  var index;
  while (length > 0) {
    index = Math.floor(randomSource() * length--);
    temporary = x3[length];
    x3[length] = x3[index];
    x3[index] = temporary;
  }
  return x3;
}
function shuffle(x3, randomSource) {
  var sample2 = x3.slice();
  return shuffleInPlace(sample2, randomSource);
}
function sample(x3, n2, randomSource) {
  var shuffled = shuffle(x3, randomSource);
  return shuffled.slice(0, n2);
}
function makeMatrix(columns, rows) {
  var matrix = [];
  for (var i2 = 0; i2 < columns; i2++) {
    var column = [];
    for (var j2 = 0; j2 < rows; j2++) {
      column.push(0);
    }
    matrix.push(column);
  }
  return matrix;
}
function uniqueCountSorted(x3) {
  var uniqueValueCount = 0;
  var lastSeenValue;
  for (var i2 = 0; i2 < x3.length; i2++) {
    if (i2 === 0 || x3[i2] !== lastSeenValue) {
      lastSeenValue = x3[i2];
      uniqueValueCount++;
    }
  }
  return uniqueValueCount;
}
function ssq(j2, i2, sums, sumsOfSquares) {
  var sji;
  if (j2 > 0) {
    var muji = (sums[i2] - sums[j2 - 1]) / (i2 - j2 + 1);
    sji = sumsOfSquares[i2] - sumsOfSquares[j2 - 1] - (i2 - j2 + 1) * muji * muji;
  } else {
    sji = sumsOfSquares[i2] - sums[i2] * sums[i2] / (i2 + 1);
  }
  if (sji < 0) {
    return 0;
  }
  return sji;
}
function fillMatrixColumn(iMin, iMax, cluster, matrix, backtrackMatrix, sums, sumsOfSquares) {
  if (iMin > iMax) {
    return;
  }
  var i2 = Math.floor((iMin + iMax) / 2);
  matrix[cluster][i2] = matrix[cluster - 1][i2 - 1];
  backtrackMatrix[cluster][i2] = i2;
  var jlow = cluster;
  if (iMin > cluster) {
    jlow = Math.max(jlow, backtrackMatrix[cluster][iMin - 1] || 0);
  }
  jlow = Math.max(jlow, backtrackMatrix[cluster - 1][i2] || 0);
  var jhigh = i2 - 1;
  if (iMax < matrix[0].length - 1) {
    jhigh = Math.min(jhigh, backtrackMatrix[cluster][iMax + 1] || 0);
  }
  var sji;
  var sjlowi;
  var ssqjlow;
  var ssqj;
  for (var j2 = jhigh; j2 >= jlow; --j2) {
    sji = ssq(j2, i2, sums, sumsOfSquares);
    if (sji + matrix[cluster - 1][jlow - 1] >= matrix[cluster][i2]) {
      break;
    }
    sjlowi = ssq(jlow, i2, sums, sumsOfSquares);
    ssqjlow = sjlowi + matrix[cluster - 1][jlow - 1];
    if (ssqjlow < matrix[cluster][i2]) {
      matrix[cluster][i2] = ssqjlow;
      backtrackMatrix[cluster][i2] = jlow;
    }
    jlow++;
    ssqj = sji + matrix[cluster - 1][j2 - 1];
    if (ssqj < matrix[cluster][i2]) {
      matrix[cluster][i2] = ssqj;
      backtrackMatrix[cluster][i2] = j2;
    }
  }
  fillMatrixColumn(
    iMin,
    i2 - 1,
    cluster,
    matrix,
    backtrackMatrix,
    sums,
    sumsOfSquares
  );
  fillMatrixColumn(
    i2 + 1,
    iMax,
    cluster,
    matrix,
    backtrackMatrix,
    sums,
    sumsOfSquares
  );
}
function fillMatrices(data, matrix, backtrackMatrix) {
  var nValues = matrix[0].length;
  var shift = data[Math.floor(nValues / 2)];
  var sums = [];
  var sumsOfSquares = [];
  for (var i2 = 0, shiftedValue = void 0; i2 < nValues; ++i2) {
    shiftedValue = data[i2] - shift;
    if (i2 === 0) {
      sums.push(shiftedValue);
      sumsOfSquares.push(shiftedValue * shiftedValue);
    } else {
      sums.push(sums[i2 - 1] + shiftedValue);
      sumsOfSquares.push(
        sumsOfSquares[i2 - 1] + shiftedValue * shiftedValue
      );
    }
    matrix[0][i2] = ssq(0, i2, sums, sumsOfSquares);
    backtrackMatrix[0][i2] = 0;
  }
  var iMin;
  for (var cluster = 1; cluster < matrix.length; ++cluster) {
    if (cluster < matrix.length - 1) {
      iMin = cluster;
    } else {
      iMin = nValues - 1;
    }
    fillMatrixColumn(
      iMin,
      nValues - 1,
      cluster,
      matrix,
      backtrackMatrix,
      sums,
      sumsOfSquares
    );
  }
}
function ckmeans(x3, nClusters) {
  if (nClusters > x3.length) {
    throw new Error(
      "cannot generate more classes than there are data values"
    );
  }
  var sorted = numericSort(x3);
  var uniqueCount = uniqueCountSorted(sorted);
  if (uniqueCount === 1) {
    return [sorted];
  }
  var matrix = makeMatrix(nClusters, sorted.length);
  var backtrackMatrix = makeMatrix(nClusters, sorted.length);
  fillMatrices(sorted, matrix, backtrackMatrix);
  var clusters = [];
  var clusterRight = backtrackMatrix[0].length - 1;
  for (var cluster = backtrackMatrix.length - 1; cluster >= 0; cluster--) {
    var clusterLeft = backtrackMatrix[cluster][clusterRight];
    clusters[cluster] = sorted.slice(clusterLeft, clusterRight + 1);
    if (cluster > 0) {
      clusterRight = clusterLeft - 1;
    }
  }
  return clusters;
}
function jenksBreaks(data, lowerClassLimits, nClasses) {
  var k2 = data.length;
  var kclass = [];
  var countNum = nClasses;
  kclass[nClasses] = data[data.length - 1];
  while (countNum > 0) {
    kclass[countNum - 1] = data[lowerClassLimits[k2][countNum] - 1];
    k2 = lowerClassLimits[k2][countNum] - 1;
    countNum--;
  }
  return kclass;
}
function jenksMatrices(data, nClasses) {
  var lowerClassLimits = [];
  var varianceCombinations = [];
  var i2;
  var j2;
  var variance3 = 0;
  for (i2 = 0; i2 < data.length + 1; i2++) {
    var tmp1 = [];
    var tmp2 = [];
    for (j2 = 0; j2 < nClasses + 1; j2++) {
      tmp1.push(0);
      tmp2.push(0);
    }
    lowerClassLimits.push(tmp1);
    varianceCombinations.push(tmp2);
  }
  for (i2 = 1; i2 < nClasses + 1; i2++) {
    lowerClassLimits[1][i2] = 1;
    varianceCombinations[1][i2] = 0;
    for (j2 = 2; j2 < data.length + 1; j2++) {
      varianceCombinations[j2][i2] = Number.POSITIVE_INFINITY;
    }
  }
  for (var l2 = 2; l2 < data.length + 1; l2++) {
    var sum3 = 0;
    var sumSquares = 0;
    var w2 = 0;
    var i4 = 0;
    for (var m2 = 1; m2 < l2 + 1; m2++) {
      var lowerClassLimit = l2 - m2 + 1;
      var val = data[lowerClassLimit - 1];
      w2++;
      sum3 += val;
      sumSquares += val * val;
      variance3 = sumSquares - sum3 * sum3 / w2;
      i4 = lowerClassLimit - 1;
      if (i4 !== 0) {
        for (j2 = 2; j2 < nClasses + 1; j2++) {
          if (varianceCombinations[l2][j2] >= variance3 + varianceCombinations[i4][j2 - 1]) {
            lowerClassLimits[l2][j2] = lowerClassLimit;
            varianceCombinations[l2][j2] = variance3 + varianceCombinations[i4][j2 - 1];
          }
        }
      }
    }
    lowerClassLimits[l2][1] = 1;
    varianceCombinations[l2][1] = variance3;
  }
  return {
    lowerClassLimits,
    varianceCombinations
  };
}
function jenks(data, nClasses) {
  if (nClasses > data.length) {
    return null;
  }
  data = data.slice().sort(function(a2, b2) {
    return a2 - b2;
  });
  var matrices = jenksMatrices(data, nClasses);
  var lowerClassLimits = matrices.lowerClassLimits;
  return jenksBreaks(data, lowerClassLimits, nClasses);
}
function equalIntervalBreaks(x3, nClasses) {
  if (x3.length < 2) {
    return x3;
  }
  var theMin = min2(x3);
  var theMax = max2(x3);
  var breaks = [theMin];
  var breakSize = (theMax - theMin) / nClasses;
  for (var i2 = 1; i2 < nClasses; i2++) {
    breaks.push(breaks[0] + breakSize * i2);
  }
  breaks.push(theMax);
  return breaks;
}
function sampleCovariance(x3, y3) {
  if (x3.length !== y3.length) {
    throw new Error("sampleCovariance requires samples with equal lengths");
  }
  if (x3.length < 2) {
    throw new Error(
      "sampleCovariance requires at least two data points in each sample"
    );
  }
  var xmean = mean(x3);
  var ymean = mean(y3);
  var sum3 = 0;
  for (var i2 = 0; i2 < x3.length; i2++) {
    sum3 += (x3[i2] - xmean) * (y3[i2] - ymean);
  }
  var besselsCorrection = x3.length - 1;
  return sum3 / besselsCorrection;
}
function sampleVariance(x3) {
  if (x3.length < 2) {
    throw new Error("sampleVariance requires at least two data points");
  }
  var sumSquaredDeviationsValue = sumNthPowerDeviations(x3, 2);
  var besselsCorrection = x3.length - 1;
  return sumSquaredDeviationsValue / besselsCorrection;
}
function sampleStandardDeviation(x3) {
  var sampleVarianceX = sampleVariance(x3);
  return Math.sqrt(sampleVarianceX);
}
function sampleCorrelation(x3, y3) {
  var cov = sampleCovariance(x3, y3);
  var xstd = sampleStandardDeviation(x3);
  var ystd = sampleStandardDeviation(y3);
  return cov / xstd / ystd;
}
function sampleRankCorrelation(x3, y3) {
  var xIndexes = x3.map(function(value, index) {
    return [value, index];
  }).sort(function(a2, b2) {
    return a2[0] - b2[0];
  }).map(function(pair2) {
    return pair2[1];
  });
  var yIndexes = y3.map(function(value, index) {
    return [value, index];
  }).sort(function(a2, b2) {
    return a2[0] - b2[0];
  }).map(function(pair2) {
    return pair2[1];
  });
  var xRanks = Array(xIndexes.length);
  var yRanks = Array(xIndexes.length);
  for (var i2 = 0; i2 < xIndexes.length; i2++) {
    xRanks[xIndexes[i2]] = i2;
    yRanks[yIndexes[i2]] = i2;
  }
  return sampleCorrelation(xRanks, yRanks);
}
function sampleSkewness(x3) {
  if (x3.length < 3) {
    throw new Error("sampleSkewness requires at least three data points");
  }
  var meanValue = mean(x3);
  var tempValue;
  var sumSquaredDeviations = 0;
  var sumCubedDeviations = 0;
  for (var i2 = 0; i2 < x3.length; i2++) {
    tempValue = x3[i2] - meanValue;
    sumSquaredDeviations += tempValue * tempValue;
    sumCubedDeviations += tempValue * tempValue * tempValue;
  }
  var besselsCorrection = x3.length - 1;
  var theSampleStandardDeviation = Math.sqrt(
    sumSquaredDeviations / besselsCorrection
  );
  var n2 = x3.length;
  var cubedS = Math.pow(theSampleStandardDeviation, 3);
  return n2 * sumCubedDeviations / ((n2 - 1) * (n2 - 2) * cubedS);
}
function sampleKurtosis(x3) {
  var n2 = x3.length;
  if (n2 < 4) {
    throw new Error("sampleKurtosis requires at least four data points");
  }
  var meanValue = mean(x3);
  var tempValue;
  var secondCentralMoment = 0;
  var fourthCentralMoment = 0;
  for (var i2 = 0; i2 < n2; i2++) {
    tempValue = x3[i2] - meanValue;
    secondCentralMoment += tempValue * tempValue;
    fourthCentralMoment += tempValue * tempValue * tempValue * tempValue;
  }
  return (n2 - 1) / ((n2 - 2) * (n2 - 3)) * (n2 * (n2 + 1) * fourthCentralMoment / (secondCentralMoment * secondCentralMoment) - 3 * (n2 - 1));
}
function permutationsHeap(elements) {
  var indexes = new Array(elements.length);
  var permutations = [elements.slice()];
  for (var i2 = 0; i2 < elements.length; i2++) {
    indexes[i2] = 0;
  }
  for (var i$1 = 0; i$1 < elements.length; ) {
    if (indexes[i$1] < i$1) {
      var swapFrom = 0;
      if (i$1 % 2 !== 0) {
        swapFrom = indexes[i$1];
      }
      var temp2 = elements[swapFrom];
      elements[swapFrom] = elements[i$1];
      elements[i$1] = temp2;
      permutations.push(elements.slice());
      indexes[i$1]++;
      i$1 = 0;
    } else {
      indexes[i$1] = 0;
      i$1++;
    }
  }
  return permutations;
}
function combinations(x3, k2) {
  var i2;
  var subI;
  var combinationList = [];
  var subsetCombinations;
  var next;
  for (i2 = 0; i2 < x3.length; i2++) {
    if (k2 === 1) {
      combinationList.push([x3[i2]]);
    } else {
      subsetCombinations = combinations(x3.slice(i2 + 1, x3.length), k2 - 1);
      for (subI = 0; subI < subsetCombinations.length; subI++) {
        next = subsetCombinations[subI];
        next.unshift(x3[i2]);
        combinationList.push(next);
      }
    }
  }
  return combinationList;
}
function combinationsReplacement(x3, k2) {
  var combinationList = [];
  for (var i2 = 0; i2 < x3.length; i2++) {
    if (k2 === 1) {
      combinationList.push([x3[i2]]);
    } else {
      var subsetCombinations = combinationsReplacement(
        x3.slice(i2, x3.length),
        k2 - 1
      );
      for (var j2 = 0; j2 < subsetCombinations.length; j2++) {
        combinationList.push([x3[i2]].concat(subsetCombinations[j2]));
      }
    }
  }
  return combinationList;
}
function addToMean(mean2, n2, newValue) {
  return mean2 + (newValue - mean2) / (n2 + 1);
}
function combineMeans(mean1, n1, mean2, n2) {
  return (mean1 * n1 + mean2 * n2) / (n1 + n2);
}
function combineVariances(variance1, mean1, n1, variance22, mean2, n2) {
  var newMean = combineMeans(mean1, n1, mean2, n2);
  return (n1 * (variance1 + Math.pow(mean1 - newMean, 2)) + n2 * (variance22 + Math.pow(mean2 - newMean, 2))) / (n1 + n2);
}
function geometricMean(x3) {
  if (x3.length === 0) {
    throw new Error("geometricMean requires at least one data point");
  }
  var value = 1;
  for (var i2 = 0; i2 < x3.length; i2++) {
    if (x3[i2] < 0) {
      throw new Error(
        "geometricMean requires only non-negative numbers as input"
      );
    }
    value *= x3[i2];
  }
  return Math.pow(value, 1 / x3.length);
}
function logAverage(x3) {
  if (x3.length === 0) {
    throw new Error("logAverage requires at least one data point");
  }
  var value = 0;
  for (var i2 = 0; i2 < x3.length; i2++) {
    if (x3[i2] < 0) {
      throw new Error(
        "logAverage requires only non-negative numbers as input"
      );
    }
    value += Math.log(x3[i2]);
  }
  return Math.exp(value / x3.length);
}
function harmonicMean(x3) {
  if (x3.length === 0) {
    throw new Error("harmonicMean requires at least one data point");
  }
  var reciprocalSum = 0;
  for (var i2 = 0; i2 < x3.length; i2++) {
    if (x3[i2] <= 0) {
      throw new Error(
        "harmonicMean requires only positive numbers as input"
      );
    }
    reciprocalSum += 1 / x3[i2];
  }
  return x3.length / reciprocalSum;
}
function meanSimple(x3) {
  if (x3.length === 0) {
    throw new Error("meanSimple requires at least one data point");
  }
  return sumSimple(x3) / x3.length;
}
function medianSorted(sorted) {
  return quantileSorted2(sorted, 0.5);
}
function subtractFromMean(mean2, n2, value) {
  return (mean2 * n2 - value) / (n2 - 1);
}
function rootMeanSquare(x3) {
  if (x3.length === 0) {
    throw new Error("rootMeanSquare requires at least one data point");
  }
  var sumOfSquares = 0;
  for (var i2 = 0; i2 < x3.length; i2++) {
    sumOfSquares += Math.pow(x3[i2], 2);
  }
  return Math.sqrt(sumOfSquares / x3.length);
}
function coefficientOfVariation(x3) {
  return sampleStandardDeviation(x3) / mean(x3);
}
function tTest(x3, expectedValue) {
  var sampleMean = mean(x3);
  var sd = standardDeviation(x3);
  var rootN = Math.sqrt(x3.length);
  return (sampleMean - expectedValue) / (sd / rootN);
}
function tTestTwoSample(sampleX, sampleY, difference) {
  var n2 = sampleX.length;
  var m2 = sampleY.length;
  if (!n2 || !m2) {
    return null;
  }
  if (!difference) {
    difference = 0;
  }
  var meanX = mean(sampleX);
  var meanY = mean(sampleY);
  var sampleVarianceX = sampleVariance(sampleX);
  var sampleVarianceY = sampleVariance(sampleY);
  if (typeof meanX === "number" && typeof meanY === "number" && typeof sampleVarianceX === "number" && typeof sampleVarianceY === "number") {
    var weightedVariance = ((n2 - 1) * sampleVarianceX + (m2 - 1) * sampleVarianceY) / (n2 + m2 - 2);
    return (meanX - meanY - difference) / Math.sqrt(weightedVariance * (1 / n2 + 1 / m2));
  }
}
function wilcoxonRankSum(sampleX, sampleY) {
  if (!sampleX.length || !sampleY.length) {
    throw new Error("Neither sample can be empty");
  }
  var pooledSamples = sampleX.map(function(x3) {
    return { label: "x", value: x3 };
  }).concat(sampleY.map(function(y3) {
    return { label: "y", value: y3 };
  })).sort(function(a2, b2) {
    return a2.value - b2.value;
  });
  for (var rank = 0; rank < pooledSamples.length; rank++) {
    pooledSamples[rank].rank = rank;
  }
  var tiedRanks = [pooledSamples[0].rank];
  for (var i2 = 1; i2 < pooledSamples.length; i2++) {
    if (pooledSamples[i2].value === pooledSamples[i2 - 1].value) {
      tiedRanks.push(pooledSamples[i2].rank);
      if (i2 === pooledSamples.length - 1) {
        replaceRanksInPlace(pooledSamples, tiedRanks);
      }
    } else if (tiedRanks.length > 1) {
      replaceRanksInPlace(pooledSamples, tiedRanks);
    } else {
      tiedRanks = [pooledSamples[i2].rank];
    }
  }
  function replaceRanksInPlace(pooledSamples2, tiedRanks2) {
    var average = (tiedRanks2[0] + tiedRanks2[tiedRanks2.length - 1]) / 2;
    for (var i3 = 0; i3 < tiedRanks2.length; i3++) {
      pooledSamples2[tiedRanks2[i3]].rank = average;
    }
  }
  var rankSum = 0;
  for (var i$1 = 0; i$1 < pooledSamples.length; i$1++) {
    var sample2 = pooledSamples[i$1];
    if (sample2.label === "x") {
      rankSum += sample2.rank + 1;
    }
  }
  return rankSum;
}
var BayesianClassifier = function BayesianClassifier2() {
  this.totalCount = 0;
  this.data = {};
};
BayesianClassifier.prototype.train = function train(item, category) {
  if (!this.data[category]) {
    this.data[category] = {};
  }
  for (var k2 in item) {
    var v2 = item[k2];
    if (this.data[category][k2] === void 0) {
      this.data[category][k2] = {};
    }
    if (this.data[category][k2][v2] === void 0) {
      this.data[category][k2][v2] = 0;
    }
    this.data[category][k2][v2]++;
  }
  this.totalCount++;
};
BayesianClassifier.prototype.score = function score(item) {
  var odds = {};
  var category;
  for (var k2 in item) {
    var v2 = item[k2];
    for (category in this.data) {
      odds[category] = {};
      if (this.data[category][k2]) {
        odds[category][k2 + "_" + v2] = (this.data[category][k2][v2] || 0) / this.totalCount;
      } else {
        odds[category][k2 + "_" + v2] = 0;
      }
    }
  }
  var oddsSums = {};
  for (category in odds) {
    oddsSums[category] = 0;
    for (var combination in odds[category]) {
      oddsSums[category] += odds[category][combination];
    }
  }
  return oddsSums;
};
var PerceptronModel = function PerceptronModel2() {
  this.weights = [];
  this.bias = 0;
};
PerceptronModel.prototype.predict = function predict(features) {
  if (features.length !== this.weights.length) {
    return null;
  }
  var score2 = 0;
  for (var i2 = 0; i2 < this.weights.length; i2++) {
    score2 += this.weights[i2] * features[i2];
  }
  score2 += this.bias;
  if (score2 > 0) {
    return 1;
  } else {
    return 0;
  }
};
PerceptronModel.prototype.train = function train2(features, label) {
  if (label !== 0 && label !== 1) {
    return null;
  }
  if (features.length !== this.weights.length) {
    this.weights = features;
    this.bias = 1;
  }
  var prediction = this.predict(features);
  if (typeof prediction === "number" && prediction !== label) {
    var gradient = label - prediction;
    for (var i2 = 0; i2 < this.weights.length; i2++) {
      this.weights[i2] += gradient * features[i2];
    }
    this.bias += gradient;
  }
  return this;
};
var epsilon3 = 1e-4;
function factorial(n2) {
  if (n2 < 0) {
    throw new Error("factorial requires a non-negative value");
  }
  if (Math.floor(n2) !== n2) {
    throw new Error("factorial requires an integer input");
  }
  var accumulator = 1;
  for (var i2 = 2; i2 <= n2; i2++) {
    accumulator *= i2;
  }
  return accumulator;
}
function gamma(n2) {
  if (Number.isInteger(n2)) {
    if (n2 <= 0) {
      return Number.NaN;
    } else {
      return factorial(n2 - 1);
    }
  }
  n2--;
  if (n2 < 0) {
    return Math.PI / (Math.sin(Math.PI * -n2) * gamma(-n2));
  } else {
    var seriesCoefficient = Math.pow(n2 / Math.E, n2) * Math.sqrt(2 * Math.PI * (n2 + 1 / 6));
    var seriesDenom = n2 + 1 / 4;
    var seriesExpansion = 1 + 1 / 144 / Math.pow(seriesDenom, 2) - 1 / 12960 / Math.pow(seriesDenom, 3) - 257 / 207360 / Math.pow(seriesDenom, 4) - 52 / 2612736 / Math.pow(seriesDenom, 5) + 5741173 / 9405849600 / Math.pow(seriesDenom, 6) + 37529 / 18811699200 / Math.pow(seriesDenom, 7);
    return seriesCoefficient * seriesExpansion;
  }
}
var COEFFICIENTS = [
  0.9999999999999971,
  57.15623566586292,
  -59.59796035547549,
  14.136097974741746,
  -0.4919138160976202,
  3399464998481189e-20,
  4652362892704858e-20,
  -9837447530487956e-20,
  1580887032249125e-19,
  -21026444172410488e-20,
  21743961811521265e-20,
  -1643181065367639e-19,
  8441822398385275e-20,
  -26190838401581408e-21,
  36899182659531625e-22
];
var g = 607 / 128;
var LOGSQRT2PI = Math.log(Math.sqrt(2 * Math.PI));
function gammaln(n2) {
  if (n2 <= 0) {
    return Number.POSITIVE_INFINITY;
  }
  n2--;
  var a2 = COEFFICIENTS[0];
  for (var i2 = 1; i2 < 15; i2++) {
    a2 += COEFFICIENTS[i2] / (n2 + i2);
  }
  var tmp = g + 0.5 + n2;
  return LOGSQRT2PI + Math.log(a2) - tmp + (n2 + 0.5) * Math.log(tmp);
}
function bernoulliDistribution(p2) {
  if (p2 < 0 || p2 > 1) {
    throw new Error(
      "bernoulliDistribution requires probability to be between 0 and 1 inclusive"
    );
  }
  return [1 - p2, p2];
}
function binomialDistribution(trials, probability) {
  if (probability < 0 || probability > 1 || trials <= 0 || trials % 1 !== 0) {
    return void 0;
  }
  var x3 = 0;
  var cumulativeProbability = 0;
  var cells = [];
  var binomialCoefficient = 1;
  do {
    cells[x3] = binomialCoefficient * Math.pow(probability, x3) * Math.pow(1 - probability, trials - x3);
    cumulativeProbability += cells[x3];
    x3++;
    binomialCoefficient = binomialCoefficient * (trials - x3 + 1) / x3;
  } while (cumulativeProbability < 1 - epsilon3);
  return cells;
}
function poissonDistribution(lambda) {
  if (lambda <= 0) {
    return void 0;
  }
  var x3 = 0;
  var cumulativeProbability = 0;
  var cells = [];
  var factorialX = 1;
  do {
    cells[x3] = Math.exp(-lambda) * Math.pow(lambda, x3) / factorialX;
    cumulativeProbability += cells[x3];
    x3++;
    factorialX *= x3;
  } while (cumulativeProbability < 1 - epsilon3);
  return cells;
}
var chiSquaredDistributionTable = {
  1: {
    0.995: 0,
    0.99: 0,
    0.975: 0,
    0.95: 0,
    0.9: 0.02,
    0.5: 0.45,
    0.1: 2.71,
    0.05: 3.84,
    0.025: 5.02,
    0.01: 6.63,
    5e-3: 7.88
  },
  2: {
    0.995: 0.01,
    0.99: 0.02,
    0.975: 0.05,
    0.95: 0.1,
    0.9: 0.21,
    0.5: 1.39,
    0.1: 4.61,
    0.05: 5.99,
    0.025: 7.38,
    0.01: 9.21,
    5e-3: 10.6
  },
  3: {
    0.995: 0.07,
    0.99: 0.11,
    0.975: 0.22,
    0.95: 0.35,
    0.9: 0.58,
    0.5: 2.37,
    0.1: 6.25,
    0.05: 7.81,
    0.025: 9.35,
    0.01: 11.34,
    5e-3: 12.84
  },
  4: {
    0.995: 0.21,
    0.99: 0.3,
    0.975: 0.48,
    0.95: 0.71,
    0.9: 1.06,
    0.5: 3.36,
    0.1: 7.78,
    0.05: 9.49,
    0.025: 11.14,
    0.01: 13.28,
    5e-3: 14.86
  },
  5: {
    0.995: 0.41,
    0.99: 0.55,
    0.975: 0.83,
    0.95: 1.15,
    0.9: 1.61,
    0.5: 4.35,
    0.1: 9.24,
    0.05: 11.07,
    0.025: 12.83,
    0.01: 15.09,
    5e-3: 16.75
  },
  6: {
    0.995: 0.68,
    0.99: 0.87,
    0.975: 1.24,
    0.95: 1.64,
    0.9: 2.2,
    0.5: 5.35,
    0.1: 10.65,
    0.05: 12.59,
    0.025: 14.45,
    0.01: 16.81,
    5e-3: 18.55
  },
  7: {
    0.995: 0.99,
    0.99: 1.25,
    0.975: 1.69,
    0.95: 2.17,
    0.9: 2.83,
    0.5: 6.35,
    0.1: 12.02,
    0.05: 14.07,
    0.025: 16.01,
    0.01: 18.48,
    5e-3: 20.28
  },
  8: {
    0.995: 1.34,
    0.99: 1.65,
    0.975: 2.18,
    0.95: 2.73,
    0.9: 3.49,
    0.5: 7.34,
    0.1: 13.36,
    0.05: 15.51,
    0.025: 17.53,
    0.01: 20.09,
    5e-3: 21.96
  },
  9: {
    0.995: 1.73,
    0.99: 2.09,
    0.975: 2.7,
    0.95: 3.33,
    0.9: 4.17,
    0.5: 8.34,
    0.1: 14.68,
    0.05: 16.92,
    0.025: 19.02,
    0.01: 21.67,
    5e-3: 23.59
  },
  10: {
    0.995: 2.16,
    0.99: 2.56,
    0.975: 3.25,
    0.95: 3.94,
    0.9: 4.87,
    0.5: 9.34,
    0.1: 15.99,
    0.05: 18.31,
    0.025: 20.48,
    0.01: 23.21,
    5e-3: 25.19
  },
  11: {
    0.995: 2.6,
    0.99: 3.05,
    0.975: 3.82,
    0.95: 4.57,
    0.9: 5.58,
    0.5: 10.34,
    0.1: 17.28,
    0.05: 19.68,
    0.025: 21.92,
    0.01: 24.72,
    5e-3: 26.76
  },
  12: {
    0.995: 3.07,
    0.99: 3.57,
    0.975: 4.4,
    0.95: 5.23,
    0.9: 6.3,
    0.5: 11.34,
    0.1: 18.55,
    0.05: 21.03,
    0.025: 23.34,
    0.01: 26.22,
    5e-3: 28.3
  },
  13: {
    0.995: 3.57,
    0.99: 4.11,
    0.975: 5.01,
    0.95: 5.89,
    0.9: 7.04,
    0.5: 12.34,
    0.1: 19.81,
    0.05: 22.36,
    0.025: 24.74,
    0.01: 27.69,
    5e-3: 29.82
  },
  14: {
    0.995: 4.07,
    0.99: 4.66,
    0.975: 5.63,
    0.95: 6.57,
    0.9: 7.79,
    0.5: 13.34,
    0.1: 21.06,
    0.05: 23.68,
    0.025: 26.12,
    0.01: 29.14,
    5e-3: 31.32
  },
  15: {
    0.995: 4.6,
    0.99: 5.23,
    0.975: 6.27,
    0.95: 7.26,
    0.9: 8.55,
    0.5: 14.34,
    0.1: 22.31,
    0.05: 25,
    0.025: 27.49,
    0.01: 30.58,
    5e-3: 32.8
  },
  16: {
    0.995: 5.14,
    0.99: 5.81,
    0.975: 6.91,
    0.95: 7.96,
    0.9: 9.31,
    0.5: 15.34,
    0.1: 23.54,
    0.05: 26.3,
    0.025: 28.85,
    0.01: 32,
    5e-3: 34.27
  },
  17: {
    0.995: 5.7,
    0.99: 6.41,
    0.975: 7.56,
    0.95: 8.67,
    0.9: 10.09,
    0.5: 16.34,
    0.1: 24.77,
    0.05: 27.59,
    0.025: 30.19,
    0.01: 33.41,
    5e-3: 35.72
  },
  18: {
    0.995: 6.26,
    0.99: 7.01,
    0.975: 8.23,
    0.95: 9.39,
    0.9: 10.87,
    0.5: 17.34,
    0.1: 25.99,
    0.05: 28.87,
    0.025: 31.53,
    0.01: 34.81,
    5e-3: 37.16
  },
  19: {
    0.995: 6.84,
    0.99: 7.63,
    0.975: 8.91,
    0.95: 10.12,
    0.9: 11.65,
    0.5: 18.34,
    0.1: 27.2,
    0.05: 30.14,
    0.025: 32.85,
    0.01: 36.19,
    5e-3: 38.58
  },
  20: {
    0.995: 7.43,
    0.99: 8.26,
    0.975: 9.59,
    0.95: 10.85,
    0.9: 12.44,
    0.5: 19.34,
    0.1: 28.41,
    0.05: 31.41,
    0.025: 34.17,
    0.01: 37.57,
    5e-3: 40
  },
  21: {
    0.995: 8.03,
    0.99: 8.9,
    0.975: 10.28,
    0.95: 11.59,
    0.9: 13.24,
    0.5: 20.34,
    0.1: 29.62,
    0.05: 32.67,
    0.025: 35.48,
    0.01: 38.93,
    5e-3: 41.4
  },
  22: {
    0.995: 8.64,
    0.99: 9.54,
    0.975: 10.98,
    0.95: 12.34,
    0.9: 14.04,
    0.5: 21.34,
    0.1: 30.81,
    0.05: 33.92,
    0.025: 36.78,
    0.01: 40.29,
    5e-3: 42.8
  },
  23: {
    0.995: 9.26,
    0.99: 10.2,
    0.975: 11.69,
    0.95: 13.09,
    0.9: 14.85,
    0.5: 22.34,
    0.1: 32.01,
    0.05: 35.17,
    0.025: 38.08,
    0.01: 41.64,
    5e-3: 44.18
  },
  24: {
    0.995: 9.89,
    0.99: 10.86,
    0.975: 12.4,
    0.95: 13.85,
    0.9: 15.66,
    0.5: 23.34,
    0.1: 33.2,
    0.05: 36.42,
    0.025: 39.36,
    0.01: 42.98,
    5e-3: 45.56
  },
  25: {
    0.995: 10.52,
    0.99: 11.52,
    0.975: 13.12,
    0.95: 14.61,
    0.9: 16.47,
    0.5: 24.34,
    0.1: 34.28,
    0.05: 37.65,
    0.025: 40.65,
    0.01: 44.31,
    5e-3: 46.93
  },
  26: {
    0.995: 11.16,
    0.99: 12.2,
    0.975: 13.84,
    0.95: 15.38,
    0.9: 17.29,
    0.5: 25.34,
    0.1: 35.56,
    0.05: 38.89,
    0.025: 41.92,
    0.01: 45.64,
    5e-3: 48.29
  },
  27: {
    0.995: 11.81,
    0.99: 12.88,
    0.975: 14.57,
    0.95: 16.15,
    0.9: 18.11,
    0.5: 26.34,
    0.1: 36.74,
    0.05: 40.11,
    0.025: 43.19,
    0.01: 46.96,
    5e-3: 49.65
  },
  28: {
    0.995: 12.46,
    0.99: 13.57,
    0.975: 15.31,
    0.95: 16.93,
    0.9: 18.94,
    0.5: 27.34,
    0.1: 37.92,
    0.05: 41.34,
    0.025: 44.46,
    0.01: 48.28,
    5e-3: 50.99
  },
  29: {
    0.995: 13.12,
    0.99: 14.26,
    0.975: 16.05,
    0.95: 17.71,
    0.9: 19.77,
    0.5: 28.34,
    0.1: 39.09,
    0.05: 42.56,
    0.025: 45.72,
    0.01: 49.59,
    5e-3: 52.34
  },
  30: {
    0.995: 13.79,
    0.99: 14.95,
    0.975: 16.79,
    0.95: 18.49,
    0.9: 20.6,
    0.5: 29.34,
    0.1: 40.26,
    0.05: 43.77,
    0.025: 46.98,
    0.01: 50.89,
    5e-3: 53.67
  },
  40: {
    0.995: 20.71,
    0.99: 22.16,
    0.975: 24.43,
    0.95: 26.51,
    0.9: 29.05,
    0.5: 39.34,
    0.1: 51.81,
    0.05: 55.76,
    0.025: 59.34,
    0.01: 63.69,
    5e-3: 66.77
  },
  50: {
    0.995: 27.99,
    0.99: 29.71,
    0.975: 32.36,
    0.95: 34.76,
    0.9: 37.69,
    0.5: 49.33,
    0.1: 63.17,
    0.05: 67.5,
    0.025: 71.42,
    0.01: 76.15,
    5e-3: 79.49
  },
  60: {
    0.995: 35.53,
    0.99: 37.48,
    0.975: 40.48,
    0.95: 43.19,
    0.9: 46.46,
    0.5: 59.33,
    0.1: 74.4,
    0.05: 79.08,
    0.025: 83.3,
    0.01: 88.38,
    5e-3: 91.95
  },
  70: {
    0.995: 43.28,
    0.99: 45.44,
    0.975: 48.76,
    0.95: 51.74,
    0.9: 55.33,
    0.5: 69.33,
    0.1: 85.53,
    0.05: 90.53,
    0.025: 95.02,
    0.01: 100.42,
    5e-3: 104.22
  },
  80: {
    0.995: 51.17,
    0.99: 53.54,
    0.975: 57.15,
    0.95: 60.39,
    0.9: 64.28,
    0.5: 79.33,
    0.1: 96.58,
    0.05: 101.88,
    0.025: 106.63,
    0.01: 112.33,
    5e-3: 116.32
  },
  90: {
    0.995: 59.2,
    0.99: 61.75,
    0.975: 65.65,
    0.95: 69.13,
    0.9: 73.29,
    0.5: 89.33,
    0.1: 107.57,
    0.05: 113.14,
    0.025: 118.14,
    0.01: 124.12,
    5e-3: 128.3
  },
  100: {
    0.995: 67.33,
    0.99: 70.06,
    0.975: 74.22,
    0.95: 77.93,
    0.9: 82.36,
    0.5: 99.33,
    0.1: 118.5,
    0.05: 124.34,
    0.025: 129.56,
    0.01: 135.81,
    5e-3: 140.17
  }
};
function chiSquaredGoodnessOfFit(data, distributionType, significance) {
  var inputMean = mean(data);
  var chiSquared = 0;
  var c2 = 1;
  var hypothesizedDistribution = distributionType(inputMean);
  var observedFrequencies = [];
  var expectedFrequencies = [];
  for (var i2 = 0; i2 < data.length; i2++) {
    if (observedFrequencies[data[i2]] === void 0) {
      observedFrequencies[data[i2]] = 0;
    }
    observedFrequencies[data[i2]]++;
  }
  for (var i$1 = 0; i$1 < observedFrequencies.length; i$1++) {
    if (observedFrequencies[i$1] === void 0) {
      observedFrequencies[i$1] = 0;
    }
  }
  for (var k2 in hypothesizedDistribution) {
    if (k2 in observedFrequencies) {
      expectedFrequencies[+k2] = hypothesizedDistribution[k2] * data.length;
    }
  }
  for (var k$1 = expectedFrequencies.length - 1; k$1 >= 0; k$1--) {
    if (expectedFrequencies[k$1] < 3) {
      expectedFrequencies[k$1 - 1] += expectedFrequencies[k$1];
      expectedFrequencies.pop();
      observedFrequencies[k$1 - 1] += observedFrequencies[k$1];
      observedFrequencies.pop();
    }
  }
  for (var k$2 = 0; k$2 < observedFrequencies.length; k$2++) {
    chiSquared += Math.pow(observedFrequencies[k$2] - expectedFrequencies[k$2], 2) / expectedFrequencies[k$2];
  }
  var degreesOfFreedom = observedFrequencies.length - c2 - 1;
  return chiSquaredDistributionTable[degreesOfFreedom][significance] < chiSquared;
}
var SQRT_2PI$1 = Math.sqrt(2 * Math.PI);
var kernels = {
  /**
   * The gaussian kernel.
   * @private
   */
  gaussian: function(u2) {
    return Math.exp(-0.5 * u2 * u2) / SQRT_2PI$1;
  }
};
var bandwidthMethods = {
  /**
   * The ["normal reference distribution"
   * rule-of-thumb](https://stat.ethz.ch/R-manual/R-devel/library/MASS/html/bandwidth.nrd.html),
   * a commonly used version of [Silverman's
   * rule-of-thumb](https://en.wikipedia.org/wiki/Kernel_density_estimation#A_rule-of-thumb_bandwidth_estimator).
   * @private
   */
  nrd: function(x3) {
    var s2 = sampleStandardDeviation(x3);
    var iqr = interquartileRange(x3);
    if (typeof iqr === "number") {
      s2 = Math.min(s2, iqr / 1.34);
    }
    return 1.06 * s2 * Math.pow(x3.length, -0.2);
  }
};
function kernelDensityEstimation(X, kernel, bandwidthMethod) {
  var kernelFn;
  if (kernel === void 0) {
    kernelFn = kernels.gaussian;
  } else if (typeof kernel === "string") {
    if (!kernels[kernel]) {
      throw new Error('Unknown kernel "' + kernel + '"');
    }
    kernelFn = kernels[kernel];
  } else {
    kernelFn = kernel;
  }
  var bandwidth;
  if (typeof bandwidthMethod === "undefined") {
    bandwidth = bandwidthMethods.nrd(X);
  } else if (typeof bandwidthMethod === "string") {
    if (!bandwidthMethods[bandwidthMethod]) {
      throw new Error(
        'Unknown bandwidth method "' + bandwidthMethod + '"'
      );
    }
    bandwidth = bandwidthMethods[bandwidthMethod](X);
  } else {
    bandwidth = bandwidthMethod;
  }
  return function(x3) {
    var i2 = 0;
    var sum3 = 0;
    for (i2 = 0; i2 < X.length; i2++) {
      sum3 += kernelFn((x3 - X[i2]) / bandwidth);
    }
    return sum3 / bandwidth / X.length;
  };
}
function zScore(x3, mean2, standardDeviation2) {
  return (x3 - mean2) / standardDeviation2;
}
var SQRT_2PI = Math.sqrt(2 * Math.PI);
function cumulativeDistribution(z2) {
  var sum3 = z2;
  var tmp = z2;
  for (var i2 = 1; i2 < 15; i2++) {
    tmp *= z2 * z2 / (2 * i2 + 1);
    sum3 += tmp;
  }
  return Math.round((0.5 + sum3 / SQRT_2PI * Math.exp(-z2 * z2 / 2)) * 1e4) / 1e4;
}
var standardNormalTable = [];
for (z2 = 0; z2 <= 3.09; z2 += 0.01) {
  standardNormalTable.push(cumulativeDistribution(z2));
}
var z2;
function cumulativeStdNormalProbability(z2) {
  var absZ = Math.abs(z2);
  var index = Math.min(
    Math.round(absZ * 100),
    standardNormalTable.length - 1
  );
  if (z2 >= 0) {
    return standardNormalTable[index];
  } else {
    return Math.round((1 - standardNormalTable[index]) * 1e4) / 1e4;
  }
}
function cumulativeStdLogisticProbability(x3) {
  return 1 / (Math.exp(-x3) + 1);
}
function errorFunction(x3) {
  var t2 = 1 / (1 + 0.5 * Math.abs(x3));
  var tau3 = t2 * Math.exp(
    -x3 * x3 + ((((((((0.17087277 * t2 - 0.82215223) * t2 + 1.48851587) * t2 - 1.13520398) * t2 + 0.27886807) * t2 - 0.18628806) * t2 + 0.09678418) * t2 + 0.37409196) * t2 + 1.00002368) * t2 - 1.26551223
  );
  if (x3 >= 0) {
    return 1 - tau3;
  } else {
    return tau3 - 1;
  }
}
function inverseErrorFunction(x3) {
  var a2 = 8 * (Math.PI - 3) / (3 * Math.PI * (4 - Math.PI));
  var inv = Math.sqrt(
    Math.sqrt(
      Math.pow(2 / (Math.PI * a2) + Math.log(1 - x3 * x3) / 2, 2) - Math.log(1 - x3 * x3) / a2
    ) - (2 / (Math.PI * a2) + Math.log(1 - x3 * x3) / 2)
  );
  if (x3 >= 0) {
    return inv;
  } else {
    return -inv;
  }
}
function probit(p2) {
  if (p2 === 0) {
    p2 = epsilon3;
  } else if (p2 >= 1) {
    p2 = 1 - epsilon3;
  }
  return Math.sqrt(2) * inverseErrorFunction(2 * p2 - 1);
}
function logit(p2) {
  if (p2 <= 0 || p2 >= 1) {
    throw new Error("p must be strictly between zero and one");
  }
  return Math.log(p2 / (1 - p2));
}
function permutationTest(sampleX, sampleY, alternative, k2, randomSource) {
  if (k2 === void 0) {
    k2 = 1e4;
  }
  if (alternative === void 0) {
    alternative = "two_side";
  }
  if (alternative !== "two_side" && alternative !== "greater" && alternative !== "less") {
    throw new Error(
      "`alternative` must be either 'two_side', 'greater', or 'less'."
    );
  }
  var meanX = mean(sampleX);
  var meanY = mean(sampleY);
  var testStatistic = meanX - meanY;
  var testStatDsn = new Array(k2);
  var allData = sampleX.concat(sampleY);
  var midIndex = Math.floor(allData.length / 2);
  for (var i2 = 0; i2 < k2; i2++) {
    shuffleInPlace(allData, randomSource);
    var permLeft = allData.slice(0, midIndex);
    var permRight = allData.slice(midIndex, allData.length);
    var permTestStatistic = mean(permLeft) - mean(permRight);
    testStatDsn[i2] = permTestStatistic;
  }
  var numExtremeTStats = 0;
  if (alternative === "two_side") {
    for (var i$1 = 0; i$1 <= k2; i$1++) {
      if (Math.abs(testStatDsn[i$1]) >= Math.abs(testStatistic)) {
        numExtremeTStats += 1;
      }
    }
  } else if (alternative === "greater") {
    for (var i$2 = 0; i$2 <= k2; i$2++) {
      if (testStatDsn[i$2] >= testStatistic) {
        numExtremeTStats += 1;
      }
    }
  } else {
    for (var i$3 = 0; i$3 <= k2; i$3++) {
      if (testStatDsn[i$3] <= testStatistic) {
        numExtremeTStats += 1;
      }
    }
  }
  return numExtremeTStats / k2;
}
function sign2(x3) {
  if (typeof x3 === "number") {
    if (x3 < 0) {
      return -1;
    } else if (x3 === 0) {
      return 0;
    } else {
      return 1;
    }
  } else {
    throw new TypeError("not a number");
  }
}
function bisect2(func, start, end, maxIterations, errorTolerance) {
  if (typeof func !== "function") {
    throw new TypeError("func must be a function");
  }
  for (var i2 = 0; i2 < maxIterations; i2++) {
    var output = (start + end) / 2;
    if (func(output) === 0 || Math.abs((end - start) / 2) < errorTolerance) {
      return output;
    }
    if (sign2(func(output)) === sign2(func(start))) {
      start = output;
    } else {
      end = output;
    }
  }
  throw new Error("maximum number of iterations exceeded");
}
function euclideanDistance(left, right) {
  var sum3 = 0;
  for (var i2 = 0; i2 < left.length; i2++) {
    var diff = left[i2] - right[i2];
    sum3 += diff * diff;
  }
  return Math.sqrt(sum3);
}
function kMeansCluster(points, numCluster, randomSource) {
  if (randomSource === void 0) randomSource = Math.random;
  var oldCentroids = null;
  var newCentroids = sample(points, numCluster, randomSource);
  var labels = null;
  var change = Number.MAX_VALUE;
  while (change !== 0) {
    labels = labelPoints(points, newCentroids);
    oldCentroids = newCentroids;
    newCentroids = calculateCentroids(points, labels, numCluster);
    change = calculateChange(newCentroids, oldCentroids);
  }
  return {
    labels,
    centroids: newCentroids
  };
}
function labelPoints(points, centroids) {
  return points.map(function(p2) {
    var minDist = Number.MAX_VALUE;
    var label = -1;
    for (var i2 = 0; i2 < centroids.length; i2++) {
      var dist = euclideanDistance(p2, centroids[i2]);
      if (dist < minDist) {
        minDist = dist;
        label = i2;
      }
    }
    return label;
  });
}
function calculateCentroids(points, labels, numCluster) {
  var dimension = points[0].length;
  var centroids = makeMatrix(numCluster, dimension);
  var counts = Array(numCluster).fill(0);
  var numPoints = points.length;
  for (var i2 = 0; i2 < numPoints; i2++) {
    var point2 = points[i2];
    var label = labels[i2];
    var current = centroids[label];
    for (var j2 = 0; j2 < dimension; j2++) {
      current[j2] += point2[j2];
    }
    counts[label] += 1;
  }
  for (var i$1 = 0; i$1 < numCluster; i$1++) {
    if (counts[i$1] === 0) {
      throw new Error("Centroid " + i$1 + " has no friends");
    }
    var centroid = centroids[i$1];
    for (var j$1 = 0; j$1 < dimension; j$1++) {
      centroid[j$1] /= counts[i$1];
    }
  }
  return centroids;
}
function calculateChange(left, right) {
  var total = 0;
  for (var i2 = 0; i2 < left.length; i2++) {
    total += euclideanDistance(left[i2], right[i2]);
  }
  return total;
}
function silhouette(points, labels) {
  if (points.length !== labels.length) {
    throw new Error("must have exactly as many labels as points");
  }
  var groupings = createGroups(labels);
  var distances = calculateAllDistances(points);
  var result = [];
  for (var i2 = 0; i2 < points.length; i2++) {
    var s2 = 0;
    if (groupings[labels[i2]].length > 1) {
      var a2 = meanDistanceFromPointToGroup(
        i2,
        groupings[labels[i2]],
        distances
      );
      var b2 = meanDistanceToNearestGroup(
        i2,
        labels,
        groupings,
        distances
      );
      s2 = (b2 - a2) / Math.max(a2, b2);
    }
    result.push(s2);
  }
  return result;
}
function createGroups(labels) {
  var numGroups = 1 + max2(labels);
  var result = Array(numGroups);
  for (var i2 = 0; i2 < labels.length; i2++) {
    var label = labels[i2];
    if (result[label] === void 0) {
      result[label] = [];
    }
    result[label].push(i2);
  }
  return result;
}
function calculateAllDistances(points) {
  var numPoints = points.length;
  var result = makeMatrix(numPoints, numPoints);
  for (var i2 = 0; i2 < numPoints; i2++) {
    for (var j2 = 0; j2 < i2; j2++) {
      result[i2][j2] = euclideanDistance(points[i2], points[j2]);
      result[j2][i2] = result[i2][j2];
    }
  }
  return result;
}
function meanDistanceToNearestGroup(which, labels, groupings, distances) {
  var label = labels[which];
  var result = Number.MAX_VALUE;
  for (var i2 = 0; i2 < groupings.length; i2++) {
    if (i2 !== label) {
      var d2 = meanDistanceFromPointToGroup(
        which,
        groupings[i2],
        distances
      );
      if (d2 < result) {
        result = d2;
      }
    }
  }
  return result;
}
function meanDistanceFromPointToGroup(which, group, distances) {
  var total = 0;
  for (var i2 = 0; i2 < group.length; i2++) {
    total += distances[which][group[i2]];
  }
  return total / group.length;
}
function silhouetteMetric(points, labels) {
  var values = silhouette(points, labels);
  return max2(values);
}
function relativeError(actual, expected) {
  if (actual === 0 && expected === 0) {
    return 0;
  }
  return Math.abs((actual - expected) / expected);
}
function approxEqual(actual, expected, tolerance) {
  if (tolerance === void 0) tolerance = epsilon3;
  return relativeError(actual, expected) <= tolerance;
}

// node_modules/.pnpm/@turf+clone@6.5.0/node_modules/@turf/clone/dist/es/index.js
function clone2(geojson) {
  if (!geojson) {
    throw new Error("geojson is required");
  }
  switch (geojson.type) {
    case "Feature":
      return cloneFeature(geojson);
    case "FeatureCollection":
      return cloneFeatureCollection(geojson);
    case "Point":
    case "LineString":
    case "Polygon":
    case "MultiPoint":
    case "MultiLineString":
    case "MultiPolygon":
    case "GeometryCollection":
      return cloneGeometry(geojson);
    default:
      throw new Error("unknown GeoJSON type");
  }
}
function cloneFeature(geojson) {
  var cloned = { type: "Feature" };
  Object.keys(geojson).forEach(function(key) {
    switch (key) {
      case "type":
      case "properties":
      case "geometry":
        return;
      default:
        cloned[key] = geojson[key];
    }
  });
  cloned.properties = cloneProperties(geojson.properties);
  cloned.geometry = cloneGeometry(geojson.geometry);
  return cloned;
}
function cloneProperties(properties) {
  var cloned = {};
  if (!properties) {
    return cloned;
  }
  Object.keys(properties).forEach(function(key) {
    var value = properties[key];
    if (typeof value === "object") {
      if (value === null) {
        cloned[key] = null;
      } else if (Array.isArray(value)) {
        cloned[key] = value.map(function(item) {
          return item;
        });
      } else {
        cloned[key] = cloneProperties(value);
      }
    } else {
      cloned[key] = value;
    }
  });
  return cloned;
}
function cloneFeatureCollection(geojson) {
  var cloned = { type: "FeatureCollection" };
  Object.keys(geojson).forEach(function(key) {
    switch (key) {
      case "type":
      case "features":
        return;
      default:
        cloned[key] = geojson[key];
    }
  });
  cloned.features = geojson.features.map(function(feature3) {
    return cloneFeature(feature3);
  });
  return cloned;
}
function cloneGeometry(geometry) {
  var geom = { type: geometry.type };
  if (geometry.bbox) {
    geom.bbox = geometry.bbox;
  }
  if (geometry.type === "GeometryCollection") {
    geom.geometries = geometry.geometries.map(function(g3) {
      return cloneGeometry(g3);
    });
    return geom;
  }
  geom.coordinates = deepSlice(geometry.coordinates);
  return geom;
}
function deepSlice(coords) {
  var cloned = coords;
  if (typeof cloned[0] !== "object") {
    return cloned.slice();
  }
  return cloned.map(function(coord) {
    return deepSlice(coord);
  });
}
var es_default = clone2;

// node_modules/.pnpm/@turf+invariant@6.5.0/node_modules/@turf/invariant/dist/es/index.js
function getCoords(coords) {
  if (Array.isArray(coords)) {
    return coords;
  }
  if (coords.type === "Feature") {
    if (coords.geometry !== null) {
      return coords.geometry.coordinates;
    }
  } else {
    if (coords.coordinates) {
      return coords.coordinates;
    }
  }
  throw new Error("coords must be GeoJSON Feature, Geometry Object or an Array");
}

// node_modules/.pnpm/@turf+boolean-clockwise@6.5.0/node_modules/@turf/boolean-clockwise/dist/es/index.js
function booleanClockwise(line) {
  var ring = getCoords(line);
  var sum3 = 0;
  var i2 = 1;
  var prev;
  var cur;
  while (i2 < ring.length) {
    prev = cur || ring[0];
    cur = ring[i2];
    sum3 += (cur[0] - prev[0]) * (cur[1] + prev[1]);
    i2++;
  }
  return sum3 > 0;
}

// node_modules/.pnpm/@turf+meta@6.5.0/node_modules/@turf/meta/dist/es/index.js
function featureEach(geojson, callback) {
  if (geojson.type === "Feature") {
    callback(geojson, 0);
  } else if (geojson.type === "FeatureCollection") {
    for (var i2 = 0; i2 < geojson.features.length; i2++) {
      if (callback(geojson.features[i2], i2) === false) break;
    }
  }
}
function geomEach(geojson, callback) {
  var i2, j2, g3, geometry, stopG, geometryMaybeCollection, isGeometryCollection, featureProperties, featureBBox, featureId, featureIndex = 0, isFeatureCollection = geojson.type === "FeatureCollection", isFeature = geojson.type === "Feature", stop = isFeatureCollection ? geojson.features.length : 1;
  for (i2 = 0; i2 < stop; i2++) {
    geometryMaybeCollection = isFeatureCollection ? geojson.features[i2].geometry : isFeature ? geojson.geometry : geojson;
    featureProperties = isFeatureCollection ? geojson.features[i2].properties : isFeature ? geojson.properties : {};
    featureBBox = isFeatureCollection ? geojson.features[i2].bbox : isFeature ? geojson.bbox : void 0;
    featureId = isFeatureCollection ? geojson.features[i2].id : isFeature ? geojson.id : void 0;
    isGeometryCollection = geometryMaybeCollection ? geometryMaybeCollection.type === "GeometryCollection" : false;
    stopG = isGeometryCollection ? geometryMaybeCollection.geometries.length : 1;
    for (g3 = 0; g3 < stopG; g3++) {
      geometry = isGeometryCollection ? geometryMaybeCollection.geometries[g3] : geometryMaybeCollection;
      if (geometry === null) {
        if (callback(
          null,
          featureIndex,
          featureProperties,
          featureBBox,
          featureId
        ) === false)
          return false;
        continue;
      }
      switch (geometry.type) {
        case "Point":
        case "LineString":
        case "MultiPoint":
        case "Polygon":
        case "MultiLineString":
        case "MultiPolygon": {
          if (callback(
            geometry,
            featureIndex,
            featureProperties,
            featureBBox,
            featureId
          ) === false)
            return false;
          break;
        }
        case "GeometryCollection": {
          for (j2 = 0; j2 < geometry.geometries.length; j2++) {
            if (callback(
              geometry.geometries[j2],
              featureIndex,
              featureProperties,
              featureBBox,
              featureId
            ) === false)
              return false;
          }
          break;
        }
        default:
          throw new Error("Unknown Geometry Type");
      }
    }
    featureIndex++;
  }
}
function flattenEach(geojson, callback) {
  geomEach(geojson, function(geometry, featureIndex, properties, bbox, id) {
    var type = geometry === null ? null : geometry.type;
    switch (type) {
      case null:
      case "Point":
      case "LineString":
      case "Polygon":
        if (callback(
          feature(geometry, properties, { bbox, id }),
          featureIndex,
          0
        ) === false)
          return false;
        return;
    }
    var geomType;
    switch (type) {
      case "MultiPoint":
        geomType = "Point";
        break;
      case "MultiLineString":
        geomType = "LineString";
        break;
      case "MultiPolygon":
        geomType = "Polygon";
        break;
    }
    for (var multiFeatureIndex = 0; multiFeatureIndex < geometry.coordinates.length; multiFeatureIndex++) {
      var coordinate = geometry.coordinates[multiFeatureIndex];
      var geom = {
        type: geomType,
        coordinates: coordinate
      };
      if (callback(feature(geom, properties), featureIndex, multiFeatureIndex) === false)
        return false;
    }
  });
}

// node_modules/.pnpm/@turf+rewind@6.5.0/node_modules/@turf/rewind/dist/es/index.js
function rewind(geojson, options) {
  options = options || {};
  if (!isObject2(options)) throw new Error("options is invalid");
  var reverse2 = options.reverse || false;
  var mutate = options.mutate || false;
  if (!geojson) throw new Error("<geojson> is required");
  if (typeof reverse2 !== "boolean")
    throw new Error("<reverse> must be a boolean");
  if (typeof mutate !== "boolean")
    throw new Error("<mutate> must be a boolean");
  if (mutate === false) geojson = es_default(geojson);
  var results = [];
  switch (geojson.type) {
    case "GeometryCollection":
      geomEach(geojson, function(geometry) {
        rewindFeature(geometry, reverse2);
      });
      return geojson;
    case "FeatureCollection":
      featureEach(geojson, function(feature3) {
        featureEach(rewindFeature(feature3, reverse2), function(result) {
          results.push(result);
        });
      });
      return featureCollection(results);
  }
  return rewindFeature(geojson, reverse2);
}
function rewindFeature(geojson, reverse2) {
  var type = geojson.type === "Feature" ? geojson.geometry.type : geojson.type;
  switch (type) {
    case "GeometryCollection":
      geomEach(geojson, function(geometry) {
        rewindFeature(geometry, reverse2);
      });
      return geojson;
    case "LineString":
      rewindLineString(getCoords(geojson), reverse2);
      return geojson;
    case "Polygon":
      rewindPolygon(getCoords(geojson), reverse2);
      return geojson;
    case "MultiLineString":
      getCoords(geojson).forEach(function(lineCoords) {
        rewindLineString(lineCoords, reverse2);
      });
      return geojson;
    case "MultiPolygon":
      getCoords(geojson).forEach(function(lineCoords) {
        rewindPolygon(lineCoords, reverse2);
      });
      return geojson;
    case "Point":
    case "MultiPoint":
      return geojson;
  }
}
function rewindLineString(coords, reverse2) {
  if (booleanClockwise(coords) === reverse2) coords.reverse();
}
function rewindPolygon(coords, reverse2) {
  if (booleanClockwise(coords[0]) !== reverse2) {
    coords[0].reverse();
  }
  for (var i2 = 1; i2 < coords.length; i2++) {
    if (booleanClockwise(coords[i2]) === reverse2) {
      coords[i2].reverse();
    }
  }
}
var es_default2 = rewind;

// node_modules/.pnpm/@turf+flatten@6.5.0/node_modules/@turf/flatten/dist/es/index.js
function flatten(geojson) {
  if (!geojson) throw new Error("geojson is required");
  var results = [];
  flattenEach(geojson, function(feature3) {
    results.push(feature3);
  });
  return featureCollection(results);
}
var es_default3 = flatten;

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/count.js
function count(node) {
  var sum3 = 0, children = node.children, i2 = children && children.length;
  if (!i2) sum3 = 1;
  else while (--i2 >= 0) sum3 += children[i2].value;
  node.value = sum3;
}
function count_default() {
  return this.eachAfter(count);
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/each.js
function each_default(callback, that) {
  let index = -1;
  for (const node of this) {
    callback.call(that, node, ++index, this);
  }
  return this;
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/eachBefore.js
function eachBefore_default(callback, that) {
  var node = this, nodes = [node], children, i2, index = -1;
  while (node = nodes.pop()) {
    callback.call(that, node, ++index, this);
    if (children = node.children) {
      for (i2 = children.length - 1; i2 >= 0; --i2) {
        nodes.push(children[i2]);
      }
    }
  }
  return this;
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/eachAfter.js
function eachAfter_default(callback, that) {
  var node = this, nodes = [node], next = [], children, i2, n2, index = -1;
  while (node = nodes.pop()) {
    next.push(node);
    if (children = node.children) {
      for (i2 = 0, n2 = children.length; i2 < n2; ++i2) {
        nodes.push(children[i2]);
      }
    }
  }
  while (node = next.pop()) {
    callback.call(that, node, ++index, this);
  }
  return this;
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/find.js
function find_default(callback, that) {
  let index = -1;
  for (const node of this) {
    if (callback.call(that, node, ++index, this)) {
      return node;
    }
  }
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/sum.js
function sum_default2(value) {
  return this.eachAfter(function(node) {
    var sum3 = +value(node.data) || 0, children = node.children, i2 = children && children.length;
    while (--i2 >= 0) sum3 += children[i2].value;
    node.value = sum3;
  });
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/sort.js
function sort_default(compare2) {
  return this.eachBefore(function(node) {
    if (node.children) {
      node.children.sort(compare2);
    }
  });
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/path.js
function path_default2(end) {
  var start = this, ancestor = leastCommonAncestor(start, end), nodes = [start];
  while (start !== ancestor) {
    start = start.parent;
    nodes.push(start);
  }
  var k2 = nodes.length;
  while (end !== ancestor) {
    nodes.splice(k2, 0, end);
    end = end.parent;
  }
  return nodes;
}
function leastCommonAncestor(a2, b2) {
  if (a2 === b2) return a2;
  var aNodes = a2.ancestors(), bNodes = b2.ancestors(), c2 = null;
  a2 = aNodes.pop();
  b2 = bNodes.pop();
  while (a2 === b2) {
    c2 = a2;
    a2 = aNodes.pop();
    b2 = bNodes.pop();
  }
  return c2;
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/ancestors.js
function ancestors_default() {
  var node = this, nodes = [node];
  while (node = node.parent) {
    nodes.push(node);
  }
  return nodes;
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/descendants.js
function descendants_default() {
  return Array.from(this);
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/leaves.js
function leaves_default() {
  var leaves = [];
  this.eachBefore(function(node) {
    if (!node.children) {
      leaves.push(node);
    }
  });
  return leaves;
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/links.js
function links_default() {
  var root = this, links = [];
  root.each(function(node) {
    if (node !== root) {
      links.push({ source: node.parent, target: node });
    }
  });
  return links;
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/iterator.js
function* iterator_default() {
  var node = this, current, next = [node], children, i2, n2;
  do {
    current = next.reverse(), next = [];
    while (node = current.pop()) {
      yield node;
      if (children = node.children) {
        for (i2 = 0, n2 = children.length; i2 < n2; ++i2) {
          next.push(children[i2]);
        }
      }
    }
  } while (next.length);
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/hierarchy/index.js
function hierarchy(data, children) {
  if (data instanceof Map) {
    data = [void 0, data];
    if (children === void 0) children = mapChildren;
  } else if (children === void 0) {
    children = objectChildren;
  }
  var root = new Node(data), node, nodes = [root], child, childs, i2, n2;
  while (node = nodes.pop()) {
    if ((childs = children(node.data)) && (n2 = (childs = Array.from(childs)).length)) {
      node.children = childs;
      for (i2 = n2 - 1; i2 >= 0; --i2) {
        nodes.push(child = childs[i2] = new Node(childs[i2]));
        child.parent = node;
        child.depth = node.depth + 1;
      }
    }
  }
  return root.eachBefore(computeHeight);
}
function node_copy() {
  return hierarchy(this).eachBefore(copyData);
}
function objectChildren(d2) {
  return d2.children;
}
function mapChildren(d2) {
  return Array.isArray(d2) ? d2[1] : null;
}
function copyData(node) {
  if (node.data.value !== void 0) node.value = node.data.value;
  node.data = node.data.data;
}
function computeHeight(node) {
  var height = 0;
  do
    node.height = height;
  while ((node = node.parent) && node.height < ++height);
}
function Node(data) {
  this.data = data;
  this.depth = this.height = 0;
  this.parent = null;
}
Node.prototype = hierarchy.prototype = {
  constructor: Node,
  count: count_default,
  each: each_default,
  eachAfter: eachAfter_default,
  eachBefore: eachBefore_default,
  find: find_default,
  sum: sum_default2,
  sort: sort_default,
  path: path_default2,
  ancestors: ancestors_default,
  descendants: descendants_default,
  leaves: leaves_default,
  links: links_default,
  copy: node_copy,
  [Symbol.iterator]: iterator_default
};

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/treemap/dice.js
function dice_default(parent, x05, y05, x13, y13) {
  var nodes = parent.children, node, i2 = -1, n2 = nodes.length, k2 = parent.value && (x13 - x05) / parent.value;
  while (++i2 < n2) {
    node = nodes[i2], node.y0 = y05, node.y1 = y13;
    node.x0 = x05, node.x1 = x05 += node.value * k2;
  }
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/tree.js
function TreeNode(node, i2) {
  this._ = node;
  this.parent = null;
  this.children = null;
  this.A = null;
  this.a = this;
  this.z = 0;
  this.m = 0;
  this.c = 0;
  this.s = 0;
  this.t = null;
  this.i = i2;
}
TreeNode.prototype = Object.create(Node.prototype);

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/treemap/slice.js
function slice_default(parent, x05, y05, x13, y13) {
  var nodes = parent.children, node, i2 = -1, n2 = nodes.length, k2 = parent.value && (y13 - y05) / parent.value;
  while (++i2 < n2) {
    node = nodes[i2], node.x0 = x05, node.x1 = x13;
    node.y0 = y05, node.y1 = y05 += node.value * k2;
  }
}

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/treemap/squarify.js
var phi = (1 + Math.sqrt(5)) / 2;
function squarifyRatio(ratio, parent, x05, y05, x13, y13) {
  var rows = [], nodes = parent.children, row, nodeValue, i0 = 0, i1 = 0, n2 = nodes.length, dx, dy, value = parent.value, sumValue, minValue, maxValue, newRatio, minRatio, alpha, beta;
  while (i0 < n2) {
    dx = x13 - x05, dy = y13 - y05;
    do
      sumValue = nodes[i1++].value;
    while (!sumValue && i1 < n2);
    minValue = maxValue = sumValue;
    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);
    beta = sumValue * sumValue * alpha;
    minRatio = Math.max(maxValue / beta, beta / minValue);
    for (; i1 < n2; ++i1) {
      sumValue += nodeValue = nodes[i1].value;
      if (nodeValue < minValue) minValue = nodeValue;
      if (nodeValue > maxValue) maxValue = nodeValue;
      beta = sumValue * sumValue * alpha;
      newRatio = Math.max(maxValue / beta, beta / minValue);
      if (newRatio > minRatio) {
        sumValue -= nodeValue;
        break;
      }
      minRatio = newRatio;
    }
    rows.push(row = { value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1) });
    if (row.dice) dice_default(row, x05, y05, x13, value ? y05 += dy * sumValue / value : y13);
    else slice_default(row, x05, y05, value ? x05 += dx * sumValue / value : x13, y13);
    value -= sumValue, i0 = i1;
  }
  return rows;
}
var squarify_default = function custom(ratio) {
  function squarify(parent, x05, y05, x13, y13) {
    squarifyRatio(ratio, parent, x05, y05, x13, y13);
  }
  squarify.ratio = function(x3) {
    return custom((x3 = +x3) > 1 ? x3 : 1);
  };
  return squarify;
}(phi);

// node_modules/.pnpm/d3-hierarchy@3.1.2/node_modules/d3-hierarchy/src/treemap/resquarify.js
var resquarify_default = function custom2(ratio) {
  function resquarify(parent, x05, y05, x13, y13) {
    if ((rows = parent._squarify) && rows.ratio === ratio) {
      var rows, row, nodes, i2, j2 = -1, n2, m2 = rows.length, value = parent.value;
      while (++j2 < m2) {
        row = rows[j2], nodes = row.children;
        for (i2 = row.value = 0, n2 = nodes.length; i2 < n2; ++i2) row.value += nodes[i2].value;
        if (row.dice) dice_default(row, x05, y05, x13, value ? y05 += (y13 - y05) * row.value / value : y13);
        else slice_default(row, x05, y05, value ? x05 += (x13 - x05) * row.value / value : x13, y13);
        value -= row.value;
      }
    } else {
      parent._squarify = rows = squarifyRatio(ratio, parent, x05, y05, x13, y13);
      rows.ratio = ratio;
    }
  }
  resquarify.ratio = function(x3) {
    return custom2((x3 = +x3) > 1 ? x3 : 1);
  };
  return resquarify;
}(phi);

// node_modules/.pnpm/roughjs@4.5.2/node_modules/roughjs/bundled/rough.esm.js
function t(t2, e3, s2) {
  if (t2 && t2.length) {
    const [n2, a2] = e3, o2 = Math.PI / 180 * s2, h2 = Math.cos(o2), r2 = Math.sin(o2);
    t2.forEach((t3) => {
      const [e4, s3] = t3;
      t3[0] = (e4 - n2) * h2 - (s3 - a2) * r2 + n2, t3[1] = (e4 - n2) * r2 + (s3 - a2) * h2 + a2;
    });
  }
}
function e(t2) {
  const e3 = t2[0], s2 = t2[1];
  return Math.sqrt(Math.pow(e3[0] - s2[0], 2) + Math.pow(e3[1] - s2[1], 2));
}
function s(e3, s2) {
  const n2 = s2.hachureAngle + 90;
  let a2 = s2.hachureGap;
  a2 < 0 && (a2 = 4 * s2.strokeWidth), a2 = Math.max(a2, 0.1);
  const o2 = [0, 0];
  if (n2) for (const s3 of e3) t(s3, o2, n2);
  const h2 = function(t2, e4) {
    const s3 = [];
    for (const e6 of t2) {
      const t3 = [...e6];
      t3[0].join(",") !== t3[t3.length - 1].join(",") && t3.push([t3[0][0], t3[0][1]]), t3.length > 2 && s3.push(t3);
    }
    const n3 = [];
    e4 = Math.max(e4, 0.1);
    const a3 = [];
    for (const t3 of s3) for (let e6 = 0; e6 < t3.length - 1; e6++) {
      const s4 = t3[e6], n4 = t3[e6 + 1];
      if (s4[1] !== n4[1]) {
        const t4 = Math.min(s4[1], n4[1]);
        a3.push({ ymin: t4, ymax: Math.max(s4[1], n4[1]), x: t4 === s4[1] ? s4[0] : n4[0], islope: (n4[0] - s4[0]) / (n4[1] - s4[1]) });
      }
    }
    if (a3.sort((t3, e6) => t3.ymin < e6.ymin ? -1 : t3.ymin > e6.ymin ? 1 : t3.x < e6.x ? -1 : t3.x > e6.x ? 1 : t3.ymax === e6.ymax ? 0 : (t3.ymax - e6.ymax) / Math.abs(t3.ymax - e6.ymax)), !a3.length) return n3;
    let o3 = [], h3 = a3[0].ymin;
    for (; o3.length || a3.length; ) {
      if (a3.length) {
        let t3 = -1;
        for (let e6 = 0; e6 < a3.length && !(a3[e6].ymin > h3); e6++) t3 = e6;
        a3.splice(0, t3 + 1).forEach((t4) => {
          o3.push({ s: h3, edge: t4 });
        });
      }
      if (o3 = o3.filter((t3) => !(t3.edge.ymax <= h3)), o3.sort((t3, e6) => t3.edge.x === e6.edge.x ? 0 : (t3.edge.x - e6.edge.x) / Math.abs(t3.edge.x - e6.edge.x)), o3.length > 1) for (let t3 = 0; t3 < o3.length; t3 += 2) {
        const e6 = t3 + 1;
        if (e6 >= o3.length) break;
        const s4 = o3[t3].edge, a4 = o3[e6].edge;
        n3.push([[Math.round(s4.x), h3], [Math.round(a4.x), h3]]);
      }
      h3 += e4, o3.forEach((t3) => {
        t3.edge.x = t3.edge.x + e4 * t3.edge.islope;
      });
    }
    return n3;
  }(e3, a2);
  if (n2) {
    for (const s3 of e3) t(s3, o2, -n2);
    !function(e4, s3, n3) {
      const a3 = [];
      e4.forEach((t2) => a3.push(...t2)), t(a3, s3, n3);
    }(h2, o2, -n2);
  }
  return h2;
}
var n = class {
  constructor(t2) {
    this.helper = t2;
  }
  fillPolygons(t2, e3) {
    return this._fillPolygons(t2, e3);
  }
  _fillPolygons(t2, e3) {
    const n2 = s(t2, e3);
    return { type: "fillSketch", ops: this.renderLines(n2, e3) };
  }
  renderLines(t2, e3) {
    const s2 = [];
    for (const n2 of t2) s2.push(...this.helper.doubleLineOps(n2[0][0], n2[0][1], n2[1][0], n2[1][1], e3));
    return s2;
  }
};
var a = class extends n {
  fillPolygons(t2, n2) {
    let a2 = n2.hachureGap;
    a2 < 0 && (a2 = 4 * n2.strokeWidth), a2 = Math.max(a2, 0.1);
    const o2 = s(t2, Object.assign({}, n2, { hachureGap: a2 })), h2 = Math.PI / 180 * n2.hachureAngle, r2 = [], i2 = 0.5 * a2 * Math.cos(h2), c2 = 0.5 * a2 * Math.sin(h2);
    for (const [t3, s2] of o2) e([t3, s2]) && r2.push([[t3[0] - i2, t3[1] + c2], [...s2]], [[t3[0] + i2, t3[1] - c2], [...s2]]);
    return { type: "fillSketch", ops: this.renderLines(r2, n2) };
  }
};
var o = class extends n {
  fillPolygons(t2, e3) {
    const s2 = this._fillPolygons(t2, e3), n2 = Object.assign({}, e3, { hachureAngle: e3.hachureAngle + 90 }), a2 = this._fillPolygons(t2, n2);
    return s2.ops = s2.ops.concat(a2.ops), s2;
  }
};
var h = class {
  constructor(t2) {
    this.helper = t2;
  }
  fillPolygons(t2, e3) {
    const n2 = s(t2, e3 = Object.assign({}, e3, { hachureAngle: 0 }));
    return this.dotsOnLines(n2, e3);
  }
  dotsOnLines(t2, s2) {
    const n2 = [];
    let a2 = s2.hachureGap;
    a2 < 0 && (a2 = 4 * s2.strokeWidth), a2 = Math.max(a2, 0.1);
    let o2 = s2.fillWeight;
    o2 < 0 && (o2 = s2.strokeWidth / 2);
    const h2 = a2 / 4;
    for (const r2 of t2) {
      const t3 = e(r2), i2 = t3 / a2, c2 = Math.ceil(i2) - 1, l2 = t3 - c2 * a2, u2 = (r2[0][0] + r2[1][0]) / 2 - a2 / 4, p2 = Math.min(r2[0][1], r2[1][1]);
      for (let t4 = 0; t4 < c2; t4++) {
        const e3 = p2 + l2 + t4 * a2, r3 = u2 - h2 + 2 * Math.random() * h2, i3 = e3 - h2 + 2 * Math.random() * h2, c3 = this.helper.ellipse(r3, i3, o2, o2, s2);
        n2.push(...c3.ops);
      }
    }
    return { type: "fillSketch", ops: n2 };
  }
};
var r = class {
  constructor(t2) {
    this.helper = t2;
  }
  fillPolygons(t2, e3) {
    const n2 = s(t2, e3);
    return { type: "fillSketch", ops: this.dashedLine(n2, e3) };
  }
  dashedLine(t2, s2) {
    const n2 = s2.dashOffset < 0 ? s2.hachureGap < 0 ? 4 * s2.strokeWidth : s2.hachureGap : s2.dashOffset, a2 = s2.dashGap < 0 ? s2.hachureGap < 0 ? 4 * s2.strokeWidth : s2.hachureGap : s2.dashGap, o2 = [];
    return t2.forEach((t3) => {
      const h2 = e(t3), r2 = Math.floor(h2 / (n2 + a2)), i2 = (h2 + a2 - r2 * (n2 + a2)) / 2;
      let c2 = t3[0], l2 = t3[1];
      c2[0] > l2[0] && (c2 = t3[1], l2 = t3[0]);
      const u2 = Math.atan((l2[1] - c2[1]) / (l2[0] - c2[0]));
      for (let t4 = 0; t4 < r2; t4++) {
        const e3 = t4 * (n2 + a2), h3 = e3 + n2, r3 = [c2[0] + e3 * Math.cos(u2) + i2 * Math.cos(u2), c2[1] + e3 * Math.sin(u2) + i2 * Math.sin(u2)], l3 = [c2[0] + h3 * Math.cos(u2) + i2 * Math.cos(u2), c2[1] + h3 * Math.sin(u2) + i2 * Math.sin(u2)];
        o2.push(...this.helper.doubleLineOps(r3[0], r3[1], l3[0], l3[1], s2));
      }
    }), o2;
  }
};
var i = class {
  constructor(t2) {
    this.helper = t2;
  }
  fillPolygons(t2, e3) {
    const n2 = e3.hachureGap < 0 ? 4 * e3.strokeWidth : e3.hachureGap, a2 = e3.zigzagOffset < 0 ? n2 : e3.zigzagOffset, o2 = s(t2, e3 = Object.assign({}, e3, { hachureGap: n2 + a2 }));
    return { type: "fillSketch", ops: this.zigzagLines(o2, a2, e3) };
  }
  zigzagLines(t2, s2, n2) {
    const a2 = [];
    return t2.forEach((t3) => {
      const o2 = e(t3), h2 = Math.round(o2 / (2 * s2));
      let r2 = t3[0], i2 = t3[1];
      r2[0] > i2[0] && (r2 = t3[1], i2 = t3[0]);
      const c2 = Math.atan((i2[1] - r2[1]) / (i2[0] - r2[0]));
      for (let t4 = 0; t4 < h2; t4++) {
        const e3 = 2 * t4 * s2, o3 = 2 * (t4 + 1) * s2, h3 = Math.sqrt(2 * Math.pow(s2, 2)), i3 = [r2[0] + e3 * Math.cos(c2), r2[1] + e3 * Math.sin(c2)], l2 = [r2[0] + o3 * Math.cos(c2), r2[1] + o3 * Math.sin(c2)], u2 = [i3[0] + h3 * Math.cos(c2 + Math.PI / 4), i3[1] + h3 * Math.sin(c2 + Math.PI / 4)];
        a2.push(...this.helper.doubleLineOps(i3[0], i3[1], u2[0], u2[1], n2), ...this.helper.doubleLineOps(u2[0], u2[1], l2[0], l2[1], n2));
      }
    }), a2;
  }
};
var c = {};
var l = class {
  constructor(t2) {
    this.seed = t2;
  }
  next() {
    return this.seed ? (2 ** 31 - 1 & (this.seed = Math.imul(48271, this.seed))) / 2 ** 31 : Math.random();
  }
};
var u = { A: 7, a: 7, C: 6, c: 6, H: 1, h: 1, L: 2, l: 2, M: 2, m: 2, Q: 4, q: 4, S: 4, s: 4, T: 2, t: 2, V: 1, v: 1, Z: 0, z: 0 };
function p(t2, e3) {
  return t2.type === e3;
}
function f(t2) {
  const e3 = [], s2 = function(t3) {
    const e4 = new Array();
    for (; "" !== t3; ) if (t3.match(/^([ \t\r\n,]+)/)) t3 = t3.substr(RegExp.$1.length);
    else if (t3.match(/^([aAcChHlLmMqQsStTvVzZ])/)) e4[e4.length] = { type: 0, text: RegExp.$1 }, t3 = t3.substr(RegExp.$1.length);
    else {
      if (!t3.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/)) return [];
      e4[e4.length] = { type: 1, text: `${parseFloat(RegExp.$1)}` }, t3 = t3.substr(RegExp.$1.length);
    }
    return e4[e4.length] = { type: 2, text: "" }, e4;
  }(t2);
  let n2 = "BOD", a2 = 0, o2 = s2[a2];
  for (; !p(o2, 2); ) {
    let h2 = 0;
    const r2 = [];
    if ("BOD" === n2) {
      if ("M" !== o2.text && "m" !== o2.text) return f("M0,0" + t2);
      a2++, h2 = u[o2.text], n2 = o2.text;
    } else p(o2, 1) ? h2 = u[n2] : (a2++, h2 = u[o2.text], n2 = o2.text);
    if (!(a2 + h2 < s2.length)) throw new Error("Path data ended short");
    for (let t3 = a2; t3 < a2 + h2; t3++) {
      const e4 = s2[t3];
      if (!p(e4, 1)) throw new Error("Param not a number: " + n2 + "," + e4.text);
      r2[r2.length] = +e4.text;
    }
    if ("number" != typeof u[n2]) throw new Error("Bad segment: " + n2);
    {
      const t3 = { key: n2, data: r2 };
      e3.push(t3), a2 += h2, o2 = s2[a2], "M" === n2 && (n2 = "L"), "m" === n2 && (n2 = "l");
    }
  }
  return e3;
}
function d(t2) {
  let e3 = 0, s2 = 0, n2 = 0, a2 = 0;
  const o2 = [];
  for (const { key: h2, data: r2 } of t2) switch (h2) {
    case "M":
      o2.push({ key: "M", data: [...r2] }), [e3, s2] = r2, [n2, a2] = r2;
      break;
    case "m":
      e3 += r2[0], s2 += r2[1], o2.push({ key: "M", data: [e3, s2] }), n2 = e3, a2 = s2;
      break;
    case "L":
      o2.push({ key: "L", data: [...r2] }), [e3, s2] = r2;
      break;
    case "l":
      e3 += r2[0], s2 += r2[1], o2.push({ key: "L", data: [e3, s2] });
      break;
    case "C":
      o2.push({ key: "C", data: [...r2] }), e3 = r2[4], s2 = r2[5];
      break;
    case "c": {
      const t3 = r2.map((t4, n3) => n3 % 2 ? t4 + s2 : t4 + e3);
      o2.push({ key: "C", data: t3 }), e3 = t3[4], s2 = t3[5];
      break;
    }
    case "Q":
      o2.push({ key: "Q", data: [...r2] }), e3 = r2[2], s2 = r2[3];
      break;
    case "q": {
      const t3 = r2.map((t4, n3) => n3 % 2 ? t4 + s2 : t4 + e3);
      o2.push({ key: "Q", data: t3 }), e3 = t3[2], s2 = t3[3];
      break;
    }
    case "A":
      o2.push({ key: "A", data: [...r2] }), e3 = r2[5], s2 = r2[6];
      break;
    case "a":
      e3 += r2[5], s2 += r2[6], o2.push({ key: "A", data: [r2[0], r2[1], r2[2], r2[3], r2[4], e3, s2] });
      break;
    case "H":
      o2.push({ key: "H", data: [...r2] }), e3 = r2[0];
      break;
    case "h":
      e3 += r2[0], o2.push({ key: "H", data: [e3] });
      break;
    case "V":
      o2.push({ key: "V", data: [...r2] }), s2 = r2[0];
      break;
    case "v":
      s2 += r2[0], o2.push({ key: "V", data: [s2] });
      break;
    case "S":
      o2.push({ key: "S", data: [...r2] }), e3 = r2[2], s2 = r2[3];
      break;
    case "s": {
      const t3 = r2.map((t4, n3) => n3 % 2 ? t4 + s2 : t4 + e3);
      o2.push({ key: "S", data: t3 }), e3 = t3[2], s2 = t3[3];
      break;
    }
    case "T":
      o2.push({ key: "T", data: [...r2] }), e3 = r2[0], s2 = r2[1];
      break;
    case "t":
      e3 += r2[0], s2 += r2[1], o2.push({ key: "T", data: [e3, s2] });
      break;
    case "Z":
    case "z":
      o2.push({ key: "Z", data: [] }), e3 = n2, s2 = a2;
  }
  return o2;
}
function g2(t2) {
  const e3 = [];
  let s2 = "", n2 = 0, a2 = 0, o2 = 0, h2 = 0, r2 = 0, i2 = 0;
  for (const { key: c2, data: l2 } of t2) {
    switch (c2) {
      case "M":
        e3.push({ key: "M", data: [...l2] }), [n2, a2] = l2, [o2, h2] = l2;
        break;
      case "C":
        e3.push({ key: "C", data: [...l2] }), n2 = l2[4], a2 = l2[5], r2 = l2[2], i2 = l2[3];
        break;
      case "L":
        e3.push({ key: "L", data: [...l2] }), [n2, a2] = l2;
        break;
      case "H":
        n2 = l2[0], e3.push({ key: "L", data: [n2, a2] });
        break;
      case "V":
        a2 = l2[0], e3.push({ key: "L", data: [n2, a2] });
        break;
      case "S": {
        let t3 = 0, o3 = 0;
        "C" === s2 || "S" === s2 ? (t3 = n2 + (n2 - r2), o3 = a2 + (a2 - i2)) : (t3 = n2, o3 = a2), e3.push({ key: "C", data: [t3, o3, ...l2] }), r2 = l2[0], i2 = l2[1], n2 = l2[2], a2 = l2[3];
        break;
      }
      case "T": {
        const [t3, o3] = l2;
        let h3 = 0, c3 = 0;
        "Q" === s2 || "T" === s2 ? (h3 = n2 + (n2 - r2), c3 = a2 + (a2 - i2)) : (h3 = n2, c3 = a2);
        const u2 = n2 + 2 * (h3 - n2) / 3, p2 = a2 + 2 * (c3 - a2) / 3, f2 = t3 + 2 * (h3 - t3) / 3, d2 = o3 + 2 * (c3 - o3) / 3;
        e3.push({ key: "C", data: [u2, p2, f2, d2, t3, o3] }), r2 = h3, i2 = c3, n2 = t3, a2 = o3;
        break;
      }
      case "Q": {
        const [t3, s3, o3, h3] = l2, c3 = n2 + 2 * (t3 - n2) / 3, u2 = a2 + 2 * (s3 - a2) / 3, p2 = o3 + 2 * (t3 - o3) / 3, f2 = h3 + 2 * (s3 - h3) / 3;
        e3.push({ key: "C", data: [c3, u2, p2, f2, o3, h3] }), r2 = t3, i2 = s3, n2 = o3, a2 = h3;
        break;
      }
      case "A": {
        const t3 = Math.abs(l2[0]), s3 = Math.abs(l2[1]), o3 = l2[2], h3 = l2[3], r3 = l2[4], i3 = l2[5], c3 = l2[6];
        if (0 === t3 || 0 === s3) e3.push({ key: "C", data: [n2, a2, i3, c3, i3, c3] }), n2 = i3, a2 = c3;
        else if (n2 !== i3 || a2 !== c3) {
          k(n2, a2, i3, c3, t3, s3, o3, h3, r3).forEach(function(t4) {
            e3.push({ key: "C", data: t4 });
          }), n2 = i3, a2 = c3;
        }
        break;
      }
      case "Z":
        e3.push({ key: "Z", data: [] }), n2 = o2, a2 = h2;
    }
    s2 = c2;
  }
  return e3;
}
function M2(t2, e3, s2) {
  return [t2 * Math.cos(s2) - e3 * Math.sin(s2), t2 * Math.sin(s2) + e3 * Math.cos(s2)];
}
function k(t2, e3, s2, n2, a2, o2, h2, r2, i2, c2) {
  const l2 = (u2 = h2, Math.PI * u2 / 180);
  var u2;
  let p2 = [], f2 = 0, d2 = 0, g3 = 0, b2 = 0;
  if (c2) [f2, d2, g3, b2] = c2;
  else {
    [t2, e3] = M2(t2, e3, -l2), [s2, n2] = M2(s2, n2, -l2);
    const h3 = (t2 - s2) / 2, c3 = (e3 - n2) / 2;
    let u3 = h3 * h3 / (a2 * a2) + c3 * c3 / (o2 * o2);
    u3 > 1 && (u3 = Math.sqrt(u3), a2 *= u3, o2 *= u3);
    const p3 = a2 * a2, k2 = o2 * o2, y4 = p3 * k2 - p3 * c3 * c3 - k2 * h3 * h3, m3 = p3 * c3 * c3 + k2 * h3 * h3, w3 = (r2 === i2 ? -1 : 1) * Math.sqrt(Math.abs(y4 / m3));
    g3 = w3 * a2 * c3 / o2 + (t2 + s2) / 2, b2 = w3 * -o2 * h3 / a2 + (e3 + n2) / 2, f2 = Math.asin(parseFloat(((e3 - b2) / o2).toFixed(9))), d2 = Math.asin(parseFloat(((n2 - b2) / o2).toFixed(9))), t2 < g3 && (f2 = Math.PI - f2), s2 < g3 && (d2 = Math.PI - d2), f2 < 0 && (f2 = 2 * Math.PI + f2), d2 < 0 && (d2 = 2 * Math.PI + d2), i2 && f2 > d2 && (f2 -= 2 * Math.PI), !i2 && d2 > f2 && (d2 -= 2 * Math.PI);
  }
  let y3 = d2 - f2;
  if (Math.abs(y3) > 120 * Math.PI / 180) {
    const t3 = d2, e4 = s2, r3 = n2;
    d2 = i2 && d2 > f2 ? f2 + 120 * Math.PI / 180 * 1 : f2 + 120 * Math.PI / 180 * -1, p2 = k(s2 = g3 + a2 * Math.cos(d2), n2 = b2 + o2 * Math.sin(d2), e4, r3, a2, o2, h2, 0, i2, [d2, t3, g3, b2]);
  }
  y3 = d2 - f2;
  const m2 = Math.cos(f2), w2 = Math.sin(f2), x3 = Math.cos(d2), P2 = Math.sin(d2), v2 = Math.tan(y3 / 4), O2 = 4 / 3 * a2 * v2, S2 = 4 / 3 * o2 * v2, L2 = [t2, e3], T2 = [t2 + O2 * w2, e3 - S2 * m2], D2 = [s2 + O2 * P2, n2 - S2 * x3], A5 = [s2, n2];
  if (T2[0] = 2 * L2[0] - T2[0], T2[1] = 2 * L2[1] - T2[1], c2) return [T2, D2, A5].concat(p2);
  {
    p2 = [T2, D2, A5].concat(p2);
    const t3 = [];
    for (let e4 = 0; e4 < p2.length; e4 += 3) {
      const s3 = M2(p2[e4][0], p2[e4][1], l2), n3 = M2(p2[e4 + 1][0], p2[e4 + 1][1], l2), a3 = M2(p2[e4 + 2][0], p2[e4 + 2][1], l2);
      t3.push([s3[0], s3[1], n3[0], n3[1], a3[0], a3[1]]);
    }
    return t3;
  }
}
var b = { randOffset: function(t2, e3) {
  return A(t2, e3);
}, randOffsetWithRange: function(t2, e3, s2) {
  return D(t2, e3, s2);
}, ellipse: function(t2, e3, s2, n2, a2) {
  const o2 = P(s2, n2, a2);
  return v(t2, e3, a2, o2).opset;
}, doubleLineOps: function(t2, e3, s2, n2, a2) {
  return I(t2, e3, s2, n2, a2, true);
} };
function y(t2, e3, s2, n2, a2) {
  return { type: "path", ops: I(t2, e3, s2, n2, a2) };
}
function m(t2, e3, s2) {
  const n2 = (t2 || []).length;
  if (n2 > 2) {
    const a2 = [];
    for (let e4 = 0; e4 < n2 - 1; e4++) a2.push(...I(t2[e4][0], t2[e4][1], t2[e4 + 1][0], t2[e4 + 1][1], s2));
    return e3 && a2.push(...I(t2[n2 - 1][0], t2[n2 - 1][1], t2[0][0], t2[0][1], s2)), { type: "path", ops: a2 };
  }
  return 2 === n2 ? y(t2[0][0], t2[0][1], t2[1][0], t2[1][1], s2) : { type: "path", ops: [] };
}
function w(t2, e3, s2, n2, a2) {
  return function(t3, e4) {
    return m(t3, true, e4);
  }([[t2, e3], [t2 + s2, e3], [t2 + s2, e3 + n2], [t2, e3 + n2]], a2);
}
function x(t2, e3) {
  let s2 = _(t2, 1 * (1 + 0.2 * e3.roughness), e3);
  if (!e3.disableMultiStroke) {
    const n2 = _(t2, 1.5 * (1 + 0.22 * e3.roughness), function(t3) {
      const e4 = Object.assign({}, t3);
      e4.randomizer = void 0, t3.seed && (e4.seed = t3.seed + 1);
      return e4;
    }(e3));
    s2 = s2.concat(n2);
  }
  return { type: "path", ops: s2 };
}
function P(t2, e3, s2) {
  const n2 = Math.sqrt(2 * Math.PI * Math.sqrt((Math.pow(t2 / 2, 2) + Math.pow(e3 / 2, 2)) / 2)), a2 = Math.ceil(Math.max(s2.curveStepCount, s2.curveStepCount / Math.sqrt(200) * n2)), o2 = 2 * Math.PI / a2;
  let h2 = Math.abs(t2 / 2), r2 = Math.abs(e3 / 2);
  const i2 = 1 - s2.curveFitting;
  return h2 += A(h2 * i2, s2), r2 += A(r2 * i2, s2), { increment: o2, rx: h2, ry: r2 };
}
function v(t2, e3, s2, n2) {
  const [a2, o2] = z(n2.increment, t2, e3, n2.rx, n2.ry, 1, n2.increment * D(0.1, D(0.4, 1, s2), s2), s2);
  let h2 = W(a2, null, s2);
  if (!s2.disableMultiStroke && 0 !== s2.roughness) {
    const [a3] = z(n2.increment, t2, e3, n2.rx, n2.ry, 1.5, 0, s2), o3 = W(a3, null, s2);
    h2 = h2.concat(o3);
  }
  return { estimatedPoints: o2, opset: { type: "path", ops: h2 } };
}
function O(t2, e3, s2, n2, a2, o2, h2, r2, i2) {
  const c2 = t2, l2 = e3;
  let u2 = Math.abs(s2 / 2), p2 = Math.abs(n2 / 2);
  u2 += A(0.01 * u2, i2), p2 += A(0.01 * p2, i2);
  let f2 = a2, d2 = o2;
  for (; f2 < 0; ) f2 += 2 * Math.PI, d2 += 2 * Math.PI;
  d2 - f2 > 2 * Math.PI && (f2 = 0, d2 = 2 * Math.PI);
  const g3 = 2 * Math.PI / i2.curveStepCount, M3 = Math.min(g3 / 2, (d2 - f2) / 2), k2 = E(M3, c2, l2, u2, p2, f2, d2, 1, i2);
  if (!i2.disableMultiStroke) {
    const t3 = E(M3, c2, l2, u2, p2, f2, d2, 1.5, i2);
    k2.push(...t3);
  }
  return h2 && (r2 ? k2.push(...I(c2, l2, c2 + u2 * Math.cos(f2), l2 + p2 * Math.sin(f2), i2), ...I(c2, l2, c2 + u2 * Math.cos(d2), l2 + p2 * Math.sin(d2), i2)) : k2.push({ op: "lineTo", data: [c2, l2] }, { op: "lineTo", data: [c2 + u2 * Math.cos(f2), l2 + p2 * Math.sin(f2)] })), { type: "path", ops: k2 };
}
function S(t2, e3) {
  const s2 = [];
  for (const n2 of t2) if (n2.length) {
    const t3 = e3.maxRandomnessOffset || 0, a2 = n2.length;
    if (a2 > 2) {
      s2.push({ op: "move", data: [n2[0][0] + A(t3, e3), n2[0][1] + A(t3, e3)] });
      for (let o2 = 1; o2 < a2; o2++) s2.push({ op: "lineTo", data: [n2[o2][0] + A(t3, e3), n2[o2][1] + A(t3, e3)] });
    }
  }
  return { type: "fillPath", ops: s2 };
}
function L(t2, e3) {
  return function(t3, e4) {
    let s2 = t3.fillStyle || "hachure";
    if (!c[s2]) switch (s2) {
      case "zigzag":
        c[s2] || (c[s2] = new a(e4));
        break;
      case "cross-hatch":
        c[s2] || (c[s2] = new o(e4));
        break;
      case "dots":
        c[s2] || (c[s2] = new h(e4));
        break;
      case "dashed":
        c[s2] || (c[s2] = new r(e4));
        break;
      case "zigzag-line":
        c[s2] || (c[s2] = new i(e4));
        break;
      case "hachure":
      default:
        s2 = "hachure", c[s2] || (c[s2] = new n(e4));
    }
    return c[s2];
  }(e3, b).fillPolygons(t2, e3);
}
function T(t2) {
  return t2.randomizer || (t2.randomizer = new l(t2.seed || 0)), t2.randomizer.next();
}
function D(t2, e3, s2, n2 = 1) {
  return s2.roughness * n2 * (T(s2) * (e3 - t2) + t2);
}
function A(t2, e3, s2 = 1) {
  return D(-t2, t2, e3, s2);
}
function I(t2, e3, s2, n2, a2, o2 = false) {
  const h2 = o2 ? a2.disableMultiStrokeFill : a2.disableMultiStroke, r2 = C(t2, e3, s2, n2, a2, true, false);
  if (h2) return r2;
  const i2 = C(t2, e3, s2, n2, a2, true, true);
  return r2.concat(i2);
}
function C(t2, e3, s2, n2, a2, o2, h2) {
  const r2 = Math.pow(t2 - s2, 2) + Math.pow(e3 - n2, 2), i2 = Math.sqrt(r2);
  let c2 = 1;
  c2 = i2 < 200 ? 1 : i2 > 500 ? 0.4 : -16668e-7 * i2 + 1.233334;
  let l2 = a2.maxRandomnessOffset || 0;
  l2 * l2 * 100 > r2 && (l2 = i2 / 10);
  const u2 = l2 / 2, p2 = 0.2 + 0.2 * T(a2);
  let f2 = a2.bowing * a2.maxRandomnessOffset * (n2 - e3) / 200, d2 = a2.bowing * a2.maxRandomnessOffset * (t2 - s2) / 200;
  f2 = A(f2, a2, c2), d2 = A(d2, a2, c2);
  const g3 = [], M3 = () => A(u2, a2, c2), k2 = () => A(l2, a2, c2), b2 = a2.preserveVertices;
  return o2 && (h2 ? g3.push({ op: "move", data: [t2 + (b2 ? 0 : M3()), e3 + (b2 ? 0 : M3())] }) : g3.push({ op: "move", data: [t2 + (b2 ? 0 : A(l2, a2, c2)), e3 + (b2 ? 0 : A(l2, a2, c2))] })), h2 ? g3.push({ op: "bcurveTo", data: [f2 + t2 + (s2 - t2) * p2 + M3(), d2 + e3 + (n2 - e3) * p2 + M3(), f2 + t2 + 2 * (s2 - t2) * p2 + M3(), d2 + e3 + 2 * (n2 - e3) * p2 + M3(), s2 + (b2 ? 0 : M3()), n2 + (b2 ? 0 : M3())] }) : g3.push({ op: "bcurveTo", data: [f2 + t2 + (s2 - t2) * p2 + k2(), d2 + e3 + (n2 - e3) * p2 + k2(), f2 + t2 + 2 * (s2 - t2) * p2 + k2(), d2 + e3 + 2 * (n2 - e3) * p2 + k2(), s2 + (b2 ? 0 : k2()), n2 + (b2 ? 0 : k2())] }), g3;
}
function _(t2, e3, s2) {
  const n2 = [];
  n2.push([t2[0][0] + A(e3, s2), t2[0][1] + A(e3, s2)]), n2.push([t2[0][0] + A(e3, s2), t2[0][1] + A(e3, s2)]);
  for (let a2 = 1; a2 < t2.length; a2++) n2.push([t2[a2][0] + A(e3, s2), t2[a2][1] + A(e3, s2)]), a2 === t2.length - 1 && n2.push([t2[a2][0] + A(e3, s2), t2[a2][1] + A(e3, s2)]);
  return W(n2, null, s2);
}
function W(t2, e3, s2) {
  const n2 = t2.length, a2 = [];
  if (n2 > 3) {
    const o2 = [], h2 = 1 - s2.curveTightness;
    a2.push({ op: "move", data: [t2[1][0], t2[1][1]] });
    for (let e4 = 1; e4 + 2 < n2; e4++) {
      const s3 = t2[e4];
      o2[0] = [s3[0], s3[1]], o2[1] = [s3[0] + (h2 * t2[e4 + 1][0] - h2 * t2[e4 - 1][0]) / 6, s3[1] + (h2 * t2[e4 + 1][1] - h2 * t2[e4 - 1][1]) / 6], o2[2] = [t2[e4 + 1][0] + (h2 * t2[e4][0] - h2 * t2[e4 + 2][0]) / 6, t2[e4 + 1][1] + (h2 * t2[e4][1] - h2 * t2[e4 + 2][1]) / 6], o2[3] = [t2[e4 + 1][0], t2[e4 + 1][1]], a2.push({ op: "bcurveTo", data: [o2[1][0], o2[1][1], o2[2][0], o2[2][1], o2[3][0], o2[3][1]] });
    }
    if (e3 && 2 === e3.length) {
      const t3 = s2.maxRandomnessOffset;
      a2.push({ op: "lineTo", data: [e3[0] + A(t3, s2), e3[1] + A(t3, s2)] });
    }
  } else 3 === n2 ? (a2.push({ op: "move", data: [t2[1][0], t2[1][1]] }), a2.push({ op: "bcurveTo", data: [t2[1][0], t2[1][1], t2[2][0], t2[2][1], t2[2][0], t2[2][1]] })) : 2 === n2 && a2.push(...I(t2[0][0], t2[0][1], t2[1][0], t2[1][1], s2));
  return a2;
}
function z(t2, e3, s2, n2, a2, o2, h2, r2) {
  const i2 = [], c2 = [];
  if (0 === r2.roughness) {
    t2 /= 4, c2.push([e3 + n2 * Math.cos(-t2), s2 + a2 * Math.sin(-t2)]);
    for (let o3 = 0; o3 <= 2 * Math.PI; o3 += t2) {
      const t3 = [e3 + n2 * Math.cos(o3), s2 + a2 * Math.sin(o3)];
      i2.push(t3), c2.push(t3);
    }
    c2.push([e3 + n2 * Math.cos(0), s2 + a2 * Math.sin(0)]), c2.push([e3 + n2 * Math.cos(t2), s2 + a2 * Math.sin(t2)]);
  } else {
    const l2 = A(0.5, r2) - Math.PI / 2;
    c2.push([A(o2, r2) + e3 + 0.9 * n2 * Math.cos(l2 - t2), A(o2, r2) + s2 + 0.9 * a2 * Math.sin(l2 - t2)]);
    const u2 = 2 * Math.PI + l2 - 0.01;
    for (let h3 = l2; h3 < u2; h3 += t2) {
      const t3 = [A(o2, r2) + e3 + n2 * Math.cos(h3), A(o2, r2) + s2 + a2 * Math.sin(h3)];
      i2.push(t3), c2.push(t3);
    }
    c2.push([A(o2, r2) + e3 + n2 * Math.cos(l2 + 2 * Math.PI + 0.5 * h2), A(o2, r2) + s2 + a2 * Math.sin(l2 + 2 * Math.PI + 0.5 * h2)]), c2.push([A(o2, r2) + e3 + 0.98 * n2 * Math.cos(l2 + h2), A(o2, r2) + s2 + 0.98 * a2 * Math.sin(l2 + h2)]), c2.push([A(o2, r2) + e3 + 0.9 * n2 * Math.cos(l2 + 0.5 * h2), A(o2, r2) + s2 + 0.9 * a2 * Math.sin(l2 + 0.5 * h2)]);
  }
  return [c2, i2];
}
function E(t2, e3, s2, n2, a2, o2, h2, r2, i2) {
  const c2 = o2 + A(0.1, i2), l2 = [];
  l2.push([A(r2, i2) + e3 + 0.9 * n2 * Math.cos(c2 - t2), A(r2, i2) + s2 + 0.9 * a2 * Math.sin(c2 - t2)]);
  for (let o3 = c2; o3 <= h2; o3 += t2) l2.push([A(r2, i2) + e3 + n2 * Math.cos(o3), A(r2, i2) + s2 + a2 * Math.sin(o3)]);
  return l2.push([e3 + n2 * Math.cos(h2), s2 + a2 * Math.sin(h2)]), l2.push([e3 + n2 * Math.cos(h2), s2 + a2 * Math.sin(h2)]), W(l2, null, i2);
}
function $(t2, e3, s2, n2, a2, o2, h2, r2) {
  const i2 = [], c2 = [r2.maxRandomnessOffset || 1, (r2.maxRandomnessOffset || 1) + 0.3];
  let l2 = [0, 0];
  const u2 = r2.disableMultiStroke ? 1 : 2, p2 = r2.preserveVertices;
  for (let f2 = 0; f2 < u2; f2++) 0 === f2 ? i2.push({ op: "move", data: [h2[0], h2[1]] }) : i2.push({ op: "move", data: [h2[0] + (p2 ? 0 : A(c2[0], r2)), h2[1] + (p2 ? 0 : A(c2[0], r2))] }), l2 = p2 ? [a2, o2] : [a2 + A(c2[f2], r2), o2 + A(c2[f2], r2)], i2.push({ op: "bcurveTo", data: [t2 + A(c2[f2], r2), e3 + A(c2[f2], r2), s2 + A(c2[f2], r2), n2 + A(c2[f2], r2), l2[0], l2[1]] });
  return i2;
}
function G(t2) {
  return [...t2];
}
function R(t2, e3) {
  return Math.pow(t2[0] - e3[0], 2) + Math.pow(t2[1] - e3[1], 2);
}
function q(t2, e3, s2) {
  const n2 = R(e3, s2);
  if (0 === n2) return R(t2, e3);
  let a2 = ((t2[0] - e3[0]) * (s2[0] - e3[0]) + (t2[1] - e3[1]) * (s2[1] - e3[1])) / n2;
  return a2 = Math.max(0, Math.min(1, a2)), R(t2, j(e3, s2, a2));
}
function j(t2, e3, s2) {
  return [t2[0] + (e3[0] - t2[0]) * s2, t2[1] + (e3[1] - t2[1]) * s2];
}
function F(t2, e3, s2, n2) {
  const a2 = n2 || [];
  if (function(t3, e4) {
    const s3 = t3[e4 + 0], n3 = t3[e4 + 1], a3 = t3[e4 + 2], o3 = t3[e4 + 3];
    let h3 = 3 * n3[0] - 2 * s3[0] - o3[0];
    h3 *= h3;
    let r2 = 3 * n3[1] - 2 * s3[1] - o3[1];
    r2 *= r2;
    let i2 = 3 * a3[0] - 2 * o3[0] - s3[0];
    i2 *= i2;
    let c2 = 3 * a3[1] - 2 * o3[1] - s3[1];
    return c2 *= c2, h3 < i2 && (h3 = i2), r2 < c2 && (r2 = c2), h3 + r2;
  }(t2, e3) < s2) {
    const s3 = t2[e3 + 0];
    if (a2.length) {
      (o2 = a2[a2.length - 1], h2 = s3, Math.sqrt(R(o2, h2))) > 1 && a2.push(s3);
    } else a2.push(s3);
    a2.push(t2[e3 + 3]);
  } else {
    const n3 = 0.5, o3 = t2[e3 + 0], h3 = t2[e3 + 1], r2 = t2[e3 + 2], i2 = t2[e3 + 3], c2 = j(o3, h3, n3), l2 = j(h3, r2, n3), u2 = j(r2, i2, n3), p2 = j(c2, l2, n3), f2 = j(l2, u2, n3), d2 = j(p2, f2, n3);
    F([o3, c2, p2, d2], 0, s2, a2), F([d2, f2, u2, i2], 0, s2, a2);
  }
  var o2, h2;
  return a2;
}
function V(t2, e3) {
  return Z(t2, 0, t2.length, e3);
}
function Z(t2, e3, s2, n2, a2) {
  const o2 = a2 || [], h2 = t2[e3], r2 = t2[s2 - 1];
  let i2 = 0, c2 = 1;
  for (let n3 = e3 + 1; n3 < s2 - 1; ++n3) {
    const e4 = q(t2[n3], h2, r2);
    e4 > i2 && (i2 = e4, c2 = n3);
  }
  return Math.sqrt(i2) > n2 ? (Z(t2, e3, c2 + 1, n2, o2), Z(t2, c2, s2, n2, o2)) : (o2.length || o2.push(h2), o2.push(r2)), o2;
}
function Q(t2, e3 = 0.15, s2) {
  const n2 = [], a2 = (t2.length - 1) / 3;
  for (let s3 = 0; s3 < a2; s3++) {
    F(t2, 3 * s3, e3, n2);
  }
  return s2 && s2 > 0 ? Z(n2, 0, n2.length, s2) : n2;
}
var H = "none";
var N = class {
  constructor(t2) {
    this.defaultOptions = { maxRandomnessOffset: 2, roughness: 1, bowing: 1, stroke: "#000", strokeWidth: 1, curveTightness: 0, curveFitting: 0.95, curveStepCount: 9, fillStyle: "hachure", fillWeight: -1, hachureAngle: -41, hachureGap: -1, dashOffset: -1, dashGap: -1, zigzagOffset: -1, seed: 0, disableMultiStroke: false, disableMultiStrokeFill: false, preserveVertices: false }, this.config = t2 || {}, this.config.options && (this.defaultOptions = this._o(this.config.options));
  }
  static newSeed() {
    return Math.floor(Math.random() * 2 ** 31);
  }
  _o(t2) {
    return t2 ? Object.assign({}, this.defaultOptions, t2) : this.defaultOptions;
  }
  _d(t2, e3, s2) {
    return { shape: t2, sets: e3 || [], options: s2 || this.defaultOptions };
  }
  line(t2, e3, s2, n2, a2) {
    const o2 = this._o(a2);
    return this._d("line", [y(t2, e3, s2, n2, o2)], o2);
  }
  rectangle(t2, e3, s2, n2, a2) {
    const o2 = this._o(a2), h2 = [], r2 = w(t2, e3, s2, n2, o2);
    if (o2.fill) {
      const a3 = [[t2, e3], [t2 + s2, e3], [t2 + s2, e3 + n2], [t2, e3 + n2]];
      "solid" === o2.fillStyle ? h2.push(S([a3], o2)) : h2.push(L([a3], o2));
    }
    return o2.stroke !== H && h2.push(r2), this._d("rectangle", h2, o2);
  }
  ellipse(t2, e3, s2, n2, a2) {
    const o2 = this._o(a2), h2 = [], r2 = P(s2, n2, o2), i2 = v(t2, e3, o2, r2);
    if (o2.fill) if ("solid" === o2.fillStyle) {
      const s3 = v(t2, e3, o2, r2).opset;
      s3.type = "fillPath", h2.push(s3);
    } else h2.push(L([i2.estimatedPoints], o2));
    return o2.stroke !== H && h2.push(i2.opset), this._d("ellipse", h2, o2);
  }
  circle(t2, e3, s2, n2) {
    const a2 = this.ellipse(t2, e3, s2, s2, n2);
    return a2.shape = "circle", a2;
  }
  linearPath(t2, e3) {
    const s2 = this._o(e3);
    return this._d("linearPath", [m(t2, false, s2)], s2);
  }
  arc(t2, e3, s2, n2, a2, o2, h2 = false, r2) {
    const i2 = this._o(r2), c2 = [], l2 = O(t2, e3, s2, n2, a2, o2, h2, true, i2);
    if (h2 && i2.fill) if ("solid" === i2.fillStyle) {
      const h3 = Object.assign({}, i2);
      h3.disableMultiStroke = true;
      const r3 = O(t2, e3, s2, n2, a2, o2, true, false, h3);
      r3.type = "fillPath", c2.push(r3);
    } else c2.push(function(t3, e4, s3, n3, a3, o3, h3) {
      const r3 = t3, i3 = e4;
      let c3 = Math.abs(s3 / 2), l3 = Math.abs(n3 / 2);
      c3 += A(0.01 * c3, h3), l3 += A(0.01 * l3, h3);
      let u2 = a3, p2 = o3;
      for (; u2 < 0; ) u2 += 2 * Math.PI, p2 += 2 * Math.PI;
      p2 - u2 > 2 * Math.PI && (u2 = 0, p2 = 2 * Math.PI);
      const f2 = (p2 - u2) / h3.curveStepCount, d2 = [];
      for (let t4 = u2; t4 <= p2; t4 += f2) d2.push([r3 + c3 * Math.cos(t4), i3 + l3 * Math.sin(t4)]);
      return d2.push([r3 + c3 * Math.cos(p2), i3 + l3 * Math.sin(p2)]), d2.push([r3, i3]), L([d2], h3);
    }(t2, e3, s2, n2, a2, o2, i2));
    return i2.stroke !== H && c2.push(l2), this._d("arc", c2, i2);
  }
  curve(t2, e3) {
    const s2 = this._o(e3), n2 = [], a2 = x(t2, s2);
    if (s2.fill && s2.fill !== H && t2.length >= 3) {
      const e4 = Q(function(t3, e6 = 0) {
        const s3 = t3.length;
        if (s3 < 3) throw new Error("A curve must have at least three points.");
        const n3 = [];
        if (3 === s3) n3.push(G(t3[0]), G(t3[1]), G(t3[2]), G(t3[2]));
        else {
          const s4 = [];
          s4.push(t3[0], t3[0]);
          for (let e7 = 1; e7 < t3.length; e7++) s4.push(t3[e7]), e7 === t3.length - 1 && s4.push(t3[e7]);
          const a3 = [], o2 = 1 - e6;
          n3.push(G(s4[0]));
          for (let t4 = 1; t4 + 2 < s4.length; t4++) {
            const e7 = s4[t4];
            a3[0] = [e7[0], e7[1]], a3[1] = [e7[0] + (o2 * s4[t4 + 1][0] - o2 * s4[t4 - 1][0]) / 6, e7[1] + (o2 * s4[t4 + 1][1] - o2 * s4[t4 - 1][1]) / 6], a3[2] = [s4[t4 + 1][0] + (o2 * s4[t4][0] - o2 * s4[t4 + 2][0]) / 6, s4[t4 + 1][1] + (o2 * s4[t4][1] - o2 * s4[t4 + 2][1]) / 6], a3[3] = [s4[t4 + 1][0], s4[t4 + 1][1]], n3.push(a3[1], a3[2], a3[3]);
          }
        }
        return n3;
      }(t2), 10, (1 + s2.roughness) / 2);
      "solid" === s2.fillStyle ? n2.push(S([e4], s2)) : n2.push(L([e4], s2));
    }
    return s2.stroke !== H && n2.push(a2), this._d("curve", n2, s2);
  }
  polygon(t2, e3) {
    const s2 = this._o(e3), n2 = [], a2 = m(t2, true, s2);
    return s2.fill && ("solid" === s2.fillStyle ? n2.push(S([t2], s2)) : n2.push(L([t2], s2))), s2.stroke !== H && n2.push(a2), this._d("polygon", n2, s2);
  }
  path(t2, e3) {
    const s2 = this._o(e3), n2 = [];
    if (!t2) return this._d("path", n2, s2);
    t2 = (t2 || "").replace(/\n/g, " ").replace(/(-\s)/g, "-").replace("/(ss)/g", " ");
    const a2 = s2.fill && "transparent" !== s2.fill && s2.fill !== H, o2 = s2.stroke !== H, h2 = !!(s2.simplification && s2.simplification < 1), r2 = function(t3, e4, s3) {
      const n3 = g2(d(f(t3))), a3 = [];
      let o3 = [], h3 = [0, 0], r3 = [];
      const i2 = () => {
        r3.length >= 4 && o3.push(...Q(r3, e4)), r3 = [];
      }, c2 = () => {
        i2(), o3.length && (a3.push(o3), o3 = []);
      };
      for (const { key: t4, data: e6 } of n3) switch (t4) {
        case "M":
          c2(), h3 = [e6[0], e6[1]], o3.push(h3);
          break;
        case "L":
          i2(), o3.push([e6[0], e6[1]]);
          break;
        case "C":
          if (!r3.length) {
            const t5 = o3.length ? o3[o3.length - 1] : h3;
            r3.push([t5[0], t5[1]]);
          }
          r3.push([e6[0], e6[1]]), r3.push([e6[2], e6[3]]), r3.push([e6[4], e6[5]]);
          break;
        case "Z":
          i2(), o3.push([h3[0], h3[1]]);
      }
      if (c2(), !s3) return a3;
      const l2 = [];
      for (const t4 of a3) {
        const e6 = V(t4, s3);
        e6.length && l2.push(e6);
      }
      return l2;
    }(t2, 1, h2 ? 4 - 4 * s2.simplification : (1 + s2.roughness) / 2);
    return a2 && ("solid" === s2.fillStyle ? n2.push(S(r2, s2)) : n2.push(L(r2, s2))), o2 && (h2 ? r2.forEach((t3) => {
      n2.push(m(t3, false, s2));
    }) : n2.push(function(t3, e4) {
      const s3 = g2(d(f(t3))), n3 = [];
      let a3 = [0, 0], o3 = [0, 0];
      for (const { key: t4, data: h3 } of s3) switch (t4) {
        case "M": {
          const t5 = 1 * (e4.maxRandomnessOffset || 0), s4 = e4.preserveVertices;
          n3.push({ op: "move", data: h3.map((n4) => n4 + (s4 ? 0 : A(t5, e4))) }), o3 = [h3[0], h3[1]], a3 = [h3[0], h3[1]];
          break;
        }
        case "L":
          n3.push(...I(o3[0], o3[1], h3[0], h3[1], e4)), o3 = [h3[0], h3[1]];
          break;
        case "C": {
          const [t5, s4, a4, r3, i2, c2] = h3;
          n3.push(...$(t5, s4, a4, r3, i2, c2, o3, e4)), o3 = [i2, c2];
          break;
        }
        case "Z":
          n3.push(...I(o3[0], o3[1], a3[0], a3[1], e4)), o3 = [a3[0], a3[1]];
      }
      return { type: "path", ops: n3 };
    }(t2, s2))), this._d("path", n2, s2);
  }
  opsToPath(t2, e3) {
    let s2 = "";
    for (const n2 of t2.ops) {
      const t3 = "number" == typeof e3 && e3 >= 0 ? n2.data.map((t4) => +t4.toFixed(e3)) : n2.data;
      switch (n2.op) {
        case "move":
          s2 += `M${t3[0]} ${t3[1]} `;
          break;
        case "bcurveTo":
          s2 += `C${t3[0]} ${t3[1]}, ${t3[2]} ${t3[3]}, ${t3[4]} ${t3[5]} `;
          break;
        case "lineTo":
          s2 += `L${t3[0]} ${t3[1]} `;
      }
    }
    return s2.trim();
  }
  toPaths(t2) {
    const e3 = t2.sets || [], s2 = t2.options || this.defaultOptions, n2 = [];
    for (const t3 of e3) {
      let e4 = null;
      switch (t3.type) {
        case "path":
          e4 = { d: this.opsToPath(t3), stroke: s2.stroke, strokeWidth: s2.strokeWidth, fill: H };
          break;
        case "fillPath":
          e4 = { d: this.opsToPath(t3), stroke: H, strokeWidth: 0, fill: s2.fill || H };
          break;
        case "fillSketch":
          e4 = this.fillSketch(t3, s2);
      }
      e4 && n2.push(e4);
    }
    return n2;
  }
  fillSketch(t2, e3) {
    let s2 = e3.fillWeight;
    return s2 < 0 && (s2 = e3.strokeWidth / 2), { d: this.opsToPath(t2), stroke: e3.fill || H, strokeWidth: s2, fill: H };
  }
};
var B = class {
  constructor(t2, e3) {
    this.canvas = t2, this.ctx = this.canvas.getContext("2d"), this.gen = new N(e3);
  }
  draw(t2) {
    const e3 = t2.sets || [], s2 = t2.options || this.getDefaultOptions(), n2 = this.ctx, a2 = t2.options.fixedDecimalPlaceDigits;
    for (const o2 of e3) switch (o2.type) {
      case "path":
        n2.save(), n2.strokeStyle = "none" === s2.stroke ? "transparent" : s2.stroke, n2.lineWidth = s2.strokeWidth, s2.strokeLineDash && n2.setLineDash(s2.strokeLineDash), s2.strokeLineDashOffset && (n2.lineDashOffset = s2.strokeLineDashOffset), this._drawToContext(n2, o2, a2), n2.restore();
        break;
      case "fillPath": {
        n2.save(), n2.fillStyle = s2.fill || "";
        const e4 = "curve" === t2.shape || "polygon" === t2.shape || "path" === t2.shape ? "evenodd" : "nonzero";
        this._drawToContext(n2, o2, a2, e4), n2.restore();
        break;
      }
      case "fillSketch":
        this.fillSketch(n2, o2, s2);
    }
  }
  fillSketch(t2, e3, s2) {
    let n2 = s2.fillWeight;
    n2 < 0 && (n2 = s2.strokeWidth / 2), t2.save(), s2.fillLineDash && t2.setLineDash(s2.fillLineDash), s2.fillLineDashOffset && (t2.lineDashOffset = s2.fillLineDashOffset), t2.strokeStyle = s2.fill || "", t2.lineWidth = n2, this._drawToContext(t2, e3, s2.fixedDecimalPlaceDigits), t2.restore();
  }
  _drawToContext(t2, e3, s2, n2 = "nonzero") {
    t2.beginPath();
    for (const n3 of e3.ops) {
      const e4 = "number" == typeof s2 && s2 >= 0 ? n3.data.map((t3) => +t3.toFixed(s2)) : n3.data;
      switch (n3.op) {
        case "move":
          t2.moveTo(e4[0], e4[1]);
          break;
        case "bcurveTo":
          t2.bezierCurveTo(e4[0], e4[1], e4[2], e4[3], e4[4], e4[5]);
          break;
        case "lineTo":
          t2.lineTo(e4[0], e4[1]);
      }
    }
    "fillPath" === e3.type ? t2.fill(n2) : t2.stroke();
  }
  get generator() {
    return this.gen;
  }
  getDefaultOptions() {
    return this.gen.defaultOptions;
  }
  line(t2, e3, s2, n2, a2) {
    const o2 = this.gen.line(t2, e3, s2, n2, a2);
    return this.draw(o2), o2;
  }
  rectangle(t2, e3, s2, n2, a2) {
    const o2 = this.gen.rectangle(t2, e3, s2, n2, a2);
    return this.draw(o2), o2;
  }
  ellipse(t2, e3, s2, n2, a2) {
    const o2 = this.gen.ellipse(t2, e3, s2, n2, a2);
    return this.draw(o2), o2;
  }
  circle(t2, e3, s2, n2) {
    const a2 = this.gen.circle(t2, e3, s2, n2);
    return this.draw(a2), a2;
  }
  linearPath(t2, e3) {
    const s2 = this.gen.linearPath(t2, e3);
    return this.draw(s2), s2;
  }
  polygon(t2, e3) {
    const s2 = this.gen.polygon(t2, e3);
    return this.draw(s2), s2;
  }
  arc(t2, e3, s2, n2, a2, o2, h2 = false, r2) {
    const i2 = this.gen.arc(t2, e3, s2, n2, a2, o2, h2, r2);
    return this.draw(i2), i2;
  }
  curve(t2, e3) {
    const s2 = this.gen.curve(t2, e3);
    return this.draw(s2), s2;
  }
  path(t2, e3) {
    const s2 = this.gen.path(t2, e3);
    return this.draw(s2), s2;
  }
};
var J = "http://www.w3.org/2000/svg";
var K = class {
  constructor(t2, e3) {
    this.svg = t2, this.gen = new N(e3);
  }
  draw(t2) {
    const e3 = t2.sets || [], s2 = t2.options || this.getDefaultOptions(), n2 = this.svg.ownerDocument || window.document, a2 = n2.createElementNS(J, "g"), o2 = t2.options.fixedDecimalPlaceDigits;
    for (const h2 of e3) {
      let e4 = null;
      switch (h2.type) {
        case "path":
          e4 = n2.createElementNS(J, "path"), e4.setAttribute("d", this.opsToPath(h2, o2)), e4.setAttribute("stroke", s2.stroke), e4.setAttribute("stroke-width", s2.strokeWidth + ""), e4.setAttribute("fill", "none"), s2.strokeLineDash && e4.setAttribute("stroke-dasharray", s2.strokeLineDash.join(" ").trim()), s2.strokeLineDashOffset && e4.setAttribute("stroke-dashoffset", `${s2.strokeLineDashOffset}`);
          break;
        case "fillPath":
          e4 = n2.createElementNS(J, "path"), e4.setAttribute("d", this.opsToPath(h2, o2)), e4.setAttribute("stroke", "none"), e4.setAttribute("stroke-width", "0"), e4.setAttribute("fill", s2.fill || ""), "curve" !== t2.shape && "polygon" !== t2.shape || e4.setAttribute("fill-rule", "evenodd");
          break;
        case "fillSketch":
          e4 = this.fillSketch(n2, h2, s2);
      }
      e4 && a2.appendChild(e4);
    }
    return a2;
  }
  fillSketch(t2, e3, s2) {
    let n2 = s2.fillWeight;
    n2 < 0 && (n2 = s2.strokeWidth / 2);
    const a2 = t2.createElementNS(J, "path");
    return a2.setAttribute("d", this.opsToPath(e3, s2.fixedDecimalPlaceDigits)), a2.setAttribute("stroke", s2.fill || ""), a2.setAttribute("stroke-width", n2 + ""), a2.setAttribute("fill", "none"), s2.fillLineDash && a2.setAttribute("stroke-dasharray", s2.fillLineDash.join(" ").trim()), s2.fillLineDashOffset && a2.setAttribute("stroke-dashoffset", `${s2.fillLineDashOffset}`), a2;
  }
  get generator() {
    return this.gen;
  }
  getDefaultOptions() {
    return this.gen.defaultOptions;
  }
  opsToPath(t2, e3) {
    return this.gen.opsToPath(t2, e3);
  }
  line(t2, e3, s2, n2, a2) {
    const o2 = this.gen.line(t2, e3, s2, n2, a2);
    return this.draw(o2);
  }
  rectangle(t2, e3, s2, n2, a2) {
    const o2 = this.gen.rectangle(t2, e3, s2, n2, a2);
    return this.draw(o2);
  }
  ellipse(t2, e3, s2, n2, a2) {
    const o2 = this.gen.ellipse(t2, e3, s2, n2, a2);
    return this.draw(o2);
  }
  circle(t2, e3, s2, n2) {
    const a2 = this.gen.circle(t2, e3, s2, n2);
    return this.draw(a2);
  }
  linearPath(t2, e3) {
    const s2 = this.gen.linearPath(t2, e3);
    return this.draw(s2);
  }
  polygon(t2, e3) {
    const s2 = this.gen.polygon(t2, e3);
    return this.draw(s2);
  }
  arc(t2, e3, s2, n2, a2, o2, h2 = false, r2) {
    const i2 = this.gen.arc(t2, e3, s2, n2, a2, o2, h2, r2);
    return this.draw(i2);
  }
  curve(t2, e3) {
    const s2 = this.gen.curve(t2, e3);
    return this.draw(s2);
  }
  path(t2, e3) {
    const s2 = this.gen.path(t2, e3);
    return this.draw(s2);
  }
};
var U = { canvas: (t2, e3) => new B(t2, e3), svg: (t2, e3) => new K(t2, e3), generator: (t2) => new N(t2), newSeed: () => N.newSeed() };

export {
  isBoolean_default,
  isFunction_default,
  isNil_default,
  isValid_default,
  isObject_default,
  isObjectLike_default,
  isPlainObject_default,
  isUndefined_default,
  isString_default,
  isArray_default,
  isNumber_default,
  isNumeric_default,
  isValidNumber_default,
  isValidUrl_default,
  isBase64_default,
  isEmpty_default,
  get_default,
  has_default,
  cloneDeep,
  baseMerge,
  merge,
  pickWithout,
  isEqual,
  mixin,
  array,
  last,
  maxInArray,
  minInArray,
  arrayEqual,
  uniqArray,
  shuffleArray,
  flattenArray,
  range,
  toNumber,
  LoggerLevel,
  Logger,
  findZeroOfFunction,
  binaryFuzzySearch,
  binaryFuzzySearchInNumberRange,
  median,
  isNumberClose,
  isGreater,
  isLess,
  clamp_default,
  clampRange_default,
  clamper,
  debounce_default,
  throttle_default,
  interpolateString,
  toValidNumber,
  seedRandom,
  epsilon,
  pi,
  halfPi,
  tau,
  pi2,
  abs,
  atan2,
  cos,
  max,
  min,
  sin,
  sqrt,
  acos,
  asin,
  pointAt,
  crossProduct,
  getDecimalPlaces,
  precisionAdd,
  precisionSub,
  Point,
  PointService,
  degreeToRadian,
  radianToDegree,
  clampRadian,
  clampAngleByRadian,
  polarToCartesian,
  cartesianToPolar,
  getAngleByPoint,
  normalizeAngle,
  calculateMaxRadius,
  computeQuadrant,
  getIntersectPoint,
  getRectIntersect,
  rectInsideAnotherRect,
  isRectIntersect,
  pointInRect,
  rotatePoint,
  isRotateAABBIntersect,
  getAABBFromPoints,
  polygonContainPoint,
  isPointInLine,
  polygonIntersectPolygon,
  getContextFont,
  TextMeasure,
  calculateAnchorOfBounds,
  transformBoundsWithMatrix,
  Bounds,
  AABBBounds,
  OBBBounds,
  Matrix,
  normalTransform,
  LRU,
  hslToRgb,
  rgbToHsl,
  DEFAULT_COLORS,
  Color,
  hexToRgb,
  normalizePadding,
  getContainerSize,
  getElementAbsolutePosition,
  styleStringToObject,
  lowerCamelCaseToMiddle,
  toCamelCase,
  isHTMLElement,
  isPointInPolygon,
  destination,
  SMALL,
  intersectionArea,
  circleOverlap,
  circleCircleIntersection,
  getCenter,
  TimeUtil,
  numberSpecifierReg,
  NumberUtil,
  zeros,
  zerosM,
  norm2,
  scale,
  nelderMead,
  conjugateGradient,
  import_eventemitter3,
  U,
  ScaleEnum,
  isContinuous,
  isValidScaleType,
  isDiscrete,
  scaleWholeRangeSize,
  OrdinalScale,
  BandScale,
  LinearScale,
  LogScale,
  PointScale,
  SqrtScale,
  SymlogScale,
  ThresholdScale,
  path_default,
  conicEqualArea_default,
  albers_default,
  albersUsa_default,
  azimuthalEqualArea_default,
  azimuthalEquidistant_default,
  mercator_default,
  conicConformal_default,
  equirectangular_default,
  conicEquidistant_default,
  equalEarth_default,
  gnomonic_default,
  identity_default3 as identity_default,
  naturalEarth1_default,
  orthographic_default,
  stereographic_default,
  transverseMercator_default,
  feature_default,
  init_src,
  require_geojson_dissolve,
  require_simplify_geojson,
  simple_statistics_exports,
  dsv_default,
  csvParse,
  tsvParse,
  require_geobuf,
  require_pbf,
  es_default2 as es_default,
  es_default3 as es_default2
};
/*! Bundled license information:

ieee754/index.js:
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
//# sourceMappingURL=chunk-AMHJVIGW.js.map

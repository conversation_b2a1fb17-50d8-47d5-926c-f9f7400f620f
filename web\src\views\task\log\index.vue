<!--
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-10 19:30:05
 * @LastEditors: zhai
 * @LastEditTime: 2024-07-04 22:18:49
-->
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ValueBuilderContext, useFs } from '@fast-crud/fast-crud';
import type {
  AddReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  DelReq,
  EditReq,
  UserPageQuery,
  UserPageRes,
  ValueResolveContext
} from '@fast-crud/fast-crud';
import { fetchGetTaskRecordList } from '@/service/api';

interface Props {
  selkeys: Array<number>;
}

const props = withDefaults(defineProps<Props>(), {
  selkeys: []
});

// 初始化响应式状态
const selectedRowKeys = ref(props.selkeys);

// 监听props.selkeys
watch(
  () => props.selkeys,
  newVal => {
    selectedRowKeys.value = newVal;
  }
);

const emit = defineEmits<{
  (e: 'select-types', value: Array<number>): void;
}>();

function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery): Promise<UserPageRes> => {
    return fetchGetTaskRecordList(query);
  };

  const editRequest = async (ctx: EditReq) => {};

  const delRequest = async (ctx: DelReq) => {};

  const addRequest = async (req: AddReq) => {};

  return {
    crudOptions: {
      container: {
        // is: 'fs-layout-card'
      },
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest
      },
      table: {
        striped: true
      },
      toolbar: {
        show: false
      },
      actionbar: {
        show: false
      },
      rowHandle: {
        show: true,
        width: 70,
        align: 'center',
        titleAlign: 'center',
        buttons: {
          edit: { show: false },
          remove: { show: false }
        }
      },
      search: {
        show: true
      },
      form: {
        wrapper: {
          draggable: false
        }
      },
      columns: {
        id: {
          title: 'ID',
          key: 'id',
          type: 'number',
          column: {
            show: false
          },
          form: {
            show: false
          }
        },
        job_id: {
          title: '任务ID',
          type: 'text',
          column: {
            width: 70,
            align: 'center',
            titleAlign: 'center'
          },
          search: { show: true }
        },
        name: {
          title: '任务名称',
          type: 'text',
          search: { show: true }
        },
        job_class: {
          title: '调用目标',
          type: 'text'
        },
        group: {
          title: '任务分组',
          type: 'text',
          search: { show: true }
        },
        exec_strategy: {
          title: '执行策略',
          type: 'text'
        },
        expression: {
          title: '表达式',
          type: 'text'
        },
        start_time: {
          title: '开始执行时间',
          type: 'datetime'
        },
        end_time: {
          title: '执行完成时间',
          type: 'datetime'
        },
        process_time: {
          title: '耗时(秒)',
          type: 'text'
        },
        retval: {
          title: '任务返回值',
          type: 'text',
          search: { show: true }
        },

        exception: {
          title: '异常信息',
          type: 'text',
          column: {
            ellipsis: {
              tooltip: true
            }
          }
        },

        traceback: {
          title: '堆栈跟踪',
          type: 'text',
          column: {
            ellipsis: {
              tooltip: true
            }
          }
        }
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

// 页面打开后获取列表数据
onMounted(() => {
  crudExpose.doRefresh();
});
</script>

<template>
  <div class="h-full">
    <FsCrud ref="crudRef" v-bind="crudBinding"></FsCrud>
  </div>
</template>

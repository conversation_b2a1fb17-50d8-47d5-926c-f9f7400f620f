/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-21 19:42:39
 * @LastEditors: zhai
 * @LastEditTime: 2024-06-21 20:01:57
 */
import { Crud<PERSON><PERSON> } from '../crud-api';
import { FastCrudApi } from '../fast-crud-api';

/// ///////////////////////////////////////////////////////////////////

export class FastDummyApi extends FastCrudApi<FastDummyApi> {
  constructor() {
    super('dummy');
  }
}

export const fast_dummy_api = FastDummyApi.instance();

/// ///////////////////////////////////////////////////////////////////

export class FastDepartmentApi extends FastCrudApi<FastDepartmentApi> {
  constructor() {
    super('department');
  }
}

export const fast_department_api = FastDepartmentApi.instance();

/// ///////////////////////////////////////////////////////////////////
export class FastEmployeeApi extends FastCrudApi<FastEmployeeApi> {
  constructor() {
    super('employee');
  }
}

export const fast_employee_api = FastEmployeeApi.instance();

import {
  require_vue
} from "./chunk-XGUISF7U.js";
import "./chunk-TID6LRNE.js";
import {
  __commonJS
} from "./chunk-ULBN3QDT.js";

// node_modules/.pnpm/@kanbang+plugin-renderer-vue3@1.10.7/node_modules/@kanbang/plugin-renderer-vue3/dist/index.js
var require_dist = __commonJS({
  "node_modules/.pnpm/@kanbang+plugin-renderer-vue3@1.10.7/node_modules/@kanbang/plugin-renderer-vue3/dist/index.js"(exports, module) {
    (function webpackUniversalModuleDefinition(root, factory) {
      if (typeof exports === "object" && typeof module === "object")
        module.exports = factory(require_vue());
      else if (typeof define === "function" && define.amd)
        define("BaklavaJSRendererVue", ["vue"], factory);
      else if (typeof exports === "object")
        exports["BaklavaJSRendererVue"] = factory(require_vue());
      else
        root["BaklavaJSRendererVue"] = factory(root["Vue"]);
    })(typeof self !== "undefined" ? self : exports, function(__WEBPACK_EXTERNAL_MODULE__0__) {
      return (
        /******/
        function(modules) {
          var installedModules = {};
          function __webpack_require__(moduleId) {
            if (installedModules[moduleId]) {
              return installedModules[moduleId].exports;
            }
            var module2 = installedModules[moduleId] = {
              /******/
              i: moduleId,
              /******/
              l: false,
              /******/
              exports: {}
              /******/
            };
            modules[moduleId].call(module2.exports, module2, module2.exports, __webpack_require__);
            module2.l = true;
            return module2.exports;
          }
          __webpack_require__.m = modules;
          __webpack_require__.c = installedModules;
          __webpack_require__.d = function(exports2, name, getter) {
            if (!__webpack_require__.o(exports2, name)) {
              Object.defineProperty(exports2, name, { enumerable: true, get: getter });
            }
          };
          __webpack_require__.r = function(exports2) {
            if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
              Object.defineProperty(exports2, Symbol.toStringTag, { value: "Module" });
            }
            Object.defineProperty(exports2, "__esModule", { value: true });
          };
          __webpack_require__.t = function(value, mode) {
            if (mode & 1) value = __webpack_require__(value);
            if (mode & 8) return value;
            if (mode & 4 && typeof value === "object" && value && value.__esModule) return value;
            var ns = /* @__PURE__ */ Object.create(null);
            __webpack_require__.r(ns);
            Object.defineProperty(ns, "default", { enumerable: true, value });
            if (mode & 2 && typeof value != "string") for (var key in value) __webpack_require__.d(ns, key, (function(key2) {
              return value[key2];
            }).bind(null, key));
            return ns;
          };
          __webpack_require__.n = function(module2) {
            var getter = module2 && module2.__esModule ? (
              /******/
              function getDefault() {
                return module2["default"];
              }
            ) : (
              /******/
              function getModuleExports() {
                return module2;
              }
            );
            __webpack_require__.d(getter, "a", getter);
            return getter;
          };
          __webpack_require__.o = function(object, property) {
            return Object.prototype.hasOwnProperty.call(object, property);
          };
          __webpack_require__.p = "";
          return __webpack_require__(__webpack_require__.s = 103);
        }([
          /* 0 */
          /***/
          function(module2, exports2) {
            module2.exports = __WEBPACK_EXTERNAL_MODULE__0__;
          },
          /* 1 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.r(__webpack_exports__);
            __webpack_require__.d(__webpack_exports__, "__extends", function() {
              return __extends;
            });
            __webpack_require__.d(__webpack_exports__, "__assign", function() {
              return __assign;
            });
            __webpack_require__.d(__webpack_exports__, "__rest", function() {
              return __rest;
            });
            __webpack_require__.d(__webpack_exports__, "__decorate", function() {
              return __decorate;
            });
            __webpack_require__.d(__webpack_exports__, "__param", function() {
              return __param;
            });
            __webpack_require__.d(__webpack_exports__, "__metadata", function() {
              return __metadata;
            });
            __webpack_require__.d(__webpack_exports__, "__awaiter", function() {
              return __awaiter;
            });
            __webpack_require__.d(__webpack_exports__, "__generator", function() {
              return __generator;
            });
            __webpack_require__.d(__webpack_exports__, "__createBinding", function() {
              return __createBinding;
            });
            __webpack_require__.d(__webpack_exports__, "__exportStar", function() {
              return __exportStar;
            });
            __webpack_require__.d(__webpack_exports__, "__values", function() {
              return __values;
            });
            __webpack_require__.d(__webpack_exports__, "__read", function() {
              return __read;
            });
            __webpack_require__.d(__webpack_exports__, "__spread", function() {
              return __spread;
            });
            __webpack_require__.d(__webpack_exports__, "__spreadArrays", function() {
              return __spreadArrays;
            });
            __webpack_require__.d(__webpack_exports__, "__await", function() {
              return __await;
            });
            __webpack_require__.d(__webpack_exports__, "__asyncGenerator", function() {
              return __asyncGenerator;
            });
            __webpack_require__.d(__webpack_exports__, "__asyncDelegator", function() {
              return __asyncDelegator;
            });
            __webpack_require__.d(__webpack_exports__, "__asyncValues", function() {
              return __asyncValues;
            });
            __webpack_require__.d(__webpack_exports__, "__makeTemplateObject", function() {
              return __makeTemplateObject;
            });
            __webpack_require__.d(__webpack_exports__, "__importStar", function() {
              return __importStar;
            });
            __webpack_require__.d(__webpack_exports__, "__importDefault", function() {
              return __importDefault;
            });
            __webpack_require__.d(__webpack_exports__, "__classPrivateFieldGet", function() {
              return __classPrivateFieldGet;
            });
            __webpack_require__.d(__webpack_exports__, "__classPrivateFieldSet", function() {
              return __classPrivateFieldSet;
            });
            var extendStatics = function(d, b) {
              extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
                d2.__proto__ = b2;
              } || function(d2, b2) {
                for (var p in b2) if (b2.hasOwnProperty(p)) d2[p] = b2[p];
              };
              return extendStatics(d, b);
            };
            function __extends(d, b) {
              extendStatics(d, b);
              function __() {
                this.constructor = d;
              }
              d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
            }
            var __assign = function() {
              __assign = Object.assign || function __assign2(t) {
                for (var s, i = 1, n = arguments.length; i < n; i++) {
                  s = arguments[i];
                  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
                }
                return t;
              };
              return __assign.apply(this, arguments);
            };
            function __rest(s, e) {
              var t = {};
              for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
                t[p] = s[p];
              if (s != null && typeof Object.getOwnPropertySymbols === "function")
                for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
                  if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                    t[p[i]] = s[p[i]];
                }
              return t;
            }
            function __decorate(decorators, target, key, desc) {
              var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
              if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
              else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
              return c > 3 && r && Object.defineProperty(target, key, r), r;
            }
            function __param(paramIndex, decorator) {
              return function(target, key) {
                decorator(target, key, paramIndex);
              };
            }
            function __metadata(metadataKey, metadataValue) {
              if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(metadataKey, metadataValue);
            }
            function __awaiter(thisArg, _arguments, P, generator) {
              function adopt(value) {
                return value instanceof P ? value : new P(function(resolve) {
                  resolve(value);
                });
              }
              return new (P || (P = Promise))(function(resolve, reject) {
                function fulfilled(value) {
                  try {
                    step(generator.next(value));
                  } catch (e) {
                    reject(e);
                  }
                }
                function rejected(value) {
                  try {
                    step(generator["throw"](value));
                  } catch (e) {
                    reject(e);
                  }
                }
                function step(result) {
                  result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
                }
                step((generator = generator.apply(thisArg, _arguments || [])).next());
              });
            }
            function __generator(thisArg, body) {
              var _ = { label: 0, sent: function() {
                if (t[0] & 1) throw t[1];
                return t[1];
              }, trys: [], ops: [] }, f, y, t, g;
              return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
                return this;
              }), g;
              function verb(n) {
                return function(v) {
                  return step([n, v]);
                };
              }
              function step(op) {
                if (f) throw new TypeError("Generator is already executing.");
                while (_) try {
                  if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
                  if (y = 0, t) op = [op[0] & 2, t.value];
                  switch (op[0]) {
                    case 0:
                    case 1:
                      t = op;
                      break;
                    case 4:
                      _.label++;
                      return { value: op[1], done: false };
                    case 5:
                      _.label++;
                      y = op[1];
                      op = [0];
                      continue;
                    case 7:
                      op = _.ops.pop();
                      _.trys.pop();
                      continue;
                    default:
                      if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                      }
                      if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                      }
                      if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                      }
                      if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                      }
                      if (t[2]) _.ops.pop();
                      _.trys.pop();
                      continue;
                  }
                  op = body.call(thisArg, _);
                } catch (e) {
                  op = [6, e];
                  y = 0;
                } finally {
                  f = t = 0;
                }
                if (op[0] & 5) throw op[1];
                return { value: op[0] ? op[1] : void 0, done: true };
              }
            }
            function __createBinding(o, m, k, k2) {
              if (k2 === void 0) k2 = k;
              o[k2] = m[k];
            }
            function __exportStar(m, exports2) {
              for (var p in m) if (p !== "default" && !exports2.hasOwnProperty(p)) exports2[p] = m[p];
            }
            function __values(o) {
              var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
              if (m) return m.call(o);
              if (o && typeof o.length === "number") return {
                next: function() {
                  if (o && i >= o.length) o = void 0;
                  return { value: o && o[i++], done: !o };
                }
              };
              throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
            }
            function __read(o, n) {
              var m = typeof Symbol === "function" && o[Symbol.iterator];
              if (!m) return o;
              var i = m.call(o), r, ar = [], e;
              try {
                while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
              } catch (error) {
                e = { error };
              } finally {
                try {
                  if (r && !r.done && (m = i["return"])) m.call(i);
                } finally {
                  if (e) throw e.error;
                }
              }
              return ar;
            }
            function __spread() {
              for (var ar = [], i = 0; i < arguments.length; i++)
                ar = ar.concat(__read(arguments[i]));
              return ar;
            }
            function __spreadArrays() {
              for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
              for (var r = Array(s), k = 0, i = 0; i < il; i++)
                for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
                  r[k] = a[j];
              return r;
            }
            ;
            function __await(v) {
              return this instanceof __await ? (this.v = v, this) : new __await(v);
            }
            function __asyncGenerator(thisArg, _arguments, generator) {
              if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
              var g = generator.apply(thisArg, _arguments || []), i, q = [];
              return i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
                return this;
              }, i;
              function verb(n) {
                if (g[n]) i[n] = function(v) {
                  return new Promise(function(a, b) {
                    q.push([n, v, a, b]) > 1 || resume(n, v);
                  });
                };
              }
              function resume(n, v) {
                try {
                  step(g[n](v));
                } catch (e) {
                  settle(q[0][3], e);
                }
              }
              function step(r) {
                r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
              }
              function fulfill(value) {
                resume("next", value);
              }
              function reject(value) {
                resume("throw", value);
              }
              function settle(f, v) {
                if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
              }
            }
            function __asyncDelegator(o) {
              var i, p;
              return i = {}, verb("next"), verb("throw", function(e) {
                throw e;
              }), verb("return"), i[Symbol.iterator] = function() {
                return this;
              }, i;
              function verb(n, f) {
                i[n] = o[n] ? function(v) {
                  return (p = !p) ? { value: __await(o[n](v)), done: n === "return" } : f ? f(v) : v;
                } : f;
              }
            }
            function __asyncValues(o) {
              if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
              var m = o[Symbol.asyncIterator], i;
              return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
                return this;
              }, i);
              function verb(n) {
                i[n] = o[n] && function(v) {
                  return new Promise(function(resolve, reject) {
                    v = o[n](v), settle(resolve, reject, v.done, v.value);
                  });
                };
              }
              function settle(resolve, reject, d, v) {
                Promise.resolve(v).then(function(v2) {
                  resolve({ value: v2, done: d });
                }, reject);
              }
            }
            function __makeTemplateObject(cooked, raw) {
              if (Object.defineProperty) {
                Object.defineProperty(cooked, "raw", { value: raw });
              } else {
                cooked.raw = raw;
              }
              return cooked;
            }
            ;
            function __importStar(mod) {
              if (mod && mod.__esModule) return mod;
              var result = {};
              if (mod != null) {
                for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
              }
              result.default = mod;
              return result;
            }
            function __importDefault(mod) {
              return mod && mod.__esModule ? mod : { default: mod };
            }
            function __classPrivateFieldGet(receiver, privateMap) {
              if (!privateMap.has(receiver)) {
                throw new TypeError("attempted to get private field on non-instance");
              }
              return privateMap.get(receiver);
            }
            function __classPrivateFieldSet(receiver, privateMap, value) {
              if (!privateMap.has(receiver)) {
                throw new TypeError("attempted to set private field on non-instance");
              }
              privateMap.set(receiver, value);
              return value;
            }
          },
          /* 2 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return vue_class_component__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
            __webpack_require__.d(__webpack_exports__, "e", function() {
              return vue_class_component__WEBPACK_IMPORTED_MODULE_0__["b"];
            });
            var _decorators_Emit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(52);
            var _decorators_Inject__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(53);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _decorators_Inject__WEBPACK_IMPORTED_MODULE_2__["a"];
            });
            var _decorators_Model__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54);
            var _decorators_Prop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(55);
            __webpack_require__.d(__webpack_exports__, "c", function() {
              return _decorators_Prop__WEBPACK_IMPORTED_MODULE_4__["a"];
            });
            var _decorators_Provide__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(56);
            __webpack_require__.d(__webpack_exports__, "d", function() {
              return _decorators_Provide__WEBPACK_IMPORTED_MODULE_5__["a"];
            });
            var _decorators_Ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(57);
            var _decorators_Watch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(58);
            __webpack_require__.d(__webpack_exports__, "f", function() {
              return _decorators_Watch__WEBPACK_IMPORTED_MODULE_7__["a"];
            });
          },
          /* 3 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return Options;
            });
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return Vue;
            });
            __webpack_require__.d(__webpack_exports__, "c", function() {
              return createDecorator;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function _classCallCheck(instance, Constructor) {
              if (!(instance instanceof Constructor)) {
                throw new TypeError("Cannot call a class as a function");
              }
            }
            function _defineProperties(target, props) {
              for (var i = 0; i < props.length; i++) {
                var descriptor = props[i];
                descriptor.enumerable = descriptor.enumerable || false;
                descriptor.configurable = true;
                if ("value" in descriptor) descriptor.writable = true;
                Object.defineProperty(target, descriptor.key, descriptor);
              }
            }
            function _createClass(Constructor, protoProps, staticProps) {
              if (protoProps) _defineProperties(Constructor.prototype, protoProps);
              if (staticProps) _defineProperties(Constructor, staticProps);
              return Constructor;
            }
            function _defineProperty(obj, key, value) {
              if (key in obj) {
                Object.defineProperty(obj, key, {
                  value,
                  enumerable: true,
                  configurable: true,
                  writable: true
                });
              } else {
                obj[key] = value;
              }
              return obj;
            }
            function ownKeys(object, enumerableOnly) {
              var keys = Object.keys(object);
              if (Object.getOwnPropertySymbols) {
                var symbols = Object.getOwnPropertySymbols(object);
                if (enumerableOnly) symbols = symbols.filter(function(sym) {
                  return Object.getOwnPropertyDescriptor(object, sym).enumerable;
                });
                keys.push.apply(keys, symbols);
              }
              return keys;
            }
            function _objectSpread2(target) {
              for (var i = 1; i < arguments.length; i++) {
                var source = arguments[i] != null ? arguments[i] : {};
                if (i % 2) {
                  ownKeys(Object(source), true).forEach(function(key) {
                    _defineProperty(target, key, source[key]);
                  });
                } else if (Object.getOwnPropertyDescriptors) {
                  Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
                } else {
                  ownKeys(Object(source)).forEach(function(key) {
                    Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
                  });
                }
              }
              return target;
            }
            function _inherits(subClass, superClass) {
              if (typeof superClass !== "function" && superClass !== null) {
                throw new TypeError("Super expression must either be null or a function");
              }
              subClass.prototype = Object.create(superClass && superClass.prototype, {
                constructor: {
                  value: subClass,
                  writable: true,
                  configurable: true
                }
              });
              if (superClass) _setPrototypeOf(subClass, superClass);
            }
            function _getPrototypeOf(o) {
              _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf2(o2) {
                return o2.__proto__ || Object.getPrototypeOf(o2);
              };
              return _getPrototypeOf(o);
            }
            function _setPrototypeOf(o, p) {
              _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf2(o2, p2) {
                o2.__proto__ = p2;
                return o2;
              };
              return _setPrototypeOf(o, p);
            }
            function _isNativeReflectConstruct() {
              if (typeof Reflect === "undefined" || !Reflect.construct) return false;
              if (Reflect.construct.sham) return false;
              if (typeof Proxy === "function") return true;
              try {
                Date.prototype.toString.call(Reflect.construct(Date, [], function() {
                }));
                return true;
              } catch (e) {
                return false;
              }
            }
            function _construct(Parent, args, Class) {
              if (_isNativeReflectConstruct()) {
                _construct = Reflect.construct;
              } else {
                _construct = function _construct2(Parent2, args2, Class2) {
                  var a = [null];
                  a.push.apply(a, args2);
                  var Constructor = Function.bind.apply(Parent2, a);
                  var instance = new Constructor();
                  if (Class2) _setPrototypeOf(instance, Class2.prototype);
                  return instance;
                };
              }
              return _construct.apply(null, arguments);
            }
            function _assertThisInitialized(self2) {
              if (self2 === void 0) {
                throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
              }
              return self2;
            }
            function _possibleConstructorReturn(self2, call) {
              if (call && (typeof call === "object" || typeof call === "function")) {
                return call;
              }
              return _assertThisInitialized(self2);
            }
            function _createSuper(Derived) {
              var hasNativeReflectConstruct = _isNativeReflectConstruct();
              return function _createSuperInternal() {
                var Super = _getPrototypeOf(Derived), result;
                if (hasNativeReflectConstruct) {
                  var NewTarget = _getPrototypeOf(this).constructor;
                  result = Reflect.construct(Super, arguments, NewTarget);
                } else {
                  result = Super.apply(this, arguments);
                }
                return _possibleConstructorReturn(this, result);
              };
            }
            function _toConsumableArray(arr) {
              return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();
            }
            function _arrayWithoutHoles(arr) {
              if (Array.isArray(arr)) return _arrayLikeToArray(arr);
            }
            function _iterableToArray(iter) {
              if (typeof Symbol !== "undefined" && Symbol.iterator in Object(iter)) return Array.from(iter);
            }
            function _unsupportedIterableToArray(o, minLen) {
              if (!o) return;
              if (typeof o === "string") return _arrayLikeToArray(o, minLen);
              var n = Object.prototype.toString.call(o).slice(8, -1);
              if (n === "Object" && o.constructor) n = o.constructor.name;
              if (n === "Map" || n === "Set") return Array.from(o);
              if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
            }
            function _arrayLikeToArray(arr, len) {
              if (len == null || len > arr.length) len = arr.length;
              for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];
              return arr2;
            }
            function _nonIterableSpread() {
              throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
            }
            function defineGetter(obj, key, getter) {
              Object.defineProperty(obj, key, {
                get: getter,
                enumerable: false,
                configurable: true
              });
            }
            function defineProxy(proxy, key, target) {
              Object.defineProperty(proxy, key, {
                get: function get() {
                  return target[key].value;
                },
                set: function set(value) {
                  target[key].value = value;
                },
                enumerable: true,
                configurable: true
              });
            }
            function getSuper(Ctor) {
              var superProto = Object.getPrototypeOf(Ctor.prototype);
              if (!superProto) {
                return void 0;
              }
              return superProto.constructor;
            }
            function getOwn(value, key) {
              return value.hasOwnProperty(key) ? value[key] : void 0;
            }
            var VueImpl = function() {
              function VueImpl2(props, ctx) {
                var _this = this;
                _classCallCheck(this, VueImpl2);
                defineGetter(this, "$props", function() {
                  return props;
                });
                defineGetter(this, "$attrs", function() {
                  return ctx.attrs;
                });
                defineGetter(this, "$slots", function() {
                  return ctx.slots;
                });
                defineGetter(this, "$emit", function() {
                  return ctx.emit;
                });
                Object.keys(props).forEach(function(key) {
                  Object.defineProperty(_this, key, {
                    enumerable: false,
                    configurable: true,
                    writable: true,
                    value: props[key]
                  });
                });
              }
              _createClass(VueImpl2, null, [{
                key: "registerHooks",
                value: function registerHooks(keys) {
                  var _this$__h;
                  (_this$__h = this.__h).push.apply(_this$__h, _toConsumableArray(keys));
                }
              }, {
                key: "with",
                value: function _with(Props) {
                  var propsMeta = new Props();
                  var props = {};
                  Object.keys(propsMeta).forEach(function(key) {
                    var meta = propsMeta[key];
                    props[key] = meta !== null && meta !== void 0 ? meta : null;
                  });
                  var PropsMixin = function(_this2) {
                    _inherits(PropsMixin2, _this2);
                    var _super = _createSuper(PropsMixin2);
                    function PropsMixin2() {
                      _classCallCheck(this, PropsMixin2);
                      return _super.apply(this, arguments);
                    }
                    return PropsMixin2;
                  }(this);
                  PropsMixin.__b = {
                    props
                  };
                  return PropsMixin;
                }
              }, {
                key: "__vccOpts",
                get: function get() {
                  if (this === Vue) {
                    return {};
                  }
                  var Ctor = this;
                  var cache = getOwn(Ctor, "__c");
                  if (cache) {
                    return cache;
                  }
                  var options = _objectSpread2({}, getOwn(Ctor, "__o"));
                  Ctor.__c = options;
                  var Super = getSuper(Ctor);
                  if (Super) {
                    options["extends"] = Super.__vccOpts;
                  }
                  var base = getOwn(Ctor, "__b");
                  if (base) {
                    options.mixins = options.mixins || [];
                    options.mixins.unshift(base);
                  }
                  options.methods = _objectSpread2({}, options.methods);
                  options.computed = _objectSpread2({}, options.computed);
                  var proto = Ctor.prototype;
                  Object.getOwnPropertyNames(proto).forEach(function(key) {
                    if (key === "constructor") {
                      return;
                    }
                    if (Ctor.__h.indexOf(key) > -1) {
                      options[key] = proto[key];
                      return;
                    }
                    var descriptor = Object.getOwnPropertyDescriptor(proto, key);
                    if (typeof descriptor.value === "function") {
                      options.methods[key] = descriptor.value;
                      return;
                    }
                    if (descriptor.get || descriptor.set) {
                      options.computed[key] = {
                        get: descriptor.get,
                        set: descriptor.set
                      };
                      return;
                    }
                  });
                  options.setup = function(props, ctx) {
                    var _promise;
                    var data = new Ctor(props, ctx);
                    var dataKeys = Object.keys(data);
                    var plainData = {};
                    var promise = null;
                    dataKeys.forEach(function(key) {
                      if (data[key] === void 0 || data[key] && data[key].__s) {
                        return;
                      }
                      plainData[key] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["ref"])(data[key]);
                      defineProxy(data, key, plainData);
                    });
                    dataKeys.forEach(function(key) {
                      if (data[key] && data[key].__s) {
                        var setupState = data[key].__s();
                        if (setupState instanceof Promise) {
                          if (!promise) {
                            promise = Promise.resolve(plainData);
                          }
                          promise = promise.then(function() {
                            return setupState.then(function(value) {
                              plainData[key] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["proxyRefs"])(value);
                              return plainData;
                            });
                          });
                        } else {
                          plainData[key] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["proxyRefs"])(setupState);
                        }
                      }
                    });
                    return (_promise = promise) !== null && _promise !== void 0 ? _promise : plainData;
                  };
                  var decorators = getOwn(Ctor, "__d");
                  if (decorators) {
                    decorators.forEach(function(fn) {
                      return fn(options);
                    });
                  }
                  var injections = ["render", "ssrRender", "__file", "__cssModules", "__scopeId", "__hmrId"];
                  injections.forEach(function(key) {
                    if (Ctor[key]) {
                      options[key] = Ctor[key];
                    }
                  });
                  return options;
                }
              }]);
              return VueImpl2;
            }();
            VueImpl.__h = ["data", "beforeCreate", "created", "beforeMount", "mounted", "beforeUnmount", "unmounted", "beforeUpdate", "updated", "activated", "deactivated", "render", "errorCaptured", "serverPrefetch"];
            var Vue = VueImpl;
            function Options(options) {
              return function(Component) {
                Component.__o = options;
                return Component;
              };
            }
            function createDecorator(factory) {
              return function(target, key, index) {
                var Ctor = typeof target === "function" ? target : target.constructor;
                if (!Ctor.__d) {
                  Ctor.__d = [];
                }
                if (typeof index !== "number") {
                  index = void 0;
                }
                Ctor.__d.push(function(options) {
                  return factory(options, key, index);
                });
              };
            }
            function mixins() {
              for (var _len = arguments.length, Ctors = new Array(_len), _key = 0; _key < _len; _key++) {
                Ctors[_key] = arguments[_key];
              }
              var _a;
              return _a = function(_Vue) {
                _inherits(MixedVue, _Vue);
                var _super = _createSuper(MixedVue);
                function MixedVue() {
                  var _this;
                  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
                    args[_key2] = arguments[_key2];
                  }
                  _classCallCheck(this, MixedVue);
                  _this = _super.call.apply(_super, [this].concat(args));
                  Ctors.forEach(function(Ctor) {
                    var data = _construct(Ctor, args);
                    Object.keys(data).forEach(function(key) {
                      _this[key] = data[key];
                    });
                  });
                  return _this;
                }
                return MixedVue;
              }(Vue), _a.__b = {
                mixins: Ctors.map(function(Ctor) {
                  return Ctor.__vccOpts;
                })
              }, _a;
            }
            function setup(setupFn) {
              return {
                __s: setupFn
              };
            }
            function prop(options) {
              return options;
            }
          },
          /* 4 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(59);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _connection__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
            var _editor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60);
            var _eventDataTypes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(61);
            var _node__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(62);
            var _nodeInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(63);
            var _nodeOption__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(64);
            var _state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(65);
            var _plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(66);
          },
          /* 5 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return resizeObservers;
            });
            var resizeObservers = [];
          },
          /* 6 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "d", function() {
              return isSVG;
            });
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return isHidden;
            });
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return isElement;
            });
            __webpack_require__.d(__webpack_exports__, "c", function() {
              return isReplacedElement;
            });
            var isSVG = function(target) {
              return target instanceof SVGElement && "getBBox" in target;
            };
            var isHidden = function(target) {
              if (isSVG(target)) {
                var _a = target.getBBox(), width = _a.width, height = _a.height;
                return !width && !height;
              }
              var _b = target, offsetWidth = _b.offsetWidth, offsetHeight = _b.offsetHeight;
              return !(offsetWidth || offsetHeight || target.getClientRects().length);
            };
            var isElement = function(obj) {
              var _a, _b;
              var scope = (_b = (_a = obj) === null || _a === void 0 ? void 0 : _a.ownerDocument) === null || _b === void 0 ? void 0 : _b.defaultView;
              return !!(scope && obj instanceof scope.Element);
            };
            var isReplacedElement = function(target) {
              switch (target.tagName) {
                case "INPUT":
                  if (target.type !== "image") {
                    break;
                  }
                case "VIDEO":
                case "AUDIO":
                case "EMBED":
                case "OBJECT":
                case "CANVAS":
                case "IFRAME":
                case "IMG":
                  return true;
              }
              return false;
            };
          },
          /* 7 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return getDomElementOfNode;
            });
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return getDomElements;
            });
            function getDomElementOfNode(node) {
              return document.getElementById(node.id);
            }
            function getDomElements(ni) {
              var nodeDOM = document.getElementById(ni.parent.id);
              var interfaceDOM = document.getElementById(ni.id);
              var portDOM = interfaceDOM === null || interfaceDOM === void 0 ? void 0 : interfaceDOM.getElementsByClassName("__port");
              return {
                node: nodeDOM,
                interface: interfaceDOM,
                port: portDOM && portDOM.length > 0 ? portDOM[0] : null
              };
            }
          },
          /* 8 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return global;
            });
            var global = typeof window !== "undefined" ? window : {};
          },
          /* 9 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _ConnectionView_vue_vue_type_template_id_21b4c967__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82);
            var _ConnectionView_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(13);
            _ConnectionView_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _ConnectionView_vue_vue_type_template_id_21b4c967__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _ConnectionView_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 10 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return calculateBoxSize;
            });
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return calculateBoxSizes;
            });
            var _ResizeObserverBoxOptions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(22);
            var _DOMRectReadOnly__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(49);
            var _utils_element__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6);
            var _utils_global__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(8);
            var cache = /* @__PURE__ */ new WeakMap();
            var scrollRegexp = /auto|scroll/;
            var verticalRegexp = /^tb|vertical/;
            var IE = /msie|trident/i.test(_utils_global__WEBPACK_IMPORTED_MODULE_3__[
              /* global */
              "a"
            ].navigator && _utils_global__WEBPACK_IMPORTED_MODULE_3__[
              /* global */
              "a"
            ].navigator.userAgent);
            var parseDimension = function(pixel) {
              return parseFloat(pixel || "0");
            };
            var size = function(inlineSize, blockSize, switchSizes) {
              if (inlineSize === void 0) {
                inlineSize = 0;
              }
              if (blockSize === void 0) {
                blockSize = 0;
              }
              if (switchSizes === void 0) {
                switchSizes = false;
              }
              return Object.freeze({
                inlineSize: (switchSizes ? blockSize : inlineSize) || 0,
                blockSize: (switchSizes ? inlineSize : blockSize) || 0
              });
            };
            var zeroBoxes = Object.freeze({
              devicePixelContentBoxSize: size(),
              borderBoxSize: size(),
              contentBoxSize: size(),
              contentRect: new _DOMRectReadOnly__WEBPACK_IMPORTED_MODULE_1__[
                /* DOMRectReadOnly */
                "a"
              ](0, 0, 0, 0)
            });
            var calculateBoxSizes = function(target, forceRecalculation) {
              if (forceRecalculation === void 0) {
                forceRecalculation = false;
              }
              if (cache.has(target) && !forceRecalculation) {
                return cache.get(target);
              }
              if (Object(_utils_element__WEBPACK_IMPORTED_MODULE_2__[
                /* isHidden */
                "b"
              ])(target)) {
                cache.set(target, zeroBoxes);
                return zeroBoxes;
              }
              var cs = getComputedStyle(target);
              var svg = Object(_utils_element__WEBPACK_IMPORTED_MODULE_2__[
                /* isSVG */
                "d"
              ])(target) && target.ownerSVGElement && target.getBBox();
              var removePadding = !IE && cs.boxSizing === "border-box";
              var switchSizes = verticalRegexp.test(cs.writingMode || "");
              var canScrollVertically = !svg && scrollRegexp.test(cs.overflowY || "");
              var canScrollHorizontally = !svg && scrollRegexp.test(cs.overflowX || "");
              var paddingTop = svg ? 0 : parseDimension(cs.paddingTop);
              var paddingRight = svg ? 0 : parseDimension(cs.paddingRight);
              var paddingBottom = svg ? 0 : parseDimension(cs.paddingBottom);
              var paddingLeft = svg ? 0 : parseDimension(cs.paddingLeft);
              var borderTop = svg ? 0 : parseDimension(cs.borderTopWidth);
              var borderRight = svg ? 0 : parseDimension(cs.borderRightWidth);
              var borderBottom = svg ? 0 : parseDimension(cs.borderBottomWidth);
              var borderLeft = svg ? 0 : parseDimension(cs.borderLeftWidth);
              var horizontalPadding = paddingLeft + paddingRight;
              var verticalPadding = paddingTop + paddingBottom;
              var horizontalBorderArea = borderLeft + borderRight;
              var verticalBorderArea = borderTop + borderBottom;
              var horizontalScrollbarThickness = !canScrollHorizontally ? 0 : target.offsetHeight - verticalBorderArea - target.clientHeight;
              var verticalScrollbarThickness = !canScrollVertically ? 0 : target.offsetWidth - horizontalBorderArea - target.clientWidth;
              var widthReduction = removePadding ? horizontalPadding + horizontalBorderArea : 0;
              var heightReduction = removePadding ? verticalPadding + verticalBorderArea : 0;
              var contentWidth = svg ? svg.width : parseDimension(cs.width) - widthReduction - verticalScrollbarThickness;
              var contentHeight = svg ? svg.height : parseDimension(cs.height) - heightReduction - horizontalScrollbarThickness;
              var borderBoxWidth = contentWidth + horizontalPadding + verticalScrollbarThickness + horizontalBorderArea;
              var borderBoxHeight = contentHeight + verticalPadding + horizontalScrollbarThickness + verticalBorderArea;
              var boxes = Object.freeze({
                devicePixelContentBoxSize: size(Math.round(contentWidth * devicePixelRatio), Math.round(contentHeight * devicePixelRatio), switchSizes),
                borderBoxSize: size(borderBoxWidth, borderBoxHeight, switchSizes),
                contentBoxSize: size(contentWidth, contentHeight, switchSizes),
                contentRect: new _DOMRectReadOnly__WEBPACK_IMPORTED_MODULE_1__[
                  /* DOMRectReadOnly */
                  "a"
                ](paddingLeft, paddingTop, contentWidth, contentHeight)
              });
              cache.set(target, boxes);
              return boxes;
            };
            var calculateBoxSize = function(target, observedBox, forceRecalculation) {
              var _a = calculateBoxSizes(target, forceRecalculation), borderBoxSize = _a.borderBoxSize, contentBoxSize = _a.contentBoxSize, devicePixelContentBoxSize = _a.devicePixelContentBoxSize;
              switch (observedBox) {
                case _ResizeObserverBoxOptions__WEBPACK_IMPORTED_MODULE_0__[
                  /* ResizeObserverBoxOptions */
                  "a"
                ].DEVICE_PIXEL_CONTENT_BOX:
                  return devicePixelContentBoxSize;
                case _ResizeObserverBoxOptions__WEBPACK_IMPORTED_MODULE_0__[
                  /* ResizeObserverBoxOptions */
                  "a"
                ].BORDER_BOX:
                  return borderBoxSize;
                default:
                  return contentBoxSize;
              }
            };
          },
          /* 11 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return getPortCoordinates;
            });
            function getPortCoordinates(resolved) {
              if (resolved.node && resolved.interface && resolved.port) {
                return [
                  resolved.node.offsetLeft + resolved.interface.offsetLeft + resolved.port.offsetLeft + resolved.port.clientWidth / 2,
                  resolved.node.offsetTop + resolved.interface.offsetTop + resolved.port.offsetTop + resolved.port.clientHeight / 2
                ];
              } else {
                return [0, 0];
              }
            }
          },
          /* 12 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Editor_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Editor_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 13 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_ConnectionView_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(26);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_ConnectionView_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 14 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_ConnectionWrapper_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(27);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_ConnectionWrapper_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 15 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_TemporaryConnection_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(28);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_TemporaryConnection_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 16 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Node_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(29);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Node_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 17 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_NodeInterface_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(30);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_NodeInterface_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 18 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_NodeOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(31);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_NodeOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 19 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_ContextMenu_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(32);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_ContextMenu_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 20 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Sidebar_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(33);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Sidebar_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 21 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Minimap_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(34);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Minimap_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 22 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return ResizeObserverBoxOptions;
            });
            var ResizeObserverBoxOptions;
            (function(ResizeObserverBoxOptions2) {
              ResizeObserverBoxOptions2["BORDER_BOX"] = "border-box";
              ResizeObserverBoxOptions2["CONTENT_BOX"] = "content-box";
              ResizeObserverBoxOptions2["DEVICE_PIXEL_CONTENT_BOX"] = "device-pixel-content-box";
            })(ResizeObserverBoxOptions || (ResizeObserverBoxOptions = {}));
          },
          /* 23 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return ResizeObserverController;
            });
            var _utils_scheduler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(46);
            var _ResizeObservation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(92);
            var _ResizeObserverDetail__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(93);
            var _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5);
            var observerMap = /* @__PURE__ */ new WeakMap();
            var getObservationIndex = function(observationTargets, target) {
              for (var i = 0; i < observationTargets.length; i += 1) {
                if (observationTargets[i].target === target) {
                  return i;
                }
              }
              return -1;
            };
            var ResizeObserverController = function() {
              function ResizeObserverController2() {
              }
              ResizeObserverController2.connect = function(resizeObserver, callback) {
                var detail = new _ResizeObserverDetail__WEBPACK_IMPORTED_MODULE_2__[
                  /* ResizeObserverDetail */
                  "a"
                ](resizeObserver, callback);
                observerMap.set(resizeObserver, detail);
              };
              ResizeObserverController2.observe = function(resizeObserver, target, options) {
                var detail = observerMap.get(resizeObserver);
                var firstObservation = detail.observationTargets.length === 0;
                if (getObservationIndex(detail.observationTargets, target) < 0) {
                  firstObservation && _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_3__[
                    /* resizeObservers */
                    "a"
                  ].push(detail);
                  detail.observationTargets.push(new _ResizeObservation__WEBPACK_IMPORTED_MODULE_1__[
                    /* ResizeObservation */
                    "a"
                  ](target, options && options.box));
                  Object(_utils_scheduler__WEBPACK_IMPORTED_MODULE_0__[
                    /* updateCount */
                    "b"
                  ])(1);
                  _utils_scheduler__WEBPACK_IMPORTED_MODULE_0__[
                    /* scheduler */
                    "a"
                  ].schedule();
                }
              };
              ResizeObserverController2.unobserve = function(resizeObserver, target) {
                var detail = observerMap.get(resizeObserver);
                var index = getObservationIndex(detail.observationTargets, target);
                var lastObservation = detail.observationTargets.length === 1;
                if (index >= 0) {
                  lastObservation && _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_3__[
                    /* resizeObservers */
                    "a"
                  ].splice(_utils_resizeObservers__WEBPACK_IMPORTED_MODULE_3__[
                    /* resizeObservers */
                    "a"
                  ].indexOf(detail), 1);
                  detail.observationTargets.splice(index, 1);
                  Object(_utils_scheduler__WEBPACK_IMPORTED_MODULE_0__[
                    /* updateCount */
                    "b"
                  ])(-1);
                }
              };
              ResizeObserverController2.disconnect = function(resizeObserver) {
                var _this = this;
                var detail = observerMap.get(resizeObserver);
                detail.observationTargets.slice().forEach(function(ot) {
                  return _this.unobserve(resizeObserver, ot.target);
                });
                detail.activeTargets.splice(0, detail.activeTargets.length);
              };
              return ResizeObserverController2;
            }();
          },
          /* 24 */
          /***/
          function(module2, exports2, __webpack_require__) {
            "use strict";
            Object.defineProperty(exports2, "__esModule", { value: true });
            exports2.SequentialHook = exports2.Hook = exports2.PreventableBaklavaEvent = exports2.BaklavaEvent = void 0;
            var tslib_1 = __webpack_require__(1);
            var BaklavaEvent = (
              /** @class */
              function() {
                function BaklavaEvent2() {
                  this.listeners = /* @__PURE__ */ new Map();
                }
                BaklavaEvent2.prototype.addListener = function(token, listener) {
                  this.listeners.set(token, listener);
                };
                BaklavaEvent2.prototype.removeListener = function(token) {
                  if (this.listeners.has(token)) {
                    this.listeners.delete(token);
                  }
                };
                BaklavaEvent2.prototype.emit = function(data) {
                  this.listeners.forEach(function(l) {
                    return l(data);
                  });
                };
                return BaklavaEvent2;
              }()
            );
            exports2.BaklavaEvent = BaklavaEvent;
            var PreventableBaklavaEvent = (
              /** @class */
              function(_super) {
                tslib_1.__extends(PreventableBaklavaEvent2, _super);
                function PreventableBaklavaEvent2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                PreventableBaklavaEvent2.prototype.emit = function(data) {
                  var e_1, _a;
                  try {
                    for (var _b = tslib_1.__values(Array.from(this.listeners.values())), _c = _b.next(); !_c.done; _c = _b.next()) {
                      var l = _c.value;
                      if (l(data) === false) {
                        return true;
                      }
                    }
                  } catch (e_1_1) {
                    e_1 = { error: e_1_1 };
                  } finally {
                    try {
                      if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                    } finally {
                      if (e_1) throw e_1.error;
                    }
                  }
                  return false;
                };
                return PreventableBaklavaEvent2;
              }(BaklavaEvent)
            );
            exports2.PreventableBaklavaEvent = PreventableBaklavaEvent;
            var Hook = (
              /** @class */
              function() {
                function Hook2() {
                  this.tapMap = /* @__PURE__ */ new Map();
                  this.taps = [];
                }
                Hook2.prototype.tap = function(token, tapFn) {
                  if (this.tapMap.has(token)) {
                    this.untap(token);
                  }
                  this.tapMap.set(token, tapFn);
                  this.taps.push(tapFn);
                };
                Hook2.prototype.untap = function(token) {
                  if (this.tapMap.has(token)) {
                    var tapFn = this.tapMap.get(token);
                    this.tapMap.delete(token);
                    var i = this.taps.indexOf(tapFn);
                    if (i >= 0) {
                      this.taps.splice(i, 1);
                    }
                  }
                };
                return Hook2;
              }()
            );
            exports2.Hook = Hook;
            var SequentialHook = (
              /** @class */
              function(_super) {
                tslib_1.__extends(SequentialHook2, _super);
                function SequentialHook2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                SequentialHook2.prototype.execute = function(data) {
                  var e_2, _a;
                  var currentValue = data;
                  try {
                    for (var _b = tslib_1.__values(this.taps), _c = _b.next(); !_c.done; _c = _b.next()) {
                      var tapFn = _c.value;
                      currentValue = tapFn(currentValue);
                    }
                  } catch (e_2_1) {
                    e_2 = { error: e_2_1 };
                  } finally {
                    try {
                      if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                    } finally {
                      if (e_2) throw e_2.error;
                    }
                  }
                  return currentValue;
                };
                return SequentialHook2;
              }(Hook)
            );
            exports2.SequentialHook = SequentialHook;
          },
          /* 25 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4);
            var _clipboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(80);
            var _history__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(81);
            var EditorView = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(EditorView2, _super);
                function EditorView2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.nodeeditor = _this;
                  _this.selectedNodeViews = [];
                  _this.temporaryConnection = null;
                  _this.hoveringOver = null;
                  _this.selectedNodes = [];
                  _this.ctrlPressed = false;
                  _this.draggingStartPoint = null;
                  _this.draggingStartPanning = null;
                  _this.counter = 0;
                  _this.contextMenu = {
                    items: [],
                    show: false,
                    x: 0,
                    y: 0
                  };
                  return _this;
                }
                Object.defineProperty(EditorView2.prototype, "styles", {
                  get: function() {
                    return {
                      "transform-origin": "0 0",
                      "transform": "scale(" + this.plugin.scaling + ") translate(" + this.plugin.panning.x + "px, " + this.plugin.panning.y + "px)"
                    };
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(EditorView2.prototype, "backgroundStyle", {
                  get: function() {
                    var positionLeft = this.plugin.panning.x * this.plugin.scaling;
                    var positionTop = this.plugin.panning.y * this.plugin.scaling;
                    var size = this.plugin.scaling * this.plugin.backgroundGrid.gridSize;
                    var subSize = size / this.plugin.backgroundGrid.gridDivision;
                    var backgroundSize = size + "px " + size + "px, " + size + "px " + size + "px";
                    var subGridBackgroundSize = this.plugin.scaling > this.plugin.backgroundGrid.subGridVisibleThreshold ? ", " + subSize + "px " + subSize + "px, " + subSize + "px " + subSize + "px" : "";
                    return {
                      "background-position": "left " + positionLeft + "px top " + positionTop + "px",
                      "background-size": backgroundSize + " " + subGridBackgroundSize
                    };
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(EditorView2.prototype, "nodes", {
                  get: function() {
                    return this.plugin.editor ? this.plugin.editor.nodes : [];
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(EditorView2.prototype, "connections", {
                  get: function() {
                    return this.plugin.editor ? this.plugin.editor.connections : [];
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(EditorView2.prototype, "hasEnginePlugin", {
                  get: function() {
                    var e_1, _a;
                    try {
                      for (var _b = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__values"])(this.plugin.editor.plugins.values()), _c = _b.next(); !_c.done; _c = _b.next()) {
                        var p = _c.value;
                        if (p.type === "EnginePlugin") {
                          return true;
                        }
                      }
                    } catch (e_1_1) {
                      e_1 = { error: e_1_1 };
                    } finally {
                      try {
                        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                      } finally {
                        if (e_1) throw e_1.error;
                      }
                    }
                    return false;
                  },
                  enumerable: false,
                  configurable: true
                });
                EditorView2.prototype.mounted = function() {
                  var _this = this;
                  this.updateContextMenu();
                  this.plugin.editor.events.registerNodeType.addListener(this, function() {
                    return _this.updateContextMenu();
                  });
                  this.plugin.editor.hooks.load.tap(this, function(s) {
                    _this.counter++;
                    return s;
                  });
                  this.clipboard = new _clipboard__WEBPACK_IMPORTED_MODULE_3__[
                    /* default */
                    "a"
                  ](this.plugin.editor);
                  this.history = new _history__WEBPACK_IMPORTED_MODULE_4__[
                    /* default */
                    "a"
                  ](this.plugin);
                };
                EditorView2.prototype.updateContextMenu = function() {
                  var _this = this;
                  var categories = Array.from(this.plugin.editor.nodeCategories.keys()).filter(function(c) {
                    return c !== "default";
                  }).map(function(c) {
                    var nodes = Array.from(_this.plugin.editor.nodeCategories.get(c)).map(function(n) {
                      return {
                        value: "addNode:" + n,
                        label: _this.plugin.nodeTypeAliases[n] || n
                      };
                    });
                    return { label: c, submenu: nodes };
                  });
                  var defaultNodes = this.plugin.editor.nodeCategories.get("default").map(function(n) {
                    return {
                      value: "addNode:" + n,
                      label: _this.plugin.nodeTypeAliases[n] || n
                    };
                  });
                  var addNodeSubmenu = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__spread"])(categories);
                  if (categories.length > 0 && defaultNodes.length > 0) {
                    addNodeSubmenu.push({ isDivider: true });
                  }
                  addNodeSubmenu.push.apply(addNodeSubmenu, Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__spread"])(defaultNodes));
                  this.contextMenu.items = [
                    {
                      label: "Add Node",
                      submenu: addNodeSubmenu
                    },
                    {
                      label: "Copy Nodes",
                      value: "copy",
                      disabledFunction: function() {
                        return _this.selectedNodes.length === 0;
                      }
                    },
                    {
                      label: "Paste Nodes",
                      value: "paste",
                      disabledFunction: function() {
                        return _this.clipboard.isEmpty;
                      }
                    }
                  ];
                };
                EditorView2.prototype.hoveredOver = function(ni) {
                  this.hoveringOver = ni;
                  if (ni && this.temporaryConnection) {
                    this.temporaryConnection.to = ni;
                    this.temporaryConnection.status = this.plugin.editor.checkConnection(this.temporaryConnection.from, this.temporaryConnection.to) ? _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__[
                      /* TemporaryConnectionState */
                      "a"
                    ].ALLOWED : _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__[
                      /* TemporaryConnectionState */
                      "a"
                    ].FORBIDDEN;
                    if (this.hasEnginePlugin) {
                      this.connections.filter(function(c) {
                        return c.to === ni;
                      }).forEach(function(c) {
                        c.isInDanger = true;
                      });
                    }
                  } else if (!ni && this.temporaryConnection) {
                    this.temporaryConnection.to = void 0;
                    this.temporaryConnection.status = _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__[
                      /* TemporaryConnectionState */
                      "a"
                    ].NONE;
                    this.connections.forEach(function(c) {
                      c.isInDanger = false;
                    });
                  }
                };
                EditorView2.prototype.mouseMoveHandler = function(ev) {
                  if (this.temporaryConnection) {
                    this.temporaryConnection.mx = ev.offsetX / this.plugin.scaling - this.plugin.panning.x;
                    this.temporaryConnection.my = ev.offsetY / this.plugin.scaling - this.plugin.panning.y;
                  } else if (this.draggingStartPoint) {
                    var dx = ev.screenX - this.draggingStartPoint.x;
                    var dy = ev.screenY - this.draggingStartPoint.y;
                    this.plugin.panning.x = this.draggingStartPanning.x + dx / this.plugin.scaling;
                    this.plugin.panning.y = this.draggingStartPanning.y + dy / this.plugin.scaling;
                  }
                };
                EditorView2.prototype.mouseDown = function(ev) {
                  var _this = this;
                  if (ev.button === 0) {
                    if (this.hoveringOver) {
                      var connection = this.connections.find(function(c) {
                        return c.to === _this.hoveringOver;
                      });
                      if (this.hoveringOver.isInput && connection) {
                        this.temporaryConnection = {
                          status: _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__[
                            /* TemporaryConnectionState */
                            "a"
                          ].NONE,
                          from: connection.from
                        };
                        this.plugin.editor.removeConnection(connection);
                      } else {
                        this.temporaryConnection = {
                          status: _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__[
                            /* TemporaryConnectionState */
                            "a"
                          ].NONE,
                          from: this.hoveringOver
                        };
                      }
                      this.temporaryConnection.mx = null;
                      this.temporaryConnection.my = null;
                    } else if (ev.target === this.$el) {
                      this.unselectAllNodes();
                      this.draggingStartPoint = {
                        x: ev.screenX,
                        y: ev.screenY
                      };
                      this.draggingStartPanning = {
                        x: this.plugin.panning.x,
                        y: this.plugin.panning.y
                      };
                    }
                  }
                };
                EditorView2.prototype.mouseUp = function() {
                  this.draggingStartPoint = null;
                  this.draggingStartPanning = null;
                  var tc = this.temporaryConnection;
                  if (tc && this.hoveringOver) {
                    this.plugin.editor.addConnection(tc.from, tc.to);
                  }
                  this.temporaryConnection = null;
                };
                EditorView2.prototype.mouseWheel = function(ev) {
                  ev.preventDefault();
                  var scrollAmount = ev.deltaY;
                  if (ev.deltaMode === 1) {
                    scrollAmount *= 32;
                  }
                  var newScale = this.plugin.scaling * (1 - scrollAmount / 3e3);
                  var currentPoint = [
                    ev.offsetX / this.plugin.scaling - this.plugin.panning.x,
                    ev.offsetY / this.plugin.scaling - this.plugin.panning.y
                  ];
                  var newPoint = [ev.offsetX / newScale - this.plugin.panning.x, ev.offsetY / newScale - this.plugin.panning.y];
                  var diff = [newPoint[0] - currentPoint[0], newPoint[1] - currentPoint[1]];
                  this.plugin.panning.x += diff[0];
                  this.plugin.panning.y += diff[1];
                  this.plugin.scaling = newScale;
                };
                EditorView2.prototype.keyDown = function(ev) {
                  var _this = this;
                  if (ev.key === "Delete" && this.selectedNodes.length > 0) {
                    this.selectedNodes.forEach(function(n) {
                      return _this.plugin.editor.removeNode(n);
                    });
                  } else if (ev.key === "Tab") {
                    ev.preventDefault();
                  } else if (ev.key === "Control") {
                    this.ctrlPressed = true;
                  } else if (ev.key === "z" && ev.ctrlKey) {
                    this.history.undo();
                  } else if (ev.key === "y" && ev.ctrlKey) {
                    this.history.redo();
                  }
                };
                EditorView2.prototype.keyUp = function(ev) {
                  if (ev.key === "Control") {
                    this.ctrlPressed = false;
                  }
                };
                EditorView2.prototype.selectNode = function(node, nodeView) {
                  if (!this.ctrlPressed) {
                    this.unselectAllNodes();
                  }
                  this.selectedNodes.push(node);
                  this.selectedNodeViews.push(nodeView);
                };
                EditorView2.prototype.unselectAllNodes = function() {
                  this.selectedNodes.splice(0, this.selectedNodes.length);
                  this.selectedNodeViews.splice(0, this.selectedNodeViews.length);
                };
                EditorView2.prototype.openContextMenu = function(event) {
                  if (this.plugin.disable_context_menu) {
                    return;
                  }
                  this.contextMenu.show = true;
                  this.contextMenu.x = event.offsetX;
                  this.contextMenu.y = event.offsetY;
                };
                EditorView2.prototype.onContextMenuClick = function(action) {
                  if (action.startsWith("addNode:")) {
                    var nodeName = action.substring(action.indexOf(":") + 1);
                    var nt = this.plugin.editor.nodeTypes.get(nodeName);
                    if (nt) {
                      var node = this.plugin.editor.addNode(new nt());
                      if (node) {
                        node.position.x = this.contextMenu.x / this.plugin.scaling - this.plugin.panning.x;
                        node.position.y = this.contextMenu.y / this.plugin.scaling - this.plugin.panning.y;
                      }
                    }
                  } else if (action === "copy" && this.selectedNodes.length > 0) {
                    this.clipboard.copy(this.selectedNodes);
                  } else if (action === "paste" && !this.clipboard.isEmpty) {
                    this.clipboard.paste();
                  }
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Object, required: true }),
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Provide */
                    "d"
                  ])()
                ], EditorView2.prototype, "plugin", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Provide */
                    "d"
                  ])({ to: "editor" })
                ], EditorView2.prototype, "nodeeditor", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Provide */
                    "d"
                  ])()
                ], EditorView2.prototype, "selectedNodeViews", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("plugin.nodeTypeAliases")
                ], EditorView2.prototype, "updateContextMenu", null);
                return EditorView2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = EditorView;
          },
          /* 26 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4);
            var Connection = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(Connection2, _super);
                function Connection2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                Connection2.prototype.mounted = function() {
                  this.plugin.hooks.renderConnection.execute(this);
                };
                Connection2.prototype.updated = function() {
                  this.plugin.hooks.renderConnection.execute(this);
                };
                Object.defineProperty(Connection2.prototype, "d", {
                  get: function() {
                    var _a = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.transform(this.x1, this.y1), 2), tx1 = _a[0], ty1 = _a[1];
                    var _b = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.transform(this.x2, this.y2), 2), tx2 = _b[0], ty2 = _b[1];
                    if (this.plugin.useStraightConnections) {
                      return "M " + tx1 + " " + ty1 + " L " + tx2 + " " + ty2;
                    } else {
                      var dx = 0.3 * Math.abs(tx1 - tx2);
                      return "M " + tx1 + " " + ty1 + " C " + (tx1 + dx) + " " + ty1 + ", " + (tx2 - dx) + " " + ty2 + ", " + tx2 + " " + ty2;
                    }
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(Connection2.prototype, "classes", {
                  get: function() {
                    return {
                      "connection": true,
                      "--temporary": this.isTemporary,
                      "--allowed": this.state === _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__[
                        /* TemporaryConnectionState */
                        "a"
                      ].ALLOWED,
                      "--forbidden": this.state === _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__[
                        /* TemporaryConnectionState */
                        "a"
                      ].FORBIDDEN
                    };
                  },
                  enumerable: false,
                  configurable: true
                });
                Connection2.prototype.transform = function(x, y) {
                  var tx = (x + this.plugin.panning.x) * this.plugin.scaling;
                  var ty = (y + this.plugin.panning.y) * this.plugin.scaling;
                  return [tx, ty];
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Number })
                ], Connection2.prototype, "x1", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Number })
                ], Connection2.prototype, "y1", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Number })
                ], Connection2.prototype, "x2", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Number })
                ], Connection2.prototype, "y2", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Number, default: _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_2__[
                    /* TemporaryConnectionState */
                    "a"
                  ].NONE })
                ], Connection2.prototype, "state", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Boolean, default: false })
                ], Connection2.prototype, "isTemporary", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Object })
                ], Connection2.prototype, "connection", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Inject */
                    "a"
                  ])()
                ], Connection2.prototype, "plugin", void 0);
                return Connection2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = Connection;
          },
          /* 27 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _juggle_resize_observer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(84);
            var _ConnectionView_vue__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9);
            var _domResolver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7);
            var _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4);
            var ResizeObserver = window.ResizeObserver || _juggle_resize_observer__WEBPACK_IMPORTED_MODULE_2__[
              /* ResizeObserver */
              "a"
            ];
            var ConnectionWrapper = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(ConnectionWrapper2, _super);
                function ConnectionWrapper2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.d = { x1: 0, y1: 0, x2: 0, y2: 0 };
                  return _this;
                }
                Object.defineProperty(ConnectionWrapper2.prototype, "state", {
                  get: function() {
                    return this.connection.isInDanger ? _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_5__[
                      /* TemporaryConnectionState */
                      "a"
                    ].FORBIDDEN : _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_5__[
                      /* TemporaryConnectionState */
                      "a"
                    ].NONE;
                  },
                  enumerable: false,
                  configurable: true
                });
                ConnectionWrapper2.prototype.mounted = function() {
                  return Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__awaiter"])(this, void 0, void 0, function() {
                    return Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__generator"])(this, function(_a) {
                      switch (_a.label) {
                        case 0:
                          return [4, this.$nextTick()];
                        case 1:
                          _a.sent();
                          this.updateCoords();
                          return [
                            2
                            /*return*/
                          ];
                      }
                    });
                  });
                };
                ConnectionWrapper2.prototype.beforeDestroy = function() {
                  this.resizeObserver.disconnect();
                };
                ConnectionWrapper2.prototype.updateCoords = function() {
                  var _this = this;
                  var from = Object(_domResolver__WEBPACK_IMPORTED_MODULE_4__[
                    /* default */
                    "a"
                  ])(this.connection.from);
                  var to = Object(_domResolver__WEBPACK_IMPORTED_MODULE_4__[
                    /* default */
                    "a"
                  ])(this.connection.to);
                  if (from.node && to.node) {
                    if (!this.resizeObserver) {
                      this.resizeObserver = new ResizeObserver(function() {
                        _this.updateCoords();
                      });
                      this.resizeObserver.observe(from.node);
                      this.resizeObserver.observe(to.node);
                    }
                  }
                  var _a = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.getPortCoordinates(from), 2), x1 = _a[0], y1 = _a[1];
                  var _b = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.getPortCoordinates(to), 2), x2 = _b[0], y2 = _b[1];
                  this.d = { x1, y1, x2, y2 };
                };
                ConnectionWrapper2.prototype.getPortCoordinates = function(resolved) {
                  if (resolved.node && resolved.interface && resolved.port) {
                    return [
                      resolved.node.offsetLeft + resolved.interface.offsetLeft + resolved.port.offsetLeft + resolved.port.clientWidth / 2,
                      resolved.node.offsetTop + resolved.interface.offsetTop + resolved.port.offsetTop + resolved.port.clientHeight / 2
                    ];
                  } else {
                    return [0, 0];
                  }
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Object })
                ], ConnectionWrapper2.prototype, "connection", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("connection.from.parent.position", { deep: true }),
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("connection.to.parent.position", { deep: true })
                ], ConnectionWrapper2.prototype, "updateCoords", null);
                ConnectionWrapper2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "b"
                  ])({
                    components: {
                      "connection-view": _ConnectionView_vue__WEBPACK_IMPORTED_MODULE_3__[
                        /* default */
                        "a"
                      ]
                    }
                  })
                ], ConnectionWrapper2);
                return ConnectionWrapper2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = ConnectionWrapper;
          },
          /* 28 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _ConnectionView_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9);
            var _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(4);
            var _domResolver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7);
            var _portCoordinates__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(11);
            var TemporaryConnection = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(TemporaryConnection2, _super);
                function TemporaryConnection2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                Object.defineProperty(TemporaryConnection2.prototype, "status", {
                  get: function() {
                    return this.connection ? this.connection.status : _baklavajs_core_types__WEBPACK_IMPORTED_MODULE_3__[
                      /* TemporaryConnectionState */
                      "a"
                    ].NONE;
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(TemporaryConnection2.prototype, "d", {
                  get: function() {
                    if (!this.connection) {
                      return {
                        input: [0, 0],
                        output: [0, 0]
                      };
                    }
                    var start = Object(_portCoordinates__WEBPACK_IMPORTED_MODULE_5__[
                      /* getPortCoordinates */
                      "a"
                    ])(Object(_domResolver__WEBPACK_IMPORTED_MODULE_4__[
                      /* default */
                      "a"
                    ])(this.connection.from));
                    var end = this.connection.to ? Object(_portCoordinates__WEBPACK_IMPORTED_MODULE_5__[
                      /* getPortCoordinates */
                      "a"
                    ])(Object(_domResolver__WEBPACK_IMPORTED_MODULE_4__[
                      /* default */
                      "a"
                    ])(this.connection.to)) : [this.connection.mx || start[0], this.connection.my || start[1]];
                    if (this.connection.from.isInput) {
                      return {
                        input: end,
                        output: start
                      };
                    } else {
                      return {
                        input: start,
                        output: end
                      };
                    }
                  },
                  enumerable: false,
                  configurable: true
                });
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Object })
                ], TemporaryConnection2.prototype, "connection", void 0);
                TemporaryConnection2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "b"
                  ])({
                    components: {
                      "connection-view": _ConnectionView_vue__WEBPACK_IMPORTED_MODULE_2__[
                        /* default */
                        "a"
                      ]
                    }
                  })
                ], TemporaryConnection2);
                return TemporaryConnection2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = TemporaryConnection;
          },
          /* 29 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _utility_cssNames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96);
            var NodeView = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(NodeView2, _super);
                function NodeView2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.draggingStartPosition = null;
                  _this.draggingStartPoint = null;
                  _this.renaming = false;
                  _this.tempName = "";
                  _this.contextMenu = {
                    show: false,
                    x: 0,
                    y: 0,
                    items: [
                      { value: "rename", label: "Rename" },
                      { value: "delete", label: "Delete" }
                    ]
                  };
                  return _this;
                }
                Object.defineProperty(NodeView2.prototype, "classes", {
                  get: function() {
                    var _a;
                    return _a = {
                      "node": true,
                      "--selected": this.selected,
                      "--dragging": !!this.draggingStartPoint,
                      "--two-column": !!this.node.twoColumn
                    }, _a["--type-" + Object(_utility_cssNames__WEBPACK_IMPORTED_MODULE_2__[
                      /* sanitizeName */
                      "a"
                    ])(this.node.type)] = true, _a[this.node.customClasses] = true, _a;
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(NodeView2.prototype, "styles", {
                  get: function() {
                    return {
                      top: this.node.position.y + "px",
                      left: this.node.position.x + "px",
                      width: this.node.width + "px"
                    };
                  },
                  enumerable: false,
                  configurable: true
                });
                NodeView2.prototype.mounted = function() {
                  var _this = this;
                  this.node.events.addInterface.addListener(this, function() {
                    return _this.update();
                  });
                  this.node.events.removeInterface.addListener(this, function() {
                    return _this.update();
                  });
                  this.node.events.addOption.addListener(this, function() {
                    return _this.update();
                  });
                  this.node.events.removeOption.addListener(this, function() {
                    return _this.update();
                  });
                  this.plugin.hooks.renderNode.execute(this);
                };
                NodeView2.prototype.updated = function() {
                  this.plugin.hooks.renderNode.execute(this);
                };
                NodeView2.prototype.beforeDestroy = function() {
                  this.node.events.addInterface.removeListener(this);
                  this.node.events.removeInterface.removeListener(this);
                  this.node.events.addOption.removeListener(this);
                  this.node.events.removeOption.removeListener(this);
                };
                NodeView2.prototype.update = function() {
                  this.$forceUpdate();
                };
                NodeView2.prototype.startDrag = function(ev) {
                  this.select();
                  if (this.selectedNodeViews.length === 0 || this.selectedNodeViews[0] === void 0) {
                    this.selectedNodeViews.splice(0, this.selectedNodeViews.length);
                    this.selectedNodeViews.push(this);
                  }
                  this.selectedNodeViews.forEach(function(elem) {
                    elem.draggingStartPoint = {
                      x: ev.screenX,
                      y: ev.screenY
                    };
                    elem.draggingStartPosition = {
                      x: elem.node.position.x,
                      y: elem.node.position.y
                    };
                    document.addEventListener("mousemove", elem.handleMove);
                    document.addEventListener("mouseup", elem.stopDrag);
                  });
                };
                NodeView2.prototype.select = function() {
                  this.$emit("select", this);
                };
                NodeView2.prototype.stopDrag = function() {
                  this.selectedNodeViews.forEach(function(elem) {
                    elem.draggingStartPoint = null;
                    elem.draggingStartPosition = null;
                    document.removeEventListener("mousemove", elem.handleMove);
                    document.removeEventListener("mouseup", elem.stopDrag);
                  });
                };
                NodeView2.prototype.handleMove = function(ev) {
                  this.selectedNodeViews.forEach(function(elem) {
                    if (elem.draggingStartPoint) {
                      var dx = ev.screenX - elem.draggingStartPoint.x;
                      var dy = ev.screenY - elem.draggingStartPoint.y;
                      elem.node.position.x = elem.draggingStartPosition.x + dx / elem.plugin.scaling;
                      elem.node.position.y = elem.draggingStartPosition.y + dy / elem.plugin.scaling;
                    }
                  });
                };
                NodeView2.prototype.openContextMenu = function(ev) {
                  this.contextMenu.show = true;
                  this.contextMenu.x = ev.offsetX;
                  this.contextMenu.y = ev.offsetY;
                };
                NodeView2.prototype.onContextMenu = function(action) {
                  switch (action) {
                    case "delete":
                      this.plugin.editor.removeNode(this.node);
                      break;
                    case "rename":
                      this.tempName = this.node.name;
                      this.renaming = true;
                  }
                };
                NodeView2.prototype.doneRenaming = function() {
                  this.node.name = this.tempName;
                  this.renaming = false;
                };
                NodeView2.prototype.openSidebar = function(optionName) {
                  this.plugin.sidebar.nodeId = this.node.id;
                  this.plugin.sidebar.optionName = optionName;
                  this.plugin.sidebar.visible = true;
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Object })
                ], NodeView2.prototype, "node", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Boolean, default: false })
                ], NodeView2.prototype, "selected", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Inject */
                    "a"
                  ])()
                ], NodeView2.prototype, "plugin", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Inject */
                    "a"
                  ])()
                ], NodeView2.prototype, "selectedNodeViews", void 0);
                NodeView2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "b"
                  ])({
                    directives: {}
                  })
                ], NodeView2);
                return NodeView2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = NodeView;
          },
          /* 30 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var NodeInterfaceView = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(NodeInterfaceView2, _super);
                function NodeInterfaceView2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.value = null;
                  _this.isConnected = false;
                  return _this;
                }
                Object.defineProperty(NodeInterfaceView2.prototype, "classes", {
                  get: function() {
                    return {
                      "node-interface": true,
                      "--input": this.intf.isInput,
                      "--output": !this.intf.isInput,
                      "--connected": this.isConnected
                    };
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(NodeInterfaceView2.prototype, "displayName", {
                  get: function() {
                    return this.intf.displayName || this.name;
                  },
                  enumerable: false,
                  configurable: true
                });
                NodeInterfaceView2.prototype.beforeMount = function() {
                  var _this = this;
                  this.value = this.intf.value;
                  this.intf.events.setValue.addListener(this, function(v) {
                    _this.value = v;
                  });
                  this.intf.events.setConnectionCount.addListener(this, function(c) {
                    _this.$forceUpdate();
                    _this.isConnected = c > 0;
                  });
                  this.intf.events.updated.addListener(this, function(v) {
                    _this.$forceUpdate();
                  });
                  this.isConnected = this.intf.connectionCount > 0;
                };
                NodeInterfaceView2.prototype.mounted = function() {
                  this.plugin.hooks.renderInterface.execute(this);
                };
                NodeInterfaceView2.prototype.updated = function() {
                  this.plugin.hooks.renderInterface.execute(this);
                };
                NodeInterfaceView2.prototype.beforeDestroy = function() {
                  this.intf.events.setValue.removeListener(this);
                  this.intf.events.setConnectionCount.removeListener(this);
                  this.intf.events.updated.removeListener(this);
                };
                NodeInterfaceView2.prototype.startHover = function() {
                  this.editor.hoveredOver(this.intf);
                };
                NodeInterfaceView2.prototype.endHover = function() {
                  this.editor.hoveredOver(void 0);
                };
                NodeInterfaceView2.prototype.getOptionComponent = function(name) {
                  if (!name || !this.plugin.options) {
                    return;
                  }
                  return this.plugin.options[name];
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Object, default: function() {
                    return {};
                  } })
                ], NodeInterfaceView2.prototype, "intf", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: String, default: "" })
                ], NodeInterfaceView2.prototype, "name", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Inject */
                    "a"
                  ])()
                ], NodeInterfaceView2.prototype, "plugin", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Inject */
                    "a"
                  ])()
                ], NodeInterfaceView2.prototype, "editor", void 0);
                return NodeInterfaceView2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = NodeInterfaceView;
          },
          /* 31 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var NodeOptionView = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(NodeOptionView2, _super);
                function NodeOptionView2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.value = null;
                  return _this;
                }
                Object.defineProperty(NodeOptionView2.prototype, "component", {
                  get: function() {
                    if (!this.plugin.options || !this.componentName) {
                      return;
                    }
                    return this.plugin.options[this.componentName];
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(NodeOptionView2.prototype, "displayName", {
                  get: function() {
                    return this.option.displayName || this.name;
                  },
                  enumerable: false,
                  configurable: true
                });
                NodeOptionView2.prototype.beforeMount = function() {
                  var _this = this;
                  this.value = this.option.value;
                  this.option.events.setValue.addListener(this, function(v) {
                    _this.value = v;
                  });
                };
                NodeOptionView2.prototype.mounted = function() {
                  this.plugin.hooks.renderOption.execute(this);
                };
                NodeOptionView2.prototype.updated = function() {
                  this.plugin.hooks.renderOption.execute(this);
                };
                NodeOptionView2.prototype.beforeDestroy = function() {
                  this.option.events.setValue.removeListener(this);
                };
                NodeOptionView2.prototype.updateValue = function(v) {
                  this.option.value = v;
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])()
                ], NodeOptionView2.prototype, "name", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])()
                ], NodeOptionView2.prototype, "option", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])()
                ], NodeOptionView2.prototype, "componentName", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])()
                ], NodeOptionView2.prototype, "node", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Inject */
                    "a"
                  ])()
                ], NodeOptionView2.prototype, "plugin", void 0);
                return NodeOptionView2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = NodeOptionView;
          },
          /* 32 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var ContextMenu = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(ContextMenu2, _super);
                function ContextMenu2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.activeMenu = -1;
                  _this.activeMenuResetTimeout = null;
                  _this.height = 0;
                  _this.rootIsFlipped = { x: false, y: false };
                  return _this;
                }
                ContextMenu_1 = ContextMenu2;
                Object.defineProperty(ContextMenu2.prototype, "styles", {
                  get: function() {
                    var s = {};
                    if (!this.isNested) {
                      s.top = (this.flippedY ? this.y - this.height : this.y) + "px";
                      s.left = this.x + "px";
                    }
                    return s;
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(ContextMenu2.prototype, "classes", {
                  get: function() {
                    return {
                      "dark-context-menu": true,
                      "--flipped-x": this.flippedX,
                      "--flipped-y": this.flippedY,
                      "--nested": this.isNested
                    };
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(ContextMenu2.prototype, "_items", {
                  get: function() {
                    return this.items.map(function(i) {
                      return Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__assign"])(Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__assign"])({}, i), { hover: false });
                    });
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(ContextMenu2.prototype, "flippedX", {
                  get: function() {
                    return this.flippable && (this.rootIsFlipped.x || this.isFlipped.x);
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(ContextMenu2.prototype, "flippedY", {
                  get: function() {
                    return this.flippable && (this.rootIsFlipped.y || this.isFlipped.y);
                  },
                  enumerable: false,
                  configurable: true
                });
                ContextMenu2.prototype.onClick = function(item) {
                  if (!item.submenu && item.value) {
                    this.$emit("click", item.value);
                    this.$emit("update:modelValue", false);
                  }
                };
                ContextMenu2.prototype.onChildClick = function(value) {
                  this.$emit("click", value);
                  this.activeMenu = -1;
                  if (!this.isNested) {
                    this.$emit("update:modelValue", false);
                  }
                };
                ContextMenu2.prototype.onClickOutside = function(event) {
                  if (this.modelValue) {
                    this.$emit("update:modelValue", false);
                  }
                };
                ContextMenu2.prototype.onMouseEnter = function(event, index) {
                  if (this.items[index].submenu) {
                    this.activeMenu = index;
                    if (this.activeMenuResetTimeout !== null) {
                      clearTimeout(this.activeMenuResetTimeout);
                      this.activeMenuResetTimeout = null;
                    }
                  }
                };
                ContextMenu2.prototype.onMouseLeave = function(event, index) {
                  var _this = this;
                  if (this.items[index].submenu) {
                    this.activeMenuResetTimeout = window.setTimeout(function() {
                      _this.activeMenu = -1;
                      _this.activeMenuResetTimeout = null;
                    }, 200);
                  }
                };
                ContextMenu2.prototype.created = function() {
                  if (this.$options.components) {
                    this.$options.components["context-menu"] = ContextMenu_1;
                  } else {
                    this.$options.components = { "context-menu": ContextMenu_1 };
                  }
                };
                ContextMenu2.prototype.updateFlipped = function() {
                  this.height = this.items.length * 30;
                  var parentWidth = this.$parent.$el.offsetWidth;
                  var parentHeight = this.$parent.$el.offsetHeight;
                  this.rootIsFlipped.x = !this.isNested && this.x > parentWidth * 0.75;
                  this.rootIsFlipped.y = !this.isNested && this.y + this.height > parentHeight - 20;
                };
                ContextMenu2.prototype.updateDisabledValues = function() {
                  if (this.modelValue) {
                    this.items.forEach(function(item) {
                      if (item.disabledFunction) {
                        item.disabled = item.disabledFunction();
                      }
                    });
                  }
                };
                var ContextMenu_1;
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Boolean, default: false })
                ], ContextMenu2.prototype, "modelValue", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Array, default: function() {
                    return [];
                  } })
                ], ContextMenu2.prototype, "items", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Number, default: 0 })
                ], ContextMenu2.prototype, "x", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Number, default: 0 })
                ], ContextMenu2.prototype, "y", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Boolean, default: false })
                ], ContextMenu2.prototype, "isNested", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Object, default: function() {
                    return { x: false, y: false };
                  } })
                ], ContextMenu2.prototype, "isFlipped", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])({ type: Boolean, default: false })
                ], ContextMenu2.prototype, "flippable", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("y"),
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("items")
                ], ContextMenu2.prototype, "updateFlipped", null);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("modelValue", { immediate: true })
                ], ContextMenu2.prototype, "updateDisabledValues", null);
                ContextMenu2 = ContextMenu_1 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "b"
                  ])({
                    directives: {},
                    emits: ["update:modelValue", "click"]
                  })
                ], ContextMenu2);
                return ContextMenu2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = ContextMenu;
          },
          /* 33 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var Sidebar = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(Sidebar2, _super);
                function Sidebar2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.width = 300;
                  return _this;
                }
                Object.defineProperty(Sidebar2.prototype, "nodeName", {
                  get: function() {
                    var id = this.plugin.sidebar.nodeId;
                    var n = this.plugin.editor.nodes.find(function(x) {
                      return x.id === id;
                    });
                    return n ? n.name : "";
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(Sidebar2.prototype, "styles", {
                  get: function() {
                    return {
                      width: this.width + "px"
                    };
                  },
                  enumerable: false,
                  configurable: true
                });
                Sidebar2.prototype.close = function() {
                  this.plugin.sidebar.visible = false;
                };
                Sidebar2.prototype.startResize = function() {
                  var _this = this;
                  window.addEventListener("mousemove", this.onMouseMove);
                  window.addEventListener("mouseup", function() {
                    window.removeEventListener("mousemove", _this.onMouseMove);
                  }, { once: true });
                };
                Sidebar2.prototype.onMouseMove = function(event) {
                  var maxwidth = this.$parent.$el.getBoundingClientRect().width;
                  this.width -= event.movementX;
                  if (this.width < 300) {
                    this.width = 300;
                  } else if (this.width > 0.9 * maxwidth) {
                    this.width = 0.9 * maxwidth;
                  }
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Inject */
                    "a"
                  ])()
                ], Sidebar2.prototype, "plugin", void 0);
                return Sidebar2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = Sidebar;
          },
          /* 34 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _connection_domResolver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7);
            var _connection_portCoordinates__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(11);
            var Minimap = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__extends"])(Minimap2, _super);
                function Minimap2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.intervalHandle = 0;
                  _this.showViewBounds = false;
                  _this.dragging = false;
                  _this.bounds = { x1: 0, y1: 0, x2: 0, y2: 0 };
                  return _this;
                }
                Minimap2.prototype.mounted = function() {
                  var _this = this;
                  var _a;
                  var canvas = this.$refs.cv;
                  this.ctx = (_a = canvas.getContext("2d")) !== null && _a !== void 0 ? _a : void 0;
                  if (this.ctx) {
                    this.ctx.imageSmoothingQuality = "high";
                  }
                  this.intervalHandle = setInterval(function() {
                    return _this.updateCanvas();
                  }, 250);
                };
                Minimap2.prototype.beforeDestroy = function() {
                  clearInterval(this.intervalHandle);
                };
                Minimap2.prototype.updateCanvas = function() {
                  var e_1, _a, e_2, _b, e_3, _c, e_4, _d;
                  var _e, _f;
                  if (!this.ctx) {
                    return;
                  }
                  var nodeCoords = /* @__PURE__ */ new Map();
                  var nodeDomElements = /* @__PURE__ */ new Map();
                  try {
                    for (var _g = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__values"])(this.nodes), _h = _g.next(); !_h.done; _h = _g.next()) {
                      var n = _h.value;
                      var domElement = Object(_connection_domResolver__WEBPACK_IMPORTED_MODULE_2__[
                        /* getDomElementOfNode */
                        "b"
                      ])(n);
                      var width = (_e = domElement === null || domElement === void 0 ? void 0 : domElement.clientWidth) !== null && _e !== void 0 ? _e : 0;
                      var height = (_f = domElement === null || domElement === void 0 ? void 0 : domElement.clientHeight) !== null && _f !== void 0 ? _f : 0;
                      nodeCoords.set(n, { x1: n.position.x, y1: n.position.y, x2: n.position.x + width, y2: n.position.y + height });
                      nodeDomElements.set(n, domElement);
                    }
                  } catch (e_1_1) {
                    e_1 = { error: e_1_1 };
                  } finally {
                    try {
                      if (_h && !_h.done && (_a = _g.return)) _a.call(_g);
                    } finally {
                      if (e_1) throw e_1.error;
                    }
                  }
                  var bounds = {
                    x1: Number.MAX_SAFE_INTEGER,
                    y1: Number.MAX_SAFE_INTEGER,
                    x2: Number.MIN_SAFE_INTEGER,
                    y2: Number.MIN_SAFE_INTEGER
                  };
                  try {
                    for (var _j = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__values"])(nodeCoords.values()), _k = _j.next(); !_k.done; _k = _j.next()) {
                      var nc = _k.value;
                      if (nc.x1 < bounds.x1) {
                        bounds.x1 = nc.x1;
                      }
                      if (nc.y1 < bounds.y1) {
                        bounds.y1 = nc.y1;
                      }
                      if (nc.x2 > bounds.x2) {
                        bounds.x2 = nc.x2;
                      }
                      if (nc.y2 > bounds.y2) {
                        bounds.y2 = nc.y2;
                      }
                    }
                  } catch (e_2_1) {
                    e_2 = { error: e_2_1 };
                  } finally {
                    try {
                      if (_k && !_k.done && (_b = _j.return)) _b.call(_j);
                    } finally {
                      if (e_2) throw e_2.error;
                    }
                  }
                  var padding = 50;
                  bounds.x1 -= padding;
                  bounds.y1 -= padding;
                  bounds.x2 += padding;
                  bounds.y2 += padding;
                  this.bounds = bounds;
                  this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
                  this.ctx.strokeStyle = "white";
                  try {
                    for (var _l = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__values"])(this.connections), _m = _l.next(); !_m.done; _m = _l.next()) {
                      var c = _m.value;
                      var toDom = Object(_connection_domResolver__WEBPACK_IMPORTED_MODULE_2__[
                        /* default */
                        "a"
                      ])(c.to);
                      var _o = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(Object(_connection_portCoordinates__WEBPACK_IMPORTED_MODULE_3__[
                        /* getPortCoordinates */
                        "a"
                      ])(Object(_connection_domResolver__WEBPACK_IMPORTED_MODULE_2__[
                        /* default */
                        "a"
                      ])(c.from)), 2), origX1 = _o[0], origY1 = _o[1];
                      var _p = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(Object(_connection_portCoordinates__WEBPACK_IMPORTED_MODULE_3__[
                        /* getPortCoordinates */
                        "a"
                      ])(Object(_connection_domResolver__WEBPACK_IMPORTED_MODULE_2__[
                        /* default */
                        "a"
                      ])(c.to)), 2), origX2 = _p[0], origY2 = _p[1];
                      var _q = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.transformCoordinates(origX1, origY1), 2), x1 = _q[0], y1 = _q[1];
                      var _r = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.transformCoordinates(origX2, origY2), 2), x2 = _r[0], y2 = _r[1];
                      this.ctx.beginPath();
                      this.ctx.moveTo(x1, y1);
                      if (this.plugin.useStraightConnections) {
                        this.ctx.lineTo(x2, y2);
                      } else {
                        var dx = 0.3 * Math.abs(x1 - x2);
                        this.ctx.bezierCurveTo(x1 + dx, y1, x2 - dx, y2, x2, y2);
                      }
                      this.ctx.stroke();
                    }
                  } catch (e_3_1) {
                    e_3 = { error: e_3_1 };
                  } finally {
                    try {
                      if (_m && !_m.done && (_c = _l.return)) _c.call(_l);
                    } finally {
                      if (e_3) throw e_3.error;
                    }
                  }
                  this.ctx.strokeStyle = "lightgray";
                  try {
                    for (var _s = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__values"])(nodeCoords.entries()), _t = _s.next(); !_t.done; _t = _s.next()) {
                      var _u = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(_t.value, 2), n = _u[0], nc = _u[1];
                      var _v = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.transformCoordinates(nc.x1, nc.y1), 2), x1 = _v[0], y1 = _v[1];
                      var _w = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.transformCoordinates(nc.x2, nc.y2), 2), x2 = _w[0], y2 = _w[1];
                      this.ctx.fillStyle = this.getNodeColor(nodeDomElements.get(n));
                      this.ctx.beginPath();
                      this.ctx.rect(x1, y1, x2 - x1, y2 - y1);
                      this.ctx.fill();
                      this.ctx.stroke();
                    }
                  } catch (e_4_1) {
                    e_4 = { error: e_4_1 };
                  } finally {
                    try {
                      if (_t && !_t.done && (_d = _s.return)) _d.call(_s);
                    } finally {
                      if (e_4) throw e_4.error;
                    }
                  }
                  if (this.showViewBounds) {
                    var viewBounds = this.getViewBounds();
                    var _x = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.transformCoordinates(viewBounds.x1, viewBounds.y1), 2), x1 = _x[0], y1 = _x[1];
                    var _y = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.transformCoordinates(viewBounds.x2, viewBounds.y2), 2), x2 = _y[0], y2 = _y[1];
                    this.ctx.fillStyle = "rgba(255, 255, 255, 0.2)";
                    this.ctx.fillRect(x1, y1, x2 - x1, y2 - y1);
                  }
                };
                Minimap2.prototype.transformCoordinates = function(origX, origY) {
                  return [
                    (origX - this.bounds.x1) / (this.bounds.x2 - this.bounds.x1) * this.ctx.canvas.clientWidth,
                    (origY - this.bounds.y1) / (this.bounds.y2 - this.bounds.y1) * this.ctx.canvas.clientHeight
                  ];
                };
                Minimap2.prototype.reverseTransform = function(thisX, thisY) {
                  return [
                    thisX * (this.bounds.x2 - this.bounds.x1) / this.ctx.canvas.clientWidth + this.bounds.x1,
                    thisY * (this.bounds.y2 - this.bounds.y1) / this.ctx.canvas.clientHeight + this.bounds.y1
                  ];
                };
                Minimap2.prototype.getNodeColor = function(domElement) {
                  if (domElement) {
                    var content = domElement.querySelector(".__content");
                    if (content) {
                      var contentColor = this.getComputedColor(content);
                      if (contentColor) {
                        return contentColor;
                      }
                    }
                    var nodeColor = this.getComputedColor(domElement);
                    if (nodeColor) {
                      return nodeColor;
                    }
                  }
                  return "gray";
                };
                Minimap2.prototype.getComputedColor = function(domElement) {
                  var c = getComputedStyle(domElement).backgroundColor;
                  if (c && c !== "rgba(0, 0, 0, 0)") {
                    return c;
                  }
                };
                Minimap2.prototype.getViewBounds = function() {
                  var parentWidth = this.$parent.$el.offsetWidth;
                  var parentHeight = this.$parent.$el.offsetHeight;
                  var x2 = parentWidth / this.plugin.scaling - this.plugin.panning.x;
                  var y2 = parentHeight / this.plugin.scaling - this.plugin.panning.y;
                  return { x1: -this.plugin.panning.x, y1: -this.plugin.panning.y, x2, y2 };
                };
                Minimap2.prototype.mousedown = function(ev) {
                  if (ev.button === 0) {
                    this.dragging = true;
                    this.mousemove(ev);
                  }
                };
                Minimap2.prototype.mousemove = function(ev) {
                  if (this.dragging) {
                    var _a = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__read"])(this.reverseTransform(ev.offsetX, ev.offsetY), 2), cx = _a[0], cy = _a[1];
                    var viewBounds = this.getViewBounds();
                    var dx = (viewBounds.x1 - viewBounds.x2) / 2;
                    var dy = (viewBounds.y1 - viewBounds.y2) / 2;
                    this.plugin.panning.x = -(cx + dx);
                    this.plugin.panning.y = -(cy + dy);
                  }
                };
                Minimap2.prototype.mouseup = function(ev) {
                  this.dragging = false;
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])()
                ], Minimap2.prototype, "nodes", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "c"
                  ])()
                ], Minimap2.prototype, "connections", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Inject */
                    "a"
                  ])()
                ], Minimap2.prototype, "plugin", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__decorate"])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("showViewBounds"),
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("plugin.panning.x"),
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("plugin.panning.y"),
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "f"
                  ])("plugin.scaling")
                ], Minimap2.prototype, "updateCanvas", null);
                return Minimap2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "e"
              ])
            );
            __webpack_exports__["a"] = Minimap;
          },
          /* 35 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _Editor_vue_vue_type_template_id_a6582abc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(79);
            var _Editor_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(12);
            _Editor_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _Editor_vue_vue_type_template_id_a6582abc__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _Editor_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 36 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return ResizeObserverEntry;
            });
            var _algorithms_calculateBoxSize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10);
            var ResizeObserverEntry = /* @__PURE__ */ function() {
              function ResizeObserverEntry2(target) {
                var boxes = Object(_algorithms_calculateBoxSize__WEBPACK_IMPORTED_MODULE_0__[
                  /* calculateBoxSizes */
                  "b"
                ])(target);
                this.target = target;
                this.contentRect = boxes.contentRect;
                this.borderBoxSize = [boxes.borderBoxSize];
                this.contentBoxSize = [boxes.contentBoxSize];
                this.devicePixelContentBoxSize = [boxes.devicePixelContentBoxSize];
              }
              return ResizeObserverEntry2;
            }();
          },
          /* 37 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _ConnectionWrapper_vue_vue_type_template_id_56e03f2e__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(83);
            var _ConnectionWrapper_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(14);
            _ConnectionWrapper_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _ConnectionWrapper_vue_vue_type_template_id_56e03f2e__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _ConnectionWrapper_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 38 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return calculateDepthForNode;
            });
            var _utils_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(6);
            var calculateDepthForNode = function(node) {
              if (Object(_utils_element__WEBPACK_IMPORTED_MODULE_0__[
                /* isHidden */
                "b"
              ])(node)) {
                return Infinity;
              }
              var depth = 0;
              var parent = node.parentNode;
              while (parent) {
                depth += 1;
                parent = parent.parentNode;
              }
              return depth;
            };
          },
          /* 39 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _TemporaryConnection_vue_vue_type_template_id_e0dc6546__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(94);
            var _TemporaryConnection_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15);
            _TemporaryConnection_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _TemporaryConnection_vue_vue_type_template_id_e0dc6546__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _TemporaryConnection_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 40 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _Node_vue_vue_type_template_id_61d0bf3a__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(95);
            var _Node_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(16);
            _Node_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _Node_vue_vue_type_template_id_61d0bf3a__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _Node_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 41 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _NodeInterface_vue_vue_type_template_id_522de0f9__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97);
            var _NodeInterface_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(17);
            _NodeInterface_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _NodeInterface_vue_vue_type_template_id_522de0f9__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _NodeInterface_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 42 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _NodeOption_vue_vue_type_template_id_0f2a2624__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(98);
            var _NodeOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(18);
            _NodeOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _NodeOption_vue_vue_type_template_id_0f2a2624__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _NodeOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 43 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _ContextMenu_vue_vue_type_template_id_41109a43__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(99);
            var _ContextMenu_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19);
            _ContextMenu_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _ContextMenu_vue_vue_type_template_id_41109a43__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _ContextMenu_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 44 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _Sidebar_vue_vue_type_template_id_2dc89b20__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(100);
            var _Sidebar_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(20);
            _Sidebar_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _Sidebar_vue_vue_type_template_id_2dc89b20__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _Sidebar_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 45 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _Minimap_vue_vue_type_template_id_64fd4b08__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(101);
            var _Minimap_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(21);
            _Minimap_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _Minimap_vue_vue_type_template_id_64fd4b08__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _Minimap_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 46 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return scheduler;
            });
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return updateCount;
            });
            var _process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85);
            var _global__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8);
            var _queueResizeObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(90);
            var watching = 0;
            var isWatching = function() {
              return !!watching;
            };
            var CATCH_PERIOD = 250;
            var observerConfig = { attributes: true, characterData: true, childList: true, subtree: true };
            var events = [
              "resize",
              "load",
              "transitionend",
              "animationend",
              "animationstart",
              "animationiteration",
              "keyup",
              "keydown",
              "mouseup",
              "mousedown",
              "mouseover",
              "mouseout",
              "blur",
              "focus"
            ];
            var time = function(timeout) {
              if (timeout === void 0) {
                timeout = 0;
              }
              return Date.now() + timeout;
            };
            var scheduled = false;
            var Scheduler = function() {
              function Scheduler2() {
                var _this = this;
                this.stopped = true;
                this.listener = function() {
                  return _this.schedule();
                };
              }
              Scheduler2.prototype.run = function(timeout) {
                var _this = this;
                if (timeout === void 0) {
                  timeout = CATCH_PERIOD;
                }
                if (scheduled) {
                  return;
                }
                scheduled = true;
                var until = time(timeout);
                Object(_queueResizeObserver__WEBPACK_IMPORTED_MODULE_2__[
                  /* queueResizeObserver */
                  "a"
                ])(function() {
                  var elementsHaveResized = false;
                  try {
                    elementsHaveResized = Object(_process__WEBPACK_IMPORTED_MODULE_0__[
                      /* process */
                      "a"
                    ])();
                  } finally {
                    scheduled = false;
                    timeout = until - time();
                    if (!isWatching()) {
                      return;
                    }
                    if (elementsHaveResized) {
                      _this.run(1e3);
                    } else if (timeout > 0) {
                      _this.run(timeout);
                    } else {
                      _this.start();
                    }
                  }
                });
              };
              Scheduler2.prototype.schedule = function() {
                this.stop();
                this.run();
              };
              Scheduler2.prototype.observe = function() {
                var _this = this;
                var cb = function() {
                  return _this.observer && _this.observer.observe(document.body, observerConfig);
                };
                document.body ? cb() : _global__WEBPACK_IMPORTED_MODULE_1__[
                  /* global */
                  "a"
                ].addEventListener("DOMContentLoaded", cb);
              };
              Scheduler2.prototype.start = function() {
                var _this = this;
                if (this.stopped) {
                  this.stopped = false;
                  this.observer = new MutationObserver(this.listener);
                  this.observe();
                  events.forEach(function(name) {
                    return _global__WEBPACK_IMPORTED_MODULE_1__[
                      /* global */
                      "a"
                    ].addEventListener(name, _this.listener, true);
                  });
                }
              };
              Scheduler2.prototype.stop = function() {
                var _this = this;
                if (!this.stopped) {
                  this.observer && this.observer.disconnect();
                  events.forEach(function(name) {
                    return _global__WEBPACK_IMPORTED_MODULE_1__[
                      /* global */
                      "a"
                    ].removeEventListener(name, _this.listener, true);
                  });
                  this.stopped = true;
                }
              };
              return Scheduler2;
            }();
            var scheduler = new Scheduler();
            var updateCount = function(n) {
              !watching && n > 0 && scheduler.start();
              watching += n;
              !watching && scheduler.stop();
            };
          },
          /* 47 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var NodeStep = (
              /** @class */
              function() {
                function NodeStep2(type, data) {
                  this.type = type;
                  if (type === "addNode") {
                    this.nodeId = data;
                  } else {
                    this.nodeState = data;
                  }
                }
                NodeStep2.prototype.undo = function(editor) {
                  if (this.type === "addNode") {
                    this.removeNode(editor);
                  } else {
                    this.addNode(editor);
                  }
                };
                NodeStep2.prototype.redo = function(editor) {
                  if (this.type === "addNode" && this.nodeState) {
                    this.addNode(editor);
                  } else if (this.type === "removeNode" && this.nodeId) {
                    this.removeNode(editor);
                  }
                };
                NodeStep2.prototype.addNode = function(editor) {
                  var nodeType = editor.nodeTypes.get(this.nodeState.type);
                  if (!nodeType) {
                    return;
                  }
                  var n = new nodeType();
                  editor.addNode(n);
                  n.load(this.nodeState);
                  this.nodeId = n.id;
                };
                NodeStep2.prototype.removeNode = function(editor) {
                  var _this = this;
                  var node = editor.nodes.find(function(n) {
                    return n.id === _this.nodeId;
                  });
                  if (!node) {
                    return;
                  }
                  this.nodeState = node.save();
                  editor.removeNode(node);
                };
                return NodeStep2;
              }()
            );
            __webpack_exports__["a"] = NodeStep;
          },
          /* 48 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var ConnectionStep = (
              /** @class */
              function() {
                function ConnectionStep2(type, data) {
                  this.type = type;
                  if (type === "addConnection") {
                    this.connectionId = data;
                  } else {
                    var d = data;
                    this.connectionState = {
                      id: d.id,
                      from: d.from.id,
                      to: d.to.id
                    };
                  }
                }
                ConnectionStep2.prototype.undo = function(editor) {
                  if (this.type === "addConnection") {
                    this.removeConnection(editor);
                  } else {
                    this.addConnection(editor);
                  }
                };
                ConnectionStep2.prototype.redo = function(editor) {
                  if (this.type === "addConnection" && this.connectionState) {
                    this.addConnection(editor);
                  } else if (this.type === "removeConnection" && this.connectionId) {
                    this.removeConnection(editor);
                  }
                };
                ConnectionStep2.prototype.addConnection = function(editor) {
                  var fromIntf = editor.findNodeInterface(this.connectionState.from);
                  var toIntf = editor.findNodeInterface(this.connectionState.to);
                  if (!fromIntf || !toIntf) {
                    return;
                  }
                  editor.addConnection(fromIntf, toIntf);
                };
                ConnectionStep2.prototype.removeConnection = function(editor) {
                  var _this = this;
                  var connection = editor.connections.find(function(c) {
                    return c.id === _this.connectionId;
                  });
                  if (!connection) {
                    return;
                  }
                  this.connectionState = {
                    id: connection.id,
                    from: connection.from.id,
                    to: connection.to.id
                  };
                  editor.removeConnection(connection);
                };
                return ConnectionStep2;
              }()
            );
            __webpack_exports__["a"] = ConnectionStep;
          },
          /* 49 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return DOMRectReadOnly;
            });
            var DOMRectReadOnly = function() {
              function DOMRectReadOnly2(x, y, width, height) {
                this.x = x;
                this.y = y;
                this.width = width;
                this.height = height;
                this.top = this.y;
                this.left = this.x;
                this.bottom = this.top + this.height;
                this.right = this.left + this.width;
                return Object.freeze(this);
              }
              DOMRectReadOnly2.prototype.toJSON = function() {
                var _a = this, x = _a.x, y = _a.y, top = _a.top, right = _a.right, bottom = _a.bottom, left = _a.left, width = _a.width, height = _a.height;
                return { x, y, top, right, bottom, left, width, height };
              };
              DOMRectReadOnly2.fromRect = function(rectangle) {
                return new DOMRectReadOnly2(rectangle.x, rectangle.y, rectangle.width, rectangle.height);
              };
              return DOMRectReadOnly2;
            }();
          },
          /* 50 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return gatherActiveObservationsAtDepth;
            });
            var _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5);
            var _calculateDepthForNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(38);
            var gatherActiveObservationsAtDepth = function(depth) {
              _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_0__[
                /* resizeObservers */
                "a"
              ].forEach(function processObserver(ro) {
                ro.activeTargets.splice(0, ro.activeTargets.length);
                ro.skippedTargets.splice(0, ro.skippedTargets.length);
                ro.observationTargets.forEach(function processTarget(ot) {
                  if (ot.isActive()) {
                    if (Object(_calculateDepthForNode__WEBPACK_IMPORTED_MODULE_1__[
                      /* calculateDepthForNode */
                      "a"
                    ])(ot.target) > depth) {
                      ro.activeTargets.push(ot);
                    } else {
                      ro.skippedTargets.push(ot);
                    }
                  }
                });
              });
            };
          },
          /* 51 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = { class: "connections-container" };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "div",
                {
                  tabindex: "-1",
                  class: [
                    "node-editor",
                    { "ignore-mouse": !!_ctx.temporaryConnection, "--temporary-connection": !!_ctx.temporaryConnection }
                  ],
                  onMousemove: _cache[2] || (_cache[2] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withModifiers"])((...args) => _ctx.mouseMoveHandler && _ctx.mouseMoveHandler(...args), ["self"])),
                  onMousedown: _cache[3] || (_cache[3] = (...args) => _ctx.mouseDown && _ctx.mouseDown(...args)),
                  onMouseup: _cache[4] || (_cache[4] = (...args) => _ctx.mouseUp && _ctx.mouseUp(...args)),
                  onWheel: _cache[5] || (_cache[5] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withModifiers"])((...args) => _ctx.mouseWheel && _ctx.mouseWheel(...args), ["self"])),
                  onKeydown: _cache[6] || (_cache[6] = (...args) => _ctx.keyDown && _ctx.keyDown(...args)),
                  onKeyup: _cache[7] || (_cache[7] = (...args) => _ctx.keyUp && _ctx.keyUp(...args)),
                  onContextmenu: _cache[8] || (_cache[8] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withModifiers"])((...args) => _ctx.openContextMenu && _ctx.openContextMenu(...args), ["self", "prevent"]))
                },
                [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    {
                      class: "background",
                      style: _ctx.backgroundStyle
                    },
                    null,
                    4
                    /* STYLE */
                  ),
                  (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("svg", _hoisted_1, [
                    (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(true), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                      vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                      null,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["renderList"])(_ctx.connections, (connection) => {
                        return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("g", {
                          key: connection.id + _ctx.counter.toString()
                        }, [
                          Object(vue__WEBPACK_IMPORTED_MODULE_0__["renderSlot"])(_ctx.$slots, "connections", { connection }, () => [
                            (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.connection), { connection }, null, 8, ["connection"]))
                          ])
                        ]);
                      }),
                      128
                      /* KEYED_FRAGMENT */
                    )),
                    _ctx.temporaryConnection ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.tempConnection), {
                      key: 0,
                      connection: _ctx.temporaryConnection
                    }, null, 8, ["connection"])) : Object(vue__WEBPACK_IMPORTED_MODULE_0__["createCommentVNode"])("v-if", true)
                  ])),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    {
                      class: "node-container",
                      style: _ctx.styles
                    },
                    [
                      (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(true), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                        vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                        null,
                        Object(vue__WEBPACK_IMPORTED_MODULE_0__["renderList"])(_ctx.nodes, (node) => {
                          return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.node), {
                            key: node.id + _ctx.counter.toString(),
                            node,
                            selected: _ctx.selectedNodes.includes(node),
                            onSelect: ($event) => _ctx.selectNode(node, $event)
                          }, null, 8, ["node", "selected", "onSelect"]);
                        }),
                        128
                        /* KEYED_FRAGMENT */
                      ))
                    ],
                    4
                    /* STYLE */
                  ),
                  (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.contextMenu), {
                    modelValue: _ctx.contextMenu.show,
                    "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => _ctx.contextMenu.show = $event),
                    x: _ctx.contextMenu.x,
                    y: _ctx.contextMenu.y,
                    items: _ctx.contextMenu.items,
                    flippable: "",
                    onClick: _ctx.onContextMenuClick
                  }, null, 8, ["modelValue", "x", "y", "items", "onClick"])),
                  (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.sidebar))),
                  _ctx.plugin.enableMinimap ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.minimap), {
                    key: 0,
                    nodes: _ctx.nodes,
                    connections: _ctx.connections
                  }, null, 8, ["nodes", "connections"])) : Object(vue__WEBPACK_IMPORTED_MODULE_0__["createCommentVNode"])("v-if", true)
                ],
                34
                /* CLASS, HYDRATE_EVENTS */
              );
            }
          },
          /* 52 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            const hyphenateRE = /\B([A-Z])/g;
            const hyphenate = (str) => str.replace(hyphenateRE, "-$1").toLowerCase();
            function Emit(event) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, propertyKey) => {
                const emitName = event || hyphenate(propertyKey);
                componentOptions.emits || (componentOptions.emits = []);
                componentOptions.emits.push(emitName);
                const original = componentOptions.methods[propertyKey];
                componentOptions.methods[propertyKey] = function emitter(...args) {
                  const emit = (returnValue2) => {
                    if (returnValue2 === void 0) {
                      if (args.length === 0) {
                        this.$emit(emitName);
                      } else if (args.length === 1) {
                        this.$emit(emitName, args[0]);
                      } else {
                        this.$emit(emitName, ...args);
                      }
                    } else {
                      args.unshift(returnValue2);
                      this.$emit(emitName, ...args);
                    }
                  };
                  const returnValue = original.apply(this, args);
                  if (isPromise(returnValue)) {
                    returnValue.then(emit);
                  } else {
                    emit(returnValue);
                  }
                  return returnValue;
                };
              });
            }
            function isPromise(obj) {
              return obj instanceof Promise || obj && typeof obj.then === "function";
            }
          },
          /* 53 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return Inject;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            var vue_class_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3);
            function Inject(options = /* @__PURE__ */ Object.create(null)) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_1__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                const originalSetup = componentOptions.setup;
                componentOptions.setup = (props, ctx) => {
                  const result = originalSetup === null || originalSetup === void 0 ? void 0 : originalSetup(props, ctx);
                  const injectedValue = Object(vue__WEBPACK_IMPORTED_MODULE_0__["inject"])(options.from || key, options.default);
                  return Object.assign(Object.assign({}, result), { [key]: injectedValue });
                };
              });
            }
          },
          /* 54 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            function Model(propName, propOptions) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                const eventName = `update:${propName}`;
                componentOptions.props || (componentOptions.props = /* @__PURE__ */ Object.create(null));
                componentOptions.props[propName] = propOptions;
                componentOptions.emits || (componentOptions.emits = []);
                componentOptions.emits.push(eventName);
                componentOptions.computed || (componentOptions.computed = /* @__PURE__ */ Object.create(null));
                componentOptions.computed[key] = {
                  get() {
                    return this[propName];
                  },
                  set(newValue) {
                    this.$emit(eventName, newValue);
                  }
                };
              });
            }
          },
          /* 55 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return Prop;
            });
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            function Prop(propOptions) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                componentOptions.props || (componentOptions.props = /* @__PURE__ */ Object.create(null));
                componentOptions.props[key] = propOptions;
              });
            }
          },
          /* 56 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return Provide;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            var vue_class_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3);
            function Provide(options) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_1__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                const originalProvide = componentOptions.provide;
                componentOptions.provide = function() {
                  const providedValue = typeof originalProvide === "function" ? originalProvide.call(this) : originalProvide;
                  return Object.assign(Object.assign({}, providedValue), { [(options === null || options === void 0 ? void 0 : options.to) || key]: (options === null || options === void 0 ? void 0 : options.reactive) ? Object(vue__WEBPACK_IMPORTED_MODULE_0__["computed"])(() => this[key]) : this[key] });
                };
              });
            }
          },
          /* 57 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            function Ref(refKey) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                componentOptions.computed || (componentOptions.computed = /* @__PURE__ */ Object.create(null));
                componentOptions.computed[key] = {
                  cache: false,
                  get() {
                    return this.$refs[refKey || key];
                  }
                };
              });
            }
          },
          /* 58 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return Watch;
            });
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            function Watch(path, watchOptions) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, handler) => {
                componentOptions.watch || (componentOptions.watch = /* @__PURE__ */ Object.create(null));
                const watch = componentOptions.watch;
                if (typeof watch[path] === "object" && !Array.isArray(watch[path])) {
                  watch[path] = [watch[path]];
                } else if (typeof watch[path] === "undefined") {
                  watch[path] = [];
                }
                watch[path].push(Object.assign({ handler }, watchOptions));
              });
            }
          },
          /* 59 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return TemporaryConnectionState;
            });
            var TemporaryConnectionState;
            (function(TemporaryConnectionState2) {
              TemporaryConnectionState2[TemporaryConnectionState2["NONE"] = 0] = "NONE";
              TemporaryConnectionState2[TemporaryConnectionState2["ALLOWED"] = 1] = "ALLOWED";
              TemporaryConnectionState2[TemporaryConnectionState2["FORBIDDEN"] = 2] = "FORBIDDEN";
            })(TemporaryConnectionState || (TemporaryConnectionState = {}));
          },
          /* 60 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
          },
          /* 61 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
          },
          /* 62 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
          },
          /* 63 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
          },
          /* 64 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
          },
          /* 65 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
          },
          /* 66 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
          },
          /* 67 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("path", {
                d: _ctx.d,
                class: _ctx.classes
              }, null, 10, ["d"]);
            }
          },
          /* 68 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              const _component_connection_view = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveComponent"])("connection-view");
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(_component_connection_view, {
                x1: _ctx.d.x1,
                y1: _ctx.d.y1,
                x2: _ctx.d.x2,
                y2: _ctx.d.y2,
                state: _ctx.state,
                connection: _ctx.connection
              }, null, 8, ["x1", "y1", "x2", "y2", "state", "connection"]);
            }
          },
          /* 69 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return ResizeObserver;
            });
            var _ResizeObserverController__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(23);
            var _utils_element__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6);
            var ResizeObserver = function() {
              function ResizeObserver2(callback) {
                if (arguments.length === 0) {
                  throw new TypeError("Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.");
                }
                if (typeof callback !== "function") {
                  throw new TypeError("Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function.");
                }
                _ResizeObserverController__WEBPACK_IMPORTED_MODULE_0__[
                  /* ResizeObserverController */
                  "a"
                ].connect(this, callback);
              }
              ResizeObserver2.prototype.observe = function(target, options) {
                if (arguments.length === 0) {
                  throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': 1 argument required, but only 0 present.");
                }
                if (!Object(_utils_element__WEBPACK_IMPORTED_MODULE_1__[
                  /* isElement */
                  "a"
                ])(target)) {
                  throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': parameter 1 is not of type 'Element");
                }
                _ResizeObserverController__WEBPACK_IMPORTED_MODULE_0__[
                  /* ResizeObserverController */
                  "a"
                ].observe(this, target, options);
              };
              ResizeObserver2.prototype.unobserve = function(target) {
                if (arguments.length === 0) {
                  throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': 1 argument required, but only 0 present.");
                }
                if (!Object(_utils_element__WEBPACK_IMPORTED_MODULE_1__[
                  /* isElement */
                  "a"
                ])(target)) {
                  throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': parameter 1 is not of type 'Element");
                }
                _ResizeObserverController__WEBPACK_IMPORTED_MODULE_0__[
                  /* ResizeObserverController */
                  "a"
                ].unobserve(this, target);
              };
              ResizeObserver2.prototype.disconnect = function() {
                _ResizeObserverController__WEBPACK_IMPORTED_MODULE_0__[
                  /* ResizeObserverController */
                  "a"
                ].disconnect(this);
              };
              ResizeObserver2.toString = function() {
                return "function ResizeObserver () { [polyfill code] }";
              };
              return ResizeObserver2;
            }();
          },
          /* 70 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              const _component_connection_view = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveComponent"])("connection-view");
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(_component_connection_view, {
                x1: _ctx.d.input[0],
                y1: _ctx.d.input[1],
                x2: _ctx.d.output[0],
                y2: _ctx.d.output[1],
                state: _ctx.status,
                connection: _ctx.connection,
                "is-temporary": ""
              }, null, 8, ["x1", "y1", "x2", "y2", "state", "connection"]);
            }
          },
          /* 71 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = { key: 0 };
            const _hoisted_2 = { class: "__content" };
            const _hoisted_3 = { class: "__outputs" };
            const _hoisted_4 = { class: "__options" };
            const _hoisted_5 = { class: "__inputs" };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              const _directive_click_outside_element = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDirective"])("click-outside-element");
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", {
                id: _ctx.node.id,
                class: _ctx.classes,
                style: _ctx.styles
              }, [
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                  "div",
                  {
                    class: "__title",
                    onMousedown: _cache[4] || (_cache[4] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withModifiers"])((...args) => _ctx.startDrag && _ctx.startDrag(...args), ["self", "stop"])),
                    onContextmenu: _cache[5] || (_cache[5] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withModifiers"])((...args) => _ctx.openContextMenu && _ctx.openContextMenu(...args), ["self", "prevent"]))
                  },
                  [
                    !_ctx.renaming ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                      "span",
                      _hoisted_1,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.node.name),
                      1
                      /* TEXT */
                    )) : Object(vue__WEBPACK_IMPORTED_MODULE_0__["withDirectives"])((Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                      "input",
                      {
                        key: 1,
                        type: "text",
                        class: "dark-input",
                        "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => _ctx.tempName = $event),
                        placeholder: "Node Name",
                        onKeydown: _cache[2] || (_cache[2] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withKeys"])((...args) => _ctx.doneRenaming && _ctx.doneRenaming(...args), ["enter"]))
                      },
                      null,
                      544
                      /* HYDRATE_EVENTS, NEED_PATCH */
                    )), [
                      [vue__WEBPACK_IMPORTED_MODULE_0__["vModelText"], _ctx.tempName],
                      [_directive_click_outside_element, _ctx.doneRenaming]
                    ]),
                    (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.contextMenu), {
                      modelValue: _ctx.contextMenu.show,
                      "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => _ctx.contextMenu.show = $event),
                      x: _ctx.contextMenu.x,
                      y: _ctx.contextMenu.y,
                      items: _ctx.contextMenu.items,
                      onClick: _ctx.onContextMenu
                    }, null, 8, ["modelValue", "x", "y", "items", "onClick"]))
                  ],
                  32
                  /* HYDRATE_EVENTS */
                ),
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", _hoisted_2, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createCommentVNode"])(" Outputs "),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", _hoisted_3, [
                    (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(true), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                      vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                      null,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["renderList"])(_ctx.node.outputInterfaces, (output, name) => {
                        return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.nodeInterface), {
                          key: output.id,
                          name,
                          intf: output
                        }, null, 8, ["name", "intf"]);
                      }),
                      128
                      /* KEYED_FRAGMENT */
                    ))
                  ]),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createCommentVNode"])(" Options "),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", _hoisted_4, [
                    (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(true), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                      vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                      null,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["renderList"])(_ctx.node.options, ([name, option]) => {
                        return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                          vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                          { key: name },
                          [
                            (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.nodeOption), {
                              name,
                              option,
                              componentName: option.optionComponent,
                              node: _ctx.node,
                              onOpenSidebar: ($event) => _ctx.openSidebar(name)
                            }, null, 8, ["name", "option", "componentName", "node", "onOpenSidebar"])),
                            _ctx.plugin.sidebar.nodeId === _ctx.node.id && _ctx.plugin.sidebar.optionName === name && option.sidebarComponent ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(vue__WEBPACK_IMPORTED_MODULE_0__["Teleport"], {
                              key: "sb_" + name,
                              to: "#sidebar"
                            }, [
                              (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.nodeOption), {
                                key: _ctx.node.id + name,
                                name,
                                option,
                                componentName: option.sidebarComponent,
                                node: _ctx.node
                              }, null, 8, ["name", "option", "componentName", "node"]))
                            ])) : Object(vue__WEBPACK_IMPORTED_MODULE_0__["createCommentVNode"])("v-if", true)
                          ],
                          64
                          /* STABLE_FRAGMENT */
                        );
                      }),
                      128
                      /* KEYED_FRAGMENT */
                    ))
                  ]),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createCommentVNode"])(" Inputs "),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", _hoisted_5, [
                    (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(true), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                      vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                      null,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["renderList"])(_ctx.node.inputInterfaces, (input, name) => {
                        return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.plugin.components.nodeInterface), {
                          key: input.id,
                          name,
                          intf: input
                        }, null, 8, ["name", "intf"]);
                      }),
                      128
                      /* KEYED_FRAGMENT */
                    ))
                  ])
                ])
              ], 14, ["id"]);
            }
          },
          /* 72 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = {
              key: 0,
              class: "align-middle"
            };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", {
                id: _ctx.intf.id,
                class: _ctx.classes
              }, [
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                  "div",
                  {
                    class: "__port",
                    onMouseover: _cache[1] || (_cache[1] = (...args) => _ctx.startHover && _ctx.startHover(...args)),
                    onMouseout: _cache[2] || (_cache[2] = (...args) => _ctx.endHover && _ctx.endHover(...args))
                  },
                  null,
                  32
                  /* HYDRATE_EVENTS */
                ),
                _ctx.intf.connectionCount > 0 || !_ctx.intf.option || !_ctx.getOptionComponent(_ctx.intf.option) ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                  "span",
                  _hoisted_1,
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.displayName),
                  1
                  /* TEXT */
                )) : (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.getOptionComponent(_ctx.intf.option)), {
                  key: 1,
                  option: _ctx.intf,
                  value: _ctx.value,
                  onInput: _cache[3] || (_cache[3] = ($event) => _ctx.intf.value = $event),
                  name: _ctx.displayName
                }, null, 8, ["option", "value", "name"]))
              ], 10, ["id"]);
            }
          },
          /* 73 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDynamicComponent"])(_ctx.component), {
                name: _ctx.displayName,
                class: "node-option",
                node: _ctx.node,
                value: _ctx.value,
                option: _ctx.option,
                onInput: _ctx.updateValue,
                onOpenSidebar: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("openSidebar"))
              }, null, 8, ["name", "node", "value", "option", "onInput"]);
            }
          },
          /* 74 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = { class: "flex-fill" };
            const _hoisted_2 = {
              key: 0,
              class: "ml-3",
              style: { "line-height": "1em" }
            };
            const _hoisted_3 = Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
              "svg",
              {
                width: "13",
                height: "13",
                viewBox: "-60 120 250 250"
              },
              [
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("path", {
                  d: "M160.875 279.5625 L70.875 369.5625 L70.875 189.5625 L160.875 279.5625 Z",
                  stroke: "none",
                  fill: "white"
                })
              ],
              -1
              /* HOISTED */
            );
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              const _component_context_menu = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveComponent"])("context-menu", true);
              const _directive_click_outside_element = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDirective"])("click-outside-element");
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["withDirectives"])((Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "div",
                {
                  class: _ctx.classes,
                  style: _ctx.styles
                },
                [
                  (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(true), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                    vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                    null,
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["renderList"])(_ctx._items, (item, index) => {
                      return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                        vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                        null,
                        [
                          item.isDivider ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", {
                            key: `divider-${index}`,
                            class: "divider"
                          })) : (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", {
                            key: `item-${index}`,
                            class: [{ "item": true, "submenu": !!item.submenu, "--disabled": !!item.disabled }, "d-flex align-items-center"],
                            onMouseenter: ($event) => _ctx.onMouseEnter($event, index),
                            onMouseleave: ($event) => _ctx.onMouseLeave($event, index),
                            onClick: Object(vue__WEBPACK_IMPORTED_MODULE_0__["withModifiers"])(($event) => _ctx.onClick(item), ["stop", "prevent"])
                          }, [
                            Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                              "div",
                              _hoisted_1,
                              Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(item.label),
                              1
                              /* TEXT */
                            ),
                            item.submenu ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_2, [
                              _hoisted_3
                            ])) : Object(vue__WEBPACK_IMPORTED_MODULE_0__["createCommentVNode"])("v-if", true),
                            item.submenu ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(_component_context_menu, {
                              key: 1,
                              "model-value": _ctx.activeMenu === index,
                              items: item.submenu,
                              "is-nested": true,
                              "is-flipped": { x: _ctx.flippedX, y: _ctx.flippedY },
                              flippable: _ctx.flippable,
                              onClick: _ctx.onChildClick
                            }, null, 8, ["model-value", "items", "is-flipped", "flippable", "onClick"])) : Object(vue__WEBPACK_IMPORTED_MODULE_0__["createCommentVNode"])("v-if", true)
                          ], 42, ["onMouseenter", "onMouseleave", "onClick"]))
                        ],
                        64
                        /* STABLE_FRAGMENT */
                      );
                    }),
                    256
                    /* UNKEYED_FRAGMENT */
                  ))
                ],
                6
                /* CLASS, STYLE */
              )), [
                [vue__WEBPACK_IMPORTED_MODULE_0__["vShow"], _ctx.modelValue],
                [_directive_click_outside_element, _ctx.onClickOutside]
              ]);
            }
          },
          /* 75 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = { class: "d-flex align-items-center" };
            const _hoisted_2 = { class: "ml-2" };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "div",
                {
                  id: "sidebar",
                  class: ["sidebar", { "--open": _ctx.plugin.sidebar.visible }],
                  style: _ctx.styles
                },
                [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    {
                      class: "__resizer",
                      onMousedown: _cache[1] || (_cache[1] = (...args) => _ctx.startResize && _ctx.startResize(...args))
                    },
                    null,
                    32
                    /* HYDRATE_EVENTS */
                  ),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", _hoisted_1, [
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("button", {
                      tabindex: "-1",
                      class: "__close",
                      onClick: _cache[2] || (_cache[2] = (...args) => _ctx.close && _ctx.close(...args))
                    }, "×"),
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", _hoisted_2, [
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                        "b",
                        null,
                        Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.nodeName),
                        1
                        /* TEXT */
                      )
                    ])
                  ])
                ],
                6
                /* CLASS, STYLE */
              );
            }
          },
          /* 76 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "canvas",
                {
                  ref: "cv",
                  class: "minimap",
                  onMouseenter: _cache[1] || (_cache[1] = ($event) => _ctx.showViewBounds = true),
                  onMouseleave: _cache[2] || (_cache[2] = () => {
                    this.showViewBounds = false;
                    this.mouseup();
                  }),
                  onMousedown: _cache[3] || (_cache[3] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withModifiers"])((...args) => _ctx.mousedown && _ctx.mousedown(...args), ["self"])),
                  onMousemove: _cache[4] || (_cache[4] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withModifiers"])((...args) => _ctx.mousemove && _ctx.mousemove(...args), ["self"])),
                  onMouseup: _cache[5] || (_cache[5] = (...args) => _ctx.mouseup && _ctx.mouseup(...args))
                },
                null,
                544
                /* HYDRATE_EVENTS, NEED_PATCH */
              );
            }
          },
          /* 77 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return BaklavaVuePlugin;
            });
            var _components_Editor_vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(35);
            var vue_click_outside_element__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(102);
            var vue_click_outside_element__WEBPACK_IMPORTED_MODULE_1___default = __webpack_require__.n(vue_click_outside_element__WEBPACK_IMPORTED_MODULE_1__);
            var Baklava = {
              install: function(app) {
                app.component("baklava-editor", _components_Editor_vue__WEBPACK_IMPORTED_MODULE_0__[
                  /* default */
                  "a"
                ]);
                app.use(vue_click_outside_element__WEBPACK_IMPORTED_MODULE_1___default.a);
              }
            };
            var BaklavaVuePlugin = Baklava;
          },
          /* 78 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return ViewPlugin;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            var _baklavajs_events__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(24);
            var _baklavajs_events__WEBPACK_IMPORTED_MODULE_1___default = __webpack_require__.n(_baklavajs_events__WEBPACK_IMPORTED_MODULE_1__);
            var _components_node_Node_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(40);
            var _components_node_NodeOption_vue__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(42);
            var _components_node_NodeInterface_vue__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(41);
            var _components_connection_ConnectionWrapper_vue__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(37);
            var _components_connection_TemporaryConnection_vue__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(39);
            var _components_ContextMenu_vue__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(43);
            var _components_Sidebar_vue__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(44);
            var _components_Minimap_vue__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(45);
            var ViewPlugin = (
              /** @class */
              function() {
                function ViewPlugin2() {
                  this.type = "ViewPlugin";
                  this.panning = { x: 0, y: 0 };
                  this.scaling = 1;
                  this.sidebar = { visible: false, nodeId: "", optionName: "" };
                  this.disable_context_menu = false;
                  this.useStraightConnections = false;
                  this.enableMinimap = false;
                  this.backgroundGrid = {
                    gridSize: 100,
                    gridDivision: 5,
                    subGridVisibleThreshold: 0.6
                  };
                  this.options = {};
                  this.nodeTypeAliases = {};
                  this.hooks = {
                    /** Called whenever a node is rendered */
                    renderNode: new _baklavajs_events__WEBPACK_IMPORTED_MODULE_1__["SequentialHook"](),
                    /** Called whenever an option is rendered */
                    renderOption: new _baklavajs_events__WEBPACK_IMPORTED_MODULE_1__["SequentialHook"](),
                    /** Called whenever an interface is rendered */
                    renderInterface: new _baklavajs_events__WEBPACK_IMPORTED_MODULE_1__["SequentialHook"](),
                    /** Called whenever a connection is rendered */
                    renderConnection: new _baklavajs_events__WEBPACK_IMPORTED_MODULE_1__["SequentialHook"]()
                  };
                  this.components = {
                    node: Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(_components_node_Node_vue__WEBPACK_IMPORTED_MODULE_2__[
                      /* default */
                      "a"
                    ]),
                    nodeOption: Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(_components_node_NodeOption_vue__WEBPACK_IMPORTED_MODULE_3__[
                      /* default */
                      "a"
                    ]),
                    nodeInterface: Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(_components_node_NodeInterface_vue__WEBPACK_IMPORTED_MODULE_4__[
                      /* default */
                      "a"
                    ]),
                    connection: Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(_components_connection_ConnectionWrapper_vue__WEBPACK_IMPORTED_MODULE_5__[
                      /* default */
                      "a"
                    ]),
                    tempConnection: Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(_components_connection_TemporaryConnection_vue__WEBPACK_IMPORTED_MODULE_6__[
                      /* default */
                      "a"
                    ]),
                    contextMenu: Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(_components_ContextMenu_vue__WEBPACK_IMPORTED_MODULE_7__[
                      /* default */
                      "a"
                    ]),
                    sidebar: Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(_components_Sidebar_vue__WEBPACK_IMPORTED_MODULE_8__[
                      /* default */
                      "a"
                    ]),
                    minimap: Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(_components_Minimap_vue__WEBPACK_IMPORTED_MODULE_9__[
                      /* default */
                      "a"
                    ])
                  };
                }
                ViewPlugin2.prototype.register = function(editor) {
                  var _this = this;
                  this.editor = editor;
                  this.editor.hooks.load.tap(this, function(d) {
                    _this.panning = d.panning;
                    _this.scaling = d.scaling;
                    return d;
                  });
                  this.editor.hooks.save.tap(this, function(d) {
                    d.panning = _this.panning;
                    d.scaling = _this.scaling;
                    return d;
                  });
                  this.editor.events.beforeAddNode.addListener(this, function(node) {
                    var n = node;
                    n.position = { x: 0, y: 0 };
                    n.disablePointerEvents = false;
                    n.twoColumn = n.twoColumn || false;
                    n.width = n.width || 200;
                    n.customClasses = n.customClasses || "";
                    node.hooks.save.tap(_this, function(state) {
                      state.position = n.position;
                      state.width = n.width;
                      state.twoColumn = n.twoColumn;
                      state.customClasses = n.customClasses;
                      return state;
                    });
                    node.hooks.load.tap(_this, function(state) {
                      n.position = state.position || { x: 0, y: 0 };
                      n.width = state.width || 200;
                      n.twoColumn = state.twoColumn || false;
                      return state;
                    });
                  });
                };
                ViewPlugin2.prototype.registerOption = function(name, component) {
                  this.options[name] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["markRaw"])(component);
                };
                ViewPlugin2.prototype.setNodeTypeAlias = function(nodeType, alias) {
                  if (alias) {
                    this.nodeTypeAliases[nodeType] = alias;
                  } else if (this.nodeTypeAliases[nodeType]) {
                    delete this.nodeTypeAliases[nodeType];
                  }
                };
                return ViewPlugin2;
              }()
            );
          },
          /* 79 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Editor_vue_vue_type_template_id_a6582abc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(51);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Editor_vue_vue_type_template_id_a6582abc__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 80 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var Clipboard = (
              /** @class */
              function() {
                function Clipboard2(editor) {
                  this.nodeBuffer = "";
                  this.connectionBuffer = "";
                  this.editor = editor;
                }
                Object.defineProperty(Clipboard2.prototype, "isEmpty", {
                  get: function() {
                    return !this.nodeBuffer;
                  },
                  enumerable: false,
                  configurable: true
                });
                Clipboard2.prototype.clear = function() {
                  this.nodeBuffer = "";
                  this.connectionBuffer = "";
                };
                Clipboard2.prototype.copy = function(selectedNodes) {
                  this.connectionBuffer = JSON.stringify(this.editor.connections.filter(function(conn) {
                    return selectedNodes.includes(conn.from.parent) && selectedNodes.includes(conn.to.parent);
                  }).map(function(conn) {
                    return { from: conn.from.id, to: conn.to.id };
                  }));
                  this.nodeBuffer = JSON.stringify(selectedNodes.map(function(n) {
                    return n.save();
                  }));
                };
                Clipboard2.prototype.paste = function() {
                  var e_1, _a, e_2, _b;
                  var _this = this;
                  var idmap = /* @__PURE__ */ new Map();
                  var intfmap = /* @__PURE__ */ new Map();
                  var parsedNodeBuffer = JSON.parse(this.nodeBuffer);
                  var parsedConnectionBuffer = JSON.parse(this.connectionBuffer);
                  var _loop_1 = function(n2) {
                    var nodeType = this_1.editor.nodeTypes.get(n2.type);
                    if (!nodeType) {
                      console.warn("Node type " + n2.type + " not registered");
                      return { value: void 0 };
                    }
                    var copiedNode = new nodeType();
                    var generatedId = copiedNode.id;
                    copiedNode.interfaces.forEach(function(intf) {
                      intf.hooks.load.tap(_this, function(intfState) {
                        var newIntfId = _this.editor.generateId("ni");
                        idmap.set(intfState.id, newIntfId);
                        intfmap.set(intfState.id, generatedId);
                        intf.id = newIntfId;
                        intf.hooks.load.untap(_this);
                        return intfState;
                      });
                    });
                    copiedNode.hooks.load.tap(this_1, function(nodeState) {
                      var ns = nodeState;
                      if (ns.position) {
                        ns.position.x += 10;
                        ns.position.y += 10;
                      }
                      return ns;
                    });
                    this_1.editor.addNode(copiedNode);
                    copiedNode.load(n2);
                    copiedNode.id = generatedId;
                    idmap.set(n2.id, generatedId);
                  };
                  var this_1 = this;
                  try {
                    for (var parsedNodeBuffer_1 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__values"])(parsedNodeBuffer), parsedNodeBuffer_1_1 = parsedNodeBuffer_1.next(); !parsedNodeBuffer_1_1.done; parsedNodeBuffer_1_1 = parsedNodeBuffer_1.next()) {
                      var n = parsedNodeBuffer_1_1.value;
                      var state_1 = _loop_1(n);
                      if (typeof state_1 === "object")
                        return state_1.value;
                    }
                  } catch (e_1_1) {
                    e_1 = { error: e_1_1 };
                  } finally {
                    try {
                      if (parsedNodeBuffer_1_1 && !parsedNodeBuffer_1_1.done && (_a = parsedNodeBuffer_1.return)) _a.call(parsedNodeBuffer_1);
                    } finally {
                      if (e_1) throw e_1.error;
                    }
                  }
                  var _loop_2 = function(c2) {
                    var fromNode = this_2.editor.nodes.find(function(n2) {
                      return n2.id === intfmap.get(c2.from);
                    });
                    var toNode = this_2.editor.nodes.find(function(n2) {
                      return n2.id === intfmap.get(c2.to);
                    });
                    if (!fromNode || !toNode) {
                      return "continue";
                    }
                    var fromIntf = Array.from(fromNode.interfaces.values()).find(function(intf) {
                      return intf.id === idmap.get(c2.from);
                    });
                    var toIntf = Array.from(toNode.interfaces.values()).find(function(intf) {
                      return intf.id === idmap.get(c2.to);
                    });
                    if (!fromIntf || !toIntf) {
                      return "continue";
                    }
                    this_2.editor.addConnection(fromIntf, toIntf);
                  };
                  var this_2 = this;
                  try {
                    for (var parsedConnectionBuffer_1 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__["__values"])(parsedConnectionBuffer), parsedConnectionBuffer_1_1 = parsedConnectionBuffer_1.next(); !parsedConnectionBuffer_1_1.done; parsedConnectionBuffer_1_1 = parsedConnectionBuffer_1.next()) {
                      var c = parsedConnectionBuffer_1_1.value;
                      _loop_2(c);
                    }
                  } catch (e_2_1) {
                    e_2 = { error: e_2_1 };
                  } finally {
                    try {
                      if (parsedConnectionBuffer_1_1 && !parsedConnectionBuffer_1_1.done && (_b = parsedConnectionBuffer_1.return)) _b.call(parsedConnectionBuffer_1);
                    } finally {
                      if (e_2) throw e_2.error;
                    }
                  }
                };
                return Clipboard2;
              }()
            );
            __webpack_exports__["a"] = Clipboard;
          },
          /* 81 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _nodeStep__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(47);
            var _connectionStep__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(48);
            var History = (
              /** @class */
              function() {
                function History2(viewPlugin) {
                  var _this = this;
                  this.maxSteps = 200;
                  this.steps = [];
                  this.changeBySelf = false;
                  this.currentIndex = -1;
                  this.activeTransaction = false;
                  this.transactionSteps = [];
                  this.viewPlugin = viewPlugin;
                  this.viewPlugin.editor.events.addNode.addListener(this, function(node) {
                    _this.addStep(new _nodeStep__WEBPACK_IMPORTED_MODULE_0__[
                      /* default */
                      "a"
                    ]("addNode", node.id));
                  });
                  this.viewPlugin.editor.events.removeNode.addListener(this, function(node) {
                    _this.addStep(new _nodeStep__WEBPACK_IMPORTED_MODULE_0__[
                      /* default */
                      "a"
                    ]("removeNode", node.save()));
                  });
                  this.viewPlugin.editor.events.addConnection.addListener(this, function(conn) {
                    _this.addStep(new _connectionStep__WEBPACK_IMPORTED_MODULE_1__[
                      /* default */
                      "a"
                    ]("addConnection", conn.id));
                  });
                  this.viewPlugin.editor.events.removeConnection.addListener(this, function(conn) {
                    _this.addStep(new _connectionStep__WEBPACK_IMPORTED_MODULE_1__[
                      /* default */
                      "a"
                    ]("removeConnection", conn));
                  });
                }
                History2.prototype.startTransaction = function() {
                  this.activeTransaction = true;
                };
                History2.prototype.commitTransaction = function() {
                  this.activeTransaction = false;
                };
                History2.prototype.undo = function() {
                  if (this.steps.length === 0 || this.currentIndex === -1) {
                    return;
                  }
                  this.changeBySelf = true;
                  this.steps[this.currentIndex--].undo(this.viewPlugin.editor);
                  this.changeBySelf = false;
                };
                History2.prototype.redo = function() {
                  if (this.steps.length === 0 || this.currentIndex >= this.steps.length - 1) {
                    return;
                  }
                  this.changeBySelf = true;
                  this.steps[++this.currentIndex].redo(this.viewPlugin.editor);
                  this.changeBySelf = false;
                };
                History2.prototype.addStep = function(step) {
                  if (this.changeBySelf) {
                    return;
                  }
                  if (this.activeTransaction) {
                    this.transactionSteps.push(step);
                  } else {
                    if (this.currentIndex !== this.steps.length - 1) {
                      this.steps = this.steps.slice(0, this.currentIndex + 1);
                    }
                    this.steps.push(step);
                    this.currentIndex++;
                    while (this.steps.length > this.maxSteps) {
                      this.steps.shift();
                    }
                  }
                };
                return History2;
              }()
            );
            __webpack_exports__["a"] = History;
          },
          /* 82 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_ConnectionView_vue_vue_type_template_id_21b4c967__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_ConnectionView_vue_vue_type_template_id_21b4c967__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 83 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_ConnectionWrapper_vue_vue_type_template_id_56e03f2e__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(68);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_ConnectionWrapper_vue_vue_type_template_id_56e03f2e__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 84 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _ResizeObserver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(69);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _ResizeObserver__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
            var _ResizeObserverEntry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(36);
          },
          /* 85 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return process;
            });
            var _algorithms_hasActiveObservations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(86);
            var _algorithms_hasSkippedObservations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(87);
            var _algorithms_deliverResizeLoopError__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(88);
            var _algorithms_broadcastActiveObservations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(89);
            var _algorithms_gatherActiveObservationsAtDepth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(50);
            var process = function() {
              var depth = 0;
              Object(_algorithms_gatherActiveObservationsAtDepth__WEBPACK_IMPORTED_MODULE_4__[
                /* gatherActiveObservationsAtDepth */
                "a"
              ])(depth);
              while (Object(_algorithms_hasActiveObservations__WEBPACK_IMPORTED_MODULE_0__[
                /* hasActiveObservations */
                "a"
              ])()) {
                depth = Object(_algorithms_broadcastActiveObservations__WEBPACK_IMPORTED_MODULE_3__[
                  /* broadcastActiveObservations */
                  "a"
                ])();
                Object(_algorithms_gatherActiveObservationsAtDepth__WEBPACK_IMPORTED_MODULE_4__[
                  /* gatherActiveObservationsAtDepth */
                  "a"
                ])(depth);
              }
              if (Object(_algorithms_hasSkippedObservations__WEBPACK_IMPORTED_MODULE_1__[
                /* hasSkippedObservations */
                "a"
              ])()) {
                Object(_algorithms_deliverResizeLoopError__WEBPACK_IMPORTED_MODULE_2__[
                  /* deliverResizeLoopError */
                  "a"
                ])();
              }
              return depth > 0;
            };
          },
          /* 86 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return hasActiveObservations;
            });
            var _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5);
            var hasActiveObservations = function() {
              return _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_0__[
                /* resizeObservers */
                "a"
              ].some(function(ro) {
                return ro.activeTargets.length > 0;
              });
            };
          },
          /* 87 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return hasSkippedObservations;
            });
            var _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5);
            var hasSkippedObservations = function() {
              return _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_0__[
                /* resizeObservers */
                "a"
              ].some(function(ro) {
                return ro.skippedTargets.length > 0;
              });
            };
          },
          /* 88 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return deliverResizeLoopError;
            });
            var msg = "ResizeObserver loop completed with undelivered notifications.";
            var deliverResizeLoopError = function() {
              var event;
              if (typeof ErrorEvent === "function") {
                event = new ErrorEvent("error", {
                  message: msg
                });
              } else {
                event = document.createEvent("Event");
                event.initEvent("error", false, false);
                event.message = msg;
              }
              window.dispatchEvent(event);
            };
          },
          /* 89 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return broadcastActiveObservations;
            });
            var _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5);
            var _ResizeObserverEntry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(36);
            var _calculateDepthForNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(38);
            var _calculateBoxSize__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(10);
            var broadcastActiveObservations = function() {
              var shallowestDepth = Infinity;
              var callbacks = [];
              _utils_resizeObservers__WEBPACK_IMPORTED_MODULE_0__[
                /* resizeObservers */
                "a"
              ].forEach(function processObserver(ro) {
                if (ro.activeTargets.length === 0) {
                  return;
                }
                var entries = [];
                ro.activeTargets.forEach(function processTarget(ot) {
                  var entry = new _ResizeObserverEntry__WEBPACK_IMPORTED_MODULE_1__[
                    /* ResizeObserverEntry */
                    "a"
                  ](ot.target);
                  var targetDepth = Object(_calculateDepthForNode__WEBPACK_IMPORTED_MODULE_2__[
                    /* calculateDepthForNode */
                    "a"
                  ])(ot.target);
                  entries.push(entry);
                  ot.lastReportedSize = Object(_calculateBoxSize__WEBPACK_IMPORTED_MODULE_3__[
                    /* calculateBoxSize */
                    "a"
                  ])(ot.target, ot.observedBox);
                  if (targetDepth < shallowestDepth) {
                    shallowestDepth = targetDepth;
                  }
                });
                callbacks.push(function resizeObserverCallback() {
                  ro.callback.call(ro.observer, entries, ro.observer);
                });
                ro.activeTargets.splice(0, ro.activeTargets.length);
              });
              for (var _i = 0, callbacks_1 = callbacks; _i < callbacks_1.length; _i++) {
                var callback = callbacks_1[_i];
                callback();
              }
              return shallowestDepth;
            };
          },
          /* 90 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return queueResizeObserver;
            });
            var _queueMicroTask__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(91);
            var queueResizeObserver = function(cb) {
              Object(_queueMicroTask__WEBPACK_IMPORTED_MODULE_0__[
                /* queueMicroTask */
                "a"
              ])(function ResizeObserver() {
                requestAnimationFrame(cb);
              });
            };
          },
          /* 91 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return queueMicroTask;
            });
            var trigger;
            var callbacks = [];
            var notify = function() {
              return callbacks.splice(0).forEach(function(cb) {
                return cb();
              });
            };
            var queueMicroTask = function(callback) {
              if (!trigger) {
                var toggle_1 = 0;
                var el_1 = document.createTextNode("");
                var config = { characterData: true };
                new MutationObserver(function() {
                  return notify();
                }).observe(el_1, config);
                trigger = function() {
                  el_1.textContent = "" + (toggle_1 ? toggle_1-- : toggle_1++);
                };
              }
              callbacks.push(callback);
              trigger();
            };
          },
          /* 92 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return ResizeObservation;
            });
            var _ResizeObserverBoxOptions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(22);
            var _algorithms_calculateBoxSize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10);
            var _utils_element__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6);
            var skipNotifyOnElement = function(target) {
              return !Object(_utils_element__WEBPACK_IMPORTED_MODULE_2__[
                /* isSVG */
                "d"
              ])(target) && !Object(_utils_element__WEBPACK_IMPORTED_MODULE_2__[
                /* isReplacedElement */
                "c"
              ])(target) && getComputedStyle(target).display === "inline";
            };
            var ResizeObservation = function() {
              function ResizeObservation2(target, observedBox) {
                this.target = target;
                this.observedBox = observedBox || _ResizeObserverBoxOptions__WEBPACK_IMPORTED_MODULE_0__[
                  /* ResizeObserverBoxOptions */
                  "a"
                ].CONTENT_BOX;
                this.lastReportedSize = {
                  inlineSize: 0,
                  blockSize: 0
                };
              }
              ResizeObservation2.prototype.isActive = function() {
                var size = Object(_algorithms_calculateBoxSize__WEBPACK_IMPORTED_MODULE_1__[
                  /* calculateBoxSize */
                  "a"
                ])(this.target, this.observedBox, true);
                if (skipNotifyOnElement(this.target)) {
                  this.lastReportedSize = size;
                }
                if (this.lastReportedSize.inlineSize !== size.inlineSize || this.lastReportedSize.blockSize !== size.blockSize) {
                  return true;
                }
                return false;
              };
              return ResizeObservation2;
            }();
          },
          /* 93 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return ResizeObserverDetail;
            });
            var ResizeObserverDetail = /* @__PURE__ */ function() {
              function ResizeObserverDetail2(resizeObserver, callback) {
                this.activeTargets = [];
                this.skippedTargets = [];
                this.observationTargets = [];
                this.observer = resizeObserver;
                this.callback = callback;
              }
              return ResizeObserverDetail2;
            }();
          },
          /* 94 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_TemporaryConnection_vue_vue_type_template_id_e0dc6546__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(70);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_TemporaryConnection_vue_vue_type_template_id_e0dc6546__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 95 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Node_vue_vue_type_template_id_61d0bf3a__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(71);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Node_vue_vue_type_template_id_61d0bf3a__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 96 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return sanitizeName;
            });
            function sanitizeName(name) {
              return name.replace(" ", "-");
            }
          },
          /* 97 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_NodeInterface_vue_vue_type_template_id_522de0f9__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(72);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_NodeInterface_vue_vue_type_template_id_522de0f9__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 98 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_NodeOption_vue_vue_type_template_id_0f2a2624__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(73);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_NodeOption_vue_vue_type_template_id_0f2a2624__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 99 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_ContextMenu_vue_vue_type_template_id_41109a43__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(74);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_ContextMenu_vue_vue_type_template_id_41109a43__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 100 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Sidebar_vue_vue_type_template_id_2dc89b20__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(75);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Sidebar_vue_vue_type_template_id_2dc89b20__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 101 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Minimap_vue_vue_type_template_id_64fd4b08__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(76);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Minimap_vue_vue_type_template_id_64fd4b08__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 102 */
          /***/
          function(module2, exports2, __webpack_require__) {
            !function(e, t) {
              true ? t(exports2) : void 0;
            }(this, function(e) {
              "use strict";
              function t(e2) {
                return (t2) => {
                  t2.target instanceof Element && (this === t2.target || this.contains(t2.target) || e2.value(t2));
                };
              }
              const i = { beforeMount(e2, i2, n2) {
                var o;
                if (!("Function" === (null == (o = null == i2 ? void 0 : i2.value) ? void 0 : o.constructor.name))) throw Error("[v-click-outside-element] Function should be provided in the directive");
                e2.clickOutside = t.bind(e2)(i2), window.addEventListener("click", e2.clickOutside);
              }, beforeUnmount(e2) {
                window.removeEventListener("click", e2.clickOutside);
              } }, n = { install(e2, t2 = "click-outside-element") {
                e2.directive(t2, i);
              } };
              e.default = n, e.directive = i, Object.defineProperties(e, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
            });
          },
          /* 103 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.r(__webpack_exports__);
            __webpack_require__.d(__webpack_exports__, "Components", function() {
              return Components;
            });
            var _components_Editor_vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(35);
            __webpack_require__.d(__webpack_exports__, "Editor", function() {
              return _components_Editor_vue__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
            var _components_connection_ConnectionView_vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
            var _components_connection_ConnectionWrapper_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(37);
            var _components_connection_TemporaryConnection_vue__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(39);
            var _components_node_Node_vue__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(40);
            var _components_node_NodeInterface_vue__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(41);
            var _components_node_NodeOption_vue__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(42);
            var _components_ContextMenu_vue__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(43);
            var _components_Sidebar_vue__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(44);
            var _components_Minimap_vue__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(45);
            var _baklavaVuePlugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(77);
            __webpack_require__.d(__webpack_exports__, "BaklavaVuePlugin", function() {
              return _baklavaVuePlugin__WEBPACK_IMPORTED_MODULE_10__["a"];
            });
            var _viewPlugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(78);
            __webpack_require__.d(__webpack_exports__, "ViewPlugin", function() {
              return _viewPlugin__WEBPACK_IMPORTED_MODULE_11__["a"];
            });
            var Components = {
              Connection: _components_connection_ConnectionView_vue__WEBPACK_IMPORTED_MODULE_1__[
                /* default */
                "a"
              ],
              ConnectionWrapper: _components_connection_ConnectionWrapper_vue__WEBPACK_IMPORTED_MODULE_2__[
                /* default */
                "a"
              ],
              TemporaryConnection: _components_connection_TemporaryConnection_vue__WEBPACK_IMPORTED_MODULE_3__[
                /* default */
                "a"
              ],
              Node: _components_node_Node_vue__WEBPACK_IMPORTED_MODULE_4__[
                /* default */
                "a"
              ],
              NodeInterface: _components_node_NodeInterface_vue__WEBPACK_IMPORTED_MODULE_5__[
                /* default */
                "a"
              ],
              NodeOption: _components_node_NodeOption_vue__WEBPACK_IMPORTED_MODULE_6__[
                /* default */
                "a"
              ],
              ContextMenu: _components_ContextMenu_vue__WEBPACK_IMPORTED_MODULE_7__[
                /* default */
                "a"
              ],
              Sidebar: _components_Sidebar_vue__WEBPACK_IMPORTED_MODULE_8__[
                /* default */
                "a"
              ],
              Minimap: _components_Minimap_vue__WEBPACK_IMPORTED_MODULE_9__[
                /* default */
                "a"
              ]
            };
          }
          /******/
        ])
      );
    });
  }
});
export default require_dist();
/*! Bundled license information:

@kanbang/plugin-renderer-vue3/dist/index.js:
  (*! *****************************************************************************
  Copyright (c) Microsoft Corporation.
  
  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.
  
  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** *)
  (**
    * vue-class-component v8.0.0-rc.1
    * (c) 2015-present Evan You
    * @license MIT
    *)
*/
//# sourceMappingURL=@kanbang_plugin-renderer-vue3.js.map

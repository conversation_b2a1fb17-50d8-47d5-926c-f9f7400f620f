<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue';
import { useMQTT } from 'mqtt-vue-hook';
import WaveChart from '@/components/waveChart/index.vue';
import { NDropdown, NButton, NTable } from 'naive-ui';
// import { decode, encode } from '@msgpack/msgpack';
import FFT from 'fft.js/lib/fft';

const mqttHook = useMQTT();
// let chartContainerRef = ref(null);

let VPhaseRef = ref();
let VFreqRef = ref();
let CPhaseRef = ref();
let CFreqRef = ref();
let ExcRef = ref();

let VPhaseOptions = reactive({ title: '电压', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let VFreqOptions = reactive({ title: '频率', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let CPhaseOptions = reactive({ title: '电流', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let CFreqOptions = reactive({ title: '频率', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let ExcOptions = reactive({ title: '电流', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });

const tabKey = ref('tension');
const sectabKey = ref('exci');
const showRightPanel = ref(true);
// 模拟本地数据
// const useLocalData = false;
// const fileList = [
//   '/data_simple/data_type0_20250719_225436.json',
//   '/data_simple/data_type0_20250719_225441.json',
//   '/data_simple/data_type0_20250719_225446.json',
//   '/data_simple/data_type0_20250719_225451.json'
// ];
// let currentFileIndex = 0;
// const simulateLocalData = async () => {
//   const loopNext = async () => {
//     const file = fileList[currentFileIndex];
//     try {
//       const res = await fetch(file);
//       const json = await res.json();
//       const rows = json.ElecSignal.slice(1);
//       const timeIndex = 0;
//       const len = rows.length;
//       if (len <= 1) return;
//       const tStart = rows[0][timeIndex];
//       const tEnd = rows[len - 1][timeIndex];
//       const step = (tEnd - tStart) / (len - 1);
//       const x = Array.from({ length: len }, (_, i) => tStart + i * step);
//       const headerMap = {
//         'VA': 2, 'VB': 3, 'VC': 4,
//         'IA': 5, 'IB': 6, 'IC': 7,
//         'IExc': 8
//       };
//       const getSeries = (names) => names.map(name => ({
//         name,
//         value: rows.map(row => row[headerMap[name]])
//       }));

//       const yDataVoltage = getSeries(['VA', 'VB', 'VC']);
//       const yDataCurrent = getSeries(['IA', 'IB', 'IC']);
//       const yDataExc = getSeries(['IExc']);

//       VPhaseRef.value?.appendData({ x, y: yDataVoltage }, 10000);
//       CPhaseRef.value?.appendData({ x, y: yDataCurrent }, 10000);
//       ExcRef.value?.appendData({ x, y: yDataExc }, 10000);

//       yDataVoltage.forEach(series => {
//         fullData[`voltage-${series.name}`] = x.map((xi, i) => ({ x: xi, y: series.value[i] }));
//       });
//       yDataCurrent.forEach(series => {
//         fullData[`current-${series.name}`] = x.map((xi, i) => ({ x: xi, y: series.value[i] }));
//       });
//       yDataExc.forEach(series => {
//         fullData[`excitation-${series.name}`] = x.map((xi, i) => ({ x: xi, y: series.value[i] }));
//       });

//       updateTable(selectedLine.value);
//     } catch (err) {
//       console.error('加载失败:', err);
//     }
//     currentFileIndex = (currentFileIndex + 1) % fileList.length;
//     setTimeout(loopNext, 2000);
//   };
//   loopNext();
// };

let fullData = reactive({});
let tableData = ref([]);
const selectedLine = ref('excitation-励磁电流');

const dropdownOptions = [
  {
    label: '电压',
    key: 'voltage',
    children: [
      { label: 'A相电压', key: 'voltage-A相电压' },
      { label: 'B相电压', key: 'voltage-B相电压' },
      { label: 'C相电压', key: 'voltage-C相电压' }
    ]
  },
  {
    label: '电流',
    key: 'current',
    children: [
      { label: 'A相电流', key: 'current-A相电流' },
      { label: 'B相电流', key: 'current-B相电流' },
      { label: 'C相电流', key: 'current-C相电流' }
    ]
  },
  {
    label: '励磁电流',
    key: 'excitation',
    children: [
      { label: 'IExc-励磁电流', key: 'excitation-励磁电流' }
    ]
  },
  {
    label: '频谱（电压）',
    key: 'freq-voltage',
    children: [
      { label: 'A相电压', key: 'freq-频谱(A相电压)' },
      { label: 'B相电压', key: 'freq-频谱(B相电压)' },
      { label: 'C相电压', key: 'freq-频谱(C相电压)' }
    ]
  },
  {
    label: '频谱（电流）',
    key: 'freq-current',
    children: [
      { label: 'A相电流', key: 'freq-频谱(A相电流)' },
      { label: 'B相电流', key: 'freq-频谱(B相电流)' },
      { label: 'C相电流', key: 'freq-频谱(C相电流)' }
    ]
  }
];

// 更新右侧列表
const updateTable = (lineKey) => {
  tableData.value = fullData[lineKey] || [];
};
// 选择列表线路
const handleSelect = (key) => {
  selectedLine.value = key;
  updateTable(key);
};

watch(showRightPanel, async () => {
  await nextTick();
  [VPhaseRef, VFreqRef, CPhaseRef, CFreqRef, ExcRef].forEach(refItem => {
    refItem.value?.resize?.();
  });
});

function getGroupKey(name: string): string {
  if (['A相电压', 'B相电压', 'C相电压'].includes(name)) return 'voltage';
  if (['A相电流', 'B相电流', 'C相电流'].includes(name)) return 'current';
  if (name === '励磁电流') return 'excitation';
  if (['频谱(A相电压)', '频谱(B相电压)', '频谱(C相电压)', '频谱(A相电流)', '频谱(B相电流)', '频谱(C相电流)'].includes(name)) return 'freq';
  return 'unknown';
}

onMounted(() => {
  mqttHook.registerEvent('TVS/data/type1', (topic, message) => {
    try {
      // ① 解码：正确解码 JSON 格式的字节数组
      const jsonStr = new TextDecoder().decode(message);
      const decoded = JSON.parse(jsonStr);
      console.log('✅ 解码成功:', decoded);

      const raw = decoded.ElecSignal;
      if (!raw || raw.length <= 1) return;

      const headers = raw[0]; // 表头
      const rows = raw.slice(1); // 数据行

      const tIndex = headers.indexOf('Tstamp');
      const channelIndices = {
        VA: headers.indexOf('VA'),
        VB: headers.indexOf('VB'),
        VC: headers.indexOf('VC'),
        IA: headers.indexOf('IA'),
        IB: headers.indexOf('IB'),
        IC: headers.indexOf('IC'),
        IExc: headers.indexOf('IExc')
      };
      const lineName = {
        'VA': 'A相电压',
        'VB': 'B相电压',
        'VC': 'C相电压',
        'IA': 'A相电流',
        'IB': 'B相电流',
        'IC': 'C相电流',
        'IExc': '励磁电流'
      }
      const x = rows.map(row => {
        const rawT = Number(row[tIndex]);
        const ms = Math.floor(rawT / 1000);
        const d = new Date(ms - 8 * 60 * 60 * 1000);
        const pad = (n: number, len = 2) => String(n).padStart(len, '0');
        return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ` +
          `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}.${pad(d.getMilliseconds(), 3)}`;
      });

      const buildY = (names: string[]) =>
        names.map(name => ({
          name: lineName[name],
          value: rows.map(row => row[channelIndices[name]])
        }));

      const chartDataV = { x, y: buildY(['VA', 'VB', 'VC']) };
      const chartDataI = { x, y: buildY(['IA', 'IB', 'IC']) };
      const chartDataE = { x, y: buildY(['IExc']) };

      VPhaseRef.value?.appendData(chartDataV, 10000);
      CPhaseRef.value?.appendData(chartDataI, 10000);
      ExcRef.value?.appendData(chartDataE, 10000);

      // 增加频谱处理逻辑（每组计算一次），提取时间戳（微秒）并计算采样率
      if (rows.length < 2) {
        console.warn('❗️数据不足，跳过频谱计算');
        return;
      }
      const t0 = Number(rows[0][tIndex]);
      const t1 = Number(rows[1][tIndex]);
      const dt_us = t1 - t0;
      const fs = 1e6 / dt_us;  // Hz，采样率
      const N = rows.length;

      // 准备信号通道数据
      const getSignal = (ch: keyof typeof channelIndices) =>
        rows.map(row => Number(row[channelIndices[ch]]));

      const signalVA = getSignal('VA');
      const signalVB = getSignal('VB');
      const signalVC = getSignal('VC');
      const signalIA = getSignal('IA');
      const signalIB = getSignal('IB');
      const signalIC = getSignal('IC');

      const computeFFT = (signal: number[]) => {
        const fft = new FFT(N);
        const out = fft.createComplexArray();
        fft.realTransform(out, signal);
        fft.completeSpectrum(out);
        const mag = [];
        for (let i = 0; i < N / 2; i++) {
          const re = out[2 * i], im = out[2 * i + 1];
          mag.push(Math.sqrt(re * re + im * im));
        }
        return mag;
      };

      // 频率轴（仅 N/2 点）
      const freq = Array.from({ length: N / 2 }, (_, i) => i * fs / N);

      const chartDataFV = {
        x: freq,
        y: [
          { name: '频谱(A相电压)', value: computeFFT(signalVA) },
          { name: '频谱(B相电压)', value: computeFFT(signalVB) },
          { name: '频谱(C相电压)', value: computeFFT(signalVC) },
        ],
      };

      const chartDataFI = {
        x: freq,
        y: [
          { name: '频谱(A相电流)', value: computeFFT(signalIA) },
          { name: '频谱(B相电流)', value: computeFFT(signalIB) },
          { name: '频谱(C相电流)', value: computeFFT(signalIC) },
        ],
      };

      VFreqRef.value?.setData(chartDataFV);
      CFreqRef.value?.setData(chartDataFI);

      // 更新 fullData（用于表格展示）
      const assignFullData = (chartData: typeof chartDataV) => {
        chartData.y.forEach(series => {
          fullData[`${getGroupKey(series.name)}-${series.name}`] = chartData.x.map((xi, i) => ({
            x: xi,
            y: series.value[i]
          }));
        });
      };
      // 分别更新表格数据源
      assignFullData(chartDataV);
      assignFullData(chartDataI);
      assignFullData(chartDataE);
      assignFullData(chartDataFV);
      assignFullData(chartDataFI);

      // 更新右侧表格展示
      updateTable(selectedLine.value);
    } catch (err) {
      console.error('❌ MQTT 解码失败:', err);
    }
  }, 'mqtt-wave-key');
});

onUnmounted(() => {
  mqttHook.unRegisterEvent('TVS/data/type1', 'mqtt-wave-key');
});
</script>

<template>
  <div class="flex h-full">
    <div class="flex flex-col" :style="{ width: showRightPanel ? 'calc(100% - 13rem)' : '100%' }">
      <div class="flex h-1/2 border-b border-gray-700">
        <n-tabs v-model:value="tabKey" placement="left" type="line">
          <n-tab-pane name="tension" tab="三相电压">
            <div class="flex h-full">
              <WaveChart ref="VPhaseRef" height="100%" :options="VPhaseOptions"
                class="flex-1 border-r border-gray-700" />
              <WaveChart ref="VFreqRef" height="100%" :options="VFreqOptions" class="flex-1" />
            </div>
          </n-tab-pane>
          <n-tab-pane name="current" tab="三相电流">
            <div class="flex h-full">
              <WaveChart ref="CPhaseRef" height="100%" :options="CPhaseOptions"
                class="flex-1 border-r border-gray-700" />
              <WaveChart ref="CFreqRef" height="100%" :options="CFreqOptions" class="flex-1" />
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>

      <div class="h-1/2 flex">
        <n-tabs v-model:value="sectabKey" placement="left" type="line">
          <n-tab-pane name="exci" tab="励磁电流">
            <div class="flex h-full">
              <WaveChart ref="ExcRef" :options="ExcOptions" class="flex-1" />
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
    </div>

    <!-- 右侧可收缩面板 -->
    <!-- ✅ 外层容器必须设置 relative -->
    <div class="relative h-full">
      <!-- ✅ 收起状态的展开按钮 -->
      <n-button v-show="!showRightPanel" class="expand-icon" size="tiny" text @click="showRightPanel = true"
        type="primary">
        <template #icon>
          <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
        </template>
      </n-button>

      <!-- 展开状态的右侧面板 -->
      <transition name="slide">
        <div v-show="showRightPanel" class="w-52 h-full border-l border-gray-700 bg-black text-white flex flex-col">
          <!-- 按钮行：折叠 + 选择 -->
          <div class="flex p-2 gap-1">
            <n-button quaternary size="small" @click="showRightPanel = false"
              style="background-color: #2a2a2a; border: 1px solid #888;width: 28px;height:28px;">
              <template #icon>
                <img src="@/assets/imgs/TablerChevronRightPipe.svg" class="sicon-white " />
              </template>
            </n-button>
            <n-dropdown trigger="click" :options="dropdownOptions" @select="handleSelect">
              <n-button size="small" class="flex-1 selectline">
                选择线路 - {{ selectedLine.split('-')[1] }}
              </n-button>
            </n-dropdown>
          </div>

          <!-- 表格部分 -->
          <div class="flex-1 overflow-auto p-2">
            <n-table :bordered="false" :single-line="false" size="small">
              <thead>
                <tr>
                  <th>序号</th>
                  <th>X</th>
                  <th>Y</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(row, index) in tableData" :key="index">
                  <td>{{ index + 1 }}</td>
                  <td>{{ row.x }}</td>
                  <td>{{ row.y }}</td>
                </tr>
              </tbody>
            </n-table>
          </div>
        </div>
      </transition>
    </div>


  </div>
</template>

<style scoped>
.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.toggle-btn {
  writing-mode: vertical-lr;
  transform: rotate(180deg);
}


.expand-icon {
  position: fixed;
  /* 固定在页面右侧 */
  top: 123px;
  /* 可根据需要调整垂直位置 */
  right: 0;
  z-index: 1000;

  width: 28px;
  height: 28px;
  padding: 0;
  background-color: #2a2a2a;
  border: 1px solid #888;
  border-radius: 4px;
}

.sicon-white {
  filter: brightness(0) invert(1);
  width: 16px;
  height: 16px;
}

:deep(.n-tabs-tab) {
  padding: 10px 6px 5px 0px !important;
}

:deep(.n-table) {
  font-size: 10px;
}

.selectline {
  font-size: 12px;
}
</style>

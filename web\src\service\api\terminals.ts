/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-21 19:42:39
 * @LastEditors: zhai
 * @LastEditTime: 2024-06-21 20:01:57
 */
import { Crud<PERSON><PERSON> } from '../crud-api';
import { FastCrudApi } from '../fast-crud-api';

/// ///////////////////////////////////////////////////////////////////

// 基础终端数据结构
export interface TerminalCard {
  id: string;
  name: string;
  serial_code: string;
  description: string | null;
  client_id: string;
  status: boolean;
}


export class FastTerminalsApi extends FastCrudApi<FastTerminalsApi> {
  constructor() {
    super('terminals');
  }
}

export const fast_terminals_api = FastTerminalsApi.instance();

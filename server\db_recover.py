'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-04-26 13:43:14
LastEditors: zhai
LastEditTime: 2025-04-26 13:43:56
'''
import sqlite3


# 创建新数据库并导入旧数据
try:
    new_conn = sqlite3.connect('repaired_database_file.db')
    old_conn = sqlite3.connect('file:app_system.sqlite3?mode=ro', uri=True)
    
    # 复制数据
    old_conn.backup(new_conn)
    
    old_conn.close()
    new_conn.close()
    print("修复完成，新数据库: repaired_database_file.db")
except Exception as e:
    print("修复失败:", e)


# try:
#     # 尝试连接数据库
#     conn = sqlite3.connect('app_system.sqlite3')
#     cursor = conn.cursor()
#     cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
#     print("Tables:", cursor.fetchall())
#     conn.close()
# except sqlite3.DatabaseError as e:
#     print("数据库损坏:", e)
#     print("尝试修复...")
    
#     # 创建新数据库并导入旧数据
#     try:
#         new_conn = sqlite3.connect('repaired_database_file.db')
#         old_conn = sqlite3.connect('file:app_system.sqlite3?mode=ro', uri=True)
        
#         # 复制数据
#         old_conn.backup(new_conn)
        
#         old_conn.close()
#         new_conn.close()
#         print("修复完成，新数据库: repaired_database_file.db")
#     except Exception as e:
#         print("修复失败:", e)
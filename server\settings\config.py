"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-04 15:11:53
LastEditors: zhai
LastEditTime: 2025-01-05 20:08:22
"""

from pathlib import Path
import sys
from typing import Any, Optional

from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field
from yarl import URL


def GetBaseDir():
    if getattr(sys, "frozen", False):
        # 如果是被打包的应用程序，使用 sys.executable 获取路径
        root_path = Path(sys.executable).resolve()
    else:
        # 当前文件（config.py）的路径
        # root_path = Path(__file__).resolve().parent

        # 获取主脚本的路径(run.py)
        root_path = Path(sys.argv[0]).resolve()

    return root_path.parent


class Settings(BaseSettings):
    VERSION: str = "1.0.0"
    APP_TITLE: str = "FisheryCloud"
    APP_DESCRIPTION: str = "fishery cloud system"

    CORS_ORIGINS: list[str] = Field(default_factory=lambda: ["*"])
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list[str] = Field(default_factory=lambda: ["*"])
    CORS_ALLOW_HEADERS: list[str] = Field(default_factory=lambda: ["*"])

    ADD_LOG_ORIGINS_INCLUDE: list[str] = Field(default_factory=lambda: ["*"])
    ADD_LOG_ORIGINS_DECLUDE: list[str] = Field(
        default_factory=lambda: ["/system-manage", "/redoc", "/doc", "/openapi.json"]
    )

    DEBUG: bool = False

    BASE_DIR: Path = GetBaseDir()
    LOGS_ROOT: Path = BASE_DIR / "logs"
    STATIC_ROOT: Path = BASE_DIR / "static/"

    SECRET_KEY: str = "015a42020f023ac2c3eda3d45fe5ca3fef8921ce63589f6d4fcdef9814cd7fa7"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 12  # 12 hours
    JWT_REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days

    DATETIME_FORMAT: str = "%Y-%m-%d %H:%M:%S"

    HOST_PORT: int = 9999
    WORKERS: int = 1

    # Variables for the database
    DB_ENGINE: str = "sqlite"

    DB_FILE: str = "db_system.sqlite3"  # 适用于 SQLite
    DB_HOST: str = "localhost"  # 适用于 PostgreSQL/MySQL/MSSQL
    DB_PORT: Optional[int] = None  # 适用于 PostgreSQL/MySQL/MSSQL
    DB_USER: Optional[str] = None  # 适用于 PostgreSQL/MySQL/MSSQL
    DB_PASSWORD: Optional[str] = None  # 适用于 PostgreSQL/MySQL/MSSQL
    DB_NAME: str = "ifd"  # 适用于 PostgreSQL/MySQL/MSSQL

    SSE_BATCH_SIZE: int = 2048

    # QDB 配置
    QDB_HOST: str = "127.0.0.1"
    QDB_PORT: Optional[int] = 8812
    QDB_USER: Optional[str] = "admin"
    QDB_PASSWORD: Optional[str] = "quest"
    QDB_DATABASE: str = "qdb"

    @property
    def TORTOISE_ORM(self):
        # 定义数据库引擎配置映射
        # If an error occurs, you can try to delete the "migrations/app_system" folder and all tables, and then run the project again
        db_configs = {
            "sqlite": {
                "engine": "tortoise.backends.sqlite",
                "credentials": {"file_path": f"{self.BASE_DIR}/{self.DB_FILE}"},
            },
            # if you want to use PostgreSQL, you need to install tortoise-orm[asyncpg]
            "pgsql": {
                "engine": "tortoise.backends.asyncpg",
                "credentials": {
                    "host": self.DB_HOST,
                    "port": self.DB_PORT,
                    "user": self.DB_USER,
                    "password": self.DB_PASSWORD,
                    "database": self.DB_NAME,
                },
            },
            # if you want to use MySQL/MariaDB, you need to install tortoise-orm[asyncmy]
            "mysql": {
                "engine": "tortoise.backends.mysql",
                "credentials": {
                    "host": self.DB_HOST,
                    "port": self.DB_PORT,
                    "user": self.DB_USER,
                    "password": self.DB_PASSWORD,
                    "database": self.DB_NAME,
                },
            },
            # if you want to use MSSQL/Oracle, you need to install tortoise-orm[asyncodbc]
            "mssql": {
                "engine": "tortoise.backends.asyncodbc",
                "credentials": {
                    "host": self.DB_HOST,
                    "port": self.DB_PORT,
                    "user": self.DB_USER,
                    "password": self.DB_PASSWORD,
                    "database": self.DB_NAME,
                },
            },
        }

        # 获取当前数据库引擎的配置
        if self.DB_ENGINE not in db_configs:
            raise ValueError(f"Unsupported DB_ENGINE: {self.DB_ENGINE}")

        connections = {self.DB_ENGINE: db_configs[self.DB_ENGINE]}

        return {
            "connections": connections,
            "apps": {
                # don't modify `app_system`, otherwise you will need to modify all `app_systems` in app/models/admin.py
                "app_system": {
                    "models": [
                        "models.system",
                        "models.task",
                        "models.daq",
                        "models.mqtt",
                        # "models.dummy",
                        # "models.fishery",
                        "aerich.models",
                    ],
                    "default_connection": self.DB_ENGINE,
                },
            },
            "use_tz": False,
            "timezone": "Asia/Shanghai",
        }

    # Variables for Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_USER: Optional[str] = None
    REDIS_PASS: Optional[str] = None
    REDIS_BASE: Optional[int] = None

    # REDIS_URL: str = "redis://redis:6379/0"  # "redis://:password@***************:33333/0"

    MQTT_TOPIC_HEADER: str = "BHTSJ"

    @property
    def REDIS_URL(self) -> str:
        """
        Assemble REDIS URL from settings.

        :return: redis URL.
        """
        path = ""
        if self.REDIS_BASE is not None:
            path = f"/{self.REDIS_BASE}"

        url = str(
            URL.build(
                scheme="redis",
                host=self.REDIS_HOST,
                port=self.REDIS_PORT,
                user=self.REDIS_USER,
                password=self.REDIS_PASS,
                path=path,
            )
        )

        return url

    FAST_API_CALL_QUEUE: str = "redis_queue_fastapi_remote_call"
    GATEWAY_MQTT_PUBLISH_QUEUE: str = "redis_queue_gateway_mqtt_publish"

    model_config = SettingsConfigDict(
        env_file=GetBaseDir() / ".env", env_file_encoding="utf-8", extra="ignore"
    )

    # class Config:
    #     env_file = GetBaseDir() / ".env"
    #     env_file_encoding = "utf-8"
    #     extra = "ignore"

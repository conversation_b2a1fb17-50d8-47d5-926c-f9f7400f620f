"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-04 15:11:29
LastEditors: zhai
LastEditTime: 2025-01-20 21:34:50
"""

from fastapi import APIRouter

from core.dependency import DependAuth
from .auth import router_auth
from .route import router_route
from .system_manage import router_system_manage
from .file.views import router as file_router
from .operations.views import router as operations_router
from .mqttmsg.views import router as mqttmsg_router
from .telemetry.views import router as telemetry_router
from .task.views import router as task_router
from .dict.views import dict_cate_router, dict_item_router
from .tvs.record.views import router as tvs_router

# from .dummy.views import dummy_router, employee_router, team_router, department_router
# from .fishery.customer.views import router as customer_router
# from .fishery.operator.views import router as operator_router
# from .fishery.organization.views import router as organization_router
# from .fishery.tags.views import router as tags_router
# from .fishery.firmware.views import router as firmware_router
# from .fishery.device.views import router as device_router
# from .fishery.updater.views import router as updater_router
# from .fishery.fault.views import router as fault_router
# from .fishery.offline.views import router as offline_router
# from .fishery.feeding.views import router as feeding_router
# from .fishery.feedalias.views import router as feedalias_router
# from .fishery.calibration.views import router as calibration_router

from .daq.terminal.views import router as terminal_router
from .daq.blocks.views import router as blocks_router
from .daq.record.views import router as record_router
from .daq.schemas.views import router as schemas_router

v1_router = APIRouter()

v1_router.include_router(router_auth, prefix="/auth", tags=["权限认证"])
v1_router.include_router(router_route, prefix="/route", tags=["路由管理"])
v1_router.include_router(router_system_manage, prefix="/system-manage", tags=["系统管理"])

v1_router.include_router(dict_cate_router)
v1_router.include_router(dict_item_router)

v1_router.include_router(operations_router, dependencies=[DependAuth])


# # 测试Demo
# v1_router.include_router(dummy_router)
# v1_router.include_router(employee_router)
# v1_router.include_router(team_router)
# v1_router.include_router(department_router, dependencies=[DependAuth])

# # fishery
# v1_router.include_router(customer_router, dependencies=[DependAuth])
# v1_router.include_router(operator_router, dependencies=[DependAuth])
# v1_router.include_router(organization_router, dependencies=[DependAuth])
# v1_router.include_router(tags_router, dependencies=[DependAuth])
# v1_router.include_router(device_router, dependencies=[DependAuth])
# v1_router.include_router(firmware_router, dependencies=[DependAuth])
# v1_router.include_router(updater_router, dependencies=[DependAuth])
# v1_router.include_router(fault_router, dependencies=[DependAuth])
# v1_router.include_router(offline_router, dependencies=[DependAuth])
# v1_router.include_router(feeding_router, dependencies=[DependAuth])
# v1_router.include_router(calibration_router, dependencies=[DependAuth])
# v1_router.include_router(feedalias_router, dependencies=[DependAuth])

# DAQ
v1_router.include_router(terminal_router, dependencies=[DependAuth])
v1_router.include_router(blocks_router, dependencies=[DependAuth])
v1_router.include_router(schemas_router, dependencies=[DependAuth], tags=["编排"])
v1_router.include_router(record_router, dependencies=[DependAuth])

# Task
v1_router.include_router(task_router, prefix="/task", tags=["定时任务"])
v1_router.include_router(file_router, prefix="/file", dependencies=[DependAuth], tags=["文件下载"])
v1_router.include_router(telemetry_router, prefix="/telemetry", dependencies=[DependAuth], tags=["遥测"])
v1_router.include_router(mqttmsg_router, dependencies=[DependAuth])
v1_router.include_router(tvs_router, prefix="/tvs", tags=["TVS"], dependencies=[DependAuth])


{"version": 3, "sources": ["../../.pnpm/ace-builds@1.43.2/node_modules/ace-builds/src-noconflict/ext-language_tools.js"], "sourcesContent": ["ace.define(\"ace/snippets\",[\"require\",\"exports\",\"module\",\"ace/lib/dom\",\"ace/lib/oop\",\"ace/lib/event_emitter\",\"ace/lib/lang\",\"ace/range\",\"ace/range_list\",\"ace/keyboard/hash_handler\",\"ace/tokenizer\",\"ace/clipboard\",\"ace/editor\"], function(require, exports, module){\"use strict\";\nvar dom = require(\"./lib/dom\");\nvar oop = require(\"./lib/oop\");\nvar EventEmitter = require(\"./lib/event_emitter\").EventEmitter;\nvar lang = require(\"./lib/lang\");\nvar Range = require(\"./range\").Range;\nvar RangeList = require(\"./range_list\").RangeList;\nvar HashHandler = require(\"./keyboard/hash_handler\").HashHandler;\nvar Tokenizer = require(\"./tokenizer\").Tokenizer;\nvar clipboard = require(\"./clipboard\");\nvar VARIABLES = {\n    CURRENT_WORD: function (editor) {\n        return editor.session.getTextRange(editor.session.getWordRange());\n    },\n    SELECTION: function (editor, name, indentation) {\n        var text = editor.session.getTextRange();\n        if (indentation)\n            return text.replace(/\\n\\r?([ \\t]*\\S)/g, \"\\n\" + indentation + \"$1\");\n        return text;\n    },\n    CURRENT_LINE: function (editor) {\n        return editor.session.getLine(editor.getCursorPosition().row);\n    },\n    PREV_LINE: function (editor) {\n        return editor.session.getLine(editor.getCursorPosition().row - 1);\n    },\n    LINE_INDEX: function (editor) {\n        return editor.getCursorPosition().row;\n    },\n    LINE_NUMBER: function (editor) {\n        return editor.getCursorPosition().row + 1;\n    },\n    SOFT_TABS: function (editor) {\n        return editor.session.getUseSoftTabs() ? \"YES\" : \"NO\";\n    },\n    TAB_SIZE: function (editor) {\n        return editor.session.getTabSize();\n    },\n    CLIPBOARD: function (editor) {\n        return clipboard.getText && clipboard.getText();\n    },\n    FILENAME: function (editor) {\n        return /[^/\\\\]*$/.exec(this.FILEPATH(editor))[0];\n    },\n    FILENAME_BASE: function (editor) {\n        return /[^/\\\\]*$/.exec(this.FILEPATH(editor))[0].replace(/\\.[^.]*$/, \"\");\n    },\n    DIRECTORY: function (editor) {\n        return this.FILEPATH(editor).replace(/[^/\\\\]*$/, \"\");\n    },\n    FILEPATH: function (editor) { return \"/not implemented.txt\"; },\n    WORKSPACE_NAME: function () { return \"Unknown\"; },\n    FULLNAME: function () { return \"Unknown\"; },\n    BLOCK_COMMENT_START: function (editor) {\n        var mode = editor.session.$mode || {};\n        return mode.blockComment && mode.blockComment.start || \"\";\n    },\n    BLOCK_COMMENT_END: function (editor) {\n        var mode = editor.session.$mode || {};\n        return mode.blockComment && mode.blockComment.end || \"\";\n    },\n    LINE_COMMENT: function (editor) {\n        var mode = editor.session.$mode || {};\n        return mode.lineCommentStart || \"\";\n    },\n    CURRENT_YEAR: date.bind(null, { year: \"numeric\" }),\n    CURRENT_YEAR_SHORT: date.bind(null, { year: \"2-digit\" }),\n    CURRENT_MONTH: date.bind(null, { month: \"numeric\" }),\n    CURRENT_MONTH_NAME: date.bind(null, { month: \"long\" }),\n    CURRENT_MONTH_NAME_SHORT: date.bind(null, { month: \"short\" }),\n    CURRENT_DATE: date.bind(null, { day: \"2-digit\" }),\n    CURRENT_DAY_NAME: date.bind(null, { weekday: \"long\" }),\n    CURRENT_DAY_NAME_SHORT: date.bind(null, { weekday: \"short\" }),\n    CURRENT_HOUR: date.bind(null, { hour: \"2-digit\", hour12: false }),\n    CURRENT_MINUTE: date.bind(null, { minute: \"2-digit\" }),\n    CURRENT_SECOND: date.bind(null, { second: \"2-digit\" })\n};\nVARIABLES.SELECTED_TEXT = VARIABLES.SELECTION;\nfunction date(dateFormat) {\n    var str = new Date().toLocaleString(\"en-us\", dateFormat);\n    return str.length == 1 ? \"0\" + str : str;\n}\nvar SnippetManager = /** @class */ (function () {\n    function SnippetManager() {\n        this.snippetMap = {};\n        this.snippetNameMap = {};\n        this.variables = VARIABLES;\n    }\n    SnippetManager.prototype.getTokenizer = function () {\n        return SnippetManager[\"$tokenizer\"] || this.createTokenizer();\n    };\n    SnippetManager.prototype.createTokenizer = function () {\n        function TabstopToken(str) {\n            str = str.substr(1);\n            if (/^\\d+$/.test(str))\n                return [{ tabstopId: parseInt(str, 10) }];\n            return [{ text: str }];\n        }\n        function escape(ch) {\n            return \"(?:[^\\\\\\\\\" + ch + \"]|\\\\\\\\.)\";\n        }\n        var formatMatcher = {\n            regex: \"/(\" + escape(\"/\") + \"+)/\",\n            onMatch: function (val, state, stack) {\n                var ts = stack[0];\n                ts.fmtString = true;\n                ts.guard = val.slice(1, -1);\n                ts.flag = \"\";\n                return \"\";\n            },\n            next: \"formatString\"\n        };\n        SnippetManager[\"$tokenizer\"] = new Tokenizer({\n            start: [\n                { regex: /\\\\./, onMatch: function (val, state, stack) {\n                        var ch = val[1];\n                        if (ch == \"}\" && stack.length) {\n                            val = ch;\n                        }\n                        else if (\"`$\\\\\".indexOf(ch) != -1) {\n                            val = ch;\n                        }\n                        return [val];\n                    } },\n                { regex: /}/, onMatch: function (val, state, stack) {\n                        return [stack.length ? stack.shift() : val];\n                    } },\n                { regex: /\\$(?:\\d+|\\w+)/, onMatch: TabstopToken },\n                { regex: /\\$\\{[\\dA-Z_a-z]+/, onMatch: function (str, state, stack) {\n                        var t = TabstopToken(str.substr(1));\n                        stack.unshift(t[0]);\n                        return t;\n                    }, next: \"snippetVar\" },\n                { regex: /\\n/, token: \"newline\", merge: false }\n            ],\n            snippetVar: [\n                { regex: \"\\\\|\" + escape(\"\\\\|\") + \"*\\\\|\", onMatch: function (val, state, stack) {\n                        var choices = val.slice(1, -1).replace(/\\\\[,|\\\\]|,/g, function (operator) {\n                            return operator.length == 2 ? operator[1] : \"\\x00\";\n                        }).split(\"\\x00\").map(function (value) {\n                            return { value: value };\n                        });\n                        stack[0].choices = choices;\n                        return [choices[0]];\n                    }, next: \"start\" },\n                formatMatcher,\n                { regex: \"([^:}\\\\\\\\]|\\\\\\\\.)*:?\", token: \"\", next: \"start\" }\n            ],\n            formatString: [\n                { regex: /:/, onMatch: function (val, state, stack) {\n                        if (stack.length && stack[0].expectElse) {\n                            stack[0].expectElse = false;\n                            stack[0].ifEnd = { elseEnd: stack[0] };\n                            return [stack[0].ifEnd];\n                        }\n                        return \":\";\n                    } },\n                { regex: /\\\\./, onMatch: function (val, state, stack) {\n                        var ch = val[1];\n                        if (ch == \"}\" && stack.length)\n                            val = ch;\n                        else if (\"`$\\\\\".indexOf(ch) != -1)\n                            val = ch;\n                        else if (ch == \"n\")\n                            val = \"\\n\";\n                        else if (ch == \"t\")\n                            val = \"\\t\";\n                        else if (\"ulULE\".indexOf(ch) != -1)\n                            val = { changeCase: ch, local: ch > \"a\" };\n                        return [val];\n                    } },\n                { regex: \"/\\\\w*}\", onMatch: function (val, state, stack) {\n                        var next = stack.shift();\n                        if (next)\n                            next.flag = val.slice(1, -1);\n                        this.next = next && next.tabstopId ? \"start\" : \"\";\n                        return [next || val];\n                    }, next: \"start\" },\n                { regex: /\\$(?:\\d+|\\w+)/, onMatch: function (val, state, stack) {\n                        return [{ text: val.slice(1) }];\n                    } },\n                { regex: /\\${\\w+/, onMatch: function (val, state, stack) {\n                        var token = { text: val.slice(2) };\n                        stack.unshift(token);\n                        return [token];\n                    }, next: \"formatStringVar\" },\n                { regex: /\\n/, token: \"newline\", merge: false },\n                { regex: /}/, onMatch: function (val, state, stack) {\n                        var next = stack.shift();\n                        this.next = next && next.tabstopId ? \"start\" : \"\";\n                        return [next || val];\n                    }, next: \"start\" }\n            ],\n            formatStringVar: [\n                { regex: /:\\/\\w+}/, onMatch: function (val, state, stack) {\n                        var ts = stack[0];\n                        ts.formatFunction = val.slice(2, -1);\n                        return [stack.shift()];\n                    }, next: \"formatString\" },\n                formatMatcher,\n                { regex: /:[\\?\\-+]?/, onMatch: function (val, state, stack) {\n                        if (val[1] == \"+\")\n                            stack[0].ifEnd = stack[0];\n                        if (val[1] == \"?\")\n                            stack[0].expectElse = true;\n                    }, next: \"formatString\" },\n                { regex: \"([^:}\\\\\\\\]|\\\\\\\\.)*:?\", token: \"\", next: \"formatString\" }\n            ]\n        });\n        return SnippetManager[\"$tokenizer\"];\n    };\n    SnippetManager.prototype.tokenizeTmSnippet = function (str, startState) {\n        return this.getTokenizer().getLineTokens(str, startState).tokens.map(function (x) {\n            return x.value || x;\n        });\n    };\n    SnippetManager.prototype.getVariableValue = function (editor, name, indentation) {\n        if (/^\\d+$/.test(name))\n            return (this.variables.__ || {})[name] || \"\";\n        if (/^[A-Z]\\d+$/.test(name))\n            return (this.variables[name[0] + \"__\"] || {})[name.substr(1)] || \"\";\n        name = name.replace(/^TM_/, \"\");\n        if (!this.variables.hasOwnProperty(name))\n            return \"\";\n        var value = this.variables[name];\n        if (typeof value == \"function\")\n            value = this.variables[name](editor, name, indentation);\n        return value == null ? \"\" : value;\n    };\n    SnippetManager.prototype.tmStrFormat = function (str, ch, editor) {\n        if (!ch.fmt)\n            return str;\n        var flag = ch.flag || \"\";\n        var re = ch.guard;\n        re = new RegExp(re, flag.replace(/[^gim]/g, \"\"));\n        var fmtTokens = typeof ch.fmt == \"string\" ? this.tokenizeTmSnippet(ch.fmt, \"formatString\") : ch.fmt;\n        var _self = this;\n        var formatted = str.replace(re, function () {\n            var oldArgs = _self.variables.__;\n            _self.variables.__ = [].slice.call(arguments);\n            var fmtParts = _self.resolveVariables(fmtTokens, editor);\n            var gChangeCase = \"E\";\n            for (var i = 0; i < fmtParts.length; i++) {\n                var ch = fmtParts[i];\n                if (typeof ch == \"object\") {\n                    fmtParts[i] = \"\";\n                    if (ch.changeCase && ch.local) {\n                        var next = fmtParts[i + 1];\n                        if (next && typeof next == \"string\") {\n                            if (ch.changeCase == \"u\")\n                                fmtParts[i] = next[0].toUpperCase();\n                            else\n                                fmtParts[i] = next[0].toLowerCase();\n                            fmtParts[i + 1] = next.substr(1);\n                        }\n                    }\n                    else if (ch.changeCase) {\n                        gChangeCase = ch.changeCase;\n                    }\n                }\n                else if (gChangeCase == \"U\") {\n                    fmtParts[i] = ch.toUpperCase();\n                }\n                else if (gChangeCase == \"L\") {\n                    fmtParts[i] = ch.toLowerCase();\n                }\n            }\n            _self.variables.__ = oldArgs;\n            return fmtParts.join(\"\");\n        });\n        return formatted;\n    };\n    SnippetManager.prototype.tmFormatFunction = function (str, ch, editor) {\n        if (ch.formatFunction == \"upcase\")\n            return str.toUpperCase();\n        if (ch.formatFunction == \"downcase\")\n            return str.toLowerCase();\n        return str;\n    };\n    SnippetManager.prototype.resolveVariables = function (snippet, editor) {\n        var result = [];\n        var indentation = \"\";\n        var afterNewLine = true;\n        for (var i = 0; i < snippet.length; i++) {\n            var ch = snippet[i];\n            if (typeof ch == \"string\") {\n                result.push(ch);\n                if (ch == \"\\n\") {\n                    afterNewLine = true;\n                    indentation = \"\";\n                }\n                else if (afterNewLine) {\n                    indentation = /^\\t*/.exec(ch)[0];\n                    afterNewLine = /\\S/.test(ch);\n                }\n                continue;\n            }\n            if (!ch)\n                continue;\n            afterNewLine = false;\n            if (ch.fmtString) {\n                var j = snippet.indexOf(ch, i + 1);\n                if (j == -1)\n                    j = snippet.length;\n                ch.fmt = snippet.slice(i + 1, j);\n                i = j;\n            }\n            if (ch.text) {\n                var value = this.getVariableValue(editor, ch.text, indentation) + \"\";\n                if (ch.fmtString)\n                    value = this.tmStrFormat(value, ch, editor);\n                if (ch.formatFunction)\n                    value = this.tmFormatFunction(value, ch, editor);\n                if (value && !ch.ifEnd) {\n                    result.push(value);\n                    gotoNext(ch);\n                }\n                else if (!value && ch.ifEnd) {\n                    gotoNext(ch.ifEnd);\n                }\n            }\n            else if (ch.elseEnd) {\n                gotoNext(ch.elseEnd);\n            }\n            else if (ch.tabstopId != null) {\n                result.push(ch);\n            }\n            else if (ch.changeCase != null) {\n                result.push(ch);\n            }\n        }\n        function gotoNext(ch) {\n            var i1 = snippet.indexOf(ch, i + 1);\n            if (i1 != -1)\n                i = i1;\n        }\n        return result;\n    };\n    SnippetManager.prototype.getDisplayTextForSnippet = function (editor, snippetText) {\n        var processedSnippet = processSnippetText.call(this, editor, snippetText);\n        return processedSnippet.text;\n    };\n    SnippetManager.prototype.insertSnippetForSelection = function (editor, snippetText, options) {\n        if (options === void 0) { options = {}; }\n        var processedSnippet = processSnippetText.call(this, editor, snippetText, options);\n        var range = editor.getSelectionRange();\n        var end = editor.session.replace(range, processedSnippet.text);\n        var tabstopManager = new TabstopManager(editor);\n        var selectionId = editor.inVirtualSelectionMode && editor.selection.index;\n        tabstopManager.addTabstops(processedSnippet.tabstops, range.start, end, selectionId);\n    };\n    SnippetManager.prototype.insertSnippet = function (editor, snippetText, options) {\n        if (options === void 0) { options = {}; }\n        var self = this;\n        if (editor.inVirtualSelectionMode)\n            return self.insertSnippetForSelection(editor, snippetText, options);\n        editor.forEachSelection(function () {\n            self.insertSnippetForSelection(editor, snippetText, options);\n        }, null, { keepOrder: true });\n        if (editor.tabstopManager)\n            editor.tabstopManager.tabNext();\n    };\n    SnippetManager.prototype.$getScope = function (editor) {\n        var scope = editor.session.$mode.$id || \"\";\n        scope = scope.split(\"/\").pop();\n        if (scope === \"html\" || scope === \"php\") {\n            if (scope === \"php\" && !editor.session.$mode.inlinePhp)\n                scope = \"html\";\n            var c = editor.getCursorPosition();\n            var state = editor.session.getState(c.row);\n            if (typeof state === \"object\") {\n                state = state[0];\n            }\n            if (state.substring) {\n                if (state.substring(0, 3) == \"js-\")\n                    scope = \"javascript\";\n                else if (state.substring(0, 4) == \"css-\")\n                    scope = \"css\";\n                else if (state.substring(0, 4) == \"php-\")\n                    scope = \"php\";\n            }\n        }\n        return scope;\n    };\n    SnippetManager.prototype.getActiveScopes = function (editor) {\n        var scope = this.$getScope(editor);\n        var scopes = [scope];\n        var snippetMap = this.snippetMap;\n        if (snippetMap[scope] && snippetMap[scope].includeScopes) {\n            scopes.push.apply(scopes, snippetMap[scope].includeScopes);\n        }\n        scopes.push(\"_\");\n        return scopes;\n    };\n    SnippetManager.prototype.expandWithTab = function (editor, options) {\n        var self = this;\n        var result = editor.forEachSelection(function () {\n            return self.expandSnippetForSelection(editor, options);\n        }, null, { keepOrder: true });\n        if (result && editor.tabstopManager)\n            editor.tabstopManager.tabNext();\n        return result;\n    };\n    SnippetManager.prototype.expandSnippetForSelection = function (editor, options) {\n        var cursor = editor.getCursorPosition();\n        var line = editor.session.getLine(cursor.row);\n        var before = line.substring(0, cursor.column);\n        var after = line.substr(cursor.column);\n        var snippetMap = this.snippetMap;\n        var snippet;\n        this.getActiveScopes(editor).some(function (scope) {\n            var snippets = snippetMap[scope];\n            if (snippets)\n                snippet = this.findMatchingSnippet(snippets, before, after);\n            return !!snippet;\n        }, this);\n        if (!snippet)\n            return false;\n        if (options && options.dryRun)\n            return true;\n        editor.session.doc.removeInLine(cursor.row, cursor.column - snippet.replaceBefore.length, cursor.column + snippet.replaceAfter.length);\n        this.variables.M__ = snippet.matchBefore;\n        this.variables.T__ = snippet.matchAfter;\n        this.insertSnippetForSelection(editor, snippet.content);\n        this.variables.M__ = this.variables.T__ = null;\n        return true;\n    };\n    SnippetManager.prototype.findMatchingSnippet = function (snippetList, before, after) {\n        for (var i = snippetList.length; i--;) {\n            var s = snippetList[i];\n            if (s.startRe && !s.startRe.test(before))\n                continue;\n            if (s.endRe && !s.endRe.test(after))\n                continue;\n            if (!s.startRe && !s.endRe)\n                continue;\n            s.matchBefore = s.startRe ? s.startRe.exec(before) : [\"\"];\n            s.matchAfter = s.endRe ? s.endRe.exec(after) : [\"\"];\n            s.replaceBefore = s.triggerRe ? s.triggerRe.exec(before)[0] : \"\";\n            s.replaceAfter = s.endTriggerRe ? s.endTriggerRe.exec(after)[0] : \"\";\n            return s;\n        }\n    };\n    SnippetManager.prototype.register = function (snippets, scope) {\n        var snippetMap = this.snippetMap;\n        var snippetNameMap = this.snippetNameMap;\n        var self = this;\n        if (!snippets)\n            snippets = [];\n        function wrapRegexp(src) {\n            if (src && !/^\\^?\\(.*\\)\\$?$|^\\\\b$/.test(src))\n                src = \"(?:\" + src + \")\";\n            return src || \"\";\n        }\n        function guardedRegexp(re, guard, opening) {\n            re = wrapRegexp(re);\n            guard = wrapRegexp(guard);\n            if (opening) {\n                re = guard + re;\n                if (re && re[re.length - 1] != \"$\")\n                    re = re + \"$\";\n            }\n            else {\n                re = re + guard;\n                if (re && re[0] != \"^\")\n                    re = \"^\" + re;\n            }\n            return new RegExp(re);\n        }\n        function addSnippet(s) {\n            if (!s.scope)\n                s.scope = scope || \"_\";\n            scope = s.scope;\n            if (!snippetMap[scope]) {\n                snippetMap[scope] = [];\n                snippetNameMap[scope] = {};\n            }\n            var map = snippetNameMap[scope];\n            if (s.name) {\n                var old = map[s.name];\n                if (old)\n                    self.unregister(old);\n                map[s.name] = s;\n            }\n            snippetMap[scope].push(s);\n            if (s.prefix)\n                s.tabTrigger = s.prefix;\n            if (!s.content && s.body)\n                s.content = Array.isArray(s.body) ? s.body.join(\"\\n\") : s.body;\n            if (s.tabTrigger && !s.trigger) {\n                if (!s.guard && /^\\w/.test(s.tabTrigger))\n                    s.guard = \"\\\\b\";\n                s.trigger = lang.escapeRegExp(s.tabTrigger);\n            }\n            if (!s.trigger && !s.guard && !s.endTrigger && !s.endGuard)\n                return;\n            s.startRe = guardedRegexp(s.trigger, s.guard, true);\n            s.triggerRe = new RegExp(s.trigger);\n            s.endRe = guardedRegexp(s.endTrigger, s.endGuard, true);\n            s.endTriggerRe = new RegExp(s.endTrigger);\n        }\n        if (Array.isArray(snippets)) {\n            snippets.forEach(addSnippet);\n        }\n        else {\n            Object.keys(snippets).forEach(function (key) {\n                addSnippet(snippets[key]);\n            });\n        }\n        this._signal(\"registerSnippets\", { scope: scope });\n    };\n    SnippetManager.prototype.unregister = function (snippets, scope) {\n        var snippetMap = this.snippetMap;\n        var snippetNameMap = this.snippetNameMap;\n        function removeSnippet(s) {\n            var nameMap = snippetNameMap[s.scope || scope];\n            if (nameMap && nameMap[s.name]) {\n                delete nameMap[s.name];\n                var map = snippetMap[s.scope || scope];\n                var i = map && map.indexOf(s);\n                if (i >= 0)\n                    map.splice(i, 1);\n            }\n        }\n        if (snippets.content)\n            removeSnippet(snippets);\n        else if (Array.isArray(snippets))\n            snippets.forEach(removeSnippet);\n    };\n    SnippetManager.prototype.parseSnippetFile = function (str) {\n        str = str.replace(/\\r/g, \"\");\n        var list = [], /**@type{Snippet}*/ snippet = {};\n        var re = /^#.*|^({[\\s\\S]*})\\s*$|^(\\S+) (.*)$|^((?:\\n*\\t.*)+)/gm;\n        var m;\n        while (m = re.exec(str)) {\n            if (m[1]) {\n                try {\n                    snippet = JSON.parse(m[1]);\n                    list.push(snippet);\n                }\n                catch (e) { }\n            }\n            if (m[4]) {\n                snippet.content = m[4].replace(/^\\t/gm, \"\");\n                list.push(snippet);\n                snippet = {};\n            }\n            else {\n                var key = m[2], val = m[3];\n                if (key == \"regex\") {\n                    var guardRe = /\\/((?:[^\\/\\\\]|\\\\.)*)|$/g;\n                    snippet.guard = guardRe.exec(val)[1];\n                    snippet.trigger = guardRe.exec(val)[1];\n                    snippet.endTrigger = guardRe.exec(val)[1];\n                    snippet.endGuard = guardRe.exec(val)[1];\n                }\n                else if (key == \"snippet\") {\n                    snippet.tabTrigger = val.match(/^\\S*/)[0];\n                    if (!snippet.name)\n                        snippet.name = val;\n                }\n                else if (key) {\n                    snippet[key] = val;\n                }\n            }\n        }\n        return list;\n    };\n    SnippetManager.prototype.getSnippetByName = function (name, editor) {\n        var snippetMap = this.snippetNameMap;\n        var snippet;\n        this.getActiveScopes(editor).some(function (scope) {\n            var snippets = snippetMap[scope];\n            if (snippets)\n                snippet = snippets[name];\n            return !!snippet;\n        }, this);\n        return snippet;\n    };\n    return SnippetManager;\n}());\noop.implement(SnippetManager.prototype, EventEmitter);\nvar processSnippetText = function (editor, snippetText, options) {\n    if (options === void 0) { options = {}; }\n    var cursor = editor.getCursorPosition();\n    var line = editor.session.getLine(cursor.row);\n    var tabString = editor.session.getTabString();\n    var indentString = line.match(/^\\s*/)[0];\n    if (cursor.column < indentString.length)\n        indentString = indentString.slice(0, cursor.column);\n    snippetText = snippetText.replace(/\\r/g, \"\");\n    var tokens = this.tokenizeTmSnippet(snippetText);\n    tokens = this.resolveVariables(tokens, editor);\n    tokens = tokens.map(function (x) {\n        if (x == \"\\n\" && !options.excludeExtraIndent)\n            return x + indentString;\n        if (typeof x == \"string\")\n            return x.replace(/\\t/g, tabString);\n        return x;\n    });\n    var tabstops = [];\n    tokens.forEach(function (p, i) {\n        if (typeof p != \"object\")\n            return;\n        var id = p.tabstopId;\n        var ts = tabstops[id];\n        if (!ts) {\n            ts = tabstops[id] = [];\n            ts.index = id;\n            ts.value = \"\";\n            ts.parents = {};\n        }\n        if (ts.indexOf(p) !== -1)\n            return;\n        if (p.choices && !ts.choices)\n            ts.choices = p.choices;\n        ts.push(p);\n        var i1 = tokens.indexOf(p, i + 1);\n        if (i1 === -1)\n            return;\n        var value = tokens.slice(i + 1, i1);\n        var isNested = value.some(function (t) { return typeof t === \"object\"; });\n        if (isNested && !ts.value) {\n            ts.value = value;\n        }\n        else if (value.length && (!ts.value || typeof ts.value !== \"string\")) {\n            ts.value = value.join(\"\");\n        }\n    });\n    tabstops.forEach(function (ts) { ts.length = 0; });\n    var expanding = {};\n    function copyValue(val) {\n        var copy = [];\n        for (var i = 0; i < val.length; i++) {\n            var p = val[i];\n            if (typeof p == \"object\") {\n                if (expanding[p.tabstopId])\n                    continue;\n                var j = val.lastIndexOf(p, i - 1);\n                p = copy[j] || { tabstopId: p.tabstopId };\n            }\n            copy[i] = p;\n        }\n        return copy;\n    }\n    for (var i = 0; i < tokens.length; i++) {\n        var p = tokens[i];\n        if (typeof p != \"object\")\n            continue;\n        var id = p.tabstopId;\n        var ts = tabstops[id];\n        var i1 = tokens.indexOf(p, i + 1);\n        if (expanding[id]) {\n            if (expanding[id] === p) {\n                delete expanding[id];\n                Object.keys(expanding).forEach(function (parentId) {\n                    ts.parents[parentId] = true;\n                });\n            }\n            continue;\n        }\n        expanding[id] = p;\n        var value = ts.value;\n        if (typeof value !== \"string\")\n            value = copyValue(value);\n        else if (p.fmt)\n            value = this.tmStrFormat(value, p, editor);\n        tokens.splice.apply(tokens, [i + 1, Math.max(0, i1 - i)].concat(value, p));\n        if (ts.indexOf(p) === -1)\n            ts.push(p);\n    }\n    var row = 0, column = 0;\n    var text = \"\";\n    tokens.forEach(function (t) {\n        if (typeof t === \"string\") {\n            var lines = t.split(\"\\n\");\n            if (lines.length > 1) {\n                column = lines[lines.length - 1].length;\n                row += lines.length - 1;\n            }\n            else\n                column += t.length;\n            text += t;\n        }\n        else if (t) {\n            if (!t.start)\n                t.start = { row: row, column: column };\n            else\n                t.end = { row: row, column: column };\n        }\n    });\n    return {\n        text: text,\n        tabstops: tabstops,\n        tokens: tokens\n    };\n};\nvar TabstopManager = /** @class */ (function () {\n    function TabstopManager(editor) {\n        this.index = 0;\n        this.ranges = [];\n        this.tabstops = [];\n        if (editor.tabstopManager)\n            return editor.tabstopManager;\n        editor.tabstopManager = this;\n        this.$onChange = this.onChange.bind(this);\n        this.$onChangeSelection = lang.delayedCall(this.onChangeSelection.bind(this)).schedule;\n        this.$onChangeSession = this.onChangeSession.bind(this);\n        this.$onAfterExec = this.onAfterExec.bind(this);\n        this.attach(editor);\n    }\n    TabstopManager.prototype.attach = function (editor) {\n        this.$openTabstops = null;\n        this.selectedTabstop = null;\n        this.editor = editor;\n        this.session = editor.session;\n        this.editor.on(\"change\", this.$onChange);\n        this.editor.on(\"changeSelection\", this.$onChangeSelection);\n        this.editor.on(\"changeSession\", this.$onChangeSession);\n        this.editor.commands.on(\"afterExec\", this.$onAfterExec);\n        this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler);\n    };\n    TabstopManager.prototype.detach = function () {\n        this.tabstops.forEach(this.removeTabstopMarkers, this);\n        this.ranges.length = 0;\n        this.tabstops.length = 0;\n        this.selectedTabstop = null;\n        this.editor.off(\"change\", this.$onChange);\n        this.editor.off(\"changeSelection\", this.$onChangeSelection);\n        this.editor.off(\"changeSession\", this.$onChangeSession);\n        this.editor.commands.off(\"afterExec\", this.$onAfterExec);\n        this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler);\n        this.editor.tabstopManager = null;\n        this.session = null;\n        this.editor = null;\n    };\n    TabstopManager.prototype.onChange = function (delta) {\n        var isRemove = delta.action[0] == \"r\";\n        var selectedTabstop = this.selectedTabstop || {};\n        var parents = selectedTabstop.parents || {};\n        var tabstops = this.tabstops.slice();\n        for (var i = 0; i < tabstops.length; i++) {\n            var ts = tabstops[i];\n            var active = ts == selectedTabstop || parents[ts.index];\n            ts.rangeList.$bias = active ? 0 : 1;\n            if (delta.action == \"remove\" && ts !== selectedTabstop) {\n                var parentActive = ts.parents && ts.parents[selectedTabstop.index];\n                var startIndex = ts.rangeList.pointIndex(delta.start, parentActive);\n                startIndex = startIndex < 0 ? -startIndex - 1 : startIndex + 1;\n                var endIndex = ts.rangeList.pointIndex(delta.end, parentActive);\n                endIndex = endIndex < 0 ? -endIndex - 1 : endIndex - 1;\n                var toRemove = ts.rangeList.ranges.slice(startIndex, endIndex);\n                for (var j = 0; j < toRemove.length; j++)\n                    this.removeRange(toRemove[j]);\n            }\n            ts.rangeList.$onChange(delta);\n        }\n        var session = this.session;\n        if (!this.$inChange && isRemove && session.getLength() == 1 && !session.getValue())\n            this.detach();\n    };\n    TabstopManager.prototype.updateLinkedFields = function () {\n        var ts = this.selectedTabstop;\n        if (!ts || !ts.hasLinkedRanges || !ts.firstNonLinked)\n            return;\n        this.$inChange = true;\n        var session = this.session;\n        var text = session.getTextRange(ts.firstNonLinked);\n        for (var i = 0; i < ts.length; i++) {\n            var range = ts[i];\n            if (!range.linked)\n                continue;\n            var original = range.original;\n            var fmt = exports.snippetManager.tmStrFormat(text, original, this.editor);\n            session.replace(range, fmt);\n        }\n        this.$inChange = false;\n    };\n    TabstopManager.prototype.onAfterExec = function (e) {\n        if (e.command && !e.command.readOnly)\n            this.updateLinkedFields();\n    };\n    TabstopManager.prototype.onChangeSelection = function () {\n        if (!this.editor)\n            return;\n        var lead = this.editor.selection.lead;\n        var anchor = this.editor.selection.anchor;\n        var isEmpty = this.editor.selection.isEmpty();\n        for (var i = 0; i < this.ranges.length; i++) {\n            if (this.ranges[i].linked)\n                continue;\n            var containsLead = this.ranges[i].contains(lead.row, lead.column);\n            var containsAnchor = isEmpty || this.ranges[i].contains(anchor.row, anchor.column);\n            if (containsLead && containsAnchor)\n                return;\n        }\n        this.detach();\n    };\n    TabstopManager.prototype.onChangeSession = function () {\n        this.detach();\n    };\n    TabstopManager.prototype.tabNext = function (dir) {\n        var max = this.tabstops.length;\n        var index = this.index + (dir || 1);\n        index = Math.min(Math.max(index, 1), max);\n        if (index == max)\n            index = 0;\n        this.selectTabstop(index);\n        this.updateTabstopMarkers();\n        if (index === 0) {\n            this.detach();\n        }\n    };\n    TabstopManager.prototype.selectTabstop = function (index) {\n        this.$openTabstops = null;\n        var ts = this.tabstops[this.index];\n        if (ts)\n            this.addTabstopMarkers(ts);\n        this.index = index;\n        ts = this.tabstops[this.index];\n        if (!ts || !ts.length)\n            return;\n        this.selectedTabstop = ts;\n        var range = ts.firstNonLinked || ts;\n        if (ts.choices)\n            range.cursor = range.start;\n        if (!this.editor.inVirtualSelectionMode) {\n            var sel = this.editor.multiSelect;\n            sel.toSingleRange(range);\n            for (var i = 0; i < ts.length; i++) {\n                if (ts.hasLinkedRanges && ts[i].linked)\n                    continue;\n                sel.addRange(ts[i].clone(), true);\n            }\n        }\n        else {\n            this.editor.selection.fromOrientedRange(range);\n        }\n        this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler);\n        if (this.selectedTabstop && this.selectedTabstop.choices)\n            this.editor.execCommand(\"startAutocomplete\", { matches: this.selectedTabstop.choices });\n    };\n    TabstopManager.prototype.addTabstops = function (tabstops, start, end) {\n        var useLink = this.useLink || !this.editor.getOption(\"enableMultiselect\");\n        if (!this.$openTabstops)\n            this.$openTabstops = [];\n        if (!tabstops[0]) {\n            var p = Range.fromPoints(end, end);\n            moveRelative(p.start, start);\n            moveRelative(p.end, start);\n            tabstops[0] = [p];\n            tabstops[0].index = 0;\n        }\n        var i = this.index;\n        var arg = [i + 1, 0];\n        var ranges = this.ranges;\n        var snippetId = this.snippetId = (this.snippetId || 0) + 1;\n        tabstops.forEach(function (ts, index) {\n            var dest = this.$openTabstops[index] || ts;\n            dest.snippetId = snippetId;\n            for (var i = 0; i < ts.length; i++) {\n                var p = ts[i];\n                var range = Range.fromPoints(p.start, p.end || p.start);\n                movePoint(range.start, start);\n                movePoint(range.end, start);\n                range.original = p;\n                range.tabstop = dest;\n                ranges.push(range);\n                if (dest != ts)\n                    dest.unshift(range);\n                else\n                    dest[i] = range;\n                if (p.fmtString || (dest.firstNonLinked && useLink)) {\n                    range.linked = true;\n                    dest.hasLinkedRanges = true;\n                }\n                else if (!dest.firstNonLinked)\n                    dest.firstNonLinked = range;\n            }\n            if (!dest.firstNonLinked)\n                dest.hasLinkedRanges = false;\n            if (dest === ts) {\n                arg.push(dest);\n                this.$openTabstops[index] = dest;\n            }\n            this.addTabstopMarkers(dest);\n            dest.rangeList = dest.rangeList || new RangeList();\n            dest.rangeList.$bias = 0;\n            dest.rangeList.addList(dest);\n        }, this);\n        if (arg.length > 2) {\n            if (this.tabstops.length)\n                arg.push(arg.splice(2, 1)[0]);\n            this.tabstops.splice.apply(this.tabstops, arg);\n        }\n    };\n    TabstopManager.prototype.addTabstopMarkers = function (ts) {\n        var session = this.session;\n        ts.forEach(function (range) {\n            if (!range.markerId)\n                range.markerId = session.addMarker(range, \"ace_snippet-marker\", \"text\");\n        });\n    };\n    TabstopManager.prototype.removeTabstopMarkers = function (ts) {\n        var session = this.session;\n        ts.forEach(function (range) {\n            session.removeMarker(range.markerId);\n            range.markerId = null;\n        });\n    };\n    TabstopManager.prototype.updateTabstopMarkers = function () {\n        if (!this.selectedTabstop)\n            return;\n        var currentSnippetId = this.selectedTabstop.snippetId;\n        if (this.selectedTabstop.index === 0) {\n            currentSnippetId--;\n        }\n        this.tabstops.forEach(function (ts) {\n            if (ts.snippetId === currentSnippetId)\n                this.addTabstopMarkers(ts);\n            else\n                this.removeTabstopMarkers(ts);\n        }, this);\n    };\n    TabstopManager.prototype.removeRange = function (range) {\n        var i = range.tabstop.indexOf(range);\n        if (i != -1)\n            range.tabstop.splice(i, 1);\n        i = this.ranges.indexOf(range);\n        if (i != -1)\n            this.ranges.splice(i, 1);\n        i = range.tabstop.rangeList.ranges.indexOf(range);\n        if (i != -1)\n            range.tabstop.splice(i, 1);\n        this.session.removeMarker(range.markerId);\n        if (!range.tabstop.length) {\n            i = this.tabstops.indexOf(range.tabstop);\n            if (i != -1)\n                this.tabstops.splice(i, 1);\n            if (!this.tabstops.length)\n                this.detach();\n        }\n    };\n    return TabstopManager;\n}());\nTabstopManager.prototype.keyboardHandler = new HashHandler();\nTabstopManager.prototype.keyboardHandler.bindKeys({\n    \"Tab\": function (editor) {\n        if (exports.snippetManager && exports.snippetManager.expandWithTab(editor))\n            return;\n        editor.tabstopManager.tabNext(1);\n        editor.renderer.scrollCursorIntoView();\n    },\n    \"Shift-Tab\": function (editor) {\n        editor.tabstopManager.tabNext(-1);\n        editor.renderer.scrollCursorIntoView();\n    },\n    \"Esc\": function (editor) {\n        editor.tabstopManager.detach();\n    }\n});\nvar movePoint = function (point, diff) {\n    if (point.row == 0)\n        point.column += diff.column;\n    point.row += diff.row;\n};\nvar moveRelative = function (point, start) {\n    if (point.row == start.row)\n        point.column -= start.column;\n    point.row -= start.row;\n};\ndom.importCssString(\"\\n.ace_snippet-marker {\\n    -moz-box-sizing: border-box;\\n    box-sizing: border-box;\\n    background: rgba(194, 193, 208, 0.09);\\n    border: 1px dotted rgba(211, 208, 235, 0.62);\\n    position: absolute;\\n}\", \"snippets.css\", false);\nexports.snippetManager = new SnippetManager();\nvar Editor = require(\"./editor\").Editor;\n(function () {\n    this.insertSnippet = function (content, options) {\n        return exports.snippetManager.insertSnippet(this, content, options);\n    };\n    this.expandSnippet = function (options) {\n        return exports.snippetManager.expandWithTab(this, options);\n    };\n}).call(Editor.prototype);\n\n});\n\nace.define(\"ace/autocomplete/popup\",[\"require\",\"exports\",\"module\",\"ace/virtual_renderer\",\"ace/editor\",\"ace/range\",\"ace/lib/event\",\"ace/lib/lang\",\"ace/lib/dom\",\"ace/config\",\"ace/lib/useragent\"], function(require, exports, module){\"use strict\";\nvar Renderer = require(\"../virtual_renderer\").VirtualRenderer;\nvar Editor = require(\"../editor\").Editor;\nvar Range = require(\"../range\").Range;\nvar event = require(\"../lib/event\");\nvar lang = require(\"../lib/lang\");\nvar dom = require(\"../lib/dom\");\nvar nls = require(\"../config\").nls;\nvar userAgent = require(\"./../lib/useragent\");\nvar getAriaId = function (index) {\n    return \"suggest-aria-id:\".concat(index);\n};\nvar popupAriaRole = userAgent.isSafari ? \"menu\" : \"listbox\";\nvar optionAriaRole = userAgent.isSafari ? \"menuitem\" : \"option\";\nvar ariaActiveState = userAgent.isSafari ? \"aria-current\" : \"aria-selected\";\nvar $singleLineEditor = function (el) {\n    var renderer = new Renderer(el);\n    renderer.$maxLines = 4;\n    var editor = new Editor(renderer);\n    editor.setHighlightActiveLine(false);\n    editor.setShowPrintMargin(false);\n    editor.renderer.setShowGutter(false);\n    editor.renderer.setHighlightGutterLine(false);\n    editor.$mouseHandler.$focusTimeout = 0;\n    editor.$highlightTagPending = true;\n    return editor;\n};\nvar AcePopup = /** @class */ (function () {\n    function AcePopup(parentNode) {\n        var el = dom.createElement(\"div\");\n        var popup = $singleLineEditor(el);\n        if (parentNode) {\n            parentNode.appendChild(el);\n        }\n        el.style.display = \"none\";\n        popup.renderer.content.style.cursor = \"default\";\n        popup.renderer.setStyle(\"ace_autocomplete\");\n        popup.renderer.$textLayer.element.setAttribute(\"role\", popupAriaRole);\n        popup.renderer.$textLayer.element.setAttribute(\"aria-roledescription\", nls(\"autocomplete.popup.aria-roledescription\", \"Autocomplete suggestions\"));\n        popup.renderer.$textLayer.element.setAttribute(\"aria-label\", nls(\"autocomplete.popup.aria-label\", \"Autocomplete suggestions\"));\n        popup.renderer.textarea.setAttribute(\"aria-hidden\", \"true\");\n        popup.setOption(\"displayIndentGuides\", false);\n        popup.setOption(\"dragDelay\", 150);\n        var noop = function () { };\n        popup.focus = noop;\n        popup.$isFocused = true;\n        popup.renderer.$cursorLayer.restartTimer = noop;\n        popup.renderer.$cursorLayer.element.style.opacity = \"0\";\n        popup.renderer.$maxLines = 8;\n        popup.renderer.$keepTextAreaAtCursor = false;\n        popup.setHighlightActiveLine(false);\n        popup.session.highlight(\"\");\n        popup.session.$searchHighlight.clazz = \"ace_highlight-marker\";\n        popup.on(\"mousedown\", function (e) {\n            var pos = e.getDocumentPosition();\n            popup.selection.moveToPosition(pos);\n            selectionMarker.start.row = selectionMarker.end.row = pos.row;\n            e.stop();\n        });\n        var lastMouseEvent;\n        var hoverMarker = new Range(-1, 0, -1, Infinity);\n        var selectionMarker = new Range(-1, 0, -1, Infinity);\n        selectionMarker.id = popup.session.addMarker(selectionMarker, \"ace_active-line\", \"fullLine\");\n        popup.setSelectOnHover = function (val) {\n            if (!val) {\n                hoverMarker.id = popup.session.addMarker(hoverMarker, \"ace_line-hover\", \"fullLine\");\n            }\n            else if (hoverMarker.id) {\n                popup.session.removeMarker(hoverMarker.id);\n                hoverMarker.id = null;\n            }\n        };\n        popup.setSelectOnHover(false);\n        popup.on(\"mousemove\", function (e) {\n            if (!lastMouseEvent) {\n                lastMouseEvent = e;\n                return;\n            }\n            if (lastMouseEvent.x == e.x && lastMouseEvent.y == e.y) {\n                return;\n            }\n            lastMouseEvent = e;\n            lastMouseEvent.scrollTop = popup.renderer.scrollTop;\n            popup.isMouseOver = true;\n            var row = lastMouseEvent.getDocumentPosition().row;\n            if (hoverMarker.start.row != row) {\n                if (!hoverMarker.id)\n                    popup.setRow(row);\n                setHoverMarker(row);\n            }\n        });\n        popup.renderer.on(\"beforeRender\", function () {\n            if (lastMouseEvent && hoverMarker.start.row != -1) {\n                lastMouseEvent.$pos = null;\n                var row = lastMouseEvent.getDocumentPosition().row;\n                if (!hoverMarker.id)\n                    popup.setRow(row);\n                setHoverMarker(row, true);\n            }\n        });\n        popup.renderer.on(\"afterRender\", function () {\n            var t = popup.renderer.$textLayer;\n            for (var row = t.config.firstRow, l = t.config.lastRow; row <= l; row++) {\n                var popupRowElement = /** @type {HTMLElement|null} */ (t.element.childNodes[row - t.config.firstRow]);\n                popupRowElement.setAttribute(\"role\", optionAriaRole);\n                popupRowElement.setAttribute(\"aria-roledescription\", nls(\"autocomplete.popup.item.aria-roledescription\", \"item\"));\n                popupRowElement.setAttribute(\"aria-setsize\", popup.data.length);\n                popupRowElement.setAttribute(\"aria-describedby\", \"doc-tooltip\");\n                popupRowElement.setAttribute(\"aria-posinset\", row + 1);\n                var rowData = popup.getData(row);\n                if (rowData) {\n                    var ariaLabel = \"\".concat(rowData.caption || rowData.value).concat(rowData.meta ? \", \".concat(rowData.meta) : '');\n                    popupRowElement.setAttribute(\"aria-label\", ariaLabel);\n                }\n                var highlightedSpans = popupRowElement.querySelectorAll(\".ace_completion-highlight\");\n                highlightedSpans.forEach(function (span) {\n                    span.setAttribute(\"role\", \"mark\");\n                });\n            }\n        });\n        popup.renderer.on(\"afterRender\", function () {\n            var row = popup.getRow();\n            var t = popup.renderer.$textLayer;\n            var selected = /** @type {HTMLElement|null} */ (t.element.childNodes[row - t.config.firstRow]);\n            var el = document.activeElement; // Active element is textarea of main editor\n            if (selected !== popup.selectedNode && popup.selectedNode) {\n                dom.removeCssClass(popup.selectedNode, \"ace_selected\");\n                popup.selectedNode.removeAttribute(ariaActiveState);\n                popup.selectedNode.removeAttribute(\"id\");\n            }\n            el.removeAttribute(\"aria-activedescendant\");\n            popup.selectedNode = selected;\n            if (selected) {\n                var ariaId = getAriaId(row);\n                dom.addCssClass(selected, \"ace_selected\");\n                selected.id = ariaId;\n                t.element.setAttribute(\"aria-activedescendant\", ariaId);\n                el.setAttribute(\"aria-activedescendant\", ariaId);\n                selected.setAttribute(ariaActiveState, \"true\");\n            }\n        });\n        var hideHoverMarker = function () { setHoverMarker(-1); };\n        var setHoverMarker = function (row, suppressRedraw) {\n            if (row !== hoverMarker.start.row) {\n                hoverMarker.start.row = hoverMarker.end.row = row;\n                if (!suppressRedraw)\n                    popup.session._emit(\"changeBackMarker\");\n                popup._emit(\"changeHoverMarker\");\n            }\n        };\n        popup.getHoveredRow = function () {\n            return hoverMarker.start.row;\n        };\n        event.addListener(popup.container, \"mouseout\", function () {\n            popup.isMouseOver = false;\n            hideHoverMarker();\n        });\n        popup.on(\"hide\", hideHoverMarker);\n        popup.on(\"changeSelection\", hideHoverMarker);\n        popup.session.doc.getLength = function () {\n            return popup.data.length;\n        };\n        popup.session.doc.getLine = function (i) {\n            var data = popup.data[i];\n            if (typeof data == \"string\")\n                return data;\n            return (data && data.value) || \"\";\n        };\n        var bgTokenizer = popup.session.bgTokenizer;\n        bgTokenizer.$tokenizeRow = function (row) {\n            var data = popup.data[row];\n            var tokens = [];\n            if (!data)\n                return tokens;\n            if (typeof data == \"string\")\n                data = { value: data };\n            var caption = data.caption || data.value || data.name;\n            function addToken(value, className) {\n                value && tokens.push({\n                    type: (data.className || \"\") + (className || \"\"),\n                    value: value\n                });\n            }\n            var lower = caption.toLowerCase();\n            var filterText = (popup.filterText || \"\").toLowerCase();\n            var lastIndex = 0;\n            var lastI = 0;\n            for (var i = 0; i <= filterText.length; i++) {\n                if (i != lastI && (data.matchMask & (1 << i) || i == filterText.length)) {\n                    var sub = filterText.slice(lastI, i);\n                    lastI = i;\n                    var index = lower.indexOf(sub, lastIndex);\n                    if (index == -1)\n                        continue;\n                    addToken(caption.slice(lastIndex, index), \"\");\n                    lastIndex = index + sub.length;\n                    addToken(caption.slice(index, lastIndex), \"completion-highlight\");\n                }\n            }\n            addToken(caption.slice(lastIndex, caption.length), \"\");\n            tokens.push({ type: \"completion-spacer\", value: \" \" });\n            if (data.meta)\n                tokens.push({ type: \"completion-meta\", value: data.meta });\n            if (data.message)\n                tokens.push({ type: \"completion-message\", value: data.message });\n            return tokens;\n        };\n        bgTokenizer.$updateOnChange = noop;\n        bgTokenizer.start = noop;\n        popup.session.$computeWidth = function () {\n            return this.screenWidth = 0;\n        };\n        popup.isOpen = false;\n        popup.isTopdown = false;\n        popup.autoSelect = true;\n        popup.filterText = \"\";\n        popup.isMouseOver = false;\n        popup.data = [];\n        popup.setData = function (list, filterText) {\n            popup.filterText = filterText || \"\";\n            popup.setValue(lang.stringRepeat(\"\\n\", list.length), -1);\n            popup.data = list || [];\n            popup.setRow(0);\n        };\n        popup.getData = function (row) {\n            return popup.data[row];\n        };\n        popup.getRow = function () {\n            return selectionMarker.start.row;\n        };\n        popup.setRow = function (line) {\n            line = Math.max(this.autoSelect ? 0 : -1, Math.min(this.data.length - 1, line));\n            if (selectionMarker.start.row != line) {\n                popup.selection.clearSelection();\n                selectionMarker.start.row = selectionMarker.end.row = line || 0;\n                popup.session._emit(\"changeBackMarker\");\n                popup.moveCursorTo(line || 0, 0);\n                if (popup.isOpen)\n                    popup._signal(\"select\");\n            }\n        };\n        popup.on(\"changeSelection\", function () {\n            if (popup.isOpen)\n                popup.setRow(popup.selection.lead.row);\n            popup.renderer.scrollCursorIntoView();\n        });\n        popup.hide = function () {\n            this.container.style.display = \"none\";\n            popup.anchorPos = null;\n            popup.anchor = null;\n            if (popup.isOpen) {\n                popup.isOpen = false;\n                this._signal(\"hide\");\n            }\n        };\n        popup.tryShow = function (pos, lineHeight, anchor, forceShow) {\n            if (!forceShow && popup.isOpen && popup.anchorPos && popup.anchor &&\n                popup.anchorPos.top === pos.top && popup.anchorPos.left === pos.left &&\n                popup.anchor === anchor) {\n                return true;\n            }\n            var el = this.container;\n            var scrollBarSize = this.renderer.scrollBar.width || 10;\n            var screenHeight = window.innerHeight - scrollBarSize;\n            var screenWidth = window.innerWidth - scrollBarSize;\n            var renderer = this.renderer;\n            var maxH = renderer.$maxLines * lineHeight * 1.4;\n            var dims = { top: 0, bottom: 0, left: 0 };\n            var spaceBelow = screenHeight - pos.top - 3 * this.$borderSize - lineHeight;\n            var spaceAbove = pos.top - 3 * this.$borderSize;\n            if (!anchor) {\n                if (spaceAbove <= spaceBelow || spaceBelow >= maxH) {\n                    anchor = \"bottom\";\n                }\n                else {\n                    anchor = \"top\";\n                }\n            }\n            if (anchor === \"top\") {\n                dims.bottom = pos.top - this.$borderSize;\n                dims.top = dims.bottom - maxH;\n            }\n            else if (anchor === \"bottom\") {\n                dims.top = pos.top + lineHeight + this.$borderSize;\n                dims.bottom = dims.top + maxH;\n            }\n            var fitsX = dims.top >= 0 && dims.bottom <= screenHeight;\n            if (!forceShow && !fitsX) {\n                return false;\n            }\n            if (!fitsX) {\n                if (anchor === \"top\") {\n                    renderer.$maxPixelHeight = spaceAbove;\n                }\n                else {\n                    renderer.$maxPixelHeight = spaceBelow;\n                }\n            }\n            else {\n                renderer.$maxPixelHeight = null;\n            }\n            if (anchor === \"top\") {\n                el.style.top = \"\";\n                el.style.bottom = (screenHeight + scrollBarSize - dims.bottom) + \"px\";\n                popup.isTopdown = false;\n            }\n            else {\n                el.style.top = dims.top + \"px\";\n                el.style.bottom = \"\";\n                popup.isTopdown = true;\n            }\n            el.style.display = \"\";\n            var left = pos.left;\n            if (left + el.offsetWidth > screenWidth)\n                left = screenWidth - el.offsetWidth;\n            el.style.left = left + \"px\";\n            el.style.right = \"\";\n            if (!popup.isOpen) {\n                popup.isOpen = true;\n                this._signal(\"show\");\n                lastMouseEvent = null;\n            }\n            popup.anchorPos = pos;\n            popup.anchor = anchor;\n            return true;\n        };\n        popup.show = function (pos, lineHeight, topdownOnly) {\n            this.tryShow(pos, lineHeight, topdownOnly ? \"bottom\" : undefined, true);\n        };\n        popup.goTo = function (where) {\n            var row = this.getRow();\n            var max = this.session.getLength() - 1;\n            switch (where) {\n                case \"up\":\n                    row = row <= 0 ? max : row - 1;\n                    break;\n                case \"down\":\n                    row = row >= max ? -1 : row + 1;\n                    break;\n                case \"start\":\n                    row = 0;\n                    break;\n                case \"end\":\n                    row = max;\n                    break;\n            }\n            this.setRow(row);\n        };\n        popup.getTextLeftOffset = function () {\n            return this.$borderSize + this.renderer.$padding + this.$imageSize;\n        };\n        popup.$imageSize = 0;\n        popup.$borderSize = 1;\n        return popup;\n    }\n    return AcePopup;\n}());\ndom.importCssString(\"\\n.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\\n    background-color: #CAD6FA;\\n    z-index: 1;\\n}\\n.ace_dark.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\\n    background-color: #3a674e;\\n}\\n.ace_editor.ace_autocomplete .ace_line-hover {\\n    border: 1px solid #abbffe;\\n    margin-top: -1px;\\n    background: rgba(233,233,253,0.4);\\n    position: absolute;\\n    z-index: 2;\\n}\\n.ace_dark.ace_editor.ace_autocomplete .ace_line-hover {\\n    border: 1px solid rgba(109, 150, 13, 0.8);\\n    background: rgba(58, 103, 78, 0.62);\\n}\\n.ace_completion-meta {\\n    opacity: 0.5;\\n    margin-left: 0.9em;\\n}\\n.ace_completion-message {\\n    margin-left: 0.9em;\\n    color: blue;\\n}\\n.ace_editor.ace_autocomplete .ace_completion-highlight{\\n    color: #2d69c7;\\n}\\n.ace_dark.ace_editor.ace_autocomplete .ace_completion-highlight{\\n    color: #93ca12;\\n}\\n.ace_editor.ace_autocomplete {\\n    width: 300px;\\n    z-index: 200000;\\n    border: 1px lightgray solid;\\n    position: fixed;\\n    box-shadow: 2px 3px 5px rgba(0,0,0,.2);\\n    line-height: 1.4;\\n    background: #fefefe;\\n    color: #111;\\n}\\n.ace_dark.ace_editor.ace_autocomplete {\\n    border: 1px #484747 solid;\\n    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.51);\\n    line-height: 1.4;\\n    background: #25282c;\\n    color: #c1c1c1;\\n}\\n.ace_autocomplete .ace_text-layer  {\\n    width: calc(100% - 8px);\\n}\\n.ace_autocomplete .ace_line {\\n    display: flex;\\n    align-items: center;\\n}\\n.ace_autocomplete .ace_line > * {\\n    min-width: 0;\\n    flex: 0 0 auto;\\n}\\n.ace_autocomplete .ace_line .ace_ {\\n    flex: 0 1 auto;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n}\\n.ace_autocomplete .ace_completion-spacer {\\n    flex: 1;\\n}\\n.ace_autocomplete.ace_loading:after  {\\n    content: \\\"\\\";\\n    position: absolute;\\n    top: 0px;\\n    height: 2px;\\n    width: 8%;\\n    background: blue;\\n    z-index: 100;\\n    animation: ace_progress 3s infinite linear;\\n    animation-delay: 300ms;\\n    transform: translateX(-100%) scaleX(1);\\n}\\n@keyframes ace_progress {\\n    0% { transform: translateX(-100%) scaleX(1) }\\n    50% { transform: translateX(625%) scaleX(2) } \\n    100% { transform: translateX(1500%) scaleX(3) } \\n}\\n@media (prefers-reduced-motion) {\\n    .ace_autocomplete.ace_loading:after {\\n        transform: translateX(625%) scaleX(2);\\n        animation: none;\\n     }\\n}\\n\", \"autocompletion.css\", false);\nexports.AcePopup = AcePopup;\nexports.$singleLineEditor = $singleLineEditor;\nexports.getAriaId = getAriaId;\n\n});\n\nace.define(\"ace/autocomplete/inline_screenreader\",[\"require\",\"exports\",\"module\"], function(require, exports, module){\"use strict\";\nvar AceInlineScreenReader = /** @class */ (function () {\n    function AceInlineScreenReader(editor) {\n        this.editor = editor;\n        this.screenReaderDiv = document.createElement(\"div\");\n        this.screenReaderDiv.classList.add(\"ace_screenreader-only\");\n        this.editor.container.appendChild(this.screenReaderDiv);\n    }\n    AceInlineScreenReader.prototype.setScreenReaderContent = function (content) {\n        if (!this.popup && this.editor.completer && /**@type{import(\"../autocomplete\").Autocomplete}*/ (this.editor.completer).popup) {\n            this.popup = /**@type{import(\"../autocomplete\").Autocomplete}*/ (this.editor.completer).popup;\n            this.popup.renderer.on(\"afterRender\", function () {\n                var row = this.popup.getRow();\n                var t = this.popup.renderer.$textLayer;\n                var selected = t.element.childNodes[row - t.config.firstRow];\n                if (selected) {\n                    var idString = \"doc-tooltip \";\n                    for (var lineIndex = 0; lineIndex < this._lines.length; lineIndex++) {\n                        idString += \"ace-inline-screenreader-line-\".concat(lineIndex, \" \");\n                    }\n                    selected.setAttribute(\"aria-describedby\", idString);\n                }\n            }.bind(this));\n        }\n        while (this.screenReaderDiv.firstChild) {\n            this.screenReaderDiv.removeChild(this.screenReaderDiv.firstChild);\n        }\n        this._lines = content.split(/\\r\\n|\\r|\\n/);\n        var codeElement = this.createCodeBlock();\n        this.screenReaderDiv.appendChild(codeElement);\n    };\n    AceInlineScreenReader.prototype.destroy = function () {\n        this.screenReaderDiv.remove();\n    };\n    AceInlineScreenReader.prototype.createCodeBlock = function () {\n        var container = document.createElement(\"pre\");\n        container.setAttribute(\"id\", \"ace-inline-screenreader\");\n        for (var lineIndex = 0; lineIndex < this._lines.length; lineIndex++) {\n            var codeElement = document.createElement(\"code\");\n            codeElement.setAttribute(\"id\", \"ace-inline-screenreader-line-\".concat(lineIndex));\n            var line = document.createTextNode(this._lines[lineIndex]);\n            codeElement.appendChild(line);\n            container.appendChild(codeElement);\n        }\n        return container;\n    };\n    return AceInlineScreenReader;\n}());\nexports.AceInlineScreenReader = AceInlineScreenReader;\n\n});\n\nace.define(\"ace/autocomplete/inline\",[\"require\",\"exports\",\"module\",\"ace/snippets\",\"ace/autocomplete/inline_screenreader\"], function(require, exports, module){\"use strict\";\nvar snippetManager = require(\"../snippets\").snippetManager;\nvar AceInlineScreenReader = require(\"./inline_screenreader\").AceInlineScreenReader;\nvar AceInline = /** @class */ (function () {\n    function AceInline() {\n        this.editor = null;\n    }\n    AceInline.prototype.show = function (editor, completion, prefix) {\n        prefix = prefix || \"\";\n        if (editor && this.editor && this.editor !== editor) {\n            this.hide();\n            this.editor = null;\n            this.inlineScreenReader = null;\n        }\n        if (!editor || !completion) {\n            return false;\n        }\n        if (!this.inlineScreenReader) {\n            this.inlineScreenReader = new AceInlineScreenReader(editor);\n        }\n        var displayText = completion.snippet ? snippetManager.getDisplayTextForSnippet(editor, completion.snippet) : completion.value;\n        if (completion.hideInlinePreview || !displayText || !displayText.startsWith(prefix)) {\n            return false;\n        }\n        this.editor = editor;\n        this.inlineScreenReader.setScreenReaderContent(displayText);\n        displayText = displayText.slice(prefix.length);\n        if (displayText === \"\") {\n            editor.removeGhostText();\n        }\n        else {\n            editor.setGhostText(displayText);\n        }\n        return true;\n    };\n    AceInline.prototype.isOpen = function () {\n        if (!this.editor) {\n            return false;\n        }\n        return !!this.editor.renderer.$ghostText;\n    };\n    AceInline.prototype.hide = function () {\n        if (!this.editor) {\n            return false;\n        }\n        this.editor.removeGhostText();\n        return true;\n    };\n    AceInline.prototype.destroy = function () {\n        this.hide();\n        this.editor = null;\n        if (this.inlineScreenReader) {\n            this.inlineScreenReader.destroy();\n            this.inlineScreenReader = null;\n        }\n    };\n    return AceInline;\n}());\nexports.AceInline = AceInline;\n\n});\n\nace.define(\"ace/autocomplete/util\",[\"require\",\"exports\",\"module\"], function(require, exports, module){\"use strict\";\nexports.parForEach = function (array, fn, callback) {\n    var completed = 0;\n    var arLength = array.length;\n    if (arLength === 0)\n        callback();\n    for (var i = 0; i < arLength; i++) {\n        fn(array[i], function (result, err) {\n            completed++;\n            if (completed === arLength)\n                callback(result, err);\n        });\n    }\n};\nvar ID_REGEX = /[a-zA-Z_0-9\\$\\-\\u00A2-\\u2000\\u2070-\\uFFFF]/;\nexports.retrievePrecedingIdentifier = function (text, pos, regex) {\n    regex = regex || ID_REGEX;\n    var buf = [];\n    for (var i = pos - 1; i >= 0; i--) {\n        if (regex.test(text[i]))\n            buf.push(text[i]);\n        else\n            break;\n    }\n    return buf.reverse().join(\"\");\n};\nexports.retrieveFollowingIdentifier = function (text, pos, regex) {\n    regex = regex || ID_REGEX;\n    var buf = [];\n    for (var i = pos; i < text.length; i++) {\n        if (regex.test(text[i]))\n            buf.push(text[i]);\n        else\n            break;\n    }\n    return buf;\n};\nexports.getCompletionPrefix = function (editor) {\n    var pos = editor.getCursorPosition();\n    var line = editor.session.getLine(pos.row);\n    var prefix;\n    editor.completers.forEach(function (completer) {\n        if (completer.identifierRegexps) {\n            completer.identifierRegexps.forEach(function (identifierRegex) {\n                if (!prefix && identifierRegex)\n                    prefix = this.retrievePrecedingIdentifier(line, pos.column, identifierRegex);\n            }.bind(this));\n        }\n    }.bind(this));\n    return prefix || this.retrievePrecedingIdentifier(line, pos.column);\n};\nexports.triggerAutocomplete = function (editor, previousChar) {\n    var previousChar = previousChar == null\n        ? editor.session.getPrecedingCharacter()\n        : previousChar;\n    return editor.completers.some(function (completer) {\n        if (completer.triggerCharacters && Array.isArray(completer.triggerCharacters)) {\n            return completer.triggerCharacters.includes(previousChar);\n        }\n    });\n};\n\n});\n\nace.define(\"ace/autocomplete\",[\"require\",\"exports\",\"module\",\"ace/keyboard/hash_handler\",\"ace/autocomplete/popup\",\"ace/autocomplete/inline\",\"ace/autocomplete/popup\",\"ace/autocomplete/util\",\"ace/lib/lang\",\"ace/lib/dom\",\"ace/snippets\",\"ace/config\",\"ace/lib/event\",\"ace/lib/scroll\"], function(require, exports, module){\"use strict\";\nvar HashHandler = require(\"./keyboard/hash_handler\").HashHandler;\nvar AcePopup = require(\"./autocomplete/popup\").AcePopup;\nvar AceInline = require(\"./autocomplete/inline\").AceInline;\nvar getAriaId = require(\"./autocomplete/popup\").getAriaId;\nvar util = require(\"./autocomplete/util\");\nvar lang = require(\"./lib/lang\");\nvar dom = require(\"./lib/dom\");\nvar snippetManager = require(\"./snippets\").snippetManager;\nvar config = require(\"./config\");\nvar event = require(\"./lib/event\");\nvar preventParentScroll = require(\"./lib/scroll\").preventParentScroll;\nvar destroyCompleter = function (e, editor) {\n    editor.completer && editor.completer.destroy();\n};\nvar Autocomplete = /** @class */ (function () {\n    function Autocomplete() {\n        this.autoInsert = false;\n        this.autoSelect = true;\n        this.autoShown = false;\n        this.exactMatch = false;\n        this.inlineEnabled = false;\n        this.keyboardHandler = new HashHandler();\n        this.keyboardHandler.bindKeys(this.commands);\n        this.parentNode = null;\n        this.setSelectOnHover = false;\n        this.hasSeen = new Set();\n        this.showLoadingState = false;\n        this.stickySelectionDelay = 500;\n        this.blurListener = this.blurListener.bind(this);\n        this.changeListener = this.changeListener.bind(this);\n        this.mousedownListener = this.mousedownListener.bind(this);\n        this.mousewheelListener = this.mousewheelListener.bind(this);\n        this.onLayoutChange = this.onLayoutChange.bind(this);\n        this.changeTimer = lang.delayedCall(function () {\n            this.updateCompletions(true);\n        }.bind(this));\n        this.tooltipTimer = lang.delayedCall(this.updateDocTooltip.bind(this), 50);\n        this.popupTimer = lang.delayedCall(this.$updatePopupPosition.bind(this), 50);\n        this.stickySelectionTimer = lang.delayedCall(function () {\n            this.stickySelection = true;\n        }.bind(this), this.stickySelectionDelay);\n        this.$firstOpenTimer = lang.delayedCall(/**@this{Autocomplete}*/ function () {\n            var initialPosition = this.completionProvider && this.completionProvider.initialPosition;\n            if (this.autoShown || (this.popup && this.popup.isOpen) || !initialPosition || this.editor.completers.length === 0)\n                return;\n            this.completions = new FilteredList(Autocomplete.completionsForLoading);\n            this.openPopup(this.editor, initialPosition.prefix, false);\n            this.popup.renderer.setStyle(\"ace_loading\", true);\n        }.bind(this), this.stickySelectionDelay);\n    }\n    Object.defineProperty(Autocomplete, \"completionsForLoading\", {\n        get: function () {\n            return [{\n                    caption: config.nls(\"autocomplete.loading\", \"Loading...\"),\n                    value: \"\"\n                }];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Autocomplete.prototype.$init = function () {\n        this.popup = new AcePopup(this.parentNode || document.body || document.documentElement);\n        this.popup.on(\"click\", function (e) {\n            this.insertMatch();\n            e.stop();\n        }.bind(this));\n        this.popup.focus = this.editor.focus.bind(this.editor);\n        this.popup.on(\"show\", this.$onPopupShow.bind(this));\n        this.popup.on(\"hide\", this.$onHidePopup.bind(this));\n        this.popup.on(\"select\", this.$onPopupChange.bind(this));\n        event.addListener(this.popup.container, \"mouseout\", this.mouseOutListener.bind(this));\n        this.popup.on(\"changeHoverMarker\", this.tooltipTimer.bind(null, null));\n        this.popup.renderer.on(\"afterRender\", this.$onPopupRender.bind(this));\n        return this.popup;\n    };\n    Autocomplete.prototype.$initInline = function () {\n        if (!this.inlineEnabled || this.inlineRenderer)\n            return;\n        this.inlineRenderer = new AceInline();\n        return this.inlineRenderer;\n    };\n    Autocomplete.prototype.getPopup = function () {\n        return this.popup || this.$init();\n    };\n    Autocomplete.prototype.$onHidePopup = function () {\n        if (this.inlineRenderer) {\n            this.inlineRenderer.hide();\n        }\n        this.hideDocTooltip();\n        this.stickySelectionTimer.cancel();\n        this.popupTimer.cancel();\n        this.stickySelection = false;\n    };\n    Autocomplete.prototype.$seen = function (completion) {\n        if (!this.hasSeen.has(completion) && completion && completion.completer && completion.completer.onSeen && typeof completion.completer.onSeen === \"function\") {\n            completion.completer.onSeen(this.editor, completion);\n            this.hasSeen.add(completion);\n        }\n    };\n    Autocomplete.prototype.$onPopupChange = function (hide) {\n        if (this.inlineRenderer && this.inlineEnabled) {\n            var completion = hide ? null : this.popup.getData(this.popup.getRow());\n            this.$updateGhostText(completion);\n            if (this.popup.isMouseOver && this.setSelectOnHover) {\n                this.tooltipTimer.call(null, null);\n                return;\n            }\n            this.popupTimer.schedule();\n            this.tooltipTimer.schedule();\n        }\n        else {\n            this.popupTimer.call(null, null);\n            this.tooltipTimer.call(null, null);\n        }\n    };\n    Autocomplete.prototype.$updateGhostText = function (completion) {\n        var row = this.base.row;\n        var column = this.base.column;\n        var cursorColumn = this.editor.getCursorPosition().column;\n        var prefix = this.editor.session.getLine(row).slice(column, cursorColumn);\n        if (!this.inlineRenderer.show(this.editor, completion, prefix)) {\n            this.inlineRenderer.hide();\n        }\n        else {\n            this.$seen(completion);\n        }\n    };\n    Autocomplete.prototype.$onPopupRender = function () {\n        var inlineEnabled = this.inlineRenderer && this.inlineEnabled;\n        if (this.completions && this.completions.filtered && this.completions.filtered.length > 0) {\n            for (var i = this.popup.getFirstVisibleRow(); i <= this.popup.getLastVisibleRow(); i++) {\n                var completion = this.popup.getData(i);\n                if (completion && (!inlineEnabled || completion.hideInlinePreview)) {\n                    this.$seen(completion);\n                }\n            }\n        }\n    };\n    Autocomplete.prototype.$onPopupShow = function (hide) {\n        this.$onPopupChange(hide);\n        this.stickySelection = false;\n        if (this.stickySelectionDelay >= 0)\n            this.stickySelectionTimer.schedule(this.stickySelectionDelay);\n    };\n    Autocomplete.prototype.observeLayoutChanges = function () {\n        if (this.$elements || !this.editor)\n            return;\n        window.addEventListener(\"resize\", this.onLayoutChange, { passive: true });\n        window.addEventListener(\"wheel\", this.mousewheelListener);\n        var el = this.editor.container.parentNode;\n        var elements = [];\n        while (el) {\n            elements.push(el);\n            el.addEventListener(\"scroll\", this.onLayoutChange, { passive: true });\n            el = el.parentNode;\n        }\n        this.$elements = elements;\n    };\n    Autocomplete.prototype.unObserveLayoutChanges = function () {\n        var _this = this;\n        window.removeEventListener(\"resize\", this.onLayoutChange, { passive: true });\n        window.removeEventListener(\"wheel\", this.mousewheelListener);\n        this.$elements && this.$elements.forEach(function (el) {\n            el.removeEventListener(\"scroll\", _this.onLayoutChange, { passive: true });\n        });\n        this.$elements = null;\n    };\n    Autocomplete.prototype.onLayoutChange = function () {\n        if (!this.popup.isOpen)\n            return this.unObserveLayoutChanges();\n        this.$updatePopupPosition();\n        this.updateDocTooltip();\n    };\n    Autocomplete.prototype.$updatePopupPosition = function () {\n        var editor = this.editor;\n        var renderer = editor.renderer;\n        var lineHeight = renderer.layerConfig.lineHeight;\n        var pos = renderer.$cursorLayer.getPixelPosition(this.base, true);\n        pos.left -= this.popup.getTextLeftOffset();\n        var rect = editor.container.getBoundingClientRect();\n        pos.top += rect.top - renderer.layerConfig.offset;\n        pos.left += rect.left - editor.renderer.scrollLeft;\n        pos.left += renderer.gutterWidth;\n        var posGhostText = {\n            top: pos.top,\n            left: pos.left\n        };\n        if (renderer.$ghostText && renderer.$ghostTextWidget) {\n            if (this.base.row === renderer.$ghostText.position.row) {\n                posGhostText.top += renderer.$ghostTextWidget.el.offsetHeight;\n            }\n        }\n        var editorContainerBottom = editor.container.getBoundingClientRect().bottom - lineHeight;\n        var lowestPosition = editorContainerBottom < posGhostText.top ?\n            { top: editorContainerBottom, left: posGhostText.left } :\n            posGhostText;\n        if (this.popup.tryShow(lowestPosition, lineHeight, \"bottom\")) {\n            return;\n        }\n        if (this.popup.tryShow(pos, lineHeight, \"top\")) {\n            return;\n        }\n        this.popup.show(pos, lineHeight);\n    };\n    Autocomplete.prototype.openPopup = function (editor, prefix, keepPopupPosition) {\n        this.$firstOpenTimer.cancel();\n        if (!this.popup)\n            this.$init();\n        if (this.inlineEnabled && !this.inlineRenderer)\n            this.$initInline();\n        this.popup.autoSelect = this.autoSelect;\n        this.popup.setSelectOnHover(this.setSelectOnHover);\n        var oldRow = this.popup.getRow();\n        var previousSelectedItem = this.popup.data[oldRow];\n        this.popup.setData(this.completions.filtered, this.completions.filterText);\n        if (this.editor.textInput.setAriaOptions) {\n            this.editor.textInput.setAriaOptions({\n                activeDescendant: getAriaId(this.popup.getRow()),\n                inline: this.inlineEnabled\n            });\n        }\n        editor.keyBinding.addKeyboardHandler(this.keyboardHandler);\n        var newRow;\n        if (this.stickySelection)\n            newRow = this.popup.data.indexOf(previousSelectedItem);\n        if (!newRow || newRow === -1)\n            newRow = 0;\n        this.popup.setRow(this.autoSelect ? newRow : -1);\n        if (newRow === oldRow && previousSelectedItem !== this.completions.filtered[newRow])\n            this.$onPopupChange();\n        var inlineEnabled = this.inlineRenderer && this.inlineEnabled;\n        if (newRow === oldRow && inlineEnabled) {\n            var completion = this.popup.getData(this.popup.getRow());\n            this.$updateGhostText(completion);\n        }\n        if (!keepPopupPosition) {\n            this.popup.setTheme(editor.getTheme());\n            this.popup.setFontSize(editor.getFontSize());\n            this.$updatePopupPosition();\n            if (this.tooltipNode) {\n                this.updateDocTooltip();\n            }\n        }\n        this.changeTimer.cancel();\n        this.observeLayoutChanges();\n    };\n    Autocomplete.prototype.detach = function () {\n        if (this.editor) {\n            this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler);\n            this.editor.off(\"changeSelection\", this.changeListener);\n            this.editor.off(\"blur\", this.blurListener);\n            this.editor.off(\"mousedown\", this.mousedownListener);\n            this.editor.off(\"mousewheel\", this.mousewheelListener);\n        }\n        this.$firstOpenTimer.cancel();\n        this.changeTimer.cancel();\n        this.hideDocTooltip();\n        if (this.completionProvider) {\n            this.completionProvider.detach();\n        }\n        if (this.popup && this.popup.isOpen)\n            this.popup.hide();\n        if (this.popup && this.popup.renderer) {\n            this.popup.renderer.off(\"afterRender\", this.$onPopupRender);\n        }\n        if (this.base)\n            this.base.detach();\n        this.activated = false;\n        this.completionProvider = this.completions = this.base = null;\n        this.unObserveLayoutChanges();\n    };\n    Autocomplete.prototype.changeListener = function (e) {\n        var cursor = this.editor.selection.lead;\n        if (cursor.row != this.base.row || cursor.column < this.base.column) {\n            this.detach();\n        }\n        if (this.activated)\n            this.changeTimer.schedule();\n        else\n            this.detach();\n    };\n    Autocomplete.prototype.blurListener = function (e) {\n        var el = document.activeElement;\n        var text = this.editor.textInput.getElement();\n        var fromTooltip = e.relatedTarget && this.tooltipNode && this.tooltipNode.contains(e.relatedTarget);\n        var container = this.popup && this.popup.container;\n        if (el != text && el.parentNode != container && !fromTooltip\n            && el != this.tooltipNode && e.relatedTarget != text) {\n            this.detach();\n        }\n    };\n    Autocomplete.prototype.mousedownListener = function (e) {\n        this.detach();\n    };\n    Autocomplete.prototype.mousewheelListener = function (e) {\n        if (this.popup && !this.popup.isMouseOver)\n            this.detach();\n    };\n    Autocomplete.prototype.mouseOutListener = function (e) {\n        if (this.popup.isOpen)\n            this.$updatePopupPosition();\n    };\n    Autocomplete.prototype.goTo = function (where) {\n        this.popup.goTo(where);\n    };\n    Autocomplete.prototype.insertMatch = function (data, options) {\n        if (!data)\n            data = this.popup.getData(this.popup.getRow());\n        if (!data)\n            return false;\n        if (data.value === \"\") // Explicitly given nothing to insert, e.g. \"No suggestion state\"\n            return this.detach();\n        var completions = this.completions;\n        var result = this.getCompletionProvider().insertMatch(this.editor, data, completions.filterText, options);\n        if (this.completions == completions)\n            this.detach();\n        return result;\n    };\n    Autocomplete.prototype.showPopup = function (editor, options) {\n        if (this.editor)\n            this.detach();\n        this.activated = true;\n        this.editor = editor;\n        if (editor.completer != this) {\n            if (editor.completer)\n                editor.completer.detach();\n            editor.completer = this;\n        }\n        editor.on(\"changeSelection\", this.changeListener);\n        editor.on(\"blur\", this.blurListener);\n        editor.on(\"mousedown\", this.mousedownListener);\n        editor.on(\"mousewheel\", this.mousewheelListener);\n        this.updateCompletions(false, options);\n    };\n    Autocomplete.prototype.getCompletionProvider = function (initialPosition) {\n        if (!this.completionProvider)\n            this.completionProvider = new CompletionProvider(initialPosition);\n        return this.completionProvider;\n    };\n    Autocomplete.prototype.gatherCompletions = function (editor, callback) {\n        return this.getCompletionProvider().gatherCompletions(editor, callback);\n    };\n    Autocomplete.prototype.updateCompletions = function (keepPopupPosition, options) {\n        if (keepPopupPosition && this.base && this.completions) {\n            var pos = this.editor.getCursorPosition();\n            var prefix = this.editor.session.getTextRange({ start: this.base, end: pos });\n            if (prefix == this.completions.filterText)\n                return;\n            this.completions.setFilter(prefix);\n            if (!this.completions.filtered.length)\n                return this.detach();\n            if (this.completions.filtered.length == 1\n                && this.completions.filtered[0].value == prefix\n                && !this.completions.filtered[0].snippet)\n                return this.detach();\n            this.openPopup(this.editor, prefix, keepPopupPosition);\n            return;\n        }\n        if (options && options.matches) {\n            var pos = this.editor.getSelectionRange().start;\n            this.base = this.editor.session.doc.createAnchor(pos.row, pos.column);\n            this.base.$insertRight = true;\n            this.completions = new FilteredList(options.matches);\n            this.getCompletionProvider().completions = this.completions;\n            return this.openPopup(this.editor, \"\", keepPopupPosition);\n        }\n        var session = this.editor.getSession();\n        var pos = this.editor.getCursorPosition();\n        var prefix = util.getCompletionPrefix(this.editor);\n        this.base = session.doc.createAnchor(pos.row, pos.column - prefix.length);\n        this.base.$insertRight = true;\n        var completionOptions = {\n            exactMatch: this.exactMatch,\n            ignoreCaption: this.ignoreCaption\n        };\n        this.getCompletionProvider({\n            prefix: prefix,\n            pos: pos\n        }).provideCompletions(this.editor, completionOptions, \n        function (err, completions, finished) {\n            var filtered = completions.filtered;\n            var prefix = util.getCompletionPrefix(this.editor);\n            this.$firstOpenTimer.cancel();\n            if (finished) {\n                if (!filtered.length) {\n                    var emptyMessage = !this.autoShown && this.emptyMessage;\n                    if (typeof emptyMessage == \"function\")\n                        emptyMessage = this.emptyMessage(prefix);\n                    if (emptyMessage) {\n                        var completionsForEmpty = [{\n                                caption: emptyMessage,\n                                value: \"\"\n                            }\n                        ];\n                        this.completions = new FilteredList(completionsForEmpty);\n                        this.openPopup(this.editor, prefix, keepPopupPosition);\n                        this.popup.renderer.setStyle(\"ace_loading\", false);\n                        this.popup.renderer.setStyle(\"ace_empty-message\", true);\n                        return;\n                    }\n                    return this.detach();\n                }\n                if (filtered.length == 1 && filtered[0].value == prefix\n                    && !filtered[0].snippet)\n                    return this.detach();\n                if (this.autoInsert && !this.autoShown && filtered.length == 1)\n                    return this.insertMatch(filtered[0]);\n            }\n            this.completions = !finished && this.showLoadingState ?\n                new FilteredList(Autocomplete.completionsForLoading.concat(filtered), completions.filterText) :\n                completions;\n            this.openPopup(this.editor, prefix, keepPopupPosition);\n            this.popup.renderer.setStyle(\"ace_empty-message\", false);\n            this.popup.renderer.setStyle(\"ace_loading\", !finished);\n        }.bind(this));\n        if (this.showLoadingState && !this.autoShown && !(this.popup && this.popup.isOpen)) {\n            this.$firstOpenTimer.delay(this.stickySelectionDelay / 2);\n        }\n    };\n    Autocomplete.prototype.cancelContextMenu = function () {\n        this.editor.$mouseHandler.cancelContextMenu();\n    };\n    Autocomplete.prototype.updateDocTooltip = function () {\n        var popup = this.popup;\n        var all = this.completions && this.completions.filtered;\n        var selected = all && (all[popup.getHoveredRow()] || all[popup.getRow()]);\n        var doc = null;\n        if (!selected || !this.editor || !this.popup.isOpen)\n            return this.hideDocTooltip();\n        var completersLength = this.editor.completers.length;\n        for (var i = 0; i < completersLength; i++) {\n            var completer = this.editor.completers[i];\n            if (completer.getDocTooltip && selected.completerId === completer.id) {\n                doc = completer.getDocTooltip(selected);\n                break;\n            }\n        }\n        if (!doc && typeof selected != \"string\")\n            doc = selected;\n        if (typeof doc == \"string\")\n            doc = { docText: doc };\n        if (!doc || !(doc.docHTML || doc.docText))\n            return this.hideDocTooltip();\n        this.showDocTooltip(doc);\n    };\n    Autocomplete.prototype.showDocTooltip = function (item) {\n        if (!this.tooltipNode) {\n            this.tooltipNode = dom.createElement(\"div\");\n            this.tooltipNode.style.margin = \"0\";\n            this.tooltipNode.style.pointerEvents = \"auto\";\n            this.tooltipNode.style.overscrollBehavior = \"contain\";\n            this.tooltipNode.tabIndex = -1;\n            this.tooltipNode.onblur = this.blurListener.bind(this);\n            this.tooltipNode.onclick = this.onTooltipClick.bind(this);\n            this.tooltipNode.id = \"doc-tooltip\";\n            this.tooltipNode.setAttribute(\"role\", \"tooltip\");\n            this.tooltipNode.addEventListener(\"wheel\", preventParentScroll);\n        }\n        var theme = this.editor.renderer.theme;\n        this.tooltipNode.className = \"ace_tooltip ace_doc-tooltip \" +\n            (theme.isDark ? \"ace_dark \" : \"\") + (theme.cssClass || \"\");\n        var tooltipNode = this.tooltipNode;\n        if (item.docHTML) {\n            tooltipNode.innerHTML = item.docHTML;\n        }\n        else if (item.docText) {\n            tooltipNode.textContent = item.docText;\n        }\n        if (!tooltipNode.parentNode)\n            this.popup.container.appendChild(this.tooltipNode);\n        var popup = this.popup;\n        var rect = popup.container.getBoundingClientRect();\n        var targetWidth = 400;\n        var targetHeight = 300;\n        var scrollBarSize = popup.renderer.scrollBar.width || 10;\n        var leftSize = rect.left;\n        var rightSize = window.innerWidth - rect.right - scrollBarSize;\n        var topSize = popup.isTopdown ? rect.top : window.innerHeight - scrollBarSize - rect.bottom;\n        var scores = [\n            Math.min(rightSize / targetWidth, 1),\n            Math.min(leftSize / targetWidth, 1),\n            Math.min(topSize / targetHeight * 0.9),\n        ];\n        var max = Math.max.apply(Math, scores);\n        var tooltipStyle = tooltipNode.style;\n        tooltipStyle.display = \"block\";\n        if (max == scores[0]) {\n            tooltipStyle.left = (rect.right + 1) + \"px\";\n            tooltipStyle.right = \"\";\n            tooltipStyle.maxWidth = targetWidth * max + \"px\";\n            tooltipStyle.top = rect.top + \"px\";\n            tooltipStyle.bottom = \"\";\n            tooltipStyle.maxHeight = Math.min(window.innerHeight - scrollBarSize - rect.top, targetHeight) + \"px\";\n        }\n        else if (max == scores[1]) {\n            tooltipStyle.right = window.innerWidth - rect.left + \"px\";\n            tooltipStyle.left = \"\";\n            tooltipStyle.maxWidth = targetWidth * max + \"px\";\n            tooltipStyle.top = rect.top + \"px\";\n            tooltipStyle.bottom = \"\";\n            tooltipStyle.maxHeight = Math.min(window.innerHeight - scrollBarSize - rect.top, targetHeight) + \"px\";\n        }\n        else if (max == scores[2]) {\n            tooltipStyle.left = window.innerWidth - rect.left + \"px\";\n            tooltipStyle.maxWidth = Math.min(targetWidth, window.innerWidth) + \"px\";\n            if (popup.isTopdown) {\n                tooltipStyle.top = rect.bottom + \"px\";\n                tooltipStyle.left = rect.left + \"px\";\n                tooltipStyle.right = \"\";\n                tooltipStyle.bottom = \"\";\n                tooltipStyle.maxHeight = Math.min(window.innerHeight - scrollBarSize - rect.bottom, targetHeight) + \"px\";\n            }\n            else {\n                tooltipStyle.top = popup.container.offsetTop - tooltipNode.offsetHeight + \"px\";\n                tooltipStyle.left = rect.left + \"px\";\n                tooltipStyle.right = \"\";\n                tooltipStyle.bottom = \"\";\n                tooltipStyle.maxHeight = Math.min(popup.container.offsetTop, targetHeight) + \"px\";\n            }\n        }\n    };\n    Autocomplete.prototype.hideDocTooltip = function () {\n        this.tooltipTimer.cancel();\n        if (!this.tooltipNode)\n            return;\n        var el = this.tooltipNode;\n        if (!this.editor.isFocused() && document.activeElement == el)\n            this.editor.focus();\n        this.tooltipNode = null;\n        if (el.parentNode)\n            el.parentNode.removeChild(el);\n    };\n    Autocomplete.prototype.onTooltipClick = function (e) {\n        var a = e.target;\n        while (a && a != this.tooltipNode) {\n            if (a.nodeName == \"A\" && a.href) {\n                a.rel = \"noreferrer\";\n                a.target = \"_blank\";\n                break;\n            }\n            a = a.parentNode;\n        }\n    };\n    Autocomplete.prototype.destroy = function () {\n        this.detach();\n        if (this.popup) {\n            this.popup.destroy();\n            var el = this.popup.container;\n            if (el && el.parentNode)\n                el.parentNode.removeChild(el);\n        }\n        if (this.editor && this.editor.completer == this) {\n            this.editor.off(\"destroy\", destroyCompleter);\n            this.editor.completer = null;\n        }\n        this.inlineRenderer = this.popup = this.editor = null;\n    };\n    Autocomplete.for = function (editor) {\n        if (editor.completer instanceof Autocomplete) {\n            return editor.completer;\n        }\n        if (editor.completer) {\n            editor.completer.destroy();\n            editor.completer = null;\n        }\n        if (config.get(\"sharedPopups\")) {\n            if (!Autocomplete[\"$sharedInstance\"])\n                Autocomplete[\"$sharedInstance\"] = new Autocomplete();\n            editor.completer = Autocomplete[\"$sharedInstance\"];\n        }\n        else {\n            editor.completer = new Autocomplete();\n            editor.once(\"destroy\", destroyCompleter);\n        }\n        return editor.completer;\n    };\n    return Autocomplete;\n}());\nAutocomplete.prototype.commands = {\n    \"Up\": function (editor) { editor.completer.goTo(\"up\"); },\n    \"Down\": function (editor) { editor.completer.goTo(\"down\"); },\n    \"Ctrl-Up|Ctrl-Home\": function (editor) { editor.completer.goTo(\"start\"); },\n    \"Ctrl-Down|Ctrl-End\": function (editor) { editor.completer.goTo(\"end\"); },\n    \"Esc\": function (editor) { editor.completer.detach(); },\n    \"Return\": function (editor) { return editor.completer.insertMatch(); },\n    \"Shift-Return\": function (editor) { editor.completer.insertMatch(null, { deleteSuffix: true }); },\n    \"Tab\": function (editor) {\n        var result = editor.completer.insertMatch();\n        if (!result && !editor.tabstopManager)\n            editor.completer.goTo(\"down\");\n        else\n            return result;\n    },\n    \"Backspace\": function (editor) {\n        editor.execCommand(\"backspace\");\n        var prefix = util.getCompletionPrefix(editor);\n        if (!prefix && editor.completer)\n            editor.completer.detach();\n    },\n    \"PageUp\": function (editor) { editor.completer.popup.gotoPageUp(); },\n    \"PageDown\": function (editor) { editor.completer.popup.gotoPageDown(); }\n};\nAutocomplete.startCommand = {\n    name: \"startAutocomplete\",\n    exec: function (editor, options) {\n        var completer = Autocomplete.for(editor);\n        completer.autoInsert = false;\n        completer.autoSelect = true;\n        completer.autoShown = false;\n        completer.showPopup(editor, options);\n        completer.cancelContextMenu();\n    },\n    bindKey: \"Ctrl-Space|Ctrl-Shift-Space|Alt-Space\"\n};\nvar CompletionProvider = /** @class */ (function () {\n    function CompletionProvider(initialPosition) {\n        this.initialPosition = initialPosition;\n        this.active = true;\n    }\n    CompletionProvider.prototype.insertByIndex = function (editor, index, options) {\n        if (!this.completions || !this.completions.filtered) {\n            return false;\n        }\n        return this.insertMatch(editor, this.completions.filtered[index], options);\n    };\n    CompletionProvider.prototype.insertMatch = function (editor, data, options) {\n        if (!data)\n            return false;\n        editor.startOperation({ command: { name: \"insertMatch\" } });\n        if (data.completer && data.completer.insertMatch) {\n            data.completer.insertMatch(editor, data);\n        }\n        else {\n            if (!this.completions)\n                return false;\n            var replaceBefore = this.completions.filterText.length;\n            var replaceAfter = 0;\n            if (data.range && data.range.start.row === data.range.end.row) {\n                replaceBefore -= this.initialPosition.prefix.length;\n                replaceBefore += this.initialPosition.pos.column - data.range.start.column;\n                replaceAfter += data.range.end.column - this.initialPosition.pos.column;\n            }\n            if (replaceBefore || replaceAfter) {\n                var ranges;\n                if (editor.selection.getAllRanges) {\n                    ranges = editor.selection.getAllRanges();\n                }\n                else {\n                    ranges = [editor.getSelectionRange()];\n                }\n                for (var i = 0, range; range = ranges[i]; i++) {\n                    range.start.column -= replaceBefore;\n                    range.end.column += replaceAfter;\n                    editor.session.remove(range);\n                }\n            }\n            if (data.snippet) {\n                snippetManager.insertSnippet(editor, data.snippet);\n            }\n            else {\n                this.$insertString(editor, data);\n            }\n            if (data.completer && data.completer.onInsert && typeof data.completer.onInsert == \"function\") {\n                data.completer.onInsert(editor, data);\n            }\n            if (data.command && data.command === \"startAutocomplete\") {\n                editor.execCommand(data.command);\n            }\n        }\n        editor.endOperation();\n        return true;\n    };\n    CompletionProvider.prototype.$insertString = function (editor, data) {\n        var text = data.value || data;\n        editor.execCommand(\"insertstring\", text);\n    };\n    CompletionProvider.prototype.gatherCompletions = function (editor, callback) {\n        var session = editor.getSession();\n        var pos = editor.getCursorPosition();\n        var prefix = util.getCompletionPrefix(editor);\n        var matches = [];\n        this.completers = editor.completers;\n        var total = editor.completers.length;\n        editor.completers.forEach(function (completer, i) {\n            completer.getCompletions(editor, session, pos, prefix, function (err, results) {\n                if (completer.hideInlinePreview)\n                    results = results.map(function (result) {\n                        return Object.assign(result, { hideInlinePreview: completer.hideInlinePreview });\n                    });\n                if (!err && results)\n                    matches = matches.concat(results);\n                callback(null, {\n                    prefix: util.getCompletionPrefix(editor),\n                    matches: matches,\n                    finished: (--total === 0)\n                });\n            });\n        });\n        return true;\n    };\n    CompletionProvider.prototype.provideCompletions = function (editor, options, callback) {\n        var processResults = function (results) {\n            var prefix = results.prefix;\n            var matches = results.matches;\n            this.completions = new FilteredList(matches);\n            if (options.exactMatch)\n                this.completions.exactMatch = true;\n            if (options.ignoreCaption)\n                this.completions.ignoreCaption = true;\n            this.completions.setFilter(prefix);\n            if (results.finished || this.completions.filtered.length)\n                callback(null, this.completions, results.finished);\n        }.bind(this);\n        var isImmediate = true;\n        var immediateResults = null;\n        this.gatherCompletions(editor, function (err, results) {\n            if (!this.active) {\n                return;\n            }\n            if (err) {\n                callback(err, [], true);\n                this.detach();\n            }\n            var prefix = results.prefix;\n            if (prefix.indexOf(results.prefix) !== 0)\n                return;\n            if (isImmediate) {\n                immediateResults = results;\n                return;\n            }\n            processResults(results);\n        }.bind(this));\n        isImmediate = false;\n        if (immediateResults) {\n            var results = immediateResults;\n            immediateResults = null;\n            processResults(results);\n        }\n    };\n    CompletionProvider.prototype.detach = function () {\n        this.active = false;\n        this.completers && this.completers.forEach(function (completer) {\n            if (typeof completer.cancel === \"function\") {\n                completer.cancel();\n            }\n        });\n    };\n    return CompletionProvider;\n}());\nvar FilteredList = /** @class */ (function () {\n    function FilteredList(array, filterText) {\n        this.all = array;\n        this.filtered = array;\n        this.filterText = filterText || \"\";\n        this.exactMatch = false;\n        this.ignoreCaption = false;\n    }\n    FilteredList.prototype.setFilter = function (str) {\n        if (str.length > this.filterText && str.lastIndexOf(this.filterText, 0) === 0)\n            var matches = this.filtered;\n        else\n            var matches = this.all;\n        this.filterText = str;\n        matches = this.filterCompletions(matches, this.filterText);\n        matches = matches.sort(function (a, b) {\n            return b.exactMatch - a.exactMatch || b.$score - a.$score\n                || (a.caption || a.value).localeCompare(b.caption || b.value);\n        });\n        var prev = null;\n        matches = matches.filter(function (item) {\n            var caption = item.snippet || item.caption || item.value;\n            if (caption === prev)\n                return false;\n            prev = caption;\n            return true;\n        });\n        this.filtered = matches;\n    };\n    FilteredList.prototype.filterCompletions = function (items, needle) {\n        var results = [];\n        var upper = needle.toUpperCase();\n        var lower = needle.toLowerCase();\n        loop: for (var i = 0, item; item = items[i]; i++) {\n            if (item.skipFilter) {\n                item.$score = item.score;\n                results.push(item);\n                continue;\n            }\n            var caption = (!this.ignoreCaption && item.caption) || item.value || item.snippet;\n            if (!caption)\n                continue;\n            var lastIndex = -1;\n            var matchMask = 0;\n            var penalty = 0;\n            var index, distance;\n            if (this.exactMatch) {\n                if (needle !== caption.substr(0, needle.length))\n                    continue loop;\n            }\n            else {\n                var fullMatchIndex = caption.toLowerCase().indexOf(lower);\n                if (fullMatchIndex > -1) {\n                    penalty = fullMatchIndex;\n                }\n                else {\n                    for (var j = 0; j < needle.length; j++) {\n                        var i1 = caption.indexOf(lower[j], lastIndex + 1);\n                        var i2 = caption.indexOf(upper[j], lastIndex + 1);\n                        index = (i1 >= 0) ? ((i2 < 0 || i1 < i2) ? i1 : i2) : i2;\n                        if (index < 0)\n                            continue loop;\n                        distance = index - lastIndex - 1;\n                        if (distance > 0) {\n                            if (lastIndex === -1)\n                                penalty += 10;\n                            penalty += distance;\n                            matchMask = matchMask | (1 << j);\n                        }\n                        lastIndex = index;\n                    }\n                }\n            }\n            item.matchMask = matchMask;\n            item.exactMatch = penalty ? 0 : 1;\n            item.$score = (item.score || 0) - penalty;\n            results.push(item);\n        }\n        return results;\n    };\n    return FilteredList;\n}());\nexports.Autocomplete = Autocomplete;\nexports.CompletionProvider = CompletionProvider;\nexports.FilteredList = FilteredList;\n\n});\n\nace.define(\"ace/marker_group\",[\"require\",\"exports\",\"module\"], function(require, exports, module){\"use strict\";\nvar MarkerGroup = /** @class */ (function () {\n    function MarkerGroup(session, options) {\n        if (options)\n            this.markerType = options.markerType;\n        this.markers = [];\n        this.session = session;\n        session.addDynamicMarker(this);\n    }\n    MarkerGroup.prototype.getMarkerAtPosition = function (pos) {\n        return this.markers.find(function (marker) {\n            return marker.range.contains(pos.row, pos.column);\n        });\n    };\n    MarkerGroup.prototype.markersComparator = function (a, b) {\n        return a.range.start.row - b.range.start.row;\n    };\n    MarkerGroup.prototype.setMarkers = function (markers) {\n        this.markers = markers.sort(this.markersComparator).slice(0, this.MAX_MARKERS);\n        this.session._signal(\"changeBackMarker\");\n    };\n    MarkerGroup.prototype.update = function (html, markerLayer, session, config) {\n        if (!this.markers || !this.markers.length)\n            return;\n        var visibleRangeStartRow = config.firstRow, visibleRangeEndRow = config.lastRow;\n        var foldLine;\n        var markersOnOneLine = 0;\n        var lastRow = 0;\n        for (var i = 0; i < this.markers.length; i++) {\n            var marker = this.markers[i];\n            if (marker.range.end.row < visibleRangeStartRow)\n                continue;\n            if (marker.range.start.row > visibleRangeEndRow)\n                continue;\n            if (marker.range.start.row === lastRow) {\n                markersOnOneLine++;\n            }\n            else {\n                lastRow = marker.range.start.row;\n                markersOnOneLine = 0;\n            }\n            if (markersOnOneLine > 200) {\n                continue;\n            }\n            var markerVisibleRange = marker.range.clipRows(visibleRangeStartRow, visibleRangeEndRow);\n            if (markerVisibleRange.start.row === markerVisibleRange.end.row\n                && markerVisibleRange.start.column === markerVisibleRange.end.column) {\n                continue; // visible range is empty\n            }\n            var screenRange = markerVisibleRange.toScreenRange(session);\n            if (screenRange.isEmpty()) {\n                foldLine = session.getNextFoldLine(markerVisibleRange.end.row, foldLine);\n                if (foldLine && foldLine.end.row > markerVisibleRange.end.row) {\n                    visibleRangeStartRow = foldLine.end.row;\n                }\n                continue;\n            }\n            if (this.markerType === \"fullLine\") {\n                markerLayer.drawFullLineMarker(html, screenRange, marker.className, config);\n            }\n            else if (screenRange.isMultiLine()) {\n                if (this.markerType === \"line\")\n                    markerLayer.drawMultiLineMarker(html, screenRange, marker.className, config);\n                else\n                    markerLayer.drawTextMarker(html, screenRange, marker.className, config);\n            }\n            else {\n                markerLayer.drawSingleLineMarker(html, screenRange, marker.className + \" ace_br15\", config);\n            }\n        }\n    };\n    return MarkerGroup;\n}());\nMarkerGroup.prototype.MAX_MARKERS = 10000;\nexports.MarkerGroup = MarkerGroup;\n\n});\n\nace.define(\"ace/autocomplete/text_completer\",[\"require\",\"exports\",\"module\",\"ace/range\"], function(require, exports, module){var Range = require(\"../range\").Range;\nvar splitRegex = /[^a-zA-Z_0-9\\$\\-\\u00C0-\\u1FFF\\u2C00-\\uD7FF\\w]+/;\nfunction getWordIndex(doc, pos) {\n    var textBefore = doc.getTextRange(Range.fromPoints({\n        row: 0,\n        column: 0\n    }, pos));\n    return textBefore.split(splitRegex).length - 1;\n}\nfunction wordDistance(doc, pos) {\n    var prefixPos = getWordIndex(doc, pos);\n    var words = doc.getValue().split(splitRegex);\n    var wordScores = Object.create(null);\n    var currentWord = words[prefixPos];\n    words.forEach(function (word, idx) {\n        if (!word || word === currentWord)\n            return;\n        var distance = Math.abs(prefixPos - idx);\n        var score = words.length - distance;\n        if (wordScores[word]) {\n            wordScores[word] = Math.max(score, wordScores[word]);\n        }\n        else {\n            wordScores[word] = score;\n        }\n    });\n    return wordScores;\n}\nexports.getCompletions = function (editor, session, pos, prefix, callback) {\n    var wordScore = wordDistance(session, pos);\n    var wordList = Object.keys(wordScore);\n    callback(null, wordList.map(function (word) {\n        return {\n            caption: word,\n            value: word,\n            score: wordScore[word],\n            meta: \"local\"\n        };\n    }));\n};\n\n});\n\nace.define(\"ace/ext/language_tools\",[\"require\",\"exports\",\"module\",\"ace/snippets\",\"ace/autocomplete\",\"ace/config\",\"ace/lib/lang\",\"ace/autocomplete/util\",\"ace/marker_group\",\"ace/autocomplete/text_completer\",\"ace/editor\",\"ace/config\"], function(require, exports, module){/**\n * ## Language Tools extension for Ace Editor\n *\n * Provides autocompletion, snippets, and language intelligence features for the Ace code editor.\n * This extension integrates multiple completion providers including keyword completion, snippet expansion,\n * and text-based completion to enhance the coding experience with contextual suggestions and automated code generation.\n *\n * **Configuration Options:**\n * - `enableBasicAutocompletion`: Enable/disable basic completion functionality\n * - `enableLiveAutocompletion`: Enable/disable real-time completion suggestions\n * - `enableSnippets`: Enable/disable snippet expansion with Tab key\n * - `liveAutocompletionDelay`: Delay before showing live completion popup\n * - `liveAutocompletionThreshold`: Minimum prefix length to trigger completion\n *\n * **Usage:**\n * ```javascript\n * editor.setOptions({\n *   enableBasicAutocompletion: true,\n *   enableLiveAutocompletion: true,\n *   enableSnippets: true\n * });\n * ```\n *\n * @module\n */\n\"use strict\";\nvar snippetManager = require(\"../snippets\").snippetManager;\nvar Autocomplete = require(\"../autocomplete\").Autocomplete;\nvar config = require(\"../config\");\nvar lang = require(\"../lib/lang\");\nvar util = require(\"../autocomplete/util\");\nvar MarkerGroup = require(\"../marker_group\").MarkerGroup;\nvar textCompleter = require(\"../autocomplete/text_completer\");\nvar keyWordCompleter = {\n    getCompletions: function (editor, session, pos, prefix, callback) {\n        if (session.$mode.completer) {\n            return session.$mode.completer.getCompletions(editor, session, pos, prefix, callback);\n        }\n        var state = editor.session.getState(pos.row);\n        var completions = session.$mode.getCompletions(state, session, pos, prefix);\n        completions = completions.map(function (el) {\n            el.completerId = keyWordCompleter.id;\n            return el;\n        });\n        callback(null, completions);\n    },\n    id: \"keywordCompleter\"\n};\nvar transformSnippetTooltip = function (str) {\n    var record = {};\n    return str.replace(/\\${(\\d+)(:(.*?))?}/g, function (_, p1, p2, p3) {\n        return (record[p1] = p3 || '');\n    }).replace(/\\$(\\d+?)/g, function (_, p1) {\n        return record[p1];\n    });\n};\nvar snippetCompleter = {\n    getCompletions: function (editor, session, pos, prefix, callback) {\n        var scopes = [];\n        var token = session.getTokenAt(pos.row, pos.column);\n        if (token && token.type.match(/(tag-name|tag-open|tag-whitespace|attribute-name|attribute-value)\\.xml$/))\n            scopes.push('html-tag');\n        else\n            scopes = snippetManager.getActiveScopes(editor);\n        var snippetMap = snippetManager.snippetMap;\n        var completions = [];\n        scopes.forEach(function (scope) {\n            var snippets = snippetMap[scope] || [];\n            for (var i = snippets.length; i--;) {\n                var s = snippets[i];\n                var caption = s.name || s.tabTrigger;\n                if (!caption)\n                    continue;\n                completions.push({\n                    caption: caption,\n                    snippet: s.content,\n                    meta: s.tabTrigger && !s.name ? s.tabTrigger + \"\\u21E5 \" : \"snippet\",\n                    completerId: snippetCompleter.id\n                });\n            }\n        }, this);\n        callback(null, completions);\n    },\n    getDocTooltip: function (item) {\n        if (item.snippet && !item.docHTML) {\n            item.docHTML = [\n                \"<b>\", lang.escapeHTML(item.caption), \"</b>\", \"<hr></hr>\",\n                lang.escapeHTML(transformSnippetTooltip(item.snippet))\n            ].join(\"\");\n        }\n    },\n    id: \"snippetCompleter\"\n};\nvar completers = [snippetCompleter, textCompleter, keyWordCompleter];\nexports.setCompleters = function (val) {\n    completers.length = 0;\n    if (val)\n        completers.push.apply(completers, val);\n};\nexports.addCompleter = function (completer) {\n    completers.push(completer);\n};\nexports.textCompleter = textCompleter;\nexports.keyWordCompleter = keyWordCompleter;\nexports.snippetCompleter = snippetCompleter;\nvar expandSnippet = {\n    name: \"expandSnippet\",\n    exec: function (editor) {\n        return snippetManager.expandWithTab(editor);\n    },\n    bindKey: \"Tab\"\n};\nvar onChangeMode = function (e, editor) {\n    loadSnippetsForMode(editor.session.$mode);\n};\nvar loadSnippetsForMode = function (mode) {\n    if (typeof mode == \"string\")\n        mode = config.$modes[mode];\n    if (!mode)\n        return;\n    if (!snippetManager.files)\n        snippetManager.files = {};\n    loadSnippetFile(mode.$id, mode.snippetFileId);\n    if (mode.modes)\n        mode.modes.forEach(loadSnippetsForMode);\n};\nvar loadSnippetFile = function (id, snippetFilePath) {\n    if (!snippetFilePath || !id || snippetManager.files[id])\n        return;\n    snippetManager.files[id] = {};\n    config.loadModule(snippetFilePath, function (m) {\n        if (!m)\n            return;\n        snippetManager.files[id] = m;\n        if (!m.snippets && m.snippetText)\n            m.snippets = snippetManager.parseSnippetFile(m.snippetText);\n        snippetManager.register(m.snippets || [], m.scope);\n        if (m.includeScopes) {\n            snippetManager.snippetMap[m.scope].includeScopes = m.includeScopes;\n            m.includeScopes.forEach(function (x) {\n                loadSnippetsForMode(\"ace/mode/\" + x);\n            });\n        }\n    });\n};\nvar doLiveAutocomplete = function (e) {\n    var editor = e.editor;\n    var hasCompleter = editor.completer && editor.completer.activated;\n    if (e.command.name === \"backspace\") {\n        if (hasCompleter && !util.getCompletionPrefix(editor))\n            editor.completer.detach();\n    }\n    else if (e.command.name === \"insertstring\" && !hasCompleter) {\n        lastExecEvent = e;\n        var delay = e.editor.$liveAutocompletionDelay;\n        if (delay) {\n            liveAutocompleteTimer.delay(delay);\n        }\n        else {\n            showLiveAutocomplete(e);\n        }\n    }\n};\nvar lastExecEvent;\nvar liveAutocompleteTimer = lang.delayedCall(function () {\n    showLiveAutocomplete(lastExecEvent);\n}, 0);\nvar showLiveAutocomplete = function (e) {\n    var editor = e.editor;\n    var prefix = util.getCompletionPrefix(editor);\n    var previousChar = e.args;\n    var triggerAutocomplete = util.triggerAutocomplete(editor, previousChar);\n    if (prefix && prefix.length >= editor.$liveAutocompletionThreshold || triggerAutocomplete) {\n        var completer = Autocomplete.for(editor);\n        completer.autoShown = true;\n        completer.showPopup(editor);\n    }\n};\nvar Editor = require(\"../editor\").Editor;\nrequire(\"../config\").defineOptions(Editor.prototype, \"editor\", {\n    enableBasicAutocompletion: {\n        set: function (val) {\n            if (val) {\n                Autocomplete.for(this);\n                if (!this.completers)\n                    this.completers = Array.isArray(val) ? val : completers;\n                this.commands.addCommand(Autocomplete.startCommand);\n            }\n            else {\n                this.commands.removeCommand(Autocomplete.startCommand);\n            }\n        },\n        value: false\n    },\n    enableLiveAutocompletion: {\n        set: function (val) {\n            if (val) {\n                if (!this.completers)\n                    this.completers = Array.isArray(val) ? val : completers;\n                this.commands.on('afterExec', doLiveAutocomplete);\n            }\n            else {\n                this.commands.off('afterExec', doLiveAutocomplete);\n            }\n        },\n        value: false\n    },\n    liveAutocompletionDelay: {\n        initialValue: 0\n    },\n    liveAutocompletionThreshold: {\n        initialValue: 0\n    },\n    enableSnippets: {\n        set: function (val) {\n            if (val) {\n                this.commands.addCommand(expandSnippet);\n                this.on(\"changeMode\", onChangeMode);\n                onChangeMode(null, this);\n            }\n            else {\n                this.commands.removeCommand(expandSnippet);\n                this.off(\"changeMode\", onChangeMode);\n            }\n        },\n        value: false\n    }\n});\nexports.MarkerGroup = MarkerGroup;\n\n});                (function() {\n                    ace.require([\"ace/ext/language_tools\"], function(m) {\n                        if (typeof module == \"object\" && typeof exports == \"object\" && module) {\n                            module.exports = m;\n                        }\n                    });\n                })();\n            "], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAI,OAAO,gBAAe,CAAC,WAAU,WAAU,UAAS,eAAc,eAAc,yBAAwB,gBAAe,aAAY,kBAAiB,6BAA4B,iBAAgB,iBAAgB,YAAY,GAAG,SAASA,UAASC,UAASC,SAAO;AAAC;AACtQ,UAAI,MAAMF,SAAQ,WAAW;AAC7B,UAAI,MAAMA,SAAQ,WAAW;AAC7B,UAAI,eAAeA,SAAQ,qBAAqB,EAAE;AAClD,UAAI,OAAOA,SAAQ,YAAY;AAC/B,UAAI,QAAQA,SAAQ,SAAS,EAAE;AAC/B,UAAI,YAAYA,SAAQ,cAAc,EAAE;AACxC,UAAI,cAAcA,SAAQ,yBAAyB,EAAE;AACrD,UAAI,YAAYA,SAAQ,aAAa,EAAE;AACvC,UAAI,YAAYA,SAAQ,aAAa;AACrC,UAAI,YAAY;AAAA,QACZ,cAAc,SAAU,QAAQ;AAC5B,iBAAO,OAAO,QAAQ,aAAa,OAAO,QAAQ,aAAa,CAAC;AAAA,QACpE;AAAA,QACA,WAAW,SAAU,QAAQ,MAAM,aAAa;AAC5C,cAAI,OAAO,OAAO,QAAQ,aAAa;AACvC,cAAI;AACA,mBAAO,KAAK,QAAQ,oBAAoB,OAAO,cAAc,IAAI;AACrE,iBAAO;AAAA,QACX;AAAA,QACA,cAAc,SAAU,QAAQ;AAC5B,iBAAO,OAAO,QAAQ,QAAQ,OAAO,kBAAkB,EAAE,GAAG;AAAA,QAChE;AAAA,QACA,WAAW,SAAU,QAAQ;AACzB,iBAAO,OAAO,QAAQ,QAAQ,OAAO,kBAAkB,EAAE,MAAM,CAAC;AAAA,QACpE;AAAA,QACA,YAAY,SAAU,QAAQ;AAC1B,iBAAO,OAAO,kBAAkB,EAAE;AAAA,QACtC;AAAA,QACA,aAAa,SAAU,QAAQ;AAC3B,iBAAO,OAAO,kBAAkB,EAAE,MAAM;AAAA,QAC5C;AAAA,QACA,WAAW,SAAU,QAAQ;AACzB,iBAAO,OAAO,QAAQ,eAAe,IAAI,QAAQ;AAAA,QACrD;AAAA,QACA,UAAU,SAAU,QAAQ;AACxB,iBAAO,OAAO,QAAQ,WAAW;AAAA,QACrC;AAAA,QACA,WAAW,SAAU,QAAQ;AACzB,iBAAO,UAAU,WAAW,UAAU,QAAQ;AAAA,QAClD;AAAA,QACA,UAAU,SAAU,QAAQ;AACxB,iBAAO,WAAW,KAAK,KAAK,SAAS,MAAM,CAAC,EAAE,CAAC;AAAA,QACnD;AAAA,QACA,eAAe,SAAU,QAAQ;AAC7B,iBAAO,WAAW,KAAK,KAAK,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,YAAY,EAAE;AAAA,QAC3E;AAAA,QACA,WAAW,SAAU,QAAQ;AACzB,iBAAO,KAAK,SAAS,MAAM,EAAE,QAAQ,YAAY,EAAE;AAAA,QACvD;AAAA,QACA,UAAU,SAAU,QAAQ;AAAE,iBAAO;AAAA,QAAwB;AAAA,QAC7D,gBAAgB,WAAY;AAAE,iBAAO;AAAA,QAAW;AAAA,QAChD,UAAU,WAAY;AAAE,iBAAO;AAAA,QAAW;AAAA,QAC1C,qBAAqB,SAAU,QAAQ;AACnC,cAAI,OAAO,OAAO,QAAQ,SAAS,CAAC;AACpC,iBAAO,KAAK,gBAAgB,KAAK,aAAa,SAAS;AAAA,QAC3D;AAAA,QACA,mBAAmB,SAAU,QAAQ;AACjC,cAAI,OAAO,OAAO,QAAQ,SAAS,CAAC;AACpC,iBAAO,KAAK,gBAAgB,KAAK,aAAa,OAAO;AAAA,QACzD;AAAA,QACA,cAAc,SAAU,QAAQ;AAC5B,cAAI,OAAO,OAAO,QAAQ,SAAS,CAAC;AACpC,iBAAO,KAAK,oBAAoB;AAAA,QACpC;AAAA,QACA,cAAc,KAAK,KAAK,MAAM,EAAE,MAAM,UAAU,CAAC;AAAA,QACjD,oBAAoB,KAAK,KAAK,MAAM,EAAE,MAAM,UAAU,CAAC;AAAA,QACvD,eAAe,KAAK,KAAK,MAAM,EAAE,OAAO,UAAU,CAAC;AAAA,QACnD,oBAAoB,KAAK,KAAK,MAAM,EAAE,OAAO,OAAO,CAAC;AAAA,QACrD,0BAA0B,KAAK,KAAK,MAAM,EAAE,OAAO,QAAQ,CAAC;AAAA,QAC5D,cAAc,KAAK,KAAK,MAAM,EAAE,KAAK,UAAU,CAAC;AAAA,QAChD,kBAAkB,KAAK,KAAK,MAAM,EAAE,SAAS,OAAO,CAAC;AAAA,QACrD,wBAAwB,KAAK,KAAK,MAAM,EAAE,SAAS,QAAQ,CAAC;AAAA,QAC5D,cAAc,KAAK,KAAK,MAAM,EAAE,MAAM,WAAW,QAAQ,MAAM,CAAC;AAAA,QAChE,gBAAgB,KAAK,KAAK,MAAM,EAAE,QAAQ,UAAU,CAAC;AAAA,QACrD,gBAAgB,KAAK,KAAK,MAAM,EAAE,QAAQ,UAAU,CAAC;AAAA,MACzD;AACA,gBAAU,gBAAgB,UAAU;AACpC,eAAS,KAAK,YAAY;AACtB,YAAI,OAAM,oBAAI,KAAK,GAAE,eAAe,SAAS,UAAU;AACvD,eAAO,IAAI,UAAU,IAAI,MAAM,MAAM;AAAA,MACzC;AACA,UAAI;AAAA;AAAA,QAAgC,WAAY;AAC5C,mBAASG,kBAAiB;AACtB,iBAAK,aAAa,CAAC;AACnB,iBAAK,iBAAiB,CAAC;AACvB,iBAAK,YAAY;AAAA,UACrB;AACA,UAAAA,gBAAe,UAAU,eAAe,WAAY;AAChD,mBAAOA,gBAAe,YAAY,KAAK,KAAK,gBAAgB;AAAA,UAChE;AACA,UAAAA,gBAAe,UAAU,kBAAkB,WAAY;AACnD,qBAAS,aAAa,KAAK;AACvB,oBAAM,IAAI,OAAO,CAAC;AAClB,kBAAI,QAAQ,KAAK,GAAG;AAChB,uBAAO,CAAC,EAAE,WAAW,SAAS,KAAK,EAAE,EAAE,CAAC;AAC5C,qBAAO,CAAC,EAAE,MAAM,IAAI,CAAC;AAAA,YACzB;AACA,qBAAS,OAAO,IAAI;AAChB,qBAAO,cAAc,KAAK;AAAA,YAC9B;AACA,gBAAI,gBAAgB;AAAA,cAChB,OAAO,OAAO,OAAO,GAAG,IAAI;AAAA,cAC5B,SAAS,SAAU,KAAK,OAAO,OAAO;AAClC,oBAAI,KAAK,MAAM,CAAC;AAChB,mBAAG,YAAY;AACf,mBAAG,QAAQ,IAAI,MAAM,GAAG,EAAE;AAC1B,mBAAG,OAAO;AACV,uBAAO;AAAA,cACX;AAAA,cACA,MAAM;AAAA,YACV;AACA,YAAAA,gBAAe,YAAY,IAAI,IAAI,UAAU;AAAA,cACzC,OAAO;AAAA,gBACH,EAAE,OAAO,OAAO,SAAS,SAAU,KAAK,OAAO,OAAO;AAC9C,sBAAI,KAAK,IAAI,CAAC;AACd,sBAAI,MAAM,OAAO,MAAM,QAAQ;AAC3B,0BAAM;AAAA,kBACV,WACS,OAAO,QAAQ,EAAE,KAAK,IAAI;AAC/B,0BAAM;AAAA,kBACV;AACA,yBAAO,CAAC,GAAG;AAAA,gBACf,EAAE;AAAA,gBACN,EAAE,OAAO,KAAK,SAAS,SAAU,KAAK,OAAO,OAAO;AAC5C,yBAAO,CAAC,MAAM,SAAS,MAAM,MAAM,IAAI,GAAG;AAAA,gBAC9C,EAAE;AAAA,gBACN,EAAE,OAAO,iBAAiB,SAAS,aAAa;AAAA,gBAChD,EAAE,OAAO,oBAAoB,SAAS,SAAU,KAAK,OAAO,OAAO;AAC3D,sBAAI,IAAI,aAAa,IAAI,OAAO,CAAC,CAAC;AAClC,wBAAM,QAAQ,EAAE,CAAC,CAAC;AAClB,yBAAO;AAAA,gBACX,GAAG,MAAM,aAAa;AAAA,gBAC1B,EAAE,OAAO,MAAM,OAAO,WAAW,OAAO,MAAM;AAAA,cAClD;AAAA,cACA,YAAY;AAAA,gBACR,EAAE,OAAO,QAAQ,OAAO,KAAK,IAAI,QAAQ,SAAS,SAAU,KAAK,OAAO,OAAO;AACvE,sBAAI,UAAU,IAAI,MAAM,GAAG,EAAE,EAAE,QAAQ,eAAe,SAAU,UAAU;AACtE,2BAAO,SAAS,UAAU,IAAI,SAAS,CAAC,IAAI;AAAA,kBAChD,CAAC,EAAE,MAAM,IAAM,EAAE,IAAI,SAAU,OAAO;AAClC,2BAAO,EAAE,MAAa;AAAA,kBAC1B,CAAC;AACD,wBAAM,CAAC,EAAE,UAAU;AACnB,yBAAO,CAAC,QAAQ,CAAC,CAAC;AAAA,gBACtB,GAAG,MAAM,QAAQ;AAAA,gBACrB;AAAA,gBACA,EAAE,OAAO,wBAAwB,OAAO,IAAI,MAAM,QAAQ;AAAA,cAC9D;AAAA,cACA,cAAc;AAAA,gBACV,EAAE,OAAO,KAAK,SAAS,SAAU,KAAK,OAAO,OAAO;AAC5C,sBAAI,MAAM,UAAU,MAAM,CAAC,EAAE,YAAY;AACrC,0BAAM,CAAC,EAAE,aAAa;AACtB,0BAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,MAAM,CAAC,EAAE;AACrC,2BAAO,CAAC,MAAM,CAAC,EAAE,KAAK;AAAA,kBAC1B;AACA,yBAAO;AAAA,gBACX,EAAE;AAAA,gBACN,EAAE,OAAO,OAAO,SAAS,SAAU,KAAK,OAAO,OAAO;AAC9C,sBAAI,KAAK,IAAI,CAAC;AACd,sBAAI,MAAM,OAAO,MAAM;AACnB,0BAAM;AAAA,2BACD,OAAO,QAAQ,EAAE,KAAK;AAC3B,0BAAM;AAAA,2BACD,MAAM;AACX,0BAAM;AAAA,2BACD,MAAM;AACX,0BAAM;AAAA,2BACD,QAAQ,QAAQ,EAAE,KAAK;AAC5B,0BAAM,EAAE,YAAY,IAAI,OAAO,KAAK,IAAI;AAC5C,yBAAO,CAAC,GAAG;AAAA,gBACf,EAAE;AAAA,gBACN,EAAE,OAAO,UAAU,SAAS,SAAU,KAAK,OAAO,OAAO;AACjD,sBAAI,OAAO,MAAM,MAAM;AACvB,sBAAI;AACA,yBAAK,OAAO,IAAI,MAAM,GAAG,EAAE;AAC/B,uBAAK,OAAO,QAAQ,KAAK,YAAY,UAAU;AAC/C,yBAAO,CAAC,QAAQ,GAAG;AAAA,gBACvB,GAAG,MAAM,QAAQ;AAAA,gBACrB,EAAE,OAAO,iBAAiB,SAAS,SAAU,KAAK,OAAO,OAAO;AACxD,yBAAO,CAAC,EAAE,MAAM,IAAI,MAAM,CAAC,EAAE,CAAC;AAAA,gBAClC,EAAE;AAAA,gBACN,EAAE,OAAO,UAAU,SAAS,SAAU,KAAK,OAAO,OAAO;AACjD,sBAAI,QAAQ,EAAE,MAAM,IAAI,MAAM,CAAC,EAAE;AACjC,wBAAM,QAAQ,KAAK;AACnB,yBAAO,CAAC,KAAK;AAAA,gBACjB,GAAG,MAAM,kBAAkB;AAAA,gBAC/B,EAAE,OAAO,MAAM,OAAO,WAAW,OAAO,MAAM;AAAA,gBAC9C,EAAE,OAAO,KAAK,SAAS,SAAU,KAAK,OAAO,OAAO;AAC5C,sBAAI,OAAO,MAAM,MAAM;AACvB,uBAAK,OAAO,QAAQ,KAAK,YAAY,UAAU;AAC/C,yBAAO,CAAC,QAAQ,GAAG;AAAA,gBACvB,GAAG,MAAM,QAAQ;AAAA,cACzB;AAAA,cACA,iBAAiB;AAAA,gBACb,EAAE,OAAO,WAAW,SAAS,SAAU,KAAK,OAAO,OAAO;AAClD,sBAAI,KAAK,MAAM,CAAC;AAChB,qBAAG,iBAAiB,IAAI,MAAM,GAAG,EAAE;AACnC,yBAAO,CAAC,MAAM,MAAM,CAAC;AAAA,gBACzB,GAAG,MAAM,eAAe;AAAA,gBAC5B;AAAA,gBACA,EAAE,OAAO,aAAa,SAAS,SAAU,KAAK,OAAO,OAAO;AACpD,sBAAI,IAAI,CAAC,KAAK;AACV,0BAAM,CAAC,EAAE,QAAQ,MAAM,CAAC;AAC5B,sBAAI,IAAI,CAAC,KAAK;AACV,0BAAM,CAAC,EAAE,aAAa;AAAA,gBAC9B,GAAG,MAAM,eAAe;AAAA,gBAC5B,EAAE,OAAO,wBAAwB,OAAO,IAAI,MAAM,eAAe;AAAA,cACrE;AAAA,YACJ,CAAC;AACD,mBAAOA,gBAAe,YAAY;AAAA,UACtC;AACA,UAAAA,gBAAe,UAAU,oBAAoB,SAAU,KAAK,YAAY;AACpE,mBAAO,KAAK,aAAa,EAAE,cAAc,KAAK,UAAU,EAAE,OAAO,IAAI,SAAU,GAAG;AAC9E,qBAAO,EAAE,SAAS;AAAA,YACtB,CAAC;AAAA,UACL;AACA,UAAAA,gBAAe,UAAU,mBAAmB,SAAU,QAAQ,MAAM,aAAa;AAC7E,gBAAI,QAAQ,KAAK,IAAI;AACjB,sBAAQ,KAAK,UAAU,MAAM,CAAC,GAAG,IAAI,KAAK;AAC9C,gBAAI,aAAa,KAAK,IAAI;AACtB,sBAAQ,KAAK,UAAU,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,CAAC,CAAC,KAAK;AACrE,mBAAO,KAAK,QAAQ,QAAQ,EAAE;AAC9B,gBAAI,CAAC,KAAK,UAAU,eAAe,IAAI;AACnC,qBAAO;AACX,gBAAI,QAAQ,KAAK,UAAU,IAAI;AAC/B,gBAAI,OAAO,SAAS;AAChB,sBAAQ,KAAK,UAAU,IAAI,EAAE,QAAQ,MAAM,WAAW;AAC1D,mBAAO,SAAS,OAAO,KAAK;AAAA,UAChC;AACA,UAAAA,gBAAe,UAAU,cAAc,SAAU,KAAK,IAAI,QAAQ;AAC9D,gBAAI,CAAC,GAAG;AACJ,qBAAO;AACX,gBAAI,OAAO,GAAG,QAAQ;AACtB,gBAAI,KAAK,GAAG;AACZ,iBAAK,IAAI,OAAO,IAAI,KAAK,QAAQ,WAAW,EAAE,CAAC;AAC/C,gBAAI,YAAY,OAAO,GAAG,OAAO,WAAW,KAAK,kBAAkB,GAAG,KAAK,cAAc,IAAI,GAAG;AAChG,gBAAI,QAAQ;AACZ,gBAAI,YAAY,IAAI,QAAQ,IAAI,WAAY;AACxC,kBAAI,UAAU,MAAM,UAAU;AAC9B,oBAAM,UAAU,KAAK,CAAC,EAAE,MAAM,KAAK,SAAS;AAC5C,kBAAI,WAAW,MAAM,iBAAiB,WAAW,MAAM;AACvD,kBAAI,cAAc;AAClB,uBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,oBAAIC,MAAK,SAAS,CAAC;AACnB,oBAAI,OAAOA,OAAM,UAAU;AACvB,2BAAS,CAAC,IAAI;AACd,sBAAIA,IAAG,cAAcA,IAAG,OAAO;AAC3B,wBAAI,OAAO,SAAS,IAAI,CAAC;AACzB,wBAAI,QAAQ,OAAO,QAAQ,UAAU;AACjC,0BAAIA,IAAG,cAAc;AACjB,iCAAS,CAAC,IAAI,KAAK,CAAC,EAAE,YAAY;AAAA;AAElC,iCAAS,CAAC,IAAI,KAAK,CAAC,EAAE,YAAY;AACtC,+BAAS,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC;AAAA,oBACnC;AAAA,kBACJ,WACSA,IAAG,YAAY;AACpB,kCAAcA,IAAG;AAAA,kBACrB;AAAA,gBACJ,WACS,eAAe,KAAK;AACzB,2BAAS,CAAC,IAAIA,IAAG,YAAY;AAAA,gBACjC,WACS,eAAe,KAAK;AACzB,2BAAS,CAAC,IAAIA,IAAG,YAAY;AAAA,gBACjC;AAAA,cACJ;AACA,oBAAM,UAAU,KAAK;AACrB,qBAAO,SAAS,KAAK,EAAE;AAAA,YAC3B,CAAC;AACD,mBAAO;AAAA,UACX;AACA,UAAAD,gBAAe,UAAU,mBAAmB,SAAU,KAAK,IAAI,QAAQ;AACnE,gBAAI,GAAG,kBAAkB;AACrB,qBAAO,IAAI,YAAY;AAC3B,gBAAI,GAAG,kBAAkB;AACrB,qBAAO,IAAI,YAAY;AAC3B,mBAAO;AAAA,UACX;AACA,UAAAA,gBAAe,UAAU,mBAAmB,SAAU,SAAS,QAAQ;AACnE,gBAAI,SAAS,CAAC;AACd,gBAAI,cAAc;AAClB,gBAAI,eAAe;AACnB,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,kBAAI,KAAK,QAAQ,CAAC;AAClB,kBAAI,OAAO,MAAM,UAAU;AACvB,uBAAO,KAAK,EAAE;AACd,oBAAI,MAAM,MAAM;AACZ,iCAAe;AACf,gCAAc;AAAA,gBAClB,WACS,cAAc;AACnB,gCAAc,OAAO,KAAK,EAAE,EAAE,CAAC;AAC/B,iCAAe,KAAK,KAAK,EAAE;AAAA,gBAC/B;AACA;AAAA,cACJ;AACA,kBAAI,CAAC;AACD;AACJ,6BAAe;AACf,kBAAI,GAAG,WAAW;AACd,oBAAI,IAAI,QAAQ,QAAQ,IAAI,IAAI,CAAC;AACjC,oBAAI,KAAK;AACL,sBAAI,QAAQ;AAChB,mBAAG,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC;AAC/B,oBAAI;AAAA,cACR;AACA,kBAAI,GAAG,MAAM;AACT,oBAAI,QAAQ,KAAK,iBAAiB,QAAQ,GAAG,MAAM,WAAW,IAAI;AAClE,oBAAI,GAAG;AACH,0BAAQ,KAAK,YAAY,OAAO,IAAI,MAAM;AAC9C,oBAAI,GAAG;AACH,0BAAQ,KAAK,iBAAiB,OAAO,IAAI,MAAM;AACnD,oBAAI,SAAS,CAAC,GAAG,OAAO;AACpB,yBAAO,KAAK,KAAK;AACjB,2BAAS,EAAE;AAAA,gBACf,WACS,CAAC,SAAS,GAAG,OAAO;AACzB,2BAAS,GAAG,KAAK;AAAA,gBACrB;AAAA,cACJ,WACS,GAAG,SAAS;AACjB,yBAAS,GAAG,OAAO;AAAA,cACvB,WACS,GAAG,aAAa,MAAM;AAC3B,uBAAO,KAAK,EAAE;AAAA,cAClB,WACS,GAAG,cAAc,MAAM;AAC5B,uBAAO,KAAK,EAAE;AAAA,cAClB;AAAA,YACJ;AACA,qBAAS,SAASC,KAAI;AAClB,kBAAI,KAAK,QAAQ,QAAQA,KAAI,IAAI,CAAC;AAClC,kBAAI,MAAM;AACN,oBAAI;AAAA,YACZ;AACA,mBAAO;AAAA,UACX;AACA,UAAAD,gBAAe,UAAU,2BAA2B,SAAU,QAAQ,aAAa;AAC/E,gBAAI,mBAAmB,mBAAmB,KAAK,MAAM,QAAQ,WAAW;AACxE,mBAAO,iBAAiB;AAAA,UAC5B;AACA,UAAAA,gBAAe,UAAU,4BAA4B,SAAU,QAAQ,aAAa,SAAS;AACzF,gBAAI,YAAY,QAAQ;AAAE,wBAAU,CAAC;AAAA,YAAG;AACxC,gBAAI,mBAAmB,mBAAmB,KAAK,MAAM,QAAQ,aAAa,OAAO;AACjF,gBAAI,QAAQ,OAAO,kBAAkB;AACrC,gBAAI,MAAM,OAAO,QAAQ,QAAQ,OAAO,iBAAiB,IAAI;AAC7D,gBAAI,iBAAiB,IAAI,eAAe,MAAM;AAC9C,gBAAI,cAAc,OAAO,0BAA0B,OAAO,UAAU;AACpE,2BAAe,YAAY,iBAAiB,UAAU,MAAM,OAAO,KAAK,WAAW;AAAA,UACvF;AACA,UAAAA,gBAAe,UAAU,gBAAgB,SAAU,QAAQ,aAAa,SAAS;AAC7E,gBAAI,YAAY,QAAQ;AAAE,wBAAU,CAAC;AAAA,YAAG;AACxC,gBAAI,OAAO;AACX,gBAAI,OAAO;AACP,qBAAO,KAAK,0BAA0B,QAAQ,aAAa,OAAO;AACtE,mBAAO,iBAAiB,WAAY;AAChC,mBAAK,0BAA0B,QAAQ,aAAa,OAAO;AAAA,YAC/D,GAAG,MAAM,EAAE,WAAW,KAAK,CAAC;AAC5B,gBAAI,OAAO;AACP,qBAAO,eAAe,QAAQ;AAAA,UACtC;AACA,UAAAA,gBAAe,UAAU,YAAY,SAAU,QAAQ;AACnD,gBAAI,QAAQ,OAAO,QAAQ,MAAM,OAAO;AACxC,oBAAQ,MAAM,MAAM,GAAG,EAAE,IAAI;AAC7B,gBAAI,UAAU,UAAU,UAAU,OAAO;AACrC,kBAAI,UAAU,SAAS,CAAC,OAAO,QAAQ,MAAM;AACzC,wBAAQ;AACZ,kBAAI,IAAI,OAAO,kBAAkB;AACjC,kBAAI,QAAQ,OAAO,QAAQ,SAAS,EAAE,GAAG;AACzC,kBAAI,OAAO,UAAU,UAAU;AAC3B,wBAAQ,MAAM,CAAC;AAAA,cACnB;AACA,kBAAI,MAAM,WAAW;AACjB,oBAAI,MAAM,UAAU,GAAG,CAAC,KAAK;AACzB,0BAAQ;AAAA,yBACH,MAAM,UAAU,GAAG,CAAC,KAAK;AAC9B,0BAAQ;AAAA,yBACH,MAAM,UAAU,GAAG,CAAC,KAAK;AAC9B,0BAAQ;AAAA,cAChB;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AACA,UAAAA,gBAAe,UAAU,kBAAkB,SAAU,QAAQ;AACzD,gBAAI,QAAQ,KAAK,UAAU,MAAM;AACjC,gBAAI,SAAS,CAAC,KAAK;AACnB,gBAAI,aAAa,KAAK;AACtB,gBAAI,WAAW,KAAK,KAAK,WAAW,KAAK,EAAE,eAAe;AACtD,qBAAO,KAAK,MAAM,QAAQ,WAAW,KAAK,EAAE,aAAa;AAAA,YAC7D;AACA,mBAAO,KAAK,GAAG;AACf,mBAAO;AAAA,UACX;AACA,UAAAA,gBAAe,UAAU,gBAAgB,SAAU,QAAQ,SAAS;AAChE,gBAAI,OAAO;AACX,gBAAI,SAAS,OAAO,iBAAiB,WAAY;AAC7C,qBAAO,KAAK,0BAA0B,QAAQ,OAAO;AAAA,YACzD,GAAG,MAAM,EAAE,WAAW,KAAK,CAAC;AAC5B,gBAAI,UAAU,OAAO;AACjB,qBAAO,eAAe,QAAQ;AAClC,mBAAO;AAAA,UACX;AACA,UAAAA,gBAAe,UAAU,4BAA4B,SAAU,QAAQ,SAAS;AAC5E,gBAAI,SAAS,OAAO,kBAAkB;AACtC,gBAAI,OAAO,OAAO,QAAQ,QAAQ,OAAO,GAAG;AAC5C,gBAAI,SAAS,KAAK,UAAU,GAAG,OAAO,MAAM;AAC5C,gBAAI,QAAQ,KAAK,OAAO,OAAO,MAAM;AACrC,gBAAI,aAAa,KAAK;AACtB,gBAAI;AACJ,iBAAK,gBAAgB,MAAM,EAAE,KAAK,SAAU,OAAO;AAC/C,kBAAI,WAAW,WAAW,KAAK;AAC/B,kBAAI;AACA,0BAAU,KAAK,oBAAoB,UAAU,QAAQ,KAAK;AAC9D,qBAAO,CAAC,CAAC;AAAA,YACb,GAAG,IAAI;AACP,gBAAI,CAAC;AACD,qBAAO;AACX,gBAAI,WAAW,QAAQ;AACnB,qBAAO;AACX,mBAAO,QAAQ,IAAI,aAAa,OAAO,KAAK,OAAO,SAAS,QAAQ,cAAc,QAAQ,OAAO,SAAS,QAAQ,aAAa,MAAM;AACrI,iBAAK,UAAU,MAAM,QAAQ;AAC7B,iBAAK,UAAU,MAAM,QAAQ;AAC7B,iBAAK,0BAA0B,QAAQ,QAAQ,OAAO;AACtD,iBAAK,UAAU,MAAM,KAAK,UAAU,MAAM;AAC1C,mBAAO;AAAA,UACX;AACA,UAAAA,gBAAe,UAAU,sBAAsB,SAAU,aAAa,QAAQ,OAAO;AACjF,qBAAS,IAAI,YAAY,QAAQ,OAAM;AACnC,kBAAI,IAAI,YAAY,CAAC;AACrB,kBAAI,EAAE,WAAW,CAAC,EAAE,QAAQ,KAAK,MAAM;AACnC;AACJ,kBAAI,EAAE,SAAS,CAAC,EAAE,MAAM,KAAK,KAAK;AAC9B;AACJ,kBAAI,CAAC,EAAE,WAAW,CAAC,EAAE;AACjB;AACJ,gBAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,KAAK,MAAM,IAAI,CAAC,EAAE;AACxD,gBAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE;AAClD,gBAAE,gBAAgB,EAAE,YAAY,EAAE,UAAU,KAAK,MAAM,EAAE,CAAC,IAAI;AAC9D,gBAAE,eAAe,EAAE,eAAe,EAAE,aAAa,KAAK,KAAK,EAAE,CAAC,IAAI;AAClE,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,UAAAA,gBAAe,UAAU,WAAW,SAAU,UAAU,OAAO;AAC3D,gBAAI,aAAa,KAAK;AACtB,gBAAI,iBAAiB,KAAK;AAC1B,gBAAI,OAAO;AACX,gBAAI,CAAC;AACD,yBAAW,CAAC;AAChB,qBAAS,WAAW,KAAK;AACrB,kBAAI,OAAO,CAAC,uBAAuB,KAAK,GAAG;AACvC,sBAAM,QAAQ,MAAM;AACxB,qBAAO,OAAO;AAAA,YAClB;AACA,qBAAS,cAAc,IAAI,OAAO,SAAS;AACvC,mBAAK,WAAW,EAAE;AAClB,sBAAQ,WAAW,KAAK;AACxB,kBAAI,SAAS;AACT,qBAAK,QAAQ;AACb,oBAAI,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK;AAC3B,uBAAK,KAAK;AAAA,cAClB,OACK;AACD,qBAAK,KAAK;AACV,oBAAI,MAAM,GAAG,CAAC,KAAK;AACf,uBAAK,MAAM;AAAA,cACnB;AACA,qBAAO,IAAI,OAAO,EAAE;AAAA,YACxB;AACA,qBAAS,WAAW,GAAG;AACnB,kBAAI,CAAC,EAAE;AACH,kBAAE,QAAQ,SAAS;AACvB,sBAAQ,EAAE;AACV,kBAAI,CAAC,WAAW,KAAK,GAAG;AACpB,2BAAW,KAAK,IAAI,CAAC;AACrB,+BAAe,KAAK,IAAI,CAAC;AAAA,cAC7B;AACA,kBAAI,MAAM,eAAe,KAAK;AAC9B,kBAAI,EAAE,MAAM;AACR,oBAAI,MAAM,IAAI,EAAE,IAAI;AACpB,oBAAI;AACA,uBAAK,WAAW,GAAG;AACvB,oBAAI,EAAE,IAAI,IAAI;AAAA,cAClB;AACA,yBAAW,KAAK,EAAE,KAAK,CAAC;AACxB,kBAAI,EAAE;AACF,kBAAE,aAAa,EAAE;AACrB,kBAAI,CAAC,EAAE,WAAW,EAAE;AAChB,kBAAE,UAAU,MAAM,QAAQ,EAAE,IAAI,IAAI,EAAE,KAAK,KAAK,IAAI,IAAI,EAAE;AAC9D,kBAAI,EAAE,cAAc,CAAC,EAAE,SAAS;AAC5B,oBAAI,CAAC,EAAE,SAAS,MAAM,KAAK,EAAE,UAAU;AACnC,oBAAE,QAAQ;AACd,kBAAE,UAAU,KAAK,aAAa,EAAE,UAAU;AAAA,cAC9C;AACA,kBAAI,CAAC,EAAE,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,cAAc,CAAC,EAAE;AAC9C;AACJ,gBAAE,UAAU,cAAc,EAAE,SAAS,EAAE,OAAO,IAAI;AAClD,gBAAE,YAAY,IAAI,OAAO,EAAE,OAAO;AAClC,gBAAE,QAAQ,cAAc,EAAE,YAAY,EAAE,UAAU,IAAI;AACtD,gBAAE,eAAe,IAAI,OAAO,EAAE,UAAU;AAAA,YAC5C;AACA,gBAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,uBAAS,QAAQ,UAAU;AAAA,YAC/B,OACK;AACD,qBAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACzC,2BAAW,SAAS,GAAG,CAAC;AAAA,cAC5B,CAAC;AAAA,YACL;AACA,iBAAK,QAAQ,oBAAoB,EAAE,MAAa,CAAC;AAAA,UACrD;AACA,UAAAA,gBAAe,UAAU,aAAa,SAAU,UAAU,OAAO;AAC7D,gBAAI,aAAa,KAAK;AACtB,gBAAI,iBAAiB,KAAK;AAC1B,qBAAS,cAAc,GAAG;AACtB,kBAAI,UAAU,eAAe,EAAE,SAAS,KAAK;AAC7C,kBAAI,WAAW,QAAQ,EAAE,IAAI,GAAG;AAC5B,uBAAO,QAAQ,EAAE,IAAI;AACrB,oBAAI,MAAM,WAAW,EAAE,SAAS,KAAK;AACrC,oBAAI,IAAI,OAAO,IAAI,QAAQ,CAAC;AAC5B,oBAAI,KAAK;AACL,sBAAI,OAAO,GAAG,CAAC;AAAA,cACvB;AAAA,YACJ;AACA,gBAAI,SAAS;AACT,4BAAc,QAAQ;AAAA,qBACjB,MAAM,QAAQ,QAAQ;AAC3B,uBAAS,QAAQ,aAAa;AAAA,UACtC;AACA,UAAAA,gBAAe,UAAU,mBAAmB,SAAU,KAAK;AACvD,kBAAM,IAAI,QAAQ,OAAO,EAAE;AAC3B,gBAAI,OAAO,CAAC,GAAuB,UAAU,CAAC;AAC9C,gBAAI,KAAK;AACT,gBAAI;AACJ,mBAAO,IAAI,GAAG,KAAK,GAAG,GAAG;AACrB,kBAAI,EAAE,CAAC,GAAG;AACN,oBAAI;AACA,4BAAU,KAAK,MAAM,EAAE,CAAC,CAAC;AACzB,uBAAK,KAAK,OAAO;AAAA,gBACrB,SACO,GAAG;AAAA,gBAAE;AAAA,cAChB;AACA,kBAAI,EAAE,CAAC,GAAG;AACN,wBAAQ,UAAU,EAAE,CAAC,EAAE,QAAQ,SAAS,EAAE;AAC1C,qBAAK,KAAK,OAAO;AACjB,0BAAU,CAAC;AAAA,cACf,OACK;AACD,oBAAI,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC;AACzB,oBAAI,OAAO,SAAS;AAChB,sBAAI,UAAU;AACd,0BAAQ,QAAQ,QAAQ,KAAK,GAAG,EAAE,CAAC;AACnC,0BAAQ,UAAU,QAAQ,KAAK,GAAG,EAAE,CAAC;AACrC,0BAAQ,aAAa,QAAQ,KAAK,GAAG,EAAE,CAAC;AACxC,0BAAQ,WAAW,QAAQ,KAAK,GAAG,EAAE,CAAC;AAAA,gBAC1C,WACS,OAAO,WAAW;AACvB,0BAAQ,aAAa,IAAI,MAAM,MAAM,EAAE,CAAC;AACxC,sBAAI,CAAC,QAAQ;AACT,4BAAQ,OAAO;AAAA,gBACvB,WACS,KAAK;AACV,0BAAQ,GAAG,IAAI;AAAA,gBACnB;AAAA,cACJ;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AACA,UAAAA,gBAAe,UAAU,mBAAmB,SAAU,MAAM,QAAQ;AAChE,gBAAI,aAAa,KAAK;AACtB,gBAAI;AACJ,iBAAK,gBAAgB,MAAM,EAAE,KAAK,SAAU,OAAO;AAC/C,kBAAI,WAAW,WAAW,KAAK;AAC/B,kBAAI;AACA,0BAAU,SAAS,IAAI;AAC3B,qBAAO,CAAC,CAAC;AAAA,YACb,GAAG,IAAI;AACP,mBAAO;AAAA,UACX;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,UAAI,UAAU,eAAe,WAAW,YAAY;AACpD,UAAI,qBAAqB,SAAU,QAAQ,aAAa,SAAS;AAC7D,YAAI,YAAY,QAAQ;AAAE,oBAAU,CAAC;AAAA,QAAG;AACxC,YAAI,SAAS,OAAO,kBAAkB;AACtC,YAAI,OAAO,OAAO,QAAQ,QAAQ,OAAO,GAAG;AAC5C,YAAI,YAAY,OAAO,QAAQ,aAAa;AAC5C,YAAI,eAAe,KAAK,MAAM,MAAM,EAAE,CAAC;AACvC,YAAI,OAAO,SAAS,aAAa;AAC7B,yBAAe,aAAa,MAAM,GAAG,OAAO,MAAM;AACtD,sBAAc,YAAY,QAAQ,OAAO,EAAE;AAC3C,YAAI,SAAS,KAAK,kBAAkB,WAAW;AAC/C,iBAAS,KAAK,iBAAiB,QAAQ,MAAM;AAC7C,iBAAS,OAAO,IAAI,SAAU,GAAG;AAC7B,cAAI,KAAK,QAAQ,CAAC,QAAQ;AACtB,mBAAO,IAAI;AACf,cAAI,OAAO,KAAK;AACZ,mBAAO,EAAE,QAAQ,OAAO,SAAS;AACrC,iBAAO;AAAA,QACX,CAAC;AACD,YAAI,WAAW,CAAC;AAChB,eAAO,QAAQ,SAAUE,IAAGC,IAAG;AAC3B,cAAI,OAAOD,MAAK;AACZ;AACJ,cAAIE,MAAKF,GAAE;AACX,cAAIG,MAAK,SAASD,GAAE;AACpB,cAAI,CAACC,KAAI;AACL,YAAAA,MAAK,SAASD,GAAE,IAAI,CAAC;AACrB,YAAAC,IAAG,QAAQD;AACX,YAAAC,IAAG,QAAQ;AACX,YAAAA,IAAG,UAAU,CAAC;AAAA,UAClB;AACA,cAAIA,IAAG,QAAQH,EAAC,MAAM;AAClB;AACJ,cAAIA,GAAE,WAAW,CAACG,IAAG;AACjB,YAAAA,IAAG,UAAUH,GAAE;AACnB,UAAAG,IAAG,KAAKH,EAAC;AACT,cAAII,MAAK,OAAO,QAAQJ,IAAGC,KAAI,CAAC;AAChC,cAAIG,QAAO;AACP;AACJ,cAAIC,SAAQ,OAAO,MAAMJ,KAAI,GAAGG,GAAE;AAClC,cAAI,WAAWC,OAAM,KAAK,SAAU,GAAG;AAAE,mBAAO,OAAO,MAAM;AAAA,UAAU,CAAC;AACxE,cAAI,YAAY,CAACF,IAAG,OAAO;AACvB,YAAAA,IAAG,QAAQE;AAAA,UACf,WACSA,OAAM,WAAW,CAACF,IAAG,SAAS,OAAOA,IAAG,UAAU,WAAW;AAClE,YAAAA,IAAG,QAAQE,OAAM,KAAK,EAAE;AAAA,UAC5B;AAAA,QACJ,CAAC;AACD,iBAAS,QAAQ,SAAUF,KAAI;AAAE,UAAAA,IAAG,SAAS;AAAA,QAAG,CAAC;AACjD,YAAI,YAAY,CAAC;AACjB,iBAAS,UAAU,KAAK;AACpB,cAAI,OAAO,CAAC;AACZ,mBAASF,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACjC,gBAAID,KAAI,IAAIC,EAAC;AACb,gBAAI,OAAOD,MAAK,UAAU;AACtB,kBAAI,UAAUA,GAAE,SAAS;AACrB;AACJ,kBAAI,IAAI,IAAI,YAAYA,IAAGC,KAAI,CAAC;AAChC,cAAAD,KAAI,KAAK,CAAC,KAAK,EAAE,WAAWA,GAAE,UAAU;AAAA,YAC5C;AACA,iBAAKC,EAAC,IAAID;AAAA,UACd;AACA,iBAAO;AAAA,QACX;AACA,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,cAAI,IAAI,OAAO,CAAC;AAChB,cAAI,OAAO,KAAK;AACZ;AACJ,cAAI,KAAK,EAAE;AACX,cAAI,KAAK,SAAS,EAAE;AACpB,cAAI,KAAK,OAAO,QAAQ,GAAG,IAAI,CAAC;AAChC,cAAI,UAAU,EAAE,GAAG;AACf,gBAAI,UAAU,EAAE,MAAM,GAAG;AACrB,qBAAO,UAAU,EAAE;AACnB,qBAAO,KAAK,SAAS,EAAE,QAAQ,SAAU,UAAU;AAC/C,mBAAG,QAAQ,QAAQ,IAAI;AAAA,cAC3B,CAAC;AAAA,YACL;AACA;AAAA,UACJ;AACA,oBAAU,EAAE,IAAI;AAChB,cAAI,QAAQ,GAAG;AACf,cAAI,OAAO,UAAU;AACjB,oBAAQ,UAAU,KAAK;AAAA,mBAClB,EAAE;AACP,oBAAQ,KAAK,YAAY,OAAO,GAAG,MAAM;AAC7C,iBAAO,OAAO,MAAM,QAAQ,CAAC,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,CAAC;AACzE,cAAI,GAAG,QAAQ,CAAC,MAAM;AAClB,eAAG,KAAK,CAAC;AAAA,QACjB;AACA,YAAI,MAAM,GAAG,SAAS;AACtB,YAAI,OAAO;AACX,eAAO,QAAQ,SAAU,GAAG;AACxB,cAAI,OAAO,MAAM,UAAU;AACvB,gBAAI,QAAQ,EAAE,MAAM,IAAI;AACxB,gBAAI,MAAM,SAAS,GAAG;AAClB,uBAAS,MAAM,MAAM,SAAS,CAAC,EAAE;AACjC,qBAAO,MAAM,SAAS;AAAA,YAC1B;AAEI,wBAAU,EAAE;AAChB,oBAAQ;AAAA,UACZ,WACS,GAAG;AACR,gBAAI,CAAC,EAAE;AACH,gBAAE,QAAQ,EAAE,KAAU,OAAe;AAAA;AAErC,gBAAE,MAAM,EAAE,KAAU,OAAe;AAAA,UAC3C;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AACA,UAAI;AAAA;AAAA,QAAgC,WAAY;AAC5C,mBAASM,gBAAe,QAAQ;AAC5B,iBAAK,QAAQ;AACb,iBAAK,SAAS,CAAC;AACf,iBAAK,WAAW,CAAC;AACjB,gBAAI,OAAO;AACP,qBAAO,OAAO;AAClB,mBAAO,iBAAiB;AACxB,iBAAK,YAAY,KAAK,SAAS,KAAK,IAAI;AACxC,iBAAK,qBAAqB,KAAK,YAAY,KAAK,kBAAkB,KAAK,IAAI,CAAC,EAAE;AAC9E,iBAAK,mBAAmB,KAAK,gBAAgB,KAAK,IAAI;AACtD,iBAAK,eAAe,KAAK,YAAY,KAAK,IAAI;AAC9C,iBAAK,OAAO,MAAM;AAAA,UACtB;AACA,UAAAA,gBAAe,UAAU,SAAS,SAAU,QAAQ;AAChD,iBAAK,gBAAgB;AACrB,iBAAK,kBAAkB;AACvB,iBAAK,SAAS;AACd,iBAAK,UAAU,OAAO;AACtB,iBAAK,OAAO,GAAG,UAAU,KAAK,SAAS;AACvC,iBAAK,OAAO,GAAG,mBAAmB,KAAK,kBAAkB;AACzD,iBAAK,OAAO,GAAG,iBAAiB,KAAK,gBAAgB;AACrD,iBAAK,OAAO,SAAS,GAAG,aAAa,KAAK,YAAY;AACtD,iBAAK,OAAO,WAAW,mBAAmB,KAAK,eAAe;AAAA,UAClE;AACA,UAAAA,gBAAe,UAAU,SAAS,WAAY;AAC1C,iBAAK,SAAS,QAAQ,KAAK,sBAAsB,IAAI;AACrD,iBAAK,OAAO,SAAS;AACrB,iBAAK,SAAS,SAAS;AACvB,iBAAK,kBAAkB;AACvB,iBAAK,OAAO,IAAI,UAAU,KAAK,SAAS;AACxC,iBAAK,OAAO,IAAI,mBAAmB,KAAK,kBAAkB;AAC1D,iBAAK,OAAO,IAAI,iBAAiB,KAAK,gBAAgB;AACtD,iBAAK,OAAO,SAAS,IAAI,aAAa,KAAK,YAAY;AACvD,iBAAK,OAAO,WAAW,sBAAsB,KAAK,eAAe;AACjE,iBAAK,OAAO,iBAAiB;AAC7B,iBAAK,UAAU;AACf,iBAAK,SAAS;AAAA,UAClB;AACA,UAAAA,gBAAe,UAAU,WAAW,SAAU,OAAO;AACjD,gBAAI,WAAW,MAAM,OAAO,CAAC,KAAK;AAClC,gBAAI,kBAAkB,KAAK,mBAAmB,CAAC;AAC/C,gBAAI,UAAU,gBAAgB,WAAW,CAAC;AAC1C,gBAAI,WAAW,KAAK,SAAS,MAAM;AACnC,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,kBAAI,KAAK,SAAS,CAAC;AACnB,kBAAI,SAAS,MAAM,mBAAmB,QAAQ,GAAG,KAAK;AACtD,iBAAG,UAAU,QAAQ,SAAS,IAAI;AAClC,kBAAI,MAAM,UAAU,YAAY,OAAO,iBAAiB;AACpD,oBAAI,eAAe,GAAG,WAAW,GAAG,QAAQ,gBAAgB,KAAK;AACjE,oBAAI,aAAa,GAAG,UAAU,WAAW,MAAM,OAAO,YAAY;AAClE,6BAAa,aAAa,IAAI,CAAC,aAAa,IAAI,aAAa;AAC7D,oBAAI,WAAW,GAAG,UAAU,WAAW,MAAM,KAAK,YAAY;AAC9D,2BAAW,WAAW,IAAI,CAAC,WAAW,IAAI,WAAW;AACrD,oBAAI,WAAW,GAAG,UAAU,OAAO,MAAM,YAAY,QAAQ;AAC7D,yBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ;AACjC,uBAAK,YAAY,SAAS,CAAC,CAAC;AAAA,cACpC;AACA,iBAAG,UAAU,UAAU,KAAK;AAAA,YAChC;AACA,gBAAI,UAAU,KAAK;AACnB,gBAAI,CAAC,KAAK,aAAa,YAAY,QAAQ,UAAU,KAAK,KAAK,CAAC,QAAQ,SAAS;AAC7E,mBAAK,OAAO;AAAA,UACpB;AACA,UAAAA,gBAAe,UAAU,qBAAqB,WAAY;AACtD,gBAAI,KAAK,KAAK;AACd,gBAAI,CAAC,MAAM,CAAC,GAAG,mBAAmB,CAAC,GAAG;AAClC;AACJ,iBAAK,YAAY;AACjB,gBAAI,UAAU,KAAK;AACnB,gBAAI,OAAO,QAAQ,aAAa,GAAG,cAAc;AACjD,qBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,kBAAI,QAAQ,GAAG,CAAC;AAChB,kBAAI,CAAC,MAAM;AACP;AACJ,kBAAI,WAAW,MAAM;AACrB,kBAAI,MAAMV,SAAQ,eAAe,YAAY,MAAM,UAAU,KAAK,MAAM;AACxE,sBAAQ,QAAQ,OAAO,GAAG;AAAA,YAC9B;AACA,iBAAK,YAAY;AAAA,UACrB;AACA,UAAAU,gBAAe,UAAU,cAAc,SAAU,GAAG;AAChD,gBAAI,EAAE,WAAW,CAAC,EAAE,QAAQ;AACxB,mBAAK,mBAAmB;AAAA,UAChC;AACA,UAAAA,gBAAe,UAAU,oBAAoB,WAAY;AACrD,gBAAI,CAAC,KAAK;AACN;AACJ,gBAAI,OAAO,KAAK,OAAO,UAAU;AACjC,gBAAI,SAAS,KAAK,OAAO,UAAU;AACnC,gBAAI,UAAU,KAAK,OAAO,UAAU,QAAQ;AAC5C,qBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AACzC,kBAAI,KAAK,OAAO,CAAC,EAAE;AACf;AACJ,kBAAI,eAAe,KAAK,OAAO,CAAC,EAAE,SAAS,KAAK,KAAK,KAAK,MAAM;AAChE,kBAAI,iBAAiB,WAAW,KAAK,OAAO,CAAC,EAAE,SAAS,OAAO,KAAK,OAAO,MAAM;AACjF,kBAAI,gBAAgB;AAChB;AAAA,YACR;AACA,iBAAK,OAAO;AAAA,UAChB;AACA,UAAAA,gBAAe,UAAU,kBAAkB,WAAY;AACnD,iBAAK,OAAO;AAAA,UAChB;AACA,UAAAA,gBAAe,UAAU,UAAU,SAAU,KAAK;AAC9C,gBAAI,MAAM,KAAK,SAAS;AACxB,gBAAI,QAAQ,KAAK,SAAS,OAAO;AACjC,oBAAQ,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,GAAG;AACxC,gBAAI,SAAS;AACT,sBAAQ;AACZ,iBAAK,cAAc,KAAK;AACxB,iBAAK,qBAAqB;AAC1B,gBAAI,UAAU,GAAG;AACb,mBAAK,OAAO;AAAA,YAChB;AAAA,UACJ;AACA,UAAAA,gBAAe,UAAU,gBAAgB,SAAU,OAAO;AACtD,iBAAK,gBAAgB;AACrB,gBAAI,KAAK,KAAK,SAAS,KAAK,KAAK;AACjC,gBAAI;AACA,mBAAK,kBAAkB,EAAE;AAC7B,iBAAK,QAAQ;AACb,iBAAK,KAAK,SAAS,KAAK,KAAK;AAC7B,gBAAI,CAAC,MAAM,CAAC,GAAG;AACX;AACJ,iBAAK,kBAAkB;AACvB,gBAAI,QAAQ,GAAG,kBAAkB;AACjC,gBAAI,GAAG;AACH,oBAAM,SAAS,MAAM;AACzB,gBAAI,CAAC,KAAK,OAAO,wBAAwB;AACrC,kBAAI,MAAM,KAAK,OAAO;AACtB,kBAAI,cAAc,KAAK;AACvB,uBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,oBAAI,GAAG,mBAAmB,GAAG,CAAC,EAAE;AAC5B;AACJ,oBAAI,SAAS,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI;AAAA,cACpC;AAAA,YACJ,OACK;AACD,mBAAK,OAAO,UAAU,kBAAkB,KAAK;AAAA,YACjD;AACA,iBAAK,OAAO,WAAW,mBAAmB,KAAK,eAAe;AAC9D,gBAAI,KAAK,mBAAmB,KAAK,gBAAgB;AAC7C,mBAAK,OAAO,YAAY,qBAAqB,EAAE,SAAS,KAAK,gBAAgB,QAAQ,CAAC;AAAA,UAC9F;AACA,UAAAA,gBAAe,UAAU,cAAc,SAAU,UAAU,OAAO,KAAK;AACnE,gBAAI,UAAU,KAAK,WAAW,CAAC,KAAK,OAAO,UAAU,mBAAmB;AACxE,gBAAI,CAAC,KAAK;AACN,mBAAK,gBAAgB,CAAC;AAC1B,gBAAI,CAAC,SAAS,CAAC,GAAG;AACd,kBAAI,IAAI,MAAM,WAAW,KAAK,GAAG;AACjC,2BAAa,EAAE,OAAO,KAAK;AAC3B,2BAAa,EAAE,KAAK,KAAK;AACzB,uBAAS,CAAC,IAAI,CAAC,CAAC;AAChB,uBAAS,CAAC,EAAE,QAAQ;AAAA,YACxB;AACA,gBAAI,IAAI,KAAK;AACb,gBAAI,MAAM,CAAC,IAAI,GAAG,CAAC;AACnB,gBAAI,SAAS,KAAK;AAClB,gBAAI,YAAY,KAAK,aAAa,KAAK,aAAa,KAAK;AACzD,qBAAS,QAAQ,SAAU,IAAI,OAAO;AAClC,kBAAI,OAAO,KAAK,cAAc,KAAK,KAAK;AACxC,mBAAK,YAAY;AACjB,uBAASL,KAAI,GAAGA,KAAI,GAAG,QAAQA,MAAK;AAChC,oBAAID,KAAI,GAAGC,EAAC;AACZ,oBAAI,QAAQ,MAAM,WAAWD,GAAE,OAAOA,GAAE,OAAOA,GAAE,KAAK;AACtD,0BAAU,MAAM,OAAO,KAAK;AAC5B,0BAAU,MAAM,KAAK,KAAK;AAC1B,sBAAM,WAAWA;AACjB,sBAAM,UAAU;AAChB,uBAAO,KAAK,KAAK;AACjB,oBAAI,QAAQ;AACR,uBAAK,QAAQ,KAAK;AAAA;AAElB,uBAAKC,EAAC,IAAI;AACd,oBAAID,GAAE,aAAc,KAAK,kBAAkB,SAAU;AACjD,wBAAM,SAAS;AACf,uBAAK,kBAAkB;AAAA,gBAC3B,WACS,CAAC,KAAK;AACX,uBAAK,iBAAiB;AAAA,cAC9B;AACA,kBAAI,CAAC,KAAK;AACN,qBAAK,kBAAkB;AAC3B,kBAAI,SAAS,IAAI;AACb,oBAAI,KAAK,IAAI;AACb,qBAAK,cAAc,KAAK,IAAI;AAAA,cAChC;AACA,mBAAK,kBAAkB,IAAI;AAC3B,mBAAK,YAAY,KAAK,aAAa,IAAI,UAAU;AACjD,mBAAK,UAAU,QAAQ;AACvB,mBAAK,UAAU,QAAQ,IAAI;AAAA,YAC/B,GAAG,IAAI;AACP,gBAAI,IAAI,SAAS,GAAG;AAChB,kBAAI,KAAK,SAAS;AACd,oBAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;AAChC,mBAAK,SAAS,OAAO,MAAM,KAAK,UAAU,GAAG;AAAA,YACjD;AAAA,UACJ;AACA,UAAAM,gBAAe,UAAU,oBAAoB,SAAU,IAAI;AACvD,gBAAI,UAAU,KAAK;AACnB,eAAG,QAAQ,SAAU,OAAO;AACxB,kBAAI,CAAC,MAAM;AACP,sBAAM,WAAW,QAAQ,UAAU,OAAO,sBAAsB,MAAM;AAAA,YAC9E,CAAC;AAAA,UACL;AACA,UAAAA,gBAAe,UAAU,uBAAuB,SAAU,IAAI;AAC1D,gBAAI,UAAU,KAAK;AACnB,eAAG,QAAQ,SAAU,OAAO;AACxB,sBAAQ,aAAa,MAAM,QAAQ;AACnC,oBAAM,WAAW;AAAA,YACrB,CAAC;AAAA,UACL;AACA,UAAAA,gBAAe,UAAU,uBAAuB,WAAY;AACxD,gBAAI,CAAC,KAAK;AACN;AACJ,gBAAI,mBAAmB,KAAK,gBAAgB;AAC5C,gBAAI,KAAK,gBAAgB,UAAU,GAAG;AAClC;AAAA,YACJ;AACA,iBAAK,SAAS,QAAQ,SAAU,IAAI;AAChC,kBAAI,GAAG,cAAc;AACjB,qBAAK,kBAAkB,EAAE;AAAA;AAEzB,qBAAK,qBAAqB,EAAE;AAAA,YACpC,GAAG,IAAI;AAAA,UACX;AACA,UAAAA,gBAAe,UAAU,cAAc,SAAU,OAAO;AACpD,gBAAI,IAAI,MAAM,QAAQ,QAAQ,KAAK;AACnC,gBAAI,KAAK;AACL,oBAAM,QAAQ,OAAO,GAAG,CAAC;AAC7B,gBAAI,KAAK,OAAO,QAAQ,KAAK;AAC7B,gBAAI,KAAK;AACL,mBAAK,OAAO,OAAO,GAAG,CAAC;AAC3B,gBAAI,MAAM,QAAQ,UAAU,OAAO,QAAQ,KAAK;AAChD,gBAAI,KAAK;AACL,oBAAM,QAAQ,OAAO,GAAG,CAAC;AAC7B,iBAAK,QAAQ,aAAa,MAAM,QAAQ;AACxC,gBAAI,CAAC,MAAM,QAAQ,QAAQ;AACvB,kBAAI,KAAK,SAAS,QAAQ,MAAM,OAAO;AACvC,kBAAI,KAAK;AACL,qBAAK,SAAS,OAAO,GAAG,CAAC;AAC7B,kBAAI,CAAC,KAAK,SAAS;AACf,qBAAK,OAAO;AAAA,YACpB;AAAA,UACJ;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,qBAAe,UAAU,kBAAkB,IAAI,YAAY;AAC3D,qBAAe,UAAU,gBAAgB,SAAS;AAAA,QAC9C,OAAO,SAAU,QAAQ;AACrB,cAAIV,SAAQ,kBAAkBA,SAAQ,eAAe,cAAc,MAAM;AACrE;AACJ,iBAAO,eAAe,QAAQ,CAAC;AAC/B,iBAAO,SAAS,qBAAqB;AAAA,QACzC;AAAA,QACA,aAAa,SAAU,QAAQ;AAC3B,iBAAO,eAAe,QAAQ,EAAE;AAChC,iBAAO,SAAS,qBAAqB;AAAA,QACzC;AAAA,QACA,OAAO,SAAU,QAAQ;AACrB,iBAAO,eAAe,OAAO;AAAA,QACjC;AAAA,MACJ,CAAC;AACD,UAAI,YAAY,SAAU,OAAO,MAAM;AACnC,YAAI,MAAM,OAAO;AACb,gBAAM,UAAU,KAAK;AACzB,cAAM,OAAO,KAAK;AAAA,MACtB;AACA,UAAI,eAAe,SAAU,OAAO,OAAO;AACvC,YAAI,MAAM,OAAO,MAAM;AACnB,gBAAM,UAAU,MAAM;AAC1B,cAAM,OAAO,MAAM;AAAA,MACvB;AACA,UAAI,gBAAgB,qNAAqN,gBAAgB,KAAK;AAC9P,MAAAA,SAAQ,iBAAiB,IAAI,eAAe;AAC5C,UAAI,SAASD,SAAQ,UAAU,EAAE;AACjC,OAAC,WAAY;AACT,aAAK,gBAAgB,SAAU,SAAS,SAAS;AAC7C,iBAAOC,SAAQ,eAAe,cAAc,MAAM,SAAS,OAAO;AAAA,QACtE;AACA,aAAK,gBAAgB,SAAU,SAAS;AACpC,iBAAOA,SAAQ,eAAe,cAAc,MAAM,OAAO;AAAA,QAC7D;AAAA,MACJ,GAAG,KAAK,OAAO,SAAS;AAAA,IAExB,CAAC;AAED,QAAI,OAAO,0BAAyB,CAAC,WAAU,WAAU,UAAS,wBAAuB,cAAa,aAAY,iBAAgB,gBAAe,eAAc,cAAa,mBAAmB,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AACrO,UAAI,WAAWF,SAAQ,qBAAqB,EAAE;AAC9C,UAAI,SAASA,SAAQ,WAAW,EAAE;AAClC,UAAI,QAAQA,SAAQ,UAAU,EAAE;AAChC,UAAI,QAAQA,SAAQ,cAAc;AAClC,UAAI,OAAOA,SAAQ,aAAa;AAChC,UAAI,MAAMA,SAAQ,YAAY;AAC9B,UAAI,MAAMA,SAAQ,WAAW,EAAE;AAC/B,UAAI,YAAYA,SAAQ,oBAAoB;AAC5C,UAAI,YAAY,SAAU,OAAO;AAC7B,eAAO,mBAAmB,OAAO,KAAK;AAAA,MAC1C;AACA,UAAI,gBAAgB,UAAU,WAAW,SAAS;AAClD,UAAI,iBAAiB,UAAU,WAAW,aAAa;AACvD,UAAI,kBAAkB,UAAU,WAAW,iBAAiB;AAC5D,UAAI,oBAAoB,SAAU,IAAI;AAClC,YAAI,WAAW,IAAI,SAAS,EAAE;AAC9B,iBAAS,YAAY;AACrB,YAAI,SAAS,IAAI,OAAO,QAAQ;AAChC,eAAO,uBAAuB,KAAK;AACnC,eAAO,mBAAmB,KAAK;AAC/B,eAAO,SAAS,cAAc,KAAK;AACnC,eAAO,SAAS,uBAAuB,KAAK;AAC5C,eAAO,cAAc,gBAAgB;AACrC,eAAO,uBAAuB;AAC9B,eAAO;AAAA,MACX;AACA,UAAI;AAAA;AAAA,QAA0B,2BAAY;AACtC,mBAASY,UAAS,YAAY;AAC1B,gBAAI,KAAK,IAAI,cAAc,KAAK;AAChC,gBAAI,QAAQ,kBAAkB,EAAE;AAChC,gBAAI,YAAY;AACZ,yBAAW,YAAY,EAAE;AAAA,YAC7B;AACA,eAAG,MAAM,UAAU;AACnB,kBAAM,SAAS,QAAQ,MAAM,SAAS;AACtC,kBAAM,SAAS,SAAS,kBAAkB;AAC1C,kBAAM,SAAS,WAAW,QAAQ,aAAa,QAAQ,aAAa;AACpE,kBAAM,SAAS,WAAW,QAAQ,aAAa,wBAAwB,IAAI,2CAA2C,0BAA0B,CAAC;AACjJ,kBAAM,SAAS,WAAW,QAAQ,aAAa,cAAc,IAAI,iCAAiC,0BAA0B,CAAC;AAC7H,kBAAM,SAAS,SAAS,aAAa,eAAe,MAAM;AAC1D,kBAAM,UAAU,uBAAuB,KAAK;AAC5C,kBAAM,UAAU,aAAa,GAAG;AAChC,gBAAI,OAAO,WAAY;AAAA,YAAE;AACzB,kBAAM,QAAQ;AACd,kBAAM,aAAa;AACnB,kBAAM,SAAS,aAAa,eAAe;AAC3C,kBAAM,SAAS,aAAa,QAAQ,MAAM,UAAU;AACpD,kBAAM,SAAS,YAAY;AAC3B,kBAAM,SAAS,wBAAwB;AACvC,kBAAM,uBAAuB,KAAK;AAClC,kBAAM,QAAQ,UAAU,EAAE;AAC1B,kBAAM,QAAQ,iBAAiB,QAAQ;AACvC,kBAAM,GAAG,aAAa,SAAU,GAAG;AAC/B,kBAAI,MAAM,EAAE,oBAAoB;AAChC,oBAAM,UAAU,eAAe,GAAG;AAClC,8BAAgB,MAAM,MAAM,gBAAgB,IAAI,MAAM,IAAI;AAC1D,gBAAE,KAAK;AAAA,YACX,CAAC;AACD,gBAAI;AACJ,gBAAI,cAAc,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ;AAC/C,gBAAI,kBAAkB,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ;AACnD,4BAAgB,KAAK,MAAM,QAAQ,UAAU,iBAAiB,mBAAmB,UAAU;AAC3F,kBAAM,mBAAmB,SAAU,KAAK;AACpC,kBAAI,CAAC,KAAK;AACN,4BAAY,KAAK,MAAM,QAAQ,UAAU,aAAa,kBAAkB,UAAU;AAAA,cACtF,WACS,YAAY,IAAI;AACrB,sBAAM,QAAQ,aAAa,YAAY,EAAE;AACzC,4BAAY,KAAK;AAAA,cACrB;AAAA,YACJ;AACA,kBAAM,iBAAiB,KAAK;AAC5B,kBAAM,GAAG,aAAa,SAAU,GAAG;AAC/B,kBAAI,CAAC,gBAAgB;AACjB,iCAAiB;AACjB;AAAA,cACJ;AACA,kBAAI,eAAe,KAAK,EAAE,KAAK,eAAe,KAAK,EAAE,GAAG;AACpD;AAAA,cACJ;AACA,+BAAiB;AACjB,6BAAe,YAAY,MAAM,SAAS;AAC1C,oBAAM,cAAc;AACpB,kBAAI,MAAM,eAAe,oBAAoB,EAAE;AAC/C,kBAAI,YAAY,MAAM,OAAO,KAAK;AAC9B,oBAAI,CAAC,YAAY;AACb,wBAAM,OAAO,GAAG;AACpB,+BAAe,GAAG;AAAA,cACtB;AAAA,YACJ,CAAC;AACD,kBAAM,SAAS,GAAG,gBAAgB,WAAY;AAC1C,kBAAI,kBAAkB,YAAY,MAAM,OAAO,IAAI;AAC/C,+BAAe,OAAO;AACtB,oBAAI,MAAM,eAAe,oBAAoB,EAAE;AAC/C,oBAAI,CAAC,YAAY;AACb,wBAAM,OAAO,GAAG;AACpB,+BAAe,KAAK,IAAI;AAAA,cAC5B;AAAA,YACJ,CAAC;AACD,kBAAM,SAAS,GAAG,eAAe,WAAY;AACzC,kBAAI,IAAI,MAAM,SAAS;AACvB,uBAAS,MAAM,EAAE,OAAO,UAAU,IAAI,EAAE,OAAO,SAAS,OAAO,GAAG,OAAO;AACrE,oBAAI;AAAA;AAAA,kBAAmD,EAAE,QAAQ,WAAW,MAAM,EAAE,OAAO,QAAQ;AAAA;AACnG,gCAAgB,aAAa,QAAQ,cAAc;AACnD,gCAAgB,aAAa,wBAAwB,IAAI,gDAAgD,MAAM,CAAC;AAChH,gCAAgB,aAAa,gBAAgB,MAAM,KAAK,MAAM;AAC9D,gCAAgB,aAAa,oBAAoB,aAAa;AAC9D,gCAAgB,aAAa,iBAAiB,MAAM,CAAC;AACrD,oBAAI,UAAU,MAAM,QAAQ,GAAG;AAC/B,oBAAI,SAAS;AACT,sBAAI,YAAY,GAAG,OAAO,QAAQ,WAAW,QAAQ,KAAK,EAAE,OAAO,QAAQ,OAAO,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE;AAChH,kCAAgB,aAAa,cAAc,SAAS;AAAA,gBACxD;AACA,oBAAI,mBAAmB,gBAAgB,iBAAiB,2BAA2B;AACnF,iCAAiB,QAAQ,SAAU,MAAM;AACrC,uBAAK,aAAa,QAAQ,MAAM;AAAA,gBACpC,CAAC;AAAA,cACL;AAAA,YACJ,CAAC;AACD,kBAAM,SAAS,GAAG,eAAe,WAAY;AACzC,kBAAI,MAAM,MAAM,OAAO;AACvB,kBAAI,IAAI,MAAM,SAAS;AACvB,kBAAI;AAAA;AAAA,gBAA4C,EAAE,QAAQ,WAAW,MAAM,EAAE,OAAO,QAAQ;AAAA;AAC5F,kBAAIC,MAAK,SAAS;AAClB,kBAAI,aAAa,MAAM,gBAAgB,MAAM,cAAc;AACvD,oBAAI,eAAe,MAAM,cAAc,cAAc;AACrD,sBAAM,aAAa,gBAAgB,eAAe;AAClD,sBAAM,aAAa,gBAAgB,IAAI;AAAA,cAC3C;AACA,cAAAA,IAAG,gBAAgB,uBAAuB;AAC1C,oBAAM,eAAe;AACrB,kBAAI,UAAU;AACV,oBAAI,SAAS,UAAU,GAAG;AAC1B,oBAAI,YAAY,UAAU,cAAc;AACxC,yBAAS,KAAK;AACd,kBAAE,QAAQ,aAAa,yBAAyB,MAAM;AACtD,gBAAAA,IAAG,aAAa,yBAAyB,MAAM;AAC/C,yBAAS,aAAa,iBAAiB,MAAM;AAAA,cACjD;AAAA,YACJ,CAAC;AACD,gBAAI,kBAAkB,WAAY;AAAE,6BAAe,EAAE;AAAA,YAAG;AACxD,gBAAI,iBAAiB,SAAU,KAAK,gBAAgB;AAChD,kBAAI,QAAQ,YAAY,MAAM,KAAK;AAC/B,4BAAY,MAAM,MAAM,YAAY,IAAI,MAAM;AAC9C,oBAAI,CAAC;AACD,wBAAM,QAAQ,MAAM,kBAAkB;AAC1C,sBAAM,MAAM,mBAAmB;AAAA,cACnC;AAAA,YACJ;AACA,kBAAM,gBAAgB,WAAY;AAC9B,qBAAO,YAAY,MAAM;AAAA,YAC7B;AACA,kBAAM,YAAY,MAAM,WAAW,YAAY,WAAY;AACvD,oBAAM,cAAc;AACpB,8BAAgB;AAAA,YACpB,CAAC;AACD,kBAAM,GAAG,QAAQ,eAAe;AAChC,kBAAM,GAAG,mBAAmB,eAAe;AAC3C,kBAAM,QAAQ,IAAI,YAAY,WAAY;AACtC,qBAAO,MAAM,KAAK;AAAA,YACtB;AACA,kBAAM,QAAQ,IAAI,UAAU,SAAU,GAAG;AACrC,kBAAI,OAAO,MAAM,KAAK,CAAC;AACvB,kBAAI,OAAO,QAAQ;AACf,uBAAO;AACX,qBAAQ,QAAQ,KAAK,SAAU;AAAA,YACnC;AACA,gBAAI,cAAc,MAAM,QAAQ;AAChC,wBAAY,eAAe,SAAU,KAAK;AACtC,kBAAI,OAAO,MAAM,KAAK,GAAG;AACzB,kBAAI,SAAS,CAAC;AACd,kBAAI,CAAC;AACD,uBAAO;AACX,kBAAI,OAAO,QAAQ;AACf,uBAAO,EAAE,OAAO,KAAK;AACzB,kBAAI,UAAU,KAAK,WAAW,KAAK,SAAS,KAAK;AACjD,uBAAS,SAAS,OAAO,WAAW;AAChC,yBAAS,OAAO,KAAK;AAAA,kBACjB,OAAO,KAAK,aAAa,OAAO,aAAa;AAAA,kBAC7C;AAAA,gBACJ,CAAC;AAAA,cACL;AACA,kBAAI,QAAQ,QAAQ,YAAY;AAChC,kBAAI,cAAc,MAAM,cAAc,IAAI,YAAY;AACtD,kBAAI,YAAY;AAChB,kBAAI,QAAQ;AACZ,uBAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,KAAK;AACzC,oBAAI,KAAK,UAAU,KAAK,YAAa,KAAK,KAAM,KAAK,WAAW,SAAS;AACrE,sBAAI,MAAM,WAAW,MAAM,OAAO,CAAC;AACnC,0BAAQ;AACR,sBAAI,QAAQ,MAAM,QAAQ,KAAK,SAAS;AACxC,sBAAI,SAAS;AACT;AACJ,2BAAS,QAAQ,MAAM,WAAW,KAAK,GAAG,EAAE;AAC5C,8BAAY,QAAQ,IAAI;AACxB,2BAAS,QAAQ,MAAM,OAAO,SAAS,GAAG,sBAAsB;AAAA,gBACpE;AAAA,cACJ;AACA,uBAAS,QAAQ,MAAM,WAAW,QAAQ,MAAM,GAAG,EAAE;AACrD,qBAAO,KAAK,EAAE,MAAM,qBAAqB,OAAO,IAAI,CAAC;AACrD,kBAAI,KAAK;AACL,uBAAO,KAAK,EAAE,MAAM,mBAAmB,OAAO,KAAK,KAAK,CAAC;AAC7D,kBAAI,KAAK;AACL,uBAAO,KAAK,EAAE,MAAM,sBAAsB,OAAO,KAAK,QAAQ,CAAC;AACnE,qBAAO;AAAA,YACX;AACA,wBAAY,kBAAkB;AAC9B,wBAAY,QAAQ;AACpB,kBAAM,QAAQ,gBAAgB,WAAY;AACtC,qBAAO,KAAK,cAAc;AAAA,YAC9B;AACA,kBAAM,SAAS;AACf,kBAAM,YAAY;AAClB,kBAAM,aAAa;AACnB,kBAAM,aAAa;AACnB,kBAAM,cAAc;AACpB,kBAAM,OAAO,CAAC;AACd,kBAAM,UAAU,SAAU,MAAM,YAAY;AACxC,oBAAM,aAAa,cAAc;AACjC,oBAAM,SAAS,KAAK,aAAa,MAAM,KAAK,MAAM,GAAG,EAAE;AACvD,oBAAM,OAAO,QAAQ,CAAC;AACtB,oBAAM,OAAO,CAAC;AAAA,YAClB;AACA,kBAAM,UAAU,SAAU,KAAK;AAC3B,qBAAO,MAAM,KAAK,GAAG;AAAA,YACzB;AACA,kBAAM,SAAS,WAAY;AACvB,qBAAO,gBAAgB,MAAM;AAAA,YACjC;AACA,kBAAM,SAAS,SAAU,MAAM;AAC3B,qBAAO,KAAK,IAAI,KAAK,aAAa,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,SAAS,GAAG,IAAI,CAAC;AAC9E,kBAAI,gBAAgB,MAAM,OAAO,MAAM;AACnC,sBAAM,UAAU,eAAe;AAC/B,gCAAgB,MAAM,MAAM,gBAAgB,IAAI,MAAM,QAAQ;AAC9D,sBAAM,QAAQ,MAAM,kBAAkB;AACtC,sBAAM,aAAa,QAAQ,GAAG,CAAC;AAC/B,oBAAI,MAAM;AACN,wBAAM,QAAQ,QAAQ;AAAA,cAC9B;AAAA,YACJ;AACA,kBAAM,GAAG,mBAAmB,WAAY;AACpC,kBAAI,MAAM;AACN,sBAAM,OAAO,MAAM,UAAU,KAAK,GAAG;AACzC,oBAAM,SAAS,qBAAqB;AAAA,YACxC,CAAC;AACD,kBAAM,OAAO,WAAY;AACrB,mBAAK,UAAU,MAAM,UAAU;AAC/B,oBAAM,YAAY;AAClB,oBAAM,SAAS;AACf,kBAAI,MAAM,QAAQ;AACd,sBAAM,SAAS;AACf,qBAAK,QAAQ,MAAM;AAAA,cACvB;AAAA,YACJ;AACA,kBAAM,UAAU,SAAU,KAAK,YAAY,QAAQ,WAAW;AAC1D,kBAAI,CAAC,aAAa,MAAM,UAAU,MAAM,aAAa,MAAM,UACvD,MAAM,UAAU,QAAQ,IAAI,OAAO,MAAM,UAAU,SAAS,IAAI,QAChE,MAAM,WAAW,QAAQ;AACzB,uBAAO;AAAA,cACX;AACA,kBAAIA,MAAK,KAAK;AACd,kBAAI,gBAAgB,KAAK,SAAS,UAAU,SAAS;AACrD,kBAAI,eAAe,OAAO,cAAc;AACxC,kBAAI,cAAc,OAAO,aAAa;AACtC,kBAAI,WAAW,KAAK;AACpB,kBAAI,OAAO,SAAS,YAAY,aAAa;AAC7C,kBAAI,OAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,EAAE;AACxC,kBAAI,aAAa,eAAe,IAAI,MAAM,IAAI,KAAK,cAAc;AACjE,kBAAI,aAAa,IAAI,MAAM,IAAI,KAAK;AACpC,kBAAI,CAAC,QAAQ;AACT,oBAAI,cAAc,cAAc,cAAc,MAAM;AAChD,2BAAS;AAAA,gBACb,OACK;AACD,2BAAS;AAAA,gBACb;AAAA,cACJ;AACA,kBAAI,WAAW,OAAO;AAClB,qBAAK,SAAS,IAAI,MAAM,KAAK;AAC7B,qBAAK,MAAM,KAAK,SAAS;AAAA,cAC7B,WACS,WAAW,UAAU;AAC1B,qBAAK,MAAM,IAAI,MAAM,aAAa,KAAK;AACvC,qBAAK,SAAS,KAAK,MAAM;AAAA,cAC7B;AACA,kBAAI,QAAQ,KAAK,OAAO,KAAK,KAAK,UAAU;AAC5C,kBAAI,CAAC,aAAa,CAAC,OAAO;AACtB,uBAAO;AAAA,cACX;AACA,kBAAI,CAAC,OAAO;AACR,oBAAI,WAAW,OAAO;AAClB,2BAAS,kBAAkB;AAAA,gBAC/B,OACK;AACD,2BAAS,kBAAkB;AAAA,gBAC/B;AAAA,cACJ,OACK;AACD,yBAAS,kBAAkB;AAAA,cAC/B;AACA,kBAAI,WAAW,OAAO;AAClB,gBAAAA,IAAG,MAAM,MAAM;AACf,gBAAAA,IAAG,MAAM,SAAU,eAAe,gBAAgB,KAAK,SAAU;AACjE,sBAAM,YAAY;AAAA,cACtB,OACK;AACD,gBAAAA,IAAG,MAAM,MAAM,KAAK,MAAM;AAC1B,gBAAAA,IAAG,MAAM,SAAS;AAClB,sBAAM,YAAY;AAAA,cACtB;AACA,cAAAA,IAAG,MAAM,UAAU;AACnB,kBAAI,OAAO,IAAI;AACf,kBAAI,OAAOA,IAAG,cAAc;AACxB,uBAAO,cAAcA,IAAG;AAC5B,cAAAA,IAAG,MAAM,OAAO,OAAO;AACvB,cAAAA,IAAG,MAAM,QAAQ;AACjB,kBAAI,CAAC,MAAM,QAAQ;AACf,sBAAM,SAAS;AACf,qBAAK,QAAQ,MAAM;AACnB,iCAAiB;AAAA,cACrB;AACA,oBAAM,YAAY;AAClB,oBAAM,SAAS;AACf,qBAAO;AAAA,YACX;AACA,kBAAM,OAAO,SAAU,KAAK,YAAY,aAAa;AACjD,mBAAK,QAAQ,KAAK,YAAY,cAAc,WAAW,QAAW,IAAI;AAAA,YAC1E;AACA,kBAAM,OAAO,SAAU,OAAO;AAC1B,kBAAI,MAAM,KAAK,OAAO;AACtB,kBAAI,MAAM,KAAK,QAAQ,UAAU,IAAI;AACrC,sBAAQ,OAAO;AAAA,gBACX,KAAK;AACD,wBAAM,OAAO,IAAI,MAAM,MAAM;AAC7B;AAAA,gBACJ,KAAK;AACD,wBAAM,OAAO,MAAM,KAAK,MAAM;AAC9B;AAAA,gBACJ,KAAK;AACD,wBAAM;AACN;AAAA,gBACJ,KAAK;AACD,wBAAM;AACN;AAAA,cACR;AACA,mBAAK,OAAO,GAAG;AAAA,YACnB;AACA,kBAAM,oBAAoB,WAAY;AAClC,qBAAO,KAAK,cAAc,KAAK,SAAS,WAAW,KAAK;AAAA,YAC5D;AACA,kBAAM,aAAa;AACnB,kBAAM,cAAc;AACpB,mBAAO;AAAA,UACX;AACA,iBAAOD;AAAA,QACX,EAAE;AAAA;AACF,UAAI,gBAAgB,+0EAAi1E,sBAAsB,KAAK;AACh4E,MAAAX,SAAQ,WAAW;AACnB,MAAAA,SAAQ,oBAAoB;AAC5B,MAAAA,SAAQ,YAAY;AAAA,IAEpB,CAAC;AAED,QAAI,OAAO,wCAAuC,CAAC,WAAU,WAAU,QAAQ,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AACrH,UAAI;AAAA;AAAA,QAAuC,WAAY;AACnD,mBAASY,uBAAsB,QAAQ;AACnC,iBAAK,SAAS;AACd,iBAAK,kBAAkB,SAAS,cAAc,KAAK;AACnD,iBAAK,gBAAgB,UAAU,IAAI,uBAAuB;AAC1D,iBAAK,OAAO,UAAU,YAAY,KAAK,eAAe;AAAA,UAC1D;AACA,UAAAA,uBAAsB,UAAU,yBAAyB,SAAU,SAAS;AACxE,gBAAI,CAAC,KAAK,SAAS,KAAK,OAAO;AAAA,YAAiE,KAAK,OAAO,UAAW,OAAO;AAC1H,mBAAK;AAAA,cAA4D,KAAK,OAAO,UAAW;AACxF,mBAAK,MAAM,SAAS,GAAG,gBAAe,WAAY;AAC9C,oBAAI,MAAM,KAAK,MAAM,OAAO;AAC5B,oBAAI,IAAI,KAAK,MAAM,SAAS;AAC5B,oBAAI,WAAW,EAAE,QAAQ,WAAW,MAAM,EAAE,OAAO,QAAQ;AAC3D,oBAAI,UAAU;AACV,sBAAI,WAAW;AACf,2BAAS,YAAY,GAAG,YAAY,KAAK,OAAO,QAAQ,aAAa;AACjE,gCAAY,gCAAgC,OAAO,WAAW,GAAG;AAAA,kBACrE;AACA,2BAAS,aAAa,oBAAoB,QAAQ;AAAA,gBACtD;AAAA,cACJ,GAAE,KAAK,IAAI,CAAC;AAAA,YAChB;AACA,mBAAO,KAAK,gBAAgB,YAAY;AACpC,mBAAK,gBAAgB,YAAY,KAAK,gBAAgB,UAAU;AAAA,YACpE;AACA,iBAAK,SAAS,QAAQ,MAAM,YAAY;AACxC,gBAAI,cAAc,KAAK,gBAAgB;AACvC,iBAAK,gBAAgB,YAAY,WAAW;AAAA,UAChD;AACA,UAAAA,uBAAsB,UAAU,UAAU,WAAY;AAClD,iBAAK,gBAAgB,OAAO;AAAA,UAChC;AACA,UAAAA,uBAAsB,UAAU,kBAAkB,WAAY;AAC1D,gBAAI,YAAY,SAAS,cAAc,KAAK;AAC5C,sBAAU,aAAa,MAAM,yBAAyB;AACtD,qBAAS,YAAY,GAAG,YAAY,KAAK,OAAO,QAAQ,aAAa;AACjE,kBAAI,cAAc,SAAS,cAAc,MAAM;AAC/C,0BAAY,aAAa,MAAM,gCAAgC,OAAO,SAAS,CAAC;AAChF,kBAAI,OAAO,SAAS,eAAe,KAAK,OAAO,SAAS,CAAC;AACzD,0BAAY,YAAY,IAAI;AAC5B,wBAAU,YAAY,WAAW;AAAA,YACrC;AACA,mBAAO;AAAA,UACX;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,MAAAb,SAAQ,wBAAwB;AAAA,IAEhC,CAAC;AAED,QAAI,OAAO,2BAA0B,CAAC,WAAU,WAAU,UAAS,gBAAe,sCAAsC,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AAC9J,UAAI,iBAAiBF,SAAQ,aAAa,EAAE;AAC5C,UAAI,wBAAwBA,SAAQ,uBAAuB,EAAE;AAC7D,UAAI;AAAA;AAAA,QAA2B,WAAY;AACvC,mBAASe,aAAY;AACjB,iBAAK,SAAS;AAAA,UAClB;AACA,UAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ,YAAY,QAAQ;AAC7D,qBAAS,UAAU;AACnB,gBAAI,UAAU,KAAK,UAAU,KAAK,WAAW,QAAQ;AACjD,mBAAK,KAAK;AACV,mBAAK,SAAS;AACd,mBAAK,qBAAqB;AAAA,YAC9B;AACA,gBAAI,CAAC,UAAU,CAAC,YAAY;AACxB,qBAAO;AAAA,YACX;AACA,gBAAI,CAAC,KAAK,oBAAoB;AAC1B,mBAAK,qBAAqB,IAAI,sBAAsB,MAAM;AAAA,YAC9D;AACA,gBAAI,cAAc,WAAW,UAAU,eAAe,yBAAyB,QAAQ,WAAW,OAAO,IAAI,WAAW;AACxH,gBAAI,WAAW,qBAAqB,CAAC,eAAe,CAAC,YAAY,WAAW,MAAM,GAAG;AACjF,qBAAO;AAAA,YACX;AACA,iBAAK,SAAS;AACd,iBAAK,mBAAmB,uBAAuB,WAAW;AAC1D,0BAAc,YAAY,MAAM,OAAO,MAAM;AAC7C,gBAAI,gBAAgB,IAAI;AACpB,qBAAO,gBAAgB;AAAA,YAC3B,OACK;AACD,qBAAO,aAAa,WAAW;AAAA,YACnC;AACA,mBAAO;AAAA,UACX;AACA,UAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,gBAAI,CAAC,KAAK,QAAQ;AACd,qBAAO;AAAA,YACX;AACA,mBAAO,CAAC,CAAC,KAAK,OAAO,SAAS;AAAA,UAClC;AACA,UAAAA,WAAU,UAAU,OAAO,WAAY;AACnC,gBAAI,CAAC,KAAK,QAAQ;AACd,qBAAO;AAAA,YACX;AACA,iBAAK,OAAO,gBAAgB;AAC5B,mBAAO;AAAA,UACX;AACA,UAAAA,WAAU,UAAU,UAAU,WAAY;AACtC,iBAAK,KAAK;AACV,iBAAK,SAAS;AACd,gBAAI,KAAK,oBAAoB;AACzB,mBAAK,mBAAmB,QAAQ;AAChC,mBAAK,qBAAqB;AAAA,YAC9B;AAAA,UACJ;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,MAAAd,SAAQ,YAAY;AAAA,IAEpB,CAAC;AAED,QAAI,OAAO,yBAAwB,CAAC,WAAU,WAAU,QAAQ,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AACtG,MAAAD,SAAQ,aAAa,SAAU,OAAO,IAAI,UAAU;AAChD,YAAI,YAAY;AAChB,YAAI,WAAW,MAAM;AACrB,YAAI,aAAa;AACb,mBAAS;AACb,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,aAAG,MAAM,CAAC,GAAG,SAAU,QAAQ,KAAK;AAChC;AACA,gBAAI,cAAc;AACd,uBAAS,QAAQ,GAAG;AAAA,UAC5B,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,WAAW;AACf,MAAAA,SAAQ,8BAA8B,SAAU,MAAM,KAAK,OAAO;AAC9D,gBAAQ,SAAS;AACjB,YAAI,MAAM,CAAC;AACX,iBAAS,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK;AAC/B,cAAI,MAAM,KAAK,KAAK,CAAC,CAAC;AAClB,gBAAI,KAAK,KAAK,CAAC,CAAC;AAAA;AAEhB;AAAA,QACR;AACA,eAAO,IAAI,QAAQ,EAAE,KAAK,EAAE;AAAA,MAChC;AACA,MAAAA,SAAQ,8BAA8B,SAAU,MAAM,KAAK,OAAO;AAC9D,gBAAQ,SAAS;AACjB,YAAI,MAAM,CAAC;AACX,iBAAS,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,MAAM,KAAK,KAAK,CAAC,CAAC;AAClB,gBAAI,KAAK,KAAK,CAAC,CAAC;AAAA;AAEhB;AAAA,QACR;AACA,eAAO;AAAA,MACX;AACA,MAAAA,SAAQ,sBAAsB,SAAU,QAAQ;AAC5C,YAAI,MAAM,OAAO,kBAAkB;AACnC,YAAI,OAAO,OAAO,QAAQ,QAAQ,IAAI,GAAG;AACzC,YAAI;AACJ,eAAO,WAAW,SAAQ,SAAU,WAAW;AAC3C,cAAI,UAAU,mBAAmB;AAC7B,sBAAU,kBAAkB,SAAQ,SAAU,iBAAiB;AAC3D,kBAAI,CAAC,UAAU;AACX,yBAAS,KAAK,4BAA4B,MAAM,IAAI,QAAQ,eAAe;AAAA,YACnF,GAAE,KAAK,IAAI,CAAC;AAAA,UAChB;AAAA,QACJ,GAAE,KAAK,IAAI,CAAC;AACZ,eAAO,UAAU,KAAK,4BAA4B,MAAM,IAAI,MAAM;AAAA,MACtE;AACA,MAAAA,SAAQ,sBAAsB,SAAU,QAAQ,cAAc;AAC1D,YAAI,eAAe,gBAAgB,OAC7B,OAAO,QAAQ,sBAAsB,IACrC;AACN,eAAO,OAAO,WAAW,KAAK,SAAU,WAAW;AAC/C,cAAI,UAAU,qBAAqB,MAAM,QAAQ,UAAU,iBAAiB,GAAG;AAC3E,mBAAO,UAAU,kBAAkB,SAAS,YAAY;AAAA,UAC5D;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IAEA,CAAC;AAED,QAAI,OAAO,oBAAmB,CAAC,WAAU,WAAU,UAAS,6BAA4B,0BAAyB,2BAA0B,0BAAyB,yBAAwB,gBAAe,eAAc,gBAAe,cAAa,iBAAgB,gBAAgB,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AAC3T,UAAI,cAAcF,SAAQ,yBAAyB,EAAE;AACrD,UAAI,WAAWA,SAAQ,sBAAsB,EAAE;AAC/C,UAAI,YAAYA,SAAQ,uBAAuB,EAAE;AACjD,UAAI,YAAYA,SAAQ,sBAAsB,EAAE;AAChD,UAAI,OAAOA,SAAQ,qBAAqB;AACxC,UAAI,OAAOA,SAAQ,YAAY;AAC/B,UAAI,MAAMA,SAAQ,WAAW;AAC7B,UAAI,iBAAiBA,SAAQ,YAAY,EAAE;AAC3C,UAAI,SAASA,SAAQ,UAAU;AAC/B,UAAI,QAAQA,SAAQ,aAAa;AACjC,UAAI,sBAAsBA,SAAQ,cAAc,EAAE;AAClD,UAAI,mBAAmB,SAAU,GAAG,QAAQ;AACxC,eAAO,aAAa,OAAO,UAAU,QAAQ;AAAA,MACjD;AACA,UAAI;AAAA;AAAA,QAA8B,WAAY;AAC1C,mBAASgB,gBAAe;AACpB,iBAAK,aAAa;AAClB,iBAAK,aAAa;AAClB,iBAAK,YAAY;AACjB,iBAAK,aAAa;AAClB,iBAAK,gBAAgB;AACrB,iBAAK,kBAAkB,IAAI,YAAY;AACvC,iBAAK,gBAAgB,SAAS,KAAK,QAAQ;AAC3C,iBAAK,aAAa;AAClB,iBAAK,mBAAmB;AACxB,iBAAK,UAAU,oBAAI,IAAI;AACvB,iBAAK,mBAAmB;AACxB,iBAAK,uBAAuB;AAC5B,iBAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,iBAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,iBAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,iBAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAC3D,iBAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,iBAAK,cAAc,KAAK,aAAY,WAAY;AAC5C,mBAAK,kBAAkB,IAAI;AAAA,YAC/B,GAAE,KAAK,IAAI,CAAC;AACZ,iBAAK,eAAe,KAAK,YAAY,KAAK,iBAAiB,KAAK,IAAI,GAAG,EAAE;AACzE,iBAAK,aAAa,KAAK,YAAY,KAAK,qBAAqB,KAAK,IAAI,GAAG,EAAE;AAC3E,iBAAK,uBAAuB,KAAK,aAAY,WAAY;AACrD,mBAAK,kBAAkB;AAAA,YAC3B,GAAE,KAAK,IAAI,GAAG,KAAK,oBAAoB;AACvC,iBAAK,kBAAkB,KAAK;AAAA;AAAA,eAAqC,WAAY;AACzE,oBAAI,kBAAkB,KAAK,sBAAsB,KAAK,mBAAmB;AACzE,oBAAI,KAAK,aAAc,KAAK,SAAS,KAAK,MAAM,UAAW,CAAC,mBAAmB,KAAK,OAAO,WAAW,WAAW;AAC7G;AACJ,qBAAK,cAAc,IAAI,aAAaA,cAAa,qBAAqB;AACtE,qBAAK,UAAU,KAAK,QAAQ,gBAAgB,QAAQ,KAAK;AACzD,qBAAK,MAAM,SAAS,SAAS,eAAe,IAAI;AAAA,cACpD,GAAE,KAAK,IAAI;AAAA,cAAG,KAAK;AAAA,YAAoB;AAAA,UAC3C;AACA,iBAAO,eAAeA,eAAc,yBAAyB;AAAA,YACzD,KAAK,WAAY;AACb,qBAAO,CAAC;AAAA,gBACA,SAAS,OAAO,IAAI,wBAAwB,YAAY;AAAA,gBACxD,OAAO;AAAA,cACX,CAAC;AAAA,YACT;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AACD,UAAAA,cAAa,UAAU,QAAQ,WAAY;AACvC,iBAAK,QAAQ,IAAI,SAAS,KAAK,cAAc,SAAS,QAAQ,SAAS,eAAe;AACtF,iBAAK,MAAM,GAAG,UAAS,SAAU,GAAG;AAChC,mBAAK,YAAY;AACjB,gBAAE,KAAK;AAAA,YACX,GAAE,KAAK,IAAI,CAAC;AACZ,iBAAK,MAAM,QAAQ,KAAK,OAAO,MAAM,KAAK,KAAK,MAAM;AACrD,iBAAK,MAAM,GAAG,QAAQ,KAAK,aAAa,KAAK,IAAI,CAAC;AAClD,iBAAK,MAAM,GAAG,QAAQ,KAAK,aAAa,KAAK,IAAI,CAAC;AAClD,iBAAK,MAAM,GAAG,UAAU,KAAK,eAAe,KAAK,IAAI,CAAC;AACtD,kBAAM,YAAY,KAAK,MAAM,WAAW,YAAY,KAAK,iBAAiB,KAAK,IAAI,CAAC;AACpF,iBAAK,MAAM,GAAG,qBAAqB,KAAK,aAAa,KAAK,MAAM,IAAI,CAAC;AACrE,iBAAK,MAAM,SAAS,GAAG,eAAe,KAAK,eAAe,KAAK,IAAI,CAAC;AACpE,mBAAO,KAAK;AAAA,UAChB;AACA,UAAAA,cAAa,UAAU,cAAc,WAAY;AAC7C,gBAAI,CAAC,KAAK,iBAAiB,KAAK;AAC5B;AACJ,iBAAK,iBAAiB,IAAI,UAAU;AACpC,mBAAO,KAAK;AAAA,UAChB;AACA,UAAAA,cAAa,UAAU,WAAW,WAAY;AAC1C,mBAAO,KAAK,SAAS,KAAK,MAAM;AAAA,UACpC;AACA,UAAAA,cAAa,UAAU,eAAe,WAAY;AAC9C,gBAAI,KAAK,gBAAgB;AACrB,mBAAK,eAAe,KAAK;AAAA,YAC7B;AACA,iBAAK,eAAe;AACpB,iBAAK,qBAAqB,OAAO;AACjC,iBAAK,WAAW,OAAO;AACvB,iBAAK,kBAAkB;AAAA,UAC3B;AACA,UAAAA,cAAa,UAAU,QAAQ,SAAU,YAAY;AACjD,gBAAI,CAAC,KAAK,QAAQ,IAAI,UAAU,KAAK,cAAc,WAAW,aAAa,WAAW,UAAU,UAAU,OAAO,WAAW,UAAU,WAAW,YAAY;AACzJ,yBAAW,UAAU,OAAO,KAAK,QAAQ,UAAU;AACnD,mBAAK,QAAQ,IAAI,UAAU;AAAA,YAC/B;AAAA,UACJ;AACA,UAAAA,cAAa,UAAU,iBAAiB,SAAU,MAAM;AACpD,gBAAI,KAAK,kBAAkB,KAAK,eAAe;AAC3C,kBAAI,aAAa,OAAO,OAAO,KAAK,MAAM,QAAQ,KAAK,MAAM,OAAO,CAAC;AACrE,mBAAK,iBAAiB,UAAU;AAChC,kBAAI,KAAK,MAAM,eAAe,KAAK,kBAAkB;AACjD,qBAAK,aAAa,KAAK,MAAM,IAAI;AACjC;AAAA,cACJ;AACA,mBAAK,WAAW,SAAS;AACzB,mBAAK,aAAa,SAAS;AAAA,YAC/B,OACK;AACD,mBAAK,WAAW,KAAK,MAAM,IAAI;AAC/B,mBAAK,aAAa,KAAK,MAAM,IAAI;AAAA,YACrC;AAAA,UACJ;AACA,UAAAA,cAAa,UAAU,mBAAmB,SAAU,YAAY;AAC5D,gBAAI,MAAM,KAAK,KAAK;AACpB,gBAAI,SAAS,KAAK,KAAK;AACvB,gBAAI,eAAe,KAAK,OAAO,kBAAkB,EAAE;AACnD,gBAAI,SAAS,KAAK,OAAO,QAAQ,QAAQ,GAAG,EAAE,MAAM,QAAQ,YAAY;AACxE,gBAAI,CAAC,KAAK,eAAe,KAAK,KAAK,QAAQ,YAAY,MAAM,GAAG;AAC5D,mBAAK,eAAe,KAAK;AAAA,YAC7B,OACK;AACD,mBAAK,MAAM,UAAU;AAAA,YACzB;AAAA,UACJ;AACA,UAAAA,cAAa,UAAU,iBAAiB,WAAY;AAChD,gBAAI,gBAAgB,KAAK,kBAAkB,KAAK;AAChD,gBAAI,KAAK,eAAe,KAAK,YAAY,YAAY,KAAK,YAAY,SAAS,SAAS,GAAG;AACvF,uBAAS,IAAI,KAAK,MAAM,mBAAmB,GAAG,KAAK,KAAK,MAAM,kBAAkB,GAAG,KAAK;AACpF,oBAAI,aAAa,KAAK,MAAM,QAAQ,CAAC;AACrC,oBAAI,eAAe,CAAC,iBAAiB,WAAW,oBAAoB;AAChE,uBAAK,MAAM,UAAU;AAAA,gBACzB;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,UAAAA,cAAa,UAAU,eAAe,SAAU,MAAM;AAClD,iBAAK,eAAe,IAAI;AACxB,iBAAK,kBAAkB;AACvB,gBAAI,KAAK,wBAAwB;AAC7B,mBAAK,qBAAqB,SAAS,KAAK,oBAAoB;AAAA,UACpE;AACA,UAAAA,cAAa,UAAU,uBAAuB,WAAY;AACtD,gBAAI,KAAK,aAAa,CAAC,KAAK;AACxB;AACJ,mBAAO,iBAAiB,UAAU,KAAK,gBAAgB,EAAE,SAAS,KAAK,CAAC;AACxE,mBAAO,iBAAiB,SAAS,KAAK,kBAAkB;AACxD,gBAAI,KAAK,KAAK,OAAO,UAAU;AAC/B,gBAAI,WAAW,CAAC;AAChB,mBAAO,IAAI;AACP,uBAAS,KAAK,EAAE;AAChB,iBAAG,iBAAiB,UAAU,KAAK,gBAAgB,EAAE,SAAS,KAAK,CAAC;AACpE,mBAAK,GAAG;AAAA,YACZ;AACA,iBAAK,YAAY;AAAA,UACrB;AACA,UAAAA,cAAa,UAAU,yBAAyB,WAAY;AACxD,gBAAI,QAAQ;AACZ,mBAAO,oBAAoB,UAAU,KAAK,gBAAgB,EAAE,SAAS,KAAK,CAAC;AAC3E,mBAAO,oBAAoB,SAAS,KAAK,kBAAkB;AAC3D,iBAAK,aAAa,KAAK,UAAU,QAAQ,SAAU,IAAI;AACnD,iBAAG,oBAAoB,UAAU,MAAM,gBAAgB,EAAE,SAAS,KAAK,CAAC;AAAA,YAC5E,CAAC;AACD,iBAAK,YAAY;AAAA,UACrB;AACA,UAAAA,cAAa,UAAU,iBAAiB,WAAY;AAChD,gBAAI,CAAC,KAAK,MAAM;AACZ,qBAAO,KAAK,uBAAuB;AACvC,iBAAK,qBAAqB;AAC1B,iBAAK,iBAAiB;AAAA,UAC1B;AACA,UAAAA,cAAa,UAAU,uBAAuB,WAAY;AACtD,gBAAI,SAAS,KAAK;AAClB,gBAAI,WAAW,OAAO;AACtB,gBAAI,aAAa,SAAS,YAAY;AACtC,gBAAI,MAAM,SAAS,aAAa,iBAAiB,KAAK,MAAM,IAAI;AAChE,gBAAI,QAAQ,KAAK,MAAM,kBAAkB;AACzC,gBAAI,OAAO,OAAO,UAAU,sBAAsB;AAClD,gBAAI,OAAO,KAAK,MAAM,SAAS,YAAY;AAC3C,gBAAI,QAAQ,KAAK,OAAO,OAAO,SAAS;AACxC,gBAAI,QAAQ,SAAS;AACrB,gBAAI,eAAe;AAAA,cACf,KAAK,IAAI;AAAA,cACT,MAAM,IAAI;AAAA,YACd;AACA,gBAAI,SAAS,cAAc,SAAS,kBAAkB;AAClD,kBAAI,KAAK,KAAK,QAAQ,SAAS,WAAW,SAAS,KAAK;AACpD,6BAAa,OAAO,SAAS,iBAAiB,GAAG;AAAA,cACrD;AAAA,YACJ;AACA,gBAAI,wBAAwB,OAAO,UAAU,sBAAsB,EAAE,SAAS;AAC9E,gBAAI,iBAAiB,wBAAwB,aAAa,MACtD,EAAE,KAAK,uBAAuB,MAAM,aAAa,KAAK,IACtD;AACJ,gBAAI,KAAK,MAAM,QAAQ,gBAAgB,YAAY,QAAQ,GAAG;AAC1D;AAAA,YACJ;AACA,gBAAI,KAAK,MAAM,QAAQ,KAAK,YAAY,KAAK,GAAG;AAC5C;AAAA,YACJ;AACA,iBAAK,MAAM,KAAK,KAAK,UAAU;AAAA,UACnC;AACA,UAAAA,cAAa,UAAU,YAAY,SAAU,QAAQ,QAAQ,mBAAmB;AAC5E,iBAAK,gBAAgB,OAAO;AAC5B,gBAAI,CAAC,KAAK;AACN,mBAAK,MAAM;AACf,gBAAI,KAAK,iBAAiB,CAAC,KAAK;AAC5B,mBAAK,YAAY;AACrB,iBAAK,MAAM,aAAa,KAAK;AAC7B,iBAAK,MAAM,iBAAiB,KAAK,gBAAgB;AACjD,gBAAI,SAAS,KAAK,MAAM,OAAO;AAC/B,gBAAI,uBAAuB,KAAK,MAAM,KAAK,MAAM;AACjD,iBAAK,MAAM,QAAQ,KAAK,YAAY,UAAU,KAAK,YAAY,UAAU;AACzE,gBAAI,KAAK,OAAO,UAAU,gBAAgB;AACtC,mBAAK,OAAO,UAAU,eAAe;AAAA,gBACjC,kBAAkB,UAAU,KAAK,MAAM,OAAO,CAAC;AAAA,gBAC/C,QAAQ,KAAK;AAAA,cACjB,CAAC;AAAA,YACL;AACA,mBAAO,WAAW,mBAAmB,KAAK,eAAe;AACzD,gBAAI;AACJ,gBAAI,KAAK;AACL,uBAAS,KAAK,MAAM,KAAK,QAAQ,oBAAoB;AACzD,gBAAI,CAAC,UAAU,WAAW;AACtB,uBAAS;AACb,iBAAK,MAAM,OAAO,KAAK,aAAa,SAAS,EAAE;AAC/C,gBAAI,WAAW,UAAU,yBAAyB,KAAK,YAAY,SAAS,MAAM;AAC9E,mBAAK,eAAe;AACxB,gBAAI,gBAAgB,KAAK,kBAAkB,KAAK;AAChD,gBAAI,WAAW,UAAU,eAAe;AACpC,kBAAI,aAAa,KAAK,MAAM,QAAQ,KAAK,MAAM,OAAO,CAAC;AACvD,mBAAK,iBAAiB,UAAU;AAAA,YACpC;AACA,gBAAI,CAAC,mBAAmB;AACpB,mBAAK,MAAM,SAAS,OAAO,SAAS,CAAC;AACrC,mBAAK,MAAM,YAAY,OAAO,YAAY,CAAC;AAC3C,mBAAK,qBAAqB;AAC1B,kBAAI,KAAK,aAAa;AAClB,qBAAK,iBAAiB;AAAA,cAC1B;AAAA,YACJ;AACA,iBAAK,YAAY,OAAO;AACxB,iBAAK,qBAAqB;AAAA,UAC9B;AACA,UAAAA,cAAa,UAAU,SAAS,WAAY;AACxC,gBAAI,KAAK,QAAQ;AACb,mBAAK,OAAO,WAAW,sBAAsB,KAAK,eAAe;AACjE,mBAAK,OAAO,IAAI,mBAAmB,KAAK,cAAc;AACtD,mBAAK,OAAO,IAAI,QAAQ,KAAK,YAAY;AACzC,mBAAK,OAAO,IAAI,aAAa,KAAK,iBAAiB;AACnD,mBAAK,OAAO,IAAI,cAAc,KAAK,kBAAkB;AAAA,YACzD;AACA,iBAAK,gBAAgB,OAAO;AAC5B,iBAAK,YAAY,OAAO;AACxB,iBAAK,eAAe;AACpB,gBAAI,KAAK,oBAAoB;AACzB,mBAAK,mBAAmB,OAAO;AAAA,YACnC;AACA,gBAAI,KAAK,SAAS,KAAK,MAAM;AACzB,mBAAK,MAAM,KAAK;AACpB,gBAAI,KAAK,SAAS,KAAK,MAAM,UAAU;AACnC,mBAAK,MAAM,SAAS,IAAI,eAAe,KAAK,cAAc;AAAA,YAC9D;AACA,gBAAI,KAAK;AACL,mBAAK,KAAK,OAAO;AACrB,iBAAK,YAAY;AACjB,iBAAK,qBAAqB,KAAK,cAAc,KAAK,OAAO;AACzD,iBAAK,uBAAuB;AAAA,UAChC;AACA,UAAAA,cAAa,UAAU,iBAAiB,SAAU,GAAG;AACjD,gBAAI,SAAS,KAAK,OAAO,UAAU;AACnC,gBAAI,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,SAAS,KAAK,KAAK,QAAQ;AACjE,mBAAK,OAAO;AAAA,YAChB;AACA,gBAAI,KAAK;AACL,mBAAK,YAAY,SAAS;AAAA;AAE1B,mBAAK,OAAO;AAAA,UACpB;AACA,UAAAA,cAAa,UAAU,eAAe,SAAU,GAAG;AAC/C,gBAAI,KAAK,SAAS;AAClB,gBAAI,OAAO,KAAK,OAAO,UAAU,WAAW;AAC5C,gBAAI,cAAc,EAAE,iBAAiB,KAAK,eAAe,KAAK,YAAY,SAAS,EAAE,aAAa;AAClG,gBAAI,YAAY,KAAK,SAAS,KAAK,MAAM;AACzC,gBAAI,MAAM,QAAQ,GAAG,cAAc,aAAa,CAAC,eAC1C,MAAM,KAAK,eAAe,EAAE,iBAAiB,MAAM;AACtD,mBAAK,OAAO;AAAA,YAChB;AAAA,UACJ;AACA,UAAAA,cAAa,UAAU,oBAAoB,SAAU,GAAG;AACpD,iBAAK,OAAO;AAAA,UAChB;AACA,UAAAA,cAAa,UAAU,qBAAqB,SAAU,GAAG;AACrD,gBAAI,KAAK,SAAS,CAAC,KAAK,MAAM;AAC1B,mBAAK,OAAO;AAAA,UACpB;AACA,UAAAA,cAAa,UAAU,mBAAmB,SAAU,GAAG;AACnD,gBAAI,KAAK,MAAM;AACX,mBAAK,qBAAqB;AAAA,UAClC;AACA,UAAAA,cAAa,UAAU,OAAO,SAAU,OAAO;AAC3C,iBAAK,MAAM,KAAK,KAAK;AAAA,UACzB;AACA,UAAAA,cAAa,UAAU,cAAc,SAAU,MAAM,SAAS;AAC1D,gBAAI,CAAC;AACD,qBAAO,KAAK,MAAM,QAAQ,KAAK,MAAM,OAAO,CAAC;AACjD,gBAAI,CAAC;AACD,qBAAO;AACX,gBAAI,KAAK,UAAU;AACf,qBAAO,KAAK,OAAO;AACvB,gBAAI,cAAc,KAAK;AACvB,gBAAI,SAAS,KAAK,sBAAsB,EAAE,YAAY,KAAK,QAAQ,MAAM,YAAY,YAAY,OAAO;AACxG,gBAAI,KAAK,eAAe;AACpB,mBAAK,OAAO;AAChB,mBAAO;AAAA,UACX;AACA,UAAAA,cAAa,UAAU,YAAY,SAAU,QAAQ,SAAS;AAC1D,gBAAI,KAAK;AACL,mBAAK,OAAO;AAChB,iBAAK,YAAY;AACjB,iBAAK,SAAS;AACd,gBAAI,OAAO,aAAa,MAAM;AAC1B,kBAAI,OAAO;AACP,uBAAO,UAAU,OAAO;AAC5B,qBAAO,YAAY;AAAA,YACvB;AACA,mBAAO,GAAG,mBAAmB,KAAK,cAAc;AAChD,mBAAO,GAAG,QAAQ,KAAK,YAAY;AACnC,mBAAO,GAAG,aAAa,KAAK,iBAAiB;AAC7C,mBAAO,GAAG,cAAc,KAAK,kBAAkB;AAC/C,iBAAK,kBAAkB,OAAO,OAAO;AAAA,UACzC;AACA,UAAAA,cAAa,UAAU,wBAAwB,SAAU,iBAAiB;AACtE,gBAAI,CAAC,KAAK;AACN,mBAAK,qBAAqB,IAAI,mBAAmB,eAAe;AACpE,mBAAO,KAAK;AAAA,UAChB;AACA,UAAAA,cAAa,UAAU,oBAAoB,SAAU,QAAQ,UAAU;AACnE,mBAAO,KAAK,sBAAsB,EAAE,kBAAkB,QAAQ,QAAQ;AAAA,UAC1E;AACA,UAAAA,cAAa,UAAU,oBAAoB,SAAU,mBAAmB,SAAS;AAC7E,gBAAI,qBAAqB,KAAK,QAAQ,KAAK,aAAa;AACpD,kBAAI,MAAM,KAAK,OAAO,kBAAkB;AACxC,kBAAI,SAAS,KAAK,OAAO,QAAQ,aAAa,EAAE,OAAO,KAAK,MAAM,KAAK,IAAI,CAAC;AAC5E,kBAAI,UAAU,KAAK,YAAY;AAC3B;AACJ,mBAAK,YAAY,UAAU,MAAM;AACjC,kBAAI,CAAC,KAAK,YAAY,SAAS;AAC3B,uBAAO,KAAK,OAAO;AACvB,kBAAI,KAAK,YAAY,SAAS,UAAU,KACjC,KAAK,YAAY,SAAS,CAAC,EAAE,SAAS,UACtC,CAAC,KAAK,YAAY,SAAS,CAAC,EAAE;AACjC,uBAAO,KAAK,OAAO;AACvB,mBAAK,UAAU,KAAK,QAAQ,QAAQ,iBAAiB;AACrD;AAAA,YACJ;AACA,gBAAI,WAAW,QAAQ,SAAS;AAC5B,kBAAI,MAAM,KAAK,OAAO,kBAAkB,EAAE;AAC1C,mBAAK,OAAO,KAAK,OAAO,QAAQ,IAAI,aAAa,IAAI,KAAK,IAAI,MAAM;AACpE,mBAAK,KAAK,eAAe;AACzB,mBAAK,cAAc,IAAI,aAAa,QAAQ,OAAO;AACnD,mBAAK,sBAAsB,EAAE,cAAc,KAAK;AAChD,qBAAO,KAAK,UAAU,KAAK,QAAQ,IAAI,iBAAiB;AAAA,YAC5D;AACA,gBAAI,UAAU,KAAK,OAAO,WAAW;AACrC,gBAAI,MAAM,KAAK,OAAO,kBAAkB;AACxC,gBAAI,SAAS,KAAK,oBAAoB,KAAK,MAAM;AACjD,iBAAK,OAAO,QAAQ,IAAI,aAAa,IAAI,KAAK,IAAI,SAAS,OAAO,MAAM;AACxE,iBAAK,KAAK,eAAe;AACzB,gBAAI,oBAAoB;AAAA,cACpB,YAAY,KAAK;AAAA,cACjB,eAAe,KAAK;AAAA,YACxB;AACA,iBAAK,sBAAsB;AAAA,cACvB;AAAA,cACA;AAAA,YACJ,CAAC,EAAE;AAAA,cAAmB,KAAK;AAAA,cAAQ;AAAA,eACnC,SAAU,KAAK,aAAa,UAAU;AAClC,oBAAI,WAAW,YAAY;AAC3B,oBAAIC,UAAS,KAAK,oBAAoB,KAAK,MAAM;AACjD,qBAAK,gBAAgB,OAAO;AAC5B,oBAAI,UAAU;AACV,sBAAI,CAAC,SAAS,QAAQ;AAClB,wBAAI,eAAe,CAAC,KAAK,aAAa,KAAK;AAC3C,wBAAI,OAAO,gBAAgB;AACvB,qCAAe,KAAK,aAAaA,OAAM;AAC3C,wBAAI,cAAc;AACd,0BAAI,sBAAsB;AAAA,wBAAC;AAAA,0BACnB,SAAS;AAAA,0BACT,OAAO;AAAA,wBACX;AAAA,sBACJ;AACA,2BAAK,cAAc,IAAI,aAAa,mBAAmB;AACvD,2BAAK,UAAU,KAAK,QAAQA,SAAQ,iBAAiB;AACrD,2BAAK,MAAM,SAAS,SAAS,eAAe,KAAK;AACjD,2BAAK,MAAM,SAAS,SAAS,qBAAqB,IAAI;AACtD;AAAA,oBACJ;AACA,2BAAO,KAAK,OAAO;AAAA,kBACvB;AACA,sBAAI,SAAS,UAAU,KAAK,SAAS,CAAC,EAAE,SAASA,WAC1C,CAAC,SAAS,CAAC,EAAE;AAChB,2BAAO,KAAK,OAAO;AACvB,sBAAI,KAAK,cAAc,CAAC,KAAK,aAAa,SAAS,UAAU;AACzD,2BAAO,KAAK,YAAY,SAAS,CAAC,CAAC;AAAA,gBAC3C;AACA,qBAAK,cAAc,CAAC,YAAY,KAAK,mBACjC,IAAI,aAAaD,cAAa,sBAAsB,OAAO,QAAQ,GAAG,YAAY,UAAU,IAC5F;AACJ,qBAAK,UAAU,KAAK,QAAQC,SAAQ,iBAAiB;AACrD,qBAAK,MAAM,SAAS,SAAS,qBAAqB,KAAK;AACvD,qBAAK,MAAM,SAAS,SAAS,eAAe,CAAC,QAAQ;AAAA,cACzD,GAAE,KAAK,IAAI;AAAA,YAAC;AACZ,gBAAI,KAAK,oBAAoB,CAAC,KAAK,aAAa,EAAE,KAAK,SAAS,KAAK,MAAM,SAAS;AAChF,mBAAK,gBAAgB,MAAM,KAAK,uBAAuB,CAAC;AAAA,YAC5D;AAAA,UACJ;AACA,UAAAD,cAAa,UAAU,oBAAoB,WAAY;AACnD,iBAAK,OAAO,cAAc,kBAAkB;AAAA,UAChD;AACA,UAAAA,cAAa,UAAU,mBAAmB,WAAY;AAClD,gBAAI,QAAQ,KAAK;AACjB,gBAAI,MAAM,KAAK,eAAe,KAAK,YAAY;AAC/C,gBAAI,WAAW,QAAQ,IAAI,MAAM,cAAc,CAAC,KAAK,IAAI,MAAM,OAAO,CAAC;AACvE,gBAAI,MAAM;AACV,gBAAI,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK,MAAM;AACzC,qBAAO,KAAK,eAAe;AAC/B,gBAAI,mBAAmB,KAAK,OAAO,WAAW;AAC9C,qBAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACvC,kBAAI,YAAY,KAAK,OAAO,WAAW,CAAC;AACxC,kBAAI,UAAU,iBAAiB,SAAS,gBAAgB,UAAU,IAAI;AAClE,sBAAM,UAAU,cAAc,QAAQ;AACtC;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,CAAC,OAAO,OAAO,YAAY;AAC3B,oBAAM;AACV,gBAAI,OAAO,OAAO;AACd,oBAAM,EAAE,SAAS,IAAI;AACzB,gBAAI,CAAC,OAAO,EAAE,IAAI,WAAW,IAAI;AAC7B,qBAAO,KAAK,eAAe;AAC/B,iBAAK,eAAe,GAAG;AAAA,UAC3B;AACA,UAAAA,cAAa,UAAU,iBAAiB,SAAU,MAAM;AACpD,gBAAI,CAAC,KAAK,aAAa;AACnB,mBAAK,cAAc,IAAI,cAAc,KAAK;AAC1C,mBAAK,YAAY,MAAM,SAAS;AAChC,mBAAK,YAAY,MAAM,gBAAgB;AACvC,mBAAK,YAAY,MAAM,qBAAqB;AAC5C,mBAAK,YAAY,WAAW;AAC5B,mBAAK,YAAY,SAAS,KAAK,aAAa,KAAK,IAAI;AACrD,mBAAK,YAAY,UAAU,KAAK,eAAe,KAAK,IAAI;AACxD,mBAAK,YAAY,KAAK;AACtB,mBAAK,YAAY,aAAa,QAAQ,SAAS;AAC/C,mBAAK,YAAY,iBAAiB,SAAS,mBAAmB;AAAA,YAClE;AACA,gBAAI,QAAQ,KAAK,OAAO,SAAS;AACjC,iBAAK,YAAY,YAAY,kCACxB,MAAM,SAAS,cAAc,OAAO,MAAM,YAAY;AAC3D,gBAAI,cAAc,KAAK;AACvB,gBAAI,KAAK,SAAS;AACd,0BAAY,YAAY,KAAK;AAAA,YACjC,WACS,KAAK,SAAS;AACnB,0BAAY,cAAc,KAAK;AAAA,YACnC;AACA,gBAAI,CAAC,YAAY;AACb,mBAAK,MAAM,UAAU,YAAY,KAAK,WAAW;AACrD,gBAAI,QAAQ,KAAK;AACjB,gBAAI,OAAO,MAAM,UAAU,sBAAsB;AACjD,gBAAI,cAAc;AAClB,gBAAI,eAAe;AACnB,gBAAI,gBAAgB,MAAM,SAAS,UAAU,SAAS;AACtD,gBAAI,WAAW,KAAK;AACpB,gBAAI,YAAY,OAAO,aAAa,KAAK,QAAQ;AACjD,gBAAI,UAAU,MAAM,YAAY,KAAK,MAAM,OAAO,cAAc,gBAAgB,KAAK;AACrF,gBAAI,SAAS;AAAA,cACT,KAAK,IAAI,YAAY,aAAa,CAAC;AAAA,cACnC,KAAK,IAAI,WAAW,aAAa,CAAC;AAAA,cAClC,KAAK,IAAI,UAAU,eAAe,GAAG;AAAA,YACzC;AACA,gBAAI,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM;AACrC,gBAAI,eAAe,YAAY;AAC/B,yBAAa,UAAU;AACvB,gBAAI,OAAO,OAAO,CAAC,GAAG;AAClB,2BAAa,OAAQ,KAAK,QAAQ,IAAK;AACvC,2BAAa,QAAQ;AACrB,2BAAa,WAAW,cAAc,MAAM;AAC5C,2BAAa,MAAM,KAAK,MAAM;AAC9B,2BAAa,SAAS;AACtB,2BAAa,YAAY,KAAK,IAAI,OAAO,cAAc,gBAAgB,KAAK,KAAK,YAAY,IAAI;AAAA,YACrG,WACS,OAAO,OAAO,CAAC,GAAG;AACvB,2BAAa,QAAQ,OAAO,aAAa,KAAK,OAAO;AACrD,2BAAa,OAAO;AACpB,2BAAa,WAAW,cAAc,MAAM;AAC5C,2BAAa,MAAM,KAAK,MAAM;AAC9B,2BAAa,SAAS;AACtB,2BAAa,YAAY,KAAK,IAAI,OAAO,cAAc,gBAAgB,KAAK,KAAK,YAAY,IAAI;AAAA,YACrG,WACS,OAAO,OAAO,CAAC,GAAG;AACvB,2BAAa,OAAO,OAAO,aAAa,KAAK,OAAO;AACpD,2BAAa,WAAW,KAAK,IAAI,aAAa,OAAO,UAAU,IAAI;AACnE,kBAAI,MAAM,WAAW;AACjB,6BAAa,MAAM,KAAK,SAAS;AACjC,6BAAa,OAAO,KAAK,OAAO;AAChC,6BAAa,QAAQ;AACrB,6BAAa,SAAS;AACtB,6BAAa,YAAY,KAAK,IAAI,OAAO,cAAc,gBAAgB,KAAK,QAAQ,YAAY,IAAI;AAAA,cACxG,OACK;AACD,6BAAa,MAAM,MAAM,UAAU,YAAY,YAAY,eAAe;AAC1E,6BAAa,OAAO,KAAK,OAAO;AAChC,6BAAa,QAAQ;AACrB,6BAAa,SAAS;AACtB,6BAAa,YAAY,KAAK,IAAI,MAAM,UAAU,WAAW,YAAY,IAAI;AAAA,cACjF;AAAA,YACJ;AAAA,UACJ;AACA,UAAAA,cAAa,UAAU,iBAAiB,WAAY;AAChD,iBAAK,aAAa,OAAO;AACzB,gBAAI,CAAC,KAAK;AACN;AACJ,gBAAI,KAAK,KAAK;AACd,gBAAI,CAAC,KAAK,OAAO,UAAU,KAAK,SAAS,iBAAiB;AACtD,mBAAK,OAAO,MAAM;AACtB,iBAAK,cAAc;AACnB,gBAAI,GAAG;AACH,iBAAG,WAAW,YAAY,EAAE;AAAA,UACpC;AACA,UAAAA,cAAa,UAAU,iBAAiB,SAAU,GAAG;AACjD,gBAAI,IAAI,EAAE;AACV,mBAAO,KAAK,KAAK,KAAK,aAAa;AAC/B,kBAAI,EAAE,YAAY,OAAO,EAAE,MAAM;AAC7B,kBAAE,MAAM;AACR,kBAAE,SAAS;AACX;AAAA,cACJ;AACA,kBAAI,EAAE;AAAA,YACV;AAAA,UACJ;AACA,UAAAA,cAAa,UAAU,UAAU,WAAY;AACzC,iBAAK,OAAO;AACZ,gBAAI,KAAK,OAAO;AACZ,mBAAK,MAAM,QAAQ;AACnB,kBAAI,KAAK,KAAK,MAAM;AACpB,kBAAI,MAAM,GAAG;AACT,mBAAG,WAAW,YAAY,EAAE;AAAA,YACpC;AACA,gBAAI,KAAK,UAAU,KAAK,OAAO,aAAa,MAAM;AAC9C,mBAAK,OAAO,IAAI,WAAW,gBAAgB;AAC3C,mBAAK,OAAO,YAAY;AAAA,YAC5B;AACA,iBAAK,iBAAiB,KAAK,QAAQ,KAAK,SAAS;AAAA,UACrD;AACA,UAAAA,cAAa,MAAM,SAAU,QAAQ;AACjC,gBAAI,OAAO,qBAAqBA,eAAc;AAC1C,qBAAO,OAAO;AAAA,YAClB;AACA,gBAAI,OAAO,WAAW;AAClB,qBAAO,UAAU,QAAQ;AACzB,qBAAO,YAAY;AAAA,YACvB;AACA,gBAAI,OAAO,IAAI,cAAc,GAAG;AAC5B,kBAAI,CAACA,cAAa,iBAAiB;AAC/B,gBAAAA,cAAa,iBAAiB,IAAI,IAAIA,cAAa;AACvD,qBAAO,YAAYA,cAAa,iBAAiB;AAAA,YACrD,OACK;AACD,qBAAO,YAAY,IAAIA,cAAa;AACpC,qBAAO,KAAK,WAAW,gBAAgB;AAAA,YAC3C;AACA,mBAAO,OAAO;AAAA,UAClB;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,mBAAa,UAAU,WAAW;AAAA,QAC9B,MAAM,SAAU,QAAQ;AAAE,iBAAO,UAAU,KAAK,IAAI;AAAA,QAAG;AAAA,QACvD,QAAQ,SAAU,QAAQ;AAAE,iBAAO,UAAU,KAAK,MAAM;AAAA,QAAG;AAAA,QAC3D,qBAAqB,SAAU,QAAQ;AAAE,iBAAO,UAAU,KAAK,OAAO;AAAA,QAAG;AAAA,QACzE,sBAAsB,SAAU,QAAQ;AAAE,iBAAO,UAAU,KAAK,KAAK;AAAA,QAAG;AAAA,QACxE,OAAO,SAAU,QAAQ;AAAE,iBAAO,UAAU,OAAO;AAAA,QAAG;AAAA,QACtD,UAAU,SAAU,QAAQ;AAAE,iBAAO,OAAO,UAAU,YAAY;AAAA,QAAG;AAAA,QACrE,gBAAgB,SAAU,QAAQ;AAAE,iBAAO,UAAU,YAAY,MAAM,EAAE,cAAc,KAAK,CAAC;AAAA,QAAG;AAAA,QAChG,OAAO,SAAU,QAAQ;AACrB,cAAI,SAAS,OAAO,UAAU,YAAY;AAC1C,cAAI,CAAC,UAAU,CAAC,OAAO;AACnB,mBAAO,UAAU,KAAK,MAAM;AAAA;AAE5B,mBAAO;AAAA,QACf;AAAA,QACA,aAAa,SAAU,QAAQ;AAC3B,iBAAO,YAAY,WAAW;AAC9B,cAAI,SAAS,KAAK,oBAAoB,MAAM;AAC5C,cAAI,CAAC,UAAU,OAAO;AAClB,mBAAO,UAAU,OAAO;AAAA,QAChC;AAAA,QACA,UAAU,SAAU,QAAQ;AAAE,iBAAO,UAAU,MAAM,WAAW;AAAA,QAAG;AAAA,QACnE,YAAY,SAAU,QAAQ;AAAE,iBAAO,UAAU,MAAM,aAAa;AAAA,QAAG;AAAA,MAC3E;AACA,mBAAa,eAAe;AAAA,QACxB,MAAM;AAAA,QACN,MAAM,SAAU,QAAQ,SAAS;AAC7B,cAAI,YAAY,aAAa,IAAI,MAAM;AACvC,oBAAU,aAAa;AACvB,oBAAU,aAAa;AACvB,oBAAU,YAAY;AACtB,oBAAU,UAAU,QAAQ,OAAO;AACnC,oBAAU,kBAAkB;AAAA,QAChC;AAAA,QACA,SAAS;AAAA,MACb;AACA,UAAI;AAAA;AAAA,QAAoC,WAAY;AAChD,mBAASE,oBAAmB,iBAAiB;AACzC,iBAAK,kBAAkB;AACvB,iBAAK,SAAS;AAAA,UAClB;AACA,UAAAA,oBAAmB,UAAU,gBAAgB,SAAU,QAAQ,OAAO,SAAS;AAC3E,gBAAI,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY,UAAU;AACjD,qBAAO;AAAA,YACX;AACA,mBAAO,KAAK,YAAY,QAAQ,KAAK,YAAY,SAAS,KAAK,GAAG,OAAO;AAAA,UAC7E;AACA,UAAAA,oBAAmB,UAAU,cAAc,SAAU,QAAQ,MAAM,SAAS;AACxE,gBAAI,CAAC;AACD,qBAAO;AACX,mBAAO,eAAe,EAAE,SAAS,EAAE,MAAM,cAAc,EAAE,CAAC;AAC1D,gBAAI,KAAK,aAAa,KAAK,UAAU,aAAa;AAC9C,mBAAK,UAAU,YAAY,QAAQ,IAAI;AAAA,YAC3C,OACK;AACD,kBAAI,CAAC,KAAK;AACN,uBAAO;AACX,kBAAI,gBAAgB,KAAK,YAAY,WAAW;AAChD,kBAAI,eAAe;AACnB,kBAAI,KAAK,SAAS,KAAK,MAAM,MAAM,QAAQ,KAAK,MAAM,IAAI,KAAK;AAC3D,iCAAiB,KAAK,gBAAgB,OAAO;AAC7C,iCAAiB,KAAK,gBAAgB,IAAI,SAAS,KAAK,MAAM,MAAM;AACpE,gCAAgB,KAAK,MAAM,IAAI,SAAS,KAAK,gBAAgB,IAAI;AAAA,cACrE;AACA,kBAAI,iBAAiB,cAAc;AAC/B,oBAAI;AACJ,oBAAI,OAAO,UAAU,cAAc;AAC/B,2BAAS,OAAO,UAAU,aAAa;AAAA,gBAC3C,OACK;AACD,2BAAS,CAAC,OAAO,kBAAkB,CAAC;AAAA,gBACxC;AACA,yBAAS,IAAI,GAAG,OAAO,QAAQ,OAAO,CAAC,GAAG,KAAK;AAC3C,wBAAM,MAAM,UAAU;AACtB,wBAAM,IAAI,UAAU;AACpB,yBAAO,QAAQ,OAAO,KAAK;AAAA,gBAC/B;AAAA,cACJ;AACA,kBAAI,KAAK,SAAS;AACd,+BAAe,cAAc,QAAQ,KAAK,OAAO;AAAA,cACrD,OACK;AACD,qBAAK,cAAc,QAAQ,IAAI;AAAA,cACnC;AACA,kBAAI,KAAK,aAAa,KAAK,UAAU,YAAY,OAAO,KAAK,UAAU,YAAY,YAAY;AAC3F,qBAAK,UAAU,SAAS,QAAQ,IAAI;AAAA,cACxC;AACA,kBAAI,KAAK,WAAW,KAAK,YAAY,qBAAqB;AACtD,uBAAO,YAAY,KAAK,OAAO;AAAA,cACnC;AAAA,YACJ;AACA,mBAAO,aAAa;AACpB,mBAAO;AAAA,UACX;AACA,UAAAA,oBAAmB,UAAU,gBAAgB,SAAU,QAAQ,MAAM;AACjE,gBAAI,OAAO,KAAK,SAAS;AACzB,mBAAO,YAAY,gBAAgB,IAAI;AAAA,UAC3C;AACA,UAAAA,oBAAmB,UAAU,oBAAoB,SAAU,QAAQ,UAAU;AACzE,gBAAI,UAAU,OAAO,WAAW;AAChC,gBAAI,MAAM,OAAO,kBAAkB;AACnC,gBAAI,SAAS,KAAK,oBAAoB,MAAM;AAC5C,gBAAI,UAAU,CAAC;AACf,iBAAK,aAAa,OAAO;AACzB,gBAAI,QAAQ,OAAO,WAAW;AAC9B,mBAAO,WAAW,QAAQ,SAAU,WAAW,GAAG;AAC9C,wBAAU,eAAe,QAAQ,SAAS,KAAK,QAAQ,SAAU,KAAK,SAAS;AAC3E,oBAAI,UAAU;AACV,4BAAU,QAAQ,IAAI,SAAU,QAAQ;AACpC,2BAAO,OAAO,OAAO,QAAQ,EAAE,mBAAmB,UAAU,kBAAkB,CAAC;AAAA,kBACnF,CAAC;AACL,oBAAI,CAAC,OAAO;AACR,4BAAU,QAAQ,OAAO,OAAO;AACpC,yBAAS,MAAM;AAAA,kBACX,QAAQ,KAAK,oBAAoB,MAAM;AAAA,kBACvC;AAAA,kBACA,UAAW,EAAE,UAAU;AAAA,gBAC3B,CAAC;AAAA,cACL,CAAC;AAAA,YACL,CAAC;AACD,mBAAO;AAAA,UACX;AACA,UAAAA,oBAAmB,UAAU,qBAAqB,SAAU,QAAQ,SAAS,UAAU;AACnF,gBAAI,kBAAiB,SAAUC,UAAS;AACpC,kBAAI,SAASA,SAAQ;AACrB,kBAAI,UAAUA,SAAQ;AACtB,mBAAK,cAAc,IAAI,aAAa,OAAO;AAC3C,kBAAI,QAAQ;AACR,qBAAK,YAAY,aAAa;AAClC,kBAAI,QAAQ;AACR,qBAAK,YAAY,gBAAgB;AACrC,mBAAK,YAAY,UAAU,MAAM;AACjC,kBAAIA,SAAQ,YAAY,KAAK,YAAY,SAAS;AAC9C,yBAAS,MAAM,KAAK,aAAaA,SAAQ,QAAQ;AAAA,YACzD,GAAE,KAAK,IAAI;AACX,gBAAI,cAAc;AAClB,gBAAI,mBAAmB;AACvB,iBAAK,kBAAkB,SAAQ,SAAU,KAAKA,UAAS;AACnD,kBAAI,CAAC,KAAK,QAAQ;AACd;AAAA,cACJ;AACA,kBAAI,KAAK;AACL,yBAAS,KAAK,CAAC,GAAG,IAAI;AACtB,qBAAK,OAAO;AAAA,cAChB;AACA,kBAAI,SAASA,SAAQ;AACrB,kBAAI,OAAO,QAAQA,SAAQ,MAAM,MAAM;AACnC;AACJ,kBAAI,aAAa;AACb,mCAAmBA;AACnB;AAAA,cACJ;AACA,6BAAeA,QAAO;AAAA,YAC1B,GAAE,KAAK,IAAI,CAAC;AACZ,0BAAc;AACd,gBAAI,kBAAkB;AAClB,kBAAI,UAAU;AACd,iCAAmB;AACnB,6BAAe,OAAO;AAAA,YAC1B;AAAA,UACJ;AACA,UAAAD,oBAAmB,UAAU,SAAS,WAAY;AAC9C,iBAAK,SAAS;AACd,iBAAK,cAAc,KAAK,WAAW,QAAQ,SAAU,WAAW;AAC5D,kBAAI,OAAO,UAAU,WAAW,YAAY;AACxC,0BAAU,OAAO;AAAA,cACrB;AAAA,YACJ,CAAC;AAAA,UACL;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,UAAI;AAAA;AAAA,QAA8B,WAAY;AAC1C,mBAASE,cAAa,OAAO,YAAY;AACrC,iBAAK,MAAM;AACX,iBAAK,WAAW;AAChB,iBAAK,aAAa,cAAc;AAChC,iBAAK,aAAa;AAClB,iBAAK,gBAAgB;AAAA,UACzB;AACA,UAAAA,cAAa,UAAU,YAAY,SAAU,KAAK;AAC9C,gBAAI,IAAI,SAAS,KAAK,cAAc,IAAI,YAAY,KAAK,YAAY,CAAC,MAAM;AACxE,kBAAI,UAAU,KAAK;AAAA;AAEnB,kBAAI,UAAU,KAAK;AACvB,iBAAK,aAAa;AAClB,sBAAU,KAAK,kBAAkB,SAAS,KAAK,UAAU;AACzD,sBAAU,QAAQ,KAAK,SAAU,GAAG,GAAG;AACnC,qBAAO,EAAE,aAAa,EAAE,cAAc,EAAE,SAAS,EAAE,WAC3C,EAAE,WAAW,EAAE,OAAO,cAAc,EAAE,WAAW,EAAE,KAAK;AAAA,YACpE,CAAC;AACD,gBAAI,OAAO;AACX,sBAAU,QAAQ,OAAO,SAAU,MAAM;AACrC,kBAAI,UAAU,KAAK,WAAW,KAAK,WAAW,KAAK;AACnD,kBAAI,YAAY;AACZ,uBAAO;AACX,qBAAO;AACP,qBAAO;AAAA,YACX,CAAC;AACD,iBAAK,WAAW;AAAA,UACpB;AACA,UAAAA,cAAa,UAAU,oBAAoB,SAAU,OAAO,QAAQ;AAChE,gBAAI,UAAU,CAAC;AACf,gBAAI,QAAQ,OAAO,YAAY;AAC/B,gBAAI,QAAQ,OAAO,YAAY;AAC/B,iBAAM,UAAS,IAAI,GAAG,MAAM,OAAO,MAAM,CAAC,GAAG,KAAK;AAC9C,kBAAI,KAAK,YAAY;AACjB,qBAAK,SAAS,KAAK;AACnB,wBAAQ,KAAK,IAAI;AACjB;AAAA,cACJ;AACA,kBAAI,UAAW,CAAC,KAAK,iBAAiB,KAAK,WAAY,KAAK,SAAS,KAAK;AAC1E,kBAAI,CAAC;AACD;AACJ,kBAAI,YAAY;AAChB,kBAAI,YAAY;AAChB,kBAAI,UAAU;AACd,kBAAI,OAAO;AACX,kBAAI,KAAK,YAAY;AACjB,oBAAI,WAAW,QAAQ,OAAO,GAAG,OAAO,MAAM;AAC1C,2BAAS;AAAA,cACjB,OACK;AACD,oBAAI,iBAAiB,QAAQ,YAAY,EAAE,QAAQ,KAAK;AACxD,oBAAI,iBAAiB,IAAI;AACrB,4BAAU;AAAA,gBACd,OACK;AACD,2BAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,wBAAI,KAAK,QAAQ,QAAQ,MAAM,CAAC,GAAG,YAAY,CAAC;AAChD,wBAAI,KAAK,QAAQ,QAAQ,MAAM,CAAC,GAAG,YAAY,CAAC;AAChD,4BAAS,MAAM,IAAO,KAAK,KAAK,KAAK,KAAM,KAAK,KAAM;AACtD,wBAAI,QAAQ;AACR,+BAAS;AACb,+BAAW,QAAQ,YAAY;AAC/B,wBAAI,WAAW,GAAG;AACd,0BAAI,cAAc;AACd,mCAAW;AACf,iCAAW;AACX,kCAAY,YAAa,KAAK;AAAA,oBAClC;AACA,gCAAY;AAAA,kBAChB;AAAA,gBACJ;AAAA,cACJ;AACA,mBAAK,YAAY;AACjB,mBAAK,aAAa,UAAU,IAAI;AAChC,mBAAK,UAAU,KAAK,SAAS,KAAK;AAClC,sBAAQ,KAAK,IAAI;AAAA,YACrB;AACA,mBAAO;AAAA,UACX;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,MAAAnB,SAAQ,eAAe;AACvB,MAAAA,SAAQ,qBAAqB;AAC7B,MAAAA,SAAQ,eAAe;AAAA,IAEvB,CAAC;AAED,QAAI,OAAO,oBAAmB,CAAC,WAAU,WAAU,QAAQ,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AACjG,UAAI;AAAA;AAAA,QAA6B,WAAY;AACzC,mBAASmB,aAAY,SAAS,SAAS;AACnC,gBAAI;AACA,mBAAK,aAAa,QAAQ;AAC9B,iBAAK,UAAU,CAAC;AAChB,iBAAK,UAAU;AACf,oBAAQ,iBAAiB,IAAI;AAAA,UACjC;AACA,UAAAA,aAAY,UAAU,sBAAsB,SAAU,KAAK;AACvD,mBAAO,KAAK,QAAQ,KAAK,SAAU,QAAQ;AACvC,qBAAO,OAAO,MAAM,SAAS,IAAI,KAAK,IAAI,MAAM;AAAA,YACpD,CAAC;AAAA,UACL;AACA,UAAAA,aAAY,UAAU,oBAAoB,SAAU,GAAG,GAAG;AACtD,mBAAO,EAAE,MAAM,MAAM,MAAM,EAAE,MAAM,MAAM;AAAA,UAC7C;AACA,UAAAA,aAAY,UAAU,aAAa,SAAU,SAAS;AAClD,iBAAK,UAAU,QAAQ,KAAK,KAAK,iBAAiB,EAAE,MAAM,GAAG,KAAK,WAAW;AAC7E,iBAAK,QAAQ,QAAQ,kBAAkB;AAAA,UAC3C;AACA,UAAAA,aAAY,UAAU,SAAS,SAAU,MAAM,aAAa,SAAS,QAAQ;AACzE,gBAAI,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ;AAC/B;AACJ,gBAAI,uBAAuB,OAAO,UAAU,qBAAqB,OAAO;AACxE,gBAAI;AACJ,gBAAI,mBAAmB;AACvB,gBAAI,UAAU;AACd,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,kBAAI,SAAS,KAAK,QAAQ,CAAC;AAC3B,kBAAI,OAAO,MAAM,IAAI,MAAM;AACvB;AACJ,kBAAI,OAAO,MAAM,MAAM,MAAM;AACzB;AACJ,kBAAI,OAAO,MAAM,MAAM,QAAQ,SAAS;AACpC;AAAA,cACJ,OACK;AACD,0BAAU,OAAO,MAAM,MAAM;AAC7B,mCAAmB;AAAA,cACvB;AACA,kBAAI,mBAAmB,KAAK;AACxB;AAAA,cACJ;AACA,kBAAI,qBAAqB,OAAO,MAAM,SAAS,sBAAsB,kBAAkB;AACvF,kBAAI,mBAAmB,MAAM,QAAQ,mBAAmB,IAAI,OACrD,mBAAmB,MAAM,WAAW,mBAAmB,IAAI,QAAQ;AACtE;AAAA,cACJ;AACA,kBAAI,cAAc,mBAAmB,cAAc,OAAO;AAC1D,kBAAI,YAAY,QAAQ,GAAG;AACvB,2BAAW,QAAQ,gBAAgB,mBAAmB,IAAI,KAAK,QAAQ;AACvE,oBAAI,YAAY,SAAS,IAAI,MAAM,mBAAmB,IAAI,KAAK;AAC3D,yCAAuB,SAAS,IAAI;AAAA,gBACxC;AACA;AAAA,cACJ;AACA,kBAAI,KAAK,eAAe,YAAY;AAChC,4BAAY,mBAAmB,MAAM,aAAa,OAAO,WAAW,MAAM;AAAA,cAC9E,WACS,YAAY,YAAY,GAAG;AAChC,oBAAI,KAAK,eAAe;AACpB,8BAAY,oBAAoB,MAAM,aAAa,OAAO,WAAW,MAAM;AAAA;AAE3E,8BAAY,eAAe,MAAM,aAAa,OAAO,WAAW,MAAM;AAAA,cAC9E,OACK;AACD,4BAAY,qBAAqB,MAAM,aAAa,OAAO,YAAY,aAAa,MAAM;AAAA,cAC9F;AAAA,YACJ;AAAA,UACJ;AACA,iBAAOA;AAAA,QACX,EAAE;AAAA;AACF,kBAAY,UAAU,cAAc;AACpC,MAAApB,SAAQ,cAAc;AAAA,IAEtB,CAAC;AAED,QAAI,OAAO,mCAAkC,CAAC,WAAU,WAAU,UAAS,WAAW,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC,UAAI,QAAQF,SAAQ,UAAU,EAAE;AAC5J,UAAI,aAAa;AACjB,eAAS,aAAa,KAAK,KAAK;AAC5B,YAAI,aAAa,IAAI,aAAa,MAAM,WAAW;AAAA,UAC/C,KAAK;AAAA,UACL,QAAQ;AAAA,QACZ,GAAG,GAAG,CAAC;AACP,eAAO,WAAW,MAAM,UAAU,EAAE,SAAS;AAAA,MACjD;AACA,eAAS,aAAa,KAAK,KAAK;AAC5B,YAAI,YAAY,aAAa,KAAK,GAAG;AACrC,YAAI,QAAQ,IAAI,SAAS,EAAE,MAAM,UAAU;AAC3C,YAAI,aAAa,uBAAO,OAAO,IAAI;AACnC,YAAI,cAAc,MAAM,SAAS;AACjC,cAAM,QAAQ,SAAU,MAAM,KAAK;AAC/B,cAAI,CAAC,QAAQ,SAAS;AAClB;AACJ,cAAI,WAAW,KAAK,IAAI,YAAY,GAAG;AACvC,cAAI,QAAQ,MAAM,SAAS;AAC3B,cAAI,WAAW,IAAI,GAAG;AAClB,uBAAW,IAAI,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,CAAC;AAAA,UACvD,OACK;AACD,uBAAW,IAAI,IAAI;AAAA,UACvB;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AACA,MAAAC,SAAQ,iBAAiB,SAAU,QAAQ,SAAS,KAAK,QAAQ,UAAU;AACvE,YAAI,YAAY,aAAa,SAAS,GAAG;AACzC,YAAI,WAAW,OAAO,KAAK,SAAS;AACpC,iBAAS,MAAM,SAAS,IAAI,SAAU,MAAM;AACxC,iBAAO;AAAA,YACH,SAAS;AAAA,YACT,OAAO;AAAA,YACP,OAAO,UAAU,IAAI;AAAA,YACrB,MAAM;AAAA,UACV;AAAA,QACJ,CAAC,CAAC;AAAA,MACN;AAAA,IAEA,CAAC;AAED,QAAI,OAAO,0BAAyB,CAAC,WAAU,WAAU,UAAS,gBAAe,oBAAmB,cAAa,gBAAe,yBAAwB,oBAAmB,mCAAkC,cAAa,YAAY,GAAG,SAASD,UAASC,UAASC,SAAO;AAyB3Q;AACA,UAAI,iBAAiBF,SAAQ,aAAa,EAAE;AAC5C,UAAI,eAAeA,SAAQ,iBAAiB,EAAE;AAC9C,UAAI,SAASA,SAAQ,WAAW;AAChC,UAAI,OAAOA,SAAQ,aAAa;AAChC,UAAI,OAAOA,SAAQ,sBAAsB;AACzC,UAAI,cAAcA,SAAQ,iBAAiB,EAAE;AAC7C,UAAI,gBAAgBA,SAAQ,gCAAgC;AAC5D,UAAI,mBAAmB;AAAA,QACnB,gBAAgB,SAAU,QAAQ,SAAS,KAAK,QAAQ,UAAU;AAC9D,cAAI,QAAQ,MAAM,WAAW;AACzB,mBAAO,QAAQ,MAAM,UAAU,eAAe,QAAQ,SAAS,KAAK,QAAQ,QAAQ;AAAA,UACxF;AACA,cAAI,QAAQ,OAAO,QAAQ,SAAS,IAAI,GAAG;AAC3C,cAAI,cAAc,QAAQ,MAAM,eAAe,OAAO,SAAS,KAAK,MAAM;AAC1E,wBAAc,YAAY,IAAI,SAAU,IAAI;AACxC,eAAG,cAAc,iBAAiB;AAClC,mBAAO;AAAA,UACX,CAAC;AACD,mBAAS,MAAM,WAAW;AAAA,QAC9B;AAAA,QACA,IAAI;AAAA,MACR;AACA,UAAI,0BAA0B,SAAU,KAAK;AACzC,YAAI,SAAS,CAAC;AACd,eAAO,IAAI,QAAQ,uBAAuB,SAAU,GAAG,IAAI,IAAI,IAAI;AAC/D,iBAAQ,OAAO,EAAE,IAAI,MAAM;AAAA,QAC/B,CAAC,EAAE,QAAQ,aAAa,SAAU,GAAG,IAAI;AACrC,iBAAO,OAAO,EAAE;AAAA,QACpB,CAAC;AAAA,MACL;AACA,UAAI,mBAAmB;AAAA,QACnB,gBAAgB,SAAU,QAAQ,SAAS,KAAK,QAAQ,UAAU;AAC9D,cAAI,SAAS,CAAC;AACd,cAAI,QAAQ,QAAQ,WAAW,IAAI,KAAK,IAAI,MAAM;AAClD,cAAI,SAAS,MAAM,KAAK,MAAM,yEAAyE;AACnG,mBAAO,KAAK,UAAU;AAAA;AAEtB,qBAAS,eAAe,gBAAgB,MAAM;AAClD,cAAI,aAAa,eAAe;AAChC,cAAI,cAAc,CAAC;AACnB,iBAAO,QAAQ,SAAU,OAAO;AAC5B,gBAAI,WAAW,WAAW,KAAK,KAAK,CAAC;AACrC,qBAAS,IAAI,SAAS,QAAQ,OAAM;AAChC,kBAAI,IAAI,SAAS,CAAC;AAClB,kBAAI,UAAU,EAAE,QAAQ,EAAE;AAC1B,kBAAI,CAAC;AACD;AACJ,0BAAY,KAAK;AAAA,gBACb;AAAA,gBACA,SAAS,EAAE;AAAA,gBACX,MAAM,EAAE,cAAc,CAAC,EAAE,OAAO,EAAE,aAAa,OAAY;AAAA,gBAC3D,aAAa,iBAAiB;AAAA,cAClC,CAAC;AAAA,YACL;AAAA,UACJ,GAAG,IAAI;AACP,mBAAS,MAAM,WAAW;AAAA,QAC9B;AAAA,QACA,eAAe,SAAU,MAAM;AAC3B,cAAI,KAAK,WAAW,CAAC,KAAK,SAAS;AAC/B,iBAAK,UAAU;AAAA,cACX;AAAA,cAAO,KAAK,WAAW,KAAK,OAAO;AAAA,cAAG;AAAA,cAAQ;AAAA,cAC9C,KAAK,WAAW,wBAAwB,KAAK,OAAO,CAAC;AAAA,YACzD,EAAE,KAAK,EAAE;AAAA,UACb;AAAA,QACJ;AAAA,QACA,IAAI;AAAA,MACR;AACA,UAAI,aAAa,CAAC,kBAAkB,eAAe,gBAAgB;AACnE,MAAAC,SAAQ,gBAAgB,SAAU,KAAK;AACnC,mBAAW,SAAS;AACpB,YAAI;AACA,qBAAW,KAAK,MAAM,YAAY,GAAG;AAAA,MAC7C;AACA,MAAAA,SAAQ,eAAe,SAAU,WAAW;AACxC,mBAAW,KAAK,SAAS;AAAA,MAC7B;AACA,MAAAA,SAAQ,gBAAgB;AACxB,MAAAA,SAAQ,mBAAmB;AAC3B,MAAAA,SAAQ,mBAAmB;AAC3B,UAAI,gBAAgB;AAAA,QAChB,MAAM;AAAA,QACN,MAAM,SAAU,QAAQ;AACpB,iBAAO,eAAe,cAAc,MAAM;AAAA,QAC9C;AAAA,QACA,SAAS;AAAA,MACb;AACA,UAAI,eAAe,SAAU,GAAG,QAAQ;AACpC,4BAAoB,OAAO,QAAQ,KAAK;AAAA,MAC5C;AACA,UAAI,sBAAsB,SAAU,MAAM;AACtC,YAAI,OAAO,QAAQ;AACf,iBAAO,OAAO,OAAO,IAAI;AAC7B,YAAI,CAAC;AACD;AACJ,YAAI,CAAC,eAAe;AAChB,yBAAe,QAAQ,CAAC;AAC5B,wBAAgB,KAAK,KAAK,KAAK,aAAa;AAC5C,YAAI,KAAK;AACL,eAAK,MAAM,QAAQ,mBAAmB;AAAA,MAC9C;AACA,UAAI,kBAAkB,SAAU,IAAI,iBAAiB;AACjD,YAAI,CAAC,mBAAmB,CAAC,MAAM,eAAe,MAAM,EAAE;AAClD;AACJ,uBAAe,MAAM,EAAE,IAAI,CAAC;AAC5B,eAAO,WAAW,iBAAiB,SAAU,GAAG;AAC5C,cAAI,CAAC;AACD;AACJ,yBAAe,MAAM,EAAE,IAAI;AAC3B,cAAI,CAAC,EAAE,YAAY,EAAE;AACjB,cAAE,WAAW,eAAe,iBAAiB,EAAE,WAAW;AAC9D,yBAAe,SAAS,EAAE,YAAY,CAAC,GAAG,EAAE,KAAK;AACjD,cAAI,EAAE,eAAe;AACjB,2BAAe,WAAW,EAAE,KAAK,EAAE,gBAAgB,EAAE;AACrD,cAAE,cAAc,QAAQ,SAAU,GAAG;AACjC,kCAAoB,cAAc,CAAC;AAAA,YACvC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,qBAAqB,SAAU,GAAG;AAClC,YAAI,SAAS,EAAE;AACf,YAAI,eAAe,OAAO,aAAa,OAAO,UAAU;AACxD,YAAI,EAAE,QAAQ,SAAS,aAAa;AAChC,cAAI,gBAAgB,CAAC,KAAK,oBAAoB,MAAM;AAChD,mBAAO,UAAU,OAAO;AAAA,QAChC,WACS,EAAE,QAAQ,SAAS,kBAAkB,CAAC,cAAc;AACzD,0BAAgB;AAChB,cAAI,QAAQ,EAAE,OAAO;AACrB,cAAI,OAAO;AACP,kCAAsB,MAAM,KAAK;AAAA,UACrC,OACK;AACD,iCAAqB,CAAC;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ;AACA,UAAI;AACJ,UAAI,wBAAwB,KAAK,YAAY,WAAY;AACrD,6BAAqB,aAAa;AAAA,MACtC,GAAG,CAAC;AACJ,UAAI,uBAAuB,SAAU,GAAG;AACpC,YAAI,SAAS,EAAE;AACf,YAAI,SAAS,KAAK,oBAAoB,MAAM;AAC5C,YAAI,eAAe,EAAE;AACrB,YAAI,sBAAsB,KAAK,oBAAoB,QAAQ,YAAY;AACvE,YAAI,UAAU,OAAO,UAAU,OAAO,gCAAgC,qBAAqB;AACvF,cAAI,YAAY,aAAa,IAAI,MAAM;AACvC,oBAAU,YAAY;AACtB,oBAAU,UAAU,MAAM;AAAA,QAC9B;AAAA,MACJ;AACA,UAAI,SAASD,SAAQ,WAAW,EAAE;AAClC,MAAAA,SAAQ,WAAW,EAAE,cAAc,OAAO,WAAW,UAAU;AAAA,QAC3D,2BAA2B;AAAA,UACvB,KAAK,SAAU,KAAK;AAChB,gBAAI,KAAK;AACL,2BAAa,IAAI,IAAI;AACrB,kBAAI,CAAC,KAAK;AACN,qBAAK,aAAa,MAAM,QAAQ,GAAG,IAAI,MAAM;AACjD,mBAAK,SAAS,WAAW,aAAa,YAAY;AAAA,YACtD,OACK;AACD,mBAAK,SAAS,cAAc,aAAa,YAAY;AAAA,YACzD;AAAA,UACJ;AAAA,UACA,OAAO;AAAA,QACX;AAAA,QACA,0BAA0B;AAAA,UACtB,KAAK,SAAU,KAAK;AAChB,gBAAI,KAAK;AACL,kBAAI,CAAC,KAAK;AACN,qBAAK,aAAa,MAAM,QAAQ,GAAG,IAAI,MAAM;AACjD,mBAAK,SAAS,GAAG,aAAa,kBAAkB;AAAA,YACpD,OACK;AACD,mBAAK,SAAS,IAAI,aAAa,kBAAkB;AAAA,YACrD;AAAA,UACJ;AAAA,UACA,OAAO;AAAA,QACX;AAAA,QACA,yBAAyB;AAAA,UACrB,cAAc;AAAA,QAClB;AAAA,QACA,6BAA6B;AAAA,UACzB,cAAc;AAAA,QAClB;AAAA,QACA,gBAAgB;AAAA,UACZ,KAAK,SAAU,KAAK;AAChB,gBAAI,KAAK;AACL,mBAAK,SAAS,WAAW,aAAa;AACtC,mBAAK,GAAG,cAAc,YAAY;AAClC,2BAAa,MAAM,IAAI;AAAA,YAC3B,OACK;AACD,mBAAK,SAAS,cAAc,aAAa;AACzC,mBAAK,IAAI,cAAc,YAAY;AAAA,YACvC;AAAA,UACJ;AAAA,UACA,OAAO;AAAA,QACX;AAAA,MACJ,CAAC;AACD,MAAAC,SAAQ,cAAc;AAAA,IAEtB,CAAC;AAAkB,KAAC,WAAW;AACX,UAAI,QAAQ,CAAC,wBAAwB,GAAG,SAAS,GAAG;AAChD,YAAI,OAAO,UAAU,YAAY,OAAO,WAAW,YAAY,QAAQ;AACnE,iBAAO,UAAU;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,GAAG;AAAA;AAAA;", "names": ["require", "exports", "module", "SnippetManager", "ch", "p", "i", "id", "ts", "i1", "value", "TabstopManager", "AcePopup", "el", "AceInlineScreenReader", "AceInline", "Autocomplete", "prefix", "CompletionProvider", "results", "FilteredList", "MarkerGroup"]}
from pydantic import BaseModel, ConfigDict, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from fastapi_crud_tortoise import MODEL_ID_TYPE


class OrganizationCreateDTO(BaseModel):
    name: str
    parent_id: Optional[MODEL_ID_TYPE] = None
    area: str
    location: str
    status: bool


class Ref_OrganizationDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    level: Optional[int] = None
    area: Optional[str] = None
    location: Optional[str] = None
    status: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


class OrganizationDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    parent: Optional[Ref_OrganizationDTO] = None
    parent_id: Optional[MODEL_ID_TYPE] = None
    children: Optional[List[Ref_OrganizationDTO]] = None
    children_refids: Optional[List[MODEL_ID_TYPE]] = None
    level: Optional[int] = None
    area: Optional[str] = None
    location: Optional[str] = None
    status: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)

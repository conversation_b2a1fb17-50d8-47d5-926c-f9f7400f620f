import asyncio
import json
from fastapi import APIRouter, HTTPException, Request

from starlette.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from .schema import RecordQueryParams, RecordType
import asyncpg
from datetime import datetime, timezone
from typing import List, Optional
from settings import APP_SETTINGS
from log import log

router = APIRouter()


@router.post("/record_sse_post/", description="SSE方式返回记录数据")
async def record_sse_post(params: RecordQueryParams, request: Request):
    async def event_generator():
        try:
            conn = await asyncpg.connect(
                host="127.0.0.1",
                port=8812,
                user="admin",
                password="quest",
                database="qdb",
            )

            try:
                # Convert to naive datetime for asyncpg
                start_dt = params.start_time.replace(tzinfo=None)
                end_dt = params.end_time.replace(tzinfo=None)

                batch_size = 1000
                last_timestamp = None

                while True:
                    # 构建分页查询
                    if last_timestamp is None:
                        query = f"""
                        SELECT * FROM {params.record_type} 
                        WHERE Tstamp >= $1 AND Tstamp <= $2
                        ORDER BY Tstamp
                        LIMIT $3
                        """
                        params_query = (start_dt, end_dt, batch_size)
                    else:
                        # Convert timestamp back to naive datetime
                        last_dt = datetime.fromtimestamp(last_timestamp)
                        query = f"""
                        SELECT * FROM {params.record_type} 
                        WHERE Tstamp > $1 AND Tstamp <= $2
                        ORDER BY Tstamp
                        LIMIT $3
                        """
                        params_query = (last_dt, end_dt, batch_size)

                    records = await conn.fetch(query, *params_query)

                    if not records:
                        break

                    batch = []
                    for record in records:
                        record_dict = dict(record)
                        if "Tstamp" in record_dict:
                            # 转换时区敏感的datetime为timestamp
                            ts = record_dict["Tstamp"]
                            if ts.tzinfo is None:
                                ts = ts.replace(tzinfo=timezone.utc)
                            record_dict["Tstamp"] = ts.timestamp()
                            last_timestamp = record_dict["Tstamp"]
                        batch.append(record_dict)

                    yield {
                        "event": "data",
                        "data": {
                            "batch": batch,
                            "has_more": len(records) == batch_size,
                        },
                    }

                    if await request.is_disconnected() or len(records) < batch_size:
                        break

                    await asyncio.sleep(0.1)

                yield {"event": "end"}

            finally:
                await conn.close()

        except Exception as e:
            yield {"event": "error", "data": json.dumps({"error": str(e)})}

    return EventSourceResponse(event_generator())


@router.get(
    "/record_sse/{record_type}/{start_time}/{end_time}",
    description="SSE方式返回记录数据",
)
async def record_sse(
    record_type: RecordType,
    start_time: datetime,
    end_time: datetime,
    request: Request,
    fields: Optional[str] = None,  # 用逗号分隔的字段列表
):
    async def event_generator():
        try:
            # 检查客户端是否已断开连接
            if await request.is_disconnected():
                log.info(f"客户端已断开连接，停止生成事件")
                return

            conn = await asyncpg.connect(
                host=APP_SETTINGS.QDB_HOST,
                port=APP_SETTINGS.QDB_PORT,
                user=APP_SETTINGS.QDB_USER,
                password=APP_SETTINGS.QDB_PASSWORD,
                database=APP_SETTINGS.QDB_DATABASE,
            )

            try:
                # 解析字段列表
                selected_fields = fields.split(",") if fields else ["*"]

                # 构建SELECT部分 - 确保Tstamp在第一位，并转换为时间戳格式
                if "*" in selected_fields:
                    # 使用 * 但将 Tstamp 转换为时间戳（微秒）
                    select_clause = "cast(Tstamp as long) / 1000000.0 as Tstamp, * "
                else:
                    # 确保包含Tstamp字段
                    if "Tstamp" not in selected_fields:
                        selected_fields.insert(0, "Tstamp")
                    else:
                        # 如果已有Tstamp，移动到第一位
                        selected_fields.remove("Tstamp")

                    # 构造字段列表，确保Tstamp在第一位并转换为时间戳
                    select_clause = (
                        "cast(Tstamp as long) / 1000000.0 as Tstamp"  # 转换为秒级时间戳
                    )
                    other_fields = [f for f in selected_fields]
                    if other_fields:
                        select_clause += ", " + ", ".join(other_fields)

                log.info(f"查询时间范围: {start_time} 到 {end_time}")

                batch_size = APP_SETTINGS.SSE_BATCH_SIZE
                last_timestamp = None

                while True:
                    # 构建分页查询
                    query = f"""
                        SELECT {select_clause} FROM {record_type} 
                        WHERE Tstamp > $1 AND Tstamp <= $2
                        ORDER BY Tstamp
                        LIMIT $3
                        """

                    if last_timestamp is None:
                        params_query = (start_time, end_time, batch_size)
                    else:
                        last_dt = datetime.fromtimestamp(last_timestamp, tz=None)
                        params_query = (last_dt, end_time, batch_size)

                    # 显示完整的SQL（用于调试）
                    try:
                        formatted_query = query
                        for i, param in enumerate(params_query, 1):
                            formatted_query = formatted_query.replace(
                                f"${i}", f"'{param}'"
                            )
                        log.info(f"完整SQL: {formatted_query}")
                    except Exception as e:
                        log.warning(f"格式化SQL失败: {e}")

                    records = await conn.fetch(query, *params_query)

                    if not records:
                        break

                    batch = []
                    for record in records:
                        record_dict = dict(record)
                        if "Tstamp" in record_dict:
                            last_timestamp = record_dict["Tstamp"]
                        batch.append(record_dict)

                    log.warning(f"event: data, batch size: {len(batch)}")
                    yield {
                        "event": "data",
                        "data": {
                            "batch": batch,
                            "has_more": len(records) == batch_size,
                        },
                    }

                    if await request.is_disconnected() or len(records) < batch_size:
                        break

                    await asyncio.sleep(0.1)

                log.warning(f"event: end")
                yield {"event": "end"}

            finally:
                await conn.close()

        except Exception as e:
            log.warning(f"event: error: {e}")
            yield {"event": "error", "data": json.dumps({"error": str(e)})}

    return EventSourceResponse(event_generator())


async def get_record_csv(params: RecordQueryParams):
    try:
        conn = await asyncpg.connect(
            host="127.0.0.1",
            port=8812,
            user="admin",
            password="quest",
            database="qdb",
        )

        try:
            # Convert to naive datetime for asyncpg
            start_dt = params.start_time.replace(tzinfo=None)
            end_dt = params.end_time.replace(tzinfo=None)

            # First query to get column names
            query = f"SELECT * FROM {params.record_type} WHERE Tstamp >= $1 AND Tstamp <= $2 LIMIT 1"
            sample_record = await conn.fetchrow(query, start_dt, end_dt)
            if not sample_record:
                return

            # Get column names and create header
            field_names = sample_record.keys()
            header = ",".join(field_names)
            yield f"{header}\n"

            batch_size = 1000
            last_timestamp = None

            while True:
                # 构建分页查询
                if last_timestamp is None:
                    query = f"""
                    SELECT * FROM {params.record_type} 
                    WHERE Tstamp >= $1 AND Tstamp <= $2
                    ORDER BY Tstamp
                    LIMIT $3
                    """
                    params_query = (start_dt, end_dt, batch_size)
                else:
                    # Convert timestamp back to naive datetime
                    last_dt = datetime.fromtimestamp(last_timestamp)
                    query = f"""
                    SELECT * FROM {params.record_type} 
                    WHERE Tstamp > $1 AND Tstamp <= $2
                    ORDER BY Tstamp
                    LIMIT $3
                    """
                    params_query = (last_dt, end_dt, batch_size)

                records = await conn.fetch(query, *params_query)

                if not records:
                    break

                # Generate CSV rows
                for record in records:
                    record_dict = dict(record)
                    # Convert datetime to string
                    if "Tstamp" in record_dict:
                        ts = record_dict["Tstamp"]
                        record_dict["Tstamp"] = ts.isoformat()
                        last_timestamp = ts.timestamp()

                    # Convert values to string and join with comma
                    values = [
                        str(v) if v is not None else "" for v in record_dict.values()
                    ]
                    yield ",".join(values) + "\n"

        finally:
            await conn.close()

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export_csv", description="stream方式返回csv格式记录")
async def record_csv(params: RecordQueryParams, request: Request):
    csv_generator = get_record_csv(params)
    return StreamingResponse(csv_generator, media_type="text/csv")

{"version": 3, "sources": ["../../.pnpm/@better-scroll+core@2.5.1/node_modules/@better-scroll/core/dist/core.esm.js"], "sourcesContent": ["/*!\n * better-scroll / core\n * (c) 2016-2023 ustbhuangyi\n * Released under the MIT License.\n */\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\n\nvar propertiesConfig = [\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.currentPos',\r\n        key: 'x'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.currentPos',\r\n        key: 'y'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.hasScroll',\r\n        key: 'hasHorizontalScroll'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.hasScroll',\r\n        key: 'hasVerticalScroll'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.contentSize',\r\n        key: 'scrollerWidth'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.contentSize',\r\n        key: 'scrollerHeight'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.maxScrollPos',\r\n        key: 'maxScrollX'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.maxScrollPos',\r\n        key: 'maxScrollY'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.minScrollPos',\r\n        key: 'minScrollX'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.minScrollPos',\r\n        key: 'minScrollY'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.movingDirection',\r\n        key: 'movingDirectionX'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.movingDirection',\r\n        key: 'movingDirectionY'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorX.direction',\r\n        key: 'directionX'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBehaviorY.direction',\r\n        key: 'directionY'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.actions.enabled',\r\n        key: 'enabled'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.animater.pending',\r\n        key: 'pending'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.animater.stop',\r\n        key: 'stop'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollTo',\r\n        key: 'scrollTo'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollBy',\r\n        key: 'scrollBy'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.scrollToElement',\r\n        key: 'scrollToElement'\r\n    },\r\n    {\r\n        sourceKey: 'scroller.resetPosition',\r\n        key: 'resetPosition'\r\n    }\r\n];\n\nfunction warn(msg) {\r\n    console.error(\"[BScroll warn]: \" + msg);\r\n}\n\n// ssr support\r\nvar inBrowser = typeof window !== 'undefined';\r\nvar ua = inBrowser && navigator.userAgent.toLowerCase();\r\nvar isWeChatDevTools = !!(ua && /wechatdevtools/.test(ua));\r\nvar isAndroid = ua && ua.indexOf('android') > 0;\r\n/* istanbul ignore next */\r\nvar isIOSBadVersion = (function () {\r\n    if (typeof ua === 'string') {\r\n        var regex = /os (\\d\\d?_\\d(_\\d)?)/;\r\n        var matches = regex.exec(ua);\r\n        if (!matches)\r\n            return false;\r\n        var parts = matches[1].split('_').map(function (item) {\r\n            return parseInt(item, 10);\r\n        });\r\n        // ios version >= 13.4 issue 982\r\n        return !!(parts[0] === 13 && parts[1] >= 4);\r\n    }\r\n    return false;\r\n})();\r\n/* istanbul ignore next */\r\nvar supportsPassive = false;\r\n/* istanbul ignore next */\r\nif (inBrowser) {\r\n    var EventName = 'test-passive';\r\n    try {\r\n        var opts = {};\r\n        Object.defineProperty(opts, 'passive', {\r\n            get: function () {\r\n                supportsPassive = true;\r\n            },\r\n        }); // https://github.com/facebook/flow/issues/285\r\n        window.addEventListener(EventName, function () { }, opts);\r\n    }\r\n    catch (e) { }\r\n}\n\nfunction getNow() {\r\n    return window.performance &&\r\n        window.performance.now &&\r\n        window.performance.timing\r\n        ? window.performance.now() + window.performance.timing.navigationStart\r\n        : +new Date();\r\n}\r\nvar extend = function (target, source) {\r\n    for (var key in source) {\r\n        target[key] = source[key];\r\n    }\r\n    return target;\r\n};\r\nfunction isUndef(v) {\r\n    return v === undefined || v === null;\r\n}\r\nfunction between(x, min, max) {\r\n    if (x < min) {\r\n        return min;\r\n    }\r\n    if (x > max) {\r\n        return max;\r\n    }\r\n    return x;\r\n}\n\nvar elementStyle = (inBrowser &&\r\n    document.createElement('div').style);\r\nvar vendor = (function () {\r\n    /* istanbul ignore if  */\r\n    if (!inBrowser) {\r\n        return false;\r\n    }\r\n    var transformNames = [\r\n        {\r\n            key: 'standard',\r\n            value: 'transform',\r\n        },\r\n        {\r\n            key: 'webkit',\r\n            value: 'webkitTransform',\r\n        },\r\n        {\r\n            key: 'Moz',\r\n            value: 'MozTransform',\r\n        },\r\n        {\r\n            key: 'O',\r\n            value: 'OTransform',\r\n        },\r\n        {\r\n            key: 'ms',\r\n            value: 'msTransform',\r\n        },\r\n    ];\r\n    for (var _i = 0, transformNames_1 = transformNames; _i < transformNames_1.length; _i++) {\r\n        var obj = transformNames_1[_i];\r\n        if (elementStyle[obj.value] !== undefined) {\r\n            return obj.key;\r\n        }\r\n    }\r\n    /* istanbul ignore next  */\r\n    return false;\r\n})();\r\n/* istanbul ignore next  */\r\nfunction prefixStyle(style) {\r\n    if (vendor === false) {\r\n        return style;\r\n    }\r\n    if (vendor === 'standard') {\r\n        if (style === 'transitionEnd') {\r\n            return 'transitionend';\r\n        }\r\n        return style;\r\n    }\r\n    return vendor + style.charAt(0).toUpperCase() + style.substr(1);\r\n}\r\nfunction getElement(el) {\r\n    return (typeof el === 'string' ? document.querySelector(el) : el);\r\n}\r\nfunction addEvent(el, type, fn, capture) {\r\n    var useCapture = supportsPassive\r\n        ? {\r\n            passive: false,\r\n            capture: !!capture,\r\n        }\r\n        : !!capture;\r\n    el.addEventListener(type, fn, useCapture);\r\n}\r\nfunction removeEvent(el, type, fn, capture) {\r\n    el.removeEventListener(type, fn, {\r\n        capture: !!capture,\r\n    });\r\n}\r\nfunction maybePrevent(e) {\r\n    if (e.cancelable) {\r\n        e.preventDefault();\r\n    }\r\n}\r\nfunction offset(el) {\r\n    var left = 0;\r\n    var top = 0;\r\n    while (el) {\r\n        left -= el.offsetLeft;\r\n        top -= el.offsetTop;\r\n        el = el.offsetParent;\r\n    }\r\n    return {\r\n        left: left,\r\n        top: top,\r\n    };\r\n}\r\nvendor && vendor !== 'standard' ? '-' + vendor.toLowerCase() + '-' : '';\r\nvar transform = prefixStyle('transform');\r\nvar transition = prefixStyle('transition');\r\nvar hasPerspective = inBrowser && prefixStyle('perspective') in elementStyle;\r\n// fix issue #361\r\nvar hasTouch = inBrowser && ('ontouchstart' in window || isWeChatDevTools);\r\nvar hasTransition = inBrowser && transition in elementStyle;\r\nvar style = {\r\n    transform: transform,\r\n    transition: transition,\r\n    transitionTimingFunction: prefixStyle('transitionTimingFunction'),\r\n    transitionDuration: prefixStyle('transitionDuration'),\r\n    transitionDelay: prefixStyle('transitionDelay'),\r\n    transformOrigin: prefixStyle('transformOrigin'),\r\n    transitionEnd: prefixStyle('transitionEnd'),\r\n    transitionProperty: prefixStyle('transitionProperty'),\r\n};\r\nvar eventTypeMap = {\r\n    touchstart: 1,\r\n    touchmove: 1,\r\n    touchend: 1,\r\n    touchcancel: 1,\r\n    mousedown: 2,\r\n    mousemove: 2,\r\n    mouseup: 2,\r\n};\r\nfunction getRect(el) {\r\n    /* istanbul ignore if  */\r\n    if (el instanceof window.SVGElement) {\r\n        var rect = el.getBoundingClientRect();\r\n        return {\r\n            top: rect.top,\r\n            left: rect.left,\r\n            width: rect.width,\r\n            height: rect.height,\r\n        };\r\n    }\r\n    else {\r\n        return {\r\n            top: el.offsetTop,\r\n            left: el.offsetLeft,\r\n            width: el.offsetWidth,\r\n            height: el.offsetHeight,\r\n        };\r\n    }\r\n}\r\nfunction preventDefaultExceptionFn(el, exceptions) {\r\n    for (var i in exceptions) {\r\n        if (exceptions[i].test(el[i])) {\r\n            return true;\r\n        }\r\n    }\r\n    return false;\r\n}\r\nvar tagExceptionFn = preventDefaultExceptionFn;\r\nfunction tap(e, eventName) {\r\n    var ev = document.createEvent('Event');\r\n    ev.initEvent(eventName, true, true);\r\n    ev.pageX = e.pageX;\r\n    ev.pageY = e.pageY;\r\n    e.target.dispatchEvent(ev);\r\n}\r\nfunction click(e, event) {\r\n    if (event === void 0) { event = 'click'; }\r\n    var eventSource;\r\n    if (e.type === 'mouseup') {\r\n        eventSource = e;\r\n    }\r\n    else if (e.type === 'touchend' || e.type === 'touchcancel') {\r\n        eventSource = e.changedTouches[0];\r\n    }\r\n    var posSrc = {};\r\n    if (eventSource) {\r\n        posSrc.screenX = eventSource.screenX || 0;\r\n        posSrc.screenY = eventSource.screenY || 0;\r\n        posSrc.clientX = eventSource.clientX || 0;\r\n        posSrc.clientY = eventSource.clientY || 0;\r\n    }\r\n    var ev;\r\n    var bubbles = true;\r\n    var cancelable = true;\r\n    var ctrlKey = e.ctrlKey, shiftKey = e.shiftKey, altKey = e.altKey, metaKey = e.metaKey;\r\n    var pressedKeysMap = {\r\n        ctrlKey: ctrlKey,\r\n        shiftKey: shiftKey,\r\n        altKey: altKey,\r\n        metaKey: metaKey,\r\n    };\r\n    if (typeof MouseEvent !== 'undefined') {\r\n        try {\r\n            ev = new MouseEvent(event, extend(__assign({ bubbles: bubbles,\r\n                cancelable: cancelable }, pressedKeysMap), posSrc));\r\n        }\r\n        catch (e) {\r\n            /* istanbul ignore next */\r\n            createEvent();\r\n        }\r\n    }\r\n    else {\r\n        createEvent();\r\n    }\r\n    function createEvent() {\r\n        ev = document.createEvent('Event');\r\n        ev.initEvent(event, bubbles, cancelable);\r\n        extend(ev, posSrc);\r\n    }\r\n    // forwardedTouchEvent set to true in case of the conflict with fastclick\r\n    ev.forwardedTouchEvent = true;\r\n    ev._constructed = true;\r\n    e.target.dispatchEvent(ev);\r\n}\r\nfunction dblclick(e) {\r\n    click(e, 'dblclick');\r\n}\n\nvar ease = {\r\n    // easeOutQuint\r\n    swipe: {\r\n        style: 'cubic-bezier(0.23, 1, 0.32, 1)',\r\n        fn: function (t) {\r\n            return 1 + --t * t * t * t * t;\r\n        }\r\n    },\r\n    // easeOutQuard\r\n    swipeBounce: {\r\n        style: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',\r\n        fn: function (t) {\r\n            return t * (2 - t);\r\n        }\r\n    },\r\n    // easeOutQuart\r\n    bounce: {\r\n        style: 'cubic-bezier(0.165, 0.84, 0.44, 1)',\r\n        fn: function (t) {\r\n            return 1 - --t * t * t * t;\r\n        }\r\n    }\r\n};\n\nvar DEFAULT_INTERVAL = 1000 / 60;\r\nvar windowCompat = inBrowser && window;\r\n/* istanbul ignore next */\r\nfunction noop$1() { }\r\nvar requestAnimationFrame = (function () {\r\n    /* istanbul ignore if  */\r\n    if (!inBrowser) {\r\n        return noop$1;\r\n    }\r\n    return (windowCompat.requestAnimationFrame ||\r\n        windowCompat.webkitRequestAnimationFrame ||\r\n        windowCompat.mozRequestAnimationFrame ||\r\n        windowCompat.oRequestAnimationFrame ||\r\n        // if all else fails, use setTimeout\r\n        function (callback) {\r\n            return window.setTimeout(callback, callback.interval || DEFAULT_INTERVAL); // make interval as precise as possible.\r\n        });\r\n})();\r\nvar cancelAnimationFrame = (function () {\r\n    /* istanbul ignore if  */\r\n    if (!inBrowser) {\r\n        return noop$1;\r\n    }\r\n    return (windowCompat.cancelAnimationFrame ||\r\n        windowCompat.webkitCancelAnimationFrame ||\r\n        windowCompat.mozCancelAnimationFrame ||\r\n        windowCompat.oCancelAnimationFrame ||\r\n        function (id) {\r\n            window.clearTimeout(id);\r\n        });\r\n})();\n\n/* istanbul ignore next */\r\nvar noop = function (val) { };\r\nvar sharedPropertyDefinition = {\r\n    enumerable: true,\r\n    configurable: true,\r\n    get: noop,\r\n    set: noop,\r\n};\r\nvar getProperty = function (obj, key) {\r\n    var keys = key.split('.');\r\n    for (var i = 0; i < keys.length - 1; i++) {\r\n        obj = obj[keys[i]];\r\n        if (typeof obj !== 'object' || !obj)\r\n            return;\r\n    }\r\n    var lastKey = keys.pop();\r\n    if (typeof obj[lastKey] === 'function') {\r\n        return function () {\r\n            return obj[lastKey].apply(obj, arguments);\r\n        };\r\n    }\r\n    else {\r\n        return obj[lastKey];\r\n    }\r\n};\r\nvar setProperty = function (obj, key, value) {\r\n    var keys = key.split('.');\r\n    var temp;\r\n    for (var i = 0; i < keys.length - 1; i++) {\r\n        temp = keys[i];\r\n        if (!obj[temp])\r\n            obj[temp] = {};\r\n        obj = obj[temp];\r\n    }\r\n    obj[keys.pop()] = value;\r\n};\r\nfunction propertiesProxy(target, sourceKey, key) {\r\n    sharedPropertyDefinition.get = function proxyGetter() {\r\n        return getProperty(this, sourceKey);\r\n    };\r\n    sharedPropertyDefinition.set = function proxySetter(val) {\r\n        setProperty(this, sourceKey, val);\r\n    };\r\n    Object.defineProperty(target, key, sharedPropertyDefinition);\r\n}\n\nvar EventEmitter = /** @class */ (function () {\r\n    function EventEmitter(names) {\r\n        this.events = {};\r\n        this.eventTypes = {};\r\n        this.registerType(names);\r\n    }\r\n    EventEmitter.prototype.on = function (type, fn, context) {\r\n        if (context === void 0) { context = this; }\r\n        this.hasType(type);\r\n        if (!this.events[type]) {\r\n            this.events[type] = [];\r\n        }\r\n        this.events[type].push([fn, context]);\r\n        return this;\r\n    };\r\n    EventEmitter.prototype.once = function (type, fn, context) {\r\n        var _this = this;\r\n        if (context === void 0) { context = this; }\r\n        this.hasType(type);\r\n        var magic = function () {\r\n            var args = [];\r\n            for (var _i = 0; _i < arguments.length; _i++) {\r\n                args[_i] = arguments[_i];\r\n            }\r\n            _this.off(type, magic);\r\n            var ret = fn.apply(context, args);\r\n            if (ret === true) {\r\n                return ret;\r\n            }\r\n        };\r\n        magic.fn = fn;\r\n        this.on(type, magic);\r\n        return this;\r\n    };\r\n    EventEmitter.prototype.off = function (type, fn) {\r\n        if (!type && !fn) {\r\n            this.events = {};\r\n            return this;\r\n        }\r\n        if (type) {\r\n            this.hasType(type);\r\n            if (!fn) {\r\n                this.events[type] = [];\r\n                return this;\r\n            }\r\n            var events = this.events[type];\r\n            if (!events) {\r\n                return this;\r\n            }\r\n            var count = events.length;\r\n            while (count--) {\r\n                if (events[count][0] === fn ||\r\n                    (events[count][0] && events[count][0].fn === fn)) {\r\n                    events.splice(count, 1);\r\n                }\r\n            }\r\n            return this;\r\n        }\r\n    };\r\n    EventEmitter.prototype.trigger = function (type) {\r\n        var args = [];\r\n        for (var _i = 1; _i < arguments.length; _i++) {\r\n            args[_i - 1] = arguments[_i];\r\n        }\r\n        this.hasType(type);\r\n        var events = this.events[type];\r\n        if (!events) {\r\n            return;\r\n        }\r\n        var len = events.length;\r\n        var eventsCopy = __spreadArrays(events);\r\n        var ret;\r\n        for (var i = 0; i < len; i++) {\r\n            var event_1 = eventsCopy[i];\r\n            var fn = event_1[0], context = event_1[1];\r\n            if (fn) {\r\n                ret = fn.apply(context, args);\r\n                if (ret === true) {\r\n                    return ret;\r\n                }\r\n            }\r\n        }\r\n    };\r\n    EventEmitter.prototype.registerType = function (names) {\r\n        var _this = this;\r\n        names.forEach(function (type) {\r\n            _this.eventTypes[type] = type;\r\n        });\r\n    };\r\n    EventEmitter.prototype.destroy = function () {\r\n        this.events = {};\r\n        this.eventTypes = {};\r\n    };\r\n    EventEmitter.prototype.hasType = function (type) {\r\n        var types = this.eventTypes;\r\n        var isType = types[type] === type;\r\n        if (!isType) {\r\n            warn(\"EventEmitter has used unknown event type: \\\"\" + type + \"\\\", should be oneof [\" +\r\n                (\"\" + Object.keys(types).map(function (_) { return JSON.stringify(_); })) +\r\n                \"]\");\r\n        }\r\n    };\r\n    return EventEmitter;\r\n}());\r\nvar EventRegister = /** @class */ (function () {\r\n    function EventRegister(wrapper, events) {\r\n        this.wrapper = wrapper;\r\n        this.events = events;\r\n        this.addDOMEvents();\r\n    }\r\n    EventRegister.prototype.destroy = function () {\r\n        this.removeDOMEvents();\r\n        this.events = [];\r\n    };\r\n    EventRegister.prototype.addDOMEvents = function () {\r\n        this.handleDOMEvents(addEvent);\r\n    };\r\n    EventRegister.prototype.removeDOMEvents = function () {\r\n        this.handleDOMEvents(removeEvent);\r\n    };\r\n    EventRegister.prototype.handleDOMEvents = function (eventOperation) {\r\n        var _this = this;\r\n        var wrapper = this.wrapper;\r\n        this.events.forEach(function (event) {\r\n            eventOperation(wrapper, event.name, _this, !!event.capture);\r\n        });\r\n    };\r\n    EventRegister.prototype.handleEvent = function (e) {\r\n        var eventType = e.type;\r\n        this.events.some(function (event) {\r\n            if (event.name === eventType) {\r\n                event.handler(e);\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n    };\r\n    return EventRegister;\r\n}());\n\nvar CustomOptions = /** @class */ (function () {\r\n    function CustomOptions() {\r\n    }\r\n    return CustomOptions;\r\n}());\r\nvar OptionsConstructor = /** @class */ (function (_super) {\r\n    __extends(OptionsConstructor, _super);\r\n    function OptionsConstructor() {\r\n        var _this = _super.call(this) || this;\r\n        _this.startX = 0;\r\n        _this.startY = 0;\r\n        _this.scrollX = false;\r\n        _this.scrollY = true;\r\n        _this.freeScroll = false;\r\n        _this.directionLockThreshold = 0;\r\n        _this.eventPassthrough = \"\" /* None */;\r\n        _this.click = false;\r\n        _this.dblclick = false;\r\n        _this.tap = '';\r\n        _this.bounce = {\r\n            top: true,\r\n            bottom: true,\r\n            left: true,\r\n            right: true,\r\n        };\r\n        _this.bounceTime = 800;\r\n        _this.momentum = true;\r\n        _this.momentumLimitTime = 300;\r\n        _this.momentumLimitDistance = 15;\r\n        _this.swipeTime = 2500;\r\n        _this.swipeBounceTime = 500;\r\n        _this.deceleration = 0.0015;\r\n        _this.flickLimitTime = 200;\r\n        _this.flickLimitDistance = 100;\r\n        _this.resizePolling = 60;\r\n        _this.probeType = 0 /* Default */;\r\n        _this.stopPropagation = false;\r\n        _this.preventDefault = true;\r\n        _this.preventDefaultException = {\r\n            tagName: /^(INPUT|TEXTAREA|BUTTON|SELECT|AUDIO)$/,\r\n        };\r\n        _this.tagException = {\r\n            tagName: /^TEXTAREA$/,\r\n        };\r\n        _this.HWCompositing = true;\r\n        _this.useTransition = true;\r\n        _this.bindToWrapper = false;\r\n        _this.bindToTarget = false;\r\n        _this.disableMouse = hasTouch;\r\n        _this.disableTouch = !hasTouch;\r\n        _this.autoBlur = true;\r\n        _this.autoEndDistance = 5;\r\n        _this.outOfBoundaryDampingFactor = 1 / 3;\r\n        _this.specifiedIndexAsContent = 0;\r\n        _this.quadrant = 1 /* First */;\r\n        return _this;\r\n    }\r\n    OptionsConstructor.prototype.merge = function (options) {\r\n        if (!options)\r\n            return this;\r\n        for (var key in options) {\r\n            if (key === 'bounce') {\r\n                this.bounce = this.resolveBounce(options[key]);\r\n                continue;\r\n            }\r\n            this[key] = options[key];\r\n        }\r\n        return this;\r\n    };\r\n    OptionsConstructor.prototype.process = function () {\r\n        this.translateZ =\r\n            this.HWCompositing && hasPerspective ? ' translateZ(1px)' : '';\r\n        this.useTransition = this.useTransition && hasTransition;\r\n        this.preventDefault = !this.eventPassthrough && this.preventDefault;\r\n        // If you want eventPassthrough I have to lock one of the axes\r\n        this.scrollX =\r\n            this.eventPassthrough === \"horizontal\" /* Horizontal */\r\n                ? false\r\n                : this.scrollX;\r\n        this.scrollY =\r\n            this.eventPassthrough === \"vertical\" /* Vertical */ ? false : this.scrollY;\r\n        // With eventPassthrough we also need lockDirection mechanism\r\n        this.freeScroll = this.freeScroll && !this.eventPassthrough;\r\n        // force true when freeScroll is true\r\n        this.scrollX = this.freeScroll ? true : this.scrollX;\r\n        this.scrollY = this.freeScroll ? true : this.scrollY;\r\n        this.directionLockThreshold = this.eventPassthrough\r\n            ? 0\r\n            : this.directionLockThreshold;\r\n        return this;\r\n    };\r\n    OptionsConstructor.prototype.resolveBounce = function (bounceOptions) {\r\n        var DEFAULT_BOUNCE = {\r\n            top: true,\r\n            right: true,\r\n            bottom: true,\r\n            left: true,\r\n        };\r\n        var NEGATED_BOUNCE = {\r\n            top: false,\r\n            right: false,\r\n            bottom: false,\r\n            left: false,\r\n        };\r\n        var ret;\r\n        if (typeof bounceOptions === 'object') {\r\n            ret = extend(DEFAULT_BOUNCE, bounceOptions);\r\n        }\r\n        else {\r\n            ret = bounceOptions ? DEFAULT_BOUNCE : NEGATED_BOUNCE;\r\n        }\r\n        return ret;\r\n    };\r\n    return OptionsConstructor;\r\n}(CustomOptions));\n\nvar ActionsHandler = /** @class */ (function () {\r\n    function ActionsHandler(wrapper, options) {\r\n        this.wrapper = wrapper;\r\n        this.options = options;\r\n        this.hooks = new EventEmitter([\r\n            'beforeStart',\r\n            'start',\r\n            'move',\r\n            'end',\r\n            'click',\r\n        ]);\r\n        this.handleDOMEvents();\r\n    }\r\n    ActionsHandler.prototype.handleDOMEvents = function () {\r\n        var _a = this.options, bindToWrapper = _a.bindToWrapper, disableMouse = _a.disableMouse, disableTouch = _a.disableTouch, click = _a.click;\r\n        var wrapper = this.wrapper;\r\n        var target = bindToWrapper ? wrapper : window;\r\n        var wrapperEvents = [];\r\n        var targetEvents = [];\r\n        var shouldRegisterTouch = !disableTouch;\r\n        var shouldRegisterMouse = !disableMouse;\r\n        if (click) {\r\n            wrapperEvents.push({\r\n                name: 'click',\r\n                handler: this.click.bind(this),\r\n                capture: true,\r\n            });\r\n        }\r\n        if (shouldRegisterTouch) {\r\n            wrapperEvents.push({\r\n                name: 'touchstart',\r\n                handler: this.start.bind(this),\r\n            });\r\n            targetEvents.push({\r\n                name: 'touchmove',\r\n                handler: this.move.bind(this),\r\n            }, {\r\n                name: 'touchend',\r\n                handler: this.end.bind(this),\r\n            }, {\r\n                name: 'touchcancel',\r\n                handler: this.end.bind(this),\r\n            });\r\n        }\r\n        if (shouldRegisterMouse) {\r\n            wrapperEvents.push({\r\n                name: 'mousedown',\r\n                handler: this.start.bind(this),\r\n            });\r\n            targetEvents.push({\r\n                name: 'mousemove',\r\n                handler: this.move.bind(this),\r\n            }, {\r\n                name: 'mouseup',\r\n                handler: this.end.bind(this),\r\n            });\r\n        }\r\n        this.wrapperEventRegister = new EventRegister(wrapper, wrapperEvents);\r\n        this.targetEventRegister = new EventRegister(target, targetEvents);\r\n    };\r\n    ActionsHandler.prototype.beforeHandler = function (e, type) {\r\n        var _a = this.options, preventDefault = _a.preventDefault, stopPropagation = _a.stopPropagation, preventDefaultException = _a.preventDefaultException;\r\n        var preventDefaultConditions = {\r\n            start: function () {\r\n                return (preventDefault &&\r\n                    !preventDefaultExceptionFn(e.target, preventDefaultException));\r\n            },\r\n            end: function () {\r\n                return (preventDefault &&\r\n                    !preventDefaultExceptionFn(e.target, preventDefaultException));\r\n            },\r\n            move: function () {\r\n                return preventDefault;\r\n            },\r\n        };\r\n        if (preventDefaultConditions[type]()) {\r\n            e.preventDefault();\r\n        }\r\n        if (stopPropagation) {\r\n            e.stopPropagation();\r\n        }\r\n    };\r\n    ActionsHandler.prototype.setInitiated = function (type) {\r\n        if (type === void 0) { type = 0; }\r\n        this.initiated = type;\r\n    };\r\n    ActionsHandler.prototype.start = function (e) {\r\n        var _eventType = eventTypeMap[e.type];\r\n        if (this.initiated && this.initiated !== _eventType) {\r\n            return;\r\n        }\r\n        this.setInitiated(_eventType);\r\n        // if textarea or other html tags in options.tagException is manipulated\r\n        // do not make bs scroll\r\n        if (tagExceptionFn(e.target, this.options.tagException)) {\r\n            this.setInitiated();\r\n            return;\r\n        }\r\n        // only allow mouse left button\r\n        if (_eventType === 2 /* Mouse */ && e.button !== 0 /* Left */)\r\n            return;\r\n        if (this.hooks.trigger(this.hooks.eventTypes.beforeStart, e)) {\r\n            return;\r\n        }\r\n        this.beforeHandler(e, 'start');\r\n        var point = (e.touches ? e.touches[0] : e);\r\n        this.pointX = point.pageX;\r\n        this.pointY = point.pageY;\r\n        this.hooks.trigger(this.hooks.eventTypes.start, e);\r\n    };\r\n    ActionsHandler.prototype.move = function (e) {\r\n        if (eventTypeMap[e.type] !== this.initiated) {\r\n            return;\r\n        }\r\n        this.beforeHandler(e, 'move');\r\n        var point = (e.touches ? e.touches[0] : e);\r\n        var deltaX = point.pageX - this.pointX;\r\n        var deltaY = point.pageY - this.pointY;\r\n        this.pointX = point.pageX;\r\n        this.pointY = point.pageY;\r\n        if (this.hooks.trigger(this.hooks.eventTypes.move, {\r\n            deltaX: deltaX,\r\n            deltaY: deltaY,\r\n            e: e,\r\n        })) {\r\n            return;\r\n        }\r\n        // auto end when out of viewport\r\n        var scrollLeft = document.documentElement.scrollLeft ||\r\n            window.pageXOffset ||\r\n            document.body.scrollLeft;\r\n        var scrollTop = document.documentElement.scrollTop ||\r\n            window.pageYOffset ||\r\n            document.body.scrollTop;\r\n        var pX = this.pointX - scrollLeft;\r\n        var pY = this.pointY - scrollTop;\r\n        var autoEndDistance = this.options.autoEndDistance;\r\n        if (pX > document.documentElement.clientWidth - autoEndDistance ||\r\n            pY > document.documentElement.clientHeight - autoEndDistance ||\r\n            pX < autoEndDistance ||\r\n            pY < autoEndDistance) {\r\n            this.end(e);\r\n        }\r\n    };\r\n    ActionsHandler.prototype.end = function (e) {\r\n        if (eventTypeMap[e.type] !== this.initiated) {\r\n            return;\r\n        }\r\n        this.setInitiated();\r\n        this.beforeHandler(e, 'end');\r\n        this.hooks.trigger(this.hooks.eventTypes.end, e);\r\n    };\r\n    ActionsHandler.prototype.click = function (e) {\r\n        this.hooks.trigger(this.hooks.eventTypes.click, e);\r\n    };\r\n    ActionsHandler.prototype.setContent = function (content) {\r\n        if (content !== this.wrapper) {\r\n            this.wrapper = content;\r\n            this.rebindDOMEvents();\r\n        }\r\n    };\r\n    ActionsHandler.prototype.rebindDOMEvents = function () {\r\n        this.wrapperEventRegister.destroy();\r\n        this.targetEventRegister.destroy();\r\n        this.handleDOMEvents();\r\n    };\r\n    ActionsHandler.prototype.destroy = function () {\r\n        this.wrapperEventRegister.destroy();\r\n        this.targetEventRegister.destroy();\r\n        this.hooks.destroy();\r\n    };\r\n    return ActionsHandler;\r\n}());\n\nvar translaterMetaData = {\r\n    x: ['translateX', 'px'],\r\n    y: ['translateY', 'px'],\r\n};\r\nvar Translater = /** @class */ (function () {\r\n    function Translater(content) {\r\n        this.setContent(content);\r\n        this.hooks = new EventEmitter(['beforeTranslate', 'translate']);\r\n    }\r\n    Translater.prototype.getComputedPosition = function () {\r\n        var cssStyle = window.getComputedStyle(this.content, null);\r\n        var matrix = cssStyle[style.transform].split(')')[0].split(', ');\r\n        var x = +(matrix[12] || matrix[4]) || 0;\r\n        var y = +(matrix[13] || matrix[5]) || 0;\r\n        return {\r\n            x: x,\r\n            y: y,\r\n        };\r\n    };\r\n    Translater.prototype.translate = function (point) {\r\n        var transformStyle = [];\r\n        Object.keys(point).forEach(function (key) {\r\n            if (!translaterMetaData[key]) {\r\n                return;\r\n            }\r\n            var transformFnName = translaterMetaData[key][0];\r\n            if (transformFnName) {\r\n                var transformFnArgUnit = translaterMetaData[key][1];\r\n                var transformFnArg = point[key];\r\n                transformStyle.push(transformFnName + \"(\" + transformFnArg + transformFnArgUnit + \")\");\r\n            }\r\n        });\r\n        this.hooks.trigger(this.hooks.eventTypes.beforeTranslate, transformStyle, point);\r\n        this.style[style.transform] = transformStyle.join(' ');\r\n        this.hooks.trigger(this.hooks.eventTypes.translate, point);\r\n    };\r\n    Translater.prototype.setContent = function (content) {\r\n        if (this.content !== content) {\r\n            this.content = content;\r\n            this.style = content.style;\r\n        }\r\n    };\r\n    Translater.prototype.destroy = function () {\r\n        this.hooks.destroy();\r\n    };\r\n    return Translater;\r\n}());\n\nvar Base = /** @class */ (function () {\r\n    function Base(content, translater, options) {\r\n        this.translater = translater;\r\n        this.options = options;\r\n        this.timer = 0;\r\n        this.hooks = new EventEmitter([\r\n            'move',\r\n            'end',\r\n            'beforeForceStop',\r\n            'forceStop',\r\n            'callStop',\r\n            'time',\r\n            'timeFunction',\r\n        ]);\r\n        this.setContent(content);\r\n    }\r\n    Base.prototype.translate = function (endPoint) {\r\n        this.translater.translate(endPoint);\r\n    };\r\n    Base.prototype.setPending = function (pending) {\r\n        this.pending = pending;\r\n    };\r\n    Base.prototype.setForceStopped = function (forceStopped) {\r\n        this.forceStopped = forceStopped;\r\n    };\r\n    Base.prototype.setCallStop = function (called) {\r\n        this.callStopWhenPending = called;\r\n    };\r\n    Base.prototype.setContent = function (content) {\r\n        if (this.content !== content) {\r\n            this.content = content;\r\n            this.style = content.style;\r\n            this.stop();\r\n        }\r\n    };\r\n    Base.prototype.clearTimer = function () {\r\n        if (this.timer) {\r\n            cancelAnimationFrame(this.timer);\r\n            this.timer = 0;\r\n        }\r\n    };\r\n    Base.prototype.destroy = function () {\r\n        this.hooks.destroy();\r\n        cancelAnimationFrame(this.timer);\r\n    };\r\n    return Base;\r\n}());\n\n// iOS 13.6 - 14.x, window.getComputedStyle sometimes will get wrong transform value\r\n// when bs use transition mode\r\n// eg: translateY -100px -> -200px, when the last frame which is about to scroll to -200px\r\n// window.getComputedStyle(this.content) will calculate transformY to be -100px(startPoint)\r\n// it is weird\r\n// so we should validate position caculated by 'window.getComputedStyle'\r\nvar isValidPostion = function (startPoint, endPoint, currentPos, prePos) {\r\n    var computeDirection = function (endValue, startValue) {\r\n        var delta = endValue - startValue;\r\n        var direction = delta > 0\r\n            ? -1 /* Negative */\r\n            : delta < 0\r\n                ? 1 /* Positive */\r\n                : 0 /* Default */;\r\n        return direction;\r\n    };\r\n    var directionX = computeDirection(endPoint.x, startPoint.x);\r\n    var directionY = computeDirection(endPoint.y, startPoint.y);\r\n    var deltaX = currentPos.x - prePos.x;\r\n    var deltaY = currentPos.y - prePos.y;\r\n    return directionX * deltaX <= 0 && directionY * deltaY <= 0;\r\n};\n\nvar Transition = /** @class */ (function (_super) {\r\n    __extends(Transition, _super);\r\n    function Transition() {\r\n        return _super !== null && _super.apply(this, arguments) || this;\r\n    }\r\n    Transition.prototype.startProbe = function (startPoint, endPoint) {\r\n        var _this = this;\r\n        var prePos = startPoint;\r\n        var probe = function () {\r\n            var pos = _this.translater.getComputedPosition();\r\n            if (isValidPostion(startPoint, endPoint, pos, prePos)) {\r\n                _this.hooks.trigger(_this.hooks.eventTypes.move, pos);\r\n            }\r\n            // call bs.stop() should not dispatch end hook again.\r\n            // forceStop hook will do this.\r\n            /* istanbul ignore if  */\r\n            if (!_this.pending) {\r\n                if (_this.callStopWhenPending) {\r\n                    _this.callStopWhenPending = false;\r\n                }\r\n                else {\r\n                    // transition ends should dispatch end hook.\r\n                    _this.hooks.trigger(_this.hooks.eventTypes.end, pos);\r\n                }\r\n            }\r\n            prePos = pos;\r\n            if (_this.pending) {\r\n                _this.timer = requestAnimationFrame(probe);\r\n            }\r\n        };\r\n        // when manually call bs.stop(), then bs.scrollTo()\r\n        // we should reset callStopWhenPending to dispatch end hook\r\n        if (this.callStopWhenPending) {\r\n            this.setCallStop(false);\r\n        }\r\n        cancelAnimationFrame(this.timer);\r\n        probe();\r\n    };\r\n    Transition.prototype.transitionTime = function (time) {\r\n        if (time === void 0) { time = 0; }\r\n        this.style[style.transitionDuration] = time + 'ms';\r\n        this.hooks.trigger(this.hooks.eventTypes.time, time);\r\n    };\r\n    Transition.prototype.transitionTimingFunction = function (easing) {\r\n        this.style[style.transitionTimingFunction] = easing;\r\n        this.hooks.trigger(this.hooks.eventTypes.timeFunction, easing);\r\n    };\r\n    Transition.prototype.transitionProperty = function () {\r\n        this.style[style.transitionProperty] = style.transform;\r\n    };\r\n    Transition.prototype.move = function (startPoint, endPoint, time, easingFn) {\r\n        this.setPending(time > 0);\r\n        this.transitionTimingFunction(easingFn);\r\n        this.transitionProperty();\r\n        this.transitionTime(time);\r\n        this.translate(endPoint);\r\n        var isRealtimeProbeType = this.options.probeType === 3 /* Realtime */;\r\n        if (time && isRealtimeProbeType) {\r\n            this.startProbe(startPoint, endPoint);\r\n        }\r\n        // if we change content's transformY in a tick\r\n        // such as: 0 -> 50px -> 0\r\n        // transitionend will not be triggered\r\n        // so we forceupdate by reflow\r\n        if (!time) {\r\n            this._reflow = this.content.offsetHeight;\r\n            if (isRealtimeProbeType) {\r\n                this.hooks.trigger(this.hooks.eventTypes.move, endPoint);\r\n            }\r\n            this.hooks.trigger(this.hooks.eventTypes.end, endPoint);\r\n        }\r\n    };\r\n    Transition.prototype.doStop = function () {\r\n        var pending = this.pending;\r\n        this.setForceStopped(false);\r\n        this.setCallStop(false);\r\n        // still in transition\r\n        if (pending) {\r\n            this.setPending(false);\r\n            cancelAnimationFrame(this.timer);\r\n            var _a = this.translater.getComputedPosition(), x = _a.x, y = _a.y;\r\n            this.transitionTime();\r\n            this.translate({ x: x, y: y });\r\n            this.setForceStopped(true);\r\n            this.setCallStop(true);\r\n            this.hooks.trigger(this.hooks.eventTypes.forceStop, { x: x, y: y });\r\n        }\r\n        return pending;\r\n    };\r\n    Transition.prototype.stop = function () {\r\n        var stopFromTransition = this.doStop();\r\n        if (stopFromTransition) {\r\n            this.hooks.trigger(this.hooks.eventTypes.callStop);\r\n        }\r\n    };\r\n    return Transition;\r\n}(Base));\n\nvar Animation = /** @class */ (function (_super) {\r\n    __extends(Animation, _super);\r\n    function Animation() {\r\n        return _super !== null && _super.apply(this, arguments) || this;\r\n    }\r\n    Animation.prototype.move = function (startPoint, endPoint, time, easingFn) {\r\n        // time is 0\r\n        if (!time) {\r\n            this.translate(endPoint);\r\n            if (this.options.probeType === 3 /* Realtime */) {\r\n                this.hooks.trigger(this.hooks.eventTypes.move, endPoint);\r\n            }\r\n            this.hooks.trigger(this.hooks.eventTypes.end, endPoint);\r\n            return;\r\n        }\r\n        this.animate(startPoint, endPoint, time, easingFn);\r\n    };\r\n    Animation.prototype.animate = function (startPoint, endPoint, duration, easingFn) {\r\n        var _this = this;\r\n        var startTime = getNow();\r\n        var destTime = startTime + duration;\r\n        var isRealtimeProbeType = this.options.probeType === 3 /* Realtime */;\r\n        var step = function () {\r\n            var now = getNow();\r\n            // js animation end\r\n            if (now >= destTime) {\r\n                _this.translate(endPoint);\r\n                if (isRealtimeProbeType) {\r\n                    _this.hooks.trigger(_this.hooks.eventTypes.move, endPoint);\r\n                }\r\n                _this.hooks.trigger(_this.hooks.eventTypes.end, endPoint);\r\n                return;\r\n            }\r\n            now = (now - startTime) / duration;\r\n            var easing = easingFn(now);\r\n            var newPoint = {};\r\n            Object.keys(endPoint).forEach(function (key) {\r\n                var startValue = startPoint[key];\r\n                var endValue = endPoint[key];\r\n                newPoint[key] = (endValue - startValue) * easing + startValue;\r\n            });\r\n            _this.translate(newPoint);\r\n            if (isRealtimeProbeType) {\r\n                _this.hooks.trigger(_this.hooks.eventTypes.move, newPoint);\r\n            }\r\n            if (_this.pending) {\r\n                _this.timer = requestAnimationFrame(step);\r\n            }\r\n            // call bs.stop() should not dispatch end hook again.\r\n            // forceStop hook will do this.\r\n            /* istanbul ignore if  */\r\n            if (!_this.pending) {\r\n                if (_this.callStopWhenPending) {\r\n                    _this.callStopWhenPending = false;\r\n                }\r\n                else {\r\n                    // raf ends should dispatch end hook.\r\n                    _this.hooks.trigger(_this.hooks.eventTypes.end, endPoint);\r\n                }\r\n            }\r\n        };\r\n        this.setPending(true);\r\n        // when manually call bs.stop(), then bs.scrollTo()\r\n        // we should reset callStopWhenPending to dispatch end hook\r\n        if (this.callStopWhenPending) {\r\n            this.setCallStop(false);\r\n        }\r\n        cancelAnimationFrame(this.timer);\r\n        step();\r\n    };\r\n    Animation.prototype.doStop = function () {\r\n        var pending = this.pending;\r\n        this.setForceStopped(false);\r\n        this.setCallStop(false);\r\n        // still in requestFrameAnimation\r\n        if (pending) {\r\n            this.setPending(false);\r\n            cancelAnimationFrame(this.timer);\r\n            var pos = this.translater.getComputedPosition();\r\n            this.setForceStopped(true);\r\n            this.setCallStop(true);\r\n            this.hooks.trigger(this.hooks.eventTypes.forceStop, pos);\r\n        }\r\n        return pending;\r\n    };\r\n    Animation.prototype.stop = function () {\r\n        var stopFromAnimation = this.doStop();\r\n        if (stopFromAnimation) {\r\n            this.hooks.trigger(this.hooks.eventTypes.callStop);\r\n        }\r\n    };\r\n    return Animation;\r\n}(Base));\n\nfunction createAnimater(element, translater, options) {\r\n    var useTransition = options.useTransition;\r\n    var animaterOptions = {};\r\n    Object.defineProperty(animaterOptions, 'probeType', {\r\n        enumerable: true,\r\n        configurable: false,\r\n        get: function () {\r\n            return options.probeType;\r\n        },\r\n    });\r\n    if (useTransition) {\r\n        return new Transition(element, translater, animaterOptions);\r\n    }\r\n    else {\r\n        return new Animation(element, translater, animaterOptions);\r\n    }\r\n}\n\nvar Behavior = /** @class */ (function () {\r\n    function Behavior(wrapper, content, options) {\r\n        this.wrapper = wrapper;\r\n        this.options = options;\r\n        this.hooks = new EventEmitter([\r\n            'beforeComputeBoundary',\r\n            'computeBoundary',\r\n            'momentum',\r\n            'end',\r\n            'ignoreHasScroll'\r\n        ]);\r\n        this.refresh(content);\r\n    }\r\n    Behavior.prototype.start = function () {\r\n        this.dist = 0;\r\n        this.setMovingDirection(0 /* Default */);\r\n        this.setDirection(0 /* Default */);\r\n    };\r\n    Behavior.prototype.move = function (delta) {\r\n        delta = this.hasScroll ? delta : 0;\r\n        this.setMovingDirection(delta);\r\n        return this.performDampingAlgorithm(delta, this.options.outOfBoundaryDampingFactor);\r\n    };\r\n    Behavior.prototype.setMovingDirection = function (delta) {\r\n        this.movingDirection =\r\n            delta > 0\r\n                ? -1 /* Negative */\r\n                : delta < 0\r\n                    ? 1 /* Positive */\r\n                    : 0 /* Default */;\r\n    };\r\n    Behavior.prototype.setDirection = function (delta) {\r\n        this.direction =\r\n            delta > 0\r\n                ? -1 /* Negative */\r\n                : delta < 0\r\n                    ? 1 /* Positive */\r\n                    : 0 /* Default */;\r\n    };\r\n    Behavior.prototype.performDampingAlgorithm = function (delta, dampingFactor) {\r\n        var newPos = this.currentPos + delta;\r\n        // Slow down or stop if outside of the boundaries\r\n        if (newPos > this.minScrollPos || newPos < this.maxScrollPos) {\r\n            if ((newPos > this.minScrollPos && this.options.bounces[0]) ||\r\n                (newPos < this.maxScrollPos && this.options.bounces[1])) {\r\n                newPos = this.currentPos + delta * dampingFactor;\r\n            }\r\n            else {\r\n                newPos =\r\n                    newPos > this.minScrollPos ? this.minScrollPos : this.maxScrollPos;\r\n            }\r\n        }\r\n        return newPos;\r\n    };\r\n    Behavior.prototype.end = function (duration) {\r\n        var momentumInfo = {\r\n            duration: 0\r\n        };\r\n        var absDist = Math.abs(this.currentPos - this.startPos);\r\n        // start momentum animation if needed\r\n        if (this.options.momentum &&\r\n            duration < this.options.momentumLimitTime &&\r\n            absDist > this.options.momentumLimitDistance) {\r\n            var wrapperSize = (this.direction === -1 /* Negative */ && this.options.bounces[0]) ||\r\n                (this.direction === 1 /* Positive */ && this.options.bounces[1])\r\n                ? this.wrapperSize\r\n                : 0;\r\n            momentumInfo = this.hasScroll\r\n                ? this.momentum(this.currentPos, this.startPos, duration, this.maxScrollPos, this.minScrollPos, wrapperSize, this.options)\r\n                : { destination: this.currentPos, duration: 0 };\r\n        }\r\n        else {\r\n            this.hooks.trigger(this.hooks.eventTypes.end, momentumInfo);\r\n        }\r\n        return momentumInfo;\r\n    };\r\n    Behavior.prototype.momentum = function (current, start, time, lowerMargin, upperMargin, wrapperSize, options) {\r\n        if (options === void 0) { options = this.options; }\r\n        var distance = current - start;\r\n        var speed = Math.abs(distance) / time;\r\n        var deceleration = options.deceleration, swipeBounceTime = options.swipeBounceTime, swipeTime = options.swipeTime;\r\n        var duration = Math.min(swipeTime, (speed * 2) / deceleration);\r\n        var momentumData = {\r\n            destination: current + ((speed * speed) / deceleration) * (distance < 0 ? -1 : 1),\r\n            duration: duration,\r\n            rate: 15\r\n        };\r\n        this.hooks.trigger(this.hooks.eventTypes.momentum, momentumData, distance);\r\n        if (momentumData.destination < lowerMargin) {\r\n            momentumData.destination = wrapperSize\r\n                ? Math.max(lowerMargin - wrapperSize / 4, lowerMargin - (wrapperSize / momentumData.rate) * speed)\r\n                : lowerMargin;\r\n            momentumData.duration = swipeBounceTime;\r\n        }\r\n        else if (momentumData.destination > upperMargin) {\r\n            momentumData.destination = wrapperSize\r\n                ? Math.min(upperMargin + wrapperSize / 4, upperMargin + (wrapperSize / momentumData.rate) * speed)\r\n                : upperMargin;\r\n            momentumData.duration = swipeBounceTime;\r\n        }\r\n        momentumData.destination = Math.round(momentumData.destination);\r\n        return momentumData;\r\n    };\r\n    Behavior.prototype.updateDirection = function () {\r\n        var absDist = this.currentPos - this.absStartPos;\r\n        this.setDirection(absDist);\r\n    };\r\n    Behavior.prototype.refresh = function (content) {\r\n        var _a = this.options.rect, size = _a.size, position = _a.position;\r\n        var isWrapperStatic = window.getComputedStyle(this.wrapper, null).position === 'static';\r\n        // Force reflow\r\n        var wrapperRect = getRect(this.wrapper);\r\n        // use client is more fair than offset\r\n        this.wrapperSize = this.wrapper[size === 'width' ? 'clientWidth' : 'clientHeight'];\r\n        this.setContent(content);\r\n        var contentRect = getRect(this.content);\r\n        this.contentSize = contentRect[size];\r\n        this.relativeOffset = contentRect[position];\r\n        /* istanbul ignore if  */\r\n        if (isWrapperStatic) {\r\n            this.relativeOffset -= wrapperRect[position];\r\n        }\r\n        this.computeBoundary();\r\n        this.setDirection(0 /* Default */);\r\n    };\r\n    Behavior.prototype.setContent = function (content) {\r\n        if (content !== this.content) {\r\n            this.content = content;\r\n            this.resetState();\r\n        }\r\n    };\r\n    Behavior.prototype.resetState = function () {\r\n        this.currentPos = 0;\r\n        this.startPos = 0;\r\n        this.dist = 0;\r\n        this.setDirection(0 /* Default */);\r\n        this.setMovingDirection(0 /* Default */);\r\n        this.resetStartPos();\r\n    };\r\n    Behavior.prototype.computeBoundary = function () {\r\n        this.hooks.trigger(this.hooks.eventTypes.beforeComputeBoundary);\r\n        var boundary = {\r\n            minScrollPos: 0,\r\n            maxScrollPos: this.wrapperSize - this.contentSize\r\n        };\r\n        if (boundary.maxScrollPos < 0) {\r\n            boundary.maxScrollPos -= this.relativeOffset;\r\n            if (this.options.specifiedIndexAsContent === 0) {\r\n                boundary.minScrollPos = -this.relativeOffset;\r\n            }\r\n        }\r\n        this.hooks.trigger(this.hooks.eventTypes.computeBoundary, boundary);\r\n        this.minScrollPos = boundary.minScrollPos;\r\n        this.maxScrollPos = boundary.maxScrollPos;\r\n        this.hasScroll =\r\n            this.options.scrollable && this.maxScrollPos < this.minScrollPos;\r\n        if (!this.hasScroll && this.minScrollPos < this.maxScrollPos) {\r\n            this.maxScrollPos = this.minScrollPos;\r\n            this.contentSize = this.wrapperSize;\r\n        }\r\n    };\r\n    Behavior.prototype.updatePosition = function (pos) {\r\n        this.currentPos = pos;\r\n    };\r\n    Behavior.prototype.getCurrentPos = function () {\r\n        return this.currentPos;\r\n    };\r\n    Behavior.prototype.checkInBoundary = function () {\r\n        var position = this.adjustPosition(this.currentPos);\r\n        var inBoundary = position === this.getCurrentPos();\r\n        return {\r\n            position: position,\r\n            inBoundary: inBoundary\r\n        };\r\n    };\r\n    // adjust position when out of boundary\r\n    Behavior.prototype.adjustPosition = function (pos) {\r\n        if (!this.hasScroll &&\r\n            !this.hooks.trigger(this.hooks.eventTypes.ignoreHasScroll)) {\r\n            pos = this.minScrollPos;\r\n        }\r\n        else if (pos > this.minScrollPos) {\r\n            pos = this.minScrollPos;\r\n        }\r\n        else if (pos < this.maxScrollPos) {\r\n            pos = this.maxScrollPos;\r\n        }\r\n        return pos;\r\n    };\r\n    Behavior.prototype.updateStartPos = function () {\r\n        this.startPos = this.currentPos;\r\n    };\r\n    Behavior.prototype.updateAbsStartPos = function () {\r\n        this.absStartPos = this.currentPos;\r\n    };\r\n    Behavior.prototype.resetStartPos = function () {\r\n        this.updateStartPos();\r\n        this.updateAbsStartPos();\r\n    };\r\n    Behavior.prototype.getAbsDist = function (delta) {\r\n        this.dist += delta;\r\n        return Math.abs(this.dist);\r\n    };\r\n    Behavior.prototype.destroy = function () {\r\n        this.hooks.destroy();\r\n    };\r\n    return Behavior;\r\n}());\n\nvar _a, _b, _c, _d;\r\nvar PassthroughHandlers = (_a = {},\r\n    _a[\"yes\" /* Yes */] = function (e) {\r\n        return true;\r\n    },\r\n    _a[\"no\" /* No */] = function (e) {\r\n        maybePrevent(e);\r\n        return false;\r\n    },\r\n    _a);\r\nvar DirectionMap = (_b = {},\r\n    _b[\"horizontal\" /* Horizontal */] = (_c = {},\r\n        _c[\"yes\" /* Yes */] = \"horizontal\" /* Horizontal */,\r\n        _c[\"no\" /* No */] = \"vertical\" /* Vertical */,\r\n        _c),\r\n    _b[\"vertical\" /* Vertical */] = (_d = {},\r\n        _d[\"yes\" /* Yes */] = \"vertical\" /* Vertical */,\r\n        _d[\"no\" /* No */] = \"horizontal\" /* Horizontal */,\r\n        _d),\r\n    _b);\r\nvar DirectionLockAction = /** @class */ (function () {\r\n    function DirectionLockAction(directionLockThreshold, freeScroll, eventPassthrough) {\r\n        this.directionLockThreshold = directionLockThreshold;\r\n        this.freeScroll = freeScroll;\r\n        this.eventPassthrough = eventPassthrough;\r\n        this.reset();\r\n    }\r\n    DirectionLockAction.prototype.reset = function () {\r\n        this.directionLocked = \"\" /* Default */;\r\n    };\r\n    DirectionLockAction.prototype.checkMovingDirection = function (absDistX, absDistY, e) {\r\n        this.computeDirectionLock(absDistX, absDistY);\r\n        return this.handleEventPassthrough(e);\r\n    };\r\n    DirectionLockAction.prototype.adjustDelta = function (deltaX, deltaY) {\r\n        if (this.directionLocked === \"horizontal\" /* Horizontal */) {\r\n            deltaY = 0;\r\n        }\r\n        else if (this.directionLocked === \"vertical\" /* Vertical */) {\r\n            deltaX = 0;\r\n        }\r\n        return {\r\n            deltaX: deltaX,\r\n            deltaY: deltaY,\r\n        };\r\n    };\r\n    DirectionLockAction.prototype.computeDirectionLock = function (absDistX, absDistY) {\r\n        // If you are scrolling in one direction, lock it\r\n        if (this.directionLocked === \"\" /* Default */ && !this.freeScroll) {\r\n            if (absDistX > absDistY + this.directionLockThreshold) {\r\n                this.directionLocked = \"horizontal\" /* Horizontal */; // lock horizontally\r\n            }\r\n            else if (absDistY >= absDistX + this.directionLockThreshold) {\r\n                this.directionLocked = \"vertical\" /* Vertical */; // lock vertically\r\n            }\r\n            else {\r\n                this.directionLocked = \"none\" /* None */; // no lock\r\n            }\r\n        }\r\n    };\r\n    DirectionLockAction.prototype.handleEventPassthrough = function (e) {\r\n        var handleMap = DirectionMap[this.directionLocked];\r\n        if (handleMap) {\r\n            if (this.eventPassthrough === handleMap[\"yes\" /* Yes */]) {\r\n                return PassthroughHandlers[\"yes\" /* Yes */](e);\r\n            }\r\n            else if (this.eventPassthrough === handleMap[\"no\" /* No */]) {\r\n                return PassthroughHandlers[\"no\" /* No */](e);\r\n            }\r\n        }\r\n        return false;\r\n    };\r\n    return DirectionLockAction;\r\n}());\n\nvar applyQuadrantTransformation = function (deltaX, deltaY, quadrant) {\r\n    if (quadrant === 2 /* Second */) {\r\n        return [deltaY, -deltaX];\r\n    }\r\n    else if (quadrant === 3 /* Third */) {\r\n        return [-deltaX, -deltaY];\r\n    }\r\n    else if (quadrant === 4 /* Forth */) {\r\n        return [-deltaY, deltaX];\r\n    }\r\n    else {\r\n        return [deltaX, deltaY];\r\n    }\r\n};\r\nvar ScrollerActions = /** @class */ (function () {\r\n    function ScrollerActions(scrollBehaviorX, scrollBehaviorY, actionsHandler, animater, options) {\r\n        this.hooks = new EventEmitter([\r\n            'start',\r\n            'beforeMove',\r\n            'scrollStart',\r\n            'scroll',\r\n            'beforeEnd',\r\n            'end',\r\n            'scrollEnd',\r\n            'contentNotMoved',\r\n            'detectMovingDirection',\r\n            'coordinateTransformation',\r\n        ]);\r\n        this.scrollBehaviorX = scrollBehaviorX;\r\n        this.scrollBehaviorY = scrollBehaviorY;\r\n        this.actionsHandler = actionsHandler;\r\n        this.animater = animater;\r\n        this.options = options;\r\n        this.directionLockAction = new DirectionLockAction(options.directionLockThreshold, options.freeScroll, options.eventPassthrough);\r\n        this.enabled = true;\r\n        this.bindActionsHandler();\r\n    }\r\n    ScrollerActions.prototype.bindActionsHandler = function () {\r\n        var _this = this;\r\n        // [mouse|touch]start event\r\n        this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.start, function (e) {\r\n            if (!_this.enabled)\r\n                return true;\r\n            return _this.handleStart(e);\r\n        });\r\n        // [mouse|touch]move event\r\n        this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.move, function (_a) {\r\n            var deltaX = _a.deltaX, deltaY = _a.deltaY, e = _a.e;\r\n            if (!_this.enabled)\r\n                return true;\r\n            var _b = applyQuadrantTransformation(deltaX, deltaY, _this.options.quadrant), transformateDeltaX = _b[0], transformateDeltaY = _b[1];\r\n            var transformateDeltaData = {\r\n                deltaX: transformateDeltaX,\r\n                deltaY: transformateDeltaY,\r\n            };\r\n            _this.hooks.trigger(_this.hooks.eventTypes.coordinateTransformation, transformateDeltaData);\r\n            return _this.handleMove(transformateDeltaData.deltaX, transformateDeltaData.deltaY, e);\r\n        });\r\n        // [mouse|touch]end event\r\n        this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.end, function (e) {\r\n            if (!_this.enabled)\r\n                return true;\r\n            return _this.handleEnd(e);\r\n        });\r\n        // click\r\n        this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.click, function (e) {\r\n            // handle native click event\r\n            if (_this.enabled && !e._constructed) {\r\n                _this.handleClick(e);\r\n            }\r\n        });\r\n    };\r\n    ScrollerActions.prototype.handleStart = function (e) {\r\n        var timestamp = getNow();\r\n        this.fingerMoved = false;\r\n        this.contentMoved = false;\r\n        this.startTime = timestamp;\r\n        this.directionLockAction.reset();\r\n        this.scrollBehaviorX.start();\r\n        this.scrollBehaviorY.start();\r\n        // force stopping last transition or animation\r\n        this.animater.doStop();\r\n        this.scrollBehaviorX.resetStartPos();\r\n        this.scrollBehaviorY.resetStartPos();\r\n        this.hooks.trigger(this.hooks.eventTypes.start, e);\r\n    };\r\n    ScrollerActions.prototype.handleMove = function (deltaX, deltaY, e) {\r\n        if (this.hooks.trigger(this.hooks.eventTypes.beforeMove, e)) {\r\n            return;\r\n        }\r\n        var absDistX = this.scrollBehaviorX.getAbsDist(deltaX);\r\n        var absDistY = this.scrollBehaviorY.getAbsDist(deltaY);\r\n        var timestamp = getNow();\r\n        // We need to move at least momentumLimitDistance pixels\r\n        // for the scrolling to initiate\r\n        if (this.checkMomentum(absDistX, absDistY, timestamp)) {\r\n            return true;\r\n        }\r\n        if (this.directionLockAction.checkMovingDirection(absDistX, absDistY, e)) {\r\n            this.actionsHandler.setInitiated();\r\n            return true;\r\n        }\r\n        var delta = this.directionLockAction.adjustDelta(deltaX, deltaY);\r\n        var prevX = this.scrollBehaviorX.getCurrentPos();\r\n        var newX = this.scrollBehaviorX.move(delta.deltaX);\r\n        var prevY = this.scrollBehaviorY.getCurrentPos();\r\n        var newY = this.scrollBehaviorY.move(delta.deltaY);\r\n        if (this.hooks.trigger(this.hooks.eventTypes.detectMovingDirection)) {\r\n            return;\r\n        }\r\n        if (!this.fingerMoved) {\r\n            this.fingerMoved = true;\r\n        }\r\n        var positionChanged = newX !== prevX || newY !== prevY;\r\n        if (!this.contentMoved && !positionChanged) {\r\n            this.hooks.trigger(this.hooks.eventTypes.contentNotMoved);\r\n        }\r\n        if (!this.contentMoved && positionChanged) {\r\n            this.contentMoved = true;\r\n            this.hooks.trigger(this.hooks.eventTypes.scrollStart);\r\n        }\r\n        if (this.contentMoved && positionChanged) {\r\n            this.animater.translate({\r\n                x: newX,\r\n                y: newY,\r\n            });\r\n            this.dispatchScroll(timestamp);\r\n        }\r\n    };\r\n    ScrollerActions.prototype.dispatchScroll = function (timestamp) {\r\n        // dispatch scroll in interval time\r\n        if (timestamp - this.startTime > this.options.momentumLimitTime) {\r\n            // refresh time and starting position to initiate a momentum\r\n            this.startTime = timestamp;\r\n            this.scrollBehaviorX.updateStartPos();\r\n            this.scrollBehaviorY.updateStartPos();\r\n            if (this.options.probeType === 1 /* Throttle */) {\r\n                this.hooks.trigger(this.hooks.eventTypes.scroll, this.getCurrentPos());\r\n            }\r\n        }\r\n        // dispatch scroll all the time\r\n        if (this.options.probeType > 1 /* Throttle */) {\r\n            this.hooks.trigger(this.hooks.eventTypes.scroll, this.getCurrentPos());\r\n        }\r\n    };\r\n    ScrollerActions.prototype.checkMomentum = function (absDistX, absDistY, timestamp) {\r\n        return (timestamp - this.endTime > this.options.momentumLimitTime &&\r\n            absDistY < this.options.momentumLimitDistance &&\r\n            absDistX < this.options.momentumLimitDistance);\r\n    };\r\n    ScrollerActions.prototype.handleEnd = function (e) {\r\n        if (this.hooks.trigger(this.hooks.eventTypes.beforeEnd, e)) {\r\n            return;\r\n        }\r\n        var currentPos = this.getCurrentPos();\r\n        this.scrollBehaviorX.updateDirection();\r\n        this.scrollBehaviorY.updateDirection();\r\n        if (this.hooks.trigger(this.hooks.eventTypes.end, e, currentPos)) {\r\n            return true;\r\n        }\r\n        currentPos = this.ensureIntegerPos(currentPos);\r\n        this.animater.translate(currentPos);\r\n        this.endTime = getNow();\r\n        var duration = this.endTime - this.startTime;\r\n        this.hooks.trigger(this.hooks.eventTypes.scrollEnd, currentPos, duration);\r\n    };\r\n    ScrollerActions.prototype.ensureIntegerPos = function (currentPos) {\r\n        this.ensuringInteger = true;\r\n        var x = currentPos.x, y = currentPos.y;\r\n        var _a = this.scrollBehaviorX, minScrollPosX = _a.minScrollPos, maxScrollPosX = _a.maxScrollPos;\r\n        var _b = this.scrollBehaviorY, minScrollPosY = _b.minScrollPos, maxScrollPosY = _b.maxScrollPos;\r\n        x = x > 0 ? Math.ceil(x) : Math.floor(x);\r\n        y = y > 0 ? Math.ceil(y) : Math.floor(y);\r\n        x = between(x, maxScrollPosX, minScrollPosX);\r\n        y = between(y, maxScrollPosY, minScrollPosY);\r\n        return { x: x, y: y };\r\n    };\r\n    ScrollerActions.prototype.handleClick = function (e) {\r\n        if (!preventDefaultExceptionFn(e.target, this.options.preventDefaultException)) {\r\n            maybePrevent(e);\r\n            e.stopPropagation();\r\n        }\r\n    };\r\n    ScrollerActions.prototype.getCurrentPos = function () {\r\n        return {\r\n            x: this.scrollBehaviorX.getCurrentPos(),\r\n            y: this.scrollBehaviorY.getCurrentPos(),\r\n        };\r\n    };\r\n    ScrollerActions.prototype.refresh = function () {\r\n        this.endTime = 0;\r\n    };\r\n    ScrollerActions.prototype.destroy = function () {\r\n        this.hooks.destroy();\r\n    };\r\n    return ScrollerActions;\r\n}());\n\nfunction createActionsHandlerOptions(bsOptions) {\r\n    var options = [\r\n        'click',\r\n        'bindToWrapper',\r\n        'disableMouse',\r\n        'disableTouch',\r\n        'preventDefault',\r\n        'stopPropagation',\r\n        'tagException',\r\n        'preventDefaultException',\r\n        'autoEndDistance',\r\n    ].reduce(function (prev, cur) {\r\n        prev[cur] = bsOptions[cur];\r\n        return prev;\r\n    }, {});\r\n    return options;\r\n}\r\nfunction createBehaviorOptions(bsOptions, extraProp, bounces, rect) {\r\n    var options = [\r\n        'momentum',\r\n        'momentumLimitTime',\r\n        'momentumLimitDistance',\r\n        'deceleration',\r\n        'swipeBounceTime',\r\n        'swipeTime',\r\n        'outOfBoundaryDampingFactor',\r\n        'specifiedIndexAsContent',\r\n    ].reduce(function (prev, cur) {\r\n        prev[cur] = bsOptions[cur];\r\n        return prev;\r\n    }, {});\r\n    // add extra property\r\n    options.scrollable = !!bsOptions[extraProp];\r\n    options.bounces = bounces;\r\n    options.rect = rect;\r\n    return options;\r\n}\n\nfunction bubbling(source, target, events) {\r\n    events.forEach(function (event) {\r\n        var sourceEvent;\r\n        var targetEvent;\r\n        if (typeof event === 'string') {\r\n            sourceEvent = targetEvent = event;\r\n        }\r\n        else {\r\n            sourceEvent = event.source;\r\n            targetEvent = event.target;\r\n        }\r\n        source.on(sourceEvent, function () {\r\n            var args = [];\r\n            for (var _i = 0; _i < arguments.length; _i++) {\r\n                args[_i] = arguments[_i];\r\n            }\r\n            return target.trigger.apply(target, __spreadArrays([targetEvent], args));\r\n        });\r\n    });\r\n}\n\nfunction isSamePoint(startPoint, endPoint) {\r\n    // keys of startPoint and endPoint should be equal\r\n    var keys = Object.keys(startPoint);\r\n    for (var _i = 0, keys_1 = keys; _i < keys_1.length; _i++) {\r\n        var key = keys_1[_i];\r\n        if (startPoint[key] !== endPoint[key])\r\n            return false;\r\n    }\r\n    return true;\r\n}\n\nvar MIN_SCROLL_DISTANCE = 1;\r\nvar Scroller = /** @class */ (function () {\r\n    function Scroller(wrapper, content, options) {\r\n        this.wrapper = wrapper;\r\n        this.content = content;\r\n        this.resizeTimeout = 0;\r\n        this.hooks = new EventEmitter([\r\n            'beforeStart',\r\n            'beforeMove',\r\n            'beforeScrollStart',\r\n            'scrollStart',\r\n            'scroll',\r\n            'beforeEnd',\r\n            'scrollEnd',\r\n            'resize',\r\n            'touchEnd',\r\n            'end',\r\n            'flick',\r\n            'scrollCancel',\r\n            'momentum',\r\n            'scrollTo',\r\n            'minDistanceScroll',\r\n            'scrollToElement',\r\n            'beforeRefresh',\r\n        ]);\r\n        this.options = options;\r\n        var _a = this.options.bounce, left = _a.left, right = _a.right, top = _a.top, bottom = _a.bottom;\r\n        // direction X\r\n        this.scrollBehaviorX = new Behavior(wrapper, content, createBehaviorOptions(options, 'scrollX', [left, right], {\r\n            size: 'width',\r\n            position: 'left',\r\n        }));\r\n        // direction Y\r\n        this.scrollBehaviorY = new Behavior(wrapper, content, createBehaviorOptions(options, 'scrollY', [top, bottom], {\r\n            size: 'height',\r\n            position: 'top',\r\n        }));\r\n        this.translater = new Translater(this.content);\r\n        this.animater = createAnimater(this.content, this.translater, this.options);\r\n        this.actionsHandler = new ActionsHandler(this.options.bindToTarget ? this.content : wrapper, createActionsHandlerOptions(this.options));\r\n        this.actions = new ScrollerActions(this.scrollBehaviorX, this.scrollBehaviorY, this.actionsHandler, this.animater, this.options);\r\n        var resizeHandler = this.resize.bind(this);\r\n        this.resizeRegister = new EventRegister(window, [\r\n            {\r\n                name: 'orientationchange',\r\n                handler: resizeHandler,\r\n            },\r\n            {\r\n                name: 'resize',\r\n                handler: resizeHandler,\r\n            },\r\n        ]);\r\n        this.registerTransitionEnd();\r\n        this.init();\r\n    }\r\n    Scroller.prototype.init = function () {\r\n        var _this = this;\r\n        this.bindTranslater();\r\n        this.bindAnimater();\r\n        this.bindActions();\r\n        // enable pointer events when scrolling ends\r\n        this.hooks.on(this.hooks.eventTypes.scrollEnd, function () {\r\n            _this.togglePointerEvents(true);\r\n        });\r\n    };\r\n    Scroller.prototype.registerTransitionEnd = function () {\r\n        this.transitionEndRegister = new EventRegister(this.content, [\r\n            {\r\n                name: style.transitionEnd,\r\n                handler: this.transitionEnd.bind(this),\r\n            },\r\n        ]);\r\n    };\r\n    Scroller.prototype.bindTranslater = function () {\r\n        var _this = this;\r\n        var hooks = this.translater.hooks;\r\n        hooks.on(hooks.eventTypes.beforeTranslate, function (transformStyle) {\r\n            if (_this.options.translateZ) {\r\n                transformStyle.push(_this.options.translateZ);\r\n            }\r\n        });\r\n        // disable pointer events when scrolling\r\n        hooks.on(hooks.eventTypes.translate, function (pos) {\r\n            var prevPos = _this.getCurrentPos();\r\n            _this.updatePositions(pos);\r\n            // scrollEnd will dispatch when scroll is force stopping in touchstart handler\r\n            // so in touchend handler, don't toggle pointer-events\r\n            if (_this.actions.ensuringInteger === true) {\r\n                _this.actions.ensuringInteger = false;\r\n                return;\r\n            }\r\n            // a valid translate\r\n            if (pos.x !== prevPos.x || pos.y !== prevPos.y) {\r\n                _this.togglePointerEvents(false);\r\n            }\r\n        });\r\n    };\r\n    Scroller.prototype.bindAnimater = function () {\r\n        var _this = this;\r\n        // reset position\r\n        this.animater.hooks.on(this.animater.hooks.eventTypes.end, function (pos) {\r\n            if (!_this.resetPosition(_this.options.bounceTime)) {\r\n                _this.animater.setPending(false);\r\n                _this.hooks.trigger(_this.hooks.eventTypes.scrollEnd, pos);\r\n            }\r\n        });\r\n        bubbling(this.animater.hooks, this.hooks, [\r\n            {\r\n                source: this.animater.hooks.eventTypes.move,\r\n                target: this.hooks.eventTypes.scroll,\r\n            },\r\n            {\r\n                source: this.animater.hooks.eventTypes.forceStop,\r\n                target: this.hooks.eventTypes.scrollEnd,\r\n            },\r\n        ]);\r\n    };\r\n    Scroller.prototype.bindActions = function () {\r\n        var _this = this;\r\n        var actions = this.actions;\r\n        bubbling(actions.hooks, this.hooks, [\r\n            {\r\n                source: actions.hooks.eventTypes.start,\r\n                target: this.hooks.eventTypes.beforeStart,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.start,\r\n                target: this.hooks.eventTypes.beforeScrollStart,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.beforeMove,\r\n                target: this.hooks.eventTypes.beforeMove,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.scrollStart,\r\n                target: this.hooks.eventTypes.scrollStart,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.scroll,\r\n                target: this.hooks.eventTypes.scroll,\r\n            },\r\n            {\r\n                source: actions.hooks.eventTypes.beforeEnd,\r\n                target: this.hooks.eventTypes.beforeEnd,\r\n            },\r\n        ]);\r\n        actions.hooks.on(actions.hooks.eventTypes.end, function (e, pos) {\r\n            _this.hooks.trigger(_this.hooks.eventTypes.touchEnd, pos);\r\n            if (_this.hooks.trigger(_this.hooks.eventTypes.end, pos)) {\r\n                return true;\r\n            }\r\n            // check if it is a click operation\r\n            if (!actions.fingerMoved) {\r\n                _this.hooks.trigger(_this.hooks.eventTypes.scrollCancel);\r\n                if (_this.checkClick(e)) {\r\n                    return true;\r\n                }\r\n            }\r\n            // reset if we are outside of the boundaries\r\n            if (_this.resetPosition(_this.options.bounceTime, ease.bounce)) {\r\n                _this.animater.setForceStopped(false);\r\n                return true;\r\n            }\r\n        });\r\n        actions.hooks.on(actions.hooks.eventTypes.scrollEnd, function (pos, duration) {\r\n            var deltaX = Math.abs(pos.x - _this.scrollBehaviorX.startPos);\r\n            var deltaY = Math.abs(pos.y - _this.scrollBehaviorY.startPos);\r\n            if (_this.checkFlick(duration, deltaX, deltaY)) {\r\n                _this.animater.setForceStopped(false);\r\n                _this.hooks.trigger(_this.hooks.eventTypes.flick);\r\n                return;\r\n            }\r\n            if (_this.momentum(pos, duration)) {\r\n                _this.animater.setForceStopped(false);\r\n                return;\r\n            }\r\n            if (actions.contentMoved) {\r\n                _this.hooks.trigger(_this.hooks.eventTypes.scrollEnd, pos);\r\n            }\r\n            if (_this.animater.forceStopped) {\r\n                _this.animater.setForceStopped(false);\r\n            }\r\n        });\r\n    };\r\n    Scroller.prototype.checkFlick = function (duration, deltaX, deltaY) {\r\n        var flickMinMovingDistance = 1; // distinguish flick from click\r\n        if (this.hooks.events.flick.length > 1 &&\r\n            duration < this.options.flickLimitTime &&\r\n            deltaX < this.options.flickLimitDistance &&\r\n            deltaY < this.options.flickLimitDistance &&\r\n            (deltaY > flickMinMovingDistance || deltaX > flickMinMovingDistance)) {\r\n            return true;\r\n        }\r\n    };\r\n    Scroller.prototype.momentum = function (pos, duration) {\r\n        var meta = {\r\n            time: 0,\r\n            easing: ease.swiper,\r\n            newX: pos.x,\r\n            newY: pos.y,\r\n        };\r\n        // start momentum animation if needed\r\n        var momentumX = this.scrollBehaviorX.end(duration);\r\n        var momentumY = this.scrollBehaviorY.end(duration);\r\n        meta.newX = isUndef(momentumX.destination)\r\n            ? meta.newX\r\n            : momentumX.destination;\r\n        meta.newY = isUndef(momentumY.destination)\r\n            ? meta.newY\r\n            : momentumY.destination;\r\n        meta.time = Math.max(momentumX.duration, momentumY.duration);\r\n        this.hooks.trigger(this.hooks.eventTypes.momentum, meta, this);\r\n        // when x or y changed, do momentum animation now!\r\n        if (meta.newX !== pos.x || meta.newY !== pos.y) {\r\n            // change easing function when scroller goes out of the boundaries\r\n            if (meta.newX > this.scrollBehaviorX.minScrollPos ||\r\n                meta.newX < this.scrollBehaviorX.maxScrollPos ||\r\n                meta.newY > this.scrollBehaviorY.minScrollPos ||\r\n                meta.newY < this.scrollBehaviorY.maxScrollPos) {\r\n                meta.easing = ease.swipeBounce;\r\n            }\r\n            this.scrollTo(meta.newX, meta.newY, meta.time, meta.easing);\r\n            return true;\r\n        }\r\n    };\r\n    Scroller.prototype.checkClick = function (e) {\r\n        var cancelable = {\r\n            preventClick: this.animater.forceStopped,\r\n        };\r\n        // we scrolled less than momentumLimitDistance pixels\r\n        if (this.hooks.trigger(this.hooks.eventTypes.checkClick)) {\r\n            this.animater.setForceStopped(false);\r\n            return true;\r\n        }\r\n        if (!cancelable.preventClick) {\r\n            var _dblclick = this.options.dblclick;\r\n            var dblclickTrigged = false;\r\n            if (_dblclick && this.lastClickTime) {\r\n                var _a = _dblclick.delay, delay = _a === void 0 ? 300 : _a;\r\n                if (getNow() - this.lastClickTime < delay) {\r\n                    dblclickTrigged = true;\r\n                    dblclick(e);\r\n                }\r\n            }\r\n            if (this.options.tap) {\r\n                tap(e, this.options.tap);\r\n            }\r\n            if (this.options.click &&\r\n                !preventDefaultExceptionFn(e.target, this.options.preventDefaultException)) {\r\n                click(e);\r\n            }\r\n            this.lastClickTime = dblclickTrigged ? null : getNow();\r\n            return true;\r\n        }\r\n        return false;\r\n    };\r\n    Scroller.prototype.resize = function () {\r\n        var _this = this;\r\n        if (!this.actions.enabled) {\r\n            return;\r\n        }\r\n        // fix a scroll problem under Android condition\r\n        /* istanbul ignore if  */\r\n        if (isAndroid) {\r\n            this.wrapper.scrollTop = 0;\r\n        }\r\n        clearTimeout(this.resizeTimeout);\r\n        this.resizeTimeout = window.setTimeout(function () {\r\n            _this.hooks.trigger(_this.hooks.eventTypes.resize);\r\n        }, this.options.resizePolling);\r\n    };\r\n    /* istanbul ignore next */\r\n    Scroller.prototype.transitionEnd = function (e) {\r\n        if (e.target !== this.content || !this.animater.pending) {\r\n            return;\r\n        }\r\n        var animater = this.animater;\r\n        animater.transitionTime();\r\n        if (!this.resetPosition(this.options.bounceTime, ease.bounce)) {\r\n            this.animater.setPending(false);\r\n            if (this.options.probeType !== 3 /* Realtime */) {\r\n                this.hooks.trigger(this.hooks.eventTypes.scrollEnd, this.getCurrentPos());\r\n            }\r\n        }\r\n    };\r\n    Scroller.prototype.togglePointerEvents = function (enabled) {\r\n        if (enabled === void 0) { enabled = true; }\r\n        var el = this.content.children.length\r\n            ? this.content.children\r\n            : [this.content];\r\n        var pointerEvents = enabled ? 'auto' : 'none';\r\n        for (var i = 0; i < el.length; i++) {\r\n            var node = el[i];\r\n            // ignore BetterScroll instance's wrapper DOM\r\n            /* istanbul ignore if  */\r\n            if (node.isBScrollContainer) {\r\n                continue;\r\n            }\r\n            node.style.pointerEvents = pointerEvents;\r\n        }\r\n    };\r\n    Scroller.prototype.refresh = function (content) {\r\n        var contentChanged = this.setContent(content);\r\n        this.hooks.trigger(this.hooks.eventTypes.beforeRefresh);\r\n        this.scrollBehaviorX.refresh(content);\r\n        this.scrollBehaviorY.refresh(content);\r\n        if (contentChanged) {\r\n            this.translater.setContent(content);\r\n            this.animater.setContent(content);\r\n            this.transitionEndRegister.destroy();\r\n            this.registerTransitionEnd();\r\n            if (this.options.bindToTarget) {\r\n                this.actionsHandler.setContent(content);\r\n            }\r\n        }\r\n        this.actions.refresh();\r\n        this.wrapperOffset = offset(this.wrapper);\r\n    };\r\n    Scroller.prototype.setContent = function (content) {\r\n        var contentChanged = content !== this.content;\r\n        if (contentChanged) {\r\n            this.content = content;\r\n        }\r\n        return contentChanged;\r\n    };\r\n    Scroller.prototype.scrollBy = function (deltaX, deltaY, time, easing) {\r\n        if (time === void 0) { time = 0; }\r\n        var _a = this.getCurrentPos(), x = _a.x, y = _a.y;\r\n        easing = !easing ? ease.bounce : easing;\r\n        deltaX += x;\r\n        deltaY += y;\r\n        this.scrollTo(deltaX, deltaY, time, easing);\r\n    };\r\n    Scroller.prototype.scrollTo = function (x, y, time, easing, extraTransform) {\r\n        if (time === void 0) { time = 0; }\r\n        if (easing === void 0) { easing = ease.bounce; }\r\n        if (extraTransform === void 0) { extraTransform = {\r\n            start: {},\r\n            end: {},\r\n        }; }\r\n        var easingFn = this.options.useTransition ? easing.style : easing.fn;\r\n        var currentPos = this.getCurrentPos();\r\n        var startPoint = __assign({ x: currentPos.x, y: currentPos.y }, extraTransform.start);\r\n        var endPoint = __assign({ x: x,\r\n            y: y }, extraTransform.end);\r\n        this.hooks.trigger(this.hooks.eventTypes.scrollTo, endPoint);\r\n        // it is an useless move\r\n        if (isSamePoint(startPoint, endPoint))\r\n            return;\r\n        var deltaX = Math.abs(endPoint.x - startPoint.x);\r\n        var deltaY = Math.abs(endPoint.y - startPoint.y);\r\n        // considering of browser compatibility for decimal transform value\r\n        // force translating immediately\r\n        if (deltaX < MIN_SCROLL_DISTANCE && deltaY < MIN_SCROLL_DISTANCE) {\r\n            time = 0;\r\n            this.hooks.trigger(this.hooks.eventTypes.minDistanceScroll);\r\n        }\r\n        this.animater.move(startPoint, endPoint, time, easingFn);\r\n    };\r\n    Scroller.prototype.scrollToElement = function (el, time, offsetX, offsetY, easing) {\r\n        var targetEle = getElement(el);\r\n        var pos = offset(targetEle);\r\n        var getOffset = function (offset, size, wrapperSize) {\r\n            if (typeof offset === 'number') {\r\n                return offset;\r\n            }\r\n            // if offsetX/Y are true we center the element to the screen\r\n            return offset ? Math.round(size / 2 - wrapperSize / 2) : 0;\r\n        };\r\n        offsetX = getOffset(offsetX, targetEle.offsetWidth, this.wrapper.offsetWidth);\r\n        offsetY = getOffset(offsetY, targetEle.offsetHeight, this.wrapper.offsetHeight);\r\n        var getPos = function (pos, wrapperPos, offset, scrollBehavior) {\r\n            pos -= wrapperPos;\r\n            pos = scrollBehavior.adjustPosition(pos - offset);\r\n            return pos;\r\n        };\r\n        pos.left = getPos(pos.left, this.wrapperOffset.left, offsetX, this.scrollBehaviorX);\r\n        pos.top = getPos(pos.top, this.wrapperOffset.top, offsetY, this.scrollBehaviorY);\r\n        if (this.hooks.trigger(this.hooks.eventTypes.scrollToElement, targetEle, pos)) {\r\n            return;\r\n        }\r\n        this.scrollTo(pos.left, pos.top, time, easing);\r\n    };\r\n    Scroller.prototype.resetPosition = function (time, easing) {\r\n        if (time === void 0) { time = 0; }\r\n        if (easing === void 0) { easing = ease.bounce; }\r\n        var _a = this.scrollBehaviorX.checkInBoundary(), x = _a.position, xInBoundary = _a.inBoundary;\r\n        var _b = this.scrollBehaviorY.checkInBoundary(), y = _b.position, yInBoundary = _b.inBoundary;\r\n        if (xInBoundary && yInBoundary) {\r\n            return false;\r\n        }\r\n        /* istanbul ignore if  */\r\n        if (isIOSBadVersion) {\r\n            // fix ios 13.4 bouncing\r\n            // see it in issues 982\r\n            this.reflow();\r\n        }\r\n        // out of boundary\r\n        this.scrollTo(x, y, time, easing);\r\n        return true;\r\n    };\r\n    /* istanbul ignore next */\r\n    Scroller.prototype.reflow = function () {\r\n        this._reflow = this.content.offsetHeight;\r\n    };\r\n    Scroller.prototype.updatePositions = function (pos) {\r\n        this.scrollBehaviorX.updatePosition(pos.x);\r\n        this.scrollBehaviorY.updatePosition(pos.y);\r\n    };\r\n    Scroller.prototype.getCurrentPos = function () {\r\n        return this.actions.getCurrentPos();\r\n    };\r\n    Scroller.prototype.enable = function () {\r\n        this.actions.enabled = true;\r\n    };\r\n    Scroller.prototype.disable = function () {\r\n        cancelAnimationFrame(this.animater.timer);\r\n        this.actions.enabled = false;\r\n    };\r\n    Scroller.prototype.destroy = function () {\r\n        var _this = this;\r\n        var keys = [\r\n            'resizeRegister',\r\n            'transitionEndRegister',\r\n            'actionsHandler',\r\n            'actions',\r\n            'hooks',\r\n            'animater',\r\n            'translater',\r\n            'scrollBehaviorX',\r\n            'scrollBehaviorY',\r\n        ];\r\n        keys.forEach(function (key) { return _this[key].destroy(); });\r\n    };\r\n    return Scroller;\r\n}());\n\nvar BScrollConstructor = /** @class */ (function (_super) {\r\n    __extends(BScrollConstructor, _super);\r\n    function BScrollConstructor(el, options) {\r\n        var _this = _super.call(this, [\r\n            'refresh',\r\n            'contentChanged',\r\n            'enable',\r\n            'disable',\r\n            'beforeScrollStart',\r\n            'scrollStart',\r\n            'scroll',\r\n            'scrollEnd',\r\n            'scrollCancel',\r\n            'touchEnd',\r\n            'flick',\r\n            'destroy'\r\n        ]) || this;\r\n        var wrapper = getElement(el);\r\n        if (!wrapper) {\r\n            warn('Can not resolve the wrapper DOM.');\r\n            return _this;\r\n        }\r\n        _this.plugins = {};\r\n        _this.options = new OptionsConstructor().merge(options).process();\r\n        if (!_this.setContent(wrapper).valid) {\r\n            return _this;\r\n        }\r\n        _this.hooks = new EventEmitter([\r\n            'refresh',\r\n            'enable',\r\n            'disable',\r\n            'destroy',\r\n            'beforeInitialScrollTo',\r\n            'contentChanged'\r\n        ]);\r\n        _this.init(wrapper);\r\n        return _this;\r\n    }\r\n    BScrollConstructor.use = function (ctor) {\r\n        var name = ctor.pluginName;\r\n        var installed = BScrollConstructor.plugins.some(function (plugin) { return ctor === plugin.ctor; });\r\n        if (installed)\r\n            return BScrollConstructor;\r\n        if (isUndef(name)) {\r\n            warn(\"Plugin Class must specify plugin's name in static property by 'pluginName' field.\");\r\n            return BScrollConstructor;\r\n        }\r\n        BScrollConstructor.pluginsMap[name] = true;\r\n        BScrollConstructor.plugins.push({\r\n            name: name,\r\n            applyOrder: ctor.applyOrder,\r\n            ctor: ctor\r\n        });\r\n        return BScrollConstructor;\r\n    };\r\n    BScrollConstructor.prototype.setContent = function (wrapper) {\r\n        var contentChanged = false;\r\n        var valid = true;\r\n        var content = wrapper.children[this.options.specifiedIndexAsContent];\r\n        if (!content) {\r\n            warn('The wrapper need at least one child element to be content element to scroll.');\r\n            valid = false;\r\n        }\r\n        else {\r\n            contentChanged = this.content !== content;\r\n            if (contentChanged) {\r\n                this.content = content;\r\n            }\r\n        }\r\n        return {\r\n            valid: valid,\r\n            contentChanged: contentChanged\r\n        };\r\n    };\r\n    BScrollConstructor.prototype.init = function (wrapper) {\r\n        var _this = this;\r\n        this.wrapper = wrapper;\r\n        // mark wrapper to recognize bs instance by DOM attribute\r\n        wrapper.isBScrollContainer = true;\r\n        this.scroller = new Scroller(wrapper, this.content, this.options);\r\n        this.scroller.hooks.on(this.scroller.hooks.eventTypes.resize, function () {\r\n            _this.refresh();\r\n        });\r\n        this.eventBubbling();\r\n        this.handleAutoBlur();\r\n        this.enable();\r\n        this.proxy(propertiesConfig);\r\n        this.applyPlugins();\r\n        // maybe boundary has changed, should refresh\r\n        this.refreshWithoutReset(this.content);\r\n        var _a = this.options, startX = _a.startX, startY = _a.startY;\r\n        var position = {\r\n            x: startX,\r\n            y: startY\r\n        };\r\n        // maybe plugins want to control scroll position\r\n        if (this.hooks.trigger(this.hooks.eventTypes.beforeInitialScrollTo, position)) {\r\n            return;\r\n        }\r\n        this.scroller.scrollTo(position.x, position.y);\r\n    };\r\n    BScrollConstructor.prototype.applyPlugins = function () {\r\n        var _this = this;\r\n        var options = this.options;\r\n        BScrollConstructor.plugins\r\n            .sort(function (a, b) {\r\n            var _a;\r\n            var applyOrderMap = (_a = {},\r\n                _a[\"pre\" /* Pre */] = -1,\r\n                _a[\"post\" /* Post */] = 1,\r\n                _a);\r\n            var aOrder = a.applyOrder ? applyOrderMap[a.applyOrder] : 0;\r\n            var bOrder = b.applyOrder ? applyOrderMap[b.applyOrder] : 0;\r\n            return aOrder - bOrder;\r\n        })\r\n            .forEach(function (item) {\r\n            var ctor = item.ctor;\r\n            if (options[item.name] && typeof ctor === 'function') {\r\n                _this.plugins[item.name] = new ctor(_this);\r\n            }\r\n        });\r\n    };\r\n    BScrollConstructor.prototype.handleAutoBlur = function () {\r\n        /* istanbul ignore if  */\r\n        if (this.options.autoBlur) {\r\n            this.on(this.eventTypes.beforeScrollStart, function () {\r\n                var activeElement = document.activeElement;\r\n                if (activeElement &&\r\n                    (activeElement.tagName === 'INPUT' ||\r\n                        activeElement.tagName === 'TEXTAREA')) {\r\n                    activeElement.blur();\r\n                }\r\n            });\r\n        }\r\n    };\r\n    BScrollConstructor.prototype.eventBubbling = function () {\r\n        bubbling(this.scroller.hooks, this, [\r\n            this.eventTypes.beforeScrollStart,\r\n            this.eventTypes.scrollStart,\r\n            this.eventTypes.scroll,\r\n            this.eventTypes.scrollEnd,\r\n            this.eventTypes.scrollCancel,\r\n            this.eventTypes.touchEnd,\r\n            this.eventTypes.flick\r\n        ]);\r\n    };\r\n    BScrollConstructor.prototype.refreshWithoutReset = function (content) {\r\n        this.scroller.refresh(content);\r\n        this.hooks.trigger(this.hooks.eventTypes.refresh, content);\r\n        this.trigger(this.eventTypes.refresh, content);\r\n    };\r\n    BScrollConstructor.prototype.proxy = function (propertiesConfig) {\r\n        var _this = this;\r\n        propertiesConfig.forEach(function (_a) {\r\n            var key = _a.key, sourceKey = _a.sourceKey;\r\n            propertiesProxy(_this, sourceKey, key);\r\n        });\r\n    };\r\n    BScrollConstructor.prototype.refresh = function () {\r\n        var _a = this.setContent(this.wrapper), contentChanged = _a.contentChanged, valid = _a.valid;\r\n        if (valid) {\r\n            var content = this.content;\r\n            this.refreshWithoutReset(content);\r\n            if (contentChanged) {\r\n                this.hooks.trigger(this.hooks.eventTypes.contentChanged, content);\r\n                this.trigger(this.eventTypes.contentChanged, content);\r\n            }\r\n            this.scroller.resetPosition();\r\n        }\r\n    };\r\n    BScrollConstructor.prototype.enable = function () {\r\n        this.scroller.enable();\r\n        this.hooks.trigger(this.hooks.eventTypes.enable);\r\n        this.trigger(this.eventTypes.enable);\r\n    };\r\n    BScrollConstructor.prototype.disable = function () {\r\n        this.scroller.disable();\r\n        this.hooks.trigger(this.hooks.eventTypes.disable);\r\n        this.trigger(this.eventTypes.disable);\r\n    };\r\n    BScrollConstructor.prototype.destroy = function () {\r\n        this.hooks.trigger(this.hooks.eventTypes.destroy);\r\n        this.trigger(this.eventTypes.destroy);\r\n        this.scroller.destroy();\r\n    };\r\n    BScrollConstructor.prototype.eventRegister = function (names) {\r\n        this.registerType(names);\r\n    };\r\n    BScrollConstructor.plugins = [];\r\n    BScrollConstructor.pluginsMap = {};\r\n    return BScrollConstructor;\r\n}(EventEmitter));\r\nfunction createBScroll(el, options) {\r\n    var bs = new BScrollConstructor(el, options);\r\n    return bs;\r\n}\r\ncreateBScroll.use = BScrollConstructor.use;\r\ncreateBScroll.plugins = BScrollConstructor.plugins;\r\ncreateBScroll.pluginsMap = BScrollConstructor.pluginsMap;\r\nvar BScroll = createBScroll;\n\nexport { Behavior, CustomOptions, createBScroll, BScroll as default };\n"], "mappings": ";;;AAqBA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUA,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AACpG,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEA,SAAS,UAAU,GAAG,GAAG;AACrB,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAEA,IAAI,WAAW,WAAW;AACtB,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,iBAAiB;AACtB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,IAAK,MAAK,UAAU,CAAC,EAAE;AAC7E,WAAS,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI;AACzC,aAAS,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,KAAK;AAC1D,QAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO;AACX;AAEA,IAAI,mBAAmB;AAAA,EACnB;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AAAA,EACA;AAAA,IACI,WAAW;AAAA,IACX,KAAK;AAAA,EACT;AACJ;AAEA,SAAS,KAAK,KAAK;AACf,UAAQ,MAAM,qBAAqB,GAAG;AAC1C;AAGA,IAAI,YAAY,OAAO,WAAW;AAClC,IAAI,KAAK,aAAa,UAAU,UAAU,YAAY;AACtD,IAAI,mBAAmB,CAAC,EAAE,MAAM,iBAAiB,KAAK,EAAE;AACxD,IAAI,YAAY,MAAM,GAAG,QAAQ,SAAS,IAAI;AAE9C,IAAI,kBAAmB,WAAY;AAC/B,MAAI,OAAO,OAAO,UAAU;AACxB,QAAI,QAAQ;AACZ,QAAI,UAAU,MAAM,KAAK,EAAE;AAC3B,QAAI,CAAC;AACD,aAAO;AACX,QAAI,QAAQ,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,SAAU,MAAM;AAClD,aAAO,SAAS,MAAM,EAAE;AAAA,IAC5B,CAAC;AAED,WAAO,CAAC,EAAE,MAAM,CAAC,MAAM,MAAM,MAAM,CAAC,KAAK;AAAA,EAC7C;AACA,SAAO;AACX,EAAG;AAEH,IAAI,kBAAkB;AAEtB,IAAI,WAAW;AACP,cAAY;AAChB,MAAI;AACI,WAAO,CAAC;AACZ,WAAO,eAAe,MAAM,WAAW;AAAA,MACnC,KAAK,WAAY;AACb,0BAAkB;AAAA,MACtB;AAAA,IACJ,CAAC;AACD,WAAO,iBAAiB,WAAW,WAAY;AAAA,IAAE,GAAG,IAAI;AAAA,EAC5D,SACO,GAAG;AAAA,EAAE;AAChB;AAXQ;AAEI;AAWZ,SAAS,SAAS;AACd,SAAO,OAAO,eACV,OAAO,YAAY,OACnB,OAAO,YAAY,SACjB,OAAO,YAAY,IAAI,IAAI,OAAO,YAAY,OAAO,kBACrD,CAAC,oBAAI,KAAK;AACpB;AACA,IAAI,SAAS,SAAU,QAAQ,QAAQ;AACnC,WAAS,OAAO,QAAQ;AACpB,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC5B;AACA,SAAO;AACX;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,MAAM,UAAa,MAAM;AACpC;AACA,SAAS,QAAQ,GAAG,KAAK,KAAK;AAC1B,MAAI,IAAI,KAAK;AACT,WAAO;AAAA,EACX;AACA,MAAI,IAAI,KAAK;AACT,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAI,eAAgB,aAChB,SAAS,cAAc,KAAK,EAAE;AAClC,IAAI,SAAU,WAAY;AAEtB,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,MAAI,iBAAiB;AAAA,IACjB;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,IACA;AAAA,MACI,KAAK;AAAA,MACL,OAAO;AAAA,IACX;AAAA,EACJ;AACA,WAAS,KAAK,GAAG,mBAAmB,gBAAgB,KAAK,iBAAiB,QAAQ,MAAM;AACpF,QAAI,MAAM,iBAAiB,EAAE;AAC7B,QAAI,aAAa,IAAI,KAAK,MAAM,QAAW;AACvC,aAAO,IAAI;AAAA,IACf;AAAA,EACJ;AAEA,SAAO;AACX,EAAG;AAEH,SAAS,YAAYC,QAAO;AACxB,MAAI,WAAW,OAAO;AAClB,WAAOA;AAAA,EACX;AACA,MAAI,WAAW,YAAY;AACvB,QAAIA,WAAU,iBAAiB;AAC3B,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX;AACA,SAAO,SAASA,OAAM,OAAO,CAAC,EAAE,YAAY,IAAIA,OAAM,OAAO,CAAC;AAClE;AACA,SAAS,WAAW,IAAI;AACpB,SAAQ,OAAO,OAAO,WAAW,SAAS,cAAc,EAAE,IAAI;AAClE;AACA,SAAS,SAAS,IAAI,MAAM,IAAI,SAAS;AACrC,MAAI,aAAa,kBACX;AAAA,IACE,SAAS;AAAA,IACT,SAAS,CAAC,CAAC;AAAA,EACf,IACE,CAAC,CAAC;AACR,KAAG,iBAAiB,MAAM,IAAI,UAAU;AAC5C;AACA,SAAS,YAAY,IAAI,MAAM,IAAI,SAAS;AACxC,KAAG,oBAAoB,MAAM,IAAI;AAAA,IAC7B,SAAS,CAAC,CAAC;AAAA,EACf,CAAC;AACL;AACA,SAAS,aAAa,GAAG;AACrB,MAAI,EAAE,YAAY;AACd,MAAE,eAAe;AAAA,EACrB;AACJ;AACA,SAAS,OAAO,IAAI;AAChB,MAAI,OAAO;AACX,MAAI,MAAM;AACV,SAAO,IAAI;AACP,YAAQ,GAAG;AACX,WAAO,GAAG;AACV,SAAK,GAAG;AAAA,EACZ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACA,UAAU,WAAW,aAAa,MAAM,OAAO,YAAY,IAAI,MAAM;AACrE,IAAI,YAAY,YAAY,WAAW;AACvC,IAAI,aAAa,YAAY,YAAY;AACzC,IAAI,iBAAiB,aAAa,YAAY,aAAa,KAAK;AAEhE,IAAI,WAAW,cAAc,kBAAkB,UAAU;AACzD,IAAI,gBAAgB,aAAa,cAAc;AAC/C,IAAI,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,0BAA0B,YAAY,0BAA0B;AAAA,EAChE,oBAAoB,YAAY,oBAAoB;AAAA,EACpD,iBAAiB,YAAY,iBAAiB;AAAA,EAC9C,iBAAiB,YAAY,iBAAiB;AAAA,EAC9C,eAAe,YAAY,eAAe;AAAA,EAC1C,oBAAoB,YAAY,oBAAoB;AACxD;AACA,IAAI,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AACb;AACA,SAAS,QAAQ,IAAI;AAEjB,MAAI,cAAc,OAAO,YAAY;AACjC,QAAI,OAAO,GAAG,sBAAsB;AACpC,WAAO;AAAA,MACH,KAAK,KAAK;AAAA,MACV,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACjB;AAAA,EACJ,OACK;AACD,WAAO;AAAA,MACH,KAAK,GAAG;AAAA,MACR,MAAM,GAAG;AAAA,MACT,OAAO,GAAG;AAAA,MACV,QAAQ,GAAG;AAAA,IACf;AAAA,EACJ;AACJ;AACA,SAAS,0BAA0B,IAAI,YAAY;AAC/C,WAAS,KAAK,YAAY;AACtB,QAAI,WAAW,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG;AAC3B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAI,iBAAiB;AACrB,SAAS,IAAI,GAAG,WAAW;AACvB,MAAI,KAAK,SAAS,YAAY,OAAO;AACrC,KAAG,UAAU,WAAW,MAAM,IAAI;AAClC,KAAG,QAAQ,EAAE;AACb,KAAG,QAAQ,EAAE;AACb,IAAE,OAAO,cAAc,EAAE;AAC7B;AACA,SAAS,MAAM,GAAG,OAAO;AACrB,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAS;AACzC,MAAI;AACJ,MAAI,EAAE,SAAS,WAAW;AACtB,kBAAc;AAAA,EAClB,WACS,EAAE,SAAS,cAAc,EAAE,SAAS,eAAe;AACxD,kBAAc,EAAE,eAAe,CAAC;AAAA,EACpC;AACA,MAAI,SAAS,CAAC;AACd,MAAI,aAAa;AACb,WAAO,UAAU,YAAY,WAAW;AACxC,WAAO,UAAU,YAAY,WAAW;AACxC,WAAO,UAAU,YAAY,WAAW;AACxC,WAAO,UAAU,YAAY,WAAW;AAAA,EAC5C;AACA,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,aAAa;AACjB,MAAI,UAAU,EAAE,SAAS,WAAW,EAAE,UAAU,SAAS,EAAE,QAAQ,UAAU,EAAE;AAC/E,MAAI,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,MAAI,OAAO,eAAe,aAAa;AACnC,QAAI;AACA,WAAK,IAAI,WAAW,OAAO,OAAO,SAAS;AAAA,QAAE;AAAA,QACzC;AAAA,MAAuB,GAAG,cAAc,GAAG,MAAM,CAAC;AAAA,IAC1D,SACOC,IAAG;AAEN,kBAAY;AAAA,IAChB;AAAA,EACJ,OACK;AACD,gBAAY;AAAA,EAChB;AACA,WAAS,cAAc;AACnB,SAAK,SAAS,YAAY,OAAO;AACjC,OAAG,UAAU,OAAO,SAAS,UAAU;AACvC,WAAO,IAAI,MAAM;AAAA,EACrB;AAEA,KAAG,sBAAsB;AACzB,KAAG,eAAe;AAClB,IAAE,OAAO,cAAc,EAAE;AAC7B;AACA,SAAS,SAAS,GAAG;AACjB,QAAM,GAAG,UAAU;AACvB;AAEA,IAAI,OAAO;AAAA;AAAA,EAEP,OAAO;AAAA,IACH,OAAO;AAAA,IACP,IAAI,SAAU,GAAG;AACb,aAAO,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI;AAAA,IACjC;AAAA,EACJ;AAAA;AAAA,EAEA,aAAa;AAAA,IACT,OAAO;AAAA,IACP,IAAI,SAAU,GAAG;AACb,aAAO,KAAK,IAAI;AAAA,IACpB;AAAA,EACJ;AAAA;AAAA,EAEA,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,IAAI,SAAU,GAAG;AACb,aAAO,IAAI,EAAE,IAAI,IAAI,IAAI;AAAA,IAC7B;AAAA,EACJ;AACJ;AAEA,IAAI,mBAAmB,MAAO;AAC9B,IAAI,eAAe,aAAa;AAEhC,SAAS,SAAS;AAAE;AACpB,IAAI,wBAAyB,WAAY;AAErC,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,SAAQ,aAAa,yBACjB,aAAa,+BACb,aAAa,4BACb,aAAa;AAAA,EAEb,SAAU,UAAU;AAChB,WAAO,OAAO,WAAW,UAAU,SAAS,YAAY,gBAAgB;AAAA,EAC5E;AACR,EAAG;AACH,IAAI,uBAAwB,WAAY;AAEpC,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,SAAQ,aAAa,wBACjB,aAAa,8BACb,aAAa,2BACb,aAAa,yBACb,SAAU,IAAI;AACV,WAAO,aAAa,EAAE;AAAA,EAC1B;AACR,EAAG;AAGH,IAAI,OAAO,SAAU,KAAK;AAAE;AAC5B,IAAI,2BAA2B;AAAA,EAC3B,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,KAAK;AAAA,EACL,KAAK;AACT;AACA,IAAI,cAAc,SAAU,KAAK,KAAK;AAClC,MAAI,OAAO,IAAI,MAAM,GAAG;AACxB,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACtC,UAAM,IAAI,KAAK,CAAC,CAAC;AACjB,QAAI,OAAO,QAAQ,YAAY,CAAC;AAC5B;AAAA,EACR;AACA,MAAI,UAAU,KAAK,IAAI;AACvB,MAAI,OAAO,IAAI,OAAO,MAAM,YAAY;AACpC,WAAO,WAAY;AACf,aAAO,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS;AAAA,IAC5C;AAAA,EACJ,OACK;AACD,WAAO,IAAI,OAAO;AAAA,EACtB;AACJ;AACA,IAAI,cAAc,SAAU,KAAK,KAAK,OAAO;AACzC,MAAI,OAAO,IAAI,MAAM,GAAG;AACxB,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACtC,WAAO,KAAK,CAAC;AACb,QAAI,CAAC,IAAI,IAAI;AACT,UAAI,IAAI,IAAI,CAAC;AACjB,UAAM,IAAI,IAAI;AAAA,EAClB;AACA,MAAI,KAAK,IAAI,CAAC,IAAI;AACtB;AACA,SAAS,gBAAgB,QAAQ,WAAW,KAAK;AAC7C,2BAAyB,MAAM,SAAS,cAAc;AAClD,WAAO,YAAY,MAAM,SAAS;AAAA,EACtC;AACA,2BAAyB,MAAM,SAAS,YAAY,KAAK;AACrD,gBAAY,MAAM,WAAW,GAAG;AAAA,EACpC;AACA,SAAO,eAAe,QAAQ,KAAK,wBAAwB;AAC/D;AAEA,IAAI;AAAA;AAAA,EAA8B,WAAY;AAC1C,aAASC,cAAa,OAAO;AACzB,WAAK,SAAS,CAAC;AACf,WAAK,aAAa,CAAC;AACnB,WAAK,aAAa,KAAK;AAAA,IAC3B;AACA,IAAAA,cAAa,UAAU,KAAK,SAAU,MAAM,IAAI,SAAS;AACrD,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAM;AAC1C,WAAK,QAAQ,IAAI;AACjB,UAAI,CAAC,KAAK,OAAO,IAAI,GAAG;AACpB,aAAK,OAAO,IAAI,IAAI,CAAC;AAAA,MACzB;AACA,WAAK,OAAO,IAAI,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC;AACpC,aAAO;AAAA,IACX;AACA,IAAAA,cAAa,UAAU,OAAO,SAAU,MAAM,IAAI,SAAS;AACvD,UAAI,QAAQ;AACZ,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAM;AAC1C,WAAK,QAAQ,IAAI;AACjB,UAAI,QAAQ,WAAY;AACpB,YAAI,OAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,eAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QAC3B;AACA,cAAM,IAAI,MAAM,KAAK;AACrB,YAAI,MAAM,GAAG,MAAM,SAAS,IAAI;AAChC,YAAI,QAAQ,MAAM;AACd,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,YAAM,KAAK;AACX,WAAK,GAAG,MAAM,KAAK;AACnB,aAAO;AAAA,IACX;AACA,IAAAA,cAAa,UAAU,MAAM,SAAU,MAAM,IAAI;AAC7C,UAAI,CAAC,QAAQ,CAAC,IAAI;AACd,aAAK,SAAS,CAAC;AACf,eAAO;AAAA,MACX;AACA,UAAI,MAAM;AACN,aAAK,QAAQ,IAAI;AACjB,YAAI,CAAC,IAAI;AACL,eAAK,OAAO,IAAI,IAAI,CAAC;AACrB,iBAAO;AAAA,QACX;AACA,YAAI,SAAS,KAAK,OAAO,IAAI;AAC7B,YAAI,CAAC,QAAQ;AACT,iBAAO;AAAA,QACX;AACA,YAAI,QAAQ,OAAO;AACnB,eAAO,SAAS;AACZ,cAAI,OAAO,KAAK,EAAE,CAAC,MAAM,MACpB,OAAO,KAAK,EAAE,CAAC,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,OAAO,IAAK;AAClD,mBAAO,OAAO,OAAO,CAAC;AAAA,UAC1B;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,cAAa,UAAU,UAAU,SAAU,MAAM;AAC7C,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,MAC/B;AACA,WAAK,QAAQ,IAAI;AACjB,UAAI,SAAS,KAAK,OAAO,IAAI;AAC7B,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AACA,UAAI,MAAM,OAAO;AACjB,UAAI,aAAa,eAAe,MAAM;AACtC,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,YAAI,UAAU,WAAW,CAAC;AAC1B,YAAI,KAAK,QAAQ,CAAC,GAAG,UAAU,QAAQ,CAAC;AACxC,YAAI,IAAI;AACJ,gBAAM,GAAG,MAAM,SAAS,IAAI;AAC5B,cAAI,QAAQ,MAAM;AACd,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,cAAa,UAAU,eAAe,SAAU,OAAO;AACnD,UAAI,QAAQ;AACZ,YAAM,QAAQ,SAAU,MAAM;AAC1B,cAAM,WAAW,IAAI,IAAI;AAAA,MAC7B,CAAC;AAAA,IACL;AACA,IAAAA,cAAa,UAAU,UAAU,WAAY;AACzC,WAAK,SAAS,CAAC;AACf,WAAK,aAAa,CAAC;AAAA,IACvB;AACA,IAAAA,cAAa,UAAU,UAAU,SAAU,MAAM;AAC7C,UAAI,QAAQ,KAAK;AACjB,UAAI,SAAS,MAAM,IAAI,MAAM;AAC7B,UAAI,CAAC,QAAQ;AACT,aAAK,gDAAiD,OAAO,0BACxD,KAAK,OAAO,KAAK,KAAK,EAAE,IAAI,SAAU,GAAG;AAAE,iBAAO,KAAK,UAAU,CAAC;AAAA,QAAG,CAAC,KACvE,GAAG;AAAA,MACX;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAA+B,WAAY;AAC3C,aAASC,eAAc,SAAS,QAAQ;AACpC,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,aAAa;AAAA,IACtB;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAC1C,WAAK,gBAAgB;AACrB,WAAK,SAAS,CAAC;AAAA,IACnB;AACA,IAAAA,eAAc,UAAU,eAAe,WAAY;AAC/C,WAAK,gBAAgB,QAAQ;AAAA,IACjC;AACA,IAAAA,eAAc,UAAU,kBAAkB,WAAY;AAClD,WAAK,gBAAgB,WAAW;AAAA,IACpC;AACA,IAAAA,eAAc,UAAU,kBAAkB,SAAU,gBAAgB;AAChE,UAAI,QAAQ;AACZ,UAAI,UAAU,KAAK;AACnB,WAAK,OAAO,QAAQ,SAAU,OAAO;AACjC,uBAAe,SAAS,MAAM,MAAM,OAAO,CAAC,CAAC,MAAM,OAAO;AAAA,MAC9D,CAAC;AAAA,IACL;AACA,IAAAA,eAAc,UAAU,cAAc,SAAU,GAAG;AAC/C,UAAI,YAAY,EAAE;AAClB,WAAK,OAAO,KAAK,SAAU,OAAO;AAC9B,YAAI,MAAM,SAAS,WAAW;AAC1B,gBAAM,QAAQ,CAAC;AACf,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAA+B,2BAAY;AAC3C,aAASC,iBAAgB;AAAA,IACzB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUC,qBAAoB,MAAM;AACpC,aAASA,sBAAqB;AAC1B,UAAI,QAAQ,OAAO,KAAK,IAAI,KAAK;AACjC,YAAM,SAAS;AACf,YAAM,SAAS;AACf,YAAM,UAAU;AAChB,YAAM,UAAU;AAChB,YAAM,aAAa;AACnB,YAAM,yBAAyB;AAC/B,YAAM,mBAAmB;AACzB,YAAM,QAAQ;AACd,YAAM,WAAW;AACjB,YAAM,MAAM;AACZ,YAAM,SAAS;AAAA,QACX,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AACA,YAAM,aAAa;AACnB,YAAM,WAAW;AACjB,YAAM,oBAAoB;AAC1B,YAAM,wBAAwB;AAC9B,YAAM,YAAY;AAClB,YAAM,kBAAkB;AACxB,YAAM,eAAe;AACrB,YAAM,iBAAiB;AACvB,YAAM,qBAAqB;AAC3B,YAAM,gBAAgB;AACtB,YAAM,YAAY;AAClB,YAAM,kBAAkB;AACxB,YAAM,iBAAiB;AACvB,YAAM,0BAA0B;AAAA,QAC5B,SAAS;AAAA,MACb;AACA,YAAM,eAAe;AAAA,QACjB,SAAS;AAAA,MACb;AACA,YAAM,gBAAgB;AACtB,YAAM,gBAAgB;AACtB,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,YAAM,eAAe;AACrB,YAAM,eAAe,CAAC;AACtB,YAAM,WAAW;AACjB,YAAM,kBAAkB;AACxB,YAAM,6BAA6B,IAAI;AACvC,YAAM,0BAA0B;AAChC,YAAM,WAAW;AACjB,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,QAAQ,SAAU,SAAS;AACpD,UAAI,CAAC;AACD,eAAO;AACX,eAAS,OAAO,SAAS;AACrB,YAAI,QAAQ,UAAU;AAClB,eAAK,SAAS,KAAK,cAAc,QAAQ,GAAG,CAAC;AAC7C;AAAA,QACJ;AACA,aAAK,GAAG,IAAI,QAAQ,GAAG;AAAA,MAC3B;AACA,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,WAAK,aACD,KAAK,iBAAiB,iBAAiB,qBAAqB;AAChE,WAAK,gBAAgB,KAAK,iBAAiB;AAC3C,WAAK,iBAAiB,CAAC,KAAK,oBAAoB,KAAK;AAErD,WAAK,UACD,KAAK,qBAAqB,eACpB,QACA,KAAK;AACf,WAAK,UACD,KAAK,qBAAqB,aAA4B,QAAQ,KAAK;AAEvE,WAAK,aAAa,KAAK,cAAc,CAAC,KAAK;AAE3C,WAAK,UAAU,KAAK,aAAa,OAAO,KAAK;AAC7C,WAAK,UAAU,KAAK,aAAa,OAAO,KAAK;AAC7C,WAAK,yBAAyB,KAAK,mBAC7B,IACA,KAAK;AACX,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,gBAAgB,SAAU,eAAe;AAClE,UAAI,iBAAiB;AAAA,QACjB,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AACA,UAAI,iBAAiB;AAAA,QACjB,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,MACV;AACA,UAAI;AACJ,UAAI,OAAO,kBAAkB,UAAU;AACnC,cAAM,OAAO,gBAAgB,aAAa;AAAA,MAC9C,OACK;AACD,cAAM,gBAAgB,iBAAiB;AAAA,MAC3C;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,aAAa;AAAA;AAEf,IAAI;AAAA;AAAA,EAAgC,WAAY;AAC5C,aAASC,gBAAe,SAAS,SAAS;AACtC,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,QAAQ,IAAI,aAAa;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,WAAK,gBAAgB;AAAA,IACzB;AACA,IAAAA,gBAAe,UAAU,kBAAkB,WAAY;AACnD,UAAIC,MAAK,KAAK,SAAS,gBAAgBA,IAAG,eAAe,eAAeA,IAAG,cAAc,eAAeA,IAAG,cAAcC,SAAQD,IAAG;AACpI,UAAI,UAAU,KAAK;AACnB,UAAI,SAAS,gBAAgB,UAAU;AACvC,UAAI,gBAAgB,CAAC;AACrB,UAAI,eAAe,CAAC;AACpB,UAAI,sBAAsB,CAAC;AAC3B,UAAI,sBAAsB,CAAC;AAC3B,UAAIC,QAAO;AACP,sBAAc,KAAK;AAAA,UACf,MAAM;AAAA,UACN,SAAS,KAAK,MAAM,KAAK,IAAI;AAAA,UAC7B,SAAS;AAAA,QACb,CAAC;AAAA,MACL;AACA,UAAI,qBAAqB;AACrB,sBAAc,KAAK;AAAA,UACf,MAAM;AAAA,UACN,SAAS,KAAK,MAAM,KAAK,IAAI;AAAA,QACjC,CAAC;AACD,qBAAa,KAAK;AAAA,UACd,MAAM;AAAA,UACN,SAAS,KAAK,KAAK,KAAK,IAAI;AAAA,QAChC,GAAG;AAAA,UACC,MAAM;AAAA,UACN,SAAS,KAAK,IAAI,KAAK,IAAI;AAAA,QAC/B,GAAG;AAAA,UACC,MAAM;AAAA,UACN,SAAS,KAAK,IAAI,KAAK,IAAI;AAAA,QAC/B,CAAC;AAAA,MACL;AACA,UAAI,qBAAqB;AACrB,sBAAc,KAAK;AAAA,UACf,MAAM;AAAA,UACN,SAAS,KAAK,MAAM,KAAK,IAAI;AAAA,QACjC,CAAC;AACD,qBAAa,KAAK;AAAA,UACd,MAAM;AAAA,UACN,SAAS,KAAK,KAAK,KAAK,IAAI;AAAA,QAChC,GAAG;AAAA,UACC,MAAM;AAAA,UACN,SAAS,KAAK,IAAI,KAAK,IAAI;AAAA,QAC/B,CAAC;AAAA,MACL;AACA,WAAK,uBAAuB,IAAI,cAAc,SAAS,aAAa;AACpE,WAAK,sBAAsB,IAAI,cAAc,QAAQ,YAAY;AAAA,IACrE;AACA,IAAAF,gBAAe,UAAU,gBAAgB,SAAU,GAAG,MAAM;AACxD,UAAIC,MAAK,KAAK,SAAS,iBAAiBA,IAAG,gBAAgB,kBAAkBA,IAAG,iBAAiB,0BAA0BA,IAAG;AAC9H,UAAI,2BAA2B;AAAA,QAC3B,OAAO,WAAY;AACf,iBAAQ,kBACJ,CAAC,0BAA0B,EAAE,QAAQ,uBAAuB;AAAA,QACpE;AAAA,QACA,KAAK,WAAY;AACb,iBAAQ,kBACJ,CAAC,0BAA0B,EAAE,QAAQ,uBAAuB;AAAA,QACpE;AAAA,QACA,MAAM,WAAY;AACd,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,yBAAyB,IAAI,EAAE,GAAG;AAClC,UAAE,eAAe;AAAA,MACrB;AACA,UAAI,iBAAiB;AACjB,UAAE,gBAAgB;AAAA,MACtB;AAAA,IACJ;AACA,IAAAD,gBAAe,UAAU,eAAe,SAAU,MAAM;AACpD,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAG;AACjC,WAAK,YAAY;AAAA,IACrB;AACA,IAAAA,gBAAe,UAAU,QAAQ,SAAU,GAAG;AAC1C,UAAI,aAAa,aAAa,EAAE,IAAI;AACpC,UAAI,KAAK,aAAa,KAAK,cAAc,YAAY;AACjD;AAAA,MACJ;AACA,WAAK,aAAa,UAAU;AAG5B,UAAI,eAAe,EAAE,QAAQ,KAAK,QAAQ,YAAY,GAAG;AACrD,aAAK,aAAa;AAClB;AAAA,MACJ;AAEA,UAAI,eAAe,KAAiB,EAAE,WAAW;AAC7C;AACJ,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,aAAa,CAAC,GAAG;AAC1D;AAAA,MACJ;AACA,WAAK,cAAc,GAAG,OAAO;AAC7B,UAAI,QAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACxC,WAAK,SAAS,MAAM;AACpB,WAAK,SAAS,MAAM;AACpB,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO,CAAC;AAAA,IACrD;AACA,IAAAA,gBAAe,UAAU,OAAO,SAAU,GAAG;AACzC,UAAI,aAAa,EAAE,IAAI,MAAM,KAAK,WAAW;AACzC;AAAA,MACJ;AACA,WAAK,cAAc,GAAG,MAAM;AAC5B,UAAI,QAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACxC,UAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,UAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,WAAK,SAAS,MAAM;AACpB,WAAK,SAAS,MAAM;AACpB,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM;AAAA,QAC/C;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC,GAAG;AACA;AAAA,MACJ;AAEA,UAAI,aAAa,SAAS,gBAAgB,cACtC,OAAO,eACP,SAAS,KAAK;AAClB,UAAI,YAAY,SAAS,gBAAgB,aACrC,OAAO,eACP,SAAS,KAAK;AAClB,UAAI,KAAK,KAAK,SAAS;AACvB,UAAI,KAAK,KAAK,SAAS;AACvB,UAAI,kBAAkB,KAAK,QAAQ;AACnC,UAAI,KAAK,SAAS,gBAAgB,cAAc,mBAC5C,KAAK,SAAS,gBAAgB,eAAe,mBAC7C,KAAK,mBACL,KAAK,iBAAiB;AACtB,aAAK,IAAI,CAAC;AAAA,MACd;AAAA,IACJ;AACA,IAAAA,gBAAe,UAAU,MAAM,SAAU,GAAG;AACxC,UAAI,aAAa,EAAE,IAAI,MAAM,KAAK,WAAW;AACzC;AAAA,MACJ;AACA,WAAK,aAAa;AAClB,WAAK,cAAc,GAAG,KAAK;AAC3B,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,CAAC;AAAA,IACnD;AACA,IAAAA,gBAAe,UAAU,QAAQ,SAAU,GAAG;AAC1C,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO,CAAC;AAAA,IACrD;AACA,IAAAA,gBAAe,UAAU,aAAa,SAAU,SAAS;AACrD,UAAI,YAAY,KAAK,SAAS;AAC1B,aAAK,UAAU;AACf,aAAK,gBAAgB;AAAA,MACzB;AAAA,IACJ;AACA,IAAAA,gBAAe,UAAU,kBAAkB,WAAY;AACnD,WAAK,qBAAqB,QAAQ;AAClC,WAAK,oBAAoB,QAAQ;AACjC,WAAK,gBAAgB;AAAA,IACzB;AACA,IAAAA,gBAAe,UAAU,UAAU,WAAY;AAC3C,WAAK,qBAAqB,QAAQ;AAClC,WAAK,oBAAoB,QAAQ;AACjC,WAAK,MAAM,QAAQ;AAAA,IACvB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI,qBAAqB;AAAA,EACrB,GAAG,CAAC,cAAc,IAAI;AAAA,EACtB,GAAG,CAAC,cAAc,IAAI;AAC1B;AACA,IAAI;AAAA;AAAA,EAA4B,WAAY;AACxC,aAASG,YAAW,SAAS;AACzB,WAAK,WAAW,OAAO;AACvB,WAAK,QAAQ,IAAI,aAAa,CAAC,mBAAmB,WAAW,CAAC;AAAA,IAClE;AACA,IAAAA,YAAW,UAAU,sBAAsB,WAAY;AACnD,UAAI,WAAW,OAAO,iBAAiB,KAAK,SAAS,IAAI;AACzD,UAAI,SAAS,SAAS,MAAM,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,IAAI;AAC/D,UAAI,IAAI,EAAE,OAAO,EAAE,KAAK,OAAO,CAAC,MAAM;AACtC,UAAI,IAAI,EAAE,OAAO,EAAE,KAAK,OAAO,CAAC,MAAM;AACtC,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,YAAY,SAAU,OAAO;AAC9C,UAAI,iBAAiB,CAAC;AACtB,aAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,KAAK;AACtC,YAAI,CAAC,mBAAmB,GAAG,GAAG;AAC1B;AAAA,QACJ;AACA,YAAI,kBAAkB,mBAAmB,GAAG,EAAE,CAAC;AAC/C,YAAI,iBAAiB;AACjB,cAAI,qBAAqB,mBAAmB,GAAG,EAAE,CAAC;AAClD,cAAI,iBAAiB,MAAM,GAAG;AAC9B,yBAAe,KAAK,kBAAkB,MAAM,iBAAiB,qBAAqB,GAAG;AAAA,QACzF;AAAA,MACJ,CAAC;AACD,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,iBAAiB,gBAAgB,KAAK;AAC/E,WAAK,MAAM,MAAM,SAAS,IAAI,eAAe,KAAK,GAAG;AACrD,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,KAAK;AAAA,IAC7D;AACA,IAAAA,YAAW,UAAU,aAAa,SAAU,SAAS;AACjD,UAAI,KAAK,YAAY,SAAS;AAC1B,aAAK,UAAU;AACf,aAAK,QAAQ,QAAQ;AAAA,MACzB;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,UAAU,WAAY;AACvC,WAAK,MAAM,QAAQ;AAAA,IACvB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAsB,WAAY;AAClC,aAASC,MAAK,SAAS,YAAY,SAAS;AACxC,WAAK,aAAa;AAClB,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,QAAQ,IAAI,aAAa;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,WAAK,WAAW,OAAO;AAAA,IAC3B;AACA,IAAAA,MAAK,UAAU,YAAY,SAAU,UAAU;AAC3C,WAAK,WAAW,UAAU,QAAQ;AAAA,IACtC;AACA,IAAAA,MAAK,UAAU,aAAa,SAAU,SAAS;AAC3C,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,MAAK,UAAU,kBAAkB,SAAU,cAAc;AACrD,WAAK,eAAe;AAAA,IACxB;AACA,IAAAA,MAAK,UAAU,cAAc,SAAU,QAAQ;AAC3C,WAAK,sBAAsB;AAAA,IAC/B;AACA,IAAAA,MAAK,UAAU,aAAa,SAAU,SAAS;AAC3C,UAAI,KAAK,YAAY,SAAS;AAC1B,aAAK,UAAU;AACf,aAAK,QAAQ,QAAQ;AACrB,aAAK,KAAK;AAAA,MACd;AAAA,IACJ;AACA,IAAAA,MAAK,UAAU,aAAa,WAAY;AACpC,UAAI,KAAK,OAAO;AACZ,6BAAqB,KAAK,KAAK;AAC/B,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,IAAAA,MAAK,UAAU,UAAU,WAAY;AACjC,WAAK,MAAM,QAAQ;AACnB,2BAAqB,KAAK,KAAK;AAAA,IACnC;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAQF,IAAI,iBAAiB,SAAU,YAAY,UAAU,YAAY,QAAQ;AACrE,MAAI,mBAAmB,SAAU,UAAU,YAAY;AACnD,QAAI,QAAQ,WAAW;AACvB,QAAI,YAAY,QAAQ,IAClB,KACA,QAAQ,IACJ,IACA;AACV,WAAO;AAAA,EACX;AACA,MAAI,aAAa,iBAAiB,SAAS,GAAG,WAAW,CAAC;AAC1D,MAAI,aAAa,iBAAiB,SAAS,GAAG,WAAW,CAAC;AAC1D,MAAI,SAAS,WAAW,IAAI,OAAO;AACnC,MAAI,SAAS,WAAW,IAAI,OAAO;AACnC,SAAO,aAAa,UAAU,KAAK,aAAa,UAAU;AAC9D;AAEA,IAAI;AAAA;AAAA,EAA4B,SAAU,QAAQ;AAC9C,cAAUC,aAAY,MAAM;AAC5B,aAASA,cAAa;AAClB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,YAAW,UAAU,aAAa,SAAU,YAAY,UAAU;AAC9D,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,QAAQ,WAAY;AACpB,YAAI,MAAM,MAAM,WAAW,oBAAoB;AAC/C,YAAI,eAAe,YAAY,UAAU,KAAK,MAAM,GAAG;AACnD,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,MAAM,GAAG;AAAA,QACxD;AAIA,YAAI,CAAC,MAAM,SAAS;AAChB,cAAI,MAAM,qBAAqB;AAC3B,kBAAM,sBAAsB;AAAA,UAChC,OACK;AAED,kBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK,GAAG;AAAA,UACvD;AAAA,QACJ;AACA,iBAAS;AACT,YAAI,MAAM,SAAS;AACf,gBAAM,QAAQ,sBAAsB,KAAK;AAAA,QAC7C;AAAA,MACJ;AAGA,UAAI,KAAK,qBAAqB;AAC1B,aAAK,YAAY,KAAK;AAAA,MAC1B;AACA,2BAAqB,KAAK,KAAK;AAC/B,YAAM;AAAA,IACV;AACA,IAAAA,YAAW,UAAU,iBAAiB,SAAU,MAAM;AAClD,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAG;AACjC,WAAK,MAAM,MAAM,kBAAkB,IAAI,OAAO;AAC9C,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM,IAAI;AAAA,IACvD;AACA,IAAAA,YAAW,UAAU,2BAA2B,SAAU,QAAQ;AAC9D,WAAK,MAAM,MAAM,wBAAwB,IAAI;AAC7C,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,cAAc,MAAM;AAAA,IACjE;AACA,IAAAA,YAAW,UAAU,qBAAqB,WAAY;AAClD,WAAK,MAAM,MAAM,kBAAkB,IAAI,MAAM;AAAA,IACjD;AACA,IAAAA,YAAW,UAAU,OAAO,SAAU,YAAY,UAAU,MAAM,UAAU;AACxE,WAAK,WAAW,OAAO,CAAC;AACxB,WAAK,yBAAyB,QAAQ;AACtC,WAAK,mBAAmB;AACxB,WAAK,eAAe,IAAI;AACxB,WAAK,UAAU,QAAQ;AACvB,UAAI,sBAAsB,KAAK,QAAQ,cAAc;AACrD,UAAI,QAAQ,qBAAqB;AAC7B,aAAK,WAAW,YAAY,QAAQ;AAAA,MACxC;AAKA,UAAI,CAAC,MAAM;AACP,aAAK,UAAU,KAAK,QAAQ;AAC5B,YAAI,qBAAqB;AACrB,eAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM,QAAQ;AAAA,QAC3D;AACA,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,QAAQ;AAAA,MAC1D;AAAA,IACJ;AACA,IAAAA,YAAW,UAAU,SAAS,WAAY;AACtC,UAAI,UAAU,KAAK;AACnB,WAAK,gBAAgB,KAAK;AAC1B,WAAK,YAAY,KAAK;AAEtB,UAAI,SAAS;AACT,aAAK,WAAW,KAAK;AACrB,6BAAqB,KAAK,KAAK;AAC/B,YAAIJ,MAAK,KAAK,WAAW,oBAAoB,GAAG,IAAIA,IAAG,GAAG,IAAIA,IAAG;AACjE,aAAK,eAAe;AACpB,aAAK,UAAU,EAAE,GAAM,EAAK,CAAC;AAC7B,aAAK,gBAAgB,IAAI;AACzB,aAAK,YAAY,IAAI;AACrB,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,EAAE,GAAM,EAAK,CAAC;AAAA,MACtE;AACA,aAAO;AAAA,IACX;AACA,IAAAI,YAAW,UAAU,OAAO,WAAY;AACpC,UAAI,qBAAqB,KAAK,OAAO;AACrC,UAAI,oBAAoB;AACpB,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,QAAQ;AAAA,MACrD;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE,IAAI;AAAA;AAEN,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,aAAY;AACjB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC/D;AACA,IAAAA,WAAU,UAAU,OAAO,SAAU,YAAY,UAAU,MAAM,UAAU;AAEvE,UAAI,CAAC,MAAM;AACP,aAAK,UAAU,QAAQ;AACvB,YAAI,KAAK,QAAQ,cAAc,GAAkB;AAC7C,eAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM,QAAQ;AAAA,QAC3D;AACA,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,QAAQ;AACtD;AAAA,MACJ;AACA,WAAK,QAAQ,YAAY,UAAU,MAAM,QAAQ;AAAA,IACrD;AACA,IAAAA,WAAU,UAAU,UAAU,SAAU,YAAY,UAAU,UAAU,UAAU;AAC9E,UAAI,QAAQ;AACZ,UAAI,YAAY,OAAO;AACvB,UAAI,WAAW,YAAY;AAC3B,UAAI,sBAAsB,KAAK,QAAQ,cAAc;AACrD,UAAI,OAAO,WAAY;AACnB,YAAI,MAAM,OAAO;AAEjB,YAAI,OAAO,UAAU;AACjB,gBAAM,UAAU,QAAQ;AACxB,cAAI,qBAAqB;AACrB,kBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,MAAM,QAAQ;AAAA,UAC7D;AACA,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK,QAAQ;AACxD;AAAA,QACJ;AACA,eAAO,MAAM,aAAa;AAC1B,YAAI,SAAS,SAAS,GAAG;AACzB,YAAI,WAAW,CAAC;AAChB,eAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,KAAK;AACzC,cAAI,aAAa,WAAW,GAAG;AAC/B,cAAI,WAAW,SAAS,GAAG;AAC3B,mBAAS,GAAG,KAAK,WAAW,cAAc,SAAS;AAAA,QACvD,CAAC;AACD,cAAM,UAAU,QAAQ;AACxB,YAAI,qBAAqB;AACrB,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,MAAM,QAAQ;AAAA,QAC7D;AACA,YAAI,MAAM,SAAS;AACf,gBAAM,QAAQ,sBAAsB,IAAI;AAAA,QAC5C;AAIA,YAAI,CAAC,MAAM,SAAS;AAChB,cAAI,MAAM,qBAAqB;AAC3B,kBAAM,sBAAsB;AAAA,UAChC,OACK;AAED,kBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK,QAAQ;AAAA,UAC5D;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,WAAW,IAAI;AAGpB,UAAI,KAAK,qBAAqB;AAC1B,aAAK,YAAY,KAAK;AAAA,MAC1B;AACA,2BAAqB,KAAK,KAAK;AAC/B,WAAK;AAAA,IACT;AACA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,UAAI,UAAU,KAAK;AACnB,WAAK,gBAAgB,KAAK;AAC1B,WAAK,YAAY,KAAK;AAEtB,UAAI,SAAS;AACT,aAAK,WAAW,KAAK;AACrB,6BAAqB,KAAK,KAAK;AAC/B,YAAI,MAAM,KAAK,WAAW,oBAAoB;AAC9C,aAAK,gBAAgB,IAAI;AACzB,aAAK,YAAY,IAAI;AACrB,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,GAAG;AAAA,MAC3D;AACA,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,OAAO,WAAY;AACnC,UAAI,oBAAoB,KAAK,OAAO;AACpC,UAAI,mBAAmB;AACnB,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,QAAQ;AAAA,MACrD;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE,IAAI;AAAA;AAEN,SAAS,eAAe,SAAS,YAAY,SAAS;AAClD,MAAI,gBAAgB,QAAQ;AAC5B,MAAI,kBAAkB,CAAC;AACvB,SAAO,eAAe,iBAAiB,aAAa;AAAA,IAChD,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,KAAK,WAAY;AACb,aAAO,QAAQ;AAAA,IACnB;AAAA,EACJ,CAAC;AACD,MAAI,eAAe;AACf,WAAO,IAAI,WAAW,SAAS,YAAY,eAAe;AAAA,EAC9D,OACK;AACD,WAAO,IAAI,UAAU,SAAS,YAAY,eAAe;AAAA,EAC7D;AACJ;AAEA,IAAI;AAAA;AAAA,EAA0B,WAAY;AACtC,aAASC,UAAS,SAAS,SAAS,SAAS;AACzC,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,QAAQ,IAAI,aAAa;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,WAAK,QAAQ,OAAO;AAAA,IACxB;AACA,IAAAA,UAAS,UAAU,QAAQ,WAAY;AACnC,WAAK,OAAO;AACZ,WAAK;AAAA,QAAmB;AAAA;AAAA,MAAe;AACvC,WAAK;AAAA,QAAa;AAAA;AAAA,MAAe;AAAA,IACrC;AACA,IAAAA,UAAS,UAAU,OAAO,SAAU,OAAO;AACvC,cAAQ,KAAK,YAAY,QAAQ;AACjC,WAAK,mBAAmB,KAAK;AAC7B,aAAO,KAAK,wBAAwB,OAAO,KAAK,QAAQ,0BAA0B;AAAA,IACtF;AACA,IAAAA,UAAS,UAAU,qBAAqB,SAAU,OAAO;AACrD,WAAK,kBACD,QAAQ,IACF,KACA,QAAQ,IACJ,IACA;AAAA,IAClB;AACA,IAAAA,UAAS,UAAU,eAAe,SAAU,OAAO;AAC/C,WAAK,YACD,QAAQ,IACF,KACA,QAAQ,IACJ,IACA;AAAA,IAClB;AACA,IAAAA,UAAS,UAAU,0BAA0B,SAAU,OAAO,eAAe;AACzE,UAAI,SAAS,KAAK,aAAa;AAE/B,UAAI,SAAS,KAAK,gBAAgB,SAAS,KAAK,cAAc;AAC1D,YAAK,SAAS,KAAK,gBAAgB,KAAK,QAAQ,QAAQ,CAAC,KACpD,SAAS,KAAK,gBAAgB,KAAK,QAAQ,QAAQ,CAAC,GAAI;AACzD,mBAAS,KAAK,aAAa,QAAQ;AAAA,QACvC,OACK;AACD,mBACI,SAAS,KAAK,eAAe,KAAK,eAAe,KAAK;AAAA,QAC9D;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,MAAM,SAAU,UAAU;AACzC,UAAI,eAAe;AAAA,QACf,UAAU;AAAA,MACd;AACA,UAAI,UAAU,KAAK,IAAI,KAAK,aAAa,KAAK,QAAQ;AAEtD,UAAI,KAAK,QAAQ,YACb,WAAW,KAAK,QAAQ,qBACxB,UAAU,KAAK,QAAQ,uBAAuB;AAC9C,YAAI,cAAe,KAAK,cAAc,MAAqB,KAAK,QAAQ,QAAQ,CAAC,KAC5E,KAAK,cAAc,KAAoB,KAAK,QAAQ,QAAQ,CAAC,IAC5D,KAAK,cACL;AACN,uBAAe,KAAK,YACd,KAAK,SAAS,KAAK,YAAY,KAAK,UAAU,UAAU,KAAK,cAAc,KAAK,cAAc,aAAa,KAAK,OAAO,IACvH,EAAE,aAAa,KAAK,YAAY,UAAU,EAAE;AAAA,MACtD,OACK;AACD,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,YAAY;AAAA,MAC9D;AACA,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,WAAW,SAAU,SAAS,OAAO,MAAM,aAAa,aAAa,aAAa,SAAS;AAC1G,UAAI,YAAY,QAAQ;AAAE,kBAAU,KAAK;AAAA,MAAS;AAClD,UAAI,WAAW,UAAU;AACzB,UAAI,QAAQ,KAAK,IAAI,QAAQ,IAAI;AACjC,UAAI,eAAe,QAAQ,cAAc,kBAAkB,QAAQ,iBAAiB,YAAY,QAAQ;AACxG,UAAI,WAAW,KAAK,IAAI,WAAY,QAAQ,IAAK,YAAY;AAC7D,UAAI,eAAe;AAAA,QACf,aAAa,UAAY,QAAQ,QAAS,gBAAiB,WAAW,IAAI,KAAK;AAAA,QAC/E;AAAA,QACA,MAAM;AAAA,MACV;AACA,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,UAAU,cAAc,QAAQ;AACzE,UAAI,aAAa,cAAc,aAAa;AACxC,qBAAa,cAAc,cACrB,KAAK,IAAI,cAAc,cAAc,GAAG,cAAe,cAAc,aAAa,OAAQ,KAAK,IAC/F;AACN,qBAAa,WAAW;AAAA,MAC5B,WACS,aAAa,cAAc,aAAa;AAC7C,qBAAa,cAAc,cACrB,KAAK,IAAI,cAAc,cAAc,GAAG,cAAe,cAAc,aAAa,OAAQ,KAAK,IAC/F;AACN,qBAAa,WAAW;AAAA,MAC5B;AACA,mBAAa,cAAc,KAAK,MAAM,aAAa,WAAW;AAC9D,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,kBAAkB,WAAY;AAC7C,UAAI,UAAU,KAAK,aAAa,KAAK;AACrC,WAAK,aAAa,OAAO;AAAA,IAC7B;AACA,IAAAA,UAAS,UAAU,UAAU,SAAU,SAAS;AAC5C,UAAIN,MAAK,KAAK,QAAQ,MAAM,OAAOA,IAAG,MAAM,WAAWA,IAAG;AAC1D,UAAI,kBAAkB,OAAO,iBAAiB,KAAK,SAAS,IAAI,EAAE,aAAa;AAE/E,UAAI,cAAc,QAAQ,KAAK,OAAO;AAEtC,WAAK,cAAc,KAAK,QAAQ,SAAS,UAAU,gBAAgB,cAAc;AACjF,WAAK,WAAW,OAAO;AACvB,UAAI,cAAc,QAAQ,KAAK,OAAO;AACtC,WAAK,cAAc,YAAY,IAAI;AACnC,WAAK,iBAAiB,YAAY,QAAQ;AAE1C,UAAI,iBAAiB;AACjB,aAAK,kBAAkB,YAAY,QAAQ;AAAA,MAC/C;AACA,WAAK,gBAAgB;AACrB,WAAK;AAAA,QAAa;AAAA;AAAA,MAAe;AAAA,IACrC;AACA,IAAAM,UAAS,UAAU,aAAa,SAAU,SAAS;AAC/C,UAAI,YAAY,KAAK,SAAS;AAC1B,aAAK,UAAU;AACf,aAAK,WAAW;AAAA,MACpB;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,aAAa,WAAY;AACxC,WAAK,aAAa;AAClB,WAAK,WAAW;AAChB,WAAK,OAAO;AACZ,WAAK;AAAA,QAAa;AAAA;AAAA,MAAe;AACjC,WAAK;AAAA,QAAmB;AAAA;AAAA,MAAe;AACvC,WAAK,cAAc;AAAA,IACvB;AACA,IAAAA,UAAS,UAAU,kBAAkB,WAAY;AAC7C,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,qBAAqB;AAC9D,UAAI,WAAW;AAAA,QACX,cAAc;AAAA,QACd,cAAc,KAAK,cAAc,KAAK;AAAA,MAC1C;AACA,UAAI,SAAS,eAAe,GAAG;AAC3B,iBAAS,gBAAgB,KAAK;AAC9B,YAAI,KAAK,QAAQ,4BAA4B,GAAG;AAC5C,mBAAS,eAAe,CAAC,KAAK;AAAA,QAClC;AAAA,MACJ;AACA,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,iBAAiB,QAAQ;AAClE,WAAK,eAAe,SAAS;AAC7B,WAAK,eAAe,SAAS;AAC7B,WAAK,YACD,KAAK,QAAQ,cAAc,KAAK,eAAe,KAAK;AACxD,UAAI,CAAC,KAAK,aAAa,KAAK,eAAe,KAAK,cAAc;AAC1D,aAAK,eAAe,KAAK;AACzB,aAAK,cAAc,KAAK;AAAA,MAC5B;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,iBAAiB,SAAU,KAAK;AAC/C,WAAK,aAAa;AAAA,IACtB;AACA,IAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC3C,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,UAAS,UAAU,kBAAkB,WAAY;AAC7C,UAAI,WAAW,KAAK,eAAe,KAAK,UAAU;AAClD,UAAI,aAAa,aAAa,KAAK,cAAc;AACjD,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,IAAAA,UAAS,UAAU,iBAAiB,SAAU,KAAK;AAC/C,UAAI,CAAC,KAAK,aACN,CAAC,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,eAAe,GAAG;AAC5D,cAAM,KAAK;AAAA,MACf,WACS,MAAM,KAAK,cAAc;AAC9B,cAAM,KAAK;AAAA,MACf,WACS,MAAM,KAAK,cAAc;AAC9B,cAAM,KAAK;AAAA,MACf;AACA,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,iBAAiB,WAAY;AAC5C,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,IAAAA,UAAS,UAAU,oBAAoB,WAAY;AAC/C,WAAK,cAAc,KAAK;AAAA,IAC5B;AACA,IAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC3C,WAAK,eAAe;AACpB,WAAK,kBAAkB;AAAA,IAC3B;AACA,IAAAA,UAAS,UAAU,aAAa,SAAU,OAAO;AAC7C,WAAK,QAAQ;AACb,aAAO,KAAK,IAAI,KAAK,IAAI;AAAA,IAC7B;AACA,IAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,WAAK,MAAM,QAAQ;AAAA,IACvB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAJ,IAAQ;AAAR,IAAY;AAAZ,IAAgB;AAChB,IAAI,uBAAuB,KAAK,CAAC,GAC7B;AAAA,EAAG;AAAA;AAAe,IAAI,SAAU,GAAG;AAC/B,SAAO;AACX,GACA;AAAA,EAAG;AAAA;AAAa,IAAI,SAAU,GAAG;AAC7B,eAAa,CAAC;AACd,SAAO;AACX,GACA;AACJ,IAAI,gBAAgB,KAAK,CAAC,GACtB;AAAA,EAAG;AAAA;AAA6B,KAAK,KAAK,CAAC,GACvC;AAAA,EAAG;AAAA;AAAe,IAAI,cACtB;AAAA,EAAG;AAAA;AAAa,IAAI,YACpB,KACJ;AAAA,EAAG;AAAA;AAAyB,KAAK,KAAK,CAAC,GACnC;AAAA,EAAG;AAAA;AAAe,IAAI,YACtB;AAAA,EAAG;AAAA;AAAa,IAAI,cACpB,KACJ;AACJ,IAAI;AAAA;AAAA,EAAqC,WAAY;AACjD,aAASC,qBAAoB,wBAAwB,YAAY,kBAAkB;AAC/E,WAAK,yBAAyB;AAC9B,WAAK,aAAa;AAClB,WAAK,mBAAmB;AACxB,WAAK,MAAM;AAAA,IACf;AACA,IAAAA,qBAAoB,UAAU,QAAQ,WAAY;AAC9C,WAAK,kBAAkB;AAAA,IAC3B;AACA,IAAAA,qBAAoB,UAAU,uBAAuB,SAAU,UAAU,UAAU,GAAG;AAClF,WAAK,qBAAqB,UAAU,QAAQ;AAC5C,aAAO,KAAK,uBAAuB,CAAC;AAAA,IACxC;AACA,IAAAA,qBAAoB,UAAU,cAAc,SAAU,QAAQ,QAAQ;AAClE,UAAI,KAAK,oBAAoB,cAA+B;AACxD,iBAAS;AAAA,MACb,WACS,KAAK,oBAAoB,YAA2B;AACzD,iBAAS;AAAA,MACb;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,qBAAoB,UAAU,uBAAuB,SAAU,UAAU,UAAU;AAE/E,UAAI,KAAK,oBAAoB,MAAoB,CAAC,KAAK,YAAY;AAC/D,YAAI,WAAW,WAAW,KAAK,wBAAwB;AACnD,eAAK,kBAAkB;AAAA,QAC3B,WACS,YAAY,WAAW,KAAK,wBAAwB;AACzD,eAAK,kBAAkB;AAAA,QAC3B,OACK;AACD,eAAK,kBAAkB;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,qBAAoB,UAAU,yBAAyB,SAAU,GAAG;AAChE,UAAI,YAAY,aAAa,KAAK,eAAe;AACjD,UAAI,WAAW;AACX,YAAI,KAAK,qBAAqB;AAAA,UAAU;AAAA;AAAA,QAAe,GAAG;AACtD,iBAAO;AAAA,YAAoB;AAAA;AAAA,UAAe,EAAE,CAAC;AAAA,QACjD,WACS,KAAK,qBAAqB;AAAA,UAAU;AAAA;AAAA,QAAa,GAAG;AACzD,iBAAO;AAAA,YAAoB;AAAA;AAAA,UAAa,EAAE,CAAC;AAAA,QAC/C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI,8BAA8B,SAAU,QAAQ,QAAQ,UAAU;AAClE,MAAI,aAAa,GAAgB;AAC7B,WAAO,CAAC,QAAQ,CAAC,MAAM;AAAA,EAC3B,WACS,aAAa,GAAe;AACjC,WAAO,CAAC,CAAC,QAAQ,CAAC,MAAM;AAAA,EAC5B,WACS,aAAa,GAAe;AACjC,WAAO,CAAC,CAAC,QAAQ,MAAM;AAAA,EAC3B,OACK;AACD,WAAO,CAAC,QAAQ,MAAM;AAAA,EAC1B;AACJ;AACA,IAAI;AAAA;AAAA,EAAiC,WAAY;AAC7C,aAASC,iBAAgB,iBAAiB,iBAAiB,gBAAgB,UAAU,SAAS;AAC1F,WAAK,QAAQ,IAAI,aAAa;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,WAAK,kBAAkB;AACvB,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AACtB,WAAK,WAAW;AAChB,WAAK,UAAU;AACf,WAAK,sBAAsB,IAAI,oBAAoB,QAAQ,wBAAwB,QAAQ,YAAY,QAAQ,gBAAgB;AAC/H,WAAK,UAAU;AACf,WAAK,mBAAmB;AAAA,IAC5B;AACA,IAAAA,iBAAgB,UAAU,qBAAqB,WAAY;AACvD,UAAI,QAAQ;AAEZ,WAAK,eAAe,MAAM,GAAG,KAAK,eAAe,MAAM,WAAW,OAAO,SAAU,GAAG;AAClF,YAAI,CAAC,MAAM;AACP,iBAAO;AACX,eAAO,MAAM,YAAY,CAAC;AAAA,MAC9B,CAAC;AAED,WAAK,eAAe,MAAM,GAAG,KAAK,eAAe,MAAM,WAAW,MAAM,SAAUR,KAAI;AAClF,YAAI,SAASA,IAAG,QAAQ,SAASA,IAAG,QAAQ,IAAIA,IAAG;AACnD,YAAI,CAAC,MAAM;AACP,iBAAO;AACX,YAAIS,MAAK,4BAA4B,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,GAAG,qBAAqBA,IAAG,CAAC,GAAG,qBAAqBA,IAAG,CAAC;AACnI,YAAI,wBAAwB;AAAA,UACxB,QAAQ;AAAA,UACR,QAAQ;AAAA,QACZ;AACA,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,0BAA0B,qBAAqB;AAC1F,eAAO,MAAM,WAAW,sBAAsB,QAAQ,sBAAsB,QAAQ,CAAC;AAAA,MACzF,CAAC;AAED,WAAK,eAAe,MAAM,GAAG,KAAK,eAAe,MAAM,WAAW,KAAK,SAAU,GAAG;AAChF,YAAI,CAAC,MAAM;AACP,iBAAO;AACX,eAAO,MAAM,UAAU,CAAC;AAAA,MAC5B,CAAC;AAED,WAAK,eAAe,MAAM,GAAG,KAAK,eAAe,MAAM,WAAW,OAAO,SAAU,GAAG;AAElF,YAAI,MAAM,WAAW,CAAC,EAAE,cAAc;AAClC,gBAAM,YAAY,CAAC;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAD,iBAAgB,UAAU,cAAc,SAAU,GAAG;AACjD,UAAI,YAAY,OAAO;AACvB,WAAK,cAAc;AACnB,WAAK,eAAe;AACpB,WAAK,YAAY;AACjB,WAAK,oBAAoB,MAAM;AAC/B,WAAK,gBAAgB,MAAM;AAC3B,WAAK,gBAAgB,MAAM;AAE3B,WAAK,SAAS,OAAO;AACrB,WAAK,gBAAgB,cAAc;AACnC,WAAK,gBAAgB,cAAc;AACnC,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO,CAAC;AAAA,IACrD;AACA,IAAAA,iBAAgB,UAAU,aAAa,SAAU,QAAQ,QAAQ,GAAG;AAChE,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,YAAY,CAAC,GAAG;AACzD;AAAA,MACJ;AACA,UAAI,WAAW,KAAK,gBAAgB,WAAW,MAAM;AACrD,UAAI,WAAW,KAAK,gBAAgB,WAAW,MAAM;AACrD,UAAI,YAAY,OAAO;AAGvB,UAAI,KAAK,cAAc,UAAU,UAAU,SAAS,GAAG;AACnD,eAAO;AAAA,MACX;AACA,UAAI,KAAK,oBAAoB,qBAAqB,UAAU,UAAU,CAAC,GAAG;AACtE,aAAK,eAAe,aAAa;AACjC,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,KAAK,oBAAoB,YAAY,QAAQ,MAAM;AAC/D,UAAI,QAAQ,KAAK,gBAAgB,cAAc;AAC/C,UAAI,OAAO,KAAK,gBAAgB,KAAK,MAAM,MAAM;AACjD,UAAI,QAAQ,KAAK,gBAAgB,cAAc;AAC/C,UAAI,OAAO,KAAK,gBAAgB,KAAK,MAAM,MAAM;AACjD,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,qBAAqB,GAAG;AACjE;AAAA,MACJ;AACA,UAAI,CAAC,KAAK,aAAa;AACnB,aAAK,cAAc;AAAA,MACvB;AACA,UAAI,kBAAkB,SAAS,SAAS,SAAS;AACjD,UAAI,CAAC,KAAK,gBAAgB,CAAC,iBAAiB;AACxC,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,eAAe;AAAA,MAC5D;AACA,UAAI,CAAC,KAAK,gBAAgB,iBAAiB;AACvC,aAAK,eAAe;AACpB,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW;AAAA,MACxD;AACA,UAAI,KAAK,gBAAgB,iBAAiB;AACtC,aAAK,SAAS,UAAU;AAAA,UACpB,GAAG;AAAA,UACH,GAAG;AAAA,QACP,CAAC;AACD,aAAK,eAAe,SAAS;AAAA,MACjC;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,iBAAiB,SAAU,WAAW;AAE5D,UAAI,YAAY,KAAK,YAAY,KAAK,QAAQ,mBAAmB;AAE7D,aAAK,YAAY;AACjB,aAAK,gBAAgB,eAAe;AACpC,aAAK,gBAAgB,eAAe;AACpC,YAAI,KAAK,QAAQ,cAAc,GAAkB;AAC7C,eAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,QAAQ,KAAK,cAAc,CAAC;AAAA,QACzE;AAAA,MACJ;AAEA,UAAI,KAAK,QAAQ,YAAY,GAAkB;AAC3C,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,QAAQ,KAAK,cAAc,CAAC;AAAA,MACzE;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,gBAAgB,SAAU,UAAU,UAAU,WAAW;AAC/E,aAAQ,YAAY,KAAK,UAAU,KAAK,QAAQ,qBAC5C,WAAW,KAAK,QAAQ,yBACxB,WAAW,KAAK,QAAQ;AAAA,IAChC;AACA,IAAAA,iBAAgB,UAAU,YAAY,SAAU,GAAG;AAC/C,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,CAAC,GAAG;AACxD;AAAA,MACJ;AACA,UAAI,aAAa,KAAK,cAAc;AACpC,WAAK,gBAAgB,gBAAgB;AACrC,WAAK,gBAAgB,gBAAgB;AACrC,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,GAAG,UAAU,GAAG;AAC9D,eAAO;AAAA,MACX;AACA,mBAAa,KAAK,iBAAiB,UAAU;AAC7C,WAAK,SAAS,UAAU,UAAU;AAClC,WAAK,UAAU,OAAO;AACtB,UAAI,WAAW,KAAK,UAAU,KAAK;AACnC,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,YAAY,QAAQ;AAAA,IAC5E;AACA,IAAAA,iBAAgB,UAAU,mBAAmB,SAAU,YAAY;AAC/D,WAAK,kBAAkB;AACvB,UAAI,IAAI,WAAW,GAAG,IAAI,WAAW;AACrC,UAAIR,MAAK,KAAK,iBAAiB,gBAAgBA,IAAG,cAAc,gBAAgBA,IAAG;AACnF,UAAIS,MAAK,KAAK,iBAAiB,gBAAgBA,IAAG,cAAc,gBAAgBA,IAAG;AACnF,UAAI,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC;AACvC,UAAI,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC;AACvC,UAAI,QAAQ,GAAG,eAAe,aAAa;AAC3C,UAAI,QAAQ,GAAG,eAAe,aAAa;AAC3C,aAAO,EAAE,GAAM,EAAK;AAAA,IACxB;AACA,IAAAD,iBAAgB,UAAU,cAAc,SAAU,GAAG;AACjD,UAAI,CAAC,0BAA0B,EAAE,QAAQ,KAAK,QAAQ,uBAAuB,GAAG;AAC5E,qBAAa,CAAC;AACd,UAAE,gBAAgB;AAAA,MACtB;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,gBAAgB,WAAY;AAClD,aAAO;AAAA,QACH,GAAG,KAAK,gBAAgB,cAAc;AAAA,QACtC,GAAG,KAAK,gBAAgB,cAAc;AAAA,MAC1C;AAAA,IACJ;AACA,IAAAA,iBAAgB,UAAU,UAAU,WAAY;AAC5C,WAAK,UAAU;AAAA,IACnB;AACA,IAAAA,iBAAgB,UAAU,UAAU,WAAY;AAC5C,WAAK,MAAM,QAAQ;AAAA,IACvB;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,SAAS,4BAA4B,WAAW;AAC5C,MAAI,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,OAAO,SAAU,MAAM,KAAK;AAC1B,SAAK,GAAG,IAAI,UAAU,GAAG;AACzB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AACA,SAAS,sBAAsB,WAAW,WAAW,SAAS,MAAM;AAChE,MAAI,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,OAAO,SAAU,MAAM,KAAK;AAC1B,SAAK,GAAG,IAAI,UAAU,GAAG;AACzB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AAEL,UAAQ,aAAa,CAAC,CAAC,UAAU,SAAS;AAC1C,UAAQ,UAAU;AAClB,UAAQ,OAAO;AACf,SAAO;AACX;AAEA,SAAS,SAAS,QAAQ,QAAQ,QAAQ;AACtC,SAAO,QAAQ,SAAU,OAAO;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,UAAU,UAAU;AAC3B,oBAAc,cAAc;AAAA,IAChC,OACK;AACD,oBAAc,MAAM;AACpB,oBAAc,MAAM;AAAA,IACxB;AACA,WAAO,GAAG,aAAa,WAAY;AAC/B,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,OAAO,QAAQ,MAAM,QAAQ,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC;AAAA,IAC3E,CAAC;AAAA,EACL,CAAC;AACL;AAEA,SAAS,YAAY,YAAY,UAAU;AAEvC,MAAI,OAAO,OAAO,KAAK,UAAU;AACjC,WAAS,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,QAAQ,MAAM;AACtD,QAAI,MAAM,OAAO,EAAE;AACnB,QAAI,WAAW,GAAG,MAAM,SAAS,GAAG;AAChC,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAEA,IAAI,sBAAsB;AAC1B,IAAI;AAAA;AAAA,EAA0B,WAAY;AACtC,aAASE,UAAS,SAAS,SAAS,SAAS;AACzC,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,gBAAgB;AACrB,WAAK,QAAQ,IAAI,aAAa;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,WAAK,UAAU;AACf,UAAIV,MAAK,KAAK,QAAQ,QAAQ,OAAOA,IAAG,MAAM,QAAQA,IAAG,OAAO,MAAMA,IAAG,KAAK,SAASA,IAAG;AAE1F,WAAK,kBAAkB,IAAI,SAAS,SAAS,SAAS,sBAAsB,SAAS,WAAW,CAAC,MAAM,KAAK,GAAG;AAAA,QAC3G,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAC,CAAC;AAEF,WAAK,kBAAkB,IAAI,SAAS,SAAS,SAAS,sBAAsB,SAAS,WAAW,CAAC,KAAK,MAAM,GAAG;AAAA,QAC3G,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAC,CAAC;AACF,WAAK,aAAa,IAAI,WAAW,KAAK,OAAO;AAC7C,WAAK,WAAW,eAAe,KAAK,SAAS,KAAK,YAAY,KAAK,OAAO;AAC1E,WAAK,iBAAiB,IAAI,eAAe,KAAK,QAAQ,eAAe,KAAK,UAAU,SAAS,4BAA4B,KAAK,OAAO,CAAC;AACtI,WAAK,UAAU,IAAI,gBAAgB,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,gBAAgB,KAAK,UAAU,KAAK,OAAO;AAC/H,UAAI,gBAAgB,KAAK,OAAO,KAAK,IAAI;AACzC,WAAK,iBAAiB,IAAI,cAAc,QAAQ;AAAA,QAC5C;AAAA,UACI,MAAM;AAAA,UACN,SAAS;AAAA,QACb;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,SAAS;AAAA,QACb;AAAA,MACJ,CAAC;AACD,WAAK,sBAAsB;AAC3B,WAAK,KAAK;AAAA,IACd;AACA,IAAAU,UAAS,UAAU,OAAO,WAAY;AAClC,UAAI,QAAQ;AACZ,WAAK,eAAe;AACpB,WAAK,aAAa;AAClB,WAAK,YAAY;AAEjB,WAAK,MAAM,GAAG,KAAK,MAAM,WAAW,WAAW,WAAY;AACvD,cAAM,oBAAoB,IAAI;AAAA,MAClC,CAAC;AAAA,IACL;AACA,IAAAA,UAAS,UAAU,wBAAwB,WAAY;AACnD,WAAK,wBAAwB,IAAI,cAAc,KAAK,SAAS;AAAA,QACzD;AAAA,UACI,MAAM,MAAM;AAAA,UACZ,SAAS,KAAK,cAAc,KAAK,IAAI;AAAA,QACzC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,UAAS,UAAU,iBAAiB,WAAY;AAC5C,UAAI,QAAQ;AACZ,UAAI,QAAQ,KAAK,WAAW;AAC5B,YAAM,GAAG,MAAM,WAAW,iBAAiB,SAAU,gBAAgB;AACjE,YAAI,MAAM,QAAQ,YAAY;AAC1B,yBAAe,KAAK,MAAM,QAAQ,UAAU;AAAA,QAChD;AAAA,MACJ,CAAC;AAED,YAAM,GAAG,MAAM,WAAW,WAAW,SAAU,KAAK;AAChD,YAAI,UAAU,MAAM,cAAc;AAClC,cAAM,gBAAgB,GAAG;AAGzB,YAAI,MAAM,QAAQ,oBAAoB,MAAM;AACxC,gBAAM,QAAQ,kBAAkB;AAChC;AAAA,QACJ;AAEA,YAAI,IAAI,MAAM,QAAQ,KAAK,IAAI,MAAM,QAAQ,GAAG;AAC5C,gBAAM,oBAAoB,KAAK;AAAA,QACnC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,UAAS,UAAU,eAAe,WAAY;AAC1C,UAAI,QAAQ;AAEZ,WAAK,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,WAAW,KAAK,SAAU,KAAK;AACtE,YAAI,CAAC,MAAM,cAAc,MAAM,QAAQ,UAAU,GAAG;AAChD,gBAAM,SAAS,WAAW,KAAK;AAC/B,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,WAAW,GAAG;AAAA,QAC7D;AAAA,MACJ,CAAC;AACD,eAAS,KAAK,SAAS,OAAO,KAAK,OAAO;AAAA,QACtC;AAAA,UACI,QAAQ,KAAK,SAAS,MAAM,WAAW;AAAA,UACvC,QAAQ,KAAK,MAAM,WAAW;AAAA,QAClC;AAAA,QACA;AAAA,UACI,QAAQ,KAAK,SAAS,MAAM,WAAW;AAAA,UACvC,QAAQ,KAAK,MAAM,WAAW;AAAA,QAClC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,UAAS,UAAU,cAAc,WAAY;AACzC,UAAI,QAAQ;AACZ,UAAI,UAAU,KAAK;AACnB,eAAS,QAAQ,OAAO,KAAK,OAAO;AAAA,QAChC;AAAA,UACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,UACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,QAClC;AAAA,QACA;AAAA,UACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,UACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,QAClC;AAAA,QACA;AAAA,UACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,UACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,QAClC;AAAA,QACA;AAAA,UACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,UACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,QAClC;AAAA,QACA;AAAA,UACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,UACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,QAClC;AAAA,QACA;AAAA,UACI,QAAQ,QAAQ,MAAM,WAAW;AAAA,UACjC,QAAQ,KAAK,MAAM,WAAW;AAAA,QAClC;AAAA,MACJ,CAAC;AACD,cAAQ,MAAM,GAAG,QAAQ,MAAM,WAAW,KAAK,SAAU,GAAG,KAAK;AAC7D,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,UAAU,GAAG;AACxD,YAAI,MAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK,GAAG,GAAG;AACtD,iBAAO;AAAA,QACX;AAEA,YAAI,CAAC,QAAQ,aAAa;AACtB,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,YAAY;AACvD,cAAI,MAAM,WAAW,CAAC,GAAG;AACrB,mBAAO;AAAA,UACX;AAAA,QACJ;AAEA,YAAI,MAAM,cAAc,MAAM,QAAQ,YAAY,KAAK,MAAM,GAAG;AAC5D,gBAAM,SAAS,gBAAgB,KAAK;AACpC,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AACD,cAAQ,MAAM,GAAG,QAAQ,MAAM,WAAW,WAAW,SAAU,KAAK,UAAU;AAC1E,YAAI,SAAS,KAAK,IAAI,IAAI,IAAI,MAAM,gBAAgB,QAAQ;AAC5D,YAAI,SAAS,KAAK,IAAI,IAAI,IAAI,MAAM,gBAAgB,QAAQ;AAC5D,YAAI,MAAM,WAAW,UAAU,QAAQ,MAAM,GAAG;AAC5C,gBAAM,SAAS,gBAAgB,KAAK;AACpC,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,KAAK;AAChD;AAAA,QACJ;AACA,YAAI,MAAM,SAAS,KAAK,QAAQ,GAAG;AAC/B,gBAAM,SAAS,gBAAgB,KAAK;AACpC;AAAA,QACJ;AACA,YAAI,QAAQ,cAAc;AACtB,gBAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,WAAW,GAAG;AAAA,QAC7D;AACA,YAAI,MAAM,SAAS,cAAc;AAC7B,gBAAM,SAAS,gBAAgB,KAAK;AAAA,QACxC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,UAAS,UAAU,aAAa,SAAU,UAAU,QAAQ,QAAQ;AAChE,UAAI,yBAAyB;AAC7B,UAAI,KAAK,MAAM,OAAO,MAAM,SAAS,KACjC,WAAW,KAAK,QAAQ,kBACxB,SAAS,KAAK,QAAQ,sBACtB,SAAS,KAAK,QAAQ,uBACrB,SAAS,0BAA0B,SAAS,yBAAyB;AACtE,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,WAAW,SAAU,KAAK,UAAU;AACnD,UAAI,OAAO;AAAA,QACP,MAAM;AAAA,QACN,QAAQ,KAAK;AAAA,QACb,MAAM,IAAI;AAAA,QACV,MAAM,IAAI;AAAA,MACd;AAEA,UAAI,YAAY,KAAK,gBAAgB,IAAI,QAAQ;AACjD,UAAI,YAAY,KAAK,gBAAgB,IAAI,QAAQ;AACjD,WAAK,OAAO,QAAQ,UAAU,WAAW,IACnC,KAAK,OACL,UAAU;AAChB,WAAK,OAAO,QAAQ,UAAU,WAAW,IACnC,KAAK,OACL,UAAU;AAChB,WAAK,OAAO,KAAK,IAAI,UAAU,UAAU,UAAU,QAAQ;AAC3D,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,UAAU,MAAM,IAAI;AAE7D,UAAI,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,IAAI,GAAG;AAE5C,YAAI,KAAK,OAAO,KAAK,gBAAgB,gBACjC,KAAK,OAAO,KAAK,gBAAgB,gBACjC,KAAK,OAAO,KAAK,gBAAgB,gBACjC,KAAK,OAAO,KAAK,gBAAgB,cAAc;AAC/C,eAAK,SAAS,KAAK;AAAA,QACvB;AACA,aAAK,SAAS,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAC1D,eAAO;AAAA,MACX;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,aAAa,SAAU,GAAG;AACzC,UAAI,aAAa;AAAA,QACb,cAAc,KAAK,SAAS;AAAA,MAChC;AAEA,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,UAAU,GAAG;AACtD,aAAK,SAAS,gBAAgB,KAAK;AACnC,eAAO;AAAA,MACX;AACA,UAAI,CAAC,WAAW,cAAc;AAC1B,YAAI,YAAY,KAAK,QAAQ;AAC7B,YAAI,kBAAkB;AACtB,YAAI,aAAa,KAAK,eAAe;AACjC,cAAIV,MAAK,UAAU,OAAO,QAAQA,QAAO,SAAS,MAAMA;AACxD,cAAI,OAAO,IAAI,KAAK,gBAAgB,OAAO;AACvC,8BAAkB;AAClB,qBAAS,CAAC;AAAA,UACd;AAAA,QACJ;AACA,YAAI,KAAK,QAAQ,KAAK;AAClB,cAAI,GAAG,KAAK,QAAQ,GAAG;AAAA,QAC3B;AACA,YAAI,KAAK,QAAQ,SACb,CAAC,0BAA0B,EAAE,QAAQ,KAAK,QAAQ,uBAAuB,GAAG;AAC5E,gBAAM,CAAC;AAAA,QACX;AACA,aAAK,gBAAgB,kBAAkB,OAAO,OAAO;AACrD,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,IAAAU,UAAS,UAAU,SAAS,WAAY;AACpC,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,QAAQ,SAAS;AACvB;AAAA,MACJ;AAGA,UAAI,WAAW;AACX,aAAK,QAAQ,YAAY;AAAA,MAC7B;AACA,mBAAa,KAAK,aAAa;AAC/B,WAAK,gBAAgB,OAAO,WAAW,WAAY;AAC/C,cAAM,MAAM,QAAQ,MAAM,MAAM,WAAW,MAAM;AAAA,MACrD,GAAG,KAAK,QAAQ,aAAa;AAAA,IACjC;AAEA,IAAAA,UAAS,UAAU,gBAAgB,SAAU,GAAG;AAC5C,UAAI,EAAE,WAAW,KAAK,WAAW,CAAC,KAAK,SAAS,SAAS;AACrD;AAAA,MACJ;AACA,UAAI,WAAW,KAAK;AACpB,eAAS,eAAe;AACxB,UAAI,CAAC,KAAK,cAAc,KAAK,QAAQ,YAAY,KAAK,MAAM,GAAG;AAC3D,aAAK,SAAS,WAAW,KAAK;AAC9B,YAAI,KAAK,QAAQ,cAAc,GAAkB;AAC7C,eAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,WAAW,KAAK,cAAc,CAAC;AAAA,QAC5E;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,sBAAsB,SAAU,SAAS;AACxD,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAM;AAC1C,UAAI,KAAK,KAAK,QAAQ,SAAS,SACzB,KAAK,QAAQ,WACb,CAAC,KAAK,OAAO;AACnB,UAAI,gBAAgB,UAAU,SAAS;AACvC,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,YAAI,OAAO,GAAG,CAAC;AAGf,YAAI,KAAK,oBAAoB;AACzB;AAAA,QACJ;AACA,aAAK,MAAM,gBAAgB;AAAA,MAC/B;AAAA,IACJ;AACA,IAAAA,UAAS,UAAU,UAAU,SAAU,SAAS;AAC5C,UAAI,iBAAiB,KAAK,WAAW,OAAO;AAC5C,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,aAAa;AACtD,WAAK,gBAAgB,QAAQ,OAAO;AACpC,WAAK,gBAAgB,QAAQ,OAAO;AACpC,UAAI,gBAAgB;AAChB,aAAK,WAAW,WAAW,OAAO;AAClC,aAAK,SAAS,WAAW,OAAO;AAChC,aAAK,sBAAsB,QAAQ;AACnC,aAAK,sBAAsB;AAC3B,YAAI,KAAK,QAAQ,cAAc;AAC3B,eAAK,eAAe,WAAW,OAAO;AAAA,QAC1C;AAAA,MACJ;AACA,WAAK,QAAQ,QAAQ;AACrB,WAAK,gBAAgB,OAAO,KAAK,OAAO;AAAA,IAC5C;AACA,IAAAA,UAAS,UAAU,aAAa,SAAU,SAAS;AAC/C,UAAI,iBAAiB,YAAY,KAAK;AACtC,UAAI,gBAAgB;AAChB,aAAK,UAAU;AAAA,MACnB;AACA,aAAO;AAAA,IACX;AACA,IAAAA,UAAS,UAAU,WAAW,SAAU,QAAQ,QAAQ,MAAM,QAAQ;AAClE,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAG;AACjC,UAAIV,MAAK,KAAK,cAAc,GAAG,IAAIA,IAAG,GAAG,IAAIA,IAAG;AAChD,eAAS,CAAC,SAAS,KAAK,SAAS;AACjC,gBAAU;AACV,gBAAU;AACV,WAAK,SAAS,QAAQ,QAAQ,MAAM,MAAM;AAAA,IAC9C;AACA,IAAAU,UAAS,UAAU,WAAW,SAAU,GAAG,GAAG,MAAM,QAAQ,gBAAgB;AACxE,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAG;AACjC,UAAI,WAAW,QAAQ;AAAE,iBAAS,KAAK;AAAA,MAAQ;AAC/C,UAAI,mBAAmB,QAAQ;AAAE,yBAAiB;AAAA,UAC9C,OAAO,CAAC;AAAA,UACR,KAAK,CAAC;AAAA,QACV;AAAA,MAAG;AACH,UAAI,WAAW,KAAK,QAAQ,gBAAgB,OAAO,QAAQ,OAAO;AAClE,UAAI,aAAa,KAAK,cAAc;AACpC,UAAI,aAAa,SAAS,EAAE,GAAG,WAAW,GAAG,GAAG,WAAW,EAAE,GAAG,eAAe,KAAK;AACpF,UAAI,WAAW,SAAS;AAAA,QAAE;AAAA,QACtB;AAAA,MAAK,GAAG,eAAe,GAAG;AAC9B,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,UAAU,QAAQ;AAE3D,UAAI,YAAY,YAAY,QAAQ;AAChC;AACJ,UAAI,SAAS,KAAK,IAAI,SAAS,IAAI,WAAW,CAAC;AAC/C,UAAI,SAAS,KAAK,IAAI,SAAS,IAAI,WAAW,CAAC;AAG/C,UAAI,SAAS,uBAAuB,SAAS,qBAAqB;AAC9D,eAAO;AACP,aAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,iBAAiB;AAAA,MAC9D;AACA,WAAK,SAAS,KAAK,YAAY,UAAU,MAAM,QAAQ;AAAA,IAC3D;AACA,IAAAA,UAAS,UAAU,kBAAkB,SAAU,IAAI,MAAM,SAAS,SAAS,QAAQ;AAC/E,UAAI,YAAY,WAAW,EAAE;AAC7B,UAAI,MAAM,OAAO,SAAS;AAC1B,UAAI,YAAY,SAAUC,SAAQ,MAAM,aAAa;AACjD,YAAI,OAAOA,YAAW,UAAU;AAC5B,iBAAOA;AAAA,QACX;AAEA,eAAOA,UAAS,KAAK,MAAM,OAAO,IAAI,cAAc,CAAC,IAAI;AAAA,MAC7D;AACA,gBAAU,UAAU,SAAS,UAAU,aAAa,KAAK,QAAQ,WAAW;AAC5E,gBAAU,UAAU,SAAS,UAAU,cAAc,KAAK,QAAQ,YAAY;AAC9E,UAAI,SAAS,SAAUC,MAAK,YAAYD,SAAQ,gBAAgB;AAC5D,QAAAC,QAAO;AACP,QAAAA,OAAM,eAAe,eAAeA,OAAMD,OAAM;AAChD,eAAOC;AAAA,MACX;AACA,UAAI,OAAO,OAAO,IAAI,MAAM,KAAK,cAAc,MAAM,SAAS,KAAK,eAAe;AAClF,UAAI,MAAM,OAAO,IAAI,KAAK,KAAK,cAAc,KAAK,SAAS,KAAK,eAAe;AAC/E,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,iBAAiB,WAAW,GAAG,GAAG;AAC3E;AAAA,MACJ;AACA,WAAK,SAAS,IAAI,MAAM,IAAI,KAAK,MAAM,MAAM;AAAA,IACjD;AACA,IAAAF,UAAS,UAAU,gBAAgB,SAAU,MAAM,QAAQ;AACvD,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAG;AACjC,UAAI,WAAW,QAAQ;AAAE,iBAAS,KAAK;AAAA,MAAQ;AAC/C,UAAIV,MAAK,KAAK,gBAAgB,gBAAgB,GAAG,IAAIA,IAAG,UAAU,cAAcA,IAAG;AACnF,UAAIS,MAAK,KAAK,gBAAgB,gBAAgB,GAAG,IAAIA,IAAG,UAAU,cAAcA,IAAG;AACnF,UAAI,eAAe,aAAa;AAC5B,eAAO;AAAA,MACX;AAEA,UAAI,iBAAiB;AAGjB,aAAK,OAAO;AAAA,MAChB;AAEA,WAAK,SAAS,GAAG,GAAG,MAAM,MAAM;AAChC,aAAO;AAAA,IACX;AAEA,IAAAC,UAAS,UAAU,SAAS,WAAY;AACpC,WAAK,UAAU,KAAK,QAAQ;AAAA,IAChC;AACA,IAAAA,UAAS,UAAU,kBAAkB,SAAU,KAAK;AAChD,WAAK,gBAAgB,eAAe,IAAI,CAAC;AACzC,WAAK,gBAAgB,eAAe,IAAI,CAAC;AAAA,IAC7C;AACA,IAAAA,UAAS,UAAU,gBAAgB,WAAY;AAC3C,aAAO,KAAK,QAAQ,cAAc;AAAA,IACtC;AACA,IAAAA,UAAS,UAAU,SAAS,WAAY;AACpC,WAAK,QAAQ,UAAU;AAAA,IAC3B;AACA,IAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,2BAAqB,KAAK,SAAS,KAAK;AACxC,WAAK,QAAQ,UAAU;AAAA,IAC3B;AACA,IAAAA,UAAS,UAAU,UAAU,WAAY;AACrC,UAAI,QAAQ;AACZ,UAAI,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,WAAK,QAAQ,SAAU,KAAK;AAAE,eAAO,MAAM,GAAG,EAAE,QAAQ;AAAA,MAAG,CAAC;AAAA,IAChE;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUG,qBAAoB,MAAM;AACpC,aAASA,oBAAmB,IAAI,SAAS;AACrC,UAAI,QAAQ,OAAO,KAAK,MAAM;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC,KAAK;AACN,UAAI,UAAU,WAAW,EAAE;AAC3B,UAAI,CAAC,SAAS;AACV,aAAK,kCAAkC;AACvC,eAAO;AAAA,MACX;AACA,YAAM,UAAU,CAAC;AACjB,YAAM,UAAU,IAAI,mBAAmB,EAAE,MAAM,OAAO,EAAE,QAAQ;AAChE,UAAI,CAAC,MAAM,WAAW,OAAO,EAAE,OAAO;AAClC,eAAO;AAAA,MACX;AACA,YAAM,QAAQ,IAAI,aAAa;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,YAAM,KAAK,OAAO;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,oBAAmB,MAAM,SAAU,MAAM;AACrC,UAAI,OAAO,KAAK;AAChB,UAAI,YAAYA,oBAAmB,QAAQ,KAAK,SAAU,QAAQ;AAAE,eAAO,SAAS,OAAO;AAAA,MAAM,CAAC;AAClG,UAAI;AACA,eAAOA;AACX,UAAI,QAAQ,IAAI,GAAG;AACf,aAAK,mFAAmF;AACxF,eAAOA;AAAA,MACX;AACA,MAAAA,oBAAmB,WAAW,IAAI,IAAI;AACtC,MAAAA,oBAAmB,QAAQ,KAAK;AAAA,QAC5B;AAAA,QACA,YAAY,KAAK;AAAA,QACjB;AAAA,MACJ,CAAC;AACD,aAAOA;AAAA,IACX;AACA,IAAAA,oBAAmB,UAAU,aAAa,SAAU,SAAS;AACzD,UAAI,iBAAiB;AACrB,UAAI,QAAQ;AACZ,UAAI,UAAU,QAAQ,SAAS,KAAK,QAAQ,uBAAuB;AACnE,UAAI,CAAC,SAAS;AACV,aAAK,8EAA8E;AACnF,gBAAQ;AAAA,MACZ,OACK;AACD,yBAAiB,KAAK,YAAY;AAClC,YAAI,gBAAgB;AAChB,eAAK,UAAU;AAAA,QACnB;AAAA,MACJ;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,OAAO,SAAU,SAAS;AACnD,UAAI,QAAQ;AACZ,WAAK,UAAU;AAEf,cAAQ,qBAAqB;AAC7B,WAAK,WAAW,IAAI,SAAS,SAAS,KAAK,SAAS,KAAK,OAAO;AAChE,WAAK,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,WAAW,QAAQ,WAAY;AACtE,cAAM,QAAQ;AAAA,MAClB,CAAC;AACD,WAAK,cAAc;AACnB,WAAK,eAAe;AACpB,WAAK,OAAO;AACZ,WAAK,MAAM,gBAAgB;AAC3B,WAAK,aAAa;AAElB,WAAK,oBAAoB,KAAK,OAAO;AACrC,UAAIb,MAAK,KAAK,SAAS,SAASA,IAAG,QAAQ,SAASA,IAAG;AACvD,UAAI,WAAW;AAAA,QACX,GAAG;AAAA,QACH,GAAG;AAAA,MACP;AAEA,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,uBAAuB,QAAQ,GAAG;AAC3E;AAAA,MACJ;AACA,WAAK,SAAS,SAAS,SAAS,GAAG,SAAS,CAAC;AAAA,IACjD;AACA,IAAAa,oBAAmB,UAAU,eAAe,WAAY;AACpD,UAAI,QAAQ;AACZ,UAAI,UAAU,KAAK;AACnB,MAAAA,oBAAmB,QACd,KAAK,SAAU,GAAG,GAAG;AACtB,YAAIb;AACJ,YAAI,iBAAiBA,MAAK,CAAC,GACvBA;AAAA,UAAG;AAAA;AAAA,QAAe,IAAI,IACtBA;AAAA,UAAG;AAAA;AAAA,QAAiB,IAAI,GACxBA;AACJ,YAAI,SAAS,EAAE,aAAa,cAAc,EAAE,UAAU,IAAI;AAC1D,YAAI,SAAS,EAAE,aAAa,cAAc,EAAE,UAAU,IAAI;AAC1D,eAAO,SAAS;AAAA,MACpB,CAAC,EACI,QAAQ,SAAU,MAAM;AACzB,YAAI,OAAO,KAAK;AAChB,YAAI,QAAQ,KAAK,IAAI,KAAK,OAAO,SAAS,YAAY;AAClD,gBAAM,QAAQ,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK;AAAA,QAC7C;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAa,oBAAmB,UAAU,iBAAiB,WAAY;AAEtD,UAAI,KAAK,QAAQ,UAAU;AACvB,aAAK,GAAG,KAAK,WAAW,mBAAmB,WAAY;AACnD,cAAI,gBAAgB,SAAS;AAC7B,cAAI,kBACC,cAAc,YAAY,WACvB,cAAc,YAAY,aAAa;AAC3C,0BAAc,KAAK;AAAA,UACvB;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,IAAAA,oBAAmB,UAAU,gBAAgB,WAAY;AACrD,eAAS,KAAK,SAAS,OAAO,MAAM;AAAA,QAChC,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,QAChB,KAAK,WAAW;AAAA,MACpB,CAAC;AAAA,IACL;AACA,IAAAA,oBAAmB,UAAU,sBAAsB,SAAU,SAAS;AAClE,WAAK,SAAS,QAAQ,OAAO;AAC7B,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,SAAS,OAAO;AACzD,WAAK,QAAQ,KAAK,WAAW,SAAS,OAAO;AAAA,IACjD;AACA,IAAAA,oBAAmB,UAAU,QAAQ,SAAUC,mBAAkB;AAC7D,UAAI,QAAQ;AACZ,MAAAA,kBAAiB,QAAQ,SAAUd,KAAI;AACnC,YAAI,MAAMA,IAAG,KAAK,YAAYA,IAAG;AACjC,wBAAgB,OAAO,WAAW,GAAG;AAAA,MACzC,CAAC;AAAA,IACL;AACA,IAAAa,oBAAmB,UAAU,UAAU,WAAY;AAC/C,UAAIb,MAAK,KAAK,WAAW,KAAK,OAAO,GAAG,iBAAiBA,IAAG,gBAAgB,QAAQA,IAAG;AACvF,UAAI,OAAO;AACP,YAAI,UAAU,KAAK;AACnB,aAAK,oBAAoB,OAAO;AAChC,YAAI,gBAAgB;AAChB,eAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,gBAAgB,OAAO;AAChE,eAAK,QAAQ,KAAK,WAAW,gBAAgB,OAAO;AAAA,QACxD;AACA,aAAK,SAAS,cAAc;AAAA,MAChC;AAAA,IACJ;AACA,IAAAa,oBAAmB,UAAU,SAAS,WAAY;AAC9C,WAAK,SAAS,OAAO;AACrB,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,MAAM;AAC/C,WAAK,QAAQ,KAAK,WAAW,MAAM;AAAA,IACvC;AACA,IAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,WAAK,SAAS,QAAQ;AACtB,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO;AAChD,WAAK,QAAQ,KAAK,WAAW,OAAO;AAAA,IACxC;AACA,IAAAA,oBAAmB,UAAU,UAAU,WAAY;AAC/C,WAAK,MAAM,QAAQ,KAAK,MAAM,WAAW,OAAO;AAChD,WAAK,QAAQ,KAAK,WAAW,OAAO;AACpC,WAAK,SAAS,QAAQ;AAAA,IAC1B;AACA,IAAAA,oBAAmB,UAAU,gBAAgB,SAAU,OAAO;AAC1D,WAAK,aAAa,KAAK;AAAA,IAC3B;AACA,IAAAA,oBAAmB,UAAU,CAAC;AAC9B,IAAAA,oBAAmB,aAAa,CAAC;AACjC,WAAOA;AAAA,EACX,EAAE,YAAY;AAAA;AACd,SAAS,cAAc,IAAI,SAAS;AAChC,MAAI,KAAK,IAAI,mBAAmB,IAAI,OAAO;AAC3C,SAAO;AACX;AACA,cAAc,MAAM,mBAAmB;AACvC,cAAc,UAAU,mBAAmB;AAC3C,cAAc,aAAa,mBAAmB;AAC9C,IAAI,UAAU;", "names": ["d", "b", "__assign", "style", "e", "EventEmitter", "EventRegister", "CustomOptions", "OptionsConstructor", "ActionsHandler", "_a", "click", "Translater", "Base", "Transition", "Animation", "Behavior", "DirectionLockAction", "ScrollerActions", "_b", "<PERSON><PERSON><PERSON>", "offset", "pos", "BScrollConstructor", "propertiesConfig"]}
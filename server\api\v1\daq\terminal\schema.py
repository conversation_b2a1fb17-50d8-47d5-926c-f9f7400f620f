'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-03-21 20:45:09
LastEditors: zhai
LastEditTime: 2025-04-15 23:49:14
'''
from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class TerminalCreateDTO(BaseModel):
    name: str
    serial_code: str
    description: Optional[str] = None
    client_id: Optional[str] = None
    status: Optional[bool] = None


class TerminalDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    name: Optional[str] = None
    serial_code: Optional[str] = None
    description: Optional[str] = None
    client_id: Optional[str] = None
    status: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)

/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-29 15:32:23
 * @LastEditors: zhai
 * @LastEditTime: 2024-07-04 22:17:48
 */
import { request } from '../request';
import type { ICrudQuery } from '../fast-crud-api';
import { fastGetListApi } from '../fast-crud-api';

/** get task group list */
export function fetchGetTaskGroupList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.Task.GroupList>({
    url: '/task/groups',
    method: 'get',
    params
  });
}

/** get task list */
export function fetchGetTaskList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.Task.SchedulerList>({
    url: '/task',
    method: 'get',
    params
  });
}

export function fetchAddTask(data: any) {
  return request<Api.Task.Scheduler>({
    url: '/task',
    method: 'post',
    data
  });
}

export function fetchUpdateTask(data: any) {
  return request<Api.Task.Scheduler>({
    url: `/task/${data?.id}`,
    method: 'put',
    data
  });
}

export function fetchRunOnceTask(data?: Api.SystemManage.CommonDeleteParams) {
  return request<Api.SystemManage.UserList>({
    url: `/task/run_once`,
    method: 'post',
    params: {
      _id: data.id
    }
  });
}

/** delete task */
export function fetchDeleteTask(data?: Api.SystemManage.CommonDeleteParams) {
  return request<Api.SystemManage.UserList>({
    url: `/task/${data?.id}`,
    method: 'delete'
  });
}

// export function fetchBatchDeleteTask(data?: Api.SystemManage.CommonBatchDeleteParams) {
//   return request<Api.SystemManage.UserList>({
//     url: '/task',
//     method: 'delete',
//     params: { ids: data?.ids.join(',') }
//   });
// }

export function fetchGetTaskRecordList(query?: ICrudQuery, relationships: boolean | null = null) {
  return fastGetListApi('/task/records', query);
}

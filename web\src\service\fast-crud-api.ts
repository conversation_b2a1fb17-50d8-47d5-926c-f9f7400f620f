import type { UserPageQuery } from '@fast-crud/fast-crud';
import { request } from '@/service/request';
import { Crud<PERSON>pi, <PERSON>ton, buildQueryParams } from './crud-api';

interface IPage {
  limit: number;
  offset: number;
}

interface ISort {
  prop: string;
  order: string;
  asc: boolean;
}

interface IQuery {
  [key: string]: any;
}

export interface ICrudQuery {
  page: IPage;
  query: IQuery;
  sort?: ISort;
}

type DictObject = { [key: string]: any };

function removeEmptyValues(obj: DictObject): DictObject {
  const newObj: DictObject = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      if (value !== null && value !== undefined && value !== '') {
        newObj[key] = value;
      }
    }
  }

  return newObj;
}

export function fastGetListApi(
  url: string,
  query?: ICrudQuery,
  relationships: boolean | null = null,
  tenant_data_filter: boolean | null = null,
  user_data_filter: boolean | null = null
) {
  let query_params = '';
  if (query) {
    const { page, sort, query: filterQuery } = query;

    // 构建排序字段
    const sort_by = sort?.prop && sort?.order ? (sort.asc ? sort.prop : `-${sort.prop}`) : null;

    query_params = buildQueryParams({
      sort_by,
      relationships,
      skip: page.offset,
      limit: page.limit,
      tenant_data_filter,
      user_data_filter
    });
  }

  return request<Api.SystemManage.UserList>({
    url: `${url}${query_params}`,
    method: 'post',
    data: removeEmptyValues(query?.query || {})
  });
}

export class FastCrudApi<CLS, T = object | undefined> extends CrudApi<CLS, T> {
  constructor(prefix: string) {
    super(prefix);
  }

  async GetList(
    query: UserPageQuery,
    relationships: boolean | null = null,
    tenant_data_filter: boolean | null = null,
    user_data_filter: boolean | null = null
  ) {
    // {
    //     page:
    //     { limit: 20, offset: 0 }
    //     query
    //     :
    //     { job_id: '1', text: '2', ... }
    //     sort
    //     :
    //     { prop: 'name', order: 'ascend', asc: true }
    // }

    const { page, sort, query: filterQuery } = query;

    // 构建排序字段
    const sort_by = sort?.prop && sort?.order ? (sort.asc ? sort.prop : `-${sort.prop}`) : null;

    // 直接调用 this.query，传递所有参数
    return await this.query(
      removeEmptyValues(filterQuery), // data
      sort_by, // sort_by
      relationships, // relationships
      page.offset, // skip
      page.limit, // limit
      tenant_data_filter,
      user_data_filter
    );
  }

  async AddObj(obj: T) {
    return await this.create(obj);
  }

  async UpdateObj(obj: T) {
    return await this.update(obj);
  }

  async DelObj(id: number) {
    return await this.delete(id);
  }

  async GetObj(id: number) {
    return await this.get_by_id(id);
  }

  async BatchCreate(data: T[]) {
    return await this.batch_create(data);
  }

  async BatchDelete(ids: number[]) {
    return await this.batch_delete(ids);
  }
}

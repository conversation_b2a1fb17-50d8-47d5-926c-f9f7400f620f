{"version": 3, "sources": ["../../.pnpm/alova@3.2.6/node_modules/alova/dist/clienthook/index.esm.js"], "sourcesContent": ["/**\n  * @alova/client 2.0.0 (https://alova.js.org)\n  * Document https://alova.js.org\n  * Copyright 2024 Scott hu. All Rights Reserved\n  * Licensed under MIT (git://github.com/alovajs/alova/blob/main/LICENSE)\n*/\n\nimport { isPlainObject, instanceOf, trueValue, falseValue, newInstance, PromiseCls, pushItem, isFn, noop, len, forEach, undefinedValue, splice, $self, isNumber, clearTimeoutTimer, setTimeoutFn, createAssert, FrameworkState, FrameworkReadableState, ObjectCls, objAssign, nullValue, mapItem, objectKeys, includes, filterItem, deleteAttr, getHandlerMethod as getHandlerMethod$1, getContext, getMethodInternalKey, promiseResolve, omit, promiseThen, sloughConfig, buildNamespacedCacheKey, getTime, createEventManager, createSyncOnceRunner, promiseCatch, isArray, createAsyncQueue, usePromise, getLocalCacheConfigParam, MEMORY, objectValues, decorateEvent, isObject, walkObject, valueObject, uuid, defineProperty, globalToString, RegExpCls, regexpTest, shift, delayWithBackoff, isString, getConfig, AlovaError, promiseReject, promiseFinally, buildCompletedURL, getOptions } from '@alova/shared';\nimport { Method, queryCache, promiseStatesHook, globalConfigMap, setCache, invalidateCache, hitCacheBySource } from 'alova';\n\nconst defaultVisitorMeta = {\n    authRole: null\n};\nconst defaultLoginMeta = {\n    authRole: 'login'\n};\nconst defaultLogoutMeta = {\n    authRole: 'logout'\n};\nconst defaultRefreshTokenMeta = {\n    authRole: 'refreshToken'\n};\nconst checkMethodRole = ({ meta }, metaMatches) => {\n    if (isPlainObject(meta)) {\n        for (const key in meta) {\n            if (Object.prototype.hasOwnProperty.call(meta, key)) {\n                const matchedMetaItem = metaMatches[key];\n                if (instanceOf(matchedMetaItem, RegExp) ? matchedMetaItem.test(meta[key]) : meta[key] === matchedMetaItem) {\n                    return trueValue;\n                }\n            }\n        }\n    }\n    return falseValue;\n};\nconst waitForTokenRefreshed = (method, waitingList) => newInstance(PromiseCls, resolve => {\n    pushItem(waitingList, {\n        method,\n        resolve\n    });\n});\nconst callHandlerIfMatchesMeta = (method, authorizationInterceptor, defaultMeta, response) => {\n    if (checkMethodRole(method, (authorizationInterceptor === null || authorizationInterceptor === void 0 ? void 0 : authorizationInterceptor.metaMatches) || defaultMeta)) {\n        const handler = isFn(authorizationInterceptor)\n            ? authorizationInterceptor\n            : isPlainObject(authorizationInterceptor) && isFn(authorizationInterceptor.handler)\n                ? authorizationInterceptor.handler\n                : noop;\n        return handler(response, method);\n    }\n};\nconst refreshTokenIfExpired = async (method, waitingList, updateRefreshStatus, handlerParams, refreshToken, tokenRefreshing) => {\n    // When the number of handle params is greater than 2, it means that this function is called from the response, and the original interface needs to be requested again.\n    const fromResponse = len(handlerParams) >= 2;\n    let isExpired = refreshToken === null || refreshToken === void 0 ? void 0 : refreshToken.isExpired(...handlerParams);\n    // Compatible with synchronous and asynchronous functions\n    if (instanceOf(isExpired, PromiseCls)) {\n        isExpired = await isExpired;\n    }\n    if (isExpired) {\n        try {\n            // Make another judgment in the response to prevent multiple requests to refresh the token, intercept and wait for the token sent before the token refresh is completed.\n            let intentToRefreshToken = trueValue;\n            if (fromResponse && tokenRefreshing) {\n                intentToRefreshToken = falseValue; // The requests waiting here indicate that the token is being refreshed. When they pass, there is no need to refresh the token again.\n                await waitForTokenRefreshed(method, waitingList);\n            }\n            if (intentToRefreshToken) {\n                updateRefreshStatus(trueValue);\n                // Call refresh token\n                await (refreshToken === null || refreshToken === void 0 ? void 0 : refreshToken.handler(...handlerParams));\n                updateRefreshStatus(falseValue);\n                // After the token refresh is completed, the requests in the waiting list are notified.\n                forEach(waitingList, ({ resolve }) => resolve());\n            }\n            if (fromResponse) {\n                // Because the original interface is being requested again, superposition with the previous request will result in repeated calls to transform, so it is necessary to leave transform empty to remove one call.\n                const { config } = method;\n                const methodTransformData = config.transform;\n                config.transform = undefinedValue;\n                const resentData = await method;\n                config.transform = methodTransformData;\n                return resentData;\n            }\n        }\n        finally {\n            updateRefreshStatus(falseValue);\n            splice(waitingList, 0, len(waitingList)); // Clear waiting list\n        }\n    }\n};\nconst onResponded2Record = (onRespondedHandlers) => {\n    let successHandler = undefinedValue;\n    let errorHandler = undefinedValue;\n    let onCompleteHandler = undefinedValue;\n    if (isFn(onRespondedHandlers)) {\n        successHandler = onRespondedHandlers;\n    }\n    else if (isPlainObject(onRespondedHandlers)) {\n        const { onSuccess, onError, onComplete } = onRespondedHandlers;\n        successHandler = isFn(onSuccess) ? onSuccess : successHandler;\n        errorHandler = isFn(onError) ? onError : errorHandler;\n        onCompleteHandler = isFn(onComplete) ? onComplete : onCompleteHandler;\n    }\n    return {\n        onSuccess: successHandler,\n        onError: errorHandler,\n        onComplete: onCompleteHandler\n    };\n};\n\n/**\n * Create a client-side token authentication interceptor\n * @param options Configuration parameters\n * @returns token authentication interceptor function\n */\nconst createClientTokenAuthentication = ({ visitorMeta, login, logout, refreshToken, assignToken = noop }) => {\n    let tokenRefreshing = falseValue;\n    const waitingList = [];\n    const onAuthRequired = onBeforeRequest => async (method) => {\n        const isVisitorRole = checkMethodRole(method, visitorMeta || defaultVisitorMeta);\n        const isLoginRole = checkMethodRole(method, (login === null || login === void 0 ? void 0 : login.metaMatches) || defaultLoginMeta);\n        // Ignored, login, and token refresh requests do not perform token authentication.\n        if (!isVisitorRole &&\n            !isLoginRole &&\n            !checkMethodRole(method, (refreshToken === null || refreshToken === void 0 ? void 0 : refreshToken.metaMatches) || defaultRefreshTokenMeta)) {\n            // If the token is being refreshed, wait for the refresh to complete before sending a request.\n            if (tokenRefreshing) {\n                await waitForTokenRefreshed(method, waitingList);\n            }\n            await refreshTokenIfExpired(method, waitingList, refreshing => {\n                tokenRefreshing = refreshing;\n            }, [method], refreshToken);\n        }\n        // Requests from non-guest and logged-in roles will enter the assignment token function\n        if (!isVisitorRole && !isLoginRole) {\n            await assignToken(method);\n        }\n        return onBeforeRequest === null || onBeforeRequest === void 0 ? void 0 : onBeforeRequest(method);\n    };\n    const onResponseRefreshToken = originalResponded => {\n        const respondedRecord = onResponded2Record(originalResponded);\n        return {\n            ...respondedRecord,\n            onSuccess: async (response, method) => {\n                await callHandlerIfMatchesMeta(method, login, defaultLoginMeta, response);\n                await callHandlerIfMatchesMeta(method, logout, defaultLogoutMeta, response);\n                return (respondedRecord.onSuccess || $self)(response, method);\n            }\n        };\n    };\n    return {\n        waitingList,\n        onAuthRequired,\n        onResponseRefreshToken\n    };\n};\n/**\n * Create a server-side token authentication interceptor\n * @param options Configuration parameters\n * @returns token authentication interceptor function\n */\nconst createServerTokenAuthentication = ({ visitorMeta, login, logout, refreshTokenOnSuccess, refreshTokenOnError, assignToken = noop }) => {\n    let tokenRefreshing = falseValue;\n    const waitingList = [];\n    const onAuthRequired = onBeforeRequest => async (method) => {\n        const isVisitorRole = checkMethodRole(method, visitorMeta || defaultVisitorMeta);\n        const isLoginRole = checkMethodRole(method, (login === null || login === void 0 ? void 0 : login.metaMatches) || defaultLoginMeta);\n        // Ignored, login, and token refresh requests do not perform token authentication.\n        if (!isVisitorRole &&\n            !isLoginRole &&\n            !checkMethodRole(method, (refreshTokenOnSuccess === null || refreshTokenOnSuccess === void 0 ? void 0 : refreshTokenOnSuccess.metaMatches) || defaultRefreshTokenMeta) &&\n            !checkMethodRole(method, (refreshTokenOnError === null || refreshTokenOnError === void 0 ? void 0 : refreshTokenOnError.metaMatches) || defaultRefreshTokenMeta)) {\n            // If the token is being refreshed, wait for the refresh to complete before sending a request.\n            if (tokenRefreshing) {\n                await waitForTokenRefreshed(method, waitingList);\n            }\n        }\n        if (!isVisitorRole && !isLoginRole) {\n            await assignToken(method);\n        }\n        return onBeforeRequest === null || onBeforeRequest === void 0 ? void 0 : onBeforeRequest(method);\n    };\n    const onResponseRefreshToken = onRespondedHandlers => {\n        const respondedRecord = onResponded2Record(onRespondedHandlers);\n        return {\n            ...respondedRecord,\n            onSuccess: async (response, method) => {\n                if (!checkMethodRole(method, visitorMeta || defaultVisitorMeta) &&\n                    !checkMethodRole(method, (login === null || login === void 0 ? void 0 : login.metaMatches) || defaultLoginMeta) &&\n                    !checkMethodRole(method, (refreshTokenOnSuccess === null || refreshTokenOnSuccess === void 0 ? void 0 : refreshTokenOnSuccess.metaMatches) || defaultRefreshTokenMeta)) {\n                    const dataResent = await refreshTokenIfExpired(method, waitingList, refreshing => {\n                        tokenRefreshing = refreshing;\n                    }, [response, method], refreshTokenOnSuccess, tokenRefreshing);\n                    if (dataResent) {\n                        return dataResent;\n                    }\n                }\n                await callHandlerIfMatchesMeta(method, login, defaultLoginMeta, response);\n                await callHandlerIfMatchesMeta(method, logout, defaultLogoutMeta, response);\n                return (respondedRecord.onSuccess || $self)(response, method);\n            },\n            onError: async (error, method) => {\n                if (!checkMethodRole(method, visitorMeta || defaultVisitorMeta) &&\n                    !checkMethodRole(method, (login === null || login === void 0 ? void 0 : login.metaMatches) || defaultLoginMeta) &&\n                    !checkMethodRole(method, (refreshTokenOnError === null || refreshTokenOnError === void 0 ? void 0 : refreshTokenOnError.metaMatches) || defaultRefreshTokenMeta)) {\n                    const dataResent = await refreshTokenIfExpired(method, waitingList, refreshing => {\n                        tokenRefreshing = refreshing;\n                    }, [error, method], refreshTokenOnError, tokenRefreshing);\n                    if (dataResent) {\n                        return dataResent;\n                    }\n                }\n                return (respondedRecord.onError || noop)(error, method);\n            }\n        };\n    };\n    return {\n        waitingList,\n        onAuthRequired,\n        onResponseRefreshToken\n    };\n};\n\n/**\n * Compatible functions, throwing parameters\n * @param error mistake\n */\nconst throwFn = (error) => {\n    throw error;\n};\nfunction useCallback(onCallbackChange = noop) {\n    let callbacks = [];\n    const setCallback = (fn) => {\n        if (!callbacks.includes(fn)) {\n            callbacks.push(fn);\n            onCallbackChange(callbacks);\n        }\n        // Return unregister function\n        return () => {\n            callbacks = filterItem(callbacks, e => e !== fn);\n            onCallbackChange(callbacks);\n        };\n    };\n    const triggerCallback = (...args) => {\n        if (callbacks.length > 0) {\n            return forEach(callbacks, fn => fn(...args));\n        }\n    };\n    const removeAllCallback = () => {\n        callbacks = [];\n        onCallbackChange(callbacks);\n    };\n    return [setCallback, triggerCallback, removeAllCallback];\n}\n/**\n * Create a debounce function and trigger the function immediately when delay is 0\n * Scenario: When calling useWatcher and setting immediate to true, the first call must be executed immediately, otherwise it will cause a delayed call\n * @param {GeneralFn} fn callback function\n * @param {number|(...args: any[]) => number} delay Delay description, dynamic delay can be achieved when set as a function\n * @returns Delayed callback function\n */\nconst debounce = (fn, delay) => {\n    let timer = nullValue;\n    return function debounceFn(...args) {\n        const bindFn = fn.bind(this, ...args);\n        const delayMill = isNumber(delay) ? delay : delay(...args);\n        timer && clearTimeoutTimer(timer);\n        if (delayMill > 0) {\n            timer = setTimeoutFn(bindFn, delayMill);\n        }\n        else {\n            bindFn();\n        }\n    };\n};\n/**\n * Get the request method object\n * @param methodHandler Request method handle\n * @param args Method call parameters\n * @returns request method object\n */\nconst getHandlerMethod = (methodHandler, args = []) => {\n    const methodInstance = isFn(methodHandler) ? methodHandler(...args) : methodHandler;\n    createAssert('scene')(instanceOf(methodInstance, Method), 'hook handler must be a method instance or a function that returns method instance');\n    return methodInstance;\n};\n/**\n * Convert each value of the object and return the new object\n * @param obj object\n * @param callback callback function\n * @returns converted object\n */\nconst mapObject = (obj, callback) => {\n    const ret = {};\n    for (const key in obj) {\n        ret[key] = callback(obj[key], key, obj);\n    }\n    return ret;\n};\nvar EnumHookType;\n(function (EnumHookType) {\n    EnumHookType[EnumHookType[\"USE_REQUEST\"] = 1] = \"USE_REQUEST\";\n    EnumHookType[EnumHookType[\"USE_WATCHER\"] = 2] = \"USE_WATCHER\";\n    EnumHookType[EnumHookType[\"USE_FETCHER\"] = 3] = \"USE_FETCHER\";\n})(EnumHookType || (EnumHookType = {}));\n/**\n * create simple and unified, framework-independent states creators and handlers.\n * @param statesHook states hook from `promiseStatesHook` function of alova\n * @param referingObject refering object exported from `promiseStatesHook` function\n * @returns simple and unified states creators and handlers\n */\nfunction statesHookHelper(statesHook, referingObject = { trackedKeys: {}, bindError: falseValue }) {\n    const ref = (initialValue) => (statesHook.ref ? statesHook.ref(initialValue) : { current: initialValue });\n    referingObject = ref(referingObject).current;\n    const exportState = (state) => (statesHook.export || $self)(state, referingObject);\n    const memorize = (fn) => {\n        if (!isFn(statesHook.memorize)) {\n            return fn;\n        }\n        const memorizedFn = statesHook.memorize(fn);\n        memorizedFn.memorized = true;\n        return memorizedFn;\n    };\n    const { dehydrate } = statesHook;\n    // For performance reasons, only value is different, and the key is tracked can be updated.\n    const update = (newValue, state, key) => newValue !== dehydrate(state, key, referingObject) &&\n        referingObject.trackedKeys[key] &&\n        statesHook.update(newValue, state, key, referingObject);\n    const mapDeps = (deps) => mapItem(deps, item => (instanceOf(item, FrameworkReadableState) ? item.e : item));\n    const createdStateList = [];\n    // key of deps on computed\n    const depKeys = {};\n    return {\n        create: (initialValue, key) => {\n            pushItem(createdStateList, key); // record the keys of created states.\n            return newInstance((FrameworkState), statesHook.create(initialValue, key, referingObject), key, state => dehydrate(state, key, referingObject), exportState, (state, newValue) => update(newValue, state, key));\n        },\n        computed: (getter, depList, key) => {\n            // Collect all dependencies in computed\n            forEach(depList, dep => {\n                if (dep.k) {\n                    depKeys[dep.k] = true;\n                }\n            });\n            return newInstance((FrameworkReadableState), statesHook.computed(getter, mapDeps(depList), key, referingObject), key, state => dehydrate(state, key, referingObject), exportState);\n        },\n        effectRequest: (effectRequestParams) => statesHook.effectRequest(effectRequestParams, referingObject),\n        ref,\n        watch: (source, callback) => statesHook.watch(mapDeps(source), callback, referingObject),\n        onMounted: (callback) => statesHook.onMounted(callback, referingObject),\n        onUnmounted: (callback) => statesHook.onUnmounted(callback, referingObject),\n        memorize,\n        /**\n         * refering object that sharing some value with this object.\n         */\n        __referingObj: referingObject,\n        /**\n         * expose provider for specified use hook.\n         * @param object object that contains state proxy, framework state, operating function and event binder.\n         * @returns provider component.\n         */\n        exposeProvider: (object) => {\n            const provider = {};\n            const originalStatesMap = {};\n            for (const key in object) {\n                const value = object[key];\n                const isValueFunction = isFn(value);\n                // if it's a memorized function, don't memorize it any more, add it to provider directly.\n                // if it's start with `on`, that indicates it is an event binder, we should define a new function which return provider object.\n                // if it's a common function, add it to provider with memorize mode.\n                // Note that: in some situation, state is a function such as solid's signal, and state value is set to function in react,  the state will be detected as a function. so we should check whether the key is in `trackedKeys`\n                if (isValueFunction && !referingObject.trackedKeys[key]) {\n                    provider[key] = key.startsWith('on')\n                        ? (...args) => {\n                            value(...args);\n                            // eslint-disable-next-line\n                            return completedProvider;\n                        }\n                        : value.memorized\n                            ? value\n                            : memorize(value);\n                }\n                else {\n                    const isFrameworkState = instanceOf(value, FrameworkReadableState);\n                    if (isFrameworkState) {\n                        originalStatesMap[key] = value.s;\n                    }\n                    // otherwise, it's a state proxy or framework state, add it to provider with getter mode.\n                    ObjectCls.defineProperty(provider, key, {\n                        get: () => {\n                            // record the key that is being tracked.\n                            referingObject.trackedKeys[key] = trueValue;\n                            return isFrameworkState ? value.e : value;\n                        },\n                        // set need to set an function,\n                        // otherwise it will throw `TypeError: Cannot set property __referingObj of #<Object> which has only a getter` when setting value\n                        set: noop,\n                        enumerable: trueValue,\n                        configurable: trueValue\n                    });\n                }\n            }\n            const { update: nestedHookUpdate, __proxyState: nestedProxyState } = provider;\n            // reset the tracked keys and bingError flag, so that the nest hook providers can be initialized.\n            // Always track the dependencies in computed\n            referingObject.trackedKeys = {\n                ...depKeys\n            };\n            referingObject.bindError = falseValue;\n            const extraProvider = {\n                // expose referingObject automatically.\n                __referingObj: referingObject,\n                // the new updating function that can update the new states and nested hook states.\n                update: memorize((newStates) => {\n                    objectKeys(newStates).forEach(key => {\n                        if (includes(createdStateList, key)) {\n                            update(newStates[key], originalStatesMap[key], key);\n                        }\n                        else if (key in provider && isFn(nestedHookUpdate)) {\n                            nestedHookUpdate({\n                                [key]: newStates[key]\n                            });\n                        }\n                    });\n                }),\n                __proxyState: memorize((key) => {\n                    if (includes(createdStateList, key) && instanceOf(object[key], FrameworkReadableState)) {\n                        // need to tag the key that is being tracked so that it can be updated with `state.v = xxx`.\n                        referingObject.trackedKeys[key] = trueValue;\n                        return object[key];\n                    }\n                    return nestedProxyState(key);\n                })\n            };\n            const completedProvider = objAssign(provider, extraProvider);\n            return completedProvider;\n        },\n        /**\n         * transform state proxies to object.\n         * @param states proxy array of framework states\n         * @param filterKey filter key of state proxy\n         * @returns an object that contains the states of target form\n         */\n        objectify: (states, filterKey) => states.reduce((result, item) => {\n            result[item.k] = filterKey ? item[filterKey] : item;\n            return result;\n        }, {}),\n        transformState2Proxy: (state, key) => newInstance((FrameworkState), state, key, state => dehydrate(state, key, referingObject), exportState, (state, newValue) => update(newValue, state, key))\n    };\n}\n\nconst coreAssert = createAssert('');\nconst requestHookAssert = createAssert('useRequest');\nconst watcherHookAssert = createAssert('useWatcher');\nconst fetcherHookAssert = createAssert('useFetcher');\nconst coreHookAssert = (hookType) => ({\n    [EnumHookType.USE_REQUEST]: requestHookAssert,\n    [EnumHookType.USE_WATCHER]: watcherHookAssert,\n    [EnumHookType.USE_FETCHER]: fetcherHookAssert\n})[hookType];\n/**\n * Assert whether it is a method instance\n * @param methodInstance method instance\n */\nconst assertMethod = (assert, methodInstance) => assert(instanceOf(methodInstance, Method), 'expected a method instance.');\n\nconst KEY_SUCCESS = 'success';\nconst KEY_ERROR = 'error';\nconst KEY_COMPLETE = 'complete';\n\nvar createHook = (ht, c, eventManager, ro) => ({\n    /** The method instance of the last request */\n    m: undefinedValue,\n    /** saveStatesFns */\n    sf: [],\n    /** removeStatesFns */\n    rf: [],\n    /** frontStates */\n    fs: {},\n    /** eventManager */\n    em: eventManager,\n    /** hookType, useRequest=1, useWatcher=2, useFetcher=3 */\n    ht,\n    /** hook config */\n    c,\n    /** referingObject */\n    ro,\n    /** managedStates */\n    ms: {}\n});\n\n// base event\nclass AlovaEventBase {\n    constructor(method, args) {\n        this.method = method;\n        this.args = args;\n    }\n    clone() {\n        return { ...this };\n    }\n    static spawn(method, args) {\n        return new AlovaEventBase(method, args);\n    }\n}\nclass AlovaSuccessEvent extends AlovaEventBase {\n    constructor(base, data, fromCache) {\n        super(base.method, base.args);\n        this.data = data;\n        this.fromCache = fromCache;\n    }\n}\nclass AlovaErrorEvent extends AlovaEventBase {\n    constructor(base, error) {\n        super(base.method, base.args);\n        this.error = error;\n    }\n}\nclass AlovaCompleteEvent extends AlovaEventBase {\n    constructor(base, status, data, fromCache, error) {\n        super(base.method, base.args);\n        this.status = status;\n        this.data = data;\n        this.fromCache = status === 'error' ? false : fromCache;\n        this.error = error;\n    }\n}\n// extend event\nclass AlovaSSEEvent extends AlovaEventBase {\n    constructor(base, eventSource) {\n        super(base.method, base.args);\n        this.eventSource = eventSource;\n    }\n}\nclass AlovaSSEErrorEvent extends AlovaSSEEvent {\n    constructor(base, error) {\n        super(base, base.eventSource);\n        this.error = error;\n    }\n}\nclass AlovaSSEMessageEvent extends AlovaSSEEvent {\n    constructor(base, data) {\n        super(base, base.eventSource);\n        this.data = data;\n    }\n}\n/** Sq top level events */\nclass SQEvent {\n    constructor(behavior, method, silentMethod) {\n        this.behavior = behavior;\n        this.method = method;\n        this.silentMethod = silentMethod;\n    }\n}\n/** Sq global events */\nclass GlobalSQEvent extends SQEvent {\n    constructor(behavior, method, silentMethod, queueName, retryTimes) {\n        super(behavior, method, silentMethod);\n        this.queueName = queueName;\n        this.retryTimes = retryTimes;\n    }\n}\nclass GlobalSQSuccessEvent extends GlobalSQEvent {\n    constructor(behavior, method, silentMethod, queueName, retryTimes, data, vDataResponse) {\n        super(behavior, method, silentMethod, queueName, retryTimes);\n        this.data = data;\n        this.vDataResponse = vDataResponse;\n    }\n}\nclass GlobalSQErrorEvent extends GlobalSQEvent {\n    constructor(behavior, method, silentMethod, queueName, retryTimes, error, retryDelay) {\n        super(behavior, method, silentMethod, queueName, retryTimes);\n        this.error = error;\n        this.retryDelay = retryDelay;\n    }\n}\nclass GlobalSQFailEvent extends GlobalSQEvent {\n    constructor(behavior, method, silentMethod, queueName, retryTimes, error) {\n        super(behavior, method, silentMethod, queueName, retryTimes);\n        this.error = error;\n    }\n}\n/** Sq event */\nclass ScopedSQEvent extends SQEvent {\n    constructor(behavior, method, silentMethod, args) {\n        super(behavior, method, silentMethod);\n        this.args = args;\n    }\n}\nclass ScopedSQSuccessEvent extends ScopedSQEvent {\n    constructor(behavior, method, silentMethod, args, data) {\n        super(behavior, method, silentMethod, args);\n        this.data = data;\n    }\n}\nclass ScopedSQErrorEvent extends ScopedSQEvent {\n    constructor(behavior, method, silentMethod, args, error) {\n        super(behavior, method, silentMethod, args);\n        this.error = error;\n    }\n}\nclass ScopedSQRetryEvent extends ScopedSQEvent {\n    constructor(behavior, method, silentMethod, args, retryTimes, retryDelay) {\n        super(behavior, method, silentMethod, args);\n        this.retryTimes = retryTimes;\n        this.retryDelay = retryDelay;\n    }\n}\nclass ScopedSQCompleteEvent extends ScopedSQEvent {\n    constructor(behavior, method, silentMethod, args, status, data, error) {\n        super(behavior, method, silentMethod, args);\n        this.status = status;\n        this.data = data;\n        this.error = error;\n    }\n}\nclass RetriableRetryEvent extends AlovaEventBase {\n    constructor(base, retryTimes, retryDelay) {\n        super(base.method, base.args);\n        this.retryTimes = retryTimes;\n        this.retryDelay = retryDelay;\n    }\n}\nclass RetriableFailEvent extends AlovaErrorEvent {\n    constructor(base, error, retryTimes) {\n        super(base, error);\n        this.retryTimes = retryTimes;\n    }\n}\n\nconst defaultMiddleware = (_, next) => next();\n\nconst stateCache = {};\n/**\n * @description Get State cache data\n * @param baseURL Base URL\n * @param key Request key value\n * @returns Cached response data, if not returned {}\n */\nconst getStateCache = (namespace, key) => {\n    const cachedState = stateCache[namespace] || {};\n    return cachedState[key] || {};\n};\n/**\n * @description Set State cache data\n * @param baseURL Base URL\n * @param key Request key value\n * @param data cache data\n */\nconst setStateCache = (namespace, key, data, hookInstance) => {\n    const cachedState = (stateCache[namespace] = stateCache[namespace] || {});\n    cachedState[key] = {\n        s: data,\n        h: hookInstance\n    };\n};\n/**\n * @description Clear State cache data\n * @param baseURL Base URL\n * @param key Request key value\n */\nconst removeStateCache = (namespace, key) => {\n    const cachedState = stateCache[namespace];\n    if (cachedState) {\n        deleteAttr(cachedState, key);\n    }\n};\n\n/**\n * Unified processing of request logic for useRequest/useWatcher/useFetcher and other request hook functions\n * @param hookInstance hook instance\n * @param methodHandler Request method object or get function\n * @param sendCallingArgs send function parameters\n * @returns Request status\n */\nfunction useHookToSendRequest(hookInstance, methodHandler, sendCallingArgs = []) {\n    const currentHookAssert = coreHookAssert(hookInstance.ht);\n    let methodInstance = getHandlerMethod$1(methodHandler, currentHookAssert, sendCallingArgs);\n    const { fs: frontStates, ht: hookType, c: useHookConfig, ms: managedStates } = hookInstance;\n    const { loading: loadingState, data: dataState, error: errorState } = frontStates;\n    const isFetcher = hookType === EnumHookType.USE_FETCHER;\n    const { force: forceRequest = falseValue, middleware = defaultMiddleware } = useHookConfig;\n    const alovaInstance = getContext(methodInstance);\n    const { id } = alovaInstance;\n    // If it is a silent request, on success will be called directly after the request, on error will not be triggered, and progress will not be updated.\n    const methodKey = getMethodInternalKey(methodInstance);\n    const { abortLast = trueValue } = useHookConfig;\n    const isFirstRequest = !hookInstance.m;\n    hookInstance.m = methodInstance;\n    return (async () => {\n        // Initialize status data, which does not need to be loaded when pulling data, because pulling data does not require returning data.\n        let removeStates = noop;\n        let saveStates = noop;\n        let isNextCalled = falseValue;\n        let responseHandlePromise = promiseResolve(undefinedValue);\n        let offDownloadEvent = noop;\n        let offUploadEvent = noop;\n        const cachedResponse = await queryCache(methodInstance);\n        let fromCache = () => !!cachedResponse;\n        // Whether it is a controlled loading state. When it is true, loading will no longer be set to false in response processing.\n        let controlledLoading = falseValue;\n        if (!isFetcher) {\n            // Store the initial state in cache for subsequent updates\n            saveStates = frontStates => setStateCache(id, methodKey, frontStates, hookInstance);\n            saveStates({ ...frontStates, ...managedStates });\n            // Setting the state removal function will be passed to the effect request in the hook, and it will be set to be called when the component is unloaded.\n            removeStates = () => removeStateCache(id, methodKey);\n        }\n        // The middleware function next callback function allows you to modify mandatory request parameters and even replace the method instance that is about to send the request.\n        const guardNext = guardNextConfig => {\n            isNextCalled = trueValue;\n            const { force: guardNextForceRequest = forceRequest, method: guardNextReplacingMethod = methodInstance } = guardNextConfig || {};\n            const forceRequestFinally = sloughConfig(guardNextForceRequest, [\n                newInstance((AlovaEventBase), methodInstance, sendCallingArgs)\n            ]);\n            const progressUpdater = (stage) => ({ loaded, total }) => {\n                frontStates[stage].v = {\n                    loaded,\n                    total\n                };\n            };\n            methodInstance = guardNextReplacingMethod;\n            // The latest controller needs to be saved every time a request is sent\n            pushItem(hookInstance.sf, saveStates);\n            pushItem(hookInstance.rf, removeStates);\n            // Loading will not be changed when the loading state is controlled\n            // The cache is missed, or loading needs to be set to true when forcing a request.\n            if (!controlledLoading) {\n                loadingState.v = !!forceRequestFinally || !cachedResponse;\n            }\n            // Determine whether to trigger a progress update based on the tracking status of downloading and uploading\n            const { downloading: enableDownload, uploading: enableUpload } = hookInstance.ro.trackedKeys;\n            offDownloadEvent = enableDownload ? methodInstance.onDownload(progressUpdater('downloading')) : offDownloadEvent;\n            offUploadEvent = enableUpload ? methodInstance.onUpload(progressUpdater('uploading')) : offUploadEvent;\n            responseHandlePromise = methodInstance.send(forceRequestFinally);\n            fromCache = () => methodInstance.fromCache || falseValue;\n            return responseHandlePromise;\n        };\n        // Call middleware function\n        const commonContext = {\n            method: methodInstance,\n            cachedResponse,\n            config: useHookConfig,\n            abort: () => methodInstance.abort()\n        };\n        // Whether it is necessary to update the response data and call the response callback\n        const toUpdateResponse = () => hookType !== EnumHookType.USE_WATCHER || !abortLast || hookInstance.m === methodInstance;\n        const controlLoading = (control = trueValue) => {\n            // only reset loading state in first request\n            if (control && isFirstRequest) {\n                loadingState.v = falseValue;\n            }\n            controlledLoading = control;\n        };\n        // Call middleware function\n        const middlewareCompletePromise = isFetcher\n            ? middleware({\n                ...commonContext,\n                args: sendCallingArgs,\n                fetch: (methodInstance, ...args) => {\n                    assertMethod(currentHookAssert, methodInstance);\n                    return useHookToSendRequest(hookInstance, methodInstance, args);\n                },\n                proxyStates: omit(frontStates, 'data'),\n                controlLoading\n            }, guardNext)\n            : middleware({\n                ...commonContext,\n                args: sendCallingArgs,\n                send: (...args) => useHookToSendRequest(hookInstance, methodHandler, args),\n                proxyStates: frontStates,\n                controlLoading\n            }, guardNext);\n        let finallyResponse = undefinedValue;\n        const baseEvent = (AlovaEventBase).spawn(methodInstance, sendCallingArgs);\n        try {\n            // Unified processing of responses\n            const middlewareReturnedData = await middlewareCompletePromise;\n            const afterSuccess = (data) => {\n                // Update cached response data\n                if (!isFetcher) {\n                    toUpdateResponse() && (dataState.v = data);\n                }\n                else if (hookInstance.c.updateState !== falseValue) {\n                    // Update the status in the cache, usually entered in use fetcher\n                    const cachedState = getStateCache(id, methodKey).s;\n                    cachedState && (cachedState.data.v = data);\n                }\n                // If the response data needs to be updated, the corresponding callback function is triggered after the request.\n                if (toUpdateResponse()) {\n                    errorState.v = undefinedValue;\n                    // Loading status will no longer change to false when controlled\n                    !controlledLoading && (loadingState.v = falseValue);\n                    hookInstance.em.emit(KEY_SUCCESS, newInstance((AlovaSuccessEvent), baseEvent, data, fromCache()));\n                    hookInstance.em.emit(KEY_COMPLETE, newInstance((AlovaCompleteEvent), baseEvent, KEY_SUCCESS, data, fromCache(), undefinedValue));\n                }\n                return data;\n            };\n            finallyResponse =\n                // When no data is returned or undefined is returned in the middleware, get the real response data\n                // Otherwise, use the returned data and no longer wait for the response promise. At this time, you also need to call the response callback.\n                middlewareReturnedData !== undefinedValue\n                    ? afterSuccess(middlewareReturnedData)\n                    : isNextCalled\n                        ? // There are two possibilities when middlewareCompletePromise is resolve\n                            // 1. The request is normal\n                            // 2. The request is incorrect, but the error is captured by the middleware function. At this time, the success callback will also be called, that is, afterSuccess(undefinedValue)\n                            await promiseThen(responseHandlePromise, afterSuccess, () => afterSuccess(undefinedValue))\n                        : // If is next called is not called, no data is returned\n                            undefinedValue;\n            // When the next function is not called, update loading to false.\n            !isNextCalled && !controlledLoading && (loadingState.v = falseValue);\n        }\n        catch (error) {\n            if (toUpdateResponse()) {\n                // Controls the output of error messages\n                errorState.v = error;\n                // Loading status will no longer change to false when controlled\n                !controlledLoading && (loadingState.v = falseValue);\n                hookInstance.em.emit(KEY_ERROR, newInstance((AlovaErrorEvent), baseEvent, error));\n                hookInstance.em.emit(KEY_COMPLETE, newInstance((AlovaCompleteEvent), baseEvent, KEY_ERROR, undefinedValue, fromCache(), error));\n            }\n            throw error;\n        }\n        // Unbind download and upload events after responding\n        offDownloadEvent();\n        offUploadEvent();\n        return finallyResponse;\n    })();\n}\n\nconst refCurrent = (ref) => ref.current;\n/**\n * Create request status and uniformly process consistent logic in useRequest, useWatcher, and useFetcher\n * This function will call the creation function of statesHook to create the corresponding request state.\n * When the value is empty, it means useFetcher enters, and data status and cache status are not needed at this time.\n * @param methodInstance request method object\n * @param useHookConfig hook request configuration object\n * @param initialData Initial data data\n * @param immediate Whether to initiate a request immediately\n * @param watchingStates The monitored status, if not passed in, call handleRequest directly.\n * @param debounceDelay Delay time for request initiation\n * @returns Current request status, operation function and event binding function\n */\nfunction createRequestState(hookType, methodHandler, useHookConfig, initialData, immediate = falseValue, watchingStates, debounceDelay = 0) {\n    var _a;\n    // shallow clone config object to avoid passing the same useHookConfig object which may cause vue2 state update error\n    useHookConfig = { ...useHookConfig };\n    const { __referingObj: referingObject = { trackedKeys: {}, bindError: falseValue } } = useHookConfig;\n    let initialLoading = !!immediate;\n    let cachedResponse = undefinedValue;\n    // When sending a request immediately, you need to determine the initial loading value by whether to force the request and whether there is a cache. This has the following two benefits:\n    // 1. Sending the request immediately under react can save one rendering time\n    // 2. In the HTML rendered by SSR, the initial view is in the loading state to avoid the loading view flashing when displayed on the client.\n    if (immediate) {\n        // An error may be reported when calling the get handler method, and try/catch is required.\n        try {\n            const methodInstance = getHandlerMethod$1(methodHandler, coreHookAssert(hookType));\n            const alovaInstance = getContext(methodInstance);\n            const l1CacheResult = alovaInstance.l1Cache.get(buildNamespacedCacheKey(alovaInstance.id, getMethodInternalKey(methodInstance)));\n            // The cache is only checked synchronously, so it does not take effect on asynchronous l1Cache adapters.\n            // It is recommended not to set up the asynchronous l1Cache adapter on the client side\n            if (l1CacheResult && !instanceOf(l1CacheResult, PromiseCls)) {\n                const [data, expireTimestamp] = l1CacheResult;\n                // If there is no expiration time, it means that the data will never expire. Otherwise, you need to determine whether it has expired.\n                if (!expireTimestamp || expireTimestamp > getTime()) {\n                    cachedResponse = data;\n                }\n            }\n            const forceRequestFinally = sloughConfig((_a = useHookConfig.force) !== null && _a !== void 0 ? _a : falseValue);\n            initialLoading = !!forceRequestFinally || !cachedResponse;\n        }\n        catch (_b) { }\n    }\n    const { create, effectRequest, ref, objectify, exposeProvider, transformState2Proxy } = statesHookHelper(promiseStatesHook(), referingObject);\n    const progress = {\n        total: 0,\n        loaded: 0\n    };\n    // Put the externally incoming supervised states into the front states collection together\n    const { managedStates = {} } = useHookConfig;\n    const managedStatesProxy = mapObject(managedStates, (state, key) => transformState2Proxy(state, key));\n    const data = create(cachedResponse !== null && cachedResponse !== void 0 ? cachedResponse : (isFn(initialData) ? initialData() : initialData), 'data');\n    const loading = create(initialLoading, 'loading');\n    const error = create(undefinedValue, 'error');\n    const downloading = create({ ...progress }, 'downloading');\n    const uploading = create({ ...progress }, 'uploading');\n    const frontStates = objectify([data, loading, error, downloading, uploading]);\n    const eventManager = createEventManager();\n    const hookInstance = refCurrent(ref(createHook(hookType, useHookConfig, eventManager, referingObject)));\n    /**\n     * ## react ##Every time the function is executed, the following items need to be reset\n     */\n    hookInstance.fs = frontStates;\n    hookInstance.em = eventManager;\n    hookInstance.c = useHookConfig;\n    hookInstance.ms = managedStatesProxy;\n    const hasWatchingStates = watchingStates !== undefinedValue;\n    // Initialize request event\n    // Unified send request function\n    const handleRequest = (handler = methodHandler, sendCallingArgs) => useHookToSendRequest(hookInstance, handler, sendCallingArgs);\n    // only call once when multiple values changed at the same time\n    const onceRunner = refCurrent(ref(createSyncOnceRunner()));\n    // Call handleRequest in a way that catches the exception\n    // Catching exceptions prevents exceptions from being thrown out\n    const wrapEffectRequest = (ro = referingObject, handler) => {\n        onceRunner(() => {\n            promiseCatch(handleRequest(handler), error => {\n                // the error tracking indicates that the error need to throw.\n                if (!ro.bindError && !ro.trackedKeys.error) {\n                    throw error;\n                }\n            });\n        });\n    };\n    /**\n     * fix: #421\n     * Use ref wraps to prevent react from creating new debounce function in every render\n     * Explicit passing is required because the context will change\n     */\n    const debouncingSendHandler = ref(debounce((_, ro, handler) => wrapEffectRequest(ro, handler), (changedIndex) => isNumber(changedIndex) ? (isArray(debounceDelay) ? debounceDelay[changedIndex] : debounceDelay) : 0));\n    // Do not send requests when rendering on the server side\n    if (!globalConfigMap.ssr) {\n        effectRequest({\n            handler: \n            // When `watchingStates` is an array, it indicates the watching states (including an empty array). When it is undefined, it indicates the non-watching state.\n            hasWatchingStates\n                ? (changedIndex) => debouncingSendHandler.current(changedIndex, referingObject, methodHandler)\n                : () => wrapEffectRequest(referingObject),\n            removeStates: () => forEach(hookInstance.rf, fn => fn()),\n            saveStates: states => forEach(hookInstance.sf, fn => fn(states)),\n            frontStates: { ...frontStates, ...managedStatesProxy },\n            watchingStates,\n            immediate: immediate !== null && immediate !== void 0 ? immediate : trueValue\n        });\n    }\n    return exposeProvider({\n        ...objectify([data, loading, error, downloading, uploading]),\n        abort: () => hookInstance.m && hookInstance.m.abort(),\n        /**\n         * Manually initiate a request by executing this method\n         * @param sendCallingArgs Parameters passed in when calling the send function\n         * @param methodInstance method object\n         * @param isFetcher Whether to call isFetcher\n         * @returns Request promise\n         */\n        send: (sendCallingArgs, methodInstance) => handleRequest(methodInstance, sendCallingArgs),\n        onSuccess(handler) {\n            eventManager.on(KEY_SUCCESS, handler);\n        },\n        onError(handler) {\n            // will not throw error when bindError is true.\n            // it will reset in `exposeProvider` so that ignore the error binding in custom use hooks.\n            referingObject.bindError = trueValue;\n            eventManager.on(KEY_ERROR, handler);\n        },\n        onComplete(handler) {\n            eventManager.on(KEY_COMPLETE, handler);\n        }\n    });\n}\n\n/**\n * Fetch request data and cache request method object\n */\nfunction useFetcher(config = {}) {\n    const props = createRequestState(EnumHookType.USE_FETCHER, noop, config);\n    const { send } = props;\n    deleteAttr(props, 'send');\n    return objAssign(props, {\n        /**\n         * Fetch data fetch will definitely send a request, and if the currently requested data has a corresponding management state, this state will be updated.\n         * @param matcher Method object\n         */\n        fetch: (matcher, ...args) => {\n            assertMethod(fetcherHookAssert, matcher);\n            return send(args, matcher);\n        }\n    });\n}\n\nfunction useRequest(handler, config = {}) {\n    const { immediate = trueValue, initialData } = config;\n    const props = createRequestState(EnumHookType.USE_REQUEST, handler, config, initialData, !!immediate);\n    const { send } = props;\n    return objAssign(props, {\n        send: (...args) => send(args)\n    });\n}\n\nfunction useWatcher(handler, watchingStates, config = {}) {\n    watcherHookAssert(watchingStates && len(watchingStates) > 0, 'expected at least one watching state');\n    const { immediate, debounce = 0, initialData } = config;\n    const props = createRequestState(EnumHookType.USE_WATCHER, handler, config, initialData, !!immediate, // !!immediate means not send request immediately\n    watchingStates, debounce);\n    const { send } = props;\n    return objAssign(props, {\n        send: (...args) => send(args)\n    });\n}\n\nvar createSnapshotMethodsManager = (handler) => {\n    let methodSnapshots = {};\n    return {\n        snapshots: () => methodSnapshots,\n        save(methodInstance, force = falseValue) {\n            const key = getMethodInternalKey(methodInstance);\n            // Because it is impossible to locate the location of the total data in the cache\n            // Therefore, this field is maintained redundantly here.\n            if (!methodSnapshots[key] || force) {\n                methodSnapshots[key] = {\n                    entity: methodInstance\n                };\n            }\n        },\n        get: (entityOrPage) => methodSnapshots[getMethodInternalKey(instanceOf(entityOrPage, (Method)) ? entityOrPage : handler(entityOrPage))],\n        remove(key) {\n            if (key) {\n                delete methodSnapshots[key];\n            }\n            else {\n                methodSnapshots = {};\n            }\n        }\n    };\n};\n\nconst paginationAssert = createAssert('usePagination');\nconst indexAssert = (index, rawData) => paginationAssert(isNumber(index) && index < len(rawData), 'index must be a number that less than list length');\nvar usePagination = (handler, config = {}) => {\n    const { create, computed, ref, watch, exposeProvider, objectify, __referingObj: referingObject } = statesHookHelper(promiseStatesHook());\n    const { preloadPreviousPage = trueValue, preloadNextPage = trueValue, total: totalGetter = res => res.total, data: dataGetter = res => res.data, append = falseValue, initialPage = 1, initialPageSize = 10, watchingStates = [], initialData, immediate = trueValue, middleware, force = noop, ...others } = config;\n    const handlerRef = ref(handler);\n    const isReset = ref(falseValue); // Used to control whether to reset\n    // The number of requests during reset. In order to prevent repeated requests during reset, use this parameter to limit requests.\n    const page = create(initialPage, 'page');\n    const pageSize = create(initialPageSize, 'pageSize');\n    const data = create((initialData ? dataGetter(initialData) || [] : []), 'data');\n    const total = create(initialData ? totalGetter(initialData) : undefinedValue, 'total');\n    // Save snapshots of all method instances used by the current hook\n    const { snapshots: methodSnapshots, get: getSnapshotMethods, save: saveSnapshot, remove: removeSnapshot } = ref(createSnapshotMethodsManager(page => handlerRef.current(page, pageSize.v))).current;\n    const listDataGetter = (rawData) => dataGetter(rawData) || rawData;\n    // Initialize fetcher\n    const fetchStates = useFetcher({\n        __referingObj: referingObject,\n        updateState: falseValue,\n        force: ({ args }) => args[0]\n    });\n    const { loading, fetch, abort: abortFetch, onSuccess: onFetchSuccess } = fetchStates;\n    const fetchingRef = ref(loading);\n    const getHandlerMethod = (refreshPage = page.v) => {\n        const pageSizeVal = pageSize.v;\n        const handlerMethod = handler(refreshPage, pageSizeVal);\n        // Define unified additional names to facilitate management\n        saveSnapshot(handlerMethod);\n        return handlerMethod;\n    };\n    // When monitoring status changes, reset page to 1\n    watch(watchingStates, () => {\n        page.v = initialPage;\n        isReset.current = trueValue;\n    });\n    // Compatible with react, store functions that require proxy here\n    // In this way, the latest operation function can be called in the proxy function and avoid the react closure trap.\n    const delegationActions = ref({});\n    // Calculate data, total, is last page parameters\n    const pageCount = computed(() => {\n        const totalVal = total.v;\n        return totalVal !== undefinedValue ? Math.ceil(totalVal / pageSize.v) : undefinedValue;\n    }, [pageSize, total], 'pageCount');\n    const createDelegationAction = (actionName) => (...args) => delegationActions.current[actionName](...args);\n    const states = useWatcher(getHandlerMethod, [...watchingStates, page.e, pageSize.e], {\n        __referingObj: referingObject,\n        immediate,\n        initialData,\n        managedStates: objectify([data, page, pageSize, total], 's'),\n        middleware(ctx, next) {\n            if (!middleware) {\n                return next();\n            }\n            return middleware({\n                ...ctx,\n                delegatingActions: {\n                    refresh: createDelegationAction('refresh'),\n                    insert: createDelegationAction('insert'),\n                    remove: createDelegationAction('remove'),\n                    replace: createDelegationAction('replace'),\n                    reload: createDelegationAction('reload'),\n                    getState: (stateKey) => {\n                        const states = {\n                            page,\n                            pageSize,\n                            data,\n                            pageCount,\n                            total,\n                            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n                            isLastPage\n                        };\n                        return states[stateKey].v;\n                    }\n                }\n            }, next);\n        },\n        force: event => event.args[1] || (isFn(force) ? force(event) : force),\n        ...others\n    });\n    const { send } = states;\n    const nestedData = states.__proxyState('data');\n    // Determine whether data can be preloaded\n    const canPreload = async (payload) => {\n        const { rawData = nestedData.v, preloadPage, fetchMethod, forceRequest = falseValue, isNextPage = falseValue } = payload;\n        const { e: expireMilliseconds } = getLocalCacheConfigParam(fetchMethod);\n        // If the cache time is less than or equal to the current time, it means that the cache is not set and the data will no longer be pre-pulled at this time.\n        // Or there is already a cache and it is not pre-fetched.\n        if (expireMilliseconds(MEMORY) <= getTime()) {\n            return falseValue;\n        }\n        if (forceRequest) {\n            return trueValue;\n        }\n        if (await queryCache(fetchMethod)) {\n            return falseValue;\n        }\n        const pageCountVal = pageCount.v;\n        const exceedPageCount = pageCountVal\n            ? preloadPage > pageCountVal\n            : isNextPage // If it is judged to preload the next page of data and there is no page count, it is judged by whether the data volume of the last page reaches the page size.\n                ? len(listDataGetter(rawData)) < pageSize.v\n                : falseValue;\n        return preloadPage > 0 && !exceedPageCount;\n    };\n    // Preload next page data\n    const fetchNextPage = async (rawData, force = falseValue) => {\n        const nextPage = page.v + 1;\n        const fetchMethod = getHandlerMethod(nextPage);\n        if (preloadNextPage &&\n            (await canPreload({\n                rawData,\n                preloadPage: nextPage,\n                fetchMethod,\n                isNextPage: trueValue,\n                forceRequest: force\n            }))) {\n            promiseCatch(fetch(fetchMethod, force), noop);\n        }\n    };\n    // Preload previous page data\n    const fetchPreviousPage = async (rawData) => {\n        const prevPage = page.v - 1;\n        const fetchMethod = getHandlerMethod(prevPage);\n        if (preloadPreviousPage &&\n            (await canPreload({\n                rawData,\n                preloadPage: prevPage,\n                fetchMethod\n            }))) {\n            promiseCatch(fetch(fetchMethod), noop);\n        }\n    };\n    // If the returned data is smaller than the page size, it is considered the last page.\n    const isLastPage = computed(() => {\n        const dataRaw = nestedData.v;\n        if (!dataRaw) {\n            return trueValue;\n        }\n        const statesDataVal = listDataGetter(dataRaw);\n        const pageVal = page.v;\n        const pageCountVal = pageCount.v;\n        const dataLen = isArray(statesDataVal) ? len(statesDataVal) : 0;\n        return pageCountVal ? pageVal >= pageCountVal : dataLen < pageSize.v;\n    }, [page, pageCount, nestedData, pageSize], 'isLastPage');\n    // Update current page cache\n    const updateCurrentPageCache = async () => {\n        const snapshotItem = getSnapshotMethods(page.v);\n        if (snapshotItem) {\n            await setCache(snapshotItem.entity, (rawData) => {\n                // When caching is turned off, raw data is undefined\n                if (rawData) {\n                    const cachedListData = listDataGetter(rawData) || [];\n                    splice(cachedListData, 0, len(cachedListData), ...data.v);\n                    return rawData;\n                }\n            });\n        }\n    };\n    onFetchSuccess(({ method, data: rawData }) => {\n        // When fetch has not responded yet and the page is flipped to the page number corresponding to fetch, the list data needs to be updated manually.\n        const snapshotItem = getSnapshotMethods(page.v);\n        if (snapshotItem && getMethodInternalKey(snapshotItem.entity) === getMethodInternalKey(method)) {\n            // If data is appended, data is updated\n            const listData = listDataGetter(rawData); // Update data parameters\n            if (append) {\n                // The current page data needs to be replaced during pull-down loading.\n                const dataRaw = data.v;\n                const pageSizeVal = pageSize.v;\n                // When performing a removal operation, the number of replacements is less than pageSize, and dataRaw % pageSizeVal will be greater than 0.\n                // When adding a new operation, the number of replacements is equal to pageSize. At this time, dataRaw % pageSizeVal will be equal to 0. No replacement is needed at this time.\n                const replaceNumber = len(dataRaw) % pageSizeVal;\n                if (replaceNumber > 0) {\n                    const rawData = [...data.v];\n                    splice(rawData, (page.v - 1) * pageSizeVal, replaceNumber, ...listData);\n                    data.v = rawData;\n                }\n            }\n            else {\n                data.v = listData;\n            }\n        }\n    });\n    const awaitResolve = ref(undefinedValue);\n    const awaitReject = ref(undefinedValue);\n    states\n        .onSuccess(({ data: rawData, args: [refreshPage, isRefresh], method }) => {\n        const { total: cachedTotal } = getSnapshotMethods(method) || {};\n        const typedRawData = rawData;\n        total.v = cachedTotal !== undefinedValue ? cachedTotal : totalGetter(typedRawData);\n        if (!isRefresh) {\n            fetchPreviousPage(typedRawData);\n            fetchNextPage(typedRawData);\n        }\n        const pageSizeVal = pageSize.v;\n        const listData = listDataGetter(typedRawData); // Get array\n        paginationAssert(isArray(listData), 'Got wrong array, did you return the correct array of list in `data` function');\n        // If data is appended, data is updated\n        if (append) {\n            // If it is reset, clear the data first\n            if (isReset.current) {\n                data.v = [];\n            }\n            if (refreshPage === undefinedValue) {\n                data.v = [...data.v, ...listData];\n            }\n            else if (refreshPage) {\n                const rawData = [...data.v];\n                // If the page is refreshed, the data on that page is replaced.\n                splice(rawData, (refreshPage - 1) * pageSizeVal, pageSizeVal, ...listData);\n                data.v = rawData;\n            }\n        }\n        else {\n            data.v = listData;\n        }\n    })\n        .onSuccess(({ data }) => {\n        var _a;\n        (_a = awaitResolve.current) === null || _a === void 0 ? void 0 : _a.call(awaitResolve, data);\n    })\n        .onError(({ error }) => {\n        var _a;\n        (_a = awaitReject.current) === null || _a === void 0 ? void 0 : _a.call(awaitReject, error);\n    })\n        .onComplete(() => {\n        // Whether the request is successful or not, it must be reset is reset\n        isReset.current = falseValue;\n    });\n    // Get the location of a list item\n    const getItemIndex = (item) => {\n        const index = data.v.indexOf(item);\n        paginationAssert(index >= 0, 'item is not found in list');\n        return index;\n    };\n    const { addQueue: add2AsyncQueue, onComplete: onAsyncQueueRunComplete } = ref(createAsyncQueue()).current;\n    /**\n     * Refresh the specified page number data. This function will ignore the cache and force the request to be sent.\n     * If no page number is passed in, the current page will be refreshed.\n     * If a list item is passed in, the page where the list item is located will be refreshed, which is only valid in append mode.\n     * @param pageOrItemPage Refreshed page number or list item\n     */\n    const refresh = async (pageOrItemPage = page.v) => {\n        let refreshPage = pageOrItemPage;\n        let awaitPromise = promiseResolve();\n        if (append) {\n            if (!isNumber(pageOrItemPage)) {\n                const itemIndex = getItemIndex(pageOrItemPage);\n                refreshPage = Math.floor(itemIndex / pageSize.v) + 1;\n            }\n            paginationAssert(refreshPage <= page.v, \"refresh page can't greater than page\");\n            // Update current page data\n            awaitPromise = send(refreshPage, trueValue);\n        }\n        else {\n            paginationAssert(isNumber(refreshPage), 'unable to calculate refresh page by item in pagination mode');\n            // If the number of pages is equal, refresh the current page, otherwise fetch data\n            awaitPromise =\n                refreshPage === page.v\n                    ? send(undefinedValue, trueValue)\n                    : fetch(handler(refreshPage, pageSize.v), trueValue);\n        }\n        return awaitPromise;\n    };\n    // Delete all related caches except the current page and next page of this usehook\n    const invalidatePaginationCache = async (all = falseValue) => {\n        const pageVal = page.v;\n        const snapshotObj = methodSnapshots();\n        let snapshots = objectValues(snapshotObj);\n        if (all) {\n            removeSnapshot();\n        }\n        else {\n            // Filter out data from the previous page, current page, and next page\n            const excludeSnapshotKeys = mapItem(filterItem([getSnapshotMethods(pageVal - 1), getSnapshotMethods(pageVal), getSnapshotMethods(pageVal + 1)], Boolean), ({ entity }) => getMethodInternalKey(entity));\n            snapshots = mapItem(filterItem(objectKeys(snapshotObj), key => !includes(excludeSnapshotKeys, key)), key => {\n                const item = snapshotObj[key];\n                delete snapshotObj[key];\n                return item;\n            });\n        }\n        await invalidateCache(mapItem(snapshots, ({ entity }) => entity));\n    };\n    // The reason for taking it out separately is that\n    // No matter how many times insert, remove, or a combination of them is called synchronously, the reset operation only needs to be executed asynchronously once\n    const resetCache = async () => {\n        fetchingRef.current && abortFetch();\n        // cache invalidation\n        await invalidatePaginationCache();\n        // When the amount of data on the next page does not exceed the page size, the next page is forced to be requested. Because there is a request for sharing, the pull operation needs to be performed asynchronously after interrupting the request.\n        const snapshotItem = getSnapshotMethods(page.v + 1);\n        if (snapshotItem) {\n            const cachedListData = listDataGetter((await queryCache(snapshotItem.entity)) || {}) || [];\n            fetchNextPage(undefinedValue, len(cachedListData) < pageSize.v);\n        }\n    };\n    // Unified update of total number of items\n    const updateTotal = (offset) => {\n        if (offset === 0) {\n            return;\n        }\n        // Update current page\n        const totalVal = total.v;\n        if (isNumber(totalVal)) {\n            const offsetedTotal = Math.max(totalVal + offset, 0);\n            total.v = offsetedTotal;\n            const pageVal = page.v;\n            // Update redundant total field\n            forEach([getSnapshotMethods(pageVal - 1), getSnapshotMethods(pageVal), getSnapshotMethods(pageVal + 1)], item => {\n                item && (item.total = offsetedTotal);\n            });\n        }\n    };\n    /**\n     * Insert a piece of data\n     * If no index is passed in, it will be inserted at the front by default.\n     * If a list item is passed in, it will be inserted after the list item. If the list item is not in the list data, an error will be thrown.\n     * @param item insert\n     * @param position Insert position (index) or list item\n     */\n    const insert = (item, position = 0) => {\n        onAsyncQueueRunComplete(resetCache); // The cache needs to be reset at the end of execution\n        return add2AsyncQueue(async () => {\n            const index = isNumber(position) ? position : getItemIndex(position) + 1;\n            let popItem = undefinedValue;\n            const rawData = [...data.v];\n            // Only when the number of items currently displayed is exactly a multiple of page size, you need to remove an item of data to ensure that the number of operating pages is page size.\n            if (len(rawData) % pageSize.v === 0) {\n                popItem = rawData.pop();\n            }\n            // If the insertion position is empty, it will be inserted to the front by default.\n            splice(rawData, index, 0, item);\n            data.v = rawData;\n            updateTotal(1);\n            // The cache of the current page is updated synchronously\n            await updateCurrentPageCache();\n            // If there is a pop item, put it at the head of the next page cache, consistent with the remove operation.\n            // In this way, the performance will be consistent when insert and remove are called synchronously.\n            if (popItem) {\n                const snapshotItem = getSnapshotMethods(page.v + 1);\n                if (snapshotItem) {\n                    await setCache(snapshotItem.entity, (rawData) => {\n                        if (rawData) {\n                            const cachedListData = listDataGetter(rawData) || [];\n                            cachedListData.unshift(popItem);\n                            cachedListData.pop();\n                            return rawData;\n                        }\n                    });\n                }\n            }\n        });\n    };\n    /**\n     * Remove a piece of data\n     * If a list item is passed in, the list item will be removed. If the list item is not in the list data, an error will be thrown.\n     * @param position Removed index or list item\n     */\n    const remove = (...positions) => {\n        onAsyncQueueRunComplete(resetCache); // The cache needs to be reset at the end of execution\n        return add2AsyncQueue(async () => {\n            const indexes = mapItem(positions, position => {\n                const index = isNumber(position) ? position : getItemIndex(position);\n                indexAssert(index, data.v);\n                return index;\n            });\n            const pageVal = page.v;\n            const nextPage = pageVal + 1;\n            const snapshotItem = getSnapshotMethods(nextPage);\n            const fillingItems = []; // padding data item\n            if (snapshotItem) {\n                await setCache(snapshotItem.entity, (rawData) => {\n                    if (rawData) {\n                        const cachedListData = listDataGetter(rawData);\n                        // Start filling data from the head of the list on the next page\n                        if (isArray(cachedListData)) {\n                            pushItem(fillingItems, ...splice(cachedListData, 0, len(indexes)));\n                        }\n                        return rawData;\n                    }\n                });\n            }\n            const isLastPageVal = isLastPage.v;\n            const fillingItemsLen = len(fillingItems);\n            if (fillingItemsLen > 0 || isLastPageVal) {\n                // Delete data at the specified index\n                const newListData = filterItem(data.v, (_, index) => !includes(indexes, index));\n                // In page turning mode, if it is the last page and all items have been deleted, then turn one page forward.\n                if (!append && isLastPageVal && len(newListData) <= 0) {\n                    page.v = pageVal - 1;\n                }\n                else if (fillingItemsLen > 0) {\n                    pushItem(newListData, ...fillingItems);\n                }\n                data.v = newListData;\n            }\n            else if (fillingItemsLen <= 0 && !isLastPageVal) {\n                // When the last page of data is removed, there is no need to refresh\n                refresh(pageVal);\n            }\n            updateTotal(-len(indexes));\n            // The cache of the current page is updated synchronously\n            return updateCurrentPageCache();\n        });\n    };\n    /**\n     * Replace a piece of data\n     * If the position passed in is a list item, this list item will be replaced. If the list item is not in the list data, an error will be thrown.\n     * @param item replacement\n     * @param position Replace position (index) or list item\n     */\n    const replace = (item, position) => add2AsyncQueue(async () => {\n        paginationAssert(position !== undefinedValue, 'expect specify the replace position');\n        const index = isNumber(position) ? position : getItemIndex(position);\n        indexAssert(index, data.v);\n        const rawData = [...data.v];\n        splice(rawData, index, 1, item);\n        data.v = rawData;\n        // The cache of the current page is updated synchronously\n        await updateCurrentPageCache();\n    });\n    /**\n     * Reload the list starting from page ${initialPage} and clear the cache\n     */\n    const reload = async () => {\n        await invalidatePaginationCache(trueValue);\n        isReset.current = trueValue;\n        page.v === initialPage ? promiseCatch(send(), noop) : (page.v = initialPage);\n        const { resolve, reject, promise } = usePromise();\n        awaitResolve.current = resolve;\n        awaitReject.current = reject;\n        return promise;\n    };\n    // Compatible with react, caches the latest operation function each time, avoiding closure traps\n    delegationActions.current = {\n        refresh,\n        insert,\n        remove,\n        replace,\n        reload\n    };\n    /** @Returns */\n    return exposeProvider({\n        ...states,\n        ...objectify([data, page, pageCount, pageSize, total, isLastPage]),\n        fetching: fetchStates.loading,\n        onFetchSuccess: fetchStates.onSuccess,\n        onFetchError: fetchStates.onError,\n        onFetchComplete: fetchStates.onComplete,\n        refresh,\n        insert,\n        remove,\n        replace,\n        reload\n    });\n};\n\n/**\n * Assert serialHandlers\n * @param hookName hook name\n * @param serialHandlers Serial request method acquisition function\n */\nconst assertSerialHandlers = (hookName, serialHandlers) => createAssert(hookName)(isArray(serialHandlers) && len(serialHandlers) > 0, 'please use an array to represent serial requests');\n/**\n * Create serial request middleware\n * @param serialHandlers Serial request method acquisition function\n * @param hookMiddleware use hook middleware\n * @returns Serial request middleware\n */\nconst serialMiddleware = (serialHandlers, hookMiddleware, serialRequestMethods = []) => {\n    // The first handler is passed to the use hook externally and does not need to be requested again.\n    serialHandlers.shift();\n    return ((ctx, next) => {\n        hookMiddleware === null || hookMiddleware === void 0 ? void 0 : hookMiddleware(ctx, () => promiseResolve(undefinedValue));\n        ctx.controlLoading();\n        const loadingState = ctx.proxyStates.loading;\n        loadingState.v = trueValue;\n        let serialPromise = next();\n        for (const handler of serialHandlers) {\n            serialPromise = promiseThen(serialPromise, value => {\n                const methodItem = handler(value, ...ctx.args);\n                pushItem(serialRequestMethods, methodItem);\n                return methodItem.send();\n            });\n        }\n        return serialPromise.finally(() => {\n            loadingState.v = falseValue;\n        });\n    });\n};\n\n/**\n * Serial request hook, each serialHandlers will receive the result of the previous request\n * Applicable scenario: Serial request for a set of interfaces\n * @param serialHandlers Serial request callback array\n * @param config Configuration parameters\n * @return useSerialRequest related data and operation functions\n */\nvar useSerialRequest = (serialHandlers, config = {}) => {\n    assertSerialHandlers('useSerialRequest', serialHandlers);\n    // eslint-disable-next-line\n    const { ref, __referingObj } = statesHookHelper(promiseStatesHook());\n    const methods = ref([]).current;\n    const exposures = useRequest(serialHandlers[0], {\n        ...config,\n        __referingObj,\n        middleware: serialMiddleware(serialHandlers, config.middleware, methods)\n    });\n    // Decorate the error callback function and set event.method to the instance of the error\n    exposures.onError = decorateEvent(exposures.onError, (handler, event) => {\n        event.method = methods[len(methods) - 1];\n        handler(event);\n    });\n    return exposures;\n};\n\n/**\n * Serial request hook, each serialHandlers will receive the result of the previous request\n * Applicable scenario: After monitoring status changes, serially request a set of interfaces\n * @param serialHandlers Serial request callback array\n * @param config Configuration parameters\n * @return useSerialRequest related data and operation functions\n */\nvar useSerialWatcher = (serialHandlers, watchingStates, config = {}) => {\n    assertSerialHandlers('useSerialWatcher', serialHandlers);\n    // eslint-disable-next-line\n    const { ref, __referingObj } = statesHookHelper(promiseStatesHook());\n    const methods = ref([]).current;\n    const exposures = useWatcher(serialHandlers[0], watchingStates, {\n        ...config,\n        __referingObj,\n        middleware: serialMiddleware(serialHandlers, config.middleware, methods)\n    });\n    // Decorate the error callback function and set event.method to the instance of the error\n    exposures.onError = decorateEvent(exposures.onError, (handler, event) => {\n        event.method = methods[len(methods) - 1];\n        handler(event);\n    });\n    return exposures;\n};\n\nconst STR_VALUE_OF = 'valueOf';\nconst DEFAULT_QUEUE_NAME = 'default';\nconst BEHAVIOR_SILENT = 'silent';\nconst BEHAVIOR_QUEUE = 'queue';\nconst BEHAVIOR_STATIC = 'static';\n/**\n * Global virtual data collection array\n * It will only be an array when the method is created, and undefined at other times\n *\n * Explanation: The purpose of collecting virtual data is to determine whether virtual data is used in a method instance.\n * Includes the following forms:\n * useSQRequest((vDataId) => createMethod({ vDataId }) //Reference function parameters\n * useSQRequest(() => createMethod({ vDataId }) //Directly reference scope parameters\n *\n * Or even:\n * function createMethod(obj) {\n *   return alovaInst.Get('/list', {\n *     params: { status: obj.vDataId ? 1 : 0 }\n *   })\n * }\n * useSQRequest(() => createMethod(obj) //Directly reference scope parameters\n *\n * Ways to use dummy data include:\n * 1. Directly assign values as parameters\n * 2. Use dummy data id\n * 3. Indirect use of virtual data, such as\n *    vData ? 1 : 0\n *    !!vData\n *    vData+1\n *    etc. as calculation parameters.\n */\nlet vDataIdCollectBasket;\nconst setVDataIdCollectBasket = (value) => {\n    vDataIdCollectBasket = value;\n};\n/**\n * The dependent alova instance, its storage adapter, request adapter, etc. will be used to access the SilentMethod instance and send silent submissions\n */\nlet dependentAlovaInstance;\nconst setDependentAlova = (alovaInst) => {\n    dependentAlovaInstance = alovaInst;\n};\n/**\n * Set up a custom serializer\n */\nlet customSerializers = {};\nconst setCustomSerializers = (serializers = {}) => {\n    customSerializers = serializers;\n};\n/**\n * silentFactory status\n * 0 means not started\n * 1 means in progress, changed after calling bootSilentFactory\n * 2 indicates that the request failed, that is, when the maximum number of requests is reached according to the retry rules, or when the retry rules are not matched, the request is changed.\n */\nlet silentFactoryStatus = 0;\nconst setSilentFactoryStatus = (status) => {\n    silentFactoryStatus = status;\n};\n/**\n * The request waiting time in silentQueue, in milliseconds (ms)\n * It indicates the waiting time of the silentMethod that is about to send the request\n * If not set, or set to 0, the silentMethod request is triggered immediately\n *\n * Tips:\n * 1. When set directly, it is effective for the default queue by default.\n * 2. If you need to set other queue settings, you can specify them as objects, such as:\n * [\n *   Indicates waiting 5000ms for the queue setting request named customName\n *   { name: 'customName', wait: 5000 },\n *\n *   //Indicates that in all queues with the prefix prefix, the request setting with method instance name xxx is set to wait 5000ms\n *   { name: /^prefix/, wait: silentMethod => silentMethod.entity.config.name === 'xxx' ? 5000 : 0 },\n * ]\n *\n * >>> It only works if the request succeeds, if it fails it will use the retry policy parameters\n */\nlet queueRequestWaitSetting = [];\nconst setQueueRequestWaitSetting = (requestWaitSetting = 0) => {\n    queueRequestWaitSetting = isArray(requestWaitSetting)\n        ? requestWaitSetting\n        : [\n            {\n                queue: DEFAULT_QUEUE_NAME,\n                wait: requestWaitSetting\n            }\n        ];\n};\nconst BootEventKey = Symbol('GlobalSQBoot');\nconst BeforeEventKey = Symbol('GlobalSQBefore');\nconst SuccessEventKey = Symbol('GlobalSQSuccess');\nconst ErrorEventKey = Symbol('GlobalSQError');\nconst FailEventKey$1 = Symbol('GlobalSQFail');\n/** Global silent event management object */\nconst globalSQEventManager = createEventManager();\n/** Silent assert */\nconst silentAssert = createAssert('useSQRequest');\n\n/**\n * Update the status of the corresponding method\n * @param method request method object\n * @param handleUpdate update callback\n * @returns Whether the update is successful or not. If the corresponding status is not found, the update will not be successful.\n */\nasync function updateState(matcher, handleUpdate) {\n    let updated = falseValue;\n    // Only process the first method instance that meets the conditions. If there is no instance that meets the conditions, it will not be processed.\n    if (matcher) {\n        const { update } = promiseStatesHook();\n        const methodKey = getMethodInternalKey(matcher);\n        const { id } = getContext(matcher);\n        const { s: frontStates, h: hookInstance } = getStateCache(id, methodKey);\n        const updateStateCollection = isFn(handleUpdate)\n            ? { data: handleUpdate }\n            : handleUpdate;\n        let updatedDataColumnData = undefinedValue;\n        if (frontStates) {\n            // Loop through the updated data and assign it to the supervised state\n            forEach(objectKeys(updateStateCollection), stateName => {\n                coreAssert(stateName in frontStates, `state named \\`${stateName}\\` is not found`);\n                const targetStateProxy = frontStates[stateName];\n                let updatedData = updateStateCollection[stateName](targetStateProxy.v);\n                // shallow clone the updatedData so that can effect in react.\n                updatedData = isArray(updatedData)\n                    ? [...updatedData]\n                    : isObject(updatedData)\n                        ? { ...updatedData }\n                        : updatedData;\n                // Record the updated value of the data field, used to update cached data\n                if (stateName === 'data') {\n                    updatedDataColumnData = updatedData;\n                }\n                // Update directly using update without checking referring object.tracked keys\n                update(updatedData, frontStates[stateName].s, stateName, hookInstance.ro);\n            });\n            updated = trueValue;\n        }\n        // If data is updated, cache and persistent data need to be updated at the same time\n        if (updatedDataColumnData !== undefinedValue) {\n            setCache(matcher, updatedDataColumnData);\n        }\n    }\n    return updated;\n}\n\nvar dateSerializer = {\n    forward: data => (instanceOf(data, Date) ? data.getTime() : undefinedValue),\n    backward: ts => newInstance(Date, ts)\n};\n\nvar regexpSerializer = {\n    forward: data => (instanceOf(data, RegExp) ? data.source : undefined),\n    backward: source => newInstance(RegExp, source)\n};\n\nconst createSerializerPerformer = (customSerializers = {}) => {\n    /**\n     * Merge built-in serializers and custom serializers\n     */\n    const serializers = {\n        date: dateSerializer,\n        regexp: regexpSerializer,\n        ...customSerializers\n    };\n    /**\n     * serialized data\n     */\n    const serialize = (payload) => {\n        if (isObject(payload)) {\n            payload = walkObject(isArray(payload) ? [...payload] : { ...payload }, value => {\n                let finallyApplySerializerName = undefinedValue;\n                // Find a matching serializer and serialize the value. If not found, return the original value.\n                const serializedValue = objectKeys(serializers).reduce((currentValue, serializerName) => {\n                    if (!finallyApplySerializerName) {\n                        const serializedValueItem = serializers[serializerName].forward(currentValue);\n                        if (serializedValueItem !== undefinedValue) {\n                            finallyApplySerializerName = serializerName;\n                            currentValue = serializedValueItem;\n                        }\n                    }\n                    return currentValue;\n                }, value);\n                // You need to use the original value to judge, otherwise packaging classes such as new Number(1) will also be [object Object]\n                const toStringTag = ObjectCls.prototype.toString.call(value);\n                if (toStringTag === '[object Object]') {\n                    value = { ...value };\n                }\n                else if (isArray(value)) {\n                    value = [...value];\n                }\n                return finallyApplySerializerName !== undefinedValue ? [finallyApplySerializerName, serializedValue] : value;\n            });\n        }\n        return payload;\n    };\n    /**\n     * Deserialize data\n     */\n    const deserialize = (payload) => isObject(payload)\n        ? walkObject(payload, value => {\n            if (isArray(value) && len(value) === 2) {\n                const foundSerializer = serializers[value[0]];\n                value = foundSerializer ? foundSerializer.backward(value[1]) : value;\n            }\n            return value;\n        }, falseValue)\n        : payload;\n    return {\n        serialize,\n        deserialize\n    };\n};\n\nconst symbolVDataId = Symbol('vdid');\nconst symbolOriginal = Symbol('original');\nconst regVDataId = /\\[vd:([0-9a-z]+)\\]/;\n\n/**\n * Unified vData collection function\n * It will be called in the following 4 places\n * 1. When accessing sub-properties\n * 2. When participating in calculation and triggering [Symbol.toPrimitive]\n * 3. When getting the id of vData\n * 4. When getting its original value\n *\n * @param returnValue Return value, if it is a function then call it\n * @returns collection function\n */\nconst vDataCollectUnified = (target) => {\n    const vDataId = target === null || target === void 0 ? void 0 : target[symbolVDataId];\n    vDataId && vDataIdCollectBasket && (vDataIdCollectBasket[vDataId] = undefinedValue);\n};\n// export const vDataGetter = (key: string) => vDataCollectGetter((thisObj: any) => thisObj.__proto__[key].call(thisObj));\n\n/**\n * Dummy data is stringified. If the parameter is not dummy data, the original data is returned.\n * @param target dummy data\n * @param returnOriginalIfNotVData If it is not virtual data, return the original value.\n * @returns Virtual data id or original data\n */\nconst stringifyVData = (target, returnOriginalIfNotVData = trueValue) => {\n    vDataCollectUnified(target);\n    const vDataIdRaw = target === null || target === void 0 ? void 0 : target[symbolVDataId];\n    const vDataId = vDataIdRaw ? `[vd:${vDataIdRaw}]` : undefinedValue;\n    return vDataId || (returnOriginalIfNotVData ? target : undefinedValue);\n};\n/**\n * Create a getter function for virtual data id collection\n * @param valueReturnFn return value function\n * @returns getter function\n */\nfunction stringifyWithThis() {\n    return stringifyVData(this);\n}\n\n/**\n * Null wrapper class implementation\n */\nconst Null = function Null() { };\nNull.prototype = ObjectCls.create(nullValue, {\n    [STR_VALUE_OF]: valueObject(stringifyWithThis)\n});\n\n/**\n * Undefined wrapper class implementation\n */\nconst Undefined = function Undefined() { };\nUndefined.prototype = ObjectCls.create(nullValue, {\n    [STR_VALUE_OF]: valueObject(stringifyWithThis)\n});\n\n/**\n * Create dummy response data\n * @returns Virtual response data proxy instance\n */\nvar createVirtualResponse = (structure, vDataId = uuid()) => {\n    const transform2VData = (value, vDataIdInner = uuid()) => {\n        if (value === nullValue) {\n            value = newInstance(Null);\n        }\n        else if (value === undefinedValue) {\n            value = newInstance(Undefined);\n        }\n        else {\n            const newValue = ObjectCls(value);\n            defineProperty(newValue, STR_VALUE_OF, stringifyWithThis);\n            defineProperty(newValue, symbolOriginal, value);\n            value = newValue;\n        }\n        defineProperty(value, symbolVDataId, vDataIdInner);\n        return value;\n    };\n    const virtualResponse = transform2VData(structure, vDataId);\n    if (isPlainObject(virtualResponse) || isArray(virtualResponse)) {\n        walkObject(virtualResponse, value => transform2VData(value));\n    }\n    return virtualResponse;\n};\n\n/**\n * Get original value of variable with dummy data\n * This function will also perform vData collection\n * @param target target value\n * @param deepDehydrate Whether the depth of dehydration value\n * @returns target value with primitive type\n */\nconst dehydrateVDataUnified = (target, deepDehydrate = trueValue) => {\n    const dehydrateItem = (value) => {\n        vDataCollectUnified(value);\n        if (value === null || value === void 0 ? void 0 : value[symbolVDataId]) {\n            if (instanceOf(value, Undefined)) {\n                value = undefinedValue;\n            }\n            else if (instanceOf(value, Null)) {\n                value = nullValue;\n            }\n            else if (instanceOf(value, Number) || instanceOf(value, String) || instanceOf(value, Boolean)) {\n                value = value[symbolOriginal];\n            }\n        }\n        return value;\n    };\n    const newTarget = dehydrateItem(target);\n    // If it is an object or array, deep traversal is required to obtain the virtual data value.\n    if (deepDehydrate && (isObject(newTarget) || isArray(newTarget))) {\n        walkObject(newTarget, value => dehydrateItem(value));\n    }\n    return newTarget;\n};\n/**\n * The version above where deepDehydrate is true\n */\nvar dehydrateVData = (target) => dehydrateVDataUnified(target);\n\nconst vDataKey = '__$k';\nconst vDataValueKey = '__$v';\nconst getAlovaStorage = () => {\n    // Provide prompt when silent factory is not started\n    silentAssert(!!dependentAlovaInstance, 'alova instance is not found, Do you forget to set `alova` or call `bootSilentFactory`?');\n    return dependentAlovaInstance.l2Cache;\n};\nlet serializerPerformer = undefinedValue;\nconst silentMethodIdQueueMapStorageKey = 'alova.SQ'; // Queue collection cache key composed of Silent method instance id\nconst silentMethodStorageKeyPrefix = 'alova.SM.'; // silentMethod instance cache key prefix\n/**\n * Persistence of data collections with dummy data and serializable data\n * @param key persistence key\n * @param payload Persistent data\n */\nconst storageSetItem = async (key, payload) => {\n    const storage = getAlovaStorage();\n    if (isObject(payload)) {\n        payload = walkObject(isArray(payload) ? [...payload] : { ...payload }, (value, key, parent) => {\n            var _a;\n            if (key === vDataValueKey && parent[vDataKey]) {\n                return value;\n            }\n            // If a silent method instance is serialized, the alova instance is filtered out\n            if (key === 'context' && ((_a = value === null || value === void 0 ? void 0 : value.constructor) === null || _a === void 0 ? void 0 : _a.name) === 'Alova') {\n                return undefinedValue;\n            }\n            const vDataId = value === null || value === void 0 ? void 0 : value[symbolVDataId];\n            let primitiveValue = dehydrateVDataUnified(value, falseValue);\n            // You need to use the original value to judge, otherwise packaging classes such as new Number(1) will also be [object Object]\n            const toStringTag = globalToString(primitiveValue);\n            if (toStringTag === '[object Object]') {\n                value = { ...value };\n                primitiveValue = {};\n            }\n            else if (isArray(value)) {\n                value = [...value];\n                primitiveValue = [];\n            }\n            if (vDataId) {\n                const valueWithVData = {\n                    [vDataKey]: vDataId,\n                    // For objects and arrays, all their internal properties will be put to the outside through `...value`, so the internal ones do not need to be traversed and converted.\n                    // Therefore, empty the array or object to avoid repeated conversions and contamination of the original object.\n                    [vDataValueKey]: primitiveValue,\n                    ...value\n                };\n                // If it is a string type, there will be items like arrays such as 0, 1, and 2 as subscripts and values as characters, and they need to be filtered out.\n                if (instanceOf(value, String)) {\n                    for (let i = 0; i < len(value); i += 1) {\n                        valueWithVData === null || valueWithVData === void 0 ? true : delete valueWithVData[i];\n                    }\n                }\n                // If it is converted into virtual data, the converted value is assigned to it internally, and is uniformly processed by value in the following logic.\n                value = valueWithVData;\n            }\n            return value;\n        });\n    }\n    serializerPerformer = serializerPerformer || createSerializerPerformer(customSerializers);\n    await storage.set(key, serializerPerformer.serialize(payload));\n};\n/**\n * Take out the persistent data and convert the data into virtual data and serialized data\n * @param key Key to persistent data\n */\nconst storageGetItem = async (key) => {\n    const storagedResponse = await getAlovaStorage().get(key);\n    serializerPerformer = serializerPerformer || createSerializerPerformer(customSerializers);\n    return isObject(storagedResponse)\n        ? walkObject(serializerPerformer.deserialize(storagedResponse), value => {\n            // Convert virtual data format back to virtual data instance\n            if (isObject(value) && (value === null || value === void 0 ? void 0 : value[vDataKey])) {\n                const vDataId = value[vDataKey];\n                const vDataValue = createVirtualResponse(value[vDataValueKey], vDataId);\n                forEach(objectKeys(value), key => {\n                    if (!includes([vDataKey, vDataValueKey], key)) {\n                        vDataValue[key] = value[key];\n                    }\n                });\n                value = vDataValue;\n            }\n            return value;\n        }, falseValue)\n        : storagedResponse;\n};\n/**\n * Remove persistent data\n * @param key Key to persistent data\n */\nconst storageRemoveItem = async (key) => {\n    await getAlovaStorage().remove(key);\n};\n\n/**\n * Serialize and save silentMethod instance\n * @param silentMethodInstance silentMethod instance\n */\nconst persistSilentMethod = (silentMethodInstance) => storageSetItem(silentMethodStorageKeyPrefix + silentMethodInstance.id, silentMethodInstance);\n/**\n * Put the configuration information of silent request into the corresponding storage\n * Logic: Construct a key and use this key to put the configuration information of the silent method into the corresponding storage, and then store the key in the unified management key storage.\n * @param silentMethod SilentMethodInstance\n * @param queue Operation queue name\n */\nconst push2PersistentSilentQueue = async (silentMethodInstance, queueName) => {\n    await persistSilentMethod(silentMethodInstance);\n    // Save the silent method instance id to queue storage\n    const silentMethodIdQueueMap = ((await storageGetItem(silentMethodIdQueueMapStorageKey)) ||\n        {});\n    const currentQueue = (silentMethodIdQueueMap[queueName] = silentMethodIdQueueMap[queueName] || []);\n    pushItem(currentQueue, silentMethodInstance.id);\n    await storageSetItem(silentMethodIdQueueMapStorageKey, silentMethodIdQueueMap);\n};\n/**\n * Remove or replace silentMethod instances in the cache\n * @param queue Operation queue name\n * @param targetSilentMethodId Target silentMethod instance id\n * @param newSilentMethod The new silentMethod instance to replace. If not passed, it means deleted.\n */\nconst spliceStorageSilentMethod = async (queueName, targetSilentMethodId, newSilentMethod) => {\n    // Remove the silent method instance id from the queue\n    const silentMethodIdQueueMap = ((await storageGetItem(silentMethodIdQueueMapStorageKey)) ||\n        {});\n    const currentQueue = silentMethodIdQueueMap[queueName] || [];\n    const index = currentQueue.findIndex(id => id === targetSilentMethodId);\n    if (index >= 0) {\n        if (newSilentMethod) {\n            splice(currentQueue, index, 1, newSilentMethod.id);\n            await persistSilentMethod(newSilentMethod);\n        }\n        else {\n            splice(currentQueue, index, 1);\n        }\n        await storageRemoveItem(silentMethodStorageKeyPrefix + targetSilentMethodId);\n        // Delete this queue when it is empty\n        len(currentQueue) <= 0 && delete silentMethodIdQueueMap[queueName];\n        if (len(objectKeys(silentMethodIdQueueMap)) > 0) {\n            await storageSetItem(silentMethodIdQueueMapStorageKey, silentMethodIdQueueMap);\n        }\n        else {\n            // Remove the queue collection when it is empty\n            await storageRemoveItem(silentMethodIdQueueMapStorageKey);\n        }\n    }\n};\n\n/** Silent method queue collection */\nlet silentQueueMap = {};\n/**\n * Merge queueMap into silentMethod queue collection\n * @param queueMap silentMethod queue collection\n */\nconst merge2SilentQueueMap = (queueMap) => {\n    forEach(objectKeys(queueMap), targetQueueName => {\n        const currentQueue = (silentQueueMap[targetQueueName] = silentQueueMap[targetQueueName] || []);\n        pushItem(currentQueue, ...queueMap[targetQueueName]);\n    });\n};\n/**\n * Deeply traverse the target data and replace dummy data with real data\n * @param target target data\n * @param vDataResponse Collection of dummy data and real data\n * @returns Is there any replacement data?\n */\nconst deepReplaceVData = (target, vDataResponse) => {\n    // Search for a single value and replace a dummy data object or dummy data id with an actual value\n    const replaceVData = (value) => {\n        const vData = stringifyVData(value);\n        // If directly a dummy data object and in a vDataResponse, replace the Map with the value in the vDataResponse\n        // If it is a string, it may contain virtual data id and in vDataResponse, it also needs to be replaced with the actual value Map\n        // The virtual data not in this vDataResponse will remain unchanged. It may be the virtual data Map requested next time.\n        if (vData in vDataResponse) {\n            return vDataResponse[vData];\n        }\n        if (isString(value)) {\n            return value.replace(newInstance(RegExpCls, regVDataId.source, 'g'), mat => mat in vDataResponse ? vDataResponse[mat] : mat);\n        }\n        return value;\n    };\n    if (isObject(target) && !stringifyVData(target, falseValue)) {\n        walkObject(target, replaceVData);\n    }\n    else {\n        target = replaceVData(target);\n    }\n    return target;\n};\n/**\n * Update the method instance in the queue and replace the dummy data with actual data\n * @param vDataResponse A collection of virtual IDs and corresponding real data\n * @param targetQueue target queue\n */\nconst updateQueueMethodEntities = (vDataResponse, targetQueue) => PromiseCls.all(mapItem(targetQueue, async (silentMethodItem) => {\n    // Traverse the entity object deeply. If virtual data or virtual data ID is found, replace it with actual data.\n    deepReplaceVData(silentMethodItem.entity, vDataResponse);\n    // If the method instance is updated, re-persist this silent method instance\n    silentMethodItem.cache && (await persistSilentMethod(silentMethodItem));\n}));\n/**\n * Replace dummy data with response data\n * @param response real response data\n * @param virtualResponse dummy response data\n * @returns The corresponding real data set composed of virtual data id\n */\nconst replaceVirtualResponseWithResponse = (virtualResponse, response) => {\n    let vDataResponse = {};\n    const vDataId = stringifyVData(virtualResponse, falseValue);\n    vDataId && (vDataResponse[vDataId] = response);\n    if (isObject(virtualResponse)) {\n        for (const i in virtualResponse) {\n            vDataResponse = {\n                ...vDataResponse,\n                ...replaceVirtualResponseWithResponse(virtualResponse[i], response === null || response === void 0 ? void 0 : response[i])\n            };\n        }\n    }\n    return vDataResponse;\n};\n/**\n * Start the SilentMethod queue\n * 1. Silent submission will be put into the queue and requests will be sent in order. Only after the previous request responds will it continue to send subsequent requests.\n * 2. The number of retries is only triggered when there is no response. If the server responds incorrectly or is disconnected, it will not retry.\n * 3. When the number of retries is reached and still fails, when nextRound (next round) is set, delay the time specified by nextRound and then request again, otherwise it will try again after refreshing.\n * 4. If there is resolveHandler and rejectHandler, they will be called after the request is completed (whether successful or failed) to notify the corresponding request to continue responding.\n *\n * @param queue SilentMethodqueue\n */\nconst setSilentMethodActive = (silentMethodInstance, active) => {\n    if (active) {\n        silentMethodInstance.active = active;\n    }\n    else {\n        delete silentMethodInstance.active;\n    }\n};\nconst defaultBackoffDelay = 1000;\nconst bootSilentQueue = (queue, queueName) => {\n    /**\n     * The callback function is controlled by waiting parameters according to the request. If it is not set or is less than or equal to 0, it will be triggered immediately.\n     * @param queueName queue name\n     * @param callback callback function\n     */\n    const emitWithRequestDelay = (queueName) => {\n        const nextSilentMethod = queue[0];\n        if (nextSilentMethod) {\n            const targetSetting = queueRequestWaitSetting.find(({ queue }) => instanceOf(queue, RegExpCls) ? regexpTest(queue, queueName) : queue === queueName);\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            const callback = () => queue[0] && silentMethodRequest(queue[0]);\n            const delay = (targetSetting === null || targetSetting === void 0 ? void 0 : targetSetting.wait) ? sloughConfig(targetSetting.wait, [nextSilentMethod, queueName]) : 0;\n            delay && delay > 0 ? setTimeoutFn(callback, delay) : callback();\n        }\n    };\n    /**\n     * Run a single silentMethod instance\n     * @param silentMethodInstance silentMethod instance\n     * @param retryTimes Number of retries\n     */\n    const silentMethodRequest = (silentMethodInstance, retryTimes = 0) => {\n        // Set the current silent method instance to active status\n        setSilentMethodActive(silentMethodInstance, trueValue);\n        const { cache, id, behavior, entity, retryError = /.*/, maxRetryTimes = 0, backoff = { delay: defaultBackoffDelay }, resolveHandler = noop, rejectHandler = noop, emitter: methodEmitter, handlerArgs = [], virtualResponse, force } = silentMethodInstance;\n        // Trigger pre-request event\n        globalSQEventManager.emit(BeforeEventKey, newInstance((GlobalSQEvent), behavior, entity, silentMethodInstance, queueName, retryTimes));\n        promiseThen(entity.send(force), async (data) => {\n            // The request is successful, remove the successful silent method, and continue with the next request\n            shift(queue);\n            // If the request is successful, remove the successful silent method instance from storage and continue with the next request.\n            cache && (await spliceStorageSilentMethod(queueName, id));\n            // If there is a resolve handler, call it to notify the outside\n            resolveHandler(data);\n            // Only when there is a virtualResponse, virtual data is traversed and replaced, and global events are triggered.\n            // Generally, it is silent behavior, but queue behavior is not required.\n            if (behavior === BEHAVIOR_SILENT) {\n                // Replace dummy data in subsequent method instances in the queue with real data\n                // Only after unlocking can you access the hierarchical structure of virtualResponse normally.\n                const vDataResponse = replaceVirtualResponseWithResponse(virtualResponse, data);\n                const { targetRefMethod, updateStates } = silentMethodInstance; // It is accurate to obtain it in real time\n                // If this silentMethod has targetRefMethod, call updateState again to update the data\n                // This is an implementation of delayed data updates\n                if (instanceOf(targetRefMethod, Method) && updateStates && len(updateStates) > 0) {\n                    const updateStateCollection = {};\n                    forEach(updateStates, stateName => {\n                        // After the request is successful, replace the data with dummy data with real data\n                        updateStateCollection[stateName] = dataRaw => deepReplaceVData(dataRaw, vDataResponse);\n                    });\n                    const updated = updateState(targetRefMethod, updateStateCollection);\n                    // If the status modification is unsuccessful, modify the cached data.\n                    if (!updated) {\n                        await setCache(targetRefMethod, (dataRaw) => deepReplaceVData(dataRaw, vDataResponse));\n                    }\n                }\n                // Perform dummy data replacement on subsequent silent method instances of the current queue\n                await updateQueueMethodEntities(vDataResponse, queue);\n                // Trigger global success event\n                globalSQEventManager.emit(SuccessEventKey, newInstance((GlobalSQSuccessEvent), behavior, entity, silentMethodInstance, queueName, retryTimes, data, vDataResponse));\n            }\n            // Set to inactive state\n            setSilentMethodActive(silentMethodInstance, falseValue);\n            // Continue to the next silent method processing\n            emitWithRequestDelay(queueName);\n        }, reason => {\n            if (behavior !== BEHAVIOR_SILENT) {\n                // When the behavior is not silent and the request fails, rejectHandler is triggered.\n                // and removed from the queue and will not be retried.\n                shift(queue);\n                rejectHandler(reason);\n            }\n            else {\n                // Each request error will trigger an error callback\n                const runGlobalErrorEvent = (retryDelay) => globalSQEventManager.emit(ErrorEventKey, newInstance((GlobalSQErrorEvent), behavior, entity, silentMethodInstance, queueName, retryTimes, reason, retryDelay));\n                // In silent behavior mode, determine whether retry is needed\n                // Retry is only effective when the response error matches the retryError regular match\n                const { name: errorName = '', message: errorMsg = '' } = reason || {};\n                let regRetryErrorName;\n                let regRetryErrorMsg;\n                if (instanceOf(retryError, RegExp)) {\n                    regRetryErrorMsg = retryError;\n                }\n                else if (isObject(retryError)) {\n                    regRetryErrorName = retryError.name;\n                    regRetryErrorMsg = retryError.message;\n                }\n                const matchRetryError = (regRetryErrorName && regexpTest(regRetryErrorName, errorName)) ||\n                    (regRetryErrorMsg && regexpTest(regRetryErrorMsg, errorMsg));\n                // If there are still retry times, try again\n                if (retryTimes < maxRetryTimes && matchRetryError) {\n                    // The next retry times need to be used to calculate the delay time, so +1 is needed here.\n                    const retryDelay = delayWithBackoff(backoff, retryTimes + 1);\n                    runGlobalErrorEvent(retryDelay);\n                    setTimeoutFn(() => {\n                        retryTimes += 1;\n                        silentMethodRequest(silentMethodInstance, retryTimes);\n                        methodEmitter.emit('retry', newInstance((ScopedSQRetryEvent), behavior, entity, silentMethodInstance, handlerArgs, retryTimes, retryDelay));\n                    }, \n                    // When there are still retry times, use timeout as the next request time.\n                    retryDelay);\n                }\n                else {\n                    setSilentFactoryStatus(2);\n                    runGlobalErrorEvent();\n                    // When the number of failures is reached, or the error message does not match the retry, the failure callback is triggered.\n                    methodEmitter.emit('fallback', newInstance((ScopedSQErrorEvent), behavior, entity, silentMethodInstance, handlerArgs, reason));\n                    globalSQEventManager.emit(FailEventKey$1, newInstance((GlobalSQFailEvent), behavior, entity, silentMethodInstance, queueName, retryTimes, reason));\n                }\n            }\n            // Set to inactive state\n            setSilentMethodActive(silentMethodInstance, falseValue);\n        });\n    };\n    emitWithRequestDelay(queueName);\n};\n/**\n * Put a new silentMethod instance into the queue\n * @param silentMethodInstance silentMethod instance\n * @param cache Does silentMethod have cache?\n * @param targetQueueName target queue name\n * @param onBeforePush Events before silentMethod instance push\n */\nconst pushNewSilentMethod2Queue = async (silentMethodInstance, cache, targetQueueName = DEFAULT_QUEUE_NAME, onBeforePush = () => []) => {\n    silentMethodInstance.cache = cache;\n    const currentQueue = (silentQueueMap[targetQueueName] =\n        silentQueueMap[targetQueueName] || []);\n    const isNewQueue = len(currentQueue) <= 0;\n    const beforePushReturns = await Promise.all(onBeforePush());\n    const isPush2Queue = !beforePushReturns.some(returns => returns === falseValue);\n    // Under silent behavior, if there is no fallback event callback bound, it will be persisted.\n    // If false is returned in onBeforePushQueue, it will no longer be placed in the queue.\n    if (isPush2Queue) {\n        cache && (await push2PersistentSilentQueue(silentMethodInstance, targetQueueName));\n        pushItem(currentQueue, silentMethodInstance);\n        // If it is a new queue and the status is started, execute it\n        isNewQueue && silentFactoryStatus === 1 && bootSilentQueue(currentQueue, targetQueueName);\n    }\n    return isPush2Queue;\n};\n\n/**\n * Locate the location of the silentMethod instance\n * @param silentMethodInstance silentMethod instance\n */\nconst getBelongQueuePosition = (silentMethodInstance) => {\n    let queue = undefinedValue;\n    let queueName = '';\n    let position = 0;\n    for (const queueNameLoop in silentQueueMap) {\n        position = silentQueueMap[queueNameLoop].indexOf(silentMethodInstance);\n        if (position >= 0) {\n            queue = silentQueueMap[queueNameLoop];\n            queueName = queueNameLoop;\n            break;\n        }\n    }\n    return [queue, queueName, position];\n};\n/**\n * silentMethod instance\n * Requests that need to enter silentQueue will be packaged into silentMethod instances, which will carry various parameters of the request strategy.\n */\nclass SilentMethod {\n    constructor(entity, behavior, emitter, id = uuid(), force, retryError, maxRetryTimes, backoff, resolveHandler, rejectHandler, handlerArgs, vDatas) {\n        const thisObj = this;\n        thisObj.entity = entity;\n        thisObj.behavior = behavior;\n        thisObj.id = id;\n        thisObj.emitter = emitter;\n        thisObj.force = !!force;\n        thisObj.retryError = retryError;\n        thisObj.maxRetryTimes = maxRetryTimes;\n        thisObj.backoff = backoff;\n        thisObj.resolveHandler = resolveHandler;\n        thisObj.rejectHandler = rejectHandler;\n        thisObj.handlerArgs = handlerArgs;\n        thisObj.vDatas = vDatas;\n    }\n    /**\n     * Allow cache-time persistent updates to the current instance\n     */\n    async save() {\n        this.cache && (await persistSilentMethod(this));\n    }\n    /**\n     * Replace the current instance with a new silentMethod instance in the queue\n     * If there is a persistent cache, the cache will also be updated.\n     * @param newSilentMethod new silentMethod instance\n     */\n    async replace(newSilentMethod) {\n        const targetSilentMethod = this;\n        silentAssert(newSilentMethod.cache === targetSilentMethod.cache, 'the cache of new silentMethod must equal with this silentMethod');\n        const [queue, queueName, position] = getBelongQueuePosition(targetSilentMethod);\n        if (queue) {\n            splice(queue, position, 1, newSilentMethod);\n            targetSilentMethod.cache && (await spliceStorageSilentMethod(queueName, targetSilentMethod.id, newSilentMethod));\n        }\n    }\n    /**\n     * Remove the current instance. If there is persistent data, it will also be removed synchronously.\n     */\n    async remove() {\n        const targetSilentMethod = this;\n        const [queue, queueName, position] = getBelongQueuePosition(targetSilentMethod);\n        if (queue) {\n            splice(queue, position, 1);\n            targetSilentMethod.cache && (await spliceStorageSilentMethod(queueName, targetSilentMethod.id));\n        }\n    }\n    /**\n     * Set the method instance corresponding to the delayed update status and the corresponding status name\n     * It will find the corresponding status data and update vData to the actual data after responding to this silentMethod\n     *\n     * @param method method instance\n     * @param updateStateName Updated status name, the default is data, you can also set multiple\n     */\n    setUpdateState(method, updateStateName = 'data') {\n        if (method) {\n            this.targetRefMethod = method;\n            this.updateStates = isArray(updateStateName) ? updateStateName : [updateStateName];\n        }\n    }\n}\n\n/**\n * Deserialize the silentMethod instance according to the name of the serializer.\n * @param methodInstance Request method instance\n * @returns Request method instance\n */\nvar convertPayload2SilentMethod = (payload) => {\n    const { id, behavior, entity, retryError, maxRetryTimes, backoff, resolveHandler, rejectHandler, handlerArgs, targetRefMethod, force } = payload;\n    // Method class instantiation\n    const deserializeMethod = (methodPayload) => {\n        const { type, url, config, data } = methodPayload;\n        return newInstance(Method, type, dependentAlovaInstance, url, config, data);\n    };\n    const silentMethodInstance = newInstance(SilentMethod, deserializeMethod(entity), behavior, createEventManager(), id, force, retryError, maxRetryTimes, backoff, resolveHandler, rejectHandler, handlerArgs);\n    silentMethodInstance.cache = trueValue;\n    // Target ref method deserialization\n    if (targetRefMethod) {\n        silentMethodInstance.targetRefMethod = deserializeMethod(targetRefMethod);\n    }\n    // Put extra content on the silent method instance\n    forEach(objectKeys(payload), key => {\n        if (!includes([\n            'id',\n            'behavior',\n            'emitter',\n            'entity',\n            'retryError',\n            'maxRetryTimes',\n            'backoff',\n            'resolveHandler',\n            'rejectHandler',\n            'handlerArgs',\n            'targetRefMethod',\n            'force'\n        ], key)) {\n            silentMethodInstance[key] = payload[key];\n        }\n    });\n    return silentMethodInstance;\n};\n\n/**\n * Load silent queue data from storage\n * @returns All queue data\n */\nvar loadSilentQueueMapFromStorage = async () => {\n    const silentMethodIdQueueMap = ((await storageGetItem(silentMethodIdQueueMapStorageKey)) ||\n        {});\n    const silentQueueMap = {};\n    const readingPromises = [];\n    forEach(objectKeys(silentMethodIdQueueMap), queueName => {\n        const currentQueue = (silentQueueMap[queueName] = silentQueueMap[queueName] || []);\n        pushItem(readingPromises, ...mapItem(silentMethodIdQueueMap[queueName], async (silentMethodId) => {\n            const serializedSilentMethodPayload = await storageGetItem(silentMethodStorageKeyPrefix + silentMethodId);\n            serializedSilentMethodPayload &&\n                pushItem(currentQueue, convertPayload2SilentMethod(serializedSilentMethodPayload));\n        }));\n    });\n    await PromiseCls.all(readingPromises);\n    return silentQueueMap;\n};\n\n/**\n * Bind silentSubmit startup event\n * @param {SilentSubmitBootHandler} handler event callback function\n * @returns unbind function\n */\nconst onSilentSubmitBoot = (handler) => globalSQEventManager.on(BootEventKey, handler);\n/**\n * Bind silentSubmit success event\n * @param {SilentSubmitSuccessHandler} handler event callback function\n * @returns unbind function\n */\nconst onSilentSubmitSuccess = (handler) => globalSQEventManager.on(SuccessEventKey, handler);\n/**\n * Bind silentSubmit error event\n * Every time there is a request error, an error callback is triggered.\n * @param {SilentSubmitErrorHandler} handler event callback function\n * @returns unbind function\n */\nconst onSilentSubmitError = (handler) => globalSQEventManager.on(ErrorEventKey, handler);\n/**\n * Binding silentSubmit failure event\n * The failure event will be triggered when the maximum number of requests is reached, or when the error message does not match\n * @param {SilentSubmitFailHandler} handler event callback function\n * @returns unbind function\n */\nconst onSilentSubmitFail = (handler) => globalSQEventManager.on(FailEventKey$1, handler);\n/**\n * Bind silentSubmit to initiate a pre-request event\n * @param {BeforeSilentSubmitHandler} handler event callback function\n * @returns unbind function\n */\nconst onBeforeSilentSubmit = (handler) => globalSQEventManager.on(BeforeEventKey, handler);\n/**\n * Start silent submission, which will load the silent method in the cache and start silent submission\n * If no delay time is passed in, the sync starts immediately\n * @param {SilentFactoryBootOptions} options Delay in milliseconds\n */\nconst bootSilentFactory = (options) => {\n    if (silentFactoryStatus === 0) {\n        const { alova, delay = 500 } = options;\n        setDependentAlova(alova);\n        setCustomSerializers(options.serializers);\n        setQueueRequestWaitSetting(options.requestWait);\n        setTimeoutFn(async () => {\n            // Delayed loading puts the page’s queue at the front\n            merge2SilentQueueMap(await loadSilentQueueMapFromStorage());\n            // Loop start queue silent submission\n            // Multiple queues are executed in parallel\n            forEach(objectKeys(silentQueueMap), queueName => {\n                bootSilentQueue(silentQueueMap[queueName], queueName);\n            });\n            setSilentFactoryStatus(1); // Set status to Started\n            globalSQEventManager.emit(BootEventKey, undefinedValue);\n        }, delay);\n    }\n};\n\n/**\n * A global silentMethod instance that will have a value from before the first success event is triggered to after the last success event is triggered (synchronization period)\n * In this way, the current silentMethod instance can be obtained in updateStateEffect in onSuccess.\n */\nlet currentSilentMethod = undefinedValue;\n/**\n * Create SilentQueue middleware function\n * @param config Configuration object\n * @returns middleware function\n */\nvar createSilentQueueMiddlewares = (handler, config) => {\n    const { behavior = 'queue', queue = DEFAULT_QUEUE_NAME, retryError, maxRetryTimes, backoff } = config || {};\n    const eventEmitter = createEventManager();\n    let handlerArgs;\n    let behaviorFinally;\n    let queueFinally = DEFAULT_QUEUE_NAME;\n    let forceRequest = falseValue;\n    let silentMethodInstance;\n    /**\n     * method instance creation function\n     * @param args Call the function passed in by send\n     * @returns method instance\n     */\n    const createMethod = (...args) => {\n        silentAssert(isFn(handler), 'method handler must be a function. eg. useSQRequest(() => method)');\n        setVDataIdCollectBasket({});\n        handlerArgs = args;\n        return handler(...args);\n    };\n    // Decorate success/error/complete event\n    const decorateRequestEvent = (requestExposure) => {\n        // Set event callback decorator\n        requestExposure.onSuccess = decorateEvent(requestExposure.onSuccess, (handler, event) => {\n            currentSilentMethod = silentMethodInstance;\n            handler(newInstance((ScopedSQSuccessEvent), behaviorFinally, event.method, silentMethodInstance, event.args, event.data));\n        });\n        requestExposure.onError = decorateEvent(requestExposure.onError, (handler, event) => {\n            handler(newInstance((ScopedSQErrorEvent), behaviorFinally, event.method, silentMethodInstance, event.args, event.error));\n        });\n        requestExposure.onComplete = decorateEvent(requestExposure.onComplete, (handler, event) => {\n            handler(newInstance((ScopedSQCompleteEvent), behaviorFinally, event.method, silentMethodInstance, event.args, event.status, event.data, event.error));\n        });\n    };\n    /**\n     * middleware function\n     * @param context Request context, containing request-related values\n     * @param next continue executing function\n     * @returns Promise object\n     */\n    const middleware = ({ method, args, cachedResponse, proxyStates, config }, next) => {\n        const { silentDefaultResponse, vDataCaptured, force = falseValue } = config;\n        // Because the behavior return value may change, it should be called for each request to re-obtain the return value.\n        const baseEvent = AlovaEventBase.spawn(method, args);\n        behaviorFinally = sloughConfig(behavior, [baseEvent]);\n        queueFinally = sloughConfig(queue, [baseEvent]);\n        forceRequest = sloughConfig(force, [baseEvent]);\n        // Empty temporary collection variables\n        // They need to be cleared before returning\n        const resetCollectBasket = () => {\n            setVDataIdCollectBasket((handlerArgs = undefinedValue));\n        };\n        // If v data captured is set, first determine whether the request-related data contains virtual data.\n        if (isFn(vDataCaptured)) {\n            let hasVData = vDataIdCollectBasket && len(objectKeys(vDataIdCollectBasket)) > 0;\n            if (!hasVData) {\n                const { url, data } = method;\n                const { params, headers } = getConfig(method);\n                walkObject({ url, params, data, headers }, value => {\n                    if (!hasVData && (stringifyVData(value, falseValue) || regexpTest(regVDataId, value))) {\n                        hasVData = trueValue;\n                    }\n                    return value;\n                });\n            }\n            // If v data captured has return data, use it as the response data, otherwise continue the request\n            const customResponse = hasVData ? vDataCaptured(method) : undefinedValue;\n            if (customResponse !== undefinedValue) {\n                resetCollectBasket(); // Reset when captured by v data captured\n                return promiseResolve(customResponse);\n            }\n        }\n        if (behaviorFinally !== BEHAVIOR_STATIC) {\n            // Wait for the method in the queue to complete execution\n            const createSilentMethodPromise = () => {\n                const queueResolvePromise = newInstance(PromiseCls, (resolveHandler, rejectHandler) => {\n                    silentMethodInstance = newInstance((SilentMethod), method, behaviorFinally, eventEmitter, undefinedValue, !!forceRequest, retryError, maxRetryTimes, backoff, resolveHandler, rejectHandler, handlerArgs, vDataIdCollectBasket && objectKeys(vDataIdCollectBasket));\n                    resetCollectBasket(); // Reset when Behavior is queue and silent\n                });\n                // On before push and on pushed events are bound synchronously, so they need to be queued asynchronously to trigger the event normally.\n                promiseThen(promiseResolve(undefinedValue), async () => {\n                    const createPushEvent = () => newInstance((ScopedSQEvent), behaviorFinally, method, silentMethodInstance, args);\n                    // Put the silent method into the queue and persist it\n                    const isPushed = await pushNewSilentMethod2Queue(silentMethodInstance, \n                    // After the onFallback event is bound, even the silent behavior mode is no longer stored.\n                    // onFallback will be called synchronously, so it needs to be determined asynchronously whether there are fallbackHandlers\n                    len(eventEmitter.eventMap.fallback || []) <= 0 && behaviorFinally === BEHAVIOR_SILENT, queueFinally, \n                    // Execute the callback before putting it into the queue. If false is returned, it will prevent putting it into the queue.\n                    () => eventEmitter.emit('beforePushQueue', createPushEvent()));\n                    // Only after putting it into the queue, the callback after putting it into the queue will be executed.\n                    isPushed && eventEmitter.emit('pushedQueue', createPushEvent());\n                });\n                return queueResolvePromise;\n            };\n            if (behaviorFinally === BEHAVIOR_QUEUE) {\n                // Forced request, or loading status needs to be updated when cache is hit\n                const needSendRequest = forceRequest || !cachedResponse;\n                if (needSendRequest) {\n                    // Manually set to true\n                    proxyStates.loading.v = trueValue;\n                }\n                // When using the cache, use the cache directly, otherwise enter the request queue\n                return needSendRequest ? createSilentMethodPromise() : promiseThen(promiseResolve(cachedResponse));\n            }\n            const silentMethodPromise = createSilentMethodPromise();\n            // Create virtual response data in silent mode. Virtual response data can generate arbitrary virtual data.\n            const virtualResponse = (silentMethodInstance.virtualResponse = createVirtualResponse(isFn(silentDefaultResponse) ? silentDefaultResponse() : undefinedValue));\n            promiseThen(silentMethodPromise, realResponse => {\n                // Update after obtaining real data\n                proxyStates.data.v = realResponse;\n            });\n            // In Silent mode, the virtual response value is returned immediately, and then updated when the real data is returned.\n            return promiseResolve(virtualResponse);\n        }\n        resetCollectBasket(); // Reset when Behavior is static\n        return next();\n    };\n    return {\n        c: createMethod,\n        m: middleware,\n        d: decorateRequestEvent,\n        // event binding function\n        b: {\n            /**\n             * Bind fallback event\n             * @param handler Fallback event callback\n             */\n            onFallback: (handler) => {\n                eventEmitter.on('fallback', handler);\n            },\n            /**\n             * Event before binding to queue\n             * @param handler Event callback before enqueuing\n             */\n            onBeforePushQueue: (handler) => {\n                eventEmitter.on('beforePushQueue', handler);\n            },\n            /**\n             * Event after binding to queue\n             * @param handler Event callback after being queued\n             */\n            onPushedQueue: (handler) => {\n                eventEmitter.on('pushedQueue', handler);\n            },\n            /**\n             * retry event\n             * @param handler Retry event callback\n             */\n            onRetry: (handler) => {\n                eventEmitter.on('retry', handler);\n            }\n        }\n    };\n};\n\nfunction useSQRequest(handler, config = {}) {\n    const { exposeProvider, __referingObj: referingObj } = statesHookHelper(promiseStatesHook());\n    const { middleware = noop } = config;\n    const { c: methodCreateHandler, m: silentMiddleware, b: binders, d: decorateEvent } = createSilentQueueMiddlewares(handler, config);\n    const states = useRequest(methodCreateHandler, {\n        ...config,\n        __referingObj: referingObj,\n        middleware: (ctx, next) => {\n            const silentMidPromise = silentMiddleware(ctx, next);\n            middleware(ctx, () => silentMidPromise);\n            return silentMidPromise;\n        }\n    });\n    decorateEvent(states);\n    return exposeProvider({\n        ...states,\n        ...binders\n    });\n}\n\n/**\n * Determine whether two values are equal in a way that is compatible with virtual data\n * @param prevValue Antecedent value\n * @param nextValue consequent value\n * @returns Are they equal?\n */\nvar equals = (prevValue, nextValue) => {\n    // If equal, return directly\n    if (prevValue === nextValue) {\n        return trueValue;\n    }\n    return stringifyVData(prevValue) === stringifyVData(nextValue);\n};\n\n/**\n * Filter all silentMethod instances that meet the criteria by method name or regular expression\n * @param methodNameMatcher method name matcher\n * @param queueName Find the queue name, the default is default queue\n * @param filterActive Whether to filter out active instances\n * @returns array of silentMethod instances\n */\nconst filterSilentMethods = async (methodNameMatcher, queueName = DEFAULT_QUEUE_NAME, filterActive = falseValue) => {\n    const matchSilentMethods = (targetQueue = []) => targetQueue.filter(silentMethodItem => {\n        if (methodNameMatcher === undefinedValue) {\n            return trueValue;\n        }\n        const name = getConfig(silentMethodItem.entity).name || '';\n        const retain = instanceOf(methodNameMatcher, RegExp)\n            ? regexpTest(methodNameMatcher, name)\n            : name === methodNameMatcher;\n        return retain && (filterActive ? silentMethodItem.active : trueValue);\n    });\n    return [\n        ...matchSilentMethods(silentQueueMap[queueName]),\n        // If the silent factory is not currently started, you also need to match the silent methods in the persistent storage.\n        ...(silentFactoryStatus === 0 ? matchSilentMethods((await loadSilentQueueMapFromStorage())[queueName]) : [])\n    ];\n};\n/**\n * Find the first silentMethod instance that meets the condition by method name or regular expression\n * @param methodNameMatcher method name matcher\n * @param queueName Find the queue name, the default is default queue\n * @param filterActive Whether to filter out active instances\n * @returns silentMethod instance, undefined when not found\n */\nconst getSilentMethod = async (methodNameMatcher, queueName = DEFAULT_QUEUE_NAME, filterActive = falseValue) => (await filterSilentMethods(methodNameMatcher, queueName, filterActive))[0];\n\n/**\n * Determine whether the target data is virtual data\n * @param target target data\n * @returns Is it virtual data?\n */\nvar isVData = (target) => !!stringifyVData(target, falseValue) || regexpTest(regVDataId, target);\n\n/**\n * Update the status of the corresponding method\n * Unlike updateState, in addition to updating the state immediately, it will also update again after responding in silent mode in order to replace the virtual data with actual data.\n * @param method request method object\n * @param handleUpdate update callback\n */\nconst updateStateEffect = async (matcher, handleUpdate) => {\n    // Save the target method instance to the current silent method instance\n    if (currentSilentMethod) {\n        currentSilentMethod.setUpdateState(matcher, isFn(updateState) ? undefinedValue : objectKeys(updateState));\n        await currentSilentMethod.save();\n    }\n    return updateState(matcher, handleUpdate);\n};\n\nconst useAutoRequest = (handler, config = {}) => {\n    let notifiable = trueValue;\n    const { enableFocus = trueValue, enableVisibility = trueValue, enableNetwork = trueValue, pollingTime = 0, throttle = 1000 } = config;\n    const { onMounted, onUnmounted, __referingObj: referingObject } = statesHookHelper(promiseStatesHook());\n    const states = useRequest(handler, {\n        ...config,\n        __referingObj: referingObject\n    });\n    const notify = () => {\n        if (notifiable) {\n            states.send();\n            if (throttle > 0) {\n                notifiable = falseValue;\n                setTimeout(() => {\n                    notifiable = trueValue;\n                }, throttle);\n            }\n        }\n    };\n    let offNetwork = noop;\n    let offFocus = noop;\n    let offVisiblity = noop;\n    let offPolling = noop;\n    onMounted(() => {\n        if (!globalConfigMap.ssr) {\n            offNetwork = enableNetwork ? useAutoRequest.onNetwork(notify, config) : offNetwork;\n            offFocus = enableFocus ? useAutoRequest.onFocus(notify, config) : offFocus;\n            offVisiblity = enableVisibility ? useAutoRequest.onVisibility(notify, config) : offVisiblity;\n            offPolling = pollingTime > 0 ? useAutoRequest.onPolling(notify, config) : offPolling;\n        }\n    });\n    onUnmounted(() => {\n        offNetwork();\n        offFocus();\n        offVisiblity();\n        offPolling();\n    });\n    return states;\n};\nconst on = (type, handler) => {\n    window.addEventListener(type, handler);\n    return () => window.removeEventListener(type, handler);\n};\nuseAutoRequest.onNetwork = notify => on('online', notify);\nuseAutoRequest.onFocus = notify => on('focus', notify);\nuseAutoRequest.onVisibility = notify => {\n    const handle = () => document.visibilityState === 'visible' && notify();\n    return on('visibilitychange', handle);\n};\nuseAutoRequest.onPolling = (notify, config) => {\n    const timer = setInterval(notify, config.pollingTime);\n    return () => clearInterval(timer);\n};\n\nconst hookPrefix$1 = 'useCaptcha';\nconst captchaAssert = createAssert(hookPrefix$1);\nvar useCaptcha = (handler, config = {}) => {\n    const { initialCountdown, middleware } = config;\n    captchaAssert(initialCountdown === undefinedValue || initialCountdown > 0, 'initialCountdown must be greater than 0');\n    const { create, ref, objectify, exposeProvider, __referingObj: referingObject } = statesHookHelper(promiseStatesHook());\n    const countdown = create(0, 'countdown');\n    const requestReturned = useRequest(handler, {\n        ...config,\n        __referingObj: referingObject,\n        immediate: falseValue,\n        managedStates: objectify([countdown], 's'),\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        middleware: middleware ? (ctx, next) => middleware({ ...ctx, send }, next) : undefinedValue\n    });\n    const timer = ref(undefinedValue);\n    const send = (...args) => newInstance(PromiseCls, (resolve, reject) => {\n        if (countdown.v <= 0) {\n            requestReturned\n                .send(...args)\n                .then(result => {\n                countdown.v = config.initialCountdown || 60;\n                timer.current = setInterval(() => {\n                    countdown.v -= 1;\n                    if (countdown.v <= 0) {\n                        clearInterval(timer.current);\n                    }\n                }, 1000);\n                resolve(result);\n            })\n                .catch(reason => reject(reason));\n        }\n        else {\n            reject(newInstance(AlovaError, hookPrefix$1, 'the countdown is not over yet'));\n        }\n    });\n    return exposeProvider({\n        ...requestReturned,\n        send,\n        ...objectify([countdown])\n    });\n};\n\nconst RestoreEventKey = Symbol('FormRestore');\nconst getStoragedKey = (methodInstance, id) => `alova/form-${id || getMethodInternalKey(methodInstance)}`;\nconst sharedStates = {};\nconst cloneFormData = (form) => {\n    const shallowClone = (value) => (isArray(value) ? [...value] : isPlainObject(value) ? { ...value } : value);\n    return walkObject(shallowClone(form), shallowClone);\n};\nvar useForm = (handler, config = {}) => {\n    const typedSharedStates = sharedStates;\n    const { id, initialForm, store, resetAfterSubmiting, immediate = falseValue, middleware } = config;\n    promiseStatesHook();\n    const { create, ref: useFlag$, onMounted, watch, objectify, exposeProvider, __referingObj: referingObject } = statesHookHelper(promiseStatesHook());\n    const isStoreObject = isPlainObject(store);\n    const enableStore = isStoreObject ? store.enable : store;\n    // If the id in config also has a corresponding shared state, it will also be returned.\n    // The reason for continuing the execution is to be compatible with the problem that the number of hook executions in react cannot be changed, otherwise it will throw \"Rendered fewer hooks than expected. This may be caused by an accidental early return statement.\"\n    const sharedState = id ? typedSharedStates[id] : undefinedValue;\n    const form = create(cloneFormData(initialForm), 'form');\n    const methodHandler = handler;\n    const eventManager = createEventManager();\n    // Use computed properties to avoid calling methodHandler every time this use hook is executed.\n    const initialMethodInstance = useFlag$(sloughConfig(methodHandler, [form.v]));\n    const storageContext = getContext(initialMethodInstance.current).l2Cache;\n    const storagedKey = getStoragedKey(initialMethodInstance.current, id);\n    const reseting = useFlag$(falseValue);\n    const serializerPerformer = useFlag$(createSerializerPerformer(isStoreObject ? store.serializers : undefinedValue));\n    // Whether the shared state created by the current hook is initiated. The hook that initiates the creation needs to return the latest state. Otherwise, because the hook is called in react, the latest state cannot be obtained from the hook initiated.\n    const isCreateShardState = useFlag$(false);\n    const originalHookProvider = useRequest((...args) => methodHandler(form.v, ...args), {\n        ...config,\n        __referingObj: referingObject,\n        // Middleware function, also supports subscriber middleware\n        middleware: middleware\n            ? (ctx, next) => middleware({\n                ...ctx,\n                // eslint-disable-next-line\n                delegatingActions: { updateForm, reset }\n            }, next)\n            : undefinedValue,\n        // 1. When persistence is required, it will be triggered after data recovery\n        // 2. When there is a shared state, it means that it has been initialized before (regardless of whether there is an immediate request), and subsequent requests will no longer be automatically initiated. This is to be compatible with the issue of repeated requests when multiple forms initiate requests immediately.\n        immediate: enableStore || sharedState ? falseValue : immediate\n    });\n    /**\n     * Reset form data\n     */\n    const reset = () => {\n        reseting.current = trueValue;\n        const clonedFormData = cloneFormData(initialForm);\n        clonedFormData && (form.v = clonedFormData);\n        enableStore && storageContext.remove(storagedKey);\n    };\n    /**\n     * Update form data\n     * @param newForm new form data\n     */\n    const updateForm = (newForm) => {\n        form.v = {\n            ...form.v,\n            ...newForm\n        };\n    };\n    const hookProvider = exposeProvider({\n        // The first parameter is fixed to form data\n        ...originalHookProvider,\n        ...objectify([form]),\n        updateForm,\n        reset,\n        // Persistent data recovery event binding\n        onRestore(handler) {\n            eventManager.on(RestoreEventKey, handler);\n        }\n    });\n    // Only when there is an id, it is saved to sharedStates.\n    // In react, because a new form will be generated after updating the form, it needs to be resaved every time it is called.\n    if (id) {\n        // If there is no shared status yet, it means that the current hook is a created hook.\n        if (!sharedState) {\n            isCreateShardState.current = trueValue;\n        }\n        // Only the shared state of the created hook is saved\n        if (isCreateShardState.current) {\n            typedSharedStates[id] = {\n                hookProvider: hookProvider,\n                config\n            };\n        }\n    }\n    const { send, onSuccess } = hookProvider;\n    onMounted(() => {\n        // Update data when persistence is required\n        if (enableStore && !sharedState) {\n            // Get storage and update data\n            // It needs to be called in onMounted, otherwise it will cause it to be called repeatedly in react.\n            const storagedForm = serializerPerformer.current.deserialize(storageContext.get(storagedKey));\n            // When there is draft data, the data is restored asynchronously, otherwise the on restore event cannot be bound normally.\n            if (storagedForm) {\n                form.v = storagedForm;\n                // Trigger persistent data recovery event\n                eventManager.emit(RestoreEventKey, undefinedValue);\n            }\n            enableStore && immediate && send(...[]);\n        }\n    });\n    // Monitor changes and store them synchronously. If it is triggered by reset, no further serialization is required.\n    watch([form], () => {\n        if (reseting.current || !enableStore) {\n            reseting.current = falseValue;\n            return;\n        }\n        storageContext.set(storagedKey, serializerPerformer.current.serialize(form.v));\n    });\n    // If data needs to be cleared after submission, call reset\n    onSuccess(() => {\n        resetAfterSubmiting && reset();\n    });\n    // If there is a saved sharedState, return it\n    // If it is the shared state created by the current hook, the latest one is returned instead of the cached one.\n    return sharedState && !isCreateShardState.current ? sharedState.hookProvider : hookProvider;\n};\n\nconst RetryEventKey = Symbol('RetriableRetry');\nconst FailEventKey = Symbol('RetriableFail');\nconst hookPrefix = 'useRetriableRequest';\nconst assert$2 = createAssert(hookPrefix);\nvar useRetriableRequest = (handler, config = {}) => {\n    const { retry = 3, backoff = { delay: 1000 }, middleware = noop } = config;\n    const { ref: useFlag$, exposeProvider, __referingObj: referingObject } = statesHookHelper(promiseStatesHook());\n    const eventManager = createEventManager();\n    const retryTimes = useFlag$(0);\n    const stopManuallyError = useFlag$(undefinedValue); // Stop error object, has value when stop is triggered manually\n    const methodInstanceLastest = useFlag$(undefinedValue);\n    const argsLatest = useFlag$(undefinedValue);\n    const requesting = useFlag$(falseValue); // Is it being requested?\n    const retryTimer = useFlag$(undefinedValue);\n    const stopPromiseObj = useFlag$(usePromise());\n    const emitOnFail = (method, args, error) => {\n        // On fail needs to be triggered asynchronously, and on error and on complete should be triggered first.\n        setTimeoutFn(() => {\n            eventManager.emit(FailEventKey, newInstance((RetriableFailEvent), AlovaEventBase.spawn(method, args), error, retryTimes.current));\n            stopManuallyError.current = undefinedValue;\n            retryTimes.current = 0; // Reset the number of retries\n        });\n    };\n    const nestedHookProvider = useRequest(handler, {\n        ...config,\n        __referingObj: referingObject,\n        middleware(ctx, next) {\n            middleware({\n                ...ctx,\n                delegatingActions: {\n                    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n                    stop\n                }\n            }, () => promiseResolve());\n            const { proxyStates, args, send, method, controlLoading } = ctx;\n            controlLoading();\n            const { loading } = proxyStates;\n            const setLoading = (value = falseValue) => {\n                loading.v = value;\n            };\n            const resolveFail = (error) => {\n                setLoading();\n                proxyStates.error.v = error;\n                clearTimeout(retryTimer.current); // Clear retry timer\n                emitOnFail(method, args, error);\n            };\n            if (!loading.v) {\n                promiseCatch(stopPromiseObj.current.promise, error => {\n                    resolveFail(error);\n                    stopPromiseObj.current = usePromise();\n                });\n            }\n            setLoading(trueValue);\n            requesting.current = trueValue;\n            methodInstanceLastest.current = method;\n            argsLatest.current = args;\n            /**\n             * Consider this situation: user call stop() and send another request immediately, but now the previous request haven't finished. `next()` will raises the branch on completion.\n             *\n             * By using Promise.race(), we can cause the returned promise to be rejected immediately when call `stop()`\n             */\n            return next()\n                .then(val => {\n                // set `loading` to false when request is successful\n                setLoading();\n                return val;\n            }, \n            // Trigger retry mechanism when request fails\n            error => {\n                // There is no manual trigger to stop, and a retry is triggered when the number of retries does not reach the maximum.\n                if (!stopManuallyError.current && (isNumber(retry) ? retryTimes.current < retry : retry(error, ...args))) {\n                    retryTimes.current += 1;\n                    // Calculate retry delay time\n                    const retryDelay = delayWithBackoff(backoff, retryTimes.current);\n                    // Delay the corresponding time and try again\n                    retryTimer.current = setTimeoutFn(() => {\n                        // trigger retry event\n                        eventManager.emit(RetryEventKey, newInstance((RetriableRetryEvent), AlovaEventBase.spawn(method, args), retryTimes.current, retryDelay));\n                        // If stopped manually, retry will no longer be triggered.\n                        promiseCatch(send(...args), noop); // Captured errors will no longer be thrown out, otherwise errors will be thrown when retrying.\n                    }, retryDelay);\n                }\n                else {\n                    error = stopManuallyError.current || error; // If stop manually error has a value, it means that the stop is triggered through the stop function.\n                    resolveFail(error);\n                }\n                // Return reject to execute the subsequent error process\n                return promiseReject(error);\n            })\n                .finally(() => {\n                requesting.current = falseValue;\n            });\n        }\n    });\n    /**\n     * Stop retrying, only valid when called during retrying\n     * If the request is in progress, trigger an interrupt request and let the request error throw an error. Otherwise, manually modify the status and trigger onFail.\n     * The onFail event will be triggered immediately after stopping\n     */\n    const stop = () => {\n        assert$2(nestedHookProvider.__proxyState('loading').v, 'there is no requests being retried');\n        stopManuallyError.current = newInstance(AlovaError, hookPrefix, 'stop retry manually');\n        if (requesting.current) {\n            nestedHookProvider.abort();\n        }\n        else {\n            stopPromiseObj.current.reject(stopManuallyError.current);\n        }\n    };\n    /**\n     * Retry event binding\n     * They will be triggered after the retry is initiated\n     * @param handler Retry event callback\n     */\n    const onRetry = (handler) => {\n        eventManager.on(RetryEventKey, event => handler(event));\n    };\n    /**\n     * failed event binding\n     * They will be triggered when there are no more retries, such as when the maximum number of retries is reached, when the retry callback returns false, or when stop is manually called to stop retries.\n     * The onError event of alova will be triggered every time an error is requested.\n     *\n     * Note: If there are no retries, onError, onComplete and onFail will be triggered at the same time.\n     *\n     * @param handler Failure event callback\n     */\n    const onFail = (handler) => {\n        eventManager.on(FailEventKey, event => handler(event));\n    };\n    return exposeProvider({\n        ...nestedHookProvider,\n        stop,\n        onRetry,\n        onFail\n    });\n};\n\nconst SSEOpenEventKey = Symbol('SSEOpen');\nconst SSEMessageEventKey = Symbol('SSEMessage');\nconst SSEErrorEventKey = Symbol('SSEError');\nvar SSEHookReadyState;\n(function (SSEHookReadyState) {\n    SSEHookReadyState[SSEHookReadyState[\"CONNECTING\"] = 0] = \"CONNECTING\";\n    SSEHookReadyState[SSEHookReadyState[\"OPEN\"] = 1] = \"OPEN\";\n    SSEHookReadyState[SSEHookReadyState[\"CLOSED\"] = 2] = \"CLOSED\";\n})(SSEHookReadyState || (SSEHookReadyState = {}));\nconst assert$1 = createAssert('useSSE');\nconst MessageType = {\n    Open: 'open',\n    Error: 'error',\n    Message: 'message'\n};\nvar useSSE = (handler, config = {}) => {\n    const { initialData, withCredentials, interceptByGlobalResponded = trueValue, \n    /** abortLast = trueValue, */\n    immediate = falseValue } = config;\n    // ! Temporarily does not support specifying abortLast\n    const abortLast = trueValue;\n    let { memorize } = promiseStatesHook();\n    memorize !== null && memorize !== void 0 ? memorize : (memorize = $self);\n    const { create, ref, onMounted, onUnmounted, objectify, exposeProvider } = statesHookHelper(promiseStatesHook());\n    const usingArgs = ref([]);\n    const eventSource = ref(undefinedValue);\n    const sendPromiseObject = ref(undefinedValue);\n    const data = create(initialData, 'data');\n    const readyState = create(SSEHookReadyState.CLOSED, 'readyState');\n    let methodInstance = getHandlerMethod(handler);\n    let responseUnified;\n    const eventManager = createEventManager();\n    // UseCallback object that stores custom events, where key is eventName\n    const customEventMap = ref(new Map());\n    const onOpen = (handler) => {\n        eventManager.on(SSEOpenEventKey, handler);\n    };\n    const onMessage = (handler) => {\n        eventManager.on(SSEMessageEventKey, handler);\n    };\n    const onError = (handler) => {\n        eventManager.on(SSEErrorEventKey, handler);\n    };\n    const responseSuccessHandler = ref($self);\n    const responseErrorHandler = ref(throwFn);\n    const responseCompleteHandler = ref(noop);\n    /**\n     * Set up a response interceptor, which needs to be called after each send\n     */\n    const setResponseHandler = (instance) => {\n        // responded removed since 3.0\n        const { responded } = getOptions(instance);\n        responseUnified = responded;\n        if (isFn(responseUnified)) {\n            responseSuccessHandler.current = responseUnified;\n        }\n        else if (responseUnified && isPlainObject(responseUnified)) {\n            const { onSuccess: successHandler, onError: errorHandler, onComplete: completeHandler } = responseUnified;\n            responseSuccessHandler.current = isFn(successHandler) ? successHandler : responseSuccessHandler.current;\n            responseErrorHandler.current = isFn(errorHandler) ? errorHandler : responseErrorHandler.current;\n            responseCompleteHandler.current = isFn(completeHandler) ? completeHandler : responseCompleteHandler.current;\n        }\n    };\n    /**\n     * Process response tasks and do not cache data on failure\n     * @param handlerReturns Data returned by the interceptor\n     * @returns Processed response\n     */\n    const handleResponseTask = async (handlerReturns) => {\n        const { headers, transform: transformFn = $self } = getConfig(methodInstance);\n        const returnsData = await handlerReturns;\n        const transformedData = await transformFn(returnsData, (headers || {}));\n        data.v = transformedData;\n        // invalidate cache\n        hitCacheBySource(methodInstance);\n        return transformedData;\n    };\n    /**\n     * Create AlovaSSEHook event\n     * For specific data processing procedures, please refer to the following link\n     * @link https://alova.js.org/zh-CN/tutorial/combine-framework/response\n     */\n    const createSSEEvent = async (eventFrom, dataOrError) => {\n        assert$1(!!eventSource.current, 'EventSource is not initialized');\n        const es = eventSource.current;\n        const baseEvent = new AlovaSSEEvent(AlovaEventBase.spawn(methodInstance, usingArgs.current), es);\n        if (eventFrom === MessageType.Open) {\n            return Promise.resolve(baseEvent);\n        }\n        const globalSuccess = interceptByGlobalResponded ? responseSuccessHandler.current : $self;\n        const globalError = interceptByGlobalResponded ? responseErrorHandler.current : throwFn;\n        const globalFinally = interceptByGlobalResponded ? responseCompleteHandler.current : noop;\n        const p = promiseFinally(promiseThen(dataOrError, res => handleResponseTask(globalSuccess(res, methodInstance)), error => handleResponseTask(globalError(error, methodInstance))), \n        // Finally\n        () => {\n            globalFinally(methodInstance);\n        });\n        // Regardless, the Promise object returned by the function must be fulfilled\n        return promiseThen(p, \n        // Get processed data (data after transform)\n        res => new AlovaSSEMessageEvent(baseEvent, res), \n        // There is an error\n        error => new AlovaSSEErrorEvent(baseEvent, error));\n    };\n    /**\n     * Select the required trigger function based on the event. If the event has no errors, the callback function passed in is triggered.\n     * @param callback Callback function triggered when there is no error\n     */\n    const sendSSEEvent = (callback) => (event) => {\n        if (event.error === undefinedValue) {\n            return callback(event);\n        }\n        return eventManager.emit(SSEErrorEventKey, event);\n    };\n    // * MARK: Event handling of EventSource\n    const onCustomEvent = (eventName, callbackHandler) => {\n        var _a;\n        const currentMap = customEventMap.current;\n        if (!currentMap.has(eventName)) {\n            const useCallbackObject = useCallback(callbacks => {\n                var _a;\n                if (callbacks.length === 0) {\n                    (_a = eventSource.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(eventName, useCallbackObject[1]);\n                    customEventMap.current.delete(eventName);\n                }\n            });\n            const trigger = useCallbackObject[1];\n            currentMap.set(eventName, useCallbackObject);\n            (_a = eventSource.current) === null || _a === void 0 ? void 0 : _a.addEventListener(eventName, event => {\n                promiseThen(createSSEEvent(eventName, Promise.resolve(event.data)), sendSSEEvent(trigger));\n            });\n        }\n        const [onEvent] = currentMap.get(eventName);\n        return onEvent(callbackHandler);\n    };\n    /**\n     * Cancel the registration of custom events in useCallback\n     */\n    const offCustomEvent = () => {\n        customEventMap.current.forEach(([_1, _2, offTrigger]) => {\n            offTrigger();\n        });\n    };\n    const esOpen = memorize(() => {\n        var _a;\n        // resolve the promise returned when using send()\n        readyState.v = SSEHookReadyState.OPEN;\n        promiseThen(createSSEEvent(MessageType.Open, Promise.resolve()), event => eventManager.emit(SSEOpenEventKey, event));\n        // ! Must be resolved after calling onOpen\n        (_a = sendPromiseObject.current) === null || _a === void 0 ? void 0 : _a.resolve();\n    });\n    const esError = memorize((event) => {\n        var _a, _b;\n        readyState.v = SSEHookReadyState.CLOSED;\n        promiseThen(createSSEEvent(MessageType.Error, Promise.reject((_a = event === null || event === void 0 ? void 0 : event.message) !== null && _a !== void 0 ? _a : 'SSE Error')), sendSSEEvent(event => eventManager.emit(SSEMessageEventKey, event)));\n        (_b = sendPromiseObject.current) === null || _b === void 0 ? void 0 : _b.resolve();\n    });\n    const esMessage = memorize((event) => {\n        promiseThen(createSSEEvent(MessageType.Message, Promise.resolve(event.data)), sendSSEEvent(event => eventManager.emit(SSEMessageEventKey, event)));\n    });\n    /**\n     * Close the registration of the current eventSource\n     */\n    const close = () => {\n        const es = eventSource.current;\n        if (!es) {\n            return;\n        }\n        if (sendPromiseObject.current) {\n            // If the promise is still there when close\n            sendPromiseObject.current.resolve();\n        }\n        // * MARK: Unbinding event handling\n        es.close();\n        es.removeEventListener(MessageType.Open, esOpen);\n        es.removeEventListener(MessageType.Error, esError);\n        es.removeEventListener(MessageType.Message, esMessage);\n        readyState.v = SSEHookReadyState.CLOSED;\n        // After eventSource is closed, unregister all custom events\n        // Otherwise it may cause memory leaks\n        customEventMap.current.forEach(([_, eventTrigger], eventName) => {\n            es.removeEventListener(eventName, eventTrigger);\n        });\n    };\n    /**\n     * Send request and initialize eventSource\n     */\n    const connect = (...args) => {\n        let es = eventSource.current;\n        let promiseObj = sendPromiseObject.current;\n        if (es && abortLast) {\n            // When abortLast === true, close the previous connection and re-establish it\n            close();\n        }\n        // Set the promise object used by the send function\n        if (!promiseObj) {\n            promiseObj = sendPromiseObject.current = usePromise();\n            // Clear the promise object after open\n            promiseObj &&\n                promiseObj.promise.finally(() => {\n                    promiseObj = undefinedValue;\n                });\n        }\n        usingArgs.current = args;\n        methodInstance = getHandlerMethod(handler, args);\n        // Set up response interceptor\n        setResponseHandler(methodInstance);\n        const { params } = getConfig(methodInstance);\n        const { baseURL, url } = methodInstance;\n        const fullURL = buildCompletedURL(baseURL, url, params);\n        // Establish connection\n        es = new EventSource(fullURL, { withCredentials });\n        eventSource.current = es;\n        readyState.v = SSEHookReadyState.CONNECTING;\n        // * MARK: Register to handle events\n        // Register to handle event open error message\n        es.addEventListener(MessageType.Open, esOpen);\n        es.addEventListener(MessageType.Error, esError);\n        es.addEventListener(MessageType.Message, esMessage);\n        // and custom events\n        // If the on listener is used before connect (send), there will already be events in customEventMap.\n        customEventMap.current.forEach(([_, eventTrigger], eventName) => {\n            es === null || es === void 0 ? void 0 : es.addEventListener(eventName, event => {\n                promiseThen(createSSEEvent(eventName, Promise.resolve(event.data)), sendSSEEvent(eventTrigger));\n            });\n        });\n        return promiseObj.promise;\n    };\n    onUnmounted(() => {\n        close();\n        // The above use of eventSource.removeEventListener just disconnects eventSource and trigger.\n        // Here is the cancellation of the event registration in the useCallback object\n        eventManager.off(SSEOpenEventKey);\n        eventManager.off(SSEMessageEventKey);\n        eventManager.off(SSEErrorEventKey);\n        offCustomEvent();\n    });\n    // * MARK: initialization action\n    onMounted(() => {\n        var _a;\n        if (immediate) {\n            connect(...[]);\n            (_a = sendPromiseObject.current) === null || _a === void 0 ? void 0 : _a.promise.catch(() => { });\n        }\n    });\n    return exposeProvider({\n        send: connect,\n        close,\n        on: onCustomEvent,\n        onMessage,\n        onError,\n        onOpen,\n        eventSource,\n        ...objectify([readyState, data])\n    });\n};\n\nlet currentHookIndex = 0;\n// (id, (hookIndex, Actions))\nconst actionsMap = {};\nconst isFrontMiddlewareContext = (context) => !!context.send;\nconst assert = createAssert('subscriber');\n/**\n * Operation function delegation middleware\n * After using this middleware, you can call the delegated function through accessAction.\n * Can delegate multiple identical IDs\n * In order to eliminate the hierarchical restrictions of components\n * @param id Client ID\n * @returns alova middleware function\n */\nconst actionDelegationMiddleware = (id) => {\n    const { ref, onUnmounted } = statesHookHelper(promiseStatesHook());\n    const hookIndex = ref(currentHookIndex + 1);\n    if (hookIndex.current > currentHookIndex) {\n        currentHookIndex += 1;\n    }\n    onUnmounted(() => {\n        var _a;\n        if ((_a = actionsMap[id]) === null || _a === void 0 ? void 0 : _a[hookIndex.current]) {\n            // delete action on unmount\n            delete actionsMap[id][hookIndex.current];\n        }\n    });\n    return (context, next) => {\n        // The middleware will be called repeatedly. If you have already subscribed, you do not need to subscribe again.\n        const { abort, proxyStates, delegatingActions = {} } = context;\n        const update = (newStates) => {\n            for (const key in newStates) {\n                proxyStates[key] && (proxyStates[key].v = newStates[key]);\n            }\n        };\n        // Those with the same ID will be saved together in the form of an array\n        const hooks = (actionsMap[id] = actionsMap[id] || []);\n        const handler = isFrontMiddlewareContext(context)\n            ? {\n                ...delegatingActions,\n                send: context.send,\n                abort,\n                update\n            }\n            : {\n                ...delegatingActions,\n                fetch: context.fetch,\n                abort,\n                update\n            };\n        hooks[hookIndex.current] = handler;\n        return next();\n    };\n};\n/**\n * Access the operation function, if there are multiple matches, onMatch will be called with this\n * @param id Delegator id, or regular expression\n * @param onMatch matching subscribers\n * @param silent Default is false. If true, no error will be reported if there is no match\n */\nconst accessAction = (id, onMatch, silent = false) => {\n    const matched = [];\n    if (typeof id === 'symbol' || isString(id) || isNumber(id)) {\n        actionsMap[id] && pushItem(matched, ...objectValues(actionsMap[id]));\n    }\n    else if (instanceOf(id, RegExp)) {\n        forEach(filterItem(objectKeys(actionsMap), idItem => id.test(idItem)), idItem => {\n            pushItem(matched, ...objectValues(actionsMap[idItem]));\n        });\n    }\n    // its opposite expression is too obscure\n    if (matched.length === 0 && !silent) {\n        assert(false, `no handler can be matched by using \\`${id.toString()}\\``);\n    }\n    forEach(filterItem(matched, $self), onMatch);\n};\n\nexport { accessAction, actionDelegationMiddleware, bootSilentFactory, createClientTokenAuthentication, createServerTokenAuthentication, dehydrateVData, equals, filterSilentMethods, getSilentMethod, isVData, onBeforeSilentSubmit, onSilentSubmitBoot, onSilentSubmitError, onSilentSubmitFail, onSilentSubmitSuccess, silentQueueMap, statesHookHelper, stringifyVData, updateState, updateStateEffect, useAutoRequest, useCaptcha, useFetcher, useForm, usePagination, useRequest, useRetriableRequest, useSQRequest, useSSE, useSerialRequest, useSerialWatcher, useWatcher };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,qBAAqB;AAAA,EACvB,UAAU;AACd;AACA,IAAM,mBAAmB;AAAA,EACrB,UAAU;AACd;AACA,IAAM,oBAAoB;AAAA,EACtB,UAAU;AACd;AACA,IAAM,0BAA0B;AAAA,EAC5B,UAAU;AACd;AACA,IAAM,kBAAkB,CAAC,EAAE,KAAK,GAAG,gBAAgB;AAC/C,MAAI,cAAc,IAAI,GAAG;AACrB,eAAW,OAAO,MAAM;AACpB,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACjD,cAAM,kBAAkB,YAAY,GAAG;AACvC,YAAI,WAAW,iBAAiB,MAAM,IAAI,gBAAgB,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,MAAM,iBAAiB;AACvG,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,wBAAwB,CAAC,QAAQ,gBAAgB,YAAY,YAAY,aAAW;AACtF,WAAS,aAAa;AAAA,IAClB;AAAA,IACA;AAAA,EACJ,CAAC;AACL,CAAC;AACD,IAAM,2BAA2B,CAAC,QAAQ,0BAA0B,aAAa,aAAa;AAC1F,MAAI,gBAAgB,SAAS,6BAA6B,QAAQ,6BAA6B,SAAS,SAAS,yBAAyB,gBAAgB,WAAW,GAAG;AACpK,UAAM,UAAU,KAAK,wBAAwB,IACvC,2BACA,cAAc,wBAAwB,KAAK,KAAK,yBAAyB,OAAO,IAC5E,yBAAyB,UACzB;AACV,WAAO,QAAQ,UAAU,MAAM;AAAA,EACnC;AACJ;AACA,IAAM,wBAAwB,OAAO,QAAQ,aAAa,qBAAqB,eAAe,cAAc,oBAAoB;AAE5H,QAAM,eAAe,IAAI,aAAa,KAAK;AAC3C,MAAI,YAAY,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,UAAU,GAAG,aAAa;AAEnH,MAAI,WAAW,WAAW,UAAU,GAAG;AACnC,gBAAY,MAAM;AAAA,EACtB;AACA,MAAI,WAAW;AACX,QAAI;AAEA,UAAI,uBAAuB;AAC3B,UAAI,gBAAgB,iBAAiB;AACjC,+BAAuB;AACvB,cAAM,sBAAsB,QAAQ,WAAW;AAAA,MACnD;AACA,UAAI,sBAAsB;AACtB,4BAAoB,SAAS;AAE7B,eAAO,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,QAAQ,GAAG,aAAa;AACxG,4BAAoB,UAAU;AAE9B,gBAAQ,aAAa,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC;AAAA,MACnD;AACA,UAAI,cAAc;AAEd,cAAM,EAAE,OAAO,IAAI;AACnB,cAAM,sBAAsB,OAAO;AACnC,eAAO,YAAY;AACnB,cAAM,aAAa,MAAM;AACzB,eAAO,YAAY;AACnB,eAAO;AAAA,MACX;AAAA,IACJ,UACA;AACI,0BAAoB,UAAU;AAC9B,aAAO,aAAa,GAAG,IAAI,WAAW,CAAC;AAAA,IAC3C;AAAA,EACJ;AACJ;AACA,IAAM,qBAAqB,CAAC,wBAAwB;AAChD,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,MAAI,oBAAoB;AACxB,MAAI,KAAK,mBAAmB,GAAG;AAC3B,qBAAiB;AAAA,EACrB,WACS,cAAc,mBAAmB,GAAG;AACzC,UAAM,EAAE,WAAW,SAAS,WAAW,IAAI;AAC3C,qBAAiB,KAAK,SAAS,IAAI,YAAY;AAC/C,mBAAe,KAAK,OAAO,IAAI,UAAU;AACzC,wBAAoB,KAAK,UAAU,IAAI,aAAa;AAAA,EACxD;AACA,SAAO;AAAA,IACH,WAAW;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,EAChB;AACJ;AAOA,IAAM,kCAAkC,CAAC,EAAE,aAAa,OAAO,QAAQ,cAAc,cAAc,KAAK,MAAM;AAC1G,MAAI,kBAAkB;AACtB,QAAM,cAAc,CAAC;AACrB,QAAM,iBAAiB,qBAAmB,OAAO,WAAW;AACxD,UAAM,gBAAgB,gBAAgB,QAAQ,eAAe,kBAAkB;AAC/E,UAAM,cAAc,gBAAgB,SAAS,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,gBAAgB,gBAAgB;AAEjI,QAAI,CAAC,iBACD,CAAC,eACD,CAAC,gBAAgB,SAAS,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,gBAAgB,uBAAuB,GAAG;AAE7I,UAAI,iBAAiB;AACjB,cAAM,sBAAsB,QAAQ,WAAW;AAAA,MACnD;AACA,YAAM,sBAAsB,QAAQ,aAAa,gBAAc;AAC3D,0BAAkB;AAAA,MACtB,GAAG,CAAC,MAAM,GAAG,YAAY;AAAA,IAC7B;AAEA,QAAI,CAAC,iBAAiB,CAAC,aAAa;AAChC,YAAM,YAAY,MAAM;AAAA,IAC5B;AACA,WAAO,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,MAAM;AAAA,EACnG;AACA,QAAM,yBAAyB,uBAAqB;AAChD,UAAM,kBAAkB,mBAAmB,iBAAiB;AAC5D,WAAO;AAAA,MACH,GAAG;AAAA,MACH,WAAW,OAAO,UAAU,WAAW;AACnC,cAAM,yBAAyB,QAAQ,OAAO,kBAAkB,QAAQ;AACxE,cAAM,yBAAyB,QAAQ,QAAQ,mBAAmB,QAAQ;AAC1E,gBAAQ,gBAAgB,aAAa,OAAO,UAAU,MAAM;AAAA,MAChE;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAMA,IAAM,kCAAkC,CAAC,EAAE,aAAa,OAAO,QAAQ,uBAAuB,qBAAqB,cAAc,KAAK,MAAM;AACxI,MAAI,kBAAkB;AACtB,QAAM,cAAc,CAAC;AACrB,QAAM,iBAAiB,qBAAmB,OAAO,WAAW;AACxD,UAAM,gBAAgB,gBAAgB,QAAQ,eAAe,kBAAkB;AAC/E,UAAM,cAAc,gBAAgB,SAAS,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,gBAAgB,gBAAgB;AAEjI,QAAI,CAAC,iBACD,CAAC,eACD,CAAC,gBAAgB,SAAS,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,gBAAgB,uBAAuB,KACrK,CAAC,gBAAgB,SAAS,wBAAwB,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,gBAAgB,uBAAuB,GAAG;AAElK,UAAI,iBAAiB;AACjB,cAAM,sBAAsB,QAAQ,WAAW;AAAA,MACnD;AAAA,IACJ;AACA,QAAI,CAAC,iBAAiB,CAAC,aAAa;AAChC,YAAM,YAAY,MAAM;AAAA,IAC5B;AACA,WAAO,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,MAAM;AAAA,EACnG;AACA,QAAM,yBAAyB,yBAAuB;AAClD,UAAM,kBAAkB,mBAAmB,mBAAmB;AAC9D,WAAO;AAAA,MACH,GAAG;AAAA,MACH,WAAW,OAAO,UAAU,WAAW;AACnC,YAAI,CAAC,gBAAgB,QAAQ,eAAe,kBAAkB,KAC1D,CAAC,gBAAgB,SAAS,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,gBAAgB,gBAAgB,KAC9G,CAAC,gBAAgB,SAAS,0BAA0B,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB,gBAAgB,uBAAuB,GAAG;AACxK,gBAAM,aAAa,MAAM,sBAAsB,QAAQ,aAAa,gBAAc;AAC9E,8BAAkB;AAAA,UACtB,GAAG,CAAC,UAAU,MAAM,GAAG,uBAAuB,eAAe;AAC7D,cAAI,YAAY;AACZ,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,cAAM,yBAAyB,QAAQ,OAAO,kBAAkB,QAAQ;AACxE,cAAM,yBAAyB,QAAQ,QAAQ,mBAAmB,QAAQ;AAC1E,gBAAQ,gBAAgB,aAAa,OAAO,UAAU,MAAM;AAAA,MAChE;AAAA,MACA,SAAS,OAAO,OAAO,WAAW;AAC9B,YAAI,CAAC,gBAAgB,QAAQ,eAAe,kBAAkB,KAC1D,CAAC,gBAAgB,SAAS,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,gBAAgB,gBAAgB,KAC9G,CAAC,gBAAgB,SAAS,wBAAwB,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,gBAAgB,uBAAuB,GAAG;AAClK,gBAAM,aAAa,MAAM,sBAAsB,QAAQ,aAAa,gBAAc;AAC9E,8BAAkB;AAAA,UACtB,GAAG,CAAC,OAAO,MAAM,GAAG,qBAAqB,eAAe;AACxD,cAAI,YAAY;AACZ,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,gBAAQ,gBAAgB,WAAW,MAAM,OAAO,MAAM;AAAA,MAC1D;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAMA,IAAM,UAAU,CAAC,UAAU;AACvB,QAAM;AACV;AACA,SAAS,YAAY,mBAAmB,MAAM;AAC1C,MAAI,YAAY,CAAC;AACjB,QAAM,cAAc,CAAC,OAAO;AACxB,QAAI,CAAC,UAAU,SAAS,EAAE,GAAG;AACzB,gBAAU,KAAK,EAAE;AACjB,uBAAiB,SAAS;AAAA,IAC9B;AAEA,WAAO,MAAM;AACT,kBAAY,WAAW,WAAW,OAAK,MAAM,EAAE;AAC/C,uBAAiB,SAAS;AAAA,IAC9B;AAAA,EACJ;AACA,QAAM,kBAAkB,IAAI,SAAS;AACjC,QAAI,UAAU,SAAS,GAAG;AACtB,aAAO,QAAQ,WAAW,QAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC/C;AAAA,EACJ;AACA,QAAM,oBAAoB,MAAM;AAC5B,gBAAY,CAAC;AACb,qBAAiB,SAAS;AAAA,EAC9B;AACA,SAAO,CAAC,aAAa,iBAAiB,iBAAiB;AAC3D;AAQA,IAAM,WAAW,CAAC,IAAI,UAAU;AAC5B,MAAI,QAAQ;AACZ,SAAO,SAAS,cAAc,MAAM;AAChC,UAAM,SAAS,GAAG,KAAK,MAAM,GAAG,IAAI;AACpC,UAAM,YAAY,SAAS,KAAK,IAAI,QAAQ,MAAM,GAAG,IAAI;AACzD,aAAS,kBAAkB,KAAK;AAChC,QAAI,YAAY,GAAG;AACf,cAAQ,aAAa,QAAQ,SAAS;AAAA,IAC1C,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAOA,IAAMA,oBAAmB,CAAC,eAAe,OAAO,CAAC,MAAM;AACnD,QAAM,iBAAiB,KAAK,aAAa,IAAI,cAAc,GAAG,IAAI,IAAI;AACtE,eAAa,OAAO,EAAE,WAAW,gBAAgB,MAAM,GAAG,mFAAmF;AAC7I,SAAO;AACX;AAOA,IAAM,YAAY,CAAC,KAAK,aAAa;AACjC,QAAM,MAAM,CAAC;AACb,aAAW,OAAO,KAAK;AACnB,QAAI,GAAG,IAAI,SAAS,IAAI,GAAG,GAAG,KAAK,GAAG;AAAA,EAC1C;AACA,SAAO;AACX;AACA,IAAI;AAAA,CACH,SAAUC,eAAc;AACrB,EAAAA,cAAaA,cAAa,aAAa,IAAI,CAAC,IAAI;AAChD,EAAAA,cAAaA,cAAa,aAAa,IAAI,CAAC,IAAI;AAChD,EAAAA,cAAaA,cAAa,aAAa,IAAI,CAAC,IAAI;AACpD,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAOtC,SAAS,iBAAiB,YAAY,iBAAiB,EAAE,aAAa,CAAC,GAAG,WAAW,WAAW,GAAG;AAC/F,QAAM,MAAM,CAAC,iBAAkB,WAAW,MAAM,WAAW,IAAI,YAAY,IAAI,EAAE,SAAS,aAAa;AACvG,mBAAiB,IAAI,cAAc,EAAE;AACrC,QAAM,cAAc,CAAC,WAAW,WAAW,UAAU,OAAO,OAAO,cAAc;AACjF,QAAM,WAAW,CAAC,OAAO;AACrB,QAAI,CAAC,KAAK,WAAW,QAAQ,GAAG;AAC5B,aAAO;AAAA,IACX;AACA,UAAM,cAAc,WAAW,SAAS,EAAE;AAC1C,gBAAY,YAAY;AACxB,WAAO;AAAA,EACX;AACA,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,SAAS,CAAC,UAAU,OAAO,QAAQ,aAAa,UAAU,OAAO,KAAK,cAAc,KACtF,eAAe,YAAY,GAAG,KAC9B,WAAW,OAAO,UAAU,OAAO,KAAK,cAAc;AAC1D,QAAM,UAAU,CAAC,SAAS,QAAQ,MAAM,UAAS,WAAW,MAAM,sBAAsB,IAAI,KAAK,IAAI,IAAK;AAC1G,QAAM,mBAAmB,CAAC;AAE1B,QAAM,UAAU,CAAC;AACjB,SAAO;AAAA,IACH,QAAQ,CAAC,cAAc,QAAQ;AAC3B,eAAS,kBAAkB,GAAG;AAC9B,aAAO,YAAa,gBAAiB,WAAW,OAAO,cAAc,KAAK,cAAc,GAAG,KAAK,WAAS,UAAU,OAAO,KAAK,cAAc,GAAG,aAAa,CAAC,OAAO,aAAa,OAAO,UAAU,OAAO,GAAG,CAAC;AAAA,IAClN;AAAA,IACA,UAAU,CAAC,QAAQ,SAAS,QAAQ;AAEhC,cAAQ,SAAS,SAAO;AACpB,YAAI,IAAI,GAAG;AACP,kBAAQ,IAAI,CAAC,IAAI;AAAA,QACrB;AAAA,MACJ,CAAC;AACD,aAAO,YAAa,wBAAyB,WAAW,SAAS,QAAQ,QAAQ,OAAO,GAAG,KAAK,cAAc,GAAG,KAAK,WAAS,UAAU,OAAO,KAAK,cAAc,GAAG,WAAW;AAAA,IACrL;AAAA,IACA,eAAe,CAAC,wBAAwB,WAAW,cAAc,qBAAqB,cAAc;AAAA,IACpG;AAAA,IACA,OAAO,CAAC,QAAQ,aAAa,WAAW,MAAM,QAAQ,MAAM,GAAG,UAAU,cAAc;AAAA,IACvF,WAAW,CAAC,aAAa,WAAW,UAAU,UAAU,cAAc;AAAA,IACtE,aAAa,CAAC,aAAa,WAAW,YAAY,UAAU,cAAc;AAAA,IAC1E;AAAA;AAAA;AAAA;AAAA,IAIA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMf,gBAAgB,CAAC,WAAW;AACxB,YAAM,WAAW,CAAC;AAClB,YAAM,oBAAoB,CAAC;AAC3B,iBAAW,OAAO,QAAQ;AACtB,cAAM,QAAQ,OAAO,GAAG;AACxB,cAAM,kBAAkB,KAAK,KAAK;AAKlC,YAAI,mBAAmB,CAAC,eAAe,YAAY,GAAG,GAAG;AACrD,mBAAS,GAAG,IAAI,IAAI,WAAW,IAAI,IAC7B,IAAI,SAAS;AACX,kBAAM,GAAG,IAAI;AAEb,mBAAO;AAAA,UACX,IACE,MAAM,YACF,QACA,SAAS,KAAK;AAAA,QAC5B,OACK;AACD,gBAAM,mBAAmB,WAAW,OAAO,sBAAsB;AACjE,cAAI,kBAAkB;AAClB,8BAAkB,GAAG,IAAI,MAAM;AAAA,UACnC;AAEA,oBAAU,eAAe,UAAU,KAAK;AAAA,YACpC,KAAK,MAAM;AAEP,6BAAe,YAAY,GAAG,IAAI;AAClC,qBAAO,mBAAmB,MAAM,IAAI;AAAA,YACxC;AAAA;AAAA;AAAA,YAGA,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,cAAc;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ;AACA,YAAM,EAAE,QAAQ,kBAAkB,cAAc,iBAAiB,IAAI;AAGrE,qBAAe,cAAc;AAAA,QACzB,GAAG;AAAA,MACP;AACA,qBAAe,YAAY;AAC3B,YAAM,gBAAgB;AAAA;AAAA,QAElB,eAAe;AAAA;AAAA,QAEf,QAAQ,SAAS,CAAC,cAAc;AAC5B,qBAAW,SAAS,EAAE,QAAQ,SAAO;AACjC,gBAAI,SAAS,kBAAkB,GAAG,GAAG;AACjC,qBAAO,UAAU,GAAG,GAAG,kBAAkB,GAAG,GAAG,GAAG;AAAA,YACtD,WACS,OAAO,YAAY,KAAK,gBAAgB,GAAG;AAChD,+BAAiB;AAAA,gBACb,CAAC,GAAG,GAAG,UAAU,GAAG;AAAA,cACxB,CAAC;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,QACD,cAAc,SAAS,CAAC,QAAQ;AAC5B,cAAI,SAAS,kBAAkB,GAAG,KAAK,WAAW,OAAO,GAAG,GAAG,sBAAsB,GAAG;AAEpF,2BAAe,YAAY,GAAG,IAAI;AAClC,mBAAO,OAAO,GAAG;AAAA,UACrB;AACA,iBAAO,iBAAiB,GAAG;AAAA,QAC/B,CAAC;AAAA,MACL;AACA,YAAM,oBAAoB,UAAU,UAAU,aAAa;AAC3D,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,WAAW,CAAC,QAAQ,cAAc,OAAO,OAAO,CAAC,QAAQ,SAAS;AAC9D,aAAO,KAAK,CAAC,IAAI,YAAY,KAAK,SAAS,IAAI;AAC/C,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AAAA,IACL,sBAAsB,CAAC,OAAO,QAAQ,YAAa,gBAAiB,OAAO,KAAK,CAAAC,WAAS,UAAUA,QAAO,KAAK,cAAc,GAAG,aAAa,CAACA,QAAO,aAAa,OAAO,UAAUA,QAAO,GAAG,CAAC;AAAA,EAClM;AACJ;AAEA,IAAM,aAAa,aAAa,EAAE;AAClC,IAAM,oBAAoB,aAAa,YAAY;AACnD,IAAM,oBAAoB,aAAa,YAAY;AACnD,IAAM,oBAAoB,aAAa,YAAY;AACnD,IAAM,iBAAiB,CAAC,cAAc;AAAA,EAClC,CAAC,aAAa,WAAW,GAAG;AAAA,EAC5B,CAAC,aAAa,WAAW,GAAG;AAAA,EAC5B,CAAC,aAAa,WAAW,GAAG;AAChC,GAAG,QAAQ;AAKX,IAAM,eAAe,CAACC,SAAQ,mBAAmBA,QAAO,WAAW,gBAAgB,MAAM,GAAG,6BAA6B;AAEzH,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,eAAe;AAErB,IAAI,aAAa,CAAC,IAAI,GAAG,cAAc,QAAQ;AAAA;AAAA,EAE3C,GAAG;AAAA;AAAA,EAEH,IAAI,CAAC;AAAA;AAAA,EAEL,IAAI,CAAC;AAAA;AAAA,EAEL,IAAI,CAAC;AAAA;AAAA,EAEL,IAAI;AAAA;AAAA,EAEJ;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,CAAC;AACT;AAGA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACjB,YAAY,QAAQ,MAAM;AACtB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,QAAQ;AACJ,WAAO,EAAE,GAAG,KAAK;AAAA,EACrB;AAAA,EACA,OAAO,MAAM,QAAQ,MAAM;AACvB,WAAO,IAAI,gBAAe,QAAQ,IAAI;AAAA,EAC1C;AACJ;AACA,IAAM,oBAAN,cAAgC,eAAe;AAAA,EAC3C,YAAY,MAAM,MAAM,WAAW;AAC/B,UAAM,KAAK,QAAQ,KAAK,IAAI;AAC5B,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACrB;AACJ;AACA,IAAM,kBAAN,cAA8B,eAAe;AAAA,EACzC,YAAY,MAAM,OAAO;AACrB,UAAM,KAAK,QAAQ,KAAK,IAAI;AAC5B,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,qBAAN,cAAiC,eAAe;AAAA,EAC5C,YAAY,MAAM,QAAQ,MAAM,WAAW,OAAO;AAC9C,UAAM,KAAK,QAAQ,KAAK,IAAI;AAC5B,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,YAAY,WAAW,UAAU,QAAQ;AAC9C,SAAK,QAAQ;AAAA,EACjB;AACJ;AAEA,IAAM,gBAAN,cAA4B,eAAe;AAAA,EACvC,YAAY,MAAM,aAAa;AAC3B,UAAM,KAAK,QAAQ,KAAK,IAAI;AAC5B,SAAK,cAAc;AAAA,EACvB;AACJ;AACA,IAAM,qBAAN,cAAiC,cAAc;AAAA,EAC3C,YAAY,MAAM,OAAO;AACrB,UAAM,MAAM,KAAK,WAAW;AAC5B,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,uBAAN,cAAmC,cAAc;AAAA,EAC7C,YAAY,MAAM,MAAM;AACpB,UAAM,MAAM,KAAK,WAAW;AAC5B,SAAK,OAAO;AAAA,EAChB;AACJ;AAEA,IAAM,UAAN,MAAc;AAAA,EACV,YAAY,UAAU,QAAQ,cAAc;AACxC,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,eAAe;AAAA,EACxB;AACJ;AAEA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAChC,YAAY,UAAU,QAAQ,cAAc,WAAW,YAAY;AAC/D,UAAM,UAAU,QAAQ,YAAY;AACpC,SAAK,YAAY;AACjB,SAAK,aAAa;AAAA,EACtB;AACJ;AACA,IAAM,uBAAN,cAAmC,cAAc;AAAA,EAC7C,YAAY,UAAU,QAAQ,cAAc,WAAW,YAAY,MAAM,eAAe;AACpF,UAAM,UAAU,QAAQ,cAAc,WAAW,UAAU;AAC3D,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACzB;AACJ;AACA,IAAM,qBAAN,cAAiC,cAAc;AAAA,EAC3C,YAAY,UAAU,QAAQ,cAAc,WAAW,YAAY,OAAO,YAAY;AAClF,UAAM,UAAU,QAAQ,cAAc,WAAW,UAAU;AAC3D,SAAK,QAAQ;AACb,SAAK,aAAa;AAAA,EACtB;AACJ;AACA,IAAM,oBAAN,cAAgC,cAAc;AAAA,EAC1C,YAAY,UAAU,QAAQ,cAAc,WAAW,YAAY,OAAO;AACtE,UAAM,UAAU,QAAQ,cAAc,WAAW,UAAU;AAC3D,SAAK,QAAQ;AAAA,EACjB;AACJ;AAEA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAChC,YAAY,UAAU,QAAQ,cAAc,MAAM;AAC9C,UAAM,UAAU,QAAQ,YAAY;AACpC,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,uBAAN,cAAmC,cAAc;AAAA,EAC7C,YAAY,UAAU,QAAQ,cAAc,MAAM,MAAM;AACpD,UAAM,UAAU,QAAQ,cAAc,IAAI;AAC1C,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,qBAAN,cAAiC,cAAc;AAAA,EAC3C,YAAY,UAAU,QAAQ,cAAc,MAAM,OAAO;AACrD,UAAM,UAAU,QAAQ,cAAc,IAAI;AAC1C,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,qBAAN,cAAiC,cAAc;AAAA,EAC3C,YAAY,UAAU,QAAQ,cAAc,MAAM,YAAY,YAAY;AACtE,UAAM,UAAU,QAAQ,cAAc,IAAI;AAC1C,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACtB;AACJ;AACA,IAAM,wBAAN,cAAoC,cAAc;AAAA,EAC9C,YAAY,UAAU,QAAQ,cAAc,MAAM,QAAQ,MAAM,OAAO;AACnE,UAAM,UAAU,QAAQ,cAAc,IAAI;AAC1C,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,sBAAN,cAAkC,eAAe;AAAA,EAC7C,YAAY,MAAM,YAAY,YAAY;AACtC,UAAM,KAAK,QAAQ,KAAK,IAAI;AAC5B,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACtB;AACJ;AACA,IAAM,qBAAN,cAAiC,gBAAgB;AAAA,EAC7C,YAAY,MAAM,OAAO,YAAY;AACjC,UAAM,MAAM,KAAK;AACjB,SAAK,aAAa;AAAA,EACtB;AACJ;AAEA,IAAM,oBAAoB,CAAC,GAAG,SAAS,KAAK;AAE5C,IAAM,aAAa,CAAC;AAOpB,IAAM,gBAAgB,CAAC,WAAW,QAAQ;AACtC,QAAM,cAAc,WAAW,SAAS,KAAK,CAAC;AAC9C,SAAO,YAAY,GAAG,KAAK,CAAC;AAChC;AAOA,IAAM,gBAAgB,CAAC,WAAW,KAAK,MAAM,iBAAiB;AAC1D,QAAM,cAAe,WAAW,SAAS,IAAI,WAAW,SAAS,KAAK,CAAC;AACvE,cAAY,GAAG,IAAI;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AACJ;AAMA,IAAM,mBAAmB,CAAC,WAAW,QAAQ;AACzC,QAAM,cAAc,WAAW,SAAS;AACxC,MAAI,aAAa;AACb,eAAW,aAAa,GAAG;AAAA,EAC/B;AACJ;AASA,SAAS,qBAAqB,cAAc,eAAe,kBAAkB,CAAC,GAAG;AAC7E,QAAM,oBAAoB,eAAe,aAAa,EAAE;AACxD,MAAI,iBAAiB,iBAAmB,eAAe,mBAAmB,eAAe;AACzF,QAAM,EAAE,IAAI,aAAa,IAAI,UAAU,GAAG,eAAe,IAAI,cAAc,IAAI;AAC/E,QAAM,EAAE,SAAS,cAAc,MAAM,WAAW,OAAO,WAAW,IAAI;AACtE,QAAM,YAAY,aAAa,aAAa;AAC5C,QAAM,EAAE,OAAO,eAAe,YAAY,aAAa,kBAAkB,IAAI;AAC7E,QAAM,gBAAgB,WAAW,cAAc;AAC/C,QAAM,EAAE,GAAG,IAAI;AAEf,QAAM,YAAY,qBAAqB,cAAc;AACrD,QAAM,EAAE,YAAY,UAAU,IAAI;AAClC,QAAM,iBAAiB,CAAC,aAAa;AACrC,eAAa,IAAI;AACjB,UAAQ,YAAY;AAEhB,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,wBAAwB,eAAe,cAAc;AACzD,QAAI,mBAAmB;AACvB,QAAI,iBAAiB;AACrB,UAAM,iBAAiB,MAAM,WAAW,cAAc;AACtD,QAAI,YAAY,MAAM,CAAC,CAAC;AAExB,QAAI,oBAAoB;AACxB,QAAI,CAAC,WAAW;AAEZ,mBAAa,CAAAC,iBAAe,cAAc,IAAI,WAAWA,cAAa,YAAY;AAClF,iBAAW,EAAE,GAAG,aAAa,GAAG,cAAc,CAAC;AAE/C,qBAAe,MAAM,iBAAiB,IAAI,SAAS;AAAA,IACvD;AAEA,UAAM,YAAY,qBAAmB;AACjC,qBAAe;AACf,YAAM,EAAE,OAAO,wBAAwB,cAAc,QAAQ,2BAA2B,eAAe,IAAI,mBAAmB,CAAC;AAC/H,YAAM,sBAAsB,aAAa,uBAAuB;AAAA,QAC5D,YAAa,gBAAiB,gBAAgB,eAAe;AAAA,MACjE,CAAC;AACD,YAAM,kBAAkB,CAAC,UAAU,CAAC,EAAE,QAAQ,MAAM,MAAM;AACtD,oBAAY,KAAK,EAAE,IAAI;AAAA,UACnB;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AACA,uBAAiB;AAEjB,eAAS,aAAa,IAAI,UAAU;AACpC,eAAS,aAAa,IAAI,YAAY;AAGtC,UAAI,CAAC,mBAAmB;AACpB,qBAAa,IAAI,CAAC,CAAC,uBAAuB,CAAC;AAAA,MAC/C;AAEA,YAAM,EAAE,aAAa,gBAAgB,WAAW,aAAa,IAAI,aAAa,GAAG;AACjF,yBAAmB,iBAAiB,eAAe,WAAW,gBAAgB,aAAa,CAAC,IAAI;AAChG,uBAAiB,eAAe,eAAe,SAAS,gBAAgB,WAAW,CAAC,IAAI;AACxF,8BAAwB,eAAe,KAAK,mBAAmB;AAC/D,kBAAY,MAAM,eAAe,aAAa;AAC9C,aAAO;AAAA,IACX;AAEA,UAAM,gBAAgB;AAAA,MAClB,QAAQ;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,MACR,OAAO,MAAM,eAAe,MAAM;AAAA,IACtC;AAEA,UAAM,mBAAmB,MAAM,aAAa,aAAa,eAAe,CAAC,aAAa,aAAa,MAAM;AACzG,UAAM,iBAAiB,CAAC,UAAU,cAAc;AAE5C,UAAI,WAAW,gBAAgB;AAC3B,qBAAa,IAAI;AAAA,MACrB;AACA,0BAAoB;AAAA,IACxB;AAEA,UAAM,4BAA4B,YAC5B,WAAW;AAAA,MACT,GAAG;AAAA,MACH,MAAM;AAAA,MACN,OAAO,CAACC,oBAAmB,SAAS;AAChC,qBAAa,mBAAmBA,eAAc;AAC9C,eAAO,qBAAqB,cAAcA,iBAAgB,IAAI;AAAA,MAClE;AAAA,MACA,aAAa,KAAK,aAAa,MAAM;AAAA,MACrC;AAAA,IACJ,GAAG,SAAS,IACV,WAAW;AAAA,MACT,GAAG;AAAA,MACH,MAAM;AAAA,MACN,MAAM,IAAI,SAAS,qBAAqB,cAAc,eAAe,IAAI;AAAA,MACzE,aAAa;AAAA,MACb;AAAA,IACJ,GAAG,SAAS;AAChB,QAAI,kBAAkB;AACtB,UAAM,YAAa,eAAgB,MAAM,gBAAgB,eAAe;AACxE,QAAI;AAEA,YAAM,yBAAyB,MAAM;AACrC,YAAM,eAAe,CAAC,SAAS;AAE3B,YAAI,CAAC,WAAW;AACZ,2BAAiB,MAAM,UAAU,IAAI;AAAA,QACzC,WACS,aAAa,EAAE,gBAAgB,YAAY;AAEhD,gBAAM,cAAc,cAAc,IAAI,SAAS,EAAE;AACjD,0BAAgB,YAAY,KAAK,IAAI;AAAA,QACzC;AAEA,YAAI,iBAAiB,GAAG;AACpB,qBAAW,IAAI;AAEf,WAAC,sBAAsB,aAAa,IAAI;AACxC,uBAAa,GAAG,KAAK,aAAa,YAAa,mBAAoB,WAAW,MAAM,UAAU,CAAC,CAAC;AAChG,uBAAa,GAAG,KAAK,cAAc,YAAa,oBAAqB,WAAW,aAAa,MAAM,UAAU,GAAG,cAAc,CAAC;AAAA,QACnI;AACA,eAAO;AAAA,MACX;AACA;AAAA;AAAA,MAGI,2BAA2B,iBACrB,aAAa,sBAAsB,IACnC;AAAA;AAAA;AAAA;AAAA,QAIM,MAAM,YAAY,uBAAuB,cAAc,MAAM,aAAa,cAAc,CAAC;AAAA;AAAA;AAAA,QAEzF;AAAA;AAEhB,OAAC,gBAAgB,CAAC,sBAAsB,aAAa,IAAI;AAAA,IAC7D,SACO,OAAO;AACV,UAAI,iBAAiB,GAAG;AAEpB,mBAAW,IAAI;AAEf,SAAC,sBAAsB,aAAa,IAAI;AACxC,qBAAa,GAAG,KAAK,WAAW,YAAa,iBAAkB,WAAW,KAAK,CAAC;AAChF,qBAAa,GAAG,KAAK,cAAc,YAAa,oBAAqB,WAAW,WAAW,gBAAgB,UAAU,GAAG,KAAK,CAAC;AAAA,MAClI;AACA,YAAM;AAAA,IACV;AAEA,qBAAiB;AACjB,mBAAe;AACf,WAAO;AAAA,EACX,GAAG;AACP;AAEA,IAAM,aAAa,CAAC,QAAQ,IAAI;AAahC,SAAS,mBAAmB,UAAU,eAAe,eAAe,aAAa,YAAY,YAAY,gBAAgB,gBAAgB,GAAG;AACxI,MAAI;AAEJ,kBAAgB,EAAE,GAAG,cAAc;AACnC,QAAM,EAAE,eAAe,iBAAiB,EAAE,aAAa,CAAC,GAAG,WAAW,WAAW,EAAE,IAAI;AACvF,MAAI,iBAAiB,CAAC,CAAC;AACvB,MAAI,iBAAiB;AAIrB,MAAI,WAAW;AAEX,QAAI;AACA,YAAM,iBAAiB,iBAAmB,eAAe,eAAe,QAAQ,CAAC;AACjF,YAAM,gBAAgB,WAAW,cAAc;AAC/C,YAAM,gBAAgB,cAAc,QAAQ,IAAI,wBAAwB,cAAc,IAAI,qBAAqB,cAAc,CAAC,CAAC;AAG/H,UAAI,iBAAiB,CAAC,WAAW,eAAe,UAAU,GAAG;AACzD,cAAM,CAACC,OAAM,eAAe,IAAI;AAEhC,YAAI,CAAC,mBAAmB,kBAAkB,QAAQ,GAAG;AACjD,2BAAiBA;AAAA,QACrB;AAAA,MACJ;AACA,YAAM,sBAAsB,cAAc,KAAK,cAAc,WAAW,QAAQ,OAAO,SAAS,KAAK,UAAU;AAC/G,uBAAiB,CAAC,CAAC,uBAAuB,CAAC;AAAA,IAC/C,SACO,IAAI;AAAA,IAAE;AAAA,EACjB;AACA,QAAM,EAAE,QAAQ,eAAe,KAAK,WAAW,gBAAgB,qBAAqB,IAAI,iBAAiB,kBAAkB,GAAG,cAAc;AAC5I,QAAM,WAAW;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAEA,QAAM,EAAE,gBAAgB,CAAC,EAAE,IAAI;AAC/B,QAAM,qBAAqB,UAAU,eAAe,CAAC,OAAO,QAAQ,qBAAqB,OAAO,GAAG,CAAC;AACpG,QAAM,OAAO,OAAO,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAkB,KAAK,WAAW,IAAI,YAAY,IAAI,aAAc,MAAM;AACrJ,QAAM,UAAU,OAAO,gBAAgB,SAAS;AAChD,QAAM,QAAQ,OAAO,gBAAgB,OAAO;AAC5C,QAAM,cAAc,OAAO,EAAE,GAAG,SAAS,GAAG,aAAa;AACzD,QAAM,YAAY,OAAO,EAAE,GAAG,SAAS,GAAG,WAAW;AACrD,QAAM,cAAc,UAAU,CAAC,MAAM,SAAS,OAAO,aAAa,SAAS,CAAC;AAC5E,QAAM,eAAe,mBAAmB;AACxC,QAAM,eAAe,WAAW,IAAI,WAAW,UAAU,eAAe,cAAc,cAAc,CAAC,CAAC;AAItG,eAAa,KAAK;AAClB,eAAa,KAAK;AAClB,eAAa,IAAI;AACjB,eAAa,KAAK;AAClB,QAAM,oBAAoB,mBAAmB;AAG7C,QAAM,gBAAgB,CAAC,UAAU,eAAe,oBAAoB,qBAAqB,cAAc,SAAS,eAAe;AAE/H,QAAM,aAAa,WAAW,IAAI,qBAAqB,CAAC,CAAC;AAGzD,QAAM,oBAAoB,CAAC,KAAK,gBAAgB,YAAY;AACxD,eAAW,MAAM;AACb,mBAAa,cAAc,OAAO,GAAG,CAAAC,WAAS;AAE1C,YAAI,CAAC,GAAG,aAAa,CAAC,GAAG,YAAY,OAAO;AACxC,gBAAMA;AAAA,QACV;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAMA,QAAM,wBAAwB,IAAI,SAAS,CAAC,GAAG,IAAI,YAAY,kBAAkB,IAAI,OAAO,GAAG,CAAC,iBAAiB,SAAS,YAAY,IAAK,QAAQ,aAAa,IAAI,cAAc,YAAY,IAAI,gBAAiB,CAAC,CAAC;AAErN,MAAI,CAAC,gBAAgB,KAAK;AACtB,kBAAc;AAAA,MACV;AAAA;AAAA,QAEA,oBACM,CAAC,iBAAiB,sBAAsB,QAAQ,cAAc,gBAAgB,aAAa,IAC3F,MAAM,kBAAkB,cAAc;AAAA;AAAA,MAC5C,cAAc,MAAM,QAAQ,aAAa,IAAI,QAAM,GAAG,CAAC;AAAA,MACvD,YAAY,YAAU,QAAQ,aAAa,IAAI,QAAM,GAAG,MAAM,CAAC;AAAA,MAC/D,aAAa,EAAE,GAAG,aAAa,GAAG,mBAAmB;AAAA,MACrD;AAAA,MACA,WAAW,cAAc,QAAQ,cAAc,SAAS,YAAY;AAAA,IACxE,CAAC;AAAA,EACL;AACA,SAAO,eAAe;AAAA,IAClB,GAAG,UAAU,CAAC,MAAM,SAAS,OAAO,aAAa,SAAS,CAAC;AAAA,IAC3D,OAAO,MAAM,aAAa,KAAK,aAAa,EAAE,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQpD,MAAM,CAAC,iBAAiB,mBAAmB,cAAc,gBAAgB,eAAe;AAAA,IACxF,UAAU,SAAS;AACf,mBAAa,GAAG,aAAa,OAAO;AAAA,IACxC;AAAA,IACA,QAAQ,SAAS;AAGb,qBAAe,YAAY;AAC3B,mBAAa,GAAG,WAAW,OAAO;AAAA,IACtC;AAAA,IACA,WAAW,SAAS;AAChB,mBAAa,GAAG,cAAc,OAAO;AAAA,IACzC;AAAA,EACJ,CAAC;AACL;AAKA,SAAS,WAAW,SAAS,CAAC,GAAG;AAC7B,QAAM,QAAQ,mBAAmB,aAAa,aAAa,MAAM,MAAM;AACvE,QAAM,EAAE,KAAK,IAAI;AACjB,aAAW,OAAO,MAAM;AACxB,SAAO,UAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,IAKpB,OAAO,CAAC,YAAY,SAAS;AACzB,mBAAa,mBAAmB,OAAO;AACvC,aAAO,KAAK,MAAM,OAAO;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AAEA,SAAS,WAAW,SAAS,SAAS,CAAC,GAAG;AACtC,QAAM,EAAE,YAAY,WAAW,YAAY,IAAI;AAC/C,QAAM,QAAQ,mBAAmB,aAAa,aAAa,SAAS,QAAQ,aAAa,CAAC,CAAC,SAAS;AACpG,QAAM,EAAE,KAAK,IAAI;AACjB,SAAO,UAAU,OAAO;AAAA,IACpB,MAAM,IAAI,SAAS,KAAK,IAAI;AAAA,EAChC,CAAC;AACL;AAEA,SAAS,WAAW,SAAS,gBAAgB,SAAS,CAAC,GAAG;AACtD,oBAAkB,kBAAkB,IAAI,cAAc,IAAI,GAAG,sCAAsC;AACnG,QAAM,EAAE,WAAW,UAAAC,YAAW,GAAG,YAAY,IAAI;AACjD,QAAM,QAAQ;AAAA,IAAmB,aAAa;AAAA,IAAa;AAAA,IAAS;AAAA,IAAQ;AAAA,IAAa,CAAC,CAAC;AAAA;AAAA,IAC3F;AAAA,IAAgBA;AAAA,EAAQ;AACxB,QAAM,EAAE,KAAK,IAAI;AACjB,SAAO,UAAU,OAAO;AAAA,IACpB,MAAM,IAAI,SAAS,KAAK,IAAI;AAAA,EAChC,CAAC;AACL;AAEA,IAAI,+BAA+B,CAAC,YAAY;AAC5C,MAAI,kBAAkB,CAAC;AACvB,SAAO;AAAA,IACH,WAAW,MAAM;AAAA,IACjB,KAAK,gBAAgB,QAAQ,YAAY;AACrC,YAAM,MAAM,qBAAqB,cAAc;AAG/C,UAAI,CAAC,gBAAgB,GAAG,KAAK,OAAO;AAChC,wBAAgB,GAAG,IAAI;AAAA,UACnB,QAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,KAAK,CAAC,iBAAiB,gBAAgB,qBAAqB,WAAW,cAAe,MAAO,IAAI,eAAe,QAAQ,YAAY,CAAC,CAAC;AAAA,IACtI,OAAO,KAAK;AACR,UAAI,KAAK;AACL,eAAO,gBAAgB,GAAG;AAAA,MAC9B,OACK;AACD,0BAAkB,CAAC;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAM,mBAAmB,aAAa,eAAe;AACrD,IAAM,cAAc,CAAC,OAAO,YAAY,iBAAiB,SAAS,KAAK,KAAK,QAAQ,IAAI,OAAO,GAAG,mDAAmD;AACrJ,IAAI,gBAAgB,CAAC,SAAS,SAAS,CAAC,MAAM;AAC1C,QAAM,EAAE,QAAQ,UAAU,KAAK,OAAO,gBAAgB,WAAW,eAAe,eAAe,IAAI,iBAAiB,kBAAkB,CAAC;AACvI,QAAM,EAAE,sBAAsB,WAAW,kBAAkB,WAAW,OAAO,cAAc,SAAO,IAAI,OAAO,MAAM,aAAa,SAAO,IAAI,MAAM,SAAS,YAAY,cAAc,GAAG,kBAAkB,IAAI,iBAAiB,CAAC,GAAG,aAAa,YAAY,WAAW,YAAY,QAAQ,MAAM,GAAG,OAAO,IAAI;AAC9S,QAAM,aAAa,IAAI,OAAO;AAC9B,QAAM,UAAU,IAAI,UAAU;AAE9B,QAAM,OAAO,OAAO,aAAa,MAAM;AACvC,QAAM,WAAW,OAAO,iBAAiB,UAAU;AACnD,QAAM,OAAO,OAAQ,cAAc,WAAW,WAAW,KAAK,CAAC,IAAI,CAAC,GAAI,MAAM;AAC9E,QAAM,QAAQ,OAAO,cAAc,YAAY,WAAW,IAAI,gBAAgB,OAAO;AAErF,QAAM,EAAE,WAAW,iBAAiB,KAAK,oBAAoB,MAAM,cAAc,QAAQ,eAAe,IAAI,IAAI,6BAA6B,CAAAC,UAAQ,WAAW,QAAQA,OAAM,SAAS,CAAC,CAAC,CAAC,EAAE;AAC5L,QAAM,iBAAiB,CAAC,YAAY,WAAW,OAAO,KAAK;AAE3D,QAAM,cAAc,WAAW;AAAA,IAC3B,eAAe;AAAA,IACf,aAAa;AAAA,IACb,OAAO,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,EAC/B,CAAC;AACD,QAAM,EAAE,SAAS,OAAO,OAAO,YAAY,WAAW,eAAe,IAAI;AACzE,QAAM,cAAc,IAAI,OAAO;AAC/B,QAAMT,oBAAmB,CAAC,cAAc,KAAK,MAAM;AAC/C,UAAM,cAAc,SAAS;AAC7B,UAAM,gBAAgB,QAAQ,aAAa,WAAW;AAEtD,iBAAa,aAAa;AAC1B,WAAO;AAAA,EACX;AAEA,QAAM,gBAAgB,MAAM;AACxB,SAAK,IAAI;AACT,YAAQ,UAAU;AAAA,EACtB,CAAC;AAGD,QAAM,oBAAoB,IAAI,CAAC,CAAC;AAEhC,QAAM,YAAY,SAAS,MAAM;AAC7B,UAAM,WAAW,MAAM;AACvB,WAAO,aAAa,iBAAiB,KAAK,KAAK,WAAW,SAAS,CAAC,IAAI;AAAA,EAC5E,GAAG,CAAC,UAAU,KAAK,GAAG,WAAW;AACjC,QAAM,yBAAyB,CAAC,eAAe,IAAI,SAAS,kBAAkB,QAAQ,UAAU,EAAE,GAAG,IAAI;AACzG,QAAM,SAAS,WAAWA,mBAAkB,CAAC,GAAG,gBAAgB,KAAK,GAAG,SAAS,CAAC,GAAG;AAAA,IACjF,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA,eAAe,UAAU,CAAC,MAAM,MAAM,UAAU,KAAK,GAAG,GAAG;AAAA,IAC3D,WAAW,KAAK,MAAM;AAClB,UAAI,CAAC,YAAY;AACb,eAAO,KAAK;AAAA,MAChB;AACA,aAAO,WAAW;AAAA,QACd,GAAG;AAAA,QACH,mBAAmB;AAAA,UACf,SAAS,uBAAuB,SAAS;AAAA,UACzC,QAAQ,uBAAuB,QAAQ;AAAA,UACvC,QAAQ,uBAAuB,QAAQ;AAAA,UACvC,SAAS,uBAAuB,SAAS;AAAA,UACzC,QAAQ,uBAAuB,QAAQ;AAAA,UACvC,UAAU,CAAC,aAAa;AACpB,kBAAMU,UAAS;AAAA,cACX;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA;AAAA,cAEA;AAAA,YACJ;AACA,mBAAOA,QAAO,QAAQ,EAAE;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ,GAAG,IAAI;AAAA,IACX;AAAA,IACA,OAAO,WAAS,MAAM,KAAK,CAAC,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI;AAAA,IAC/D,GAAG;AAAA,EACP,CAAC;AACD,QAAM,EAAE,KAAK,IAAI;AACjB,QAAM,aAAa,OAAO,aAAa,MAAM;AAE7C,QAAM,aAAa,OAAO,YAAY;AAClC,UAAM,EAAE,UAAU,WAAW,GAAG,aAAa,aAAa,eAAe,YAAY,aAAa,WAAW,IAAI;AACjH,UAAM,EAAE,GAAG,mBAAmB,IAAI,yBAAyB,WAAW;AAGtE,QAAI,mBAAmB,MAAM,KAAK,QAAQ,GAAG;AACzC,aAAO;AAAA,IACX;AACA,QAAI,cAAc;AACd,aAAO;AAAA,IACX;AACA,QAAI,MAAM,WAAW,WAAW,GAAG;AAC/B,aAAO;AAAA,IACX;AACA,UAAM,eAAe,UAAU;AAC/B,UAAM,kBAAkB,eAClB,cAAc,eACd,aACI,IAAI,eAAe,OAAO,CAAC,IAAI,SAAS,IACxC;AACV,WAAO,cAAc,KAAK,CAAC;AAAA,EAC/B;AAEA,QAAM,gBAAgB,OAAO,SAASC,SAAQ,eAAe;AACzD,UAAM,WAAW,KAAK,IAAI;AAC1B,UAAM,cAAcX,kBAAiB,QAAQ;AAC7C,QAAI,mBACC,MAAM,WAAW;AAAA,MACd;AAAA,MACA,aAAa;AAAA,MACb;AAAA,MACA,YAAY;AAAA,MACZ,cAAcW;AAAA,IAClB,CAAC,GAAI;AACL,mBAAa,MAAM,aAAaA,MAAK,GAAG,IAAI;AAAA,IAChD;AAAA,EACJ;AAEA,QAAM,oBAAoB,OAAO,YAAY;AACzC,UAAM,WAAW,KAAK,IAAI;AAC1B,UAAM,cAAcX,kBAAiB,QAAQ;AAC7C,QAAI,uBACC,MAAM,WAAW;AAAA,MACd;AAAA,MACA,aAAa;AAAA,MACb;AAAA,IACJ,CAAC,GAAI;AACL,mBAAa,MAAM,WAAW,GAAG,IAAI;AAAA,IACzC;AAAA,EACJ;AAEA,QAAM,aAAa,SAAS,MAAM;AAC9B,UAAM,UAAU,WAAW;AAC3B,QAAI,CAAC,SAAS;AACV,aAAO;AAAA,IACX;AACA,UAAM,gBAAgB,eAAe,OAAO;AAC5C,UAAM,UAAU,KAAK;AACrB,UAAM,eAAe,UAAU;AAC/B,UAAM,UAAU,QAAQ,aAAa,IAAI,IAAI,aAAa,IAAI;AAC9D,WAAO,eAAe,WAAW,eAAe,UAAU,SAAS;AAAA,EACvE,GAAG,CAAC,MAAM,WAAW,YAAY,QAAQ,GAAG,YAAY;AAExD,QAAM,yBAAyB,YAAY;AACvC,UAAM,eAAe,mBAAmB,KAAK,CAAC;AAC9C,QAAI,cAAc;AACd,YAAM,SAAS,aAAa,QAAQ,CAAC,YAAY;AAE7C,YAAI,SAAS;AACT,gBAAM,iBAAiB,eAAe,OAAO,KAAK,CAAC;AACnD,iBAAO,gBAAgB,GAAG,IAAI,cAAc,GAAG,GAAG,KAAK,CAAC;AACxD,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,iBAAe,CAAC,EAAE,QAAQ,MAAM,QAAQ,MAAM;AAE1C,UAAM,eAAe,mBAAmB,KAAK,CAAC;AAC9C,QAAI,gBAAgB,qBAAqB,aAAa,MAAM,MAAM,qBAAqB,MAAM,GAAG;AAE5F,YAAM,WAAW,eAAe,OAAO;AACvC,UAAI,QAAQ;AAER,cAAM,UAAU,KAAK;AACrB,cAAM,cAAc,SAAS;AAG7B,cAAM,gBAAgB,IAAI,OAAO,IAAI;AACrC,YAAI,gBAAgB,GAAG;AACnB,gBAAMY,WAAU,CAAC,GAAG,KAAK,CAAC;AAC1B,iBAAOA,WAAU,KAAK,IAAI,KAAK,aAAa,eAAe,GAAG,QAAQ;AACtE,eAAK,IAAIA;AAAA,QACb;AAAA,MACJ,OACK;AACD,aAAK,IAAI;AAAA,MACb;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,QAAM,eAAe,IAAI,cAAc;AACvC,QAAM,cAAc,IAAI,cAAc;AACtC,SACK,UAAU,CAAC,EAAE,MAAM,SAAS,MAAM,CAAC,aAAa,SAAS,GAAG,OAAO,MAAM;AAC1E,UAAM,EAAE,OAAO,YAAY,IAAI,mBAAmB,MAAM,KAAK,CAAC;AAC9D,UAAM,eAAe;AACrB,UAAM,IAAI,gBAAgB,iBAAiB,cAAc,YAAY,YAAY;AACjF,QAAI,CAAC,WAAW;AACZ,wBAAkB,YAAY;AAC9B,oBAAc,YAAY;AAAA,IAC9B;AACA,UAAM,cAAc,SAAS;AAC7B,UAAM,WAAW,eAAe,YAAY;AAC5C,qBAAiB,QAAQ,QAAQ,GAAG,8EAA8E;AAElH,QAAI,QAAQ;AAER,UAAI,QAAQ,SAAS;AACjB,aAAK,IAAI,CAAC;AAAA,MACd;AACA,UAAI,gBAAgB,gBAAgB;AAChC,aAAK,IAAI,CAAC,GAAG,KAAK,GAAG,GAAG,QAAQ;AAAA,MACpC,WACS,aAAa;AAClB,cAAMA,WAAU,CAAC,GAAG,KAAK,CAAC;AAE1B,eAAOA,WAAU,cAAc,KAAK,aAAa,aAAa,GAAG,QAAQ;AACzE,aAAK,IAAIA;AAAA,MACb;AAAA,IACJ,OACK;AACD,WAAK,IAAI;AAAA,IACb;AAAA,EACJ,CAAC,EACI,UAAU,CAAC,EAAE,MAAAN,MAAK,MAAM;AACzB,QAAI;AACJ,KAAC,KAAK,aAAa,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,cAAcA,KAAI;AAAA,EAC/F,CAAC,EACI,QAAQ,CAAC,EAAE,MAAM,MAAM;AACxB,QAAI;AACJ,KAAC,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,aAAa,KAAK;AAAA,EAC9F,CAAC,EACI,WAAW,MAAM;AAElB,YAAQ,UAAU;AAAA,EACtB,CAAC;AAED,QAAM,eAAe,CAAC,SAAS;AAC3B,UAAM,QAAQ,KAAK,EAAE,QAAQ,IAAI;AACjC,qBAAiB,SAAS,GAAG,2BAA2B;AACxD,WAAO;AAAA,EACX;AACA,QAAM,EAAE,UAAU,gBAAgB,YAAY,wBAAwB,IAAI,IAAI,iBAAiB,CAAC,EAAE;AAOlG,QAAM,UAAU,OAAO,iBAAiB,KAAK,MAAM;AAC/C,QAAI,cAAc;AAClB,QAAI,eAAe,eAAe;AAClC,QAAI,QAAQ;AACR,UAAI,CAAC,SAAS,cAAc,GAAG;AAC3B,cAAM,YAAY,aAAa,cAAc;AAC7C,sBAAc,KAAK,MAAM,YAAY,SAAS,CAAC,IAAI;AAAA,MACvD;AACA,uBAAiB,eAAe,KAAK,GAAG,sCAAsC;AAE9E,qBAAe,KAAK,aAAa,SAAS;AAAA,IAC9C,OACK;AACD,uBAAiB,SAAS,WAAW,GAAG,6DAA6D;AAErG,qBACI,gBAAgB,KAAK,IACf,KAAK,gBAAgB,SAAS,IAC9B,MAAM,QAAQ,aAAa,SAAS,CAAC,GAAG,SAAS;AAAA,IAC/D;AACA,WAAO;AAAA,EACX;AAEA,QAAM,4BAA4B,OAAO,MAAM,eAAe;AAC1D,UAAM,UAAU,KAAK;AACrB,UAAM,cAAc,gBAAgB;AACpC,QAAI,YAAY,aAAa,WAAW;AACxC,QAAI,KAAK;AACL,qBAAe;AAAA,IACnB,OACK;AAED,YAAM,sBAAsB,QAAQ,WAAW,CAAC,mBAAmB,UAAU,CAAC,GAAG,mBAAmB,OAAO,GAAG,mBAAmB,UAAU,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,EAAE,OAAO,MAAM,qBAAqB,MAAM,CAAC;AACtM,kBAAY,QAAQ,WAAW,WAAW,WAAW,GAAG,SAAO,CAAC,SAAS,qBAAqB,GAAG,CAAC,GAAG,SAAO;AACxG,cAAM,OAAO,YAAY,GAAG;AAC5B,eAAO,YAAY,GAAG;AACtB,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,UAAM,gBAAgB,QAAQ,WAAW,CAAC,EAAE,OAAO,MAAM,MAAM,CAAC;AAAA,EACpE;AAGA,QAAM,aAAa,YAAY;AAC3B,gBAAY,WAAW,WAAW;AAElC,UAAM,0BAA0B;AAEhC,UAAM,eAAe,mBAAmB,KAAK,IAAI,CAAC;AAClD,QAAI,cAAc;AACd,YAAM,iBAAiB,eAAgB,MAAM,WAAW,aAAa,MAAM,KAAM,CAAC,CAAC,KAAK,CAAC;AACzF,oBAAc,gBAAgB,IAAI,cAAc,IAAI,SAAS,CAAC;AAAA,IAClE;AAAA,EACJ;AAEA,QAAM,cAAc,CAAC,WAAW;AAC5B,QAAI,WAAW,GAAG;AACd;AAAA,IACJ;AAEA,UAAM,WAAW,MAAM;AACvB,QAAI,SAAS,QAAQ,GAAG;AACpB,YAAM,gBAAgB,KAAK,IAAI,WAAW,QAAQ,CAAC;AACnD,YAAM,IAAI;AACV,YAAM,UAAU,KAAK;AAErB,cAAQ,CAAC,mBAAmB,UAAU,CAAC,GAAG,mBAAmB,OAAO,GAAG,mBAAmB,UAAU,CAAC,CAAC,GAAG,UAAQ;AAC7G,iBAAS,KAAK,QAAQ;AAAA,MAC1B,CAAC;AAAA,IACL;AAAA,EACJ;AAQA,QAAM,SAAS,CAAC,MAAM,WAAW,MAAM;AACnC,4BAAwB,UAAU;AAClC,WAAO,eAAe,YAAY;AAC9B,YAAM,QAAQ,SAAS,QAAQ,IAAI,WAAW,aAAa,QAAQ,IAAI;AACvE,UAAI,UAAU;AACd,YAAM,UAAU,CAAC,GAAG,KAAK,CAAC;AAE1B,UAAI,IAAI,OAAO,IAAI,SAAS,MAAM,GAAG;AACjC,kBAAU,QAAQ,IAAI;AAAA,MAC1B;AAEA,aAAO,SAAS,OAAO,GAAG,IAAI;AAC9B,WAAK,IAAI;AACT,kBAAY,CAAC;AAEb,YAAM,uBAAuB;AAG7B,UAAI,SAAS;AACT,cAAM,eAAe,mBAAmB,KAAK,IAAI,CAAC;AAClD,YAAI,cAAc;AACd,gBAAM,SAAS,aAAa,QAAQ,CAACM,aAAY;AAC7C,gBAAIA,UAAS;AACT,oBAAM,iBAAiB,eAAeA,QAAO,KAAK,CAAC;AACnD,6BAAe,QAAQ,OAAO;AAC9B,6BAAe,IAAI;AACnB,qBAAOA;AAAA,YACX;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAMA,QAAM,SAAS,IAAI,cAAc;AAC7B,4BAAwB,UAAU;AAClC,WAAO,eAAe,YAAY;AAC9B,YAAM,UAAU,QAAQ,WAAW,cAAY;AAC3C,cAAM,QAAQ,SAAS,QAAQ,IAAI,WAAW,aAAa,QAAQ;AACnE,oBAAY,OAAO,KAAK,CAAC;AACzB,eAAO;AAAA,MACX,CAAC;AACD,YAAM,UAAU,KAAK;AACrB,YAAM,WAAW,UAAU;AAC3B,YAAM,eAAe,mBAAmB,QAAQ;AAChD,YAAM,eAAe,CAAC;AACtB,UAAI,cAAc;AACd,cAAM,SAAS,aAAa,QAAQ,CAAC,YAAY;AAC7C,cAAI,SAAS;AACT,kBAAM,iBAAiB,eAAe,OAAO;AAE7C,gBAAI,QAAQ,cAAc,GAAG;AACzB,uBAAS,cAAc,GAAG,OAAO,gBAAgB,GAAG,IAAI,OAAO,CAAC,CAAC;AAAA,YACrE;AACA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL;AACA,YAAM,gBAAgB,WAAW;AACjC,YAAM,kBAAkB,IAAI,YAAY;AACxC,UAAI,kBAAkB,KAAK,eAAe;AAEtC,cAAM,cAAc,WAAW,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC,SAAS,SAAS,KAAK,CAAC;AAE9E,YAAI,CAAC,UAAU,iBAAiB,IAAI,WAAW,KAAK,GAAG;AACnD,eAAK,IAAI,UAAU;AAAA,QACvB,WACS,kBAAkB,GAAG;AAC1B,mBAAS,aAAa,GAAG,YAAY;AAAA,QACzC;AACA,aAAK,IAAI;AAAA,MACb,WACS,mBAAmB,KAAK,CAAC,eAAe;AAE7C,gBAAQ,OAAO;AAAA,MACnB;AACA,kBAAY,CAAC,IAAI,OAAO,CAAC;AAEzB,aAAO,uBAAuB;AAAA,IAClC,CAAC;AAAA,EACL;AAOA,QAAM,UAAU,CAAC,MAAM,aAAa,eAAe,YAAY;AAC3D,qBAAiB,aAAa,gBAAgB,qCAAqC;AACnF,UAAM,QAAQ,SAAS,QAAQ,IAAI,WAAW,aAAa,QAAQ;AACnE,gBAAY,OAAO,KAAK,CAAC;AACzB,UAAM,UAAU,CAAC,GAAG,KAAK,CAAC;AAC1B,WAAO,SAAS,OAAO,GAAG,IAAI;AAC9B,SAAK,IAAI;AAET,UAAM,uBAAuB;AAAA,EACjC,CAAC;AAID,QAAM,SAAS,YAAY;AACvB,UAAM,0BAA0B,SAAS;AACzC,YAAQ,UAAU;AAClB,SAAK,MAAM,cAAc,aAAa,KAAK,GAAG,IAAI,IAAK,KAAK,IAAI;AAChE,UAAM,EAAE,SAAS,QAAQ,QAAQ,IAAI,WAAW;AAChD,iBAAa,UAAU;AACvB,gBAAY,UAAU;AACtB,WAAO;AAAA,EACX;AAEA,oBAAkB,UAAU;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAEA,SAAO,eAAe;AAAA,IAClB,GAAG;AAAA,IACH,GAAG,UAAU,CAAC,MAAM,MAAM,WAAW,UAAU,OAAO,UAAU,CAAC;AAAA,IACjE,UAAU,YAAY;AAAA,IACtB,gBAAgB,YAAY;AAAA,IAC5B,cAAc,YAAY;AAAA,IAC1B,iBAAiB,YAAY;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAOA,IAAM,uBAAuB,CAAC,UAAU,mBAAmB,aAAa,QAAQ,EAAE,QAAQ,cAAc,KAAK,IAAI,cAAc,IAAI,GAAG,kDAAkD;AAOxL,IAAM,mBAAmB,CAAC,gBAAgB,gBAAgB,uBAAuB,CAAC,MAAM;AAEpF,iBAAe,MAAM;AACrB,SAAQ,CAAC,KAAK,SAAS;AACnB,uBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,KAAK,MAAM,eAAe,cAAc,CAAC;AACxH,QAAI,eAAe;AACnB,UAAM,eAAe,IAAI,YAAY;AACrC,iBAAa,IAAI;AACjB,QAAI,gBAAgB,KAAK;AACzB,eAAW,WAAW,gBAAgB;AAClC,sBAAgB,YAAY,eAAe,WAAS;AAChD,cAAM,aAAa,QAAQ,OAAO,GAAG,IAAI,IAAI;AAC7C,iBAAS,sBAAsB,UAAU;AACzC,eAAO,WAAW,KAAK;AAAA,MAC3B,CAAC;AAAA,IACL;AACA,WAAO,cAAc,QAAQ,MAAM;AAC/B,mBAAa,IAAI;AAAA,IACrB,CAAC;AAAA,EACL;AACJ;AASA,IAAI,mBAAmB,CAAC,gBAAgB,SAAS,CAAC,MAAM;AACpD,uBAAqB,oBAAoB,cAAc;AAEvD,QAAM,EAAE,KAAK,cAAc,IAAI,iBAAiB,kBAAkB,CAAC;AACnE,QAAM,UAAU,IAAI,CAAC,CAAC,EAAE;AACxB,QAAM,YAAY,WAAW,eAAe,CAAC,GAAG;AAAA,IAC5C,GAAG;AAAA,IACH;AAAA,IACA,YAAY,iBAAiB,gBAAgB,OAAO,YAAY,OAAO;AAAA,EAC3E,CAAC;AAED,YAAU,UAAU,cAAc,UAAU,SAAS,CAAC,SAAS,UAAU;AACrE,UAAM,SAAS,QAAQ,IAAI,OAAO,IAAI,CAAC;AACvC,YAAQ,KAAK;AAAA,EACjB,CAAC;AACD,SAAO;AACX;AASA,IAAI,mBAAmB,CAAC,gBAAgB,gBAAgB,SAAS,CAAC,MAAM;AACpE,uBAAqB,oBAAoB,cAAc;AAEvD,QAAM,EAAE,KAAK,cAAc,IAAI,iBAAiB,kBAAkB,CAAC;AACnE,QAAM,UAAU,IAAI,CAAC,CAAC,EAAE;AACxB,QAAM,YAAY,WAAW,eAAe,CAAC,GAAG,gBAAgB;AAAA,IAC5D,GAAG;AAAA,IACH;AAAA,IACA,YAAY,iBAAiB,gBAAgB,OAAO,YAAY,OAAO;AAAA,EAC3E,CAAC;AAED,YAAU,UAAU,cAAc,UAAU,SAAS,CAAC,SAAS,UAAU;AACrE,UAAM,SAAS,QAAQ,IAAI,OAAO,IAAI,CAAC;AACvC,YAAQ,KAAK;AAAA,EACjB,CAAC;AACD,SAAO;AACX;AAEA,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AA2BxB,IAAI;AACJ,IAAM,0BAA0B,CAAC,UAAU;AACvC,yBAAuB;AAC3B;AAIA,IAAI;AACJ,IAAM,oBAAoB,CAAC,cAAc;AACrC,2BAAyB;AAC7B;AAIA,IAAI,oBAAoB,CAAC;AACzB,IAAM,uBAAuB,CAAC,cAAc,CAAC,MAAM;AAC/C,sBAAoB;AACxB;AAOA,IAAI,sBAAsB;AAC1B,IAAM,yBAAyB,CAAC,WAAW;AACvC,wBAAsB;AAC1B;AAmBA,IAAI,0BAA0B,CAAC;AAC/B,IAAM,6BAA6B,CAAC,qBAAqB,MAAM;AAC3D,4BAA0B,QAAQ,kBAAkB,IAC9C,qBACA;AAAA,IACE;AAAA,MACI,OAAO;AAAA,MACP,MAAM;AAAA,IACV;AAAA,EACJ;AACR;AACA,IAAM,eAAe,OAAO,cAAc;AAC1C,IAAM,iBAAiB,OAAO,gBAAgB;AAC9C,IAAM,kBAAkB,OAAO,iBAAiB;AAChD,IAAM,gBAAgB,OAAO,eAAe;AAC5C,IAAM,iBAAiB,OAAO,cAAc;AAE5C,IAAM,uBAAuB,mBAAmB;AAEhD,IAAM,eAAe,aAAa,cAAc;AAQhD,eAAe,YAAY,SAAS,cAAc;AAC9C,MAAI,UAAU;AAEd,MAAI,SAAS;AACT,UAAM,EAAE,OAAO,IAAI,kBAAkB;AACrC,UAAM,YAAY,qBAAqB,OAAO;AAC9C,UAAM,EAAE,GAAG,IAAI,WAAW,OAAO;AACjC,UAAM,EAAE,GAAG,aAAa,GAAG,aAAa,IAAI,cAAc,IAAI,SAAS;AACvE,UAAM,wBAAwB,KAAK,YAAY,IACzC,EAAE,MAAM,aAAa,IACrB;AACN,QAAI,wBAAwB;AAC5B,QAAI,aAAa;AAEb,cAAQ,WAAW,qBAAqB,GAAG,eAAa;AACpD,mBAAW,aAAa,aAAa,iBAAiB,SAAS,iBAAiB;AAChF,cAAM,mBAAmB,YAAY,SAAS;AAC9C,YAAI,cAAc,sBAAsB,SAAS,EAAE,iBAAiB,CAAC;AAErE,sBAAc,QAAQ,WAAW,IAC3B,CAAC,GAAG,WAAW,IACf,SAAS,WAAW,IAChB,EAAE,GAAG,YAAY,IACjB;AAEV,YAAI,cAAc,QAAQ;AACtB,kCAAwB;AAAA,QAC5B;AAEA,eAAO,aAAa,YAAY,SAAS,EAAE,GAAG,WAAW,aAAa,EAAE;AAAA,MAC5E,CAAC;AACD,gBAAU;AAAA,IACd;AAEA,QAAI,0BAA0B,gBAAgB;AAC1C,eAAS,SAAS,qBAAqB;AAAA,IAC3C;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAI,iBAAiB;AAAA,EACjB,SAAS,UAAS,WAAW,MAAM,IAAI,IAAI,KAAK,QAAQ,IAAI;AAAA,EAC5D,UAAU,QAAM,YAAY,MAAM,EAAE;AACxC;AAEA,IAAI,mBAAmB;AAAA,EACnB,SAAS,UAAS,WAAW,MAAM,MAAM,IAAI,KAAK,SAAS;AAAA,EAC3D,UAAU,YAAU,YAAY,QAAQ,MAAM;AAClD;AAEA,IAAM,4BAA4B,CAACC,qBAAoB,CAAC,MAAM;AAI1D,QAAM,cAAc;AAAA,IAChB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,GAAGA;AAAA,EACP;AAIA,QAAM,YAAY,CAAC,YAAY;AAC3B,QAAI,SAAS,OAAO,GAAG;AACnB,gBAAU,WAAW,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,EAAE,GAAG,QAAQ,GAAG,WAAS;AAC5E,YAAI,6BAA6B;AAEjC,cAAM,kBAAkB,WAAW,WAAW,EAAE,OAAO,CAAC,cAAc,mBAAmB;AACrF,cAAI,CAAC,4BAA4B;AAC7B,kBAAM,sBAAsB,YAAY,cAAc,EAAE,QAAQ,YAAY;AAC5E,gBAAI,wBAAwB,gBAAgB;AACxC,2CAA6B;AAC7B,6BAAe;AAAA,YACnB;AAAA,UACJ;AACA,iBAAO;AAAA,QACX,GAAG,KAAK;AAER,cAAM,cAAc,UAAU,UAAU,SAAS,KAAK,KAAK;AAC3D,YAAI,gBAAgB,mBAAmB;AACnC,kBAAQ,EAAE,GAAG,MAAM;AAAA,QACvB,WACS,QAAQ,KAAK,GAAG;AACrB,kBAAQ,CAAC,GAAG,KAAK;AAAA,QACrB;AACA,eAAO,+BAA+B,iBAAiB,CAAC,4BAA4B,eAAe,IAAI;AAAA,MAC3G,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AAIA,QAAM,cAAc,CAAC,YAAY,SAAS,OAAO,IAC3C,WAAW,SAAS,WAAS;AAC3B,QAAI,QAAQ,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG;AACpC,YAAM,kBAAkB,YAAY,MAAM,CAAC,CAAC;AAC5C,cAAQ,kBAAkB,gBAAgB,SAAS,MAAM,CAAC,CAAC,IAAI;AAAA,IACnE;AACA,WAAO;AAAA,EACX,GAAG,UAAU,IACX;AACN,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,gBAAgB,OAAO,MAAM;AACnC,IAAM,iBAAiB,OAAO,UAAU;AACxC,IAAM,aAAa;AAanB,IAAM,sBAAsB,CAAC,WAAW;AACpC,QAAM,UAAU,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,aAAa;AACpF,aAAW,yBAAyB,qBAAqB,OAAO,IAAI;AACxE;AASA,IAAM,iBAAiB,CAAC,QAAQ,2BAA2B,cAAc;AACrE,sBAAoB,MAAM;AAC1B,QAAM,aAAa,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,aAAa;AACvF,QAAM,UAAU,aAAa,OAAO,UAAU,MAAM;AACpD,SAAO,YAAY,2BAA2B,SAAS;AAC3D;AAMA,SAAS,oBAAoB;AACzB,SAAO,eAAe,IAAI;AAC9B;AAKA,IAAM,OAAO,SAASC,QAAO;AAAE;AAC/B,KAAK,YAAY,UAAU,OAAO,WAAW;AAAA,EACzC,CAAC,YAAY,GAAG,YAAY,iBAAiB;AACjD,CAAC;AAKD,IAAM,YAAY,SAASC,aAAY;AAAE;AACzC,UAAU,YAAY,UAAU,OAAO,WAAW;AAAA,EAC9C,CAAC,YAAY,GAAG,YAAY,iBAAiB;AACjD,CAAC;AAMD,IAAI,wBAAwB,CAAC,WAAW,UAAU,KAAK,MAAM;AACzD,QAAM,kBAAkB,CAAC,OAAO,eAAe,KAAK,MAAM;AACtD,QAAI,UAAU,WAAW;AACrB,cAAQ,YAAY,IAAI;AAAA,IAC5B,WACS,UAAU,gBAAgB;AAC/B,cAAQ,YAAY,SAAS;AAAA,IACjC,OACK;AACD,YAAM,WAAW,UAAU,KAAK;AAChC,qBAAe,UAAU,cAAc,iBAAiB;AACxD,qBAAe,UAAU,gBAAgB,KAAK;AAC9C,cAAQ;AAAA,IACZ;AACA,mBAAe,OAAO,eAAe,YAAY;AACjD,WAAO;AAAA,EACX;AACA,QAAM,kBAAkB,gBAAgB,WAAW,OAAO;AAC1D,MAAI,cAAc,eAAe,KAAK,QAAQ,eAAe,GAAG;AAC5D,eAAW,iBAAiB,WAAS,gBAAgB,KAAK,CAAC;AAAA,EAC/D;AACA,SAAO;AACX;AASA,IAAM,wBAAwB,CAAC,QAAQ,gBAAgB,cAAc;AACjE,QAAM,gBAAgB,CAAC,UAAU;AAC7B,wBAAoB,KAAK;AACzB,QAAI,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,aAAa,GAAG;AACpE,UAAI,WAAW,OAAO,SAAS,GAAG;AAC9B,gBAAQ;AAAA,MACZ,WACS,WAAW,OAAO,IAAI,GAAG;AAC9B,gBAAQ;AAAA,MACZ,WACS,WAAW,OAAO,MAAM,KAAK,WAAW,OAAO,MAAM,KAAK,WAAW,OAAO,OAAO,GAAG;AAC3F,gBAAQ,MAAM,cAAc;AAAA,MAChC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,YAAY,cAAc,MAAM;AAEtC,MAAI,kBAAkB,SAAS,SAAS,KAAK,QAAQ,SAAS,IAAI;AAC9D,eAAW,WAAW,WAAS,cAAc,KAAK,CAAC;AAAA,EACvD;AACA,SAAO;AACX;AAIA,IAAI,iBAAiB,CAAC,WAAW,sBAAsB,MAAM;AAE7D,IAAM,WAAW;AACjB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB,MAAM;AAE1B,eAAa,CAAC,CAAC,wBAAwB,wFAAwF;AAC/H,SAAO,uBAAuB;AAClC;AACA,IAAI,sBAAsB;AAC1B,IAAM,mCAAmC;AACzC,IAAM,+BAA+B;AAMrC,IAAM,iBAAiB,OAAO,KAAK,YAAY;AAC3C,QAAM,UAAU,gBAAgB;AAChC,MAAI,SAAS,OAAO,GAAG;AACnB,cAAU,WAAW,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,EAAE,GAAG,QAAQ,GAAG,CAAC,OAAOC,MAAK,WAAW;AAC3F,UAAI;AACJ,UAAIA,SAAQ,iBAAiB,OAAO,QAAQ,GAAG;AAC3C,eAAO;AAAA,MACX;AAEA,UAAIA,SAAQ,eAAe,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,SAAS;AACxJ,eAAO;AAAA,MACX;AACA,YAAM,UAAU,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,aAAa;AACjF,UAAI,iBAAiB,sBAAsB,OAAO,UAAU;AAE5D,YAAM,cAAc,eAAe,cAAc;AACjD,UAAI,gBAAgB,mBAAmB;AACnC,gBAAQ,EAAE,GAAG,MAAM;AACnB,yBAAiB,CAAC;AAAA,MACtB,WACS,QAAQ,KAAK,GAAG;AACrB,gBAAQ,CAAC,GAAG,KAAK;AACjB,yBAAiB,CAAC;AAAA,MACtB;AACA,UAAI,SAAS;AACT,cAAM,iBAAiB;AAAA,UACnB,CAAC,QAAQ,GAAG;AAAA;AAAA;AAAA,UAGZ,CAAC,aAAa,GAAG;AAAA,UACjB,GAAG;AAAA,QACP;AAEA,YAAI,WAAW,OAAO,MAAM,GAAG;AAC3B,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG;AACpC,+BAAmB,QAAQ,mBAAmB,SAAS,OAAO,OAAO,eAAe,CAAC;AAAA,UACzF;AAAA,QACJ;AAEA,gBAAQ;AAAA,MACZ;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,wBAAsB,uBAAuB,0BAA0B,iBAAiB;AACxF,QAAM,QAAQ,IAAI,KAAK,oBAAoB,UAAU,OAAO,CAAC;AACjE;AAKA,IAAM,iBAAiB,OAAO,QAAQ;AAClC,QAAM,mBAAmB,MAAM,gBAAgB,EAAE,IAAI,GAAG;AACxD,wBAAsB,uBAAuB,0BAA0B,iBAAiB;AACxF,SAAO,SAAS,gBAAgB,IAC1B,WAAW,oBAAoB,YAAY,gBAAgB,GAAG,WAAS;AAErE,QAAI,SAAS,KAAK,MAAM,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,QAAQ,IAAI;AACpF,YAAM,UAAU,MAAM,QAAQ;AAC9B,YAAM,aAAa,sBAAsB,MAAM,aAAa,GAAG,OAAO;AACtE,cAAQ,WAAW,KAAK,GAAG,CAAAA,SAAO;AAC9B,YAAI,CAAC,SAAS,CAAC,UAAU,aAAa,GAAGA,IAAG,GAAG;AAC3C,qBAAWA,IAAG,IAAI,MAAMA,IAAG;AAAA,QAC/B;AAAA,MACJ,CAAC;AACD,cAAQ;AAAA,IACZ;AACA,WAAO;AAAA,EACX,GAAG,UAAU,IACX;AACV;AAKA,IAAM,oBAAoB,OAAO,QAAQ;AACrC,QAAM,gBAAgB,EAAE,OAAO,GAAG;AACtC;AAMA,IAAM,sBAAsB,CAAC,yBAAyB,eAAe,+BAA+B,qBAAqB,IAAI,oBAAoB;AAOjJ,IAAM,6BAA6B,OAAO,sBAAsB,cAAc;AAC1E,QAAM,oBAAoB,oBAAoB;AAE9C,QAAM,yBAA2B,MAAM,eAAe,gCAAgC,KAClF,CAAC;AACL,QAAM,eAAgB,uBAAuB,SAAS,IAAI,uBAAuB,SAAS,KAAK,CAAC;AAChG,WAAS,cAAc,qBAAqB,EAAE;AAC9C,QAAM,eAAe,kCAAkC,sBAAsB;AACjF;AAOA,IAAM,4BAA4B,OAAO,WAAW,sBAAsB,oBAAoB;AAE1F,QAAM,yBAA2B,MAAM,eAAe,gCAAgC,KAClF,CAAC;AACL,QAAM,eAAe,uBAAuB,SAAS,KAAK,CAAC;AAC3D,QAAM,QAAQ,aAAa,UAAU,QAAM,OAAO,oBAAoB;AACtE,MAAI,SAAS,GAAG;AACZ,QAAI,iBAAiB;AACjB,aAAO,cAAc,OAAO,GAAG,gBAAgB,EAAE;AACjD,YAAM,oBAAoB,eAAe;AAAA,IAC7C,OACK;AACD,aAAO,cAAc,OAAO,CAAC;AAAA,IACjC;AACA,UAAM,kBAAkB,+BAA+B,oBAAoB;AAE3E,QAAI,YAAY,KAAK,KAAK,OAAO,uBAAuB,SAAS;AACjE,QAAI,IAAI,WAAW,sBAAsB,CAAC,IAAI,GAAG;AAC7C,YAAM,eAAe,kCAAkC,sBAAsB;AAAA,IACjF,OACK;AAED,YAAM,kBAAkB,gCAAgC;AAAA,IAC5D;AAAA,EACJ;AACJ;AAGA,IAAI,iBAAiB,CAAC;AAKtB,IAAM,uBAAuB,CAAC,aAAa;AACvC,UAAQ,WAAW,QAAQ,GAAG,qBAAmB;AAC7C,UAAM,eAAgB,eAAe,eAAe,IAAI,eAAe,eAAe,KAAK,CAAC;AAC5F,aAAS,cAAc,GAAG,SAAS,eAAe,CAAC;AAAA,EACvD,CAAC;AACL;AAOA,IAAM,mBAAmB,CAAC,QAAQ,kBAAkB;AAEhD,QAAM,eAAe,CAAC,UAAU;AAC5B,UAAM,QAAQ,eAAe,KAAK;AAIlC,QAAI,SAAS,eAAe;AACxB,aAAO,cAAc,KAAK;AAAA,IAC9B;AACA,QAAI,SAAS,KAAK,GAAG;AACjB,aAAO,MAAM,QAAQ,YAAY,WAAW,WAAW,QAAQ,GAAG,GAAG,SAAO,OAAO,gBAAgB,cAAc,GAAG,IAAI,GAAG;AAAA,IAC/H;AACA,WAAO;AAAA,EACX;AACA,MAAI,SAAS,MAAM,KAAK,CAAC,eAAe,QAAQ,UAAU,GAAG;AACzD,eAAW,QAAQ,YAAY;AAAA,EACnC,OACK;AACD,aAAS,aAAa,MAAM;AAAA,EAChC;AACA,SAAO;AACX;AAMA,IAAM,4BAA4B,CAAC,eAAe,gBAAgB,WAAW,IAAI,QAAQ,aAAa,OAAO,qBAAqB;AAE9H,mBAAiB,iBAAiB,QAAQ,aAAa;AAEvD,mBAAiB,SAAU,MAAM,oBAAoB,gBAAgB;AACzE,CAAC,CAAC;AAOF,IAAM,qCAAqC,CAAC,iBAAiB,aAAa;AACtE,MAAI,gBAAgB,CAAC;AACrB,QAAM,UAAU,eAAe,iBAAiB,UAAU;AAC1D,cAAY,cAAc,OAAO,IAAI;AACrC,MAAI,SAAS,eAAe,GAAG;AAC3B,eAAW,KAAK,iBAAiB;AAC7B,sBAAgB;AAAA,QACZ,GAAG;AAAA,QACH,GAAG,mCAAmC,gBAAgB,CAAC,GAAG,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,CAAC,CAAC;AAAA,MAC7H;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAUA,IAAM,wBAAwB,CAAC,sBAAsB,WAAW;AAC5D,MAAI,QAAQ;AACR,yBAAqB,SAAS;AAAA,EAClC,OACK;AACD,WAAO,qBAAqB;AAAA,EAChC;AACJ;AACA,IAAM,sBAAsB;AAC5B,IAAM,kBAAkB,CAAC,OAAO,cAAc;AAM1C,QAAM,uBAAuB,CAACC,eAAc;AACxC,UAAM,mBAAmB,MAAM,CAAC;AAChC,QAAI,kBAAkB;AAClB,YAAM,gBAAgB,wBAAwB,KAAK,CAAC,EAAE,OAAAC,OAAM,MAAM,WAAWA,QAAO,SAAS,IAAI,WAAWA,QAAOD,UAAS,IAAIC,WAAUD,UAAS;AAEnJ,YAAM,WAAW,MAAM,MAAM,CAAC,KAAK,oBAAoB,MAAM,CAAC,CAAC;AAC/D,YAAM,SAAS,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,QAAQ,aAAa,cAAc,MAAM,CAAC,kBAAkBA,UAAS,CAAC,IAAI;AACrK,eAAS,QAAQ,IAAI,aAAa,UAAU,KAAK,IAAI,SAAS;AAAA,IAClE;AAAA,EACJ;AAMA,QAAM,sBAAsB,CAAC,sBAAsB,aAAa,MAAM;AAElE,0BAAsB,sBAAsB,SAAS;AACrD,UAAM,EAAE,OAAO,IAAI,UAAU,QAAQ,aAAa,MAAM,gBAAgB,GAAG,UAAU,EAAE,OAAO,oBAAoB,GAAG,iBAAiB,MAAM,gBAAgB,MAAM,SAAS,eAAe,cAAc,CAAC,GAAG,iBAAiB,MAAM,IAAI;AAEvO,yBAAqB,KAAK,gBAAgB,YAAa,eAAgB,UAAU,QAAQ,sBAAsB,WAAW,UAAU,CAAC;AACrI,gBAAY,OAAO,KAAK,KAAK,GAAG,OAAO,SAAS;AAE5C,YAAM,KAAK;AAEX,eAAU,MAAM,0BAA0B,WAAW,EAAE;AAEvD,qBAAe,IAAI;AAGnB,UAAI,aAAa,iBAAiB;AAG9B,cAAM,gBAAgB,mCAAmC,iBAAiB,IAAI;AAC9E,cAAM,EAAE,iBAAiB,aAAa,IAAI;AAG1C,YAAI,WAAW,iBAAiB,MAAM,KAAK,gBAAgB,IAAI,YAAY,IAAI,GAAG;AAC9E,gBAAM,wBAAwB,CAAC;AAC/B,kBAAQ,cAAc,eAAa;AAE/B,kCAAsB,SAAS,IAAI,aAAW,iBAAiB,SAAS,aAAa;AAAA,UACzF,CAAC;AACD,gBAAM,UAAU,YAAY,iBAAiB,qBAAqB;AAElE,cAAI,CAAC,SAAS;AACV,kBAAM,SAAS,iBAAiB,CAAC,YAAY,iBAAiB,SAAS,aAAa,CAAC;AAAA,UACzF;AAAA,QACJ;AAEA,cAAM,0BAA0B,eAAe,KAAK;AAEpD,6BAAqB,KAAK,iBAAiB,YAAa,sBAAuB,UAAU,QAAQ,sBAAsB,WAAW,YAAY,MAAM,aAAa,CAAC;AAAA,MACtK;AAEA,4BAAsB,sBAAsB,UAAU;AAEtD,2BAAqB,SAAS;AAAA,IAClC,GAAG,YAAU;AACT,UAAI,aAAa,iBAAiB;AAG9B,cAAM,KAAK;AACX,sBAAc,MAAM;AAAA,MACxB,OACK;AAED,cAAM,sBAAsB,CAAC,eAAe,qBAAqB,KAAK,eAAe,YAAa,oBAAqB,UAAU,QAAQ,sBAAsB,WAAW,YAAY,QAAQ,UAAU,CAAC;AAGzM,cAAM,EAAE,MAAM,YAAY,IAAI,SAAS,WAAW,GAAG,IAAI,UAAU,CAAC;AACpE,YAAI;AACJ,YAAI;AACJ,YAAI,WAAW,YAAY,MAAM,GAAG;AAChC,6BAAmB;AAAA,QACvB,WACS,SAAS,UAAU,GAAG;AAC3B,8BAAoB,WAAW;AAC/B,6BAAmB,WAAW;AAAA,QAClC;AACA,cAAM,kBAAmB,qBAAqB,WAAW,mBAAmB,SAAS,KAChF,oBAAoB,WAAW,kBAAkB,QAAQ;AAE9D,YAAI,aAAa,iBAAiB,iBAAiB;AAE/C,gBAAM,aAAa,iBAAiB,SAAS,aAAa,CAAC;AAC3D,8BAAoB,UAAU;AAC9B;AAAA,YAAa,MAAM;AACf,4BAAc;AACd,kCAAoB,sBAAsB,UAAU;AACpD,4BAAc,KAAK,SAAS,YAAa,oBAAqB,UAAU,QAAQ,sBAAsB,aAAa,YAAY,UAAU,CAAC;AAAA,YAC9I;AAAA;AAAA,YAEA;AAAA,UAAU;AAAA,QACd,OACK;AACD,iCAAuB,CAAC;AACxB,8BAAoB;AAEpB,wBAAc,KAAK,YAAY,YAAa,oBAAqB,UAAU,QAAQ,sBAAsB,aAAa,MAAM,CAAC;AAC7H,+BAAqB,KAAK,gBAAgB,YAAa,mBAAoB,UAAU,QAAQ,sBAAsB,WAAW,YAAY,MAAM,CAAC;AAAA,QACrJ;AAAA,MACJ;AAEA,4BAAsB,sBAAsB,UAAU;AAAA,IAC1D,CAAC;AAAA,EACL;AACA,uBAAqB,SAAS;AAClC;AAQA,IAAM,4BAA4B,OAAO,sBAAsB,OAAO,kBAAkB,oBAAoB,eAAe,MAAM,CAAC,MAAM;AACpI,uBAAqB,QAAQ;AAC7B,QAAM,eAAgB,eAAe,eAAe,IAChD,eAAe,eAAe,KAAK,CAAC;AACxC,QAAM,aAAa,IAAI,YAAY,KAAK;AACxC,QAAM,oBAAoB,MAAM,QAAQ,IAAI,aAAa,CAAC;AAC1D,QAAM,eAAe,CAAC,kBAAkB,KAAK,aAAW,YAAY,UAAU;AAG9E,MAAI,cAAc;AACd,aAAU,MAAM,2BAA2B,sBAAsB,eAAe;AAChF,aAAS,cAAc,oBAAoB;AAE3C,kBAAc,wBAAwB,KAAK,gBAAgB,cAAc,eAAe;AAAA,EAC5F;AACA,SAAO;AACX;AAMA,IAAM,yBAAyB,CAAC,yBAAyB;AACrD,MAAI,QAAQ;AACZ,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,aAAW,iBAAiB,gBAAgB;AACxC,eAAW,eAAe,aAAa,EAAE,QAAQ,oBAAoB;AACrE,QAAI,YAAY,GAAG;AACf,cAAQ,eAAe,aAAa;AACpC,kBAAY;AACZ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,CAAC,OAAO,WAAW,QAAQ;AACtC;AAKA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,QAAQ,UAAU,SAAS,KAAK,KAAK,GAAG,OAAO,YAAY,eAAe,SAAS,gBAAgB,eAAe,aAAa,QAAQ;AAC/I,UAAM,UAAU;AAChB,YAAQ,SAAS;AACjB,YAAQ,WAAW;AACnB,YAAQ,KAAK;AACb,YAAQ,UAAU;AAClB,YAAQ,QAAQ,CAAC,CAAC;AAClB,YAAQ,aAAa;AACrB,YAAQ,gBAAgB;AACxB,YAAQ,UAAU;AAClB,YAAQ,iBAAiB;AACzB,YAAQ,gBAAgB;AACxB,YAAQ,cAAc;AACtB,YAAQ,SAAS;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,OAAO;AACT,SAAK,SAAU,MAAM,oBAAoB,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,iBAAiB;AAC3B,UAAM,qBAAqB;AAC3B,iBAAa,gBAAgB,UAAU,mBAAmB,OAAO,iEAAiE;AAClI,UAAM,CAAC,OAAO,WAAW,QAAQ,IAAI,uBAAuB,kBAAkB;AAC9E,QAAI,OAAO;AACP,aAAO,OAAO,UAAU,GAAG,eAAe;AAC1C,yBAAmB,SAAU,MAAM,0BAA0B,WAAW,mBAAmB,IAAI,eAAe;AAAA,IAClH;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,SAAS;AACX,UAAM,qBAAqB;AAC3B,UAAM,CAAC,OAAO,WAAW,QAAQ,IAAI,uBAAuB,kBAAkB;AAC9E,QAAI,OAAO;AACP,aAAO,OAAO,UAAU,CAAC;AACzB,yBAAmB,SAAU,MAAM,0BAA0B,WAAW,mBAAmB,EAAE;AAAA,IACjG;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,QAAQ,kBAAkB,QAAQ;AAC7C,QAAI,QAAQ;AACR,WAAK,kBAAkB;AACvB,WAAK,eAAe,QAAQ,eAAe,IAAI,kBAAkB,CAAC,eAAe;AAAA,IACrF;AAAA,EACJ;AACJ;AAOA,IAAI,8BAA8B,CAAC,YAAY;AAC3C,QAAM,EAAE,IAAI,UAAU,QAAQ,YAAY,eAAe,SAAS,gBAAgB,eAAe,aAAa,iBAAiB,MAAM,IAAI;AAEzI,QAAM,oBAAoB,CAAC,kBAAkB;AACzC,UAAM,EAAE,MAAM,KAAK,QAAQ,KAAK,IAAI;AACpC,WAAO,YAAY,QAAQ,MAAM,wBAAwB,KAAK,QAAQ,IAAI;AAAA,EAC9E;AACA,QAAM,uBAAuB,YAAY,cAAc,kBAAkB,MAAM,GAAG,UAAU,mBAAmB,GAAG,IAAI,OAAO,YAAY,eAAe,SAAS,gBAAgB,eAAe,WAAW;AAC3M,uBAAqB,QAAQ;AAE7B,MAAI,iBAAiB;AACjB,yBAAqB,kBAAkB,kBAAkB,eAAe;AAAA,EAC5E;AAEA,UAAQ,WAAW,OAAO,GAAG,SAAO;AAChC,QAAI,CAAC,SAAS;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,GAAG,GAAG,GAAG;AACL,2BAAqB,GAAG,IAAI,QAAQ,GAAG;AAAA,IAC3C;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAMA,IAAI,gCAAgC,YAAY;AAC5C,QAAM,yBAA2B,MAAM,eAAe,gCAAgC,KAClF,CAAC;AACL,QAAME,kBAAiB,CAAC;AACxB,QAAM,kBAAkB,CAAC;AACzB,UAAQ,WAAW,sBAAsB,GAAG,eAAa;AACrD,UAAM,eAAgBA,gBAAe,SAAS,IAAIA,gBAAe,SAAS,KAAK,CAAC;AAChF,aAAS,iBAAiB,GAAG,QAAQ,uBAAuB,SAAS,GAAG,OAAO,mBAAmB;AAC9F,YAAM,gCAAgC,MAAM,eAAe,+BAA+B,cAAc;AACxG,uCACI,SAAS,cAAc,4BAA4B,6BAA6B,CAAC;AAAA,IACzF,CAAC,CAAC;AAAA,EACN,CAAC;AACD,QAAM,WAAW,IAAI,eAAe;AACpC,SAAOA;AACX;AAOA,IAAM,qBAAqB,CAAC,YAAY,qBAAqB,GAAG,cAAc,OAAO;AAMrF,IAAM,wBAAwB,CAAC,YAAY,qBAAqB,GAAG,iBAAiB,OAAO;AAO3F,IAAM,sBAAsB,CAAC,YAAY,qBAAqB,GAAG,eAAe,OAAO;AAOvF,IAAM,qBAAqB,CAAC,YAAY,qBAAqB,GAAG,gBAAgB,OAAO;AAMvF,IAAM,uBAAuB,CAAC,YAAY,qBAAqB,GAAG,gBAAgB,OAAO;AAMzF,IAAM,oBAAoB,CAAC,YAAY;AACnC,MAAI,wBAAwB,GAAG;AAC3B,UAAM,EAAE,OAAO,QAAQ,IAAI,IAAI;AAC/B,sBAAkB,KAAK;AACvB,yBAAqB,QAAQ,WAAW;AACxC,+BAA2B,QAAQ,WAAW;AAC9C,iBAAa,YAAY;AAErB,2BAAqB,MAAM,8BAA8B,CAAC;AAG1D,cAAQ,WAAW,cAAc,GAAG,eAAa;AAC7C,wBAAgB,eAAe,SAAS,GAAG,SAAS;AAAA,MACxD,CAAC;AACD,6BAAuB,CAAC;AACxB,2BAAqB,KAAK,cAAc,cAAc;AAAA,IAC1D,GAAG,KAAK;AAAA,EACZ;AACJ;AAMA,IAAI,sBAAsB;AAM1B,IAAI,+BAA+B,CAAC,SAAS,WAAW;AACpD,QAAM,EAAE,WAAW,SAAS,QAAQ,oBAAoB,YAAY,eAAe,QAAQ,IAAI,UAAU,CAAC;AAC1G,QAAM,eAAe,mBAAmB;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,MAAI;AAMJ,QAAM,eAAe,IAAI,SAAS;AAC9B,iBAAa,KAAK,OAAO,GAAG,mEAAmE;AAC/F,4BAAwB,CAAC,CAAC;AAC1B,kBAAc;AACd,WAAO,QAAQ,GAAG,IAAI;AAAA,EAC1B;AAEA,QAAM,uBAAuB,CAAC,oBAAoB;AAE9C,oBAAgB,YAAY,cAAc,gBAAgB,WAAW,CAACC,UAAS,UAAU;AACrF,4BAAsB;AACtB,MAAAA,SAAQ,YAAa,sBAAuB,iBAAiB,MAAM,QAAQ,sBAAsB,MAAM,MAAM,MAAM,IAAI,CAAC;AAAA,IAC5H,CAAC;AACD,oBAAgB,UAAU,cAAc,gBAAgB,SAAS,CAACA,UAAS,UAAU;AACjF,MAAAA,SAAQ,YAAa,oBAAqB,iBAAiB,MAAM,QAAQ,sBAAsB,MAAM,MAAM,MAAM,KAAK,CAAC;AAAA,IAC3H,CAAC;AACD,oBAAgB,aAAa,cAAc,gBAAgB,YAAY,CAACA,UAAS,UAAU;AACvF,MAAAA,SAAQ,YAAa,uBAAwB,iBAAiB,MAAM,QAAQ,sBAAsB,MAAM,MAAM,MAAM,QAAQ,MAAM,MAAM,MAAM,KAAK,CAAC;AAAA,IACxJ,CAAC;AAAA,EACL;AAOA,QAAM,aAAa,CAAC,EAAE,QAAQ,MAAM,gBAAgB,aAAa,QAAAC,QAAO,GAAG,SAAS;AAChF,UAAM,EAAE,uBAAuB,eAAe,QAAQ,WAAW,IAAIA;AAErE,UAAM,YAAY,eAAe,MAAM,QAAQ,IAAI;AACnD,sBAAkB,aAAa,UAAU,CAAC,SAAS,CAAC;AACpD,mBAAe,aAAa,OAAO,CAAC,SAAS,CAAC;AAC9C,mBAAe,aAAa,OAAO,CAAC,SAAS,CAAC;AAG9C,UAAM,qBAAqB,MAAM;AAC7B,8BAAyB,cAAc,cAAe;AAAA,IAC1D;AAEA,QAAI,KAAK,aAAa,GAAG;AACrB,UAAI,WAAW,wBAAwB,IAAI,WAAW,oBAAoB,CAAC,IAAI;AAC/E,UAAI,CAAC,UAAU;AACX,cAAM,EAAE,KAAK,KAAK,IAAI;AACtB,cAAM,EAAE,QAAQ,QAAQ,IAAI,UAAU,MAAM;AAC5C,mBAAW,EAAE,KAAK,QAAQ,MAAM,QAAQ,GAAG,WAAS;AAChD,cAAI,CAAC,aAAa,eAAe,OAAO,UAAU,KAAK,WAAW,YAAY,KAAK,IAAI;AACnF,uBAAW;AAAA,UACf;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AAEA,YAAM,iBAAiB,WAAW,cAAc,MAAM,IAAI;AAC1D,UAAI,mBAAmB,gBAAgB;AACnC,2BAAmB;AACnB,eAAO,eAAe,cAAc;AAAA,MACxC;AAAA,IACJ;AACA,QAAI,oBAAoB,iBAAiB;AAErC,YAAM,4BAA4B,MAAM;AACpC,cAAM,sBAAsB,YAAY,YAAY,CAAC,gBAAgB,kBAAkB;AACnF,iCAAuB,YAAa,cAAe,QAAQ,iBAAiB,cAAc,gBAAgB,CAAC,CAAC,cAAc,YAAY,eAAe,SAAS,gBAAgB,eAAe,aAAa,wBAAwB,WAAW,oBAAoB,CAAC;AAClQ,6BAAmB;AAAA,QACvB,CAAC;AAED,oBAAY,eAAe,cAAc,GAAG,YAAY;AACpD,gBAAM,kBAAkB,MAAM,YAAa,eAAgB,iBAAiB,QAAQ,sBAAsB,IAAI;AAE9G,gBAAM,WAAW,MAAM;AAAA,YAA0B;AAAA;AAAA;AAAA,YAGjD,IAAI,aAAa,SAAS,YAAY,CAAC,CAAC,KAAK,KAAK,oBAAoB;AAAA,YAAiB;AAAA;AAAA,YAEvF,MAAM,aAAa,KAAK,mBAAmB,gBAAgB,CAAC;AAAA,UAAC;AAE7D,sBAAY,aAAa,KAAK,eAAe,gBAAgB,CAAC;AAAA,QAClE,CAAC;AACD,eAAO;AAAA,MACX;AACA,UAAI,oBAAoB,gBAAgB;AAEpC,cAAM,kBAAkB,gBAAgB,CAAC;AACzC,YAAI,iBAAiB;AAEjB,sBAAY,QAAQ,IAAI;AAAA,QAC5B;AAEA,eAAO,kBAAkB,0BAA0B,IAAI,YAAY,eAAe,cAAc,CAAC;AAAA,MACrG;AACA,YAAM,sBAAsB,0BAA0B;AAEtD,YAAM,kBAAmB,qBAAqB,kBAAkB,sBAAsB,KAAK,qBAAqB,IAAI,sBAAsB,IAAI,cAAc;AAC5J,kBAAY,qBAAqB,kBAAgB;AAE7C,oBAAY,KAAK,IAAI;AAAA,MACzB,CAAC;AAED,aAAO,eAAe,eAAe;AAAA,IACzC;AACA,uBAAmB;AACnB,WAAO,KAAK;AAAA,EAChB;AACA,SAAO;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA;AAAA,IAEH,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKC,YAAY,CAACD,aAAY;AACrB,qBAAa,GAAG,YAAYA,QAAO;AAAA,MACvC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,mBAAmB,CAACA,aAAY;AAC5B,qBAAa,GAAG,mBAAmBA,QAAO;AAAA,MAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe,CAACA,aAAY;AACxB,qBAAa,GAAG,eAAeA,QAAO;AAAA,MAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS,CAACA,aAAY;AAClB,qBAAa,GAAG,SAASA,QAAO;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,SAAS,aAAa,SAAS,SAAS,CAAC,GAAG;AACxC,QAAM,EAAE,gBAAgB,eAAe,YAAY,IAAI,iBAAiB,kBAAkB,CAAC;AAC3F,QAAM,EAAE,aAAa,KAAK,IAAI;AAC9B,QAAM,EAAE,GAAG,qBAAqB,GAAG,kBAAkB,GAAG,SAAS,GAAGE,eAAc,IAAI,6BAA6B,SAAS,MAAM;AAClI,QAAM,SAAS,WAAW,qBAAqB;AAAA,IAC3C,GAAG;AAAA,IACH,eAAe;AAAA,IACf,YAAY,CAAC,KAAK,SAAS;AACvB,YAAM,mBAAmB,iBAAiB,KAAK,IAAI;AACnD,iBAAW,KAAK,MAAM,gBAAgB;AACtC,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACD,EAAAA,eAAc,MAAM;AACpB,SAAO,eAAe;AAAA,IAClB,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACL;AAQA,IAAI,SAAS,CAAC,WAAW,cAAc;AAEnC,MAAI,cAAc,WAAW;AACzB,WAAO;AAAA,EACX;AACA,SAAO,eAAe,SAAS,MAAM,eAAe,SAAS;AACjE;AASA,IAAM,sBAAsB,OAAO,mBAAmB,YAAY,oBAAoB,eAAe,eAAe;AAChH,QAAM,qBAAqB,CAAC,cAAc,CAAC,MAAM,YAAY,OAAO,sBAAoB;AACpF,QAAI,sBAAsB,gBAAgB;AACtC,aAAO;AAAA,IACX;AACA,UAAM,OAAO,UAAU,iBAAiB,MAAM,EAAE,QAAQ;AACxD,UAAM,SAAS,WAAW,mBAAmB,MAAM,IAC7C,WAAW,mBAAmB,IAAI,IAClC,SAAS;AACf,WAAO,WAAW,eAAe,iBAAiB,SAAS;AAAA,EAC/D,CAAC;AACD,SAAO;AAAA,IACH,GAAG,mBAAmB,eAAe,SAAS,CAAC;AAAA;AAAA,IAE/C,GAAI,wBAAwB,IAAI,oBAAoB,MAAM,8BAA8B,GAAG,SAAS,CAAC,IAAI,CAAC;AAAA,EAC9G;AACJ;AAQA,IAAM,kBAAkB,OAAO,mBAAmB,YAAY,oBAAoB,eAAe,gBAAgB,MAAM,oBAAoB,mBAAmB,WAAW,YAAY,GAAG,CAAC;AAOzL,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,eAAe,QAAQ,UAAU,KAAK,WAAW,YAAY,MAAM;AAQ/F,IAAM,oBAAoB,OAAO,SAAS,iBAAiB;AAEvD,MAAI,qBAAqB;AACrB,wBAAoB,eAAe,SAAS,KAAK,WAAW,IAAI,iBAAiB,WAAW,WAAW,CAAC;AACxG,UAAM,oBAAoB,KAAK;AAAA,EACnC;AACA,SAAO,YAAY,SAAS,YAAY;AAC5C;AAEA,IAAM,iBAAiB,CAAC,SAAS,SAAS,CAAC,MAAM;AAC7C,MAAI,aAAa;AACjB,QAAM,EAAE,cAAc,WAAW,mBAAmB,WAAW,gBAAgB,WAAW,cAAc,GAAG,WAAW,IAAK,IAAI;AAC/H,QAAM,EAAE,WAAW,aAAa,eAAe,eAAe,IAAI,iBAAiB,kBAAkB,CAAC;AACtG,QAAM,SAAS,WAAW,SAAS;AAAA,IAC/B,GAAG;AAAA,IACH,eAAe;AAAA,EACnB,CAAC;AACD,QAAM,SAAS,MAAM;AACjB,QAAI,YAAY;AACZ,aAAO,KAAK;AACZ,UAAI,WAAW,GAAG;AACd,qBAAa;AACb,mBAAW,MAAM;AACb,uBAAa;AAAA,QACjB,GAAG,QAAQ;AAAA,MACf;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,aAAa;AACjB,MAAI,WAAW;AACf,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,YAAU,MAAM;AACZ,QAAI,CAAC,gBAAgB,KAAK;AACtB,mBAAa,gBAAgB,eAAe,UAAU,QAAQ,MAAM,IAAI;AACxE,iBAAW,cAAc,eAAe,QAAQ,QAAQ,MAAM,IAAI;AAClE,qBAAe,mBAAmB,eAAe,aAAa,QAAQ,MAAM,IAAI;AAChF,mBAAa,cAAc,IAAI,eAAe,UAAU,QAAQ,MAAM,IAAI;AAAA,IAC9E;AAAA,EACJ,CAAC;AACD,cAAY,MAAM;AACd,eAAW;AACX,aAAS;AACT,iBAAa;AACb,eAAW;AAAA,EACf,CAAC;AACD,SAAO;AACX;AACA,IAAM,KAAK,CAAC,MAAM,YAAY;AAC1B,SAAO,iBAAiB,MAAM,OAAO;AACrC,SAAO,MAAM,OAAO,oBAAoB,MAAM,OAAO;AACzD;AACA,eAAe,YAAY,YAAU,GAAG,UAAU,MAAM;AACxD,eAAe,UAAU,YAAU,GAAG,SAAS,MAAM;AACrD,eAAe,eAAe,YAAU;AACpC,QAAM,SAAS,MAAM,SAAS,oBAAoB,aAAa,OAAO;AACtE,SAAO,GAAG,oBAAoB,MAAM;AACxC;AACA,eAAe,YAAY,CAAC,QAAQ,WAAW;AAC3C,QAAM,QAAQ,YAAY,QAAQ,OAAO,WAAW;AACpD,SAAO,MAAM,cAAc,KAAK;AACpC;AAEA,IAAM,eAAe;AACrB,IAAM,gBAAgB,aAAa,YAAY;AAC/C,IAAI,aAAa,CAAC,SAAS,SAAS,CAAC,MAAM;AACvC,QAAM,EAAE,kBAAkB,WAAW,IAAI;AACzC,gBAAc,qBAAqB,kBAAkB,mBAAmB,GAAG,yCAAyC;AACpH,QAAM,EAAE,QAAQ,KAAK,WAAW,gBAAgB,eAAe,eAAe,IAAI,iBAAiB,kBAAkB,CAAC;AACtH,QAAM,YAAY,OAAO,GAAG,WAAW;AACvC,QAAM,kBAAkB,WAAW,SAAS;AAAA,IACxC,GAAG;AAAA,IACH,eAAe;AAAA,IACf,WAAW;AAAA,IACX,eAAe,UAAU,CAAC,SAAS,GAAG,GAAG;AAAA;AAAA,IAEzC,YAAY,aAAa,CAAC,KAAK,SAAS,WAAW,EAAE,GAAG,KAAK,KAAK,GAAG,IAAI,IAAI;AAAA,EACjF,CAAC;AACD,QAAM,QAAQ,IAAI,cAAc;AAChC,QAAM,OAAO,IAAI,SAAS,YAAY,YAAY,CAAC,SAAS,WAAW;AACnE,QAAI,UAAU,KAAK,GAAG;AAClB,sBACK,KAAK,GAAG,IAAI,EACZ,KAAK,YAAU;AAChB,kBAAU,IAAI,OAAO,oBAAoB;AACzC,cAAM,UAAU,YAAY,MAAM;AAC9B,oBAAU,KAAK;AACf,cAAI,UAAU,KAAK,GAAG;AAClB,0BAAc,MAAM,OAAO;AAAA,UAC/B;AAAA,QACJ,GAAG,GAAI;AACP,gBAAQ,MAAM;AAAA,MAClB,CAAC,EACI,MAAM,YAAU,OAAO,MAAM,CAAC;AAAA,IACvC,OACK;AACD,aAAO,YAAY,YAAY,cAAc,+BAA+B,CAAC;AAAA,IACjF;AAAA,EACJ,CAAC;AACD,SAAO,eAAe;AAAA,IAClB,GAAG;AAAA,IACH;AAAA,IACA,GAAG,UAAU,CAAC,SAAS,CAAC;AAAA,EAC5B,CAAC;AACL;AAEA,IAAM,kBAAkB,OAAO,aAAa;AAC5C,IAAM,iBAAiB,CAAC,gBAAgB,OAAO,cAAc,MAAM,qBAAqB,cAAc,CAAC;AACvG,IAAM,eAAe,CAAC;AACtB,IAAM,gBAAgB,CAAC,SAAS;AAC5B,QAAM,eAAe,CAAC,UAAW,QAAQ,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,cAAc,KAAK,IAAI,EAAE,GAAG,MAAM,IAAI;AACrG,SAAO,WAAW,aAAa,IAAI,GAAG,YAAY;AACtD;AACA,IAAI,UAAU,CAAC,SAAS,SAAS,CAAC,MAAM;AACpC,QAAM,oBAAoB;AAC1B,QAAM,EAAE,IAAI,aAAa,OAAO,qBAAqB,YAAY,YAAY,WAAW,IAAI;AAC5F,oBAAkB;AAClB,QAAM,EAAE,QAAQ,KAAK,UAAU,WAAW,OAAO,WAAW,gBAAgB,eAAe,eAAe,IAAI,iBAAiB,kBAAkB,CAAC;AAClJ,QAAM,gBAAgB,cAAc,KAAK;AACzC,QAAM,cAAc,gBAAgB,MAAM,SAAS;AAGnD,QAAM,cAAc,KAAK,kBAAkB,EAAE,IAAI;AACjD,QAAM,OAAO,OAAO,cAAc,WAAW,GAAG,MAAM;AACtD,QAAM,gBAAgB;AACtB,QAAM,eAAe,mBAAmB;AAExC,QAAM,wBAAwB,SAAS,aAAa,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5E,QAAM,iBAAiB,WAAW,sBAAsB,OAAO,EAAE;AACjE,QAAM,cAAc,eAAe,sBAAsB,SAAS,EAAE;AACpE,QAAM,WAAW,SAAS,UAAU;AACpC,QAAMC,uBAAsB,SAAS,0BAA0B,gBAAgB,MAAM,cAAc,cAAc,CAAC;AAElH,QAAM,qBAAqB,SAAS,KAAK;AACzC,QAAM,uBAAuB,WAAW,IAAI,SAAS,cAAc,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,IACjF,GAAG;AAAA,IACH,eAAe;AAAA;AAAA,IAEf,YAAY,aACN,CAAC,KAAK,SAAS,WAAW;AAAA,MACxB,GAAG;AAAA;AAAA,MAEH,mBAAmB,EAAE,YAAY,MAAM;AAAA,IAC3C,GAAG,IAAI,IACL;AAAA;AAAA;AAAA,IAGN,WAAW,eAAe,cAAc,aAAa;AAAA,EACzD,CAAC;AAID,QAAM,QAAQ,MAAM;AAChB,aAAS,UAAU;AACnB,UAAM,iBAAiB,cAAc,WAAW;AAChD,uBAAmB,KAAK,IAAI;AAC5B,mBAAe,eAAe,OAAO,WAAW;AAAA,EACpD;AAKA,QAAM,aAAa,CAAC,YAAY;AAC5B,SAAK,IAAI;AAAA,MACL,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACP;AAAA,EACJ;AACA,QAAM,eAAe,eAAe;AAAA;AAAA,IAEhC,GAAG;AAAA,IACH,GAAG,UAAU,CAAC,IAAI,CAAC;AAAA,IACnB;AAAA,IACA;AAAA;AAAA,IAEA,UAAUH,UAAS;AACf,mBAAa,GAAG,iBAAiBA,QAAO;AAAA,IAC5C;AAAA,EACJ,CAAC;AAGD,MAAI,IAAI;AAEJ,QAAI,CAAC,aAAa;AACd,yBAAmB,UAAU;AAAA,IACjC;AAEA,QAAI,mBAAmB,SAAS;AAC5B,wBAAkB,EAAE,IAAI;AAAA,QACpB;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,EAAE,MAAM,UAAU,IAAI;AAC5B,YAAU,MAAM;AAEZ,QAAI,eAAe,CAAC,aAAa;AAG7B,YAAM,eAAeG,qBAAoB,QAAQ,YAAY,eAAe,IAAI,WAAW,CAAC;AAE5F,UAAI,cAAc;AACd,aAAK,IAAI;AAET,qBAAa,KAAK,iBAAiB,cAAc;AAAA,MACrD;AACA,qBAAe,aAAa,KAAK,GAAG,CAAC,CAAC;AAAA,IAC1C;AAAA,EACJ,CAAC;AAED,QAAM,CAAC,IAAI,GAAG,MAAM;AAChB,QAAI,SAAS,WAAW,CAAC,aAAa;AAClC,eAAS,UAAU;AACnB;AAAA,IACJ;AACA,mBAAe,IAAI,aAAaA,qBAAoB,QAAQ,UAAU,KAAK,CAAC,CAAC;AAAA,EACjF,CAAC;AAED,YAAU,MAAM;AACZ,2BAAuB,MAAM;AAAA,EACjC,CAAC;AAGD,SAAO,eAAe,CAAC,mBAAmB,UAAU,YAAY,eAAe;AACnF;AAEA,IAAM,gBAAgB,OAAO,gBAAgB;AAC7C,IAAM,eAAe,OAAO,eAAe;AAC3C,IAAM,aAAa;AACnB,IAAM,WAAW,aAAa,UAAU;AACxC,IAAI,sBAAsB,CAAC,SAAS,SAAS,CAAC,MAAM;AAChD,QAAM,EAAE,QAAQ,GAAG,UAAU,EAAE,OAAO,IAAK,GAAG,aAAa,KAAK,IAAI;AACpE,QAAM,EAAE,KAAK,UAAU,gBAAgB,eAAe,eAAe,IAAI,iBAAiB,kBAAkB,CAAC;AAC7G,QAAM,eAAe,mBAAmB;AACxC,QAAM,aAAa,SAAS,CAAC;AAC7B,QAAM,oBAAoB,SAAS,cAAc;AACjD,QAAM,wBAAwB,SAAS,cAAc;AACrD,QAAM,aAAa,SAAS,cAAc;AAC1C,QAAM,aAAa,SAAS,UAAU;AACtC,QAAM,aAAa,SAAS,cAAc;AAC1C,QAAM,iBAAiB,SAAS,WAAW,CAAC;AAC5C,QAAM,aAAa,CAAC,QAAQ,MAAM,UAAU;AAExC,iBAAa,MAAM;AACf,mBAAa,KAAK,cAAc,YAAa,oBAAqB,eAAe,MAAM,QAAQ,IAAI,GAAG,OAAO,WAAW,OAAO,CAAC;AAChI,wBAAkB,UAAU;AAC5B,iBAAW,UAAU;AAAA,IACzB,CAAC;AAAA,EACL;AACA,QAAM,qBAAqB,WAAW,SAAS;AAAA,IAC3C,GAAG;AAAA,IACH,eAAe;AAAA,IACf,WAAW,KAAK,MAAM;AAClB,iBAAW;AAAA,QACP,GAAG;AAAA,QACH,mBAAmB;AAAA;AAAA,UAEf;AAAA,QACJ;AAAA,MACJ,GAAG,MAAM,eAAe,CAAC;AACzB,YAAM,EAAE,aAAa,MAAM,MAAM,QAAQ,eAAe,IAAI;AAC5D,qBAAe;AACf,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,aAAa,CAAC,QAAQ,eAAe;AACvC,gBAAQ,IAAI;AAAA,MAChB;AACA,YAAM,cAAc,CAAC,UAAU;AAC3B,mBAAW;AACX,oBAAY,MAAM,IAAI;AACtB,qBAAa,WAAW,OAAO;AAC/B,mBAAW,QAAQ,MAAM,KAAK;AAAA,MAClC;AACA,UAAI,CAAC,QAAQ,GAAG;AACZ,qBAAa,eAAe,QAAQ,SAAS,WAAS;AAClD,sBAAY,KAAK;AACjB,yBAAe,UAAU,WAAW;AAAA,QACxC,CAAC;AAAA,MACL;AACA,iBAAW,SAAS;AACpB,iBAAW,UAAU;AACrB,4BAAsB,UAAU;AAChC,iBAAW,UAAU;AAMrB,aAAO,KAAK,EACP;AAAA,QAAK,SAAO;AAEb,qBAAW;AACX,iBAAO;AAAA,QACX;AAAA;AAAA,QAEA,WAAS;AAEL,cAAI,CAAC,kBAAkB,YAAY,SAAS,KAAK,IAAI,WAAW,UAAU,QAAQ,MAAM,OAAO,GAAG,IAAI,IAAI;AACtG,uBAAW,WAAW;AAEtB,kBAAM,aAAa,iBAAiB,SAAS,WAAW,OAAO;AAE/D,uBAAW,UAAU,aAAa,MAAM;AAEpC,2BAAa,KAAK,eAAe,YAAa,qBAAsB,eAAe,MAAM,QAAQ,IAAI,GAAG,WAAW,SAAS,UAAU,CAAC;AAEvI,2BAAa,KAAK,GAAG,IAAI,GAAG,IAAI;AAAA,YACpC,GAAG,UAAU;AAAA,UACjB,OACK;AACD,oBAAQ,kBAAkB,WAAW;AACrC,wBAAY,KAAK;AAAA,UACrB;AAEA,iBAAO,cAAc,KAAK;AAAA,QAC9B;AAAA,MAAC,EACI,QAAQ,MAAM;AACf,mBAAW,UAAU;AAAA,MACzB,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAMD,QAAM,OAAO,MAAM;AACf,aAAS,mBAAmB,aAAa,SAAS,EAAE,GAAG,oCAAoC;AAC3F,sBAAkB,UAAU,YAAY,YAAY,YAAY,qBAAqB;AACrF,QAAI,WAAW,SAAS;AACpB,yBAAmB,MAAM;AAAA,IAC7B,OACK;AACD,qBAAe,QAAQ,OAAO,kBAAkB,OAAO;AAAA,IAC3D;AAAA,EACJ;AAMA,QAAM,UAAU,CAACH,aAAY;AACzB,iBAAa,GAAG,eAAe,WAASA,SAAQ,KAAK,CAAC;AAAA,EAC1D;AAUA,QAAM,SAAS,CAACA,aAAY;AACxB,iBAAa,GAAG,cAAc,WAASA,SAAQ,KAAK,CAAC;AAAA,EACzD;AACA,SAAO,eAAe;AAAA,IAClB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAEA,IAAM,kBAAkB,OAAO,SAAS;AACxC,IAAM,qBAAqB,OAAO,YAAY;AAC9C,IAAM,mBAAmB,OAAO,UAAU;AAC1C,IAAI;AAAA,CACH,SAAUI,oBAAmB;AAC1B,EAAAA,mBAAkBA,mBAAkB,YAAY,IAAI,CAAC,IAAI;AACzD,EAAAA,mBAAkBA,mBAAkB,MAAM,IAAI,CAAC,IAAI;AACnD,EAAAA,mBAAkBA,mBAAkB,QAAQ,IAAI,CAAC,IAAI;AACzD,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAM,WAAW,aAAa,QAAQ;AACtC,IAAM,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AACb;AACA,IAAI,SAAS,CAAC,SAAS,SAAS,CAAC,MAAM;AACnC,QAAM;AAAA,IAAE;AAAA,IAAa;AAAA,IAAiB,6BAA6B;AAAA;AAAA,IAEnE,YAAY;AAAA,EAAW,IAAI;AAE3B,QAAM,YAAY;AAClB,MAAI,EAAE,SAAS,IAAI,kBAAkB;AACrC,eAAa,QAAQ,aAAa,SAAS,WAAY,WAAW;AAClE,QAAM,EAAE,QAAQ,KAAK,WAAW,aAAa,WAAW,eAAe,IAAI,iBAAiB,kBAAkB,CAAC;AAC/G,QAAM,YAAY,IAAI,CAAC,CAAC;AACxB,QAAM,cAAc,IAAI,cAAc;AACtC,QAAM,oBAAoB,IAAI,cAAc;AAC5C,QAAM,OAAO,OAAO,aAAa,MAAM;AACvC,QAAM,aAAa,OAAO,kBAAkB,QAAQ,YAAY;AAChE,MAAI,iBAAiBxB,kBAAiB,OAAO;AAC7C,MAAI;AACJ,QAAM,eAAe,mBAAmB;AAExC,QAAM,iBAAiB,IAAI,oBAAI,IAAI,CAAC;AACpC,QAAM,SAAS,CAACoB,aAAY;AACxB,iBAAa,GAAG,iBAAiBA,QAAO;AAAA,EAC5C;AACA,QAAM,YAAY,CAACA,aAAY;AAC3B,iBAAa,GAAG,oBAAoBA,QAAO;AAAA,EAC/C;AACA,QAAM,UAAU,CAACA,aAAY;AACzB,iBAAa,GAAG,kBAAkBA,QAAO;AAAA,EAC7C;AACA,QAAM,yBAAyB,IAAI,KAAK;AACxC,QAAM,uBAAuB,IAAI,OAAO;AACxC,QAAM,0BAA0B,IAAI,IAAI;AAIxC,QAAM,qBAAqB,CAAC,aAAa;AAErC,UAAM,EAAE,UAAU,IAAI,WAAW,QAAQ;AACzC,sBAAkB;AAClB,QAAI,KAAK,eAAe,GAAG;AACvB,6BAAuB,UAAU;AAAA,IACrC,WACS,mBAAmB,cAAc,eAAe,GAAG;AACxD,YAAM,EAAE,WAAW,gBAAgB,SAAS,cAAc,YAAY,gBAAgB,IAAI;AAC1F,6BAAuB,UAAU,KAAK,cAAc,IAAI,iBAAiB,uBAAuB;AAChG,2BAAqB,UAAU,KAAK,YAAY,IAAI,eAAe,qBAAqB;AACxF,8BAAwB,UAAU,KAAK,eAAe,IAAI,kBAAkB,wBAAwB;AAAA,IACxG;AAAA,EACJ;AAMA,QAAM,qBAAqB,OAAO,mBAAmB;AACjD,UAAM,EAAE,SAAS,WAAW,cAAc,MAAM,IAAI,UAAU,cAAc;AAC5E,UAAM,cAAc,MAAM;AAC1B,UAAM,kBAAkB,MAAM,YAAY,aAAc,WAAW,CAAC,CAAE;AACtE,SAAK,IAAI;AAET,qBAAiB,cAAc;AAC/B,WAAO;AAAA,EACX;AAMA,QAAM,iBAAiB,OAAO,WAAW,gBAAgB;AACrD,aAAS,CAAC,CAAC,YAAY,SAAS,gCAAgC;AAChE,UAAM,KAAK,YAAY;AACvB,UAAM,YAAY,IAAI,cAAc,eAAe,MAAM,gBAAgB,UAAU,OAAO,GAAG,EAAE;AAC/F,QAAI,cAAc,YAAY,MAAM;AAChC,aAAO,QAAQ,QAAQ,SAAS;AAAA,IACpC;AACA,UAAM,gBAAgB,6BAA6B,uBAAuB,UAAU;AACpF,UAAM,cAAc,6BAA6B,qBAAqB,UAAU;AAChF,UAAM,gBAAgB,6BAA6B,wBAAwB,UAAU;AACrF,UAAM,IAAI;AAAA,MAAe,YAAY,aAAa,SAAO,mBAAmB,cAAc,KAAK,cAAc,CAAC,GAAG,WAAS,mBAAmB,YAAY,OAAO,cAAc,CAAC,CAAC;AAAA;AAAA,MAEhL,MAAM;AACF,sBAAc,cAAc;AAAA,MAChC;AAAA,IAAC;AAED,WAAO;AAAA,MAAY;AAAA;AAAA,MAEnB,SAAO,IAAI,qBAAqB,WAAW,GAAG;AAAA;AAAA,MAE9C,WAAS,IAAI,mBAAmB,WAAW,KAAK;AAAA,IAAC;AAAA,EACrD;AAKA,QAAM,eAAe,CAAC,aAAa,CAAC,UAAU;AAC1C,QAAI,MAAM,UAAU,gBAAgB;AAChC,aAAO,SAAS,KAAK;AAAA,IACzB;AACA,WAAO,aAAa,KAAK,kBAAkB,KAAK;AAAA,EACpD;AAEA,QAAM,gBAAgB,CAAC,WAAW,oBAAoB;AAClD,QAAI;AACJ,UAAM,aAAa,eAAe;AAClC,QAAI,CAAC,WAAW,IAAI,SAAS,GAAG;AAC5B,YAAM,oBAAoB,YAAY,eAAa;AAC/C,YAAIK;AACJ,YAAI,UAAU,WAAW,GAAG;AACxB,WAACA,MAAK,YAAY,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,oBAAoB,WAAW,kBAAkB,CAAC,CAAC;AACtH,yBAAe,QAAQ,OAAO,SAAS;AAAA,QAC3C;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,kBAAkB,CAAC;AACnC,iBAAW,IAAI,WAAW,iBAAiB;AAC3C,OAAC,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,WAAW,WAAS;AACpG,oBAAY,eAAe,WAAW,QAAQ,QAAQ,MAAM,IAAI,CAAC,GAAG,aAAa,OAAO,CAAC;AAAA,MAC7F,CAAC;AAAA,IACL;AACA,UAAM,CAAC,OAAO,IAAI,WAAW,IAAI,SAAS;AAC1C,WAAO,QAAQ,eAAe;AAAA,EAClC;AAIA,QAAM,iBAAiB,MAAM;AACzB,mBAAe,QAAQ,QAAQ,CAAC,CAAC,IAAI,IAAI,UAAU,MAAM;AACrD,iBAAW;AAAA,IACf,CAAC;AAAA,EACL;AACA,QAAM,SAAS,SAAS,MAAM;AAC1B,QAAI;AAEJ,eAAW,IAAI,kBAAkB;AACjC,gBAAY,eAAe,YAAY,MAAM,QAAQ,QAAQ,CAAC,GAAG,WAAS,aAAa,KAAK,iBAAiB,KAAK,CAAC;AAEnH,KAAC,KAAK,kBAAkB,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAAA,EACrF,CAAC;AACD,QAAM,UAAU,SAAS,CAAC,UAAU;AAChC,QAAI,IAAI;AACR,eAAW,IAAI,kBAAkB;AACjC,gBAAY,eAAe,YAAY,OAAO,QAAQ,QAAQ,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,aAAa,QAAQ,OAAO,SAAS,KAAK,WAAW,CAAC,GAAG,aAAa,CAAAC,WAAS,aAAa,KAAK,oBAAoBA,MAAK,CAAC,CAAC;AACnP,KAAC,KAAK,kBAAkB,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAAA,EACrF,CAAC;AACD,QAAM,YAAY,SAAS,CAAC,UAAU;AAClC,gBAAY,eAAe,YAAY,SAAS,QAAQ,QAAQ,MAAM,IAAI,CAAC,GAAG,aAAa,CAAAA,WAAS,aAAa,KAAK,oBAAoBA,MAAK,CAAC,CAAC;AAAA,EACrJ,CAAC;AAID,QAAM,QAAQ,MAAM;AAChB,UAAM,KAAK,YAAY;AACvB,QAAI,CAAC,IAAI;AACL;AAAA,IACJ;AACA,QAAI,kBAAkB,SAAS;AAE3B,wBAAkB,QAAQ,QAAQ;AAAA,IACtC;AAEA,OAAG,MAAM;AACT,OAAG,oBAAoB,YAAY,MAAM,MAAM;AAC/C,OAAG,oBAAoB,YAAY,OAAO,OAAO;AACjD,OAAG,oBAAoB,YAAY,SAAS,SAAS;AACrD,eAAW,IAAI,kBAAkB;AAGjC,mBAAe,QAAQ,QAAQ,CAAC,CAAC,GAAG,YAAY,GAAG,cAAc;AAC7D,SAAG,oBAAoB,WAAW,YAAY;AAAA,IAClD,CAAC;AAAA,EACL;AAIA,QAAM,UAAU,IAAI,SAAS;AACzB,QAAI,KAAK,YAAY;AACrB,QAAI,aAAa,kBAAkB;AACnC,QAAI,MAAM,WAAW;AAEjB,YAAM;AAAA,IACV;AAEA,QAAI,CAAC,YAAY;AACb,mBAAa,kBAAkB,UAAU,WAAW;AAEpD,oBACI,WAAW,QAAQ,QAAQ,MAAM;AAC7B,qBAAa;AAAA,MACjB,CAAC;AAAA,IACT;AACA,cAAU,UAAU;AACpB,qBAAiB1B,kBAAiB,SAAS,IAAI;AAE/C,uBAAmB,cAAc;AACjC,UAAM,EAAE,OAAO,IAAI,UAAU,cAAc;AAC3C,UAAM,EAAE,SAAS,IAAI,IAAI;AACzB,UAAM,UAAU,kBAAkB,SAAS,KAAK,MAAM;AAEtD,SAAK,IAAI,YAAY,SAAS,EAAE,gBAAgB,CAAC;AACjD,gBAAY,UAAU;AACtB,eAAW,IAAI,kBAAkB;AAGjC,OAAG,iBAAiB,YAAY,MAAM,MAAM;AAC5C,OAAG,iBAAiB,YAAY,OAAO,OAAO;AAC9C,OAAG,iBAAiB,YAAY,SAAS,SAAS;AAGlD,mBAAe,QAAQ,QAAQ,CAAC,CAAC,GAAG,YAAY,GAAG,cAAc;AAC7D,aAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,WAAW,WAAS;AAC5E,oBAAY,eAAe,WAAW,QAAQ,QAAQ,MAAM,IAAI,CAAC,GAAG,aAAa,YAAY,CAAC;AAAA,MAClG,CAAC;AAAA,IACL,CAAC;AACD,WAAO,WAAW;AAAA,EACtB;AACA,cAAY,MAAM;AACd,UAAM;AAGN,iBAAa,IAAI,eAAe;AAChC,iBAAa,IAAI,kBAAkB;AACnC,iBAAa,IAAI,gBAAgB;AACjC,mBAAe;AAAA,EACnB,CAAC;AAED,YAAU,MAAM;AACZ,QAAI;AACJ,QAAI,WAAW;AACX,cAAQ,GAAG,CAAC,CAAC;AACb,OAAC,KAAK,kBAAkB,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,MAAM,MAAM;AAAA,MAAE,CAAC;AAAA,IACpG;AAAA,EACJ,CAAC;AACD,SAAO,eAAe;AAAA,IAClB,MAAM;AAAA,IACN;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG,UAAU,CAAC,YAAY,IAAI,CAAC;AAAA,EACnC,CAAC;AACL;AAEA,IAAI,mBAAmB;AAEvB,IAAM,aAAa,CAAC;AACpB,IAAM,2BAA2B,CAAC,YAAY,CAAC,CAAC,QAAQ;AACxD,IAAM,SAAS,aAAa,YAAY;AASxC,IAAM,6BAA6B,CAAC,OAAO;AACvC,QAAM,EAAE,KAAK,YAAY,IAAI,iBAAiB,kBAAkB,CAAC;AACjE,QAAM,YAAY,IAAI,mBAAmB,CAAC;AAC1C,MAAI,UAAU,UAAU,kBAAkB;AACtC,wBAAoB;AAAA,EACxB;AACA,cAAY,MAAM;AACd,QAAI;AACJ,SAAK,KAAK,WAAW,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,OAAO,GAAG;AAElF,aAAO,WAAW,EAAE,EAAE,UAAU,OAAO;AAAA,IAC3C;AAAA,EACJ,CAAC;AACD,SAAO,CAAC,SAAS,SAAS;AAEtB,UAAM,EAAE,OAAO,aAAa,oBAAoB,CAAC,EAAE,IAAI;AACvD,UAAM,SAAS,CAAC,cAAc;AAC1B,iBAAW,OAAO,WAAW;AACzB,oBAAY,GAAG,MAAM,YAAY,GAAG,EAAE,IAAI,UAAU,GAAG;AAAA,MAC3D;AAAA,IACJ;AAEA,UAAM,QAAS,WAAW,EAAE,IAAI,WAAW,EAAE,KAAK,CAAC;AACnD,UAAM,UAAU,yBAAyB,OAAO,IAC1C;AAAA,MACE,GAAG;AAAA,MACH,MAAM,QAAQ;AAAA,MACd;AAAA,MACA;AAAA,IACJ,IACE;AAAA,MACE,GAAG;AAAA,MACH,OAAO,QAAQ;AAAA,MACf;AAAA,MACA;AAAA,IACJ;AACJ,UAAM,UAAU,OAAO,IAAI;AAC3B,WAAO,KAAK;AAAA,EAChB;AACJ;AAOA,IAAM,eAAe,CAAC,IAAI,SAAS,SAAS,UAAU;AAClD,QAAM,UAAU,CAAC;AACjB,MAAI,OAAO,OAAO,YAAY,SAAS,EAAE,KAAK,SAAS,EAAE,GAAG;AACxD,eAAW,EAAE,KAAK,SAAS,SAAS,GAAG,aAAa,WAAW,EAAE,CAAC,CAAC;AAAA,EACvE,WACS,WAAW,IAAI,MAAM,GAAG;AAC7B,YAAQ,WAAW,WAAW,UAAU,GAAG,YAAU,GAAG,KAAK,MAAM,CAAC,GAAG,YAAU;AAC7E,eAAS,SAAS,GAAG,aAAa,WAAW,MAAM,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACL;AAEA,MAAI,QAAQ,WAAW,KAAK,CAAC,QAAQ;AACjC,WAAO,OAAO,wCAAwC,GAAG,SAAS,CAAC,IAAI;AAAA,EAC3E;AACA,UAAQ,WAAW,SAAS,KAAK,GAAG,OAAO;AAC/C;", "names": ["getHandlerMethod", "EnumHookType", "state", "assert", "frontStates", "methodInstance", "data", "error", "debounce", "page", "states", "force", "rawData", "customSerializers", "<PERSON><PERSON>", "Undefined", "key", "queueName", "queue", "silentQueueMap", "handler", "config", "decorateEvent", "serializerPerformer", "SSEHookReadyState", "_a", "event"]}
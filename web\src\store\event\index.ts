/*
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-04-26 08:40:27
 * @LastEditors: zhai
 * @LastEditTime: 2025-04-26 08:40:48
 */
import { defineStore } from 'pinia';

type EventHandler = (data?: any) => void;

export const useEventStore = defineStore('event', {
  state: () => ({
    listeners: {} as Record<string, EventHandler[]>
  }),
  actions: {
    on(eventName: string, handler: EventHandler) {
      if (!this.listeners[eventName]) {
        this.listeners[eventName] = [];
      }
      this.listeners[eventName].push(handler);
    },
    off(eventName: string, handler?: EventHandler) {
      if (!handler) {
        delete this.listeners[eventName];
      } else {
        const index = this.listeners[eventName]?.indexOf(handler);
        if (index > -1) {
          this.listeners[eventName].splice(index, 1);
        }
      }
    },
    emit(eventName: string, data?: any) {
      this.listeners[eventName]?.forEach(handler => handler(data));
    }
  }
});
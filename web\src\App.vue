<!--
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-01-04 15:11:53
 * @LastEditors: zhai
 * @LastEditTime: 2025-02-24 21:36:38
-->
<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { NConfigProvider, darkTheme } from 'naive-ui';
import type { WatermarkProps } from 'naive-ui';
import { useAppStore } from './store/modules/app';
import { useThemeStore } from './store/modules/theme';
import { naiveDateLocales, naiveLocales } from './locales/naive';
import { initMqttClient, releaseMqtt } from './mqtt';

defineOptions({
  name: 'App'
});

const appStore = useAppStore();
const themeStore = useThemeStore();

const naiveDarkTheme = computed(() => (themeStore.darkMode ? darkTheme : undefined));

const naiveLocale = computed(() => {
  return naiveLocales[appStore.locale];
});

const naiveDateLocale = computed(() => {
  return naiveDateLocales[appStore.locale];
});

const watermarkProps = computed<WatermarkProps>(() => {
  return {
    content: themeStore.watermark.text,
    cross: true,
    fullscreen: true,
    fontSize: 16,
    lineHeight: 16,
    width: 384,
    height: 384,
    xOffset: 12,
    yOffset: 60,
    rotate: -15,
    zIndex: 9999
  };
});

// 页面加载时
onMounted(() => {
  // initMqtt();
});

// 页面销毁时，关闭监听
onUnmounted(() => {
  releaseMqtt();
});
</script>

<template>
  <NConfigProvider
    :theme="naiveDarkTheme"
    :theme-overrides="themeStore.naiveTheme"
    :locale="naiveLocale"
    :date-locale="naiveDateLocale"
    class="h-full"
  >
    <AppProvider>
      <!--add by fs 用于给fast-crud安装naive-ui，让fs-crud拥有message notification dialog的能力-->
      <FsUiContext>
        <RouterView class="bg-layout" />
      </FsUiContext>
      <NWatermark v-if="themeStore.watermark.visible" v-bind="watermarkProps" />
    </AppProvider>
  </NConfigProvider>
</template>

<style scoped></style>

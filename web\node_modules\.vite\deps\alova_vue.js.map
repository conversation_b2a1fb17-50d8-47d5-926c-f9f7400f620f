{"version": 3, "sources": ["../../.pnpm/alova@3.2.6/node_modules/alova/dist/stateshook/vue.esm.js"], "sourcesContent": ["/**\n  * @alova/client 2.0.0 (https://alova.js.org)\n  * Document https://alova.js.org\n  * Copyright 2024 Scott hu. All Rights Reserved\n  * Licensed under MIT (git://github.com/alovajs/alova/blob/main/LICENSE)\n*/\n\nimport { forEach, trueValue, setTimeoutFn } from '@alova/shared';\nimport { ref, getCurrentInstance, onUnmounted, watch, computed, onMounted } from 'vue';\n\n// the vue's predefined hooks\nvar vue = {\n    name: 'Vue',\n    create: data => ref(data),\n    dehydrate: state => state.value,\n    update: (newVal, state) => {\n        state.value = newVal;\n    },\n    effectRequest({ handler, removeStates, immediate, watchingStates }) {\n        // if call in component, remove current hook states when unmounting component\n        if (getCurrentInstance()) {\n            onUnmounted(removeStates);\n        }\n        immediate && handler();\n        forEach(watchingStates || [], (state, i) => {\n            watch(state, () => {\n                handler(i);\n            }, { deep: trueValue });\n        });\n    },\n    computed: getter => computed(getter),\n    watch: (states, callback) => {\n        watch(states, callback, {\n            deep: trueValue\n        });\n    },\n    onMounted: callback => {\n        if (getCurrentInstance()) {\n            onMounted(callback);\n        }\n        else {\n            setTimeoutFn(callback, 10);\n        }\n    },\n    onUnmounted: callback => {\n        getCurrentInstance() && onUnmounted(callback);\n    }\n};\n\nexport { vue as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAWA,IAAI,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ,UAAQ,IAAI,IAAI;AAAA,EACxB,WAAW,WAAS,MAAM;AAAA,EAC1B,QAAQ,CAAC,QAAQ,UAAU;AACvB,UAAM,QAAQ;AAAA,EAClB;AAAA,EACA,cAAc,EAAE,SAAS,cAAc,WAAW,eAAe,GAAG;AAEhE,QAAI,mBAAmB,GAAG;AACtB,kBAAY,YAAY;AAAA,IAC5B;AACA,iBAAa,QAAQ;AACrB,YAAQ,kBAAkB,CAAC,GAAG,CAAC,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM;AACf,gBAAQ,CAAC;AAAA,MACb,GAAG,EAAE,MAAM,UAAU,CAAC;AAAA,IAC1B,CAAC;AAAA,EACL;AAAA,EACA,UAAU,YAAU,SAAS,MAAM;AAAA,EACnC,OAAO,CAAC,QAAQ,aAAa;AACzB,UAAM,QAAQ,UAAU;AAAA,MACpB,MAAM;AAAA,IACV,CAAC;AAAA,EACL;AAAA,EACA,WAAW,cAAY;AACnB,QAAI,mBAAmB,GAAG;AACtB,gBAAU,QAAQ;AAAA,IACtB,OACK;AACD,mBAAa,UAAU,EAAE;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,aAAa,cAAY;AACrB,uBAAmB,KAAK,YAAY,QAAQ;AAAA,EAChD;AACJ;", "names": []}
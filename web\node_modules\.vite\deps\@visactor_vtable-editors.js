import "./chunk-ULBN3QDT.js";

// node_modules/.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/es/input-editor.js
var InputEditor = class {
  constructor(editorConfig) {
    this.editorType = "Input", this.editorConfig = editorConfig;
  }
  createElement() {
    var _a;
    const input = document.createElement("input");
    input.setAttribute("type", "text"), (null === (_a = this.editorConfig) || void 0 === _a ? void 0 : _a.readonly) && input.setAttribute("readonly", `${this.editorConfig.readonly}`), input.style.position = "absolute", input.style.padding = "4px", input.style.width = "100%", input.style.boxSizing = "border-box", input.style.backgroundColor = "#FFFFFF", this.element = input, this.container.appendChild(input), input.addEventListener("keydown", (e) => {
      "a" === e.key && (e.ctrlKey || e.metaKey) && e.stopPropagation();
    });
  }
  setValue(value) {
    this.element.value = void 0 !== value ? value : "";
  }
  getValue() {
    return this.element.value;
  }
  onStart({ value, referencePosition, container, endEdit }) {
    this.container = container, this.successCallback = endEdit, this.element || (this.createElement(), null != value && this.setValue(value), (null == referencePosition ? void 0 : referencePosition.rect) && this.adjustPosition(referencePosition.rect)), this.element.focus();
  }
  adjustPosition(rect) {
    this.element.style.top = rect.top + "px", this.element.style.left = rect.left + "px", this.element.style.width = rect.width + "px", this.element.style.height = rect.height + "px";
  }
  endEditing() {
  }
  onEnd() {
    var _a;
    (null === (_a = this.container) || void 0 === _a ? void 0 : _a.contains(this.element)) && this.container.removeChild(this.element), this.element = void 0;
  }
  isEditorElement(target) {
    return target === this.element;
  }
};

// node_modules/.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/es/date-input-editor.js
var DateInputEditor = class extends InputEditor {
  constructor(editorConfig) {
    super(editorConfig), this.editorType = "DateInput", this.editorConfig = editorConfig;
  }
  createElement() {
    const input = document.createElement("input");
    input.setAttribute("type", "date"), input.style.padding = "4px", input.style.width = "100%", input.style.boxSizing = "border-box", input.style.position = "absolute", input.style.backgroundColor = "#FFFFFF", this.element = input, this.container.appendChild(input), input.addEventListener("keydown", (e) => {
      "a" === e.key && (e.ctrlKey || e.metaKey) && e.stopPropagation();
    });
  }
};

// node_modules/.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/es/list-editor.js
var ListEditor = class {
  constructor(editorConfig) {
    this.editorType = "Input", this.editorConfig = editorConfig;
  }
  createElement(value) {
    const select = document.createElement("select");
    select.setAttribute("type", "text"), select.style.position = "absolute", select.style.padding = "4px", select.style.width = "100%", select.style.boxSizing = "border-box", select.style.backgroundColor = "#FFFFFF", this.element = select;
    const { values } = this.editorConfig;
    let opsStr = "";
    values.forEach((item) => {
      opsStr += item === value ? `<option value="${item}" selected>${item}</option>` : `<option value="${item}" >${item}</option>`;
    }), select.innerHTML = opsStr, this.container.appendChild(select);
  }
  _bindSelectChangeEvent() {
    this.element.addEventListener("change", () => {
    });
  }
  setValue(value) {
  }
  getValue() {
    return this.element.value;
  }
  onStart({ container, value, referencePosition, endEdit }) {
    this.container = container, this.successCallback = endEdit, this.createElement(value), null != value && this.setValue(value), (null == referencePosition ? void 0 : referencePosition.rect) && this.adjustPosition(referencePosition.rect), this.element.focus();
  }
  adjustPosition(rect) {
    this.element.style.top = rect.top + "px", this.element.style.left = rect.left + "px", this.element.style.width = rect.width + "px", this.element.style.height = rect.height + "px";
  }
  endEditing() {
  }
  onEnd() {
    this.container.removeChild(this.element);
  }
  isEditorElement(target) {
    return target === this.element;
  }
};

// node_modules/.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/es/textArea-editor.js
var TextAreaEditor = class {
  constructor(editorConfig) {
    this.editorType = "TextArea", this.editorConfig = editorConfig || {};
  }
  createElement() {
    var _a;
    const input = document.createElement("textArea");
    (null === (_a = this.editorConfig) || void 0 === _a ? void 0 : _a.readonly) && input.setAttribute("readonly", `${this.editorConfig.readonly}`), input.style.resize = "none", input.style.position = "absolute", input.style.padding = "4px", input.style.width = "100%", input.style.height = "100%", input.style.boxSizing = "border-box", input.style.backgroundColor = "#FFFFFF", this.element = input, this.container.appendChild(input), input.addEventListener("keydown", (e) => {
      const _isSelectAll = "a" === e.key && (e.ctrlKey || e.metaKey), _isTextAreaNewLine = "Enter" === e.key && e.shiftKey;
      (_isSelectAll || _isTextAreaNewLine) && e.stopPropagation();
    });
  }
  setValue(value) {
    this.element.value = void 0 !== value ? value : "";
  }
  getValue() {
    return this.element.value;
  }
  onStart({ value, referencePosition, container, endEdit }) {
    this.container = container, this.successCallback = endEdit, this.element || (this.createElement(), null != value && this.setValue(value), (null == referencePosition ? void 0 : referencePosition.rect) && this.adjustPosition(referencePosition.rect)), this.element.focus();
  }
  adjustPosition(rect) {
    this.element.style.top = rect.top + "px", this.element.style.left = rect.left + "px", this.element.style.width = rect.width + "px", this.element.style.height = rect.height + "px";
  }
  endEditing() {
  }
  onEnd() {
    this.container.removeChild(this.element), this.element = void 0;
  }
  isEditorElement(target) {
    return target === this.element;
  }
};

// node_modules/.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/es/types.js
var Placement;
!function(Placement2) {
  Placement2.top = "top", Placement2.bottom = "bottom", Placement2.left = "left", Placement2.right = "right";
}(Placement || (Placement = {}));
export {
  DateInputEditor,
  InputEditor,
  ListEditor,
  Placement,
  TextAreaEditor
};
//# sourceMappingURL=@visactor_vtable-editors.js.map

@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\git\tvs\tvs_site\web\node_modules\.pnpm\npm-check-updates@17.1.11\node_modules\npm-check-updates\build\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\npm-check-updates@17.1.11\node_modules\npm-check-updates\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\npm-check-updates@17.1.11\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\git\tvs\tvs_site\web\node_modules\.pnpm\npm-check-updates@17.1.11\node_modules\npm-check-updates\build\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\npm-check-updates@17.1.11\node_modules\npm-check-updates\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\npm-check-updates@17.1.11\node_modules;E:\git\tvs\tvs_site\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\npm-check-updates\build\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\npm-check-updates\build\cli.js" %*
)

/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    BlockEditor: typeof import('./../components/blockEditor/index.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    CustomIconSelect: typeof import('./../components/custom/custom-icon-select.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    DashboardClock: typeof import('./../components/dashboardClock/index.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FreeChart: typeof import('./../components/freeChart/index.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    GithubLink: typeof import('./../components/custom/github-link.vue')['default']
    IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    IconCarbonPlay: typeof import('~icons/carbon/play')['default']
    IconCarbonStop: typeof import('~icons/carbon/stop')['default']
    'IconCharm:download': typeof import('~icons/charm/download')['default']
    'IconEmojione:artistPalette': typeof import('~icons/emojione/artist-palette')['default']
    'IconF7:circleFill': typeof import('~icons/f7/circle-fill')['default']
    'IconF7:flagCircleFill': typeof import('~icons/f7/flag-circle-fill')['default']
    'IconFad:save': typeof import('~icons/fad/save')['default']
    'IconFad:saveas': typeof import('~icons/fad/saveas')['default']
    'IconFe:question': typeof import('~icons/fe/question')['default']
    'IconFileIcons:microsoftExcel': typeof import('~icons/file-icons/microsoft-excel')['default']
    'IconGg:ratio': typeof import('~icons/gg/ratio')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    'IconIc:roundPlus': typeof import('~icons/ic/round-plus')['default']
    'IconIconParkOutline:equalRatio': typeof import('~icons/icon-park-outline/equal-ratio')['default']
    IconIcRoundDelete: typeof import('~icons/ic/round-delete')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconIcRoundRefresh: typeof import('~icons/ic/round-refresh')['default']
    IconIcRoundRemove: typeof import('~icons/ic/round-remove')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    IconLocalActivity: typeof import('~icons/local/activity')['default']
    IconLocalCast: typeof import('~icons/local/cast')['default']
    IconLocalIcRoundPlus: typeof import('~icons/local/ic-round-plus')['default']
    IconLocalIcRoundRefresh: typeof import('~icons/local/ic-round-refresh')['default']
    IconLocalIcRoundSearch: typeof import('~icons/local/ic-round-search')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    IconLocalRadixIcons_magicWand: typeof import('~icons/local/radix-icons_magic-wand')['default']
    'IconMaterialSymbols:check': typeof import('~icons/material-symbols/check')['default']
    'IconMaterialSymbols:deleteOutline': typeof import('~icons/material-symbols/delete-outline')['default']
    'IconMaterialSymbols:fullscreen': typeof import('~icons/material-symbols/fullscreen')['default']
    'IconMaterialSymbolsLight:rotate90DegreesCcwOutlineRounded': typeof import('~icons/material-symbols-light/rotate90-degrees-ccw-outline-rounded')['default']
    'IconMdi:printer': typeof import('~icons/mdi/printer')['default']
    'IconMdi:renameBoxOutline': typeof import('~icons/mdi/rename-box-outline')['default']
    IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin')['default']
    IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc')['default']
    IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return')['default']
    IconMdiRefresh: typeof import('~icons/mdi/refresh')['default']
    'IconMingcute:zoomInLine': typeof import('~icons/mingcute/zoom-in-line')['default']
    'IconMingcute:zoomOutLine': typeof import('~icons/mingcute/zoom-out-line')['default']
    'IconPrime:apple': typeof import('~icons/prime/apple')['default']
    IconUilSearch: typeof import('~icons/uil/search')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCard: typeof import('naive-ui')['NCard']
    NCascader: typeof import('naive-ui')['NCascader']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCheckboxGroup: typeof import('naive-ui')['NCheckboxGroup']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NFlex: typeof import('naive-ui')['NFlex']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputGroupLabel: typeof import('naive-ui')['NInputGroupLabel']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NPagination: typeof import('naive-ui')['NPagination']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSplit: typeof import('naive-ui')['NSplit']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NTimeline: typeof import('naive-ui')['NTimeline']
    NTimelineItem: typeof import('naive-ui')['NTimelineItem']
    NTimePicker: typeof import('naive-ui')['NTimePicker']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NWatermark: typeof import('naive-ui')['NWatermark']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    PulseChart: typeof import('./../components/pulseChart/index.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SevenSegClock: typeof import('./../components/sevenSegClock/index.vue')['default']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
    WaveChart: typeof import('./../components/waveChart/index.vue')['default']
    WebSiteLink: typeof import('./../components/custom/web-site-link.vue')['default']
  }
}

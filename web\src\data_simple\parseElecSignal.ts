export interface ChartData {
  x: number[];
  y: { name: string; value: number[] }[];
}

export function parseElecSignal(data: any[][], fields: string[]): ChartData {
  const header = data[0];
  const rows = data.slice(1);

  const timeIndex = header.indexOf("Tstamp");
  const fieldIndices = fields.map(f => header.indexOf(f));

  const x: number[] = [];
  const ySeries: { name: string; value: number[] }[] = fields.map(name => ({ name, value: [] }));

  for (const row of rows) {
    const t = row[timeIndex];
    if (typeof t === 'number') x.push(t);
    fieldIndices.forEach((i, idx) => {
      ySeries[idx].value.push(row[i]);
    });
  }

  return { x, y: ySeries };
}

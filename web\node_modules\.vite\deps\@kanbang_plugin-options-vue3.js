import {
  require_vue
} from "./chunk-XGUISF7U.js";
import "./chunk-TID6LRNE.js";
import {
  __commonJS
} from "./chunk-ULBN3QDT.js";

// node_modules/.pnpm/@kanbang+plugin-options-vue3@1.10.5/node_modules/@kanbang/plugin-options-vue3/dist/index.js
var require_dist = __commonJS({
  "node_modules/.pnpm/@kanbang+plugin-options-vue3@1.10.5/node_modules/@kanbang/plugin-options-vue3/dist/index.js"(exports, module) {
    (function webpackUniversalModuleDefinition(root, factory) {
      if (typeof exports === "object" && typeof module === "object")
        module.exports = factory(require_vue());
      else if (typeof define === "function" && define.amd)
        define("BaklavaJSOptionsVue", ["vue"], factory);
      else if (typeof exports === "object")
        exports["BaklavaJSOptionsVue"] = factory(require_vue());
      else
        root["BaklavaJSOptionsVue"] = factory(root["Vue"]);
    })(typeof self !== "undefined" ? self : exports, function(__WEBPACK_EXTERNAL_MODULE__0__) {
      return (
        /******/
        function(modules) {
          var installedModules = {};
          function __webpack_require__(moduleId) {
            if (installedModules[moduleId]) {
              return installedModules[moduleId].exports;
            }
            var module2 = installedModules[moduleId] = {
              /******/
              i: moduleId,
              /******/
              l: false,
              /******/
              exports: {}
              /******/
            };
            modules[moduleId].call(module2.exports, module2, module2.exports, __webpack_require__);
            module2.l = true;
            return module2.exports;
          }
          __webpack_require__.m = modules;
          __webpack_require__.c = installedModules;
          __webpack_require__.d = function(exports2, name, getter) {
            if (!__webpack_require__.o(exports2, name)) {
              Object.defineProperty(exports2, name, { enumerable: true, get: getter });
            }
          };
          __webpack_require__.r = function(exports2) {
            if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
              Object.defineProperty(exports2, Symbol.toStringTag, { value: "Module" });
            }
            Object.defineProperty(exports2, "__esModule", { value: true });
          };
          __webpack_require__.t = function(value, mode) {
            if (mode & 1) value = __webpack_require__(value);
            if (mode & 8) return value;
            if (mode & 4 && typeof value === "object" && value && value.__esModule) return value;
            var ns = /* @__PURE__ */ Object.create(null);
            __webpack_require__.r(ns);
            Object.defineProperty(ns, "default", { enumerable: true, value });
            if (mode & 2 && typeof value != "string") for (var key in value) __webpack_require__.d(ns, key, (function(key2) {
              return value[key2];
            }).bind(null, key));
            return ns;
          };
          __webpack_require__.n = function(module2) {
            var getter = module2 && module2.__esModule ? (
              /******/
              function getDefault() {
                return module2["default"];
              }
            ) : (
              /******/
              function getModuleExports() {
                return module2;
              }
            );
            __webpack_require__.d(getter, "a", getter);
            return getter;
          };
          __webpack_require__.o = function(object, property) {
            return Object.prototype.hasOwnProperty.call(object, property);
          };
          __webpack_require__.p = "";
          return __webpack_require__(__webpack_require__.s = 59);
        }([
          /* 0 */
          /***/
          function(module2, exports2) {
            module2.exports = __WEBPACK_EXTERNAL_MODULE__0__;
          },
          /* 1 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "d", function() {
              return __extends;
            });
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return __assign;
            });
            __webpack_require__.d(__webpack_exports__, "c", function() {
              return __decorate;
            });
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return __awaiter;
            });
            __webpack_require__.d(__webpack_exports__, "e", function() {
              return __generator;
            });
            __webpack_require__.d(__webpack_exports__, "f", function() {
              return __read;
            });
            var extendStatics = function(d, b) {
              extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
                d2.__proto__ = b2;
              } || function(d2, b2) {
                for (var p in b2) if (b2.hasOwnProperty(p)) d2[p] = b2[p];
              };
              return extendStatics(d, b);
            };
            function __extends(d, b) {
              extendStatics(d, b);
              function __() {
                this.constructor = d;
              }
              d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
            }
            var __assign = function() {
              __assign = Object.assign || function __assign2(t) {
                for (var s, i = 1, n = arguments.length; i < n; i++) {
                  s = arguments[i];
                  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
                }
                return t;
              };
              return __assign.apply(this, arguments);
            };
            function __rest(s, e) {
              var t = {};
              for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
                t[p] = s[p];
              if (s != null && typeof Object.getOwnPropertySymbols === "function")
                for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
                  if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                    t[p[i]] = s[p[i]];
                }
              return t;
            }
            function __decorate(decorators, target, key, desc) {
              var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
              if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
              else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
              return c > 3 && r && Object.defineProperty(target, key, r), r;
            }
            function __param(paramIndex, decorator) {
              return function(target, key) {
                decorator(target, key, paramIndex);
              };
            }
            function __metadata(metadataKey, metadataValue) {
              if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(metadataKey, metadataValue);
            }
            function __awaiter(thisArg, _arguments, P, generator) {
              function adopt(value) {
                return value instanceof P ? value : new P(function(resolve) {
                  resolve(value);
                });
              }
              return new (P || (P = Promise))(function(resolve, reject) {
                function fulfilled(value) {
                  try {
                    step(generator.next(value));
                  } catch (e) {
                    reject(e);
                  }
                }
                function rejected(value) {
                  try {
                    step(generator["throw"](value));
                  } catch (e) {
                    reject(e);
                  }
                }
                function step(result) {
                  result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
                }
                step((generator = generator.apply(thisArg, _arguments || [])).next());
              });
            }
            function __generator(thisArg, body) {
              var _ = { label: 0, sent: function() {
                if (t[0] & 1) throw t[1];
                return t[1];
              }, trys: [], ops: [] }, f, y, t, g;
              return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
                return this;
              }), g;
              function verb(n) {
                return function(v) {
                  return step([n, v]);
                };
              }
              function step(op) {
                if (f) throw new TypeError("Generator is already executing.");
                while (_) try {
                  if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
                  if (y = 0, t) op = [op[0] & 2, t.value];
                  switch (op[0]) {
                    case 0:
                    case 1:
                      t = op;
                      break;
                    case 4:
                      _.label++;
                      return { value: op[1], done: false };
                    case 5:
                      _.label++;
                      y = op[1];
                      op = [0];
                      continue;
                    case 7:
                      op = _.ops.pop();
                      _.trys.pop();
                      continue;
                    default:
                      if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                      }
                      if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                      }
                      if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                      }
                      if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                      }
                      if (t[2]) _.ops.pop();
                      _.trys.pop();
                      continue;
                  }
                  op = body.call(thisArg, _);
                } catch (e) {
                  op = [6, e];
                  y = 0;
                } finally {
                  f = t = 0;
                }
                if (op[0] & 5) throw op[1];
                return { value: op[0] ? op[1] : void 0, done: true };
              }
            }
            function __createBinding(o, m, k, k2) {
              if (k2 === void 0) k2 = k;
              o[k2] = m[k];
            }
            function __exportStar(m, exports2) {
              for (var p in m) if (p !== "default" && !exports2.hasOwnProperty(p)) exports2[p] = m[p];
            }
            function __values(o) {
              var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
              if (m) return m.call(o);
              if (o && typeof o.length === "number") return {
                next: function() {
                  if (o && i >= o.length) o = void 0;
                  return { value: o && o[i++], done: !o };
                }
              };
              throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
            }
            function __read(o, n) {
              var m = typeof Symbol === "function" && o[Symbol.iterator];
              if (!m) return o;
              var i = m.call(o), r, ar = [], e;
              try {
                while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
              } catch (error) {
                e = { error };
              } finally {
                try {
                  if (r && !r.done && (m = i["return"])) m.call(i);
                } finally {
                  if (e) throw e.error;
                }
              }
              return ar;
            }
            function __spread() {
              for (var ar = [], i = 0; i < arguments.length; i++)
                ar = ar.concat(__read(arguments[i]));
              return ar;
            }
            function __spreadArrays() {
              for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
              for (var r = Array(s), k = 0, i = 0; i < il; i++)
                for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
                  r[k] = a[j];
              return r;
            }
            ;
            function __await(v) {
              return this instanceof __await ? (this.v = v, this) : new __await(v);
            }
            function __asyncGenerator(thisArg, _arguments, generator) {
              if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
              var g = generator.apply(thisArg, _arguments || []), i, q = [];
              return i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
                return this;
              }, i;
              function verb(n) {
                if (g[n]) i[n] = function(v) {
                  return new Promise(function(a, b) {
                    q.push([n, v, a, b]) > 1 || resume(n, v);
                  });
                };
              }
              function resume(n, v) {
                try {
                  step(g[n](v));
                } catch (e) {
                  settle(q[0][3], e);
                }
              }
              function step(r) {
                r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
              }
              function fulfill(value) {
                resume("next", value);
              }
              function reject(value) {
                resume("throw", value);
              }
              function settle(f, v) {
                if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
              }
            }
            function __asyncDelegator(o) {
              var i, p;
              return i = {}, verb("next"), verb("throw", function(e) {
                throw e;
              }), verb("return"), i[Symbol.iterator] = function() {
                return this;
              }, i;
              function verb(n, f) {
                i[n] = o[n] ? function(v) {
                  return (p = !p) ? { value: __await(o[n](v)), done: n === "return" } : f ? f(v) : v;
                } : f;
              }
            }
            function __asyncValues(o) {
              if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
              var m = o[Symbol.asyncIterator], i;
              return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
                return this;
              }, i);
              function verb(n) {
                i[n] = o[n] && function(v) {
                  return new Promise(function(resolve, reject) {
                    v = o[n](v), settle(resolve, reject, v.done, v.value);
                  });
                };
              }
              function settle(resolve, reject, d, v) {
                Promise.resolve(v).then(function(v2) {
                  resolve({ value: v2, done: d });
                }, reject);
              }
            }
            function __makeTemplateObject(cooked, raw) {
              if (Object.defineProperty) {
                Object.defineProperty(cooked, "raw", { value: raw });
              } else {
                cooked.raw = raw;
              }
              return cooked;
            }
            ;
            function __importStar(mod) {
              if (mod && mod.__esModule) return mod;
              var result = {};
              if (mod != null) {
                for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
              }
              result.default = mod;
              return result;
            }
            function __importDefault(mod) {
              return mod && mod.__esModule ? mod : { default: mod };
            }
            function __classPrivateFieldGet(receiver, privateMap) {
              if (!privateMap.has(receiver)) {
                throw new TypeError("attempted to get private field on non-instance");
              }
              return privateMap.get(receiver);
            }
            function __classPrivateFieldSet(receiver, privateMap, value) {
              if (!privateMap.has(receiver)) {
                throw new TypeError("attempted to set private field on non-instance");
              }
              privateMap.set(receiver, value);
              return value;
            }
          },
          /* 2 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return vue_class_component__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
            __webpack_require__.d(__webpack_exports__, "c", function() {
              return vue_class_component__WEBPACK_IMPORTED_MODULE_0__["b"];
            });
            var _decorators_Emit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(27);
            var _decorators_Inject__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(28);
            var _decorators_Model__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(29);
            var _decorators_Prop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(30);
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return _decorators_Prop__WEBPACK_IMPORTED_MODULE_4__["a"];
            });
            var _decorators_Provide__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(31);
            var _decorators_Ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(32);
            var _decorators_Watch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(33);
            __webpack_require__.d(__webpack_exports__, "d", function() {
              return _decorators_Watch__WEBPACK_IMPORTED_MODULE_7__["a"];
            });
          },
          /* 3 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return Options;
            });
            __webpack_require__.d(__webpack_exports__, "b", function() {
              return Vue;
            });
            __webpack_require__.d(__webpack_exports__, "c", function() {
              return createDecorator;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function _classCallCheck(instance, Constructor) {
              if (!(instance instanceof Constructor)) {
                throw new TypeError("Cannot call a class as a function");
              }
            }
            function _defineProperties(target, props) {
              for (var i = 0; i < props.length; i++) {
                var descriptor = props[i];
                descriptor.enumerable = descriptor.enumerable || false;
                descriptor.configurable = true;
                if ("value" in descriptor) descriptor.writable = true;
                Object.defineProperty(target, descriptor.key, descriptor);
              }
            }
            function _createClass(Constructor, protoProps, staticProps) {
              if (protoProps) _defineProperties(Constructor.prototype, protoProps);
              if (staticProps) _defineProperties(Constructor, staticProps);
              return Constructor;
            }
            function _defineProperty(obj, key, value) {
              if (key in obj) {
                Object.defineProperty(obj, key, {
                  value,
                  enumerable: true,
                  configurable: true,
                  writable: true
                });
              } else {
                obj[key] = value;
              }
              return obj;
            }
            function ownKeys(object, enumerableOnly) {
              var keys = Object.keys(object);
              if (Object.getOwnPropertySymbols) {
                var symbols = Object.getOwnPropertySymbols(object);
                if (enumerableOnly) symbols = symbols.filter(function(sym) {
                  return Object.getOwnPropertyDescriptor(object, sym).enumerable;
                });
                keys.push.apply(keys, symbols);
              }
              return keys;
            }
            function _objectSpread2(target) {
              for (var i = 1; i < arguments.length; i++) {
                var source = arguments[i] != null ? arguments[i] : {};
                if (i % 2) {
                  ownKeys(Object(source), true).forEach(function(key) {
                    _defineProperty(target, key, source[key]);
                  });
                } else if (Object.getOwnPropertyDescriptors) {
                  Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
                } else {
                  ownKeys(Object(source)).forEach(function(key) {
                    Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
                  });
                }
              }
              return target;
            }
            function _inherits(subClass, superClass) {
              if (typeof superClass !== "function" && superClass !== null) {
                throw new TypeError("Super expression must either be null or a function");
              }
              subClass.prototype = Object.create(superClass && superClass.prototype, {
                constructor: {
                  value: subClass,
                  writable: true,
                  configurable: true
                }
              });
              if (superClass) _setPrototypeOf(subClass, superClass);
            }
            function _getPrototypeOf(o) {
              _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf2(o2) {
                return o2.__proto__ || Object.getPrototypeOf(o2);
              };
              return _getPrototypeOf(o);
            }
            function _setPrototypeOf(o, p) {
              _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf2(o2, p2) {
                o2.__proto__ = p2;
                return o2;
              };
              return _setPrototypeOf(o, p);
            }
            function _isNativeReflectConstruct() {
              if (typeof Reflect === "undefined" || !Reflect.construct) return false;
              if (Reflect.construct.sham) return false;
              if (typeof Proxy === "function") return true;
              try {
                Date.prototype.toString.call(Reflect.construct(Date, [], function() {
                }));
                return true;
              } catch (e) {
                return false;
              }
            }
            function _construct(Parent, args, Class) {
              if (_isNativeReflectConstruct()) {
                _construct = Reflect.construct;
              } else {
                _construct = function _construct2(Parent2, args2, Class2) {
                  var a = [null];
                  a.push.apply(a, args2);
                  var Constructor = Function.bind.apply(Parent2, a);
                  var instance = new Constructor();
                  if (Class2) _setPrototypeOf(instance, Class2.prototype);
                  return instance;
                };
              }
              return _construct.apply(null, arguments);
            }
            function _assertThisInitialized(self2) {
              if (self2 === void 0) {
                throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
              }
              return self2;
            }
            function _possibleConstructorReturn(self2, call) {
              if (call && (typeof call === "object" || typeof call === "function")) {
                return call;
              }
              return _assertThisInitialized(self2);
            }
            function _createSuper(Derived) {
              var hasNativeReflectConstruct = _isNativeReflectConstruct();
              return function _createSuperInternal() {
                var Super = _getPrototypeOf(Derived), result;
                if (hasNativeReflectConstruct) {
                  var NewTarget = _getPrototypeOf(this).constructor;
                  result = Reflect.construct(Super, arguments, NewTarget);
                } else {
                  result = Super.apply(this, arguments);
                }
                return _possibleConstructorReturn(this, result);
              };
            }
            function _toConsumableArray(arr) {
              return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();
            }
            function _arrayWithoutHoles(arr) {
              if (Array.isArray(arr)) return _arrayLikeToArray(arr);
            }
            function _iterableToArray(iter) {
              if (typeof Symbol !== "undefined" && Symbol.iterator in Object(iter)) return Array.from(iter);
            }
            function _unsupportedIterableToArray(o, minLen) {
              if (!o) return;
              if (typeof o === "string") return _arrayLikeToArray(o, minLen);
              var n = Object.prototype.toString.call(o).slice(8, -1);
              if (n === "Object" && o.constructor) n = o.constructor.name;
              if (n === "Map" || n === "Set") return Array.from(o);
              if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
            }
            function _arrayLikeToArray(arr, len) {
              if (len == null || len > arr.length) len = arr.length;
              for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];
              return arr2;
            }
            function _nonIterableSpread() {
              throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
            }
            function defineGetter(obj, key, getter) {
              Object.defineProperty(obj, key, {
                get: getter,
                enumerable: false,
                configurable: true
              });
            }
            function defineProxy(proxy, key, target) {
              Object.defineProperty(proxy, key, {
                get: function get() {
                  return target[key].value;
                },
                set: function set(value) {
                  target[key].value = value;
                },
                enumerable: true,
                configurable: true
              });
            }
            function getSuper(Ctor) {
              var superProto = Object.getPrototypeOf(Ctor.prototype);
              if (!superProto) {
                return void 0;
              }
              return superProto.constructor;
            }
            function getOwn(value, key) {
              return value.hasOwnProperty(key) ? value[key] : void 0;
            }
            var VueImpl = function() {
              function VueImpl2(props, ctx) {
                var _this = this;
                _classCallCheck(this, VueImpl2);
                defineGetter(this, "$props", function() {
                  return props;
                });
                defineGetter(this, "$attrs", function() {
                  return ctx.attrs;
                });
                defineGetter(this, "$slots", function() {
                  return ctx.slots;
                });
                defineGetter(this, "$emit", function() {
                  return ctx.emit;
                });
                Object.keys(props).forEach(function(key) {
                  Object.defineProperty(_this, key, {
                    enumerable: false,
                    configurable: true,
                    writable: true,
                    value: props[key]
                  });
                });
              }
              _createClass(VueImpl2, null, [{
                key: "registerHooks",
                value: function registerHooks(keys) {
                  var _this$__h;
                  (_this$__h = this.__h).push.apply(_this$__h, _toConsumableArray(keys));
                }
              }, {
                key: "with",
                value: function _with(Props) {
                  var propsMeta = new Props();
                  var props = {};
                  Object.keys(propsMeta).forEach(function(key) {
                    var meta = propsMeta[key];
                    props[key] = meta !== null && meta !== void 0 ? meta : null;
                  });
                  var PropsMixin = function(_this2) {
                    _inherits(PropsMixin2, _this2);
                    var _super = _createSuper(PropsMixin2);
                    function PropsMixin2() {
                      _classCallCheck(this, PropsMixin2);
                      return _super.apply(this, arguments);
                    }
                    return PropsMixin2;
                  }(this);
                  PropsMixin.__b = {
                    props
                  };
                  return PropsMixin;
                }
              }, {
                key: "__vccOpts",
                get: function get() {
                  if (this === Vue) {
                    return {};
                  }
                  var Ctor = this;
                  var cache = getOwn(Ctor, "__c");
                  if (cache) {
                    return cache;
                  }
                  var options = _objectSpread2({}, getOwn(Ctor, "__o"));
                  Ctor.__c = options;
                  var Super = getSuper(Ctor);
                  if (Super) {
                    options["extends"] = Super.__vccOpts;
                  }
                  var base = getOwn(Ctor, "__b");
                  if (base) {
                    options.mixins = options.mixins || [];
                    options.mixins.unshift(base);
                  }
                  options.methods = _objectSpread2({}, options.methods);
                  options.computed = _objectSpread2({}, options.computed);
                  var proto = Ctor.prototype;
                  Object.getOwnPropertyNames(proto).forEach(function(key) {
                    if (key === "constructor") {
                      return;
                    }
                    if (Ctor.__h.indexOf(key) > -1) {
                      options[key] = proto[key];
                      return;
                    }
                    var descriptor = Object.getOwnPropertyDescriptor(proto, key);
                    if (typeof descriptor.value === "function") {
                      options.methods[key] = descriptor.value;
                      return;
                    }
                    if (descriptor.get || descriptor.set) {
                      options.computed[key] = {
                        get: descriptor.get,
                        set: descriptor.set
                      };
                      return;
                    }
                  });
                  options.setup = function(props, ctx) {
                    var _promise;
                    var data = new Ctor(props, ctx);
                    var dataKeys = Object.keys(data);
                    var plainData = {};
                    var promise = null;
                    dataKeys.forEach(function(key) {
                      if (data[key] === void 0 || data[key] && data[key].__s) {
                        return;
                      }
                      plainData[key] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["ref"])(data[key]);
                      defineProxy(data, key, plainData);
                    });
                    dataKeys.forEach(function(key) {
                      if (data[key] && data[key].__s) {
                        var setupState = data[key].__s();
                        if (setupState instanceof Promise) {
                          if (!promise) {
                            promise = Promise.resolve(plainData);
                          }
                          promise = promise.then(function() {
                            return setupState.then(function(value) {
                              plainData[key] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["proxyRefs"])(value);
                              return plainData;
                            });
                          });
                        } else {
                          plainData[key] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["proxyRefs"])(setupState);
                        }
                      }
                    });
                    return (_promise = promise) !== null && _promise !== void 0 ? _promise : plainData;
                  };
                  var decorators = getOwn(Ctor, "__d");
                  if (decorators) {
                    decorators.forEach(function(fn) {
                      return fn(options);
                    });
                  }
                  var injections = ["render", "ssrRender", "__file", "__cssModules", "__scopeId", "__hmrId"];
                  injections.forEach(function(key) {
                    if (Ctor[key]) {
                      options[key] = Ctor[key];
                    }
                  });
                  return options;
                }
              }]);
              return VueImpl2;
            }();
            VueImpl.__h = ["data", "beforeCreate", "created", "beforeMount", "mounted", "beforeUnmount", "unmounted", "beforeUpdate", "updated", "activated", "deactivated", "render", "errorCaptured", "serverPrefetch"];
            var Vue = VueImpl;
            function Options(options) {
              return function(Component) {
                Component.__o = options;
                return Component;
              };
            }
            function createDecorator(factory) {
              return function(target, key, index) {
                var Ctor = typeof target === "function" ? target : target.constructor;
                if (!Ctor.__d) {
                  Ctor.__d = [];
                }
                if (typeof index !== "number") {
                  index = void 0;
                }
                Ctor.__d.push(function(options) {
                  return factory(options, key, index);
                });
              };
            }
            function mixins() {
              for (var _len = arguments.length, Ctors = new Array(_len), _key = 0; _key < _len; _key++) {
                Ctors[_key] = arguments[_key];
              }
              var _a;
              return _a = function(_Vue) {
                _inherits(MixedVue, _Vue);
                var _super = _createSuper(MixedVue);
                function MixedVue() {
                  var _this;
                  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
                    args[_key2] = arguments[_key2];
                  }
                  _classCallCheck(this, MixedVue);
                  _this = _super.call.apply(_super, [this].concat(args));
                  Ctors.forEach(function(Ctor) {
                    var data = _construct(Ctor, args);
                    Object.keys(data).forEach(function(key) {
                      _this[key] = data[key];
                    });
                  });
                  return _this;
                }
                return MixedVue;
              }(Vue), _a.__b = {
                mixins: Ctors.map(function(Ctor) {
                  return Ctor.__vccOpts;
                })
              }, _a;
            }
            function setup(setupFn) {
              return {
                __s: setupFn
              };
            }
            function prop(options) {
              return options;
            }
          },
          /* 4 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _Arrow_vue_vue_type_template_id_1066f486__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(54);
            var _Arrow_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10);
            _Arrow_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _Arrow_vue_vue_type_template_id_1066f486__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _Arrow_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 5 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return BaseNumericOption;
            });
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var BaseNumericOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(BaseNumericOption2, _super);
                function BaseNumericOption2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.MAX_STRING_LENGTH = 9;
                  _this.editMode = false;
                  _this.invalid = false;
                  _this.tempValue = "0";
                  return _this;
                }
                Object.defineProperty(BaseNumericOption2.prototype, "v", {
                  get: function() {
                    if (typeof this.value === "string") {
                      return parseFloat(this.value);
                    } else if (typeof this.value === "number") {
                      return this.value;
                    } else {
                      return 0;
                    }
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(BaseNumericOption2.prototype, "stringRepresentation", {
                  get: function() {
                    var s = this.v.toFixed(3);
                    return s.length > this.MAX_STRING_LENGTH ? this.v.toExponential(this.MAX_STRING_LENGTH - 5) : s;
                  },
                  enumerable: false,
                  configurable: true
                });
                BaseNumericOption2.prototype.setValue = function(newValue) {
                  if (this.validate(newValue)) {
                    this.$emit("input", newValue);
                  }
                };
                BaseNumericOption2.prototype.resetInvalid = function() {
                  this.invalid = false;
                };
                BaseNumericOption2.prototype.enterEditMode = function() {
                  return Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                    /* __awaiter */
                    "b"
                  ])(this, void 0, void 0, function() {
                    return Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                      /* __generator */
                      "e"
                    ])(this, function(_a) {
                      switch (_a.label) {
                        case 0:
                          this.tempValue = this.v.toFixed(3);
                          this.editMode = true;
                          return [4, this.$nextTick()];
                        case 1:
                          _a.sent();
                          this.$refs.input.focus();
                          return [
                            2
                            /*return*/
                          ];
                      }
                    });
                  });
                };
                BaseNumericOption2.prototype.leaveEditMode = function() {
                  var v = parseFloat(this.tempValue);
                  if (!this.validate(v)) {
                    this.invalid = true;
                  } else {
                    this.$emit("input", v);
                    this.editMode = false;
                  }
                };
                BaseNumericOption2.prototype.validate = function(v) {
                  if (Number.isNaN(v)) {
                    return false;
                  } else if (typeof this.option.min === "number" && v < this.option.min) {
                    return false;
                  } else if (typeof this.option.max === "number" && v > this.option.max) {
                    return false;
                  } else {
                    return true;
                  }
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])()
                ], BaseNumericOption2.prototype, "value", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: String })
                ], BaseNumericOption2.prototype, "name", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: Object })
                ], BaseNumericOption2.prototype, "option", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Watch */
                    "d"
                  ])("tempValue")
                ], BaseNumericOption2.prototype, "resetInvalid", null);
                return BaseNumericOption2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "c"
              ])
            );
          },
          /* 6 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_ButtonOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_ButtonOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 7 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_CheckboxOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(16);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_CheckboxOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 8 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_InputOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(17);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_InputOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 9 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_IntegerOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(18);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_IntegerOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 10 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Arrow_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(19);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_Arrow_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 11 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_NumberOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_NumberOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 12 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_SelectOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(21);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_SelectOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 13 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_SliderOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(22);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_SliderOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 14 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_TextOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(23);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_ts_loader_index_js_ref_0_node_modules_vue_loader_dist_index_js_ref_9_0_TextOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 15 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var ButtonOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(ButtonOption2, _super);
                function ButtonOption2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: String })
                ], ButtonOption2.prototype, "name", void 0);
                ButtonOption2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "a"
                  ])({
                    emits: ["openSidebar"]
                  })
                ], ButtonOption2);
                return ButtonOption2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "c"
              ])
            );
            __webpack_exports__["a"] = ButtonOption;
          },
          /* 16 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var InputOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(InputOption2, _super);
                function InputOption2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: Boolean })
                ], InputOption2.prototype, "value", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: String })
                ], InputOption2.prototype, "name", void 0);
                InputOption2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "a"
                  ])({
                    emits: ["input"]
                  })
                ], InputOption2);
                return InputOption2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "c"
              ])
            );
            __webpack_exports__["a"] = InputOption;
          },
          /* 17 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var InputOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(InputOption2, _super);
                function InputOption2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.editMode = false;
                  _this.tempValue = "";
                  return _this;
                }
                Object.defineProperty(InputOption2.prototype, "listeners", {
                  get: function() {
                    var _this = this;
                    return Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                      /* __assign */
                      "a"
                    ])(Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                      /* __assign */
                      "a"
                    ])({}, this.listeners), { input: function(ev) {
                      return _this.$emit("input", ev.target.value);
                    } });
                  },
                  enumerable: false,
                  configurable: true
                });
                InputOption2.prototype.setValue = function(newValue) {
                  this.$emit("input", newValue);
                };
                InputOption2.prototype.enterEditMode = function() {
                  return Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                    /* __awaiter */
                    "b"
                  ])(this, void 0, void 0, function() {
                    return Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                      /* __generator */
                      "e"
                    ])(this, function(_a) {
                      switch (_a.label) {
                        case 0:
                          this.editMode = true;
                          this.tempValue = this.value;
                          return [4, this.$nextTick()];
                        case 1:
                          _a.sent();
                          this.$refs.input.focus();
                          return [
                            2
                            /*return*/
                          ];
                      }
                    });
                  });
                };
                InputOption2.prototype.leaveEditMode = function() {
                  this.$emit("input", this.tempValue);
                  this.editMode = false;
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: String, default: "" })
                ], InputOption2.prototype, "value", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: String })
                ], InputOption2.prototype, "name", void 0);
                InputOption2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "a"
                  ])({
                    emits: ["input"]
                  })
                ], InputOption2);
                return InputOption2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "c"
              ])
            );
            __webpack_exports__["a"] = InputOption;
          },
          /* 18 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _Arrow_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4);
            var _BaseNumericOption__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5);
            var IntegerOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(IntegerOption2, _super);
                function IntegerOption2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                Object.defineProperty(IntegerOption2.prototype, "v", {
                  get: function() {
                    if (typeof this.value === "string") {
                      return parseInt(this.value, 10);
                    } else if (typeof this.value === "number") {
                      return Math.floor(this.value);
                    } else {
                      return 0;
                    }
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(IntegerOption2.prototype, "stringRepresentation", {
                  get: function() {
                    var s = this.v.toString();
                    return s.length > this.MAX_STRING_LENGTH ? this.v.toExponential(this.MAX_STRING_LENGTH - 5) : s;
                  },
                  enumerable: false,
                  configurable: true
                });
                IntegerOption2.prototype.increment = function() {
                  this.setValue(this.v + 1);
                };
                IntegerOption2.prototype.decrement = function() {
                  this.setValue(this.v - 1);
                };
                IntegerOption2.prototype.leaveEditMode = function() {
                  var v = parseInt(this.tempValue, 10);
                  if (!this.validate(v)) {
                    this.invalid = true;
                  } else {
                    this.$emit("input", v);
                    this.editMode = false;
                  }
                };
                IntegerOption2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "a"
                  ])({
                    components: {
                      "i-arrow": _Arrow_vue__WEBPACK_IMPORTED_MODULE_2__[
                        /* default */
                        "a"
                      ]
                    },
                    emits: ["input"]
                  })
                ], IntegerOption2);
                return IntegerOption2;
              }(_BaseNumericOption__WEBPACK_IMPORTED_MODULE_3__[
                /* BaseNumericOption */
                "a"
              ])
            );
            __webpack_exports__["a"] = IntegerOption;
          },
          /* 19 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var Arrow = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(Arrow2, _super);
                function Arrow2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                return Arrow2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "c"
              ])
            );
            __webpack_exports__["a"] = Arrow;
          },
          /* 20 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _Arrow_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4);
            var _BaseNumericOption__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5);
            var NumberOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(NumberOption2, _super);
                function NumberOption2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                NumberOption2.prototype.increment = function() {
                  this.setValue(this.v + 0.1);
                };
                NumberOption2.prototype.decrement = function() {
                  this.setValue(this.v - 0.1);
                };
                NumberOption2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "a"
                  ])({
                    components: {
                      "i-arrow": _Arrow_vue__WEBPACK_IMPORTED_MODULE_2__[
                        /* default */
                        "a"
                      ]
                    },
                    emits: ["input"]
                  })
                ], NumberOption2);
                return NumberOption2;
              }(_BaseNumericOption__WEBPACK_IMPORTED_MODULE_3__[
                /* BaseNumericOption */
                "a"
              ])
            );
            __webpack_exports__["a"] = NumberOption;
          },
          /* 21 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _Arrow_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(4);
            var SelectOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(SelectOption2, _super);
                function SelectOption2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.open = false;
                  _this.items = [];
                  return _this;
                }
                Object.defineProperty(SelectOption2.prototype, "isAdvancedMode", {
                  get: function() {
                    return !this.items.every(function(i) {
                      return typeof i === "string";
                    });
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(SelectOption2.prototype, "selectedText", {
                  get: function() {
                    var _a, _b;
                    if (this.value) {
                      return this.isAdvancedMode ? (_b = (_a = this.getItemByValue(this.value)) === null || _a === void 0 ? void 0 : _a.text) !== null && _b !== void 0 ? _b : "" : this.value;
                    } else {
                      return "";
                    }
                  },
                  enumerable: false,
                  configurable: true
                });
                SelectOption2.prototype.mounted = function() {
                  var _this = this;
                  this.items = this.option.items || [];
                  this.option.events.updated.addListener(this, function() {
                    _this.items = _this.option.items || [];
                  });
                };
                SelectOption2.prototype.beforeDestroy = function() {
                  this.option.events.updated.removeListener(this);
                };
                SelectOption2.prototype.isSelected = function(item) {
                  if (this.isAdvancedMode) {
                    return item.value === this.value;
                  } else {
                    return item === this.value;
                  }
                };
                SelectOption2.prototype.setSelected = function(item) {
                  this.$emit("input", this.isAdvancedMode ? item.value : item);
                };
                SelectOption2.prototype.getItemByValue = function(value) {
                  return this.items.find(function(i) {
                    return i.value === value;
                  });
                };
                SelectOption2.prototype.getItemText = function(item) {
                  return this.isAdvancedMode ? item.text : item;
                };
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: String })
                ], SelectOption2.prototype, "name", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])()
                ], SelectOption2.prototype, "value", void 0);
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ type: Object })
                ], SelectOption2.prototype, "option", void 0);
                SelectOption2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "a"
                  ])({
                    components: {
                      "i-arrow": _Arrow_vue__WEBPACK_IMPORTED_MODULE_2__[
                        /* default */
                        "a"
                      ]
                    },
                    directives: {},
                    emits: ["input"]
                  })
                ], SelectOption2);
                return SelectOption2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "c"
              ])
            );
            __webpack_exports__["a"] = SelectOption;
          },
          /* 22 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var _BaseNumericOption__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5);
            var SliderOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(SliderOption2, _super);
                function SliderOption2() {
                  var _this = _super !== null && _super.apply(this, arguments) || this;
                  _this.didSlide = false;
                  _this.isMouseDown = false;
                  return _this;
                }
                Object.defineProperty(SliderOption2.prototype, "min", {
                  get: function() {
                    return this.option.min || 0;
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(SliderOption2.prototype, "max", {
                  get: function() {
                    return this.option.max || 1;
                  },
                  enumerable: false,
                  configurable: true
                });
                Object.defineProperty(SliderOption2.prototype, "percentage", {
                  get: function() {
                    return Math.min(100, Math.max(0, this.v * 100 / (this.max - this.min)));
                  },
                  enumerable: false,
                  configurable: true
                });
                SliderOption2.prototype.mousedown = function() {
                  if (this.editMode) {
                    return;
                  }
                  this.isMouseDown = true;
                };
                SliderOption2.prototype.mouseup = function() {
                  if (this.editMode) {
                    return;
                  }
                  if (!this.didSlide) {
                    this.enterEditMode();
                  }
                  this.isMouseDown = false;
                  this.didSlide = false;
                };
                SliderOption2.prototype.mouseleave = function(ev) {
                  if (this.editMode) {
                    return;
                  }
                  if (this.isMouseDown) {
                    if (ev.offsetX >= this.$el.clientWidth) {
                      this.$emit("input", this.max);
                    } else if (ev.offsetX <= 0) {
                      this.$emit("input", this.min);
                    }
                  }
                  this.isMouseDown = false;
                  this.didSlide = false;
                };
                SliderOption2.prototype.mousemove = function(ev) {
                  if (this.editMode) {
                    return;
                  }
                  var v = Math.max(this.min, Math.min(this.max, (this.max - this.min) * (ev.offsetX / this.$el.clientWidth) + this.min));
                  if (this.isMouseDown) {
                    this.$emit("input", v);
                    this.didSlide = true;
                  }
                };
                SliderOption2 = Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Options */
                    "a"
                  ])({
                    emits: ["input"]
                  })
                ], SliderOption2);
                return SliderOption2;
              }(_BaseNumericOption__WEBPACK_IMPORTED_MODULE_2__[
                /* BaseNumericOption */
                "a"
              ])
            );
            __webpack_exports__["a"] = SliderOption;
          },
          /* 23 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
            var TextOption = (
              /** @class */
              function(_super) {
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __extends */
                  "d"
                ])(TextOption2, _super);
                function TextOption2() {
                  return _super !== null && _super.apply(this, arguments) || this;
                }
                Object.defineProperty(TextOption2.prototype, "sanitized", {
                  get: function() {
                    return String(this.value);
                  },
                  enumerable: false,
                  configurable: true
                });
                Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                  /* __decorate */
                  "c"
                ])([
                  Object(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                    /* Prop */
                    "b"
                  ])({ default: "" })
                ], TextOption2.prototype, "value", void 0);
                return TextOption2;
              }(vue_property_decorator__WEBPACK_IMPORTED_MODULE_1__[
                /* Vue */
                "c"
              ])
            );
            __webpack_exports__["a"] = TextOption;
          },
          /* 24 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.r(__webpack_exports__);
            var _ButtonOption_vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(25);
            __webpack_require__.d(__webpack_exports__, "ButtonOption", function() {
              return _ButtonOption_vue__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
            var _CheckboxOption_vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(34);
            __webpack_require__.d(__webpack_exports__, "CheckboxOption", function() {
              return _CheckboxOption_vue__WEBPACK_IMPORTED_MODULE_1__["a"];
            });
            var _InputOption_vue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(36);
            __webpack_require__.d(__webpack_exports__, "InputOption", function() {
              return _InputOption_vue__WEBPACK_IMPORTED_MODULE_2__["a"];
            });
            var _IntegerOption_vue__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(38);
            __webpack_require__.d(__webpack_exports__, "IntegerOption", function() {
              return _IntegerOption_vue__WEBPACK_IMPORTED_MODULE_3__["a"];
            });
            var _NumberOption_vue__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(41);
            __webpack_require__.d(__webpack_exports__, "NumberOption", function() {
              return _NumberOption_vue__WEBPACK_IMPORTED_MODULE_4__["a"];
            });
            var _SelectOption_vue__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(43);
            __webpack_require__.d(__webpack_exports__, "SelectOption", function() {
              return _SelectOption_vue__WEBPACK_IMPORTED_MODULE_5__["a"];
            });
            var _SliderOption_vue__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(45);
            __webpack_require__.d(__webpack_exports__, "SliderOption", function() {
              return _SliderOption_vue__WEBPACK_IMPORTED_MODULE_6__["a"];
            });
            var _TextOption_vue__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(47);
            __webpack_require__.d(__webpack_exports__, "TextOption", function() {
              return _TextOption_vue__WEBPACK_IMPORTED_MODULE_7__["a"];
            });
          },
          /* 25 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _ButtonOption_vue_vue_type_template_id_53b47504__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(50);
            var _ButtonOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(6);
            _ButtonOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _ButtonOption_vue_vue_type_template_id_53b47504__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _ButtonOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 26 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "button",
                {
                  onClick: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("openSidebar")),
                  class: "dark-button --block"
                },
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.name),
                1
                /* TEXT */
              );
            }
          },
          /* 27 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            const hyphenateRE = /\B([A-Z])/g;
            const hyphenate = (str) => str.replace(hyphenateRE, "-$1").toLowerCase();
            function Emit(event) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, propertyKey) => {
                const emitName = event || hyphenate(propertyKey);
                componentOptions.emits || (componentOptions.emits = []);
                componentOptions.emits.push(emitName);
                const original = componentOptions.methods[propertyKey];
                componentOptions.methods[propertyKey] = function emitter(...args) {
                  const emit = (returnValue2) => {
                    if (returnValue2 === void 0) {
                      if (args.length === 0) {
                        this.$emit(emitName);
                      } else if (args.length === 1) {
                        this.$emit(emitName, args[0]);
                      } else {
                        this.$emit(emitName, ...args);
                      }
                    } else {
                      args.unshift(returnValue2);
                      this.$emit(emitName, ...args);
                    }
                  };
                  const returnValue = original.apply(this, args);
                  if (isPromise(returnValue)) {
                    returnValue.then(emit);
                  } else {
                    emit(returnValue);
                  }
                  return returnValue;
                };
              });
            }
            function isPromise(obj) {
              return obj instanceof Promise || obj && typeof obj.then === "function";
            }
          },
          /* 28 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            var vue_class_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3);
            function Inject(options = /* @__PURE__ */ Object.create(null)) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_1__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                const originalSetup = componentOptions.setup;
                componentOptions.setup = (props, ctx) => {
                  const result = originalSetup === null || originalSetup === void 0 ? void 0 : originalSetup(props, ctx);
                  const injectedValue = Object(vue__WEBPACK_IMPORTED_MODULE_0__["inject"])(options.from || key, options.default);
                  return Object.assign(Object.assign({}, result), { [key]: injectedValue });
                };
              });
            }
          },
          /* 29 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            function Model(propName, propOptions) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                const eventName = `update:${propName}`;
                componentOptions.props || (componentOptions.props = /* @__PURE__ */ Object.create(null));
                componentOptions.props[propName] = propOptions;
                componentOptions.emits || (componentOptions.emits = []);
                componentOptions.emits.push(eventName);
                componentOptions.computed || (componentOptions.computed = /* @__PURE__ */ Object.create(null));
                componentOptions.computed[key] = {
                  get() {
                    return this[propName];
                  },
                  set(newValue) {
                    this.$emit(eventName, newValue);
                  }
                };
              });
            }
          },
          /* 30 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return Prop;
            });
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            function Prop(propOptions) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                componentOptions.props || (componentOptions.props = /* @__PURE__ */ Object.create(null));
                componentOptions.props[key] = propOptions;
              });
            }
          },
          /* 31 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            var vue_class_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(3);
            function Provide(options) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_1__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                const originalProvide = componentOptions.provide;
                componentOptions.provide = function() {
                  const providedValue = typeof originalProvide === "function" ? originalProvide.call(this) : originalProvide;
                  return Object.assign(Object.assign({}, providedValue), { [(options === null || options === void 0 ? void 0 : options.to) || key]: (options === null || options === void 0 ? void 0 : options.reactive) ? Object(vue__WEBPACK_IMPORTED_MODULE_0__["computed"])(() => this[key]) : this[key] });
                };
              });
            }
          },
          /* 32 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            function Ref(refKey) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, key) => {
                componentOptions.computed || (componentOptions.computed = /* @__PURE__ */ Object.create(null));
                componentOptions.computed[key] = {
                  cache: false,
                  get() {
                    return this.$refs[refKey || key];
                  }
                };
              });
            }
          },
          /* 33 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return Watch;
            });
            var vue_class_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(3);
            function Watch(path, watchOptions) {
              return Object(vue_class_component__WEBPACK_IMPORTED_MODULE_0__[
                /* createDecorator */
                "c"
              ])((componentOptions, handler) => {
                componentOptions.watch || (componentOptions.watch = /* @__PURE__ */ Object.create(null));
                const watch = componentOptions.watch;
                if (typeof watch[path] === "object" && !Array.isArray(watch[path])) {
                  watch[path] = [watch[path]];
                } else if (typeof watch[path] === "undefined") {
                  watch[path] = [];
                }
                watch[path].push(Object.assign({ handler }, watchOptions));
              });
            }
          },
          /* 34 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _CheckboxOption_vue_vue_type_template_id_23f75af8__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(51);
            var _CheckboxOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7);
            _CheckboxOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _CheckboxOption_vue_vue_type_template_id_23f75af8__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _CheckboxOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 35 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
              "div",
              { class: "__checkmark-container" },
              [
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("svg", {
                  xmlns: "http://www.w3.org/2000/svg",
                  width: "18",
                  height: "18",
                  viewBox: "0 0 18 18"
                }, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("path", {
                    class: "__checkmark",
                    d: "M 6 5 L 6 10 L 16 10",
                    transform: "rotate(-45 10 10)"
                  })
                ])
              ],
              -1
              /* HOISTED */
            );
            const _hoisted_2 = { class: "ml-2" };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "div",
                {
                  onClick: _cache[1] || (_cache[1] = ($event) => _ctx.$emit("input", !_ctx.value)),
                  class: { "dark-checkbox": true, "--checked": _ctx.value }
                },
                [
                  _hoisted_1,
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    _hoisted_2,
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.name),
                    1
                    /* TEXT */
                  )
                ],
                2
                /* CLASS */
              );
            }
          },
          /* 36 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _InputOption_vue_vue_type_template_id_f43adbf0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(52);
            var _InputOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8);
            _InputOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _InputOption_vue_vue_type_template_id_f43adbf0__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _InputOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 37 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = { class: "dark-num-input" };
            const _hoisted_2 = { class: "__label .text-truncate" };
            const _hoisted_3 = { class: "__value" };
            const _hoisted_4 = {
              key: 1,
              class: "__content"
            };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_1, [
                !_ctx.editMode ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", {
                  key: 0,
                  class: "__content",
                  onClick: _cache[1] || (_cache[1] = (...args) => _ctx.enterEditMode && _ctx.enterEditMode(...args))
                }, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    _hoisted_2,
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.name),
                    1
                    /* TEXT */
                  ),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    _hoisted_3,
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.value),
                    1
                    /* TEXT */
                  )
                ])) : (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_4, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["withDirectives"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "input",
                    {
                      type: "text",
                      "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => _ctx.tempValue = $event),
                      class: "dark-input",
                      ref: "input",
                      onBlur: _cache[3] || (_cache[3] = (...args) => _ctx.leaveEditMode && _ctx.leaveEditMode(...args)),
                      onKeydown: _cache[4] || (_cache[4] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withKeys"])((...args) => _ctx.leaveEditMode && _ctx.leaveEditMode(...args), ["enter"])),
                      style: { "text-align": "right" }
                    },
                    null,
                    544
                    /* HYDRATE_EVENTS, NEED_PATCH */
                  ), [
                    [vue__WEBPACK_IMPORTED_MODULE_0__["vModelText"], _ctx.tempValue]
                  ])
                ]))
              ]);
            }
          },
          /* 38 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _IntegerOption_vue_vue_type_template_id_59f29b92__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(53);
            var _IntegerOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
            _IntegerOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _IntegerOption_vue_vue_type_template_id_59f29b92__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _IntegerOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 39 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = { class: "dark-num-input" };
            const _hoisted_2 = { class: "__label .text-truncate" };
            const _hoisted_3 = { class: "__value" };
            const _hoisted_4 = {
              key: 1,
              class: "__content"
            };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              const _component_i_arrow = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveComponent"])("i-arrow");
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_1, [
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", {
                  onClick: _cache[1] || (_cache[1] = (...args) => _ctx.decrement && _ctx.decrement(...args)),
                  class: "__button --dec"
                }, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(_component_i_arrow)
                ]),
                !_ctx.editMode ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", {
                  key: 0,
                  class: "__content",
                  onClick: _cache[2] || (_cache[2] = (...args) => _ctx.enterEditMode && _ctx.enterEditMode(...args))
                }, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    _hoisted_2,
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.name),
                    1
                    /* TEXT */
                  ),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    _hoisted_3,
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.stringRepresentation),
                    1
                    /* TEXT */
                  )
                ])) : (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_4, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["withDirectives"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "input",
                    {
                      type: "number",
                      "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => _ctx.tempValue = $event),
                      ref: "input",
                      onBlur: _cache[4] || (_cache[4] = (...args) => _ctx.leaveEditMode && _ctx.leaveEditMode(...args))
                    },
                    null,
                    544
                    /* HYDRATE_EVENTS, NEED_PATCH */
                  ), [
                    [vue__WEBPACK_IMPORTED_MODULE_0__["vModelText"], _ctx.tempValue]
                  ])
                ])),
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", {
                  onClick: _cache[5] || (_cache[5] = (...args) => _ctx.increment && _ctx.increment(...args)),
                  class: "__button --inc"
                }, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(_component_i_arrow)
                ])
              ]);
            }
          },
          /* 40 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = {
              xmlns: "http://www.w3.org/2000/svg",
              width: "24",
              height: "24",
              viewBox: "0 0 24 24"
            };
            const _hoisted_2 = Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
              "path",
              { d: "M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" },
              null,
              -1
              /* HOISTED */
            );
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("svg", _hoisted_1, [
                _hoisted_2
              ]);
            }
          },
          /* 41 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _NumberOption_vue_vue_type_template_id_5303df36__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(55);
            var _NumberOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(11);
            _NumberOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _NumberOption_vue_vue_type_template_id_5303df36__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _NumberOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 42 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = { class: "dark-num-input" };
            const _hoisted_2 = { class: "__label .text-truncate" };
            const _hoisted_3 = { class: "__value" };
            const _hoisted_4 = {
              key: 1,
              class: "__content"
            };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              const _component_i_arrow = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveComponent"])("i-arrow");
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_1, [
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", {
                  onClick: _cache[1] || (_cache[1] = (...args) => _ctx.decrement && _ctx.decrement(...args)),
                  class: "__button --dec"
                }, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(_component_i_arrow)
                ]),
                !_ctx.editMode ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", {
                  key: 0,
                  class: "__content",
                  onClick: _cache[2] || (_cache[2] = (...args) => _ctx.enterEditMode && _ctx.enterEditMode(...args))
                }, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    _hoisted_2,
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.name),
                    1
                    /* TEXT */
                  ),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    _hoisted_3,
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.stringRepresentation),
                    1
                    /* TEXT */
                  )
                ])) : (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_4, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["withDirectives"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "input",
                    {
                      type: "number",
                      "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => _ctx.tempValue = $event),
                      class: ["dark-input", { "--invalid": _ctx.invalid }],
                      ref: "input",
                      onBlur: _cache[4] || (_cache[4] = (...args) => _ctx.leaveEditMode && _ctx.leaveEditMode(...args)),
                      onKeydown: _cache[5] || (_cache[5] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withKeys"])((...args) => _ctx.leaveEditMode && _ctx.leaveEditMode(...args), ["enter"])),
                      style: { "text-align": "right" }
                    },
                    null,
                    34
                    /* CLASS, HYDRATE_EVENTS */
                  ), [
                    [vue__WEBPACK_IMPORTED_MODULE_0__["vModelText"], _ctx.tempValue]
                  ])
                ])),
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", {
                  onClick: _cache[6] || (_cache[6] = (...args) => _ctx.increment && _ctx.increment(...args)),
                  class: "__button --inc"
                }, [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(_component_i_arrow)
                ])
              ]);
            }
          },
          /* 43 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _SelectOption_vue_vue_type_template_id_7bf8b055__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(56);
            var _SelectOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(12);
            _SelectOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _SelectOption_vue_vue_type_template_id_7bf8b055__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _SelectOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 44 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = { class: "__selected" };
            const _hoisted_2 = { class: "__label .text-truncate" };
            const _hoisted_3 = { class: "__text" };
            const _hoisted_4 = { class: "__icon" };
            const _hoisted_5 = { class: "__dropdown" };
            const _hoisted_6 = { class: "item --header" };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              const _component_i_arrow = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveComponent"])("i-arrow");
              const _directive_click_outside_element = Object(vue__WEBPACK_IMPORTED_MODULE_0__["resolveDirective"])("click-outside-element");
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["withDirectives"])((Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "div",
                {
                  class: ["dark-select", { "--open": _ctx.open }],
                  onClick: _cache[1] || (_cache[1] = ($event) => _ctx.open = !_ctx.open)
                },
                [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", _hoisted_1, [
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                      "div",
                      _hoisted_2,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.name),
                      1
                      /* TEXT */
                    ),
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                      "div",
                      _hoisted_3,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.selectedText),
                      1
                      /* TEXT */
                    ),
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])("div", _hoisted_4, [
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(_component_i_arrow)
                    ])
                  ]),
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(vue__WEBPACK_IMPORTED_MODULE_0__["Transition"], { name: "slide-fade" }, {
                    default: Object(vue__WEBPACK_IMPORTED_MODULE_0__["withCtx"])(() => [
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["withDirectives"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                        "div",
                        _hoisted_5,
                        [
                          Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                            "div",
                            _hoisted_6,
                            Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.name),
                            1
                            /* TEXT */
                          ),
                          (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(true), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                            vue__WEBPACK_IMPORTED_MODULE_0__["Fragment"],
                            null,
                            Object(vue__WEBPACK_IMPORTED_MODULE_0__["renderList"])(_ctx.items, (item, i) => {
                              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", {
                                key: i,
                                class: ["item", { "--active": _ctx.isSelected(item) }],
                                onClick: ($event) => _ctx.setSelected(item)
                              }, Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.getItemText(item)), 11, ["onClick"]);
                            }),
                            128
                            /* KEYED_FRAGMENT */
                          ))
                        ],
                        512
                        /* NEED_PATCH */
                      ), [
                        [vue__WEBPACK_IMPORTED_MODULE_0__["vShow"], _ctx.open]
                      ])
                    ]),
                    _: 1
                    /* STABLE */
                  })
                ],
                2
                /* CLASS */
              )), [
                [_directive_click_outside_element, () => {
                  _ctx.open = false;
                }]
              ]);
            }
          },
          /* 45 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _SliderOption_vue_vue_type_template_id_bb55bacc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(57);
            var _SliderOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(13);
            _SliderOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _SliderOption_vue_vue_type_template_id_bb55bacc__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _SliderOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 46 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            const _hoisted_1 = {
              key: 0,
              class: "__content"
            };
            const _hoisted_2 = { class: "__label" };
            const _hoisted_3 = { class: "__value" };
            const _hoisted_4 = {
              key: 1,
              class: "__content"
            };
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "div",
                {
                  class: ["dark-slider", { "ignore-mouse": !_ctx.editMode }],
                  onMousedown: _cache[4] || (_cache[4] = (...args) => _ctx.mousedown && _ctx.mousedown(...args)),
                  onMouseup: _cache[5] || (_cache[5] = (...args) => _ctx.mouseup && _ctx.mouseup(...args)),
                  onMousemove: _cache[6] || (_cache[6] = (...args) => _ctx.mousemove && _ctx.mousemove(...args)),
                  onMouseleave: _cache[7] || (_cache[7] = (...args) => _ctx.mouseleave && _ctx.mouseleave(...args))
                },
                [
                  Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                    "div",
                    {
                      class: "__slider",
                      style: { width: _ctx.percentage + "%" }
                    },
                    null,
                    4
                    /* STYLE */
                  ),
                  !_ctx.editMode ? (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_1, [
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                      "div",
                      _hoisted_2,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.name),
                      1
                      /* TEXT */
                    ),
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                      "div",
                      _hoisted_3,
                      Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.stringRepresentation),
                      1
                      /* TEXT */
                    )
                  ])) : (Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])("div", _hoisted_4, [
                    Object(vue__WEBPACK_IMPORTED_MODULE_0__["withDirectives"])(Object(vue__WEBPACK_IMPORTED_MODULE_0__["createVNode"])(
                      "input",
                      {
                        type: "number",
                        "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => _ctx.tempValue = $event),
                        class: ["dark-input", { "--invalid": _ctx.invalid }],
                        ref: "input",
                        onBlur: _cache[2] || (_cache[2] = (...args) => _ctx.leaveEditMode && _ctx.leaveEditMode(...args)),
                        onKeydown: _cache[3] || (_cache[3] = Object(vue__WEBPACK_IMPORTED_MODULE_0__["withKeys"])((...args) => _ctx.leaveEditMode && _ctx.leaveEditMode(...args), ["enter"])),
                        style: { "text-align": "right" }
                      },
                      null,
                      34
                      /* CLASS, HYDRATE_EVENTS */
                    ), [
                      [vue__WEBPACK_IMPORTED_MODULE_0__["vModelText"], _ctx.tempValue]
                    ])
                  ]))
                ],
                34
                /* CLASS, HYDRATE_EVENTS */
              );
            }
          },
          /* 47 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _TextOption_vue_vue_type_template_id_4415daf7__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58);
            var _TextOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(14);
            _TextOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ].render = _TextOption_vue_vue_type_template_id_4415daf7__WEBPACK_IMPORTED_MODULE_0__[
              /* render */
              "a"
            ];
            __webpack_exports__["a"] = _TextOption_vue_vue_type_script_lang_ts__WEBPACK_IMPORTED_MODULE_1__[
              /* default */
              "a"
            ];
          },
          /* 48 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return render;
            });
            var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
            var vue__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
            function render(_ctx, _cache, $props, $setup, $data, $options) {
              return Object(vue__WEBPACK_IMPORTED_MODULE_0__["openBlock"])(), Object(vue__WEBPACK_IMPORTED_MODULE_0__["createBlock"])(
                "div",
                null,
                Object(vue__WEBPACK_IMPORTED_MODULE_0__["toDisplayString"])(_ctx.sanitized),
                1
                /* TEXT */
              );
            }
          },
          /* 49 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return OptionPlugin;
            });
            var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
            var _options__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(24);
            var OptionPlugin = (
              /** @class */
              function() {
                function OptionPlugin2() {
                  this.type = "OptionPlugin";
                }
                OptionPlugin2.prototype.register = function(editor) {
                  var _this = this;
                  editor.events.usePlugin.addListener(this, function(p) {
                    if (p.type === "ViewPlugin") {
                      _this.registerOptions(p);
                    }
                  });
                  editor.plugins.forEach(function(p) {
                    if (p.type === "ViewPlugin") {
                      _this.registerOptions(p);
                    }
                  });
                };
                OptionPlugin2.prototype.registerOptions = function(viewPlugin) {
                  Object.entries(_options__WEBPACK_IMPORTED_MODULE_1__).forEach(function(_a) {
                    var _b = Object(tslib__WEBPACK_IMPORTED_MODULE_0__[
                      /* __read */
                      "f"
                    ])(_a, 2), k = _b[0], v = _b[1];
                    viewPlugin.registerOption(k, v);
                  });
                };
                return OptionPlugin2;
              }()
            );
          },
          /* 50 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_ButtonOption_vue_vue_type_template_id_53b47504__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(26);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_ButtonOption_vue_vue_type_template_id_53b47504__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 51 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_CheckboxOption_vue_vue_type_template_id_23f75af8__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(35);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_CheckboxOption_vue_vue_type_template_id_23f75af8__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 52 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_InputOption_vue_vue_type_template_id_f43adbf0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(37);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_InputOption_vue_vue_type_template_id_f43adbf0__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 53 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_IntegerOption_vue_vue_type_template_id_59f29b92__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(39);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_IntegerOption_vue_vue_type_template_id_59f29b92__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 54 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Arrow_vue_vue_type_template_id_1066f486__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(40);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_Arrow_vue_vue_type_template_id_1066f486__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 55 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_NumberOption_vue_vue_type_template_id_5303df36__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(42);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_NumberOption_vue_vue_type_template_id_5303df36__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 56 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_SelectOption_vue_vue_type_template_id_7bf8b055__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(44);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_SelectOption_vue_vue_type_template_id_7bf8b055__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 57 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_SliderOption_vue_vue_type_template_id_bb55bacc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(46);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_SliderOption_vue_vue_type_template_id_bb55bacc__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 58 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            var _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_TextOption_vue_vue_type_template_id_4415daf7__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(48);
            __webpack_require__.d(__webpack_exports__, "a", function() {
              return _node_modules_vue_loader_dist_templateLoader_js_ref_5_node_modules_vue_loader_dist_index_js_ref_9_0_TextOption_vue_vue_type_template_id_4415daf7__WEBPACK_IMPORTED_MODULE_0__["a"];
            });
          },
          /* 59 */
          /***/
          function(module2, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.r(__webpack_exports__);
            var _options__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(24);
            __webpack_require__.d(__webpack_exports__, "ButtonOption", function() {
              return _options__WEBPACK_IMPORTED_MODULE_0__["ButtonOption"];
            });
            __webpack_require__.d(__webpack_exports__, "CheckboxOption", function() {
              return _options__WEBPACK_IMPORTED_MODULE_0__["CheckboxOption"];
            });
            __webpack_require__.d(__webpack_exports__, "InputOption", function() {
              return _options__WEBPACK_IMPORTED_MODULE_0__["InputOption"];
            });
            __webpack_require__.d(__webpack_exports__, "IntegerOption", function() {
              return _options__WEBPACK_IMPORTED_MODULE_0__["IntegerOption"];
            });
            __webpack_require__.d(__webpack_exports__, "NumberOption", function() {
              return _options__WEBPACK_IMPORTED_MODULE_0__["NumberOption"];
            });
            __webpack_require__.d(__webpack_exports__, "SelectOption", function() {
              return _options__WEBPACK_IMPORTED_MODULE_0__["SelectOption"];
            });
            __webpack_require__.d(__webpack_exports__, "SliderOption", function() {
              return _options__WEBPACK_IMPORTED_MODULE_0__["SliderOption"];
            });
            __webpack_require__.d(__webpack_exports__, "TextOption", function() {
              return _options__WEBPACK_IMPORTED_MODULE_0__["TextOption"];
            });
            var _optionPlugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(49);
            __webpack_require__.d(__webpack_exports__, "OptionPlugin", function() {
              return _optionPlugin__WEBPACK_IMPORTED_MODULE_1__["a"];
            });
          }
          /******/
        ])
      );
    });
  }
});
export default require_dist();
/*! Bundled license information:

@kanbang/plugin-options-vue3/dist/index.js:
  (*! *****************************************************************************
  Copyright (c) Microsoft Corporation.
  
  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.
  
  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** *)
  (**
    * vue-class-component v8.0.0-rc.1
    * (c) 2015-present Evan You
    * @license MIT
    *)
*/
//# sourceMappingURL=@kanbang_plugin-options-vue3.js.map

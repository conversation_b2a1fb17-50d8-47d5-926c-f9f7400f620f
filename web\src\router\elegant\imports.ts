/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  about: () => import("@/views/about/index.vue"),
  alova_request: () => import("@/views/alova/request/index.vue"),
  alova_scenes: () => import("@/views/alova/scenes/index.vue"),
  alova_user: () => import("@/views/alova/user/index.vue"),
  "demo-route_child": () => import("@/views/demo-route/child/index.vue"),
  device_blocks: () => import("@/views/device/blocks/index.vue"),
  device_flow: () => import("@/views/device/flow/index.vue"),
  device_record: () => import("@/views/device/record/index.vue"),
  device_remote: () => import("@/views/device/remote/index.vue"),
  device_review: () => import("@/views/device/review/index.vue"),
  device_terminals: () => import("@/views/device/terminals/index.vue"),
  dummy_department: () => import("@/views/dummy/department/index.vue"),
  dummy_dummy: () => import("@/views/dummy/dummy/index.vue"),
  "function_hide-child_one": () => import("@/views/function/hide-child/one/index.vue"),
  "function_hide-child_three": () => import("@/views/function/hide-child/three/index.vue"),
  "function_hide-child_two": () => import("@/views/function/hide-child/two/index.vue"),
  "function_multi-tab": () => import("@/views/function/multi-tab/index.vue"),
  function_request: () => import("@/views/function/request/index.vue"),
  "function_super-page": () => import("@/views/function/super-page/index.vue"),
  function_tab: () => import("@/views/function/tab/index.vue"),
  "function_toggle-auth": () => import("@/views/function/toggle-auth/index.vue"),
  "history-data_electrical": () => import("@/views/history-data/electrical/index.vue"),
  "history-data": () => import("@/views/history-data/index.vue"),
  "history-data_steam": () => import("@/views/history-data/steam/index.vue"),
  "history-data_torsion": () => import("@/views/history-data/torsion/index.vue"),
  home: () => import("@/views/home/<USER>"),
  manage_api: () => import("@/views/manage/api/index.vue"),
  manage_log: () => import("@/views/manage/log/index.vue"),
  manage_menu: () => import("@/views/manage/menu/index.vue"),
  manage_role: () => import("@/views/manage/role/index.vue"),
  "manage_user-detail": () => import("@/views/manage/user-detail/[id].vue"),
  manage_user: () => import("@/views/manage/user/index.vue"),
  "multi-menu_first_child": () => import("@/views/multi-menu/first_child/index.vue"),
  "multi-menu_second_child_home": () => import("@/views/multi-menu/second_child_home/index.vue"),
  "online-monitor_electrical": () => import("@/views/online-monitor/electrical/index.vue"),
  "online-monitor_main": () => import("@/views/online-monitor/main/index.vue"),
  "online-monitor_steam": () => import("@/views/online-monitor/steam/index.vue"),
  "online-monitor_torsion": () => import("@/views/online-monitor/torsion/index.vue"),
  plugin_barcode: () => import("@/views/plugin/barcode/index.vue"),
  plugin_charts_antv: () => import("@/views/plugin/charts/antv/index.vue"),
  plugin_charts_echarts: () => import("@/views/plugin/charts/echarts/index.vue"),
  plugin_charts_vchart: () => import("@/views/plugin/charts/vchart/index.vue"),
  plugin_copy: () => import("@/views/plugin/copy/index.vue"),
  plugin_editor_markdown: () => import("@/views/plugin/editor/markdown/index.vue"),
  plugin_editor_quill: () => import("@/views/plugin/editor/quill/index.vue"),
  plugin_excel: () => import("@/views/plugin/excel/index.vue"),
  plugin_gantt_dhtmlx: () => import("@/views/plugin/gantt/dhtmlx/index.vue"),
  plugin_gantt_vtable: () => import("@/views/plugin/gantt/vtable/index.vue"),
  plugin_icon: () => import("@/views/plugin/icon/index.vue"),
  plugin_map: () => import("@/views/plugin/map/index.vue"),
  plugin_pdf: () => import("@/views/plugin/pdf/index.vue"),
  plugin_pinyin: () => import("@/views/plugin/pinyin/index.vue"),
  plugin_print: () => import("@/views/plugin/print/index.vue"),
  plugin_swiper: () => import("@/views/plugin/swiper/index.vue"),
  plugin_tables_vtable: () => import("@/views/plugin/tables/vtable/index.vue"),
  plugin_typeit: () => import("@/views/plugin/typeit/index.vue"),
  plugin_video: () => import("@/views/plugin/video/index.vue"),
  task_log: () => import("@/views/task/log/index.vue"),
  task_scheduler: () => import("@/views/task/scheduler/index.vue"),
  "user-center": () => import("@/views/user-center/index.vue"),
};

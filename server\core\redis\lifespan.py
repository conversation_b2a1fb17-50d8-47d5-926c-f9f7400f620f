"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-05 11:41:07
LastEditors: zhai
LastEditTime: 2025-01-05 12:28:18
"""

from fastapi import FastAPI
from redis.asyncio import ConnectionPool, Redis
from settings import APP_SETTINGS, REDIS_URL
from log import log


# 测试连接是否有效
async def check_redis_connection(redis_pool):
    try:
        async with Red<PERSON>(connection_pool=redis_pool) as redis:
            await redis.ping()
            log.info(f"Redis连接成功: {redis.connection_pool.connection_kwargs}")
    except Exception as e:
        log.error(f"Redis连接失败: {e}")


async def init_redis(app: FastAPI) -> None:  # pragma: no cover
    """
    Creates connection pool for redis.

    :param app: current fastapi application.
    """
    app.state.redis_pool = ConnectionPool.from_url(
        REDIS_URL,
    )

    await check_redis_connection(app.state.redis_pool)


async def shutdown_redis(app: FastAPI) -> None:  # pragma: no cover
    """
    Closes redis connection pool.

    :param app: current FastAPI app.
    """
    await app.state.redis_pool.disconnect()

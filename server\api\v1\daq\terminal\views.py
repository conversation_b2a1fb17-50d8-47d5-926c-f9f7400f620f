"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-03-21 20:45:09
LastEditors: zhai
LastEditTime: 2025-04-15 23:48:04
"""

import hashlib
from fastapi import Depends, Request

from api.v1.utils import insert_log
from core.ctx import CTX_USER_ID
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import TerminalCreateDTO, TerminalDTO
from models.daq import TerminalModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=TerminalDTO,
    create_schema=TerminalCreateDTO,
    db_model=TerminalModel,
    prefix="terminals",
    tags=["终端管理"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)


def get_client_id_by_serial_code(serial_code: str):
    hash_obj = hashlib.md5(serial_code.encode())
    hex_dig = hash_obj.hexdigest()
    id = "AdLink-" + hex_dig
    return id


@router.post("/create", response_model=RespModelT[TerminalDTO])
async def overloaded_create(
    model: TerminalCreateDTO,
    request: Request,
):
    user_id = CTX_USER_ID.get()
    terminals = await TerminalModel.filter(
        serial_code=model.serial_code, created_by=user_id
    )

    if terminals:
        return resp_fail(msg="设备已存在")

    _pk = TerminalModel.describe()["pk_field"]["db_column"]
    model.client_id = get_client_id_by_serial_code(model.serial_code)
    obj = await router.create_obj_with_model(model, request, exclude={_pk})
    return resp_success(convert_to_pydantic(obj, TerminalDTO))

   

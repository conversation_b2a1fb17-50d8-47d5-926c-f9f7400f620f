from fastapi import Depends, Request, UploadFile

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import FirmwareCreateDTO, FirmwareDTO
from models.fishery import FirmwareModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=FirmwareDTO,
    create_schema=FirmwareCreateDTO,
    db_model=FirmwareModel,
    prefix="firmware",
    tags=["固件管理"],
    # kcreate_route=False,
    kbatch_create_route=False,
    kupsert_route=False,
    kupload_file_route=True,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)

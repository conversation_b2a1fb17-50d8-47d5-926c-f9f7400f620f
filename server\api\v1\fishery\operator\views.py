import json
from fastapi import Depends, Request

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType, StatusType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
    insert_operation,
    OperationType,
    MODEL_ID_TYPE,
)
from .schema import OperatorCreateDTO, OperatorDTO, OperatorPasswordDTO
from models.fishery import OperatorModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=OperatorDTO,
    create_schema=OperatorCreateDTO,
    db_model=OperatorModel,
    prefix="operator",
    tags=["作业账号管理"],
    kcreate_route=True,
    kbatch_create_route=False,
    kdelete_route=True,
    kbatch_delete_route=False,
    kdelete_all_route=False,
    kupdate_route=True,
    kbatch_update_route=False,
    kget_by_id_route=True,
    kget_one_by_filter_route=True,
    klist_route=True,
    kquery_route=True,
    kquery_ex_route=True,
    kupsert_route=False,
    user_data_option=UserDataOption.SELF_DEFAULT,
    tenant_data_option=TenantDataOption.TENANT_DEFAULT,
)


# Overriding
@router.post("/create", response_model=RespModelT[OperatorDTO])
async def _(model: OperatorCreateDTO, request: Request):
    user_obj = await user_controller.get_by_username(model.account)
    if user_obj:
        return resp_fail(code="4090", msg="The user with this account already exists in the system.")

    tenant_id = getattr(request.state, "tenant_id", None)
    user_in = UserCreate(
        tenant_id=tenant_id,
        user_name=model.account,
        nick_name=model.name,
        user_email=model.account + "@t.com",
        password=model.password,
        user_phone=model.account,
    )
    new_user = await user_controller.create(obj_in=user_in)
    await user_controller.update_roles_by_code(new_user, ["R_OPERATOR"])

    # await insert_log(log_type=LogType.AdminLog, log_detail_type=LogDetailType.UserCreateOne, by_user_id=0)

    _pk = OperatorModel.describe()["pk_field"]["db_column"]
    model.password = get_password_hash(password=model.password)
    obj = await router.create_obj_with_model(model, request, exclude={_pk})
    return resp_success(convert_to_pydantic(obj, OperatorDTO))


@router.post("/update", response_model=RespModelT[OperatorDTO])
async def _(
    model: OperatorDTO,
    request: Request,
):
    try:
        obj = await router.db_model.get_or_none(**{router._pk: getattr(model, router._pk)})
        if not obj:
            return resp_fail(code="4090", msg="The operator not found")

        user = await user_controller.get_by_username(obj.account)
        if not user:
            return resp_fail(code="4090", msg="The user not found")

        # 更新operator
        await router.update_obj_with_model(obj, model, request)

        # 更新user
        user_update = UserUpdate(password="", nick_name=model.name)
        if model.status is not None:
            user_update.status_type = StatusType.enable if model.status else StatusType.disable
        await user_controller.update(user.id, user_update)

        # 插入日志
        await insert_operation(
            user=getattr(request.state, "user_name", "unknown"),
            action=OperationType.EDIT,
            target=router.target,
            notes=json.dumps(model, default=str, indent=1),
            tenant_id=getattr(request.state, "tenant_id", None),
            user_id=getattr(request.state, "user_id", None),
            trace_id=getattr(request.state, "x_request_id", None),
        )

        return resp_success(convert_to_pydantic(obj, OperatorDTO))
    except Exception as e:
        raise ValueError("id不存在!")


@router.post("/delete", response_model=RespModelT[bool])
async def _(
    item_id: MODEL_ID_TYPE,
    request: Request,
) -> RespModelT[bool]:
    obj = await router.db_model.get_or_none(**{router._pk: item_id})
    if not obj:
        return resp_fail(code="4090", msg="The operator not found")

    await obj.delete()

    user = await user_controller.get_by_username(obj.account)
    if user:
        await user.delete()

    await insert_operation(
        user=getattr(request.state, "user_name", "unknown"),
        action=OperationType.DELETE,
        target=router.target,
        notes=json.dumps({router._pk: item_id, "result": True}, default=str),
        tenant_id=getattr(request.state, "tenant_id", None),
        user_id=getattr(request.state, "user_id", None),
        trace_id=getattr(request.state, "x_request_id", None),
    )

    return resp_success(True)


@router.post("/updatepassword", response_model=RespModelT[OperatorDTO])
async def _(model: OperatorPasswordDTO, request: Request):
    try:
        obj = await router.db_model.get(**{router._pk: getattr(model, router._pk)})
        user = await user_controller.get_by_username(obj.account)
        if not user:
            return resp_fail(code="4090", msg="The user not found")

        model.password = get_password_hash(password=model.password)
        await router.update_obj_with_model(obj, model, request)
        await user_controller.update(user.id, UserUpdate(password=model.password))

        return resp_success(convert_to_pydantic(obj, router.schema))
    except Exception as e:
        # raise ValueError("id不存在!")
        return resp_fail(code="4090", msg="operator id not found")

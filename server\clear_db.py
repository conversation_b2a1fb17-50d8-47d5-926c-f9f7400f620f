import os
import pathlib
import shutil

# 定义要清理的数据库文件前缀和迁移目录
DB_PREFIX = "app_system."
MIGRATIONS_DIR = "migrations/app_system"

def clear_database_files():
    # 获取当前工作目录
    cwd = pathlib.Path().cwd()
    # 遍历当前工作目录下的所有文件
    for db_file in cwd.glob(f"{DB_PREFIX}*"):
        if db_file.is_file():
            db_file.unlink()  # 删除文件

def clear_migrations():
    # 清理迁移目录
    migrations_path = pathlib.Path(MIGRATIONS_DIR)
    if migrations_path.exists() and migrations_path.is_dir():
        shutil.rmtree(migrations_path)

if __name__ == "__main__":
    clear_database_files()
    clear_migrations()
    print("清理完成")


/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-01-04 15:11:54
 * @LastEditors: zhai
 * @LastEditTime: 2025-04-15 22:03:06
 */
import { createApp } from 'vue';
import './plugins/assets';
import {
  setupAppVersionNotification,
  setupDayjs,
  setupFastCrud,
  setupIconifyOffline,
  setupLoading,
  setupNProgress
} from './plugins';
import { setupStore } from './store';
import { setupRouter } from './router';
import { setupI18n } from './locales';
import App from './App.vue';
import { setupMqtt } from './mqtt';

import { BaklavaVuePlugin } from '@kanbang/plugin-renderer-vue3';
import '@kanbang/plugin-renderer-vue3/dist/styles.css';

/// ///////////////////////////////////////////////////////////////////
// Monaco
// import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
// import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
// import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
// import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
// import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';

// const pythonWorkerUrl = new URL('./@typefox/pyright-browser/dist/pyright.worker.js', window.location.href).href;
// console.info(`main.ts, pythonWorkerUrl: ${pythonWorkerUrl}`);

// import { whenReady } from '@codingame/monaco-vscode-python-default-extension';

// self.MonacoEnvironment = {
//   getWorker(_, label) {
//     if (label === 'json') {
//       return new jsonWorker();
//     }
//     if (label === 'css' || label === 'scss' || label === 'less') {
//       return new cssWorker();
//     }
//     if (label === 'html' || label === 'handlebars' || label === 'razor') {
//       return new htmlWorker();
//     }
//     if (label === 'typescript' || label === 'javascript') {
//       return new tsWorker();
//     }

//     return new editorWorker();
//   },
// };
/// ///////////////////////////////////////////////////////////////////

async function setupApp() {
  // await whenReady();
  setupLoading();

  setupNProgress();

  setupIconifyOffline();

  setupDayjs();

  const app = createApp(App);

  // BaklavaVuePlugin
  app.use(BaklavaVuePlugin);

  setupStore(app);

  await setupRouter(app);

  setupI18n(app);

  setupAppVersionNotification();

  setupFastCrud(app);
  setupMqtt(app);
  app.mount('#app');
}

setupApp();

import { mqttClient } from '@/utils/mqtt'  // 你项目封装好的 MQTT 客户端
import { onMounted, onBeforeUnmount } from 'vue'

const topic = 'torsion/sensor1' // 举例

onMounted(() => {
  mqttClient.subscribe(topic)
  mqttClient.on('message', (recvTopic, payload) => {
    if (recvTopic === topic) {
      const { x, y } = JSON.parse(payload.toString())
      updateWave(x, y)
    }
  })
})

onBeforeUnmount(() => {
  mqttClient.unsubscribe(topic)
})

// 文件位置建议：src/utils/mqtt.ts
import mqtt, { MqttClient } from 'mqtt'

let client: MqttClient | null = null

const MQTT_BROKER_URL = 'ws://your.mqtt.server:port/mqtt' // 例如 ws://localhost:9001/mqtt
const OPTIONS = {
  connectTimeout: 4000,
  clientId: 'web_client_' + Math.random().toString(16).substr(2, 8),
  username: 'your_username',
  password: 'your_password',
  keepalive: 60,
}

/** 初始化 MQTT 客户端并连接 */
export function connectMQTT() {
  if (client) return client
  client = mqtt.connect(MQTT_BROKER_URL, OPTIONS)
  client.on('connect', () => {
    console.log('✅ MQTT Connected')
  })
  client.on('error', err => {
    console.error('❌ MQTT Error:', err)
  })
  return client
}

/** 获取已连接的 MQTT 客户端 */
export function getMQTTClient(): MqttClient {
  if (!client) throw new Error('MQTT not connected yet!')
  return client
}

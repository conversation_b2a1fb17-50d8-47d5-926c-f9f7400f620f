<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { steamGroups } from './steam.js';
// import mqttHook from '@/hooks/mqttHook' // 请改成你项目中实际路径
import { useMQTT } from 'mqtt-vue-hook';

const latestValues = ref({});
const mqttHook = useMQTT();

// 本地配置的点位
const pressurePoints = ref([]);
const tempPoints = ref([]);

// 初始化本地点位
onMounted(() => {
  const pGroup = steamGroups.find(g => g.Name === '蒸汽压力')
  const tGroup = steamGroups.find(g => g.Name === '蒸汽温度')
  pressurePoints.value = pGroup?.Points || []
  tempPoints.value = tGroup?.Points || []

  mqttHook.registerEvent('TVS/data/type3', (_, message) => {
    try {
      const jsonStr = new TextDecoder().decode(message)
      const decoded = JSON.parse(jsonStr)
      console.log('解码成功', decoded)

      const headers = decoded.Thermals?.[0] || []
      const values = decoded.Thermals?.[1] || []

      const dataMap = {}
      for (let i = 1; i < headers.length; i++) {
        dataMap[headers[i]] = Number(values[i])
      }
      latestValues.value = dataMap
    } catch (err) {
      console.error('解析失败:', err)
    }
  }, 'mqtt-gear-key')
})

// 显示格式
const formatValue = (val) => {
  return val !== undefined ? val.toFixed(2) : '--'
}

onUnmounted(() => {
  console.log('取消事件')
  mqttHook.unRegisterEvent('TVS/data/type3', 'mqtt-gear-key');
});
</script>

<template>
  <div class="steam-dashboard">
    <!-- 蒸汽压力 -->
    <div class="half-section">
      <div class="label-cell with-bg">蒸汽参数</div>
      <div class="data-cards">
        <div class="data-card" v-for="point in pressurePoints" :key="point.KEY">
          <div class="label">{{ point.AN }}</div>
          <div class="code">编码：{{ point.PN }}</div>
          <div class="line"></div>

          <div class="value">
            <span class="number">{{ formatValue(latestValues[point.KEY]) }}</span>
            <span class="unit">({{ point.Unit }})</span>
          </div>
        </div>
      </div>
    </div>

    <div class="divider"></div>

    <!-- 蒸汽温度 -->
    <div class="half-section">
      <div class="label-cell with-bg">蒸汽温度</div>
      <div class="data-cards">
        <div class="data-card" v-for="point in tempPoints" :key="point.KEY">
          <div class="label">{{ point.AN }}</div>
          <div class="code">编码：{{ point.PN }}</div>
          <div class="line"></div>
          <div class="value">
            <span class="number">{{ formatValue(latestValues[point.KEY]) }}</span>
            <span class="unit">({{ point.Unit }})</span>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>



<style scoped>
.steam-dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #1e1e1e;
  color: white;
  font-family: Arial, sans-serif;
}

.half-section {
  display: flex;
  flex: 1;
  height: 50%;
  overflow: hidden;
}

.line {
  height: 1px;
  background: black;
  margin: 7px 0px 3px 0px;
}

.label-cell {
  width: 80px;
  margin: 10px 0px 10px 10px;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  writing-mode: vertical-lr;
  /* transform: rotate(180deg); */
  background-color: #2f2f2f;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;

}

.data-cards {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 12px;
  padding: 12px 16px;
  overflow-y: auto;

}

.data-card {
  background: #2c2c2c;
  color: #cfcfcf;
  border-radius: 8px;
  padding: 12px;
  width: 180px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.label {
  font-weight: bold;
  margin-bottom: 4px;
  text-align: center;
}

.value {
  font-size: 20px;
  color: #00ccff;
  text-align: center;
}

.unit {
  font-size: 14px;
  margin-left: 4px;
}

.code {
  font-size: 12px;
  margin-top: 6px;
  color: #999;
}

.divider {
  height: 1px;
  background-color: #444;
  width: 100%;
}

:deep(.n-tabs-tab) {
  padding: 10px 6px 5px 0px !important;
}
</style>

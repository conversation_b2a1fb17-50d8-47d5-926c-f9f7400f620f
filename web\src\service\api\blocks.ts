/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-21 19:42:39
 * @LastEditors: zhai
 * @LastEditTime: 2024-06-21 20:01:57
 */
import { request } from '@/service/request';
import { CrudApi } from '../crud-api';
import { FastCrudApi } from '../fast-crud-api';

/// ///////////////////////////////////////////////////////////////////

export class FastBlocksApi extends FastCrudApi<FastBlocksApi> {
  constructor() {
    super('blocks');
  }

  // Get One By Filter Value
  async get_blocks(id?: number | string) {
    return request<any, 'json'>({
      url: `/${this.prefix}/blocks/${id}`,
      method: 'get'
    });
  }
}

export const fast_blocks_api = FastBlocksApi.instance();

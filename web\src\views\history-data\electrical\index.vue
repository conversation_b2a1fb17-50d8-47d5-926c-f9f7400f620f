<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue';
// import { useMQTT } from 'mqtt-vue-hook';
import WaveChart from '@/components/waveChart/index.vue';
import { NDropdown, NButton, NTable, NDatePicker, NTabs, NTabPane } from 'naive-ui';
// import { decode, encode } from '@msgpack/msgpack';
import FFT from 'fft.js/lib/fft';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { localStg } from '@/utils/storage';
import dayjs from 'dayjs';

function get_token() {
  const token = localStg.get('token');
  return token;
}

// const mqttHook = useMQTT();

let VPhaseRef = ref();
let VFreqRef = ref();
let CPhaseRef = ref();
let CFreqRef = ref();
let ExcRef = ref();

// let chartContainerRef = ref(null);

let VPhaseOptions = reactive({ title: '电压', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let VFreqOptions = reactive({ title: '频率', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let CPhaseOptions = reactive({ title: '电流', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let CFreqOptions = reactive({ title: '频率', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
let ExcOptions = reactive({ title: '电流', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });

const tabKey = ref('tension');
const sectabKey = ref('exci');
const showRightPanel = ref(true);
const showEventPanel = ref(true);

let fullData = reactive({});
let tableData = ref([]);
let eventList = ref([
  { time: '2025-07-20 10:00:00', name: '过电压', type: '报警' },
  { time: '2025-07-20 10:05:00', name: '低电流', type: '提示' },
]);
const selectedLine = ref('线路-未选择');

const dropdownOptions = [
  {
    label: '电压',
    key: 'voltage',
    children: [
      { label: 'A相电压', key: 'voltage-A相电压' },
      { label: 'B相电压', key: 'voltage-B相电压' },
      { label: 'C相电压', key: 'voltage-C相电压' }
    ]
  },
  {
    label: '电流',
    key: 'current',
    children: [
      { label: 'A相电流', key: 'current-A相电流' },
      { label: 'B相电流', key: 'current-B相电流' },
      { label: 'C相电流', key: 'current-C相电流' }
    ]
  },
  {
    label: '励磁电流',
    key: 'excitation',
    children: [
      { label: 'IExc-励磁电流', key: 'excitation-励磁电流' }
    ]
  },
  {
    label: '频谱（电压）',
    key: 'freq-voltage',
    children: [
      { label: 'A相电压', key: 'freq-频谱(A相电压)' },
      { label: 'B相电压', key: 'freq-频谱(B相电压)' },
      { label: 'C相电压', key: 'freq-频谱(C相电压)' }
    ]
  },
  {
    label: '频谱（电流）',
    key: 'freq-current',
    children: [
      { label: 'A相电流', key: 'freq-频谱(A相电流)' },
      { label: 'B相电流', key: 'freq-频谱(B相电流)' },
      { label: 'C相电流', key: 'freq-频谱(C相电流)' }
    ]
  }
];
// 时间选择
const dateRange = ref<[number, number] | null>(null);

// 更新右侧列表
const updateTable = (lineKey) => {
  console.log('更新表格数据:', lineKey, fullData[lineKey]);
  tableData.value = fullData[lineKey] || [];
};
// 选择列表线路
const handleSelect = (key) => {
  if (!dateRange.value) {
    window.$message?.warning('请先选择时间范围');
    return;
  }
  selectedLine.value = key;
  updateTable(key);
  // to-do-更新事件列表
};

watch([showRightPanel, showEventPanel], async () => {
  await nextTick();
  [VPhaseRef, VFreqRef, CPhaseRef, CFreqRef, ExcRef].forEach(refItem => {
    refItem.value?.resize?.();
  });
});

function getGroupKey(name: string): string {
  if (['A相电压', 'B相电压', 'C相电压'].includes(name)) return 'voltage';
  if (['A相电流', 'B相电流', 'C相电流'].includes(name)) return 'current';
  if (name === '励磁电流') return 'excitation';
  if (['频谱(A相电压)', '频谱(B相电压)', '频谱(C相电压)', '频谱(A相电流)', '频谱(B相电流)', '频谱(C相电流)'].includes(name)) return 'freq';
  return 'unknown';
}
// 外部保存 eventSource 引用
let sseInstance: EventSourcePolyfill | null = null;
// 查询函数
const handleQuery = () => {
  if (!dateRange.value) {
    window.$message?.warning('请先选择时间范围');
    return;
  }
  if (sseInstance) {
    console.log('🛑 页面卸载，手动关闭 SSE');
    sseInstance?.close();
    sseInstance = null;
  }
  console.log('执行查询函数')

  const [start, end] = dateRange.value;
  const recordType = 'ElecSignal';

  // const startISO = new Date(start).toISOString();
  // const endISO = new Date(end).toISOString();

  const startISO = dayjs(start).format('YYYY-MM-DD HH:mm:ss');
  const endISO = dayjs(end).format('YYYY-MM-DD HH:mm:ss');

  console.log(startISO, endISO)
  const token = get_token();
  const base_url = import.meta.env.VITE_SERVICE_BASE_URL;

  sseInstance = new EventSourcePolyfill(`${base_url}/tvs/record_sse/${recordType}/${startISO}/${endISO}`, {
    headers: {
      token,
      Authorization: `Bearer ${token}`
    }
  });

  sseInstance.onopen = () => {
    console.log('📡 SSE连接已建立');
  };

  sseInstance.addEventListener('data', (event) => {
    try {
      console.log('接收到数据:', event.data.length);

      const raw = event.data;
      const fixedStr = raw
        .replace(/'/g, '"')
        .replace(/\bTrue\b/g, 'true')
        .replace(/\bFalse\b/g, 'false')
        .replace(/\bNone\b/g, 'null');

      const data = JSON.parse(fixedStr);
      console.log('解析后数据', data.batch);
      const rows = data.batch || [];
      if (rows.length < 2) return;
      const lineName = {
        'VA': 'A相电压',
        'VB': 'B相电压',
        'VC': 'C相电压',
        'IA': 'A相电流',
        'IB': 'B相电流',
        'IC': 'C相电流',
        'IExc': '励磁电流'
      }
      // const t0 = Number(batch[0].Tstamp);
      // const t1 = Number(batch[1].Tstamp);
      // const fs = 1 / (t1 - t0);
      // const N = rows.length;

      const x = rows.map(item => {
        const d = new Date(item.Tstamp * 1000);
        const pad = (n: number, len = 2) => String(n).padStart(len, '0');
        return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ` +
          `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}.${pad(d.getMilliseconds(), 3)}`;
      });

      const buildY = (names: string[]) =>
        names.map(name => ({
          name: lineName[name],
          value: rows.map(row => row[name])
        }));

      const chartDataV = { x, y: buildY(['VA', 'VB', 'VC']) };
      const chartDataI = { x, y: buildY(['IA', 'IB', 'IC']) };
      const chartDataE = { x, y: buildY(['IExc']) };

      VPhaseRef.value?.appendData(chartDataV, 10000);
      CPhaseRef.value?.appendData(chartDataI, 10000);
      ExcRef.value?.appendData(chartDataE, 10000);

      // 增加频谱处理逻辑（每组计算一次），提取时间戳（微秒）并计算采样率
      if (rows.length < 2) {
        console.warn('❗️数据不足，跳过频谱计算');
        return;
      }
      const t0 = Number(rows[0].Tstamp);
      const t1 = Number(rows[1].Tstamp);
      const dt_us = t1 - t0;
      const fs = 1e6 / dt_us; // Hz，采样率
      const N = rows.length;

      // 准备信号通道数据
      const getSignal = (ch: string) => rows.map(row => Number(row[ch]));

      const signalVA = getSignal('VA');
      const signalVB = getSignal('VB');
      const signalVC = getSignal('VC');
      const signalIA = getSignal('IA');
      const signalIB = getSignal('IB');
      const signalIC = getSignal('IC');

      const computeFFT = (signal: number[]) => {
        // 确保FFT长度是2的幂次方
        const nextPowerOfTwo = (n: number) => {
          return Math.pow(2, Math.ceil(Math.log2(n)));
        };

        const fftSize = nextPowerOfTwo(N);
        console.log(`原始数据长度: ${N}, FFT长度: ${fftSize}`);

        // 如果数据长度小于FFT长度，用零填充
        const paddedSignal = [...signal];
        while (paddedSignal.length < fftSize) {
          paddedSignal.push(0);
        }

        const fft = new FFT(fftSize);
        const out = fft.createComplexArray();
        fft.realTransform(out, paddedSignal);
        fft.completeSpectrum(out);
        const mag = [];
        for (let i = 0; i < fftSize / 2; i++) {
          const re = out[2 * i], im = out[2 * i + 1];
          mag.push(Math.sqrt(re * re + im * im));
        }
        return mag;
      };

      // 频率轴（使用FFT长度计算）
      const nextPowerOfTwo = (n: number) => {
        return Math.pow(2, Math.ceil(Math.log2(n)));
      };
      const fftSize = nextPowerOfTwo(N);
      const freq = Array.from({ length: fftSize / 2 }, (_, i) => i * fs / fftSize);

      const chartDataFV = {
        x: freq,
        y: [
          { name: '频谱(A相电压)', value: computeFFT(signalVA) },
          { name: '频谱(B相电压)', value: computeFFT(signalVB) },
          { name: '频谱(C相电压)', value: computeFFT(signalVC) },
        ],
      };

      const chartDataFI = {
        x: freq,
        y: [
          { name: '频谱(A相电流)', value: computeFFT(signalIA) },
          { name: '频谱(B相电流)', value: computeFFT(signalIB) },
          { name: '频谱(C相电流)', value: computeFFT(signalIC) },
        ],
      };

      VFreqRef.value?.setData(chartDataFV);
      CFreqRef.value?.setData(chartDataFI);

      // 更新 fullData（用于表格展示）
      const assignFullData = (chartData: typeof chartDataV) => {
        chartData.y.forEach(series => {
          fullData[`${getGroupKey(series.name)}-${series.name}`] = chartData.x.map((xi, i) => ({
            x: xi,
            y: series.value[i]
          }));
        });
      };
      // 分别更新表格数据源
      assignFullData(chartDataV);
      assignFullData(chartDataI);
      assignFullData(chartDataE);
      assignFullData(chartDataFV);
      assignFullData(chartDataFI);

      // 更新右侧表格展示
      updateTable(selectedLine.value);

      if (data.has_more === false) {
        console.log('🔚 数据发送完毕（has_more = false），关闭连接');
        sseInstance?.close();
        sseInstance = null;
      }
    } catch (err) {
      console.error('❌ JSON 解析失败:', err);
    }
  });

  sseInstance.addEventListener('end', () => {
    console.log('✅ 数据已接收完毕，关闭连接');
    sseInstance?.close();
    sseInstance = null;
  });

  sseInstance.onerror = (err) => {
    console.error('❌ SSE 错误:', err);
    console.log('🛑 SSE 连接出错，关闭连接');
    sseInstance?.close();
    sseInstance = null;
  };
};
const setRecent = (type: 'hour' | 'day' | 'week' | 'month') => {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  const oneDay = 24 * 60 * 60 * 1000;
  const ranges = {
    hour: [now - oneHour, now],
    day: [now - oneDay, now],
    week: [now - 7 * oneDay, now],
    month: [now - 30 * oneDay, now],
  };
  dateRange.value = ranges[type];

  handleQuery();
};
const isExporting = ref(false);
const handleExport = async () => {
  console.log('导出数据:')
  if (!dateRange.value) {
    window.$message?.warning('请先选择时间范围');
    return;
  }
  isExporting.value = true;

  const [start, end] = dateRange.value;
  const startISO = new Date(start).toISOString();
  const endISO = new Date(end).toISOString();

  const recordType = 'ElecSignal'; // 🔁 根据你的页面配置修改
  const token = get_token();
  const base_url = import.meta.env.VITE_SERVICE_BASE_URL;

  try {
    const response = await fetch(`${base_url}/tvs/export_csv`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'token': token // 如果后端使用 token 字段做鉴权
      },
      body: JSON.stringify({
        record_type: recordType,
        start_time: startISO,
        end_time: endISO
      })
    });

    if (!response.ok) {
      throw new Error(`导出失败：${response.status}`);
    }

    // 创建 Blob 下载文件
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    const now = new Date();
    const pad = (n: number) => String(n).padStart(2, '0');
    const timestamp = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}_${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(now.getSeconds())}`;

    // const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    a.download = `tvs_export_${timestamp}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  } catch (err) {
    console.error('❌ 导出失败:', err);
    window.$message?.error('导出失败');
  } finally {
    isExporting.value = false;
  }
};
onUnmounted(() => {
  if (sseInstance) {
    console.log('🛑 页面卸载，手动关闭 SSE');
    sseInstance?.close();
    sseInstance = null;
  }
});
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <!-- 查询栏 -->
    <div class="flex items-center p-2 gap-2 border-b border-gray-600 bg-black text-white">
      <n-date-picker v-model:value="dateRange" type="datetimerange" size="small" />
      <n-button type="primary" size="small" @click="handleQuery">查询</n-button>
      <n-button size="small" @click="setRecent('hour')">最近一小时</n-button>
      <n-button size="small" @click="setRecent('day')">最近一天</n-button>
      <n-button size="small" @click="setRecent('week')">最近一周</n-button>
      <n-button size="small" @click="setRecent('month')">最近一月</n-button>

      <n-button ghost size="small" @click="handleExport" :disabled="isExporting">{{ isExporting ? '下载中,请稍等...' : '导出数据'
        }}</n-button>
    </div>

    <!-- 图表及右侧面板 -->
    <div class="flex flex-1 overflow-hidden">
      <!-- 图表区 -->
      <div class="flex flex-col flex-1" :style="{
        width:
          showRightPanel && showEventPanel
            ? 'calc(100% - 32rem)'
            : showRightPanel || showEventPanel
              ? 'calc(100% - 16rem)'
              : '100%'
      }">
        <div class="flex h-1/2 border-b border-gray-700">
          <n-tabs v-model:value="tabKey" placement="left" type="line">
            <n-tab-pane name="tension" tab="三相电压">
              <div class="flex h-full">
                <WaveChart ref="VPhaseRef" height="100%" :options="VPhaseOptions"
                  class="flex-1 border-r border-gray-700" />
                <WaveChart ref="VFreqRef" height="100%" :options="VFreqOptions" class="flex-1" />
              </div>
            </n-tab-pane>
            <n-tab-pane name="current" tab="三相电流">
              <div class="flex h-full">
                <WaveChart ref="CPhaseRef" height="100%" :options="CPhaseOptions"
                  class="flex-1 border-r border-gray-700" />
                <WaveChart ref="CFreqRef" height="100%" :options="CFreqOptions" class="flex-1" />
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
        <div class="h-1/2 flex">
          <n-tabs v-model:value="sectabKey" placement="left" type="line">
            <n-tab-pane name="exci" tab="励磁电流">
              <div class="flex h-full">
                <WaveChart ref="ExcRef" :options="ExcOptions" class="flex-1" />
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
      </div>

      <!-- 数据表格面板 -->
      <div class="relative h-full">
        <n-tooltip trigger="hover" placement="left">
          <template #trigger>
            <n-button v-show="!showRightPanel" class="expand-icon" size="tiny" text @click="showRightPanel = true"
              type="primary">
              <template #icon>
                <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
              </template>
            </n-button>
          </template>
          展开-数据列表
        </n-tooltip>
        <transition name="slide">
          <div v-show="showRightPanel" class="w-48 h-full border-l border-gray-700 bg-black text-white flex flex-col">
            <div class="flex p-2 gap-1">
              <n-tooltip trigger="hover" placement="right">
                <template #trigger>
                  <n-button quaternary size="small" @click="showRightPanel = false"
                    style="background-color: #2a2a2a; border: 1px solid #888;width: 28px;height:28px;">
                    <template #icon>
                      <img src="@/assets/imgs/TablerChevronRightPipe.svg" class="sicon-white " />
                    </template>
                  </n-button>
                </template>
                收起-数据列表
              </n-tooltip>
              <n-dropdown trigger="click" :options="dropdownOptions" @select="handleSelect">
                <n-button size="small" class="flex-1">选择线路 - {{ selectedLine.split('-')[1] }}</n-button>
              </n-dropdown>
            </div>
            <div class="flex-1 overflow-auto p-2">
              <n-table :bordered="false" :single-line="false" size="small">
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>X</th>
                    <th>Y</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(row, index) in tableData" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ row.x }}</td>
                    <td>{{ row.y }}</td>
                  </tr>
                </tbody>
              </n-table>
            </div>
          </div>
        </transition>
      </div>

      <!-- 事件面板 -->
      <div class="relative h-full">
        <!-- <n-button v-show="!showEventPanel" class="expand-icon" style="top: 260px;" size="tiny" text
          @click="showEventPanel = true" type="primary">
          <template #icon>
            <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
          </template>
        </n-button> -->
        <n-tooltip trigger="hover" placement="left">
          <template #trigger>
            <n-button v-show="!showEventPanel" class="expand-icon" style="top: 200px;" size="tiny" text
              @click="showEventPanel = true" type="primary">
              <template #icon>
                <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
              </template>
            </n-button>
          </template>
          展开-事件列表
        </n-tooltip>

        <transition name="slide">
          <div v-show="showEventPanel" class="w-48 h-full border-l border-gray-700 bg-black text-white flex flex-col">
            <div class="flex p-2 gap-1">
              <n-tooltip trigger="hover" placement="right">
                <template #trigger>
                  <n-button quaternary size="small" @click="showEventPanel = false"
                    style="background-color: #2a2a2a; border: 1px solid #888;width: 28px;height:28px;">
                    <template #icon>
                      <img src="@/assets/imgs/TablerChevronRightPipe.svg" class="sicon-white " />
                    </template>
                  </n-button>
                </template>
                收起-事件列表
              </n-tooltip>
              <span class="text-sm flex-1 text-white">事件列表</span>
            </div>
            <div class="flex-1 overflow-auto p-2">
              <n-table :bordered="false" :single-line="false" size="small">
                <thead>
                  <tr>
                    <th>时间</th>
                    <th>名称</th>
                    <th>类</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, idx) in eventList" :key="idx">
                    <td>{{ item.time }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.type }}</td>
                  </tr>
                </tbody>
              </n-table>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<style scoped>
.expand-icon {
  position: fixed;
  right: 0;
  z-index: 1000;
  width: 28px;
  height: 28px;
  padding: 0;
  background-color: #2a2a2a;
  border: 1px solid #888;
  border-radius: 4px;
}

.sicon-white {
  filter: brightness(0) invert(1);
  width: 16px;
  height: 16px;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

:deep(.n-tabs-tab) {
  padding: 10px 6px 5px 0px !important;
}

:deep(.n-table) {
  font-size: 10px;
}
</style>

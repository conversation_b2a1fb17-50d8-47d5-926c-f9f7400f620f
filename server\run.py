"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-04 15:11:53
LastEditors: zhai
LastEditTime: 2025-01-19 19:17:43
"""

# 使用本地 fastapi_crud_tortoise 时添加下面的代码
# import os
# import sys

# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


import multiprocessing

import uvicorn
from settings import APP_SETTINGS

# 打包的话需要加上下面的代码
from app import app


if __name__ == "__main__":
    # 解决在将FastAPI应用打包成EXE文件后，由于指定了多个workers进程而导致的无限启动问题
    multiprocessing.freeze_support()

    # import os

    # current_dir = os.getcwd()
    # print("当前工作目录：", current_dir)

    try:
        uvicorn.run(
            "app:app", host="0.0.0.0", port=APP_SETTINGS.HOST_PORT, workers=APP_SETTINGS.WORKERS, reload=False
        )
    except KeyboardInterrupt:
        ...
    except Exception as e:
        print(e)

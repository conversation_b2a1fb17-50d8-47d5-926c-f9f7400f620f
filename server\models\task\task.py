"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-05 11:20:17
LastEditors: zhai
LastEditTime: 2025-01-05 11:21:35
"""

from tortoise import Model, fields
from models.system.utils import BaseModel


# Tortoise ORM models
class SchedulerTask(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=255, null=True)
    group = fields.CharField(max_length=255, null=True)
    job_class = fields.CharField(max_length=255)
    exec_strategy = fields.CharField(max_length=255)
    expression = fields.CharField(max_length=255)
    remark = fields.Char<PERSON><PERSON>(max_length=255, null=True)
    start_date = fields.DatetimeField(null=True)
    end_date = fields.DatetimeField(null=True)
    is_active = fields.BooleanField(default=True)

    class Meta:
        table = "scheduler_task"


class SchedulerTaskRecord(Model):
    id = fields.IntField(pk=True)
    job_id = fields.Char<PERSON><PERSON>(max_length=255)
    job_class = fields.Char<PERSON>ield(max_length=255, null=True)
    name = fields.Char<PERSON>ield(max_length=255, null=True)
    group = fields.CharField(max_length=255, null=True)
    exec_strategy = fields.CharField(max_length=255, null=True)
    expression = fields.CharField(max_length=255, null=True)
    start_time = fields.DatetimeField()
    end_time = fields.DatetimeField()
    process_time = fields.FloatField()
    retval = fields.TextField(null=True)
    exception = fields.TextField(null=True)
    traceback = fields.TextField(null=True)

    class Meta:
        table = "scheduler_task_record"


__all__ = ["SchedulerTask", "SchedulerTaskRecord"]

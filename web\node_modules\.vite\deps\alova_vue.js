import {
  forEach,
  setTimeoutFn,
  trueValue
} from "./chunk-X4ESDFMD.js";
import "./chunk-7RC5I7IX.js";
import {
  computed,
  getCurrentInstance,
  onMounted,
  onUnmounted,
  ref,
  watch
} from "./chunk-TID6LRNE.js";
import "./chunk-ULBN3QDT.js";

// node_modules/.pnpm/alova@3.2.6/node_modules/alova/dist/stateshook/vue.esm.js
var vue = {
  name: "Vue",
  create: (data) => ref(data),
  dehydrate: (state) => state.value,
  update: (newVal, state) => {
    state.value = newVal;
  },
  effectRequest({ handler, removeStates, immediate, watchingStates }) {
    if (getCurrentInstance()) {
      onUnmounted(removeStates);
    }
    immediate && handler();
    forEach(watchingStates || [], (state, i) => {
      watch(state, () => {
        handler(i);
      }, { deep: trueValue });
    });
  },
  computed: (getter) => computed(getter),
  watch: (states, callback) => {
    watch(states, callback, {
      deep: trueValue
    });
  },
  onMounted: (callback) => {
    if (getCurrentInstance()) {
      onMounted(callback);
    } else {
      setTimeoutFn(callback, 10);
    }
  },
  onUnmounted: (callback) => {
    getCurrentInstance() && onUnmounted(callback);
  }
};
export {
  vue as default
};
//# sourceMappingURL=alova_vue.js.map

import {
  falseValue,
  globalToString,
  isFn,
  isNumber,
  isSpecialRequestBody,
  isString,
  newInstance,
  promiseReject,
  promiseResolve,
  trueValue,
  undefinedValue,
  usePromise
} from "./chunk-X4ESDFMD.js";
import "./chunk-ULBN3QDT.js";

// node_modules/.pnpm/@alova+mock@2.0.10_alova@3.2.6/node_modules/@alova/mock/dist/alova-mock.esm.js
var mockLabel = "mock";
var mockLabelColor = "#64bde8";
var mockLabelBg = "#ccefff";
var realRequestLabel = "Realtime";
var realRequestLabelColor = "#999999";
var realRequestLabelBg = "#ededed";
var labelStyle = (bgColor, color) => `padding: 2px 6px; background: ${bgColor}; color: ${color};`;
var titleStyle = "color: black; font-size: 12px; font-weight: bolder";
var transform2TableData = (obj) => {
  const tableData = {};
  for (const key in obj) {
    tableData[key] = { value: obj[key] };
  }
  return tableData;
};
var consoleRequestInfo = ({ isMock, url, method, headers, query, data, responseHeaders, response }) => {
  const cole = console;
  cole.groupCollapsed(`%c${isMock ? mockLabel : realRequestLabel}`, labelStyle(isMock ? mockLabelBg : realRequestLabelBg, isMock ? mockLabelColor : realRequestLabelColor), url);
  cole.log("%c[Method]", titleStyle, method.toUpperCase());
  cole.log("%c[Request Headers]", titleStyle);
  cole.table(transform2TableData(headers));
  cole.log("%c[Query String Parameters]", titleStyle);
  cole.table(transform2TableData(query));
  cole.log("%c[Request Body]", titleStyle, data || "");
  if (isMock) {
    if (Object.keys(responseHeaders).length > 0) {
      cole.log("%c[Response Headers]", titleStyle);
      cole.table(transform2TableData(responseHeaders));
    }
    cole.log("%c[Response Body]", titleStyle, response || "");
  }
  cole.groupEnd();
};
var defaultMockResponse = ({ status = 200, responseHeaders, statusText = "ok", body }) => ({
  response: new Response(isSpecialRequestBody(body) ? body : JSON.stringify(body), {
    status,
    statusText
  }),
  headers: new Headers(responseHeaders)
});
var defaultMockError = (error) => error;
var parseUrl = (url) => {
  url = /^[^/]*\/\//.test(url) ? url : `//${url}`;
  const splitedFullPath = url.split("/").slice(3);
  const query = {};
  let pathContainedParams = splitedFullPath.pop();
  let pathname = "";
  let hash = "";
  if (pathContainedParams) {
    pathContainedParams = pathContainedParams.replace(/\?[^?#]+/, (mat) => {
      mat.substring(1).split("&").forEach((paramItem) => {
        const [key, value] = paramItem.split("=");
        key && (query[key] = value);
      });
      return "";
    });
    pathContainedParams = pathContainedParams.replace(/#[^#]*/, (mat) => {
      hash = mat;
      return "";
    });
    splitedFullPath.push(pathContainedParams);
    pathname = `/${splitedFullPath.join("/")}`;
  }
  return {
    pathname,
    query,
    hash
  };
};
function MockRequest({
  // This enable is the main switch
  enable = trueValue,
  delay = 2e3,
  httpAdapter,
  mockRequestLogger = consoleRequestInfo,
  mock,
  onMockResponse = defaultMockResponse,
  onMockError = defaultMockError,
  matchMode = "pathname"
} = { mock: {} }) {
  return (elements, method) => {
    mock = enable && mock || {};
    const { url, data, type, headers: requestHeaders } = elements;
    let pathname = method.url;
    let query = method.config.params || {};
    if (matchMode === "pathname") {
      const parsedUrl = parseUrl(url);
      pathname = parsedUrl.pathname;
      query = parsedUrl.query;
    }
    const params = {};
    const pathnameSplited = pathname.split("/");
    const foundMockDataKeys = Object.keys(mock).filter((key) => {
      if (key.startsWith("-")) {
        return falseValue;
      }
      let methodType = "GET";
      key = key.replace(/^\[(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\]/i, (_, $1) => {
        methodType = $1.toUpperCase();
        return "";
      });
      if (methodType !== type.toUpperCase()) {
        return falseValue;
      }
      const keySplited = key.split("/");
      if (keySplited.length !== pathnameSplited.length) {
        return falseValue;
      }
      for (const i in keySplited) {
        const keySplitedItem = keySplited[i];
        const matchedParamKey = (keySplitedItem.match(/^\{(.*)\}$/) || ["", ""])[1];
        if (!matchedParamKey) {
          if (keySplitedItem !== pathnameSplited[i]) {
            return falseValue;
          }
        } else {
          params[matchedParamKey] = pathnameSplited[i];
        }
      }
      return trueValue;
    });
    let finalKey = foundMockDataKeys.find((key) => !/\{.*\}/.test(key));
    finalKey = finalKey || foundMockDataKeys.shift();
    const mockDataRaw = finalKey ? mock[finalKey] : undefinedValue;
    if (mockDataRaw === undefinedValue) {
      if (httpAdapter) {
        isFn(mockRequestLogger) && mockRequestLogger({
          isMock: falseValue,
          url,
          method: type,
          params,
          headers: requestHeaders,
          query,
          data: {},
          responseHeaders: {}
        });
        return httpAdapter(elements, method);
      }
      throw new Error(`cannot find the httpAdapter.
[url]${url}`);
    }
    const promiseResolver = usePromise();
    const { resolve } = promiseResolver;
    let { promise: resonpsePromise, reject } = promiseResolver;
    const timeout = method.config.timeout || 0;
    if (timeout > 0) {
      setTimeout(() => {
        reject(new Error("request timeout"));
      }, timeout);
    }
    const timer = setTimeout(() => {
      try {
        const res = isFn(mockDataRaw) ? mockDataRaw({
          query,
          params,
          data: isString(data) || !data ? {} : data,
          headers: requestHeaders
        }) : mockDataRaw;
        resolve(newInstance(Promise, (resolveInner, rejectInner) => {
          reject = rejectInner;
          promiseResolve(res).then(resolveInner).catch(rejectInner);
        }));
      } catch (error) {
        reject(error);
      }
    }, delay);
    resonpsePromise = resonpsePromise.then((response) => {
      let status = 200;
      let statusText = "ok";
      let responseHeaders = {};
      let body = undefinedValue;
      if (response === undefinedValue) {
        status = 404;
        statusText = "api not found";
      } else if (response && isNumber(response.status) && isString(response.statusText)) {
        status = response.status;
        statusText = response.statusText;
        responseHeaders = response.responseHeaders || responseHeaders;
        body = response.body;
      } else {
        body = response;
      }
      return newInstance(Promise, (resolve2, reject2) => {
        try {
          const res = onMockResponse({ status, statusText, responseHeaders, body }, {
            headers: requestHeaders,
            query,
            params,
            data: data || {}
          }, method);
          resolve2(res);
        } catch (error) {
          reject2(error);
        }
      }).then((response2) => {
        isFn(mockRequestLogger) && mockRequestLogger({
          isMock: trueValue,
          url,
          method: type,
          params,
          headers: requestHeaders,
          query,
          data: data || {},
          responseHeaders,
          response: body
        });
        return response2;
      });
    }).catch((error) => promiseReject(onMockError(error, method)));
    return {
      response: () => resonpsePromise.then(({ response }) => response && globalToString(response) === "[object Response]" ? response.clone() : response),
      headers: () => resonpsePromise.then(({ headers }) => headers),
      abort: () => {
        clearTimeout(timer);
        reject(new Error("The user abort request"));
      }
    };
  };
}
function createAlovaMockAdapter(mockWrapper, options = { enable: true }) {
  let uniqueMockMap = {};
  mockWrapper.filter(({ enable }) => enable).forEach(({ data }) => {
    uniqueMockMap = {
      ...data,
      ...uniqueMockMap
    };
  });
  return MockRequest({
    ...options,
    mock: uniqueMockMap
  });
}
var defineMock = (mock, enable = true) => ({
  enable,
  data: mock
});
export {
  createAlovaMockAdapter,
  defineMock
};
//# sourceMappingURL=@alova_mock.js.map

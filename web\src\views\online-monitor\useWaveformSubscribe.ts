// 文件建议：src/hooks/useWaveformSubscribe.ts
// 各图表的数据结构与订阅函数
import { ref, onUnmounted } from 'vue'
import { getMQTTClient } from '@/utils/mqtt'
import { throttle } from '@/utils/throttle'

export function useWaveformSubscribe(topic: string, maxLength = 1000) {
  const xData = ref<number[]>([])
  const yData = ref<number[]>([])

  const updateChart = throttle((payload: string) => {
    const parsed = JSON.parse(payload)
    const time = parsed.time || Date.now()
    const value = parsed.value

    xData.value.push(time)
    yData.value.push(value)

    if (xData.value.length > maxLength) {
      xData.value.shift()
      yData.value.shift()
    }
  }, 100) // 控制频率，避免图表卡顿

  const client = getMQTTClient()
  client.subscribe(topic)
  client.on('message', (receivedTopic, message) => {
    if (receivedTopic === topic) updateChart(message.toString())
  })

  onUnmounted(() => {
    client.unsubscribe(topic)
  })

  return { xData, yData }
}

// 在组件中的使用
const { xData, yData } = useWaveformSubscribe('sensor/torsion/speed')

// 如果需要节流函数，避免推送太频繁
// 文件建议：src/utils/throttle.ts
export function throttle<T extends (...args: any[]) => void>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let last = 0
  return function (...args: Parameters<T>) {
    const now = Date.now()
    if (now - last > delay) {
      last = now
      fn(...args)
    }
  }
}

// 页面挂载时候
onMounted(() => {
  connectMQTT() // 确保连接
})

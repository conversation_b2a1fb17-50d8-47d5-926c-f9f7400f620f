from typing import List
from fastapi import APIRouter, File, HTTPException, Query, UploadFile
from fastapi.responses import FileResponse
from fastapi_crud_tortoise import (
    CRUD_FILE_ROOT,
    crud_original_filename,
    crud_savefile,
    resp_fail,
    resp_success,
)
from urllib.parse import unquote
from urllib.parse import quote


# 文件下载
router = APIRouter()


@router.post("/uploadfile", summary="上传文件")
async def uploadfile(file: UploadFile = File(...)):
    urls = []
    try:
        stored_filename = crud_savefile(file)
        urls.append(stored_filename)
    except Exception as e:
        return resp_fail(msg=f"Failed to upload {file.filename}, error: {str(e)}")
    finally:
        file.file.close()

    return resp_success(data=urls)


@router.post("/uploadfiles", summary="上传文件列表")
async def uploadfiles(files: List[UploadFile] = File(...)):
    urls = []
    for file in files:
        try:
            stored_filename = crud_savefile(file)
            urls.append(stored_filename)
        except Exception as e:
            return resp_fail(msg=f"Failed to upload {file.filename}, error: {str(e)}")
        finally:
            file.file.close()

    return resp_success(data=urls)


@router.get("/download/{file_path}")
def download(file_path: str):
    # URL 解码
    # file_path = unquote(file_path)
    fpath = CRUD_FILE_ROOT / file_path

    # 检查文件是否存在
    if not fpath.exists():
        raise HTTPException(status_code=404, detail="File not found")

    filename = crud_original_filename(file_path)

    # URL 编码文件名（避免中文字符导致的编码问题）
    encoded_filename = quote(filename)

    headers = {"Content-Disposition": f'attachment; filename="{encoded_filename}"'}
    return FileResponse(fpath, headers=headers)

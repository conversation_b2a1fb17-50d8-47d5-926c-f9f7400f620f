'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-04-23 22:23:09
LastEditors: zhai
LastEditTime: 2025-05-01 11:12:28
'''
"""
Descripttion:
version: 0.x
Author: zhai
Date: 2024-05-25 09:03:01
LastEditors: zhai
LastEditTime: 2025-01-18 10:16:53
"""

from datetime import datetime
from tortoise import Model, fields
from fastapi_crud_tortoise import CrudModel


class TerminalModel(CrudModel):
    """记录表"""

    name = fields.CharField(max_length=255, null=False)
    serial_code = fields.TextField(null=True, comment="序列号")
    description = fields.CharField(max_length=256, null=True, comment="描述")
    client_id = fields.CharField(max_length=255, null=True)
    status = fields.BooleanField(default=True, comment="状态 true 启用 false 禁用")
    user = fields.ForeignKeyField("app_system.User", related_name="terminals", null=True)

    class Meta:
        table = "daq_terminal"


class RecordModel(CrudModel):
    name = fields.CharField(max_length=255, null=False)
    serial_code = fields.TextField(max_length=255, null=True, comment="序列号")
    device_type = fields.CharField(max_length=255, null=True)
    description = fields.CharField(max_length=255, null=True)
    record_time = fields.DatetimeField(default=datetime.now, comment="记录时间")
    record_duration = fields.FloatField()
    config = fields.JSONField(null=True, default=None)
    sync_status = fields.BooleanField(default=False)

    class Meta:
        table = "daq_record"

class SeriesModel(CrudModel):
    record = fields.ForeignKeyField("app_system.RecordModel", related_name="series")
    name = fields.CharField(max_length=255, null=False)
    channel = fields.IntField(verbose_name="通道号")
    type = fields.CharField(max_length=255, verbose_name="类型")
    unit = fields.CharField(max_length=255, verbose_name="单位")
    metadata = fields.JSONField(null=True, default=None)

    class Meta:
        table = "daq_series"

class DataPackModel(CrudModel):
    series = fields.ForeignKeyField("app_system.SeriesModel", related_name="data_pack")
    record_time = fields.DatetimeField(default=datetime.now, comment="记录时间")
    rec_index = fields.IntField(null=True, default=None, comment="记录索引")
    rec_count = fields.IntField(null=True, default=None, comment="记录长度")
    actual_timestamp = fields.FloatField(null=True, default=None, comment="采集到数据时时间戳，单位s，基准0")
    theory_timestamp_s = fields.FloatField(null=True, default=None, comment="理论开始时间，单位秒，相对开始时间")
    theory_timestamp_e = fields.FloatField(null=True, default=None, comment="理论结束时间，单位秒，相对开始时间")
    clock_offset = fields.FloatField(null=True, default=None, comment="时间偏移")
    pack_data = fields.BinaryField(null=True, default=None)

    class Meta:
        table = "daq_data_pack"


__all__ = ["TerminalModel", "RecordModel", "SeriesModel", "DataPackModel"]

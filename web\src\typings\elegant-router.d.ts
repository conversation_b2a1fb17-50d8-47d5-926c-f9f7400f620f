/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

declare module "@elegant-router/types" {
  type ElegantConstRoute = import('@elegant-router/vue').ElegantConstRoute;

  /**
   * route layout
   */
  export type RouteLayout = "base" | "blank";

  /**
   * route map
   */
  export type RouteMap = {
    "root": "/";
    "not-found": "/:pathMatch(.*)*";
    "exception": "/exception";
    "exception_403": "/exception/403";
    "exception_404": "/exception/404";
    "exception_500": "/exception/500";
    "document": "/document";
    "document_project": "/document/project";
    "document_project-link": "/document/project-link";
    "document_vue": "/document/vue";
    "document_vite": "/document/vite";
    "document_unocss": "/document/unocss";
    "document_naive": "/document/naive";
    "document_antd": "/document/antd";
    "document_alova": "/document/alova";
    "403": "/403";
    "404": "/404";
    "500": "/500";
    "about": "/about";
    "alova": "/alova";
    "alova_request": "/alova/request";
    "alova_scenes": "/alova/scenes";
    "alova_user": "/alova/user";
    "demo-route": "/demo-route";
    "demo-route_child": "/demo-route/child";
    "device": "/device";
    "device_blocks": "/device/blocks";
    "device_flow": "/device/flow";
    "device_record": "/device/record";
    "device_remote": "/device/remote";
    "device_review": "/device/review";
    "device_terminals": "/device/terminals";
    "dummy": "/dummy";
    "dummy_department": "/dummy/department";
    "dummy_dummy": "/dummy/dummy";
    "function": "/function";
    "function_hide-child": "/function/hide-child";
    "function_hide-child_one": "/function/hide-child/one";
    "function_hide-child_three": "/function/hide-child/three";
    "function_hide-child_two": "/function/hide-child/two";
    "function_multi-tab": "/function/multi-tab";
    "function_request": "/function/request";
    "function_super-page": "/function/super-page";
    "function_tab": "/function/tab";
    "function_toggle-auth": "/function/toggle-auth";
    "history-data": "/history-data";
    "history-data_electrical": "/history-data/electrical";
    "history-data_steam": "/history-data/steam";
    "history-data_torsion": "/history-data/torsion";
    "home": "/home";
    "iframe-page": "/iframe-page/:url";
    "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?";
    "manage": "/manage";
    "manage_api": "/manage/api";
    "manage_log": "/manage/log";
    "manage_menu": "/manage/menu";
    "manage_role": "/manage/role";
    "manage_user": "/manage/user";
    "manage_user-detail": "/manage/user-detail/:id";
    "multi-menu": "/multi-menu";
    "multi-menu_first": "/multi-menu/first";
    "multi-menu_first_child": "/multi-menu/first/child";
    "multi-menu_second": "/multi-menu/second";
    "multi-menu_second_child": "/multi-menu/second/child";
    "multi-menu_second_child_home": "/multi-menu/second/child/home";
    "online-monitor": "/online-monitor";
    "online-monitor_electrical": "/online-monitor/electrical";
    "online-monitor_main": "/online-monitor/main";
    "online-monitor_steam": "/online-monitor/steam";
    "online-monitor_torsion": "/online-monitor/torsion";
    "plugin": "/plugin";
    "plugin_barcode": "/plugin/barcode";
    "plugin_charts": "/plugin/charts";
    "plugin_charts_antv": "/plugin/charts/antv";
    "plugin_charts_echarts": "/plugin/charts/echarts";
    "plugin_charts_vchart": "/plugin/charts/vchart";
    "plugin_copy": "/plugin/copy";
    "plugin_editor": "/plugin/editor";
    "plugin_editor_markdown": "/plugin/editor/markdown";
    "plugin_editor_quill": "/plugin/editor/quill";
    "plugin_excel": "/plugin/excel";
    "plugin_gantt": "/plugin/gantt";
    "plugin_gantt_dhtmlx": "/plugin/gantt/dhtmlx";
    "plugin_gantt_vtable": "/plugin/gantt/vtable";
    "plugin_icon": "/plugin/icon";
    "plugin_map": "/plugin/map";
    "plugin_pdf": "/plugin/pdf";
    "plugin_pinyin": "/plugin/pinyin";
    "plugin_print": "/plugin/print";
    "plugin_swiper": "/plugin/swiper";
    "plugin_tables": "/plugin/tables";
    "plugin_tables_vtable": "/plugin/tables/vtable";
    "plugin_typeit": "/plugin/typeit";
    "plugin_video": "/plugin/video";
    "task": "/task";
    "task_log": "/task/log";
    "task_scheduler": "/task/scheduler";
    "user-center": "/user-center";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */
  export type CustomRouteKey = Extract<
    RouteKey,
    | "root"
    | "not-found"
    | "exception"
    | "exception_403"
    | "exception_404"
    | "exception_500"
    | "document"
    | "document_project"
    | "document_project-link"
    | "document_vue"
    | "document_vite"
    | "document_unocss"
    | "document_naive"
    | "document_antd"
    | "document_alova"
  >;

  /**
   * the generated route key
   */
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "about"
    | "alova"
    | "demo-route"
    | "device"
    | "dummy"
    | "function"
    | "history-data"
    | "home"
    | "iframe-page"
    | "login"
    | "manage"
    | "multi-menu"
    | "online-monitor"
    | "plugin"
    | "task"
    | "user-center"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception"
    | "document"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "iframe-page"
    | "login"
    | "about"
    | "alova_request"
    | "alova_scenes"
    | "alova_user"
    | "demo-route_child"
    | "device_blocks"
    | "device_flow"
    | "device_record"
    | "device_remote"
    | "device_review"
    | "device_terminals"
    | "dummy_department"
    | "dummy_dummy"
    | "function_hide-child_one"
    | "function_hide-child_three"
    | "function_hide-child_two"
    | "function_multi-tab"
    | "function_request"
    | "function_super-page"
    | "function_tab"
    | "function_toggle-auth"
    | "history-data_electrical"
    | "history-data"
    | "history-data_steam"
    | "history-data_torsion"
    | "home"
    | "manage_api"
    | "manage_log"
    | "manage_menu"
    | "manage_role"
    | "manage_user-detail"
    | "manage_user"
    | "multi-menu_first_child"
    | "multi-menu_second_child_home"
    | "online-monitor_electrical"
    | "online-monitor_main"
    | "online-monitor_steam"
    | "online-monitor_torsion"
    | "plugin_barcode"
    | "plugin_charts_antv"
    | "plugin_charts_echarts"
    | "plugin_charts_vchart"
    | "plugin_copy"
    | "plugin_editor_markdown"
    | "plugin_editor_quill"
    | "plugin_excel"
    | "plugin_gantt_dhtmlx"
    | "plugin_gantt_vtable"
    | "plugin_icon"
    | "plugin_map"
    | "plugin_pdf"
    | "plugin_pinyin"
    | "plugin_print"
    | "plugin_swiper"
    | "plugin_tables_vtable"
    | "plugin_typeit"
    | "plugin_video"
    | "task_log"
    | "task_scheduler"
    | "user-center"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
    | "exception_403"
    | "exception_404"
    | "exception_500"
    | "document_project"
    | "document_project-link"
    | "document_vue"
    | "document_vite"
    | "document_unocss"
    | "document_naive"
    | "document_antd"
    | "document_alova"
  >;

  /**
   * the single level route key
   */
  export type SingleLevelRouteKey = FirstLevelRouteKey & LastLevelRouteKey;

  /**
   * the custom single level route key
   */
  export type CustomSingleLevelRouteKey = CustomFirstLevelRouteKey & CustomLastLevelRouteKey;

  /**
   * the first level route key, but not the single level
  */
  export type FirstLevelRouteNotSingleKey = Exclude<FirstLevelRouteKey, SingleLevelRouteKey>;

  /**
   * the custom first level route key, but not the single level
   */
  export type CustomFirstLevelRouteNotSingleKey = Exclude<CustomFirstLevelRouteKey, CustomSingleLevelRouteKey>;

  /**
   * the center level route key
   */
  export type CenterLevelRouteKey = Exclude<GeneratedRouteKey, FirstLevelRouteKey | LastLevelRouteKey>;

  /**
   * the custom center level route key
   */
  export type CustomCenterLevelRouteKey = Exclude<CustomRouteKey, CustomFirstLevelRouteKey | CustomLastLevelRouteKey>;

  /**
   * the center level route key
   */
  type GetChildRouteKey<K extends RouteKey, T extends RouteKey = RouteKey> = T extends `${K}_${infer R}`
    ? R extends `${string}_${string}`
      ? never
      : T
    : never;

  /**
   * the single level route
   */
  type SingleLevelRoute<K extends SingleLevelRouteKey = SingleLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}$view.${K}`;
      }
    : never;

  /**
   * the last level route
   */
  type LastLevelRoute<K extends GeneratedRouteKey> = K extends LastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `view.${K}`;
      }
    : never;
  
  /**
   * the center level route
   */
  type CenterLevelRoute<K extends GeneratedRouteKey> = K extends CenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the multi level route
   */
  type MultiLevelRoute<K extends FirstLevelRouteNotSingleKey = FirstLevelRouteNotSingleKey> = K extends string
    ? ElegantConstRoute & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}`;
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;
  
  /**
   * the custom first level route
   */
  type CustomSingleLevelRoute<K extends CustomFirstLevelRouteKey = CustomFirstLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `layout.${RouteLayout}$view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom last level route
   */
  type CustomLastLevelRoute<K extends CustomRouteKey> = K extends CustomLastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom center level route
   */
  type CustomCenterLevelRoute<K extends CustomRouteKey> = K extends CustomCenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the custom multi level route
   */
  type CustomMultiLevelRoute<K extends CustomFirstLevelRouteNotSingleKey = CustomFirstLevelRouteNotSingleKey> =
    K extends string
      ? ElegantConstRoute & {
          name: K;
          path: RouteMap[K];
          component: `layout.${RouteLayout}`;
          children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
        }
      : never;

  /**
   * the custom route
   */
  type CustomRoute = CustomSingleLevelRoute | CustomMultiLevelRoute;

  /**
   * the generated route
   */
  type GeneratedRoute = SingleLevelRoute | MultiLevelRoute;

  /**
   * the elegant route
   */
  type ElegantRoute = GeneratedRoute | CustomRoute;
}

import json
from typing import cast
from fastapi import Depends, Query, Request
from tortoise.expressions import F

from api.v1.utils import insert_log
from models.system.utils import LogDetailType, LogType
from schemas.users import UserCreate, UserUpdate
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
    insert_operation,
    OperationType,
    PAGINATION,
    apply_user_tenant_filters,
    UserDataFilter,
)
from .schema import FaultLogCreateDTO, FaultLogDTO, FaultLogQueryDTO, FaultLogDetailDTO
from models.fishery import FaultLogModel, DeviceModel, OrganizationModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=FaultLogDTO,
    create_schema=FaultLogCreateDTO,
    db_model=FaultLogModel,
    prefix="fault",
    tags=["故障记录"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.SELF_DEFAULT,
    tenant_data_option=TenantDataOption.TENANT_DEFAULT,
)


@router.post("/query_detail", response_model=RespModelT[list[FaultLogDetailDTO]])
async def get_fault_logs(
    request: Request,
    query: FaultLogQueryDTO,
    pagination: PAGINATION = router.pagination,
    sort_by: str = Query(None, description="Sort records by this field"),
    user_data_filter: router.user_data_filter_type = router.user_data_filter_defv,
    tenant_data_filter: router.tenant_data_filter_type = router.tenant_data_filter_defv,
):
    """获取故障记录列表（带设备信息）"""
    try:
        # 构建基础查询
        query_stmt = FaultLogModel.all().select_related(
            "device", "device__organization", "device__organization__parent"
        )

        query_stmt = apply_user_tenant_filters(query_stmt, request, None, tenant_data_filter)

        if user_data_filter == UserDataFilter.SELF_DATA:
            query_stmt = query_stmt.filter(device__binder_id=getattr(request.state, "user_id", None))

        if sort_by:
            query_stmt = query_stmt.order_by(sort_by)
        else:
            query_stmt = query_stmt.order_by("-fault_time")

        # 添加查询条件
        if query.device_name:
            query_stmt = query_stmt.filter(device__name__icontains=query.device_name)
        if query.name:
            query_stmt = query_stmt.filter(name__icontains=query.name)
        if query.status is not None:
            query_stmt = query_stmt.filter(status=query.status)
        if query.fault_time_start:
            query_stmt = query_stmt.filter(fault_time__gte=query.fault_time_start)
        if query.fault_time_end:
            query_stmt = query_stmt.filter(fault_time__lte=query.fault_time_end)
        if query.resolved_time_start:
            query_stmt = query_stmt.filter(resolved_time__gte=query.resolved_time_start)
        if query.resolved_time_end:
            query_stmt = query_stmt.filter(resolved_time__lte=query.resolved_time_end)
        if query.type:
            query_stmt = query_stmt.filter(device__type=query.type)
        if query.model:
            query_stmt = query_stmt.filter(device__model=query.model)
        if query.version:
            query_stmt = query_stmt.filter(device__version=query.version)

        # 处理fishery和workshop查询条件
        # if query.fishery:
        #     # 查询所有匹配的第一层组织
        #     fishery_orgs = await OrganizationModel.filter(name__icontains=query.fishery, level=0).all()
        #     if fishery_orgs:
        #         # 获取所有子组织
        #         org_ids = []
        #         for org in fishery_orgs:
        #             sub_orgs = await OrganizationModel.filter(parent_id=org.id).all()
        #             org_ids.extend([sub_org.id for sub_org in sub_orgs])
        #         query_stmt = query_stmt.filter(device__organization_id__in=org_ids)

        # if query.workshop:
        #     # 查询所有匹配的第二层组织
        #     workshop_orgs = await OrganizationModel.filter(name__icontains=query.workshop, level=1).all()
        #     if workshop_orgs:
        #         org_ids = [org.id for org in workshop_orgs]
        #         query_stmt = query_stmt.filter(device__organization_id__in=org_ids)

        if query.organization_ids:
            query_stmt = query_stmt.filter(device__organization_id__in=query.organization_ids)

        # 分页查询
        skip, limit = pagination.get("skip"), pagination.get("limit")
        total = await query_stmt.count()

        if skip:
            query_stmt = query_stmt.offset(cast(int, skip))

        if limit:
            query_stmt = query_stmt.limit(limit)

        fault_logs = await query_stmt

        # 转换为DTO
        fault_log_dtos = []
        for log in fault_logs:
            dto = convert_to_pydantic(log, FaultLogDetailDTO, relationships=False)
            dto["device_name"] = log.device.name
            # 设置workshop为设备关联的organization的名字
            if log.device.organization:
                dto["workshop"] = log.device.organization.name
            # 设置fishery为organization父节点的名字
            if log.device.organization and log.device.organization.parent:
                dto["fishery"] = log.device.organization.parent.name
            # 设置设备相关字段
            if log.device:
                dto["type"] = log.device.type
                dto["model"] = log.device.model
                # 使用记录的版本号
                # dto['version'] = log.device.version
            fault_log_dtos.append(dto)

        current = None
        size = None
        if skip and limit:
            size = limit
            current = skip // limit + 1

        return resp_success(
            fault_log_dtos,
            total,
            current,
            size,
        )

    except Exception as e:
        return resp_fail(f"查询失败: {str(e)}")

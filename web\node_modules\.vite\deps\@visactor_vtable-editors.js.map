{"version": 3, "sources": ["../../.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/src/input-editor.ts", "../../.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/src/date-input-editor.ts", "../../.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/src/list-editor.ts", "../../.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/src/textArea-editor.ts", "../../.pnpm/@visactor+vtable-editors@1.13.1/node_modules/@visactor/vtable-editors/src/types.ts"], "sourcesContent": ["import type { EditContext, IEditor, RectProps } from './types';\n\nexport interface InputEditorConfig {\n  readonly?: boolean;\n}\n\nexport class InputEditor implements IEditor {\n  editorType: string = 'Input';\n  editorConfig: InputEditorConfig;\n  container: HTMLElement;\n  successCallback?: () => void;\n  element?: HTMLInputElement;\n\n  constructor(editorConfig?: InputEditorConfig) {\n    this.editorConfig = editorConfig;\n  }\n\n  createElement() {\n    const input = document.createElement('input');\n    input.setAttribute('type', 'text');\n\n    if (this.editorConfig?.readonly) {\n      input.setAttribute('readonly', `${this.editorConfig.readonly}`);\n    }\n\n    input.style.position = 'absolute';\n    input.style.padding = '4px';\n    input.style.width = '100%';\n    input.style.boxSizing = 'border-box';\n    input.style.backgroundColor = '#FFFFFF';\n    this.element = input;\n    this.container.appendChild(input);\n\n    // 监听键盘事件\n    input.addEventListener('keydown', (e: KeyboardEvent) => {\n      if (e.key === 'a' && (e.ctrlKey || e.metaKey)) {\n        // 阻止冒泡  防止处理成表格全选事件\n        e.stopPropagation();\n      }\n    });\n  }\n\n  setValue(value: string) {\n    this.element.value = typeof value !== 'undefined' ? value : '';\n  }\n\n  getValue() {\n    return this.element.value;\n  }\n\n  onStart({ value, referencePosition, container, endEdit }: EditContext<string>) {\n    this.container = container;\n    this.successCallback = endEdit;\n    if (!this.element) {\n      this.createElement();\n\n      if (value !== undefined && value !== null) {\n        this.setValue(value);\n      }\n      if (referencePosition?.rect) {\n        this.adjustPosition(referencePosition.rect);\n      }\n    }\n    this.element.focus();\n    // do nothing\n  }\n\n  adjustPosition(rect: RectProps) {\n    this.element.style.top = rect.top + 'px';\n    this.element.style.left = rect.left + 'px';\n    this.element.style.width = rect.width + 'px';\n    this.element.style.height = rect.height + 'px';\n  }\n\n  endEditing() {\n    // do nothing\n  }\n\n  onEnd() {\n    // do nothing\n    if (this.container?.contains(this.element)) {\n      this.container.removeChild(this.element);\n    }\n    this.element = undefined;\n  }\n\n  isEditorElement(target: HTMLElement) {\n    return target === this.element;\n  }\n}\n", "import type { InputEditorConfig } from './input-editor';\nimport { InputEditor } from './input-editor';\nimport type { IEditor } from './types';\nexport class DateInputEditor extends InputEditor implements IEditor {\n  editorType: string = 'DateInput';\n  constructor(editorConfig?: InputEditorConfig) {\n    super(editorConfig);\n    this.editorConfig = editorConfig;\n  }\n  createElement() {\n    const input = document.createElement('input');\n\n    input.setAttribute('type', 'date');\n\n    input.style.padding = '4px';\n    input.style.width = '100%';\n    input.style.boxSizing = 'border-box';\n    input.style.position = 'absolute';\n    input.style.backgroundColor = '#FFFFFF';\n\n    this.element = input;\n    this.container.appendChild(input);\n    // 测试successCallback 调用是否正确\n    // input.ondblclick = () => {\n    //   debugger;\n    //   this.successCallback();\n    // };\n\n    // 监听键盘事件\n    input.addEventListener('keydown', (e: KeyboardEvent) => {\n      if (e.key === 'a' && (e.ctrlKey || e.metaKey)) {\n        // 阻止冒泡  防止处理成表格全选事件\n        e.stopPropagation();\n      }\n    });\n  }\n}\n", "import type { EditContext, IEditor, Placement, RectProps } from './types';\nexport interface ListEditorConfig {\n  values: string[];\n}\n\nexport class ListEditor implements IEditor {\n  editorType: string = 'Input';\n  input?: HTMLInputElement;\n  editorConfig?: ListEditorConfig;\n  container?: HTMLElement;\n  element?: HTMLSelectElement;\n  successCallback?: () => void;\n\n  constructor(editorConfig: ListEditorConfig) {\n    console.log('listEditor constructor');\n    this.editorConfig = editorConfig;\n  }\n\n  createElement(value: string) {\n    // create select tag\n    const select = document.createElement('select');\n    select.setAttribute('type', 'text');\n    select.style.position = 'absolute';\n    select.style.padding = '4px';\n    select.style.width = '100%';\n    select.style.boxSizing = 'border-box';\n    select.style.backgroundColor = '#FFFFFF';\n    this.element = select;\n\n    // create option tags\n    const { values } = this.editorConfig;\n    let opsStr = '';\n    values.forEach(item => {\n      opsStr +=\n        item === value\n          ? `<option value=\"${item}\" selected>${item}</option>`\n          : `<option value=\"${item}\" >${item}</option>`;\n    });\n    select.innerHTML = opsStr;\n\n    this.container.appendChild(select);\n    // this._bindSelectChangeEvent();\n  }\n\n  _bindSelectChangeEvent() {\n    this.element.addEventListener('change', () => {\n      // this.successCallback();\n    });\n  }\n\n  setValue(value: string) {\n    // do nothing\n  }\n\n  getValue() {\n    return this.element.value;\n  }\n\n  onStart({ container, value, referencePosition, endEdit }: EditContext) {\n    this.container = container;\n    this.successCallback = endEdit;\n\n    this.createElement(value);\n\n    if (value !== undefined && value !== null) {\n      this.setValue(value);\n    }\n    if (referencePosition?.rect) {\n      this.adjustPosition(referencePosition.rect);\n    }\n    this.element.focus();\n  }\n\n  adjustPosition(rect: RectProps) {\n    this.element.style.top = rect.top + 'px';\n    this.element.style.left = rect.left + 'px';\n    this.element.style.width = rect.width + 'px';\n    this.element.style.height = rect.height + 'px';\n  }\n\n  endEditing() {\n    // do nothing\n  }\n\n  onEnd() {\n    this.container.removeChild(this.element);\n  }\n\n  isEditorElement(target: HTMLElement) {\n    return target === this.element;\n  }\n}\n", "import type { EditContext, IEditor, RectProps } from './types';\n\nexport interface TextAreaEditorConfig {\n  readonly?: boolean;\n}\n\nexport class TextAreaEditor implements IEditor {\n  editorType: string = 'TextArea';\n  editorConfig: TextAreaEditorConfig;\n  container: HTMLElement;\n  successCallback?: () => void;\n  element?: HTMLTextAreaElement;\n\n  constructor(editorConfig?: TextAreaEditorConfig) {\n    this.editorConfig = editorConfig || {};\n  }\n\n  createElement() {\n    const input = document.createElement('textArea') as HTMLTextAreaElement;\n    if (this.editorConfig?.readonly) {\n      input.setAttribute('readonly', `${this.editorConfig.readonly}`);\n    }\n    input.style.resize = 'none';\n    input.style.position = 'absolute';\n    input.style.padding = '4px';\n    input.style.width = '100%';\n    input.style.height = '100%';\n    input.style.boxSizing = 'border-box';\n    input.style.backgroundColor = '#FFFFFF';\n\n    this.element = input;\n\n    this.container.appendChild(input);\n\n    // 监听键盘事件\n    input.addEventListener('keydown', (e: KeyboardEvent) => {\n      const _isSelectAll = e.key === 'a' && (e.ctrlKey || e.metaKey);\n      const _isTextAreaNewLine = e.key === 'Enter' && e.shiftKey;\n      if (_isSelectAll || _isTextAreaNewLine) {\n        // 阻止冒泡  防止处理成表格全选事件\n        e.stopPropagation();\n      }\n    });\n  }\n\n  setValue(value: string) {\n    this.element.value = typeof value !== 'undefined' ? value : '';\n  }\n\n  getValue() {\n    return this.element.value;\n  }\n\n  onStart({ value, referencePosition, container, endEdit }: EditContext<string>) {\n    this.container = container;\n    this.successCallback = endEdit;\n    if (!this.element) {\n      this.createElement();\n\n      if (value !== undefined && value !== null) {\n        this.setValue(value);\n      }\n      if (referencePosition?.rect) {\n        this.adjustPosition(referencePosition.rect);\n      }\n    }\n    this.element.focus();\n    // do nothing\n  }\n\n  adjustPosition(rect: RectProps) {\n    this.element.style.top = rect.top + 'px';\n    this.element.style.left = rect.left + 'px';\n    this.element.style.width = rect.width + 'px';\n    this.element.style.height = rect.height + 'px';\n  }\n\n  endEditing() {\n    // do nothing\n  }\n\n  onEnd() {\n    // do nothing\n    this.container.removeChild(this.element);\n    this.element = undefined;\n  }\n\n  isEditorElement(target: HTMLElement) {\n    return target === this.element;\n  }\n}\n", "// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport interface IEditor<V = any> {\n  /**\n   * Called when cell enters edit mode.\n   *\n   * Warning will be thrown if you don't provide this function\n   * after removal of `beginEditing`.\n   */\n  onStart: (context: EditContext<V>) => void;\n  /**\n   * called when cell exits edit mode.\n   *\n   * Warning will be thrown if you don't provide this function\n   * after removal of `exit`.\n   */\n  onEnd: () => void;\n  /**\n   * Called when user click somewhere while editor is in edit mode.\n   *\n   * If returns falsy, VTable will exit edit mode.\n   *\n   * If returns truthy or not defined, nothing will happen.\n   * Which means, in this scenario, you need to call `endEdit` manually\n   * to end edit mode.\n   */\n  isEditorElement?: (target: HTMLElement) => boolean;\n  /**\n   * Before set new value to table, use it to validate value.\n   * If the interface returns true, the value takes effect; otherwise, it does not take effect.\n   * @param newValue new value to be set. If not provided, the current input element value will be used.\n   * @param oldValue old value of the cell.\n   */\n  validateValue?: (newValue?: V, oldValue?: V) => boolean | Promise<boolean>;\n  /**\n   * Called when editor mode is exited by any means.\n   * Expected to return the current value of the cell.\n   */\n  getValue: () => V;\n  /**\n   * Called when cell enter edit mode.\n   * @deprecated use `onStart` instead.\n   */\n  beginEditing?: (container: HTMLElement, referencePosition: ReferencePosition, value: V) => void;\n  /**\n   * @see onEnd\n   * @deprecated use `onEnd` instead.\n   */\n  exit?: () => void;\n  /**\n   * @see isEditorElement\n   * @deprecated use `isEditorElement` instead.\n   */\n  targetIsOnEditor?: (target: HTMLElement) => boolean;\n  /**\n   * Called when cell enters edit mode with a callback function\n   * that can be used to end edit mode.\n   * @see EditContext#endEdit\n   * @deprecated callback is provided as `endEdit` in `EditContext`, use `onStart` instead.\n   */\n  bindSuccessCallback?: (callback: () => void) => void;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport interface EditContext<V = any> {\n  /** Container element of the VTable instance. */\n  container: HTMLElement;\n  /** Position info of the cell that is being edited. */\n  referencePosition: ReferencePosition;\n  /** Cell value before editing. */\n  value: V;\n  /**\n   * Callback function that can be used to end edit mode.\n   *\n   * In most cases you don't need to call this function,\n   * since Enter key click is handled by VTable automatically,\n   * and mouse click can be handled by `isEditorElement`.\n   *\n   * However, if your editor has its own complete button,\n   * or you have external elements like Tooltip,\n   * you may want to use this callback to help you\n   * end edit mode.\n   */\n  endEdit: () => void;\n  col: number;\n  row: number;\n}\n\nexport interface RectProps {\n  left: number;\n  top: number;\n  width: number;\n  height: number;\n}\n\nexport enum Placement {\n  top = 'top',\n  bottom = 'bottom',\n  left = 'left',\n  right = 'right'\n}\n\nexport interface ReferencePosition {\n  rect: RectProps;\n  placement?: Placement;\n}\n"], "mappings": ";;;AAMM,IAAO,cAAP,MAAkB;EAOtB,YAAY,cAAgC;AAN5C,SAAA,aAAqB,SAAQ,KAAA,eAAA;;EAQ7B,gBAAC;AAED,QAAA;;AACE,UAAM,aAAQ,QAAS,MAAA,IAAc,UAAS,KAAA,KAAA,iBAAA,WAAA,KAAA,SAAA,GAAA,aAAA,MAAA,aAAA,YAAA,GAAA,KAAA,aAAA,QAAA,EAAA,GAC9C,MAAM,MAAA,WAAa,YAAgB,MAAA,MAAA,UAAA,OAAA,MAAA,MAAA,QAAA,QAEnC,MAAI,MAAA,YAAK,cAAY,MAAA,MAAA,kBAAA,WAAY,KAAA,UAAA,YAC/B,UAAM,YAAa,KAAA,GAAY,MAAG,iBAAkB,WAAY,OAAA;AACjE,cAAA,EAAA,QAAA,EAAA,WAAA,EAAA,YAAA,EAAA,gBAAA;IAED,CAAA;;WAEK,OAAO;AACZ,SAAK,QAAO,QAAS,WAAG,QAAa,QAAA;;aAEhC;AACL,WAAK,KAAA,QAAU;;UAIb,EAAA,OAAc,mBAAiC,WAAA,QAAA,GAAA;qBAE3C,WAAkB,KAAA,kBAAA,SAAA,KAAA,YAAA,KAAA,cAAA,WACrB,SAAA,KAAA,SAAA,KAAA,IAAA,QAAA,oBAAA,SAAA,kBAAA,SAAA,KAAA,eAAA,kBAAA,IAAA,IACH,KAAG,QAAA,MAAA;EACL;EAEA,eAAsB,MAAA;AACpB,SAAK,QAAQ,MAAK,MAAG,KAAO,MAAK,MAAK,KAAA,QAAc,MAAQ,OAAG,KAAA,OAAA,MAChE,KAAA,QAAA,MAAA,QAAA,KAAA,QAAA,MAAA,KAAA,QAAA,MAAA,SAAA,KAAA,SAAA;EAED;eACS;EAAA;EACT,QAAC;AAED,QAAQ;AACN,KAAA,UAAK,KAAS,KAAG,cAAU,WAAA,KAAA,SAAA,GAAA,SAAA,KAAA,OAAA,MAAA,KAAA,UAAA,YAAA,KAAA,OAAA,GAC3B,KAAK,UAAA;;kBAEE,QAAA;WAEL,WAAc,KAAA;;;;;ACnDlB,IAAA,kBAAA,cAA4C,YAAA;cACpC,cAAc;AAFtB,UAAA,YAAqB,GAAA,KAAA,aAAY,aAAA,KAAA,eAAA;;EAIjC,gBAAC;AACD,UAAA,QAAa,SAAA,cAAA,OAAA;AACX,UAAM,aAAQ,QAAS,MAAA,GAAa,MAAC,MAAS,UAAA,OAAA,MAAA,MAAA,QAAA,QAE9C,MAAM,MAAA,YAAa,cAAgB,MAAA,MAAA,WAAA,YAAA,MAAA,MAAA,kBAAA,WAEnC,KAAK,UAAO,OAAU,KAAK,UAAC,YAAA,KAAA,GAAA,MAAA,iBAAA,WAAA,OAAA;AAC5B,cAAY,EAAA,QAAQ,EAAA,WAAO,EAAA,YAAA,EAAA,gBAAA;IAC3B,CAAA;;;;;ACXE,IAAO,aAAP,MAAiB;EAQrB,YAAY,cAA8B;AAP1C,SAAA,aAAqB,SAAQ,KAAA,eAAA;;gBAStB,OAAY;AAClB,UAAA,SAAA,SAAA,cAAA,QAAA;AAED,WAAA,aAA2B,QAAA,MAAA,GAAA,OAAA,MAAA,WAAA,YAAA,OAAA,MAAA,UAAA,OAEzB,OAAM,MAAM,QAAG,QAAS,OAAA,MAAc,YAAU,cAAA,OAAA,MAAA,kBAAA,WAChD,KAAA,UAAO;AACP,UAAM,EAAC,OAAc,IAAG,KAAA;AACxB,QAAA,SAAa;AACb,WAAO,QAAM,UAAQ;AACrB,gBAAa,SAAS,QAAG,kBAAa,IAAA,cAAA,IAAA,cAAA,kBAAA,IAAA,MAAA,IAAA;IACtC,CAAA,GAAA,OAAY,YAAC,QAAkB,KAAA,UAAU,YAAA,MAAA;;2BAItB;AACnB,SAAI,QAAS,iBAAG,UAAA,MAAA;IAAA,CAAA;;WAEd,OAAM;EAAA;;gBAEF,QAAE;;UAEL,EAAA,WAAA,OAAA,mBAAA,QAAA,GAAA;AACH,SAAA,YAAgB,WAAU,KAAA,kBAAA,SAAA,KAAA,cAAA,KAAA,GAE1B,QAAK,SAAU,KAAA,SAAY,KAAQ,IAAA,QAAA,oBAAA,SAAA,kBAAA,SAAA,KAAA,eAAA,kBAAA,IAAA,GAEpC,KAAA,QAAA,MAAA;EAED;iBACO,MAAQ;AAEb,SAAG,QAAA,MAAA,MAAA,KAAA,MAAA,MAAA,KAAA,QAAA,MAAA,OAAA,KAAA,OAAA,MACJ,KAAA,QAAA,MAAA,QAAA,KAAA,QAAA,MAAA,KAAA,QAAA,MAAA,SAAA,KAAA,SAAA;EAED;EAEA,aAAC;EAAA;EAED,QAAQ;AACN,SAAA,UAAY,YAAc,KAAA,OAAA;EAC5B;EAEA,gBAAU,QAAW;AACnB,WAAK,WAAY,KAAA;;;;;ACrDf,IAAO,iBAAP,MAAqB;EAOzB,YAAY,cAAmC;AAN/C,SAAA,aAAqB,YAAW,KAAA,eAAA,gBAAA,CAAA;;EAQhC,gBAAC;AAED,QAAA;;AACE,KAAA,UAAW,KAAG,KAAA,iBAAuB,WAAmC,KAAA,SAAA,GAAA,aAAA,MAAA,aAAA,YAAA,GAAA,KAAA,aAAA,QAAA,EAAA,GACxE,MAAI,MAAA,SAAK,QAAY,MAAA,MAAA,WAAA,YAAA,MAAE,MAAA,UAAU,aAC/B,MAAM,QAAA,QAAa,MAAU,MAAK,SAAK,QAAa,MAAA,MAAY,YAAA,oBACjE,MAAA,kBAAA,WAAA,KAAA,UAAA,OAAA,KAAA,UAAA,YAAA,KAAA,GACD,MAAM,iBAAe,WAAO,OAAA;AAC5B,YAAM,eAAiB,QAAU,EAAC,QAAA,EAAA,WAAA,EAAA,UAAA,qBAAA,YAAA,EAAA,OAAA,EAAA;AAClC,OAAK,gBAAc,uBAAS,EAAA,gBAAA;IAC5B,CAAA;;WAEK,OAAO;AACZ,SAAK,QAAO,QAAA,WAAkB,QAAU,QAAA;;aAInC;AAGL,WAAM,KAAA,QAAA;;UAEJ,EAAA,OAAM,mBAA4C,WAAS,QAAA,GAAA;SAC3D,YAAI,WAAgB,KAAA,kBAAoB,SAAA,KAAA,YAAA,KAAA,cAAA,WAEtC,SAAE,KAAA,SAAkB,KAAA,IAAA,QAAA,oBAAA,SAAA,kBAAA,SAAA,KAAA,eAAA,kBAAA,IAAA,SACrB,QAAA,MAAA;;EAEL,eAAC,MAAA;AAED,SAAS,QAAa,MAAA,MAAA,KAAA,MAAA,MAAA,KAAA,QAAA,MAAA,OAAA,KAAA,OAAA,MACpB,KAAK,QAAQ,MAAK,QAAG,KAAO,QAAU,MAAA,KAAa,QAAQ,MAAI,SAAA,KAAA,SAAA;EACjE;EAEA,aAAQ;EAAA;UACN;AACD,SAAA,UAAA,YAAA,KAAA,OAAA,GAAA,KAAA,UAAA;EAED;kBACO,QAAY;AACjB,WAAK,WAAA,KAAkB;;;;;ACuC3B,IAAY;UACVA,YAAA;AACA,EAAAA,WAAA,MAAA,OAAAA,WAAiB,SAAA,UAAAA,WAAA,OAAA,QAAAA,WAAA,QAAA;gBACjB,YAAA,CAAA,EAAa;", "names": ["Placement"]}
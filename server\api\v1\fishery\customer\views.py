import json
from typing import Optional
from fastapi import Depends, Request

from api.v1.utils import insert_log
from utils.security import get_password_hash
from models.system.utils import LogDetailType, LogType, StatusType
from schemas.users import UserCreate, UserUpdate
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    TenantModel,
    UserDataOption,
    TenantDataOption,
    MODEL_ID_TYPE,
    insert_operation,
    OperationType,
)
from tortoise.contrib.pydantic import pydantic_model_creator
from .schema import CustomerCreateDTO, CustomerDTO, CustomerPasswordDTO
from models.fishery import CustomerModel
from controllers.user import user_controller


router = TortoiseCRUDRouter(
    schema=CustomerDTO,
    create_schema=CustomerCreateDTO,
    db_model=CustomerModel,
    prefix="customer",
    tags=["客户管理"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)


# Overriding
@router.post("/create", response_model=RespModelT[CustomerDTO])
async def _(model: CustomerCreateDTO, request: Request):
    user_obj = await user_controller.get_by_username(model.account)
    if user_obj:
        return resp_fail(code="4090", msg="The user with this account already exists in the system.")

    tenant = TenantModel(name=model.name, description=model.location)
    await tenant.save()

    user_in = UserCreate(
        user_name=model.account,
        nick_name=model.name,
        user_email=model.account + "@t.com",
        password=model.password,
        user_phone=model.contact_info,
        tenant_id=tenant.id,
    )
    new_user = await user_controller.create(obj_in=user_in)
    await user_controller.update_roles_by_code(new_user, ["R_CUSTOMER"])

    # await insert_log(log_type=LogType.AdminLog, log_detail_type=LogDetailType.UserCreateOne, by_user_id=0)

    _pk = CustomerModel.describe()["pk_field"]["db_column"]
    obj = await router.create_obj_with_model(model, request, exclude={_pk}, tenant_id=str(tenant.id))
    return resp_success(convert_to_pydantic(obj, CustomerDTO))


@router.post("/update", response_model=RespModelT[CustomerDTO])
async def _(
    model: CustomerDTO,
    request: Request,
):
    try:
        obj = await router.db_model.get_or_none(**{router._pk: getattr(model, router._pk)})
        if not obj:
            return resp_fail(code="4090", msg="The customer not found")

        user = await user_controller.get_by_username(obj.account)
        if not user:
            return resp_fail(code="4090", msg="The user not found")

        # 更新operator
        await router.update_obj_with_model(obj, model, request)

        # 更新user
        status_type = None
        if model.status is not None:
            status_type = StatusType.enable if model.status else StatusType.disable

        # password 不可为 None
        user_update = UserUpdate(
            nick_name=model.name,
            password=model.password if model.password else "",
            user_phone=model.contact_info,
            status_type=status_type,
        )

        await user_controller.update(user.id, user_update)

        # 插入日志
        await insert_operation(
            user=getattr(request.state, "user_name", "unknown"),
            action=OperationType.EDIT,
            target=router.target,
            notes=json.dumps(model, default=str, indent=1),
            tenant_id=getattr(request.state, "tenant_id", None),
            user_id=getattr(request.state, "user_id", None),
            trace_id=getattr(request.state, "x_request_id", None),
        )

        return resp_success(convert_to_pydantic(obj, CustomerDTO))
    except Exception as e:
        raise ValueError("id不存在!")


@router.post("/delete", response_model=RespModelT[bool])
async def _(
    item_id: MODEL_ID_TYPE,
    request: Request,
) -> RespModelT[bool]:
    obj = await router.db_model.get_or_none(**{router._pk: item_id})
    if not obj:
        return resp_fail(code="4090", msg="The operator not found")

    await obj.delete()

    user = await user_controller.get_by_username(obj.account)
    if user:
        await user.delete()

    await insert_operation(
        user=getattr(request.state, "user_name", "unknown"),
        action=OperationType.DELETE,
        target=router.target,
        notes=json.dumps({router._pk: item_id, "result": True}, default=str),
        tenant_id=getattr(request.state, "tenant_id", None),
        user_id=getattr(request.state, "user_id", None),
        trace_id=getattr(request.state, "x_request_id", None),
    )

    return resp_success(True)


# @router.post("/updatepassword", response_model=RespModelT[CustomerDTO])
# async def _(model: CustomerPasswordDTO, request: Request):
#     try:
#         obj = await router.db_model.get(**{router._pk: getattr(model, router._pk)})
#         user = await user_controller.get_by_username(obj.account)
#         if not user:
#             return resp_fail(code="4090", msg="The user not found")

#         model.password = get_password_hash(password=model.password)
#         await router.update_obj_with_model(obj, model, request)
#         await user_controller.update(user.id, UserUpdate(password=model.password))

#         return resp_success(convert_to_pydantic(obj, router.schema))
#     except Exception as e:
#         # raise ValueError("id不存在!")
#         return resp_fail(code="4090", msg="operator id not found")

from tortoise import Model, fields
from fastapi_crud_tortoise import CrudModel


class BlocksModel(CrudModel):
    """代码块"""

    name = fields.CharField(max_length=255, null=False)
    code = fields.TextField(null=True, comment="脚本")
    description = fields.CharField(max_length=256, null=True, comment="描述")
    status = fields.BooleanField(default=True, comment="状态 true 启用 false 禁用")

    class Meta:
        table = "blocks"


class SchemasModel(CrudModel):
    """记录表"""

    name = fields.Char<PERSON>ield(max_length=255, null=False)
    schemas = fields.J<PERSON>NField(null=True, comment="schemas")
    description = fields.CharField(max_length=256, null=True, comment="描述")
    status = fields.BooleanField(default=True, comment="状态 true 启用 false 禁用")
    class Meta:
        table = "schemas"

    # schema 和 pydantic BaseModel 命名冲突

__all__ = [
    "BlocksModel",
    "SchemasModel",
]

from redis.asyncio import ConnectionPool, Redis
from api.v1.fishery.device.schema import DeviceStatus, DeviceDetailFeed
from utils.redis_utils import hget_redis_daily_key, hset_redis_daily_key, hget_redis_key, hset_redis_key
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional


class DeviceController:
    def __init__(self, redis: Redis):
        self.redis = redis

    async def set_device_status_by_id(self, device_id: str, status: DeviceStatus):
        """设置设备状态"""
        return await hset_redis_key(self.redis, f"device_status:{device_id}", status.model_dump())

    async def get_device_status_by_id(
        self,
        device_id: str,
        default_value: DeviceStatus = DeviceStatus(),
    ) -> DeviceStatus:
        """获取设备状态"""
        status_data = await hget_redis_key(
            self.redis, f"device_status:{device_id}", default_value.model_dump()
        )
        if status_data:
            status_data = {
                k.decode("utf-8") if isinstance(k, bytes) else k: (
                    v.decode("utf-8") if isinstance(v, bytes) else v
                )
                for k, v in status_data.items()
            }
        return DeviceStatus(**status_data) if status_data else default_value

    async def set_device_feed_by_id(self, device_id: str, feed: DeviceDetailFeed):
        """设置设备投喂信息"""
        return await hset_redis_daily_key(self.redis, f"device_feed:{device_id}", feed.model_dump())

    async def get_device_feed_by_id(
        self, device_id: str, default_value: DeviceDetailFeed = DeviceDetailFeed()
    ) -> DeviceDetailFeed:
        """获取设备投喂信息"""
        feed_data = await hget_redis_daily_key(
            self.redis, f"device_feed:{device_id}", default_value.model_dump()
        )
        if feed_data:
            feed_data = {
                k.decode("utf-8") if isinstance(k, bytes) else k: (
                    v.decode("utf-8") if isinstance(v, bytes) else v
                )
                for k, v in feed_data.items()
            }
        return DeviceDetailFeed(**feed_data) if feed_data else default_value

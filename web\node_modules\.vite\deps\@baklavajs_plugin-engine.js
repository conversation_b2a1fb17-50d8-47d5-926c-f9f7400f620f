import {
  __commonJS
} from "./chunk-ULBN3QDT.js";

// node_modules/.pnpm/@baklavajs+plugin-engine@1.10.2/node_modules/@baklavajs/plugin-engine/dist/index.cjs
var require_dist = __commonJS({
  "node_modules/.pnpm/@baklavajs+plugin-engine@1.10.2/node_modules/@baklavajs/plugin-engine/dist/index.cjs"(exports) {
    (() => {
      "use strict";
      var e = { 163: (e2, t2, r2) => {
        r2.r(t2), r2.d(t2, { __assign: () => i, __asyncDelegator: () => m, __asyncGenerator: () => b, __asyncValues: () => O, __await: () => g, __awaiter: () => l, __classPrivateFieldGet: () => C, __classPrivateFieldSet: () => S, __createBinding: () => h, __decorate: () => c, __exportStar: () => d, __extends: () => o, __generator: () => f, __importDefault: () => j, __importStar: () => E, __makeTemplateObject: () => P, __metadata: () => u, __param: () => s, __read: () => v, __rest: () => a, __spread: () => y, __spreadArray: () => w, __spreadArrays: () => _, __values: () => p });
        var n2 = function(e3, t3) {
          return n2 = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(e4, t4) {
            e4.__proto__ = t4;
          } || function(e4, t4) {
            for (var r3 in t4) Object.prototype.hasOwnProperty.call(t4, r3) && (e4[r3] = t4[r3]);
          }, n2(e3, t3);
        };
        function o(e3, t3) {
          if ("function" != typeof t3 && null !== t3) throw new TypeError("Class extends value " + String(t3) + " is not a constructor or null");
          function r3() {
            this.constructor = e3;
          }
          n2(e3, t3), e3.prototype = null === t3 ? Object.create(t3) : (r3.prototype = t3.prototype, new r3());
        }
        var i = function() {
          return i = Object.assign || function(e3) {
            for (var t3, r3 = 1, n3 = arguments.length; r3 < n3; r3++) for (var o2 in t3 = arguments[r3]) Object.prototype.hasOwnProperty.call(t3, o2) && (e3[o2] = t3[o2]);
            return e3;
          }, i.apply(this, arguments);
        };
        function a(e3, t3) {
          var r3 = {};
          for (var n3 in e3) Object.prototype.hasOwnProperty.call(e3, n3) && t3.indexOf(n3) < 0 && (r3[n3] = e3[n3]);
          if (null != e3 && "function" == typeof Object.getOwnPropertySymbols) {
            var o2 = 0;
            for (n3 = Object.getOwnPropertySymbols(e3); o2 < n3.length; o2++) t3.indexOf(n3[o2]) < 0 && Object.prototype.propertyIsEnumerable.call(e3, n3[o2]) && (r3[n3[o2]] = e3[n3[o2]]);
          }
          return r3;
        }
        function c(e3, t3, r3, n3) {
          var o2, i2 = arguments.length, a2 = i2 < 3 ? t3 : null === n3 ? n3 = Object.getOwnPropertyDescriptor(t3, r3) : n3;
          if ("object" == typeof Reflect && "function" == typeof Reflect.decorate) a2 = Reflect.decorate(e3, t3, r3, n3);
          else for (var c2 = e3.length - 1; c2 >= 0; c2--) (o2 = e3[c2]) && (a2 = (i2 < 3 ? o2(a2) : i2 > 3 ? o2(t3, r3, a2) : o2(t3, r3)) || a2);
          return i2 > 3 && a2 && Object.defineProperty(t3, r3, a2), a2;
        }
        function s(e3, t3) {
          return function(r3, n3) {
            t3(r3, n3, e3);
          };
        }
        function u(e3, t3) {
          if ("object" == typeof Reflect && "function" == typeof Reflect.metadata) return Reflect.metadata(e3, t3);
        }
        function l(e3, t3, r3, n3) {
          return new (r3 || (r3 = Promise))(function(o2, i2) {
            function a2(e4) {
              try {
                s2(n3.next(e4));
              } catch (e5) {
                i2(e5);
              }
            }
            function c2(e4) {
              try {
                s2(n3.throw(e4));
              } catch (e5) {
                i2(e5);
              }
            }
            function s2(e4) {
              var t4;
              e4.done ? o2(e4.value) : (t4 = e4.value, t4 instanceof r3 ? t4 : new r3(function(e5) {
                e5(t4);
              })).then(a2, c2);
            }
            s2((n3 = n3.apply(e3, t3 || [])).next());
          });
        }
        function f(e3, t3) {
          var r3, n3, o2, i2, a2 = { label: 0, sent: function() {
            if (1 & o2[0]) throw o2[1];
            return o2[1];
          }, trys: [], ops: [] };
          return i2 = { next: c2(0), throw: c2(1), return: c2(2) }, "function" == typeof Symbol && (i2[Symbol.iterator] = function() {
            return this;
          }), i2;
          function c2(i3) {
            return function(c3) {
              return function(i4) {
                if (r3) throw new TypeError("Generator is already executing.");
                for (; a2; ) try {
                  if (r3 = 1, n3 && (o2 = 2 & i4[0] ? n3.return : i4[0] ? n3.throw || ((o2 = n3.return) && o2.call(n3), 0) : n3.next) && !(o2 = o2.call(n3, i4[1])).done) return o2;
                  switch (n3 = 0, o2 && (i4 = [2 & i4[0], o2.value]), i4[0]) {
                    case 0:
                    case 1:
                      o2 = i4;
                      break;
                    case 4:
                      return a2.label++, { value: i4[1], done: false };
                    case 5:
                      a2.label++, n3 = i4[1], i4 = [0];
                      continue;
                    case 7:
                      i4 = a2.ops.pop(), a2.trys.pop();
                      continue;
                    default:
                      if (!((o2 = (o2 = a2.trys).length > 0 && o2[o2.length - 1]) || 6 !== i4[0] && 2 !== i4[0])) {
                        a2 = 0;
                        continue;
                      }
                      if (3 === i4[0] && (!o2 || i4[1] > o2[0] && i4[1] < o2[3])) {
                        a2.label = i4[1];
                        break;
                      }
                      if (6 === i4[0] && a2.label < o2[1]) {
                        a2.label = o2[1], o2 = i4;
                        break;
                      }
                      if (o2 && a2.label < o2[2]) {
                        a2.label = o2[2], a2.ops.push(i4);
                        break;
                      }
                      o2[2] && a2.ops.pop(), a2.trys.pop();
                      continue;
                  }
                  i4 = t3.call(e3, a2);
                } catch (e4) {
                  i4 = [6, e4], n3 = 0;
                } finally {
                  r3 = o2 = 0;
                }
                if (5 & i4[0]) throw i4[1];
                return { value: i4[0] ? i4[1] : void 0, done: true };
              }([i3, c3]);
            };
          }
        }
        var h = Object.create ? function(e3, t3, r3, n3) {
          void 0 === n3 && (n3 = r3), Object.defineProperty(e3, n3, { enumerable: true, get: function() {
            return t3[r3];
          } });
        } : function(e3, t3, r3, n3) {
          void 0 === n3 && (n3 = r3), e3[n3] = t3[r3];
        };
        function d(e3, t3) {
          for (var r3 in e3) "default" === r3 || Object.prototype.hasOwnProperty.call(t3, r3) || h(t3, e3, r3);
        }
        function p(e3) {
          var t3 = "function" == typeof Symbol && Symbol.iterator, r3 = t3 && e3[t3], n3 = 0;
          if (r3) return r3.call(e3);
          if (e3 && "number" == typeof e3.length) return { next: function() {
            return e3 && n3 >= e3.length && (e3 = void 0), { value: e3 && e3[n3++], done: !e3 };
          } };
          throw new TypeError(t3 ? "Object is not iterable." : "Symbol.iterator is not defined.");
        }
        function v(e3, t3) {
          var r3 = "function" == typeof Symbol && e3[Symbol.iterator];
          if (!r3) return e3;
          var n3, o2, i2 = r3.call(e3), a2 = [];
          try {
            for (; (void 0 === t3 || t3-- > 0) && !(n3 = i2.next()).done; ) a2.push(n3.value);
          } catch (e4) {
            o2 = { error: e4 };
          } finally {
            try {
              n3 && !n3.done && (r3 = i2.return) && r3.call(i2);
            } finally {
              if (o2) throw o2.error;
            }
          }
          return a2;
        }
        function y() {
          for (var e3 = [], t3 = 0; t3 < arguments.length; t3++) e3 = e3.concat(v(arguments[t3]));
          return e3;
        }
        function _() {
          for (var e3 = 0, t3 = 0, r3 = arguments.length; t3 < r3; t3++) e3 += arguments[t3].length;
          var n3 = Array(e3), o2 = 0;
          for (t3 = 0; t3 < r3; t3++) for (var i2 = arguments[t3], a2 = 0, c2 = i2.length; a2 < c2; a2++, o2++) n3[o2] = i2[a2];
          return n3;
        }
        function w(e3, t3, r3) {
          if (r3 || 2 === arguments.length) for (var n3, o2 = 0, i2 = t3.length; o2 < i2; o2++) !n3 && o2 in t3 || (n3 || (n3 = Array.prototype.slice.call(t3, 0, o2)), n3[o2] = t3[o2]);
          return e3.concat(n3 || Array.prototype.slice.call(t3));
        }
        function g(e3) {
          return this instanceof g ? (this.v = e3, this) : new g(e3);
        }
        function b(e3, t3, r3) {
          if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
          var n3, o2 = r3.apply(e3, t3 || []), i2 = [];
          return n3 = {}, a2("next"), a2("throw"), a2("return"), n3[Symbol.asyncIterator] = function() {
            return this;
          }, n3;
          function a2(e4) {
            o2[e4] && (n3[e4] = function(t4) {
              return new Promise(function(r4, n4) {
                i2.push([e4, t4, r4, n4]) > 1 || c2(e4, t4);
              });
            });
          }
          function c2(e4, t4) {
            try {
              (r4 = o2[e4](t4)).value instanceof g ? Promise.resolve(r4.value.v).then(s2, u2) : l2(i2[0][2], r4);
            } catch (e5) {
              l2(i2[0][3], e5);
            }
            var r4;
          }
          function s2(e4) {
            c2("next", e4);
          }
          function u2(e4) {
            c2("throw", e4);
          }
          function l2(e4, t4) {
            e4(t4), i2.shift(), i2.length && c2(i2[0][0], i2[0][1]);
          }
        }
        function m(e3) {
          var t3, r3;
          return t3 = {}, n3("next"), n3("throw", function(e4) {
            throw e4;
          }), n3("return"), t3[Symbol.iterator] = function() {
            return this;
          }, t3;
          function n3(n4, o2) {
            t3[n4] = e3[n4] ? function(t4) {
              return (r3 = !r3) ? { value: g(e3[n4](t4)), done: "return" === n4 } : o2 ? o2(t4) : t4;
            } : o2;
          }
        }
        function O(e3) {
          if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
          var t3, r3 = e3[Symbol.asyncIterator];
          return r3 ? r3.call(e3) : (e3 = p(e3), t3 = {}, n3("next"), n3("throw"), n3("return"), t3[Symbol.asyncIterator] = function() {
            return this;
          }, t3);
          function n3(r4) {
            t3[r4] = e3[r4] && function(t4) {
              return new Promise(function(n4, o2) {
                !function(e4, t5, r5, n5) {
                  Promise.resolve(n5).then(function(t6) {
                    e4({ value: t6, done: r5 });
                  }, t5);
                }(n4, o2, (t4 = e3[r4](t4)).done, t4.value);
              });
            };
          }
        }
        function P(e3, t3) {
          return Object.defineProperty ? Object.defineProperty(e3, "raw", { value: t3 }) : e3.raw = t3, e3;
        }
        var x = Object.create ? function(e3, t3) {
          Object.defineProperty(e3, "default", { enumerable: true, value: t3 });
        } : function(e3, t3) {
          e3.default = t3;
        };
        function E(e3) {
          if (e3 && e3.__esModule) return e3;
          var t3 = {};
          if (null != e3) for (var r3 in e3) "default" !== r3 && Object.prototype.hasOwnProperty.call(e3, r3) && h(t3, e3, r3);
          return x(t3, e3), t3;
        }
        function j(e3) {
          return e3 && e3.__esModule ? e3 : { default: e3 };
        }
        function C(e3, t3, r3, n3) {
          if ("a" === r3 && !n3) throw new TypeError("Private accessor was defined without a getter");
          if ("function" == typeof t3 ? e3 !== t3 || !n3 : !t3.has(e3)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
          return "m" === r3 ? n3 : "a" === r3 ? n3.call(e3) : n3 ? n3.value : t3.get(e3);
        }
        function S(e3, t3, r3, n3, o2) {
          if ("m" === n3) throw new TypeError("Private method is not writable");
          if ("a" === n3 && !o2) throw new TypeError("Private accessor was defined without a setter");
          if ("function" == typeof t3 ? e3 !== t3 || !o2 : !t3.has(e3)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
          return "a" === n3 ? o2.call(e3, r3) : o2 ? o2.value = r3 : t3.set(e3, r3), r3;
        }
      }, 749: (e2, t2, r2) => {
        t2.p$ = t2.wD = t2.EZ = void 0;
        var n2 = r2(163), o = function() {
          function e3() {
            this.listeners = /* @__PURE__ */ new Map();
          }
          return e3.prototype.addListener = function(e4, t3) {
            this.listeners.set(e4, t3);
          }, e3.prototype.removeListener = function(e4) {
            this.listeners.has(e4) && this.listeners.delete(e4);
          }, e3.prototype.emit = function(e4) {
            this.listeners.forEach(function(t3) {
              return t3(e4);
            });
          }, e3;
        }();
        t2.EZ = o;
        var i = function(e3) {
          function t3() {
            return null !== e3 && e3.apply(this, arguments) || this;
          }
          return n2.__extends(t3, e3), t3.prototype.emit = function(e4) {
            var t4, r3;
            try {
              for (var o2 = n2.__values(Array.from(this.listeners.values())), i2 = o2.next(); !i2.done; i2 = o2.next()) if (false === (0, i2.value)(e4)) return true;
            } catch (e5) {
              t4 = { error: e5 };
            } finally {
              try {
                i2 && !i2.done && (r3 = o2.return) && r3.call(o2);
              } finally {
                if (t4) throw t4.error;
              }
            }
            return false;
          }, t3;
        }(o);
        t2.wD = i;
        var a = function(e3) {
          function t3() {
            return null !== e3 && e3.apply(this, arguments) || this;
          }
          return n2.__extends(t3, e3), t3.prototype.execute = function(e4) {
            var t4, r3, o2 = e4;
            try {
              for (var i2 = n2.__values(this.taps), a2 = i2.next(); !a2.done; a2 = i2.next()) o2 = (0, a2.value)(o2);
            } catch (e5) {
              t4 = { error: e5 };
            } finally {
              try {
                a2 && !a2.done && (r3 = i2.return) && r3.call(i2);
              } finally {
                if (t4) throw t4.error;
              }
            }
            return o2;
          }, t3;
        }(function() {
          function e3() {
            this.tapMap = /* @__PURE__ */ new Map(), this.taps = [];
          }
          return e3.prototype.tap = function(e4, t3) {
            this.tapMap.has(e4) && this.untap(e4), this.tapMap.set(e4, t3), this.taps.push(t3);
          }, e3.prototype.untap = function(e4) {
            if (this.tapMap.has(e4)) {
              var t3 = this.tapMap.get(e4);
              this.tapMap.delete(e4);
              var r3 = this.taps.indexOf(t3);
              r3 >= 0 && this.taps.splice(r3, 1);
            }
          }, e3;
        }());
        t2.p$ = a;
      } }, t = {};
      function r(n2) {
        var o = t[n2];
        if (void 0 !== o) return o.exports;
        var i = t[n2] = { exports: {} };
        return e[n2](i, i.exports, r), i.exports;
      }
      r.d = (e2, t2) => {
        for (var n2 in t2) r.o(t2, n2) && !r.o(e2, n2) && Object.defineProperty(e2, n2, { enumerable: true, get: t2[n2] });
      }, r.o = (e2, t2) => Object.prototype.hasOwnProperty.call(e2, t2), r.r = (e2) => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
      };
      var n = {};
      (() => {
        r.r(n), r.d(n, { Engine: () => l, calculateOrder: () => c, containsCycle: () => u });
        var e2 = r(163), t2 = r(749);
        new Error("timeout while waiting for mutex to become available"), new Error("mutex already locked");
        const o = new Error("request for lock canceled");
        class i {
          constructor(e3, t3 = o) {
            this._value = e3, this._cancelError = t3, this._weightedQueues = [], this._weightedWaiters = [];
          }
          acquire(e3 = 1) {
            if (e3 <= 0) throw new Error(`invalid weight ${e3}: must be positive`);
            return new Promise((t3, r2) => {
              this._weightedQueues[e3 - 1] || (this._weightedQueues[e3 - 1] = []), this._weightedQueues[e3 - 1].push({ resolve: t3, reject: r2 }), this._dispatch();
            });
          }
          runExclusive(e3, t3 = 1) {
            return r2 = this, n2 = void 0, i2 = function* () {
              const [r3, n3] = yield this.acquire(t3);
              try {
                return yield e3(r3);
              } finally {
                n3();
              }
            }, new ((o2 = void 0) || (o2 = Promise))(function(e4, t4) {
              function a2(e5) {
                try {
                  s2(i2.next(e5));
                } catch (e6) {
                  t4(e6);
                }
              }
              function c2(e5) {
                try {
                  s2(i2.throw(e5));
                } catch (e6) {
                  t4(e6);
                }
              }
              function s2(t5) {
                var r3;
                t5.done ? e4(t5.value) : (r3 = t5.value, r3 instanceof o2 ? r3 : new o2(function(e5) {
                  e5(r3);
                })).then(a2, c2);
              }
              s2((i2 = i2.apply(r2, n2 || [])).next());
            });
            var r2, n2, o2, i2;
          }
          waitForUnlock(e3 = 1) {
            if (e3 <= 0) throw new Error(`invalid weight ${e3}: must be positive`);
            return new Promise((t3) => {
              this._weightedWaiters[e3 - 1] || (this._weightedWaiters[e3 - 1] = []), this._weightedWaiters[e3 - 1].push(t3), this._dispatch();
            });
          }
          isLocked() {
            return this._value <= 0;
          }
          getValue() {
            return this._value;
          }
          setValue(e3) {
            this._value = e3, this._dispatch();
          }
          release(e3 = 1) {
            if (e3 <= 0) throw new Error(`invalid weight ${e3}: must be positive`);
            this._value += e3, this._dispatch();
          }
          cancel() {
            this._weightedQueues.forEach((e3) => e3.forEach((e4) => e4.reject(this._cancelError))), this._weightedQueues = [];
          }
          _dispatch() {
            var e3;
            for (let t3 = this._value; t3 > 0; t3--) {
              const r2 = null === (e3 = this._weightedQueues[t3 - 1]) || void 0 === e3 ? void 0 : e3.shift();
              if (!r2) continue;
              const n2 = this._value, o2 = t3;
              this._value -= t3, t3 = this._value + 1, r2.resolve([n2, this._newReleaser(o2)]);
            }
            this._drainUnlockWaiters();
          }
          _newReleaser(e3) {
            let t3 = false;
            return () => {
              t3 || (t3 = true, this.release(e3));
            };
          }
          _drainUnlockWaiters() {
            for (let e3 = this._value; e3 > 0; e3--) this._weightedWaiters[e3 - 1] && (this._weightedWaiters[e3 - 1].forEach((e4) => e4()), this._weightedWaiters[e3 - 1] = []);
          }
        }
        class a {
          constructor(e3) {
            this._semaphore = new i(1, e3);
          }
          acquire() {
            return e3 = this, t3 = void 0, n2 = function* () {
              const [, e4] = yield this._semaphore.acquire();
              return e4;
            }, new ((r2 = void 0) || (r2 = Promise))(function(o2, i2) {
              function a2(e4) {
                try {
                  s2(n2.next(e4));
                } catch (e5) {
                  i2(e5);
                }
              }
              function c2(e4) {
                try {
                  s2(n2.throw(e4));
                } catch (e5) {
                  i2(e5);
                }
              }
              function s2(e4) {
                var t4;
                e4.done ? o2(e4.value) : (t4 = e4.value, t4 instanceof r2 ? t4 : new r2(function(e5) {
                  e5(t4);
                })).then(a2, c2);
              }
              s2((n2 = n2.apply(e3, t3 || [])).next());
            });
            var e3, t3, r2, n2;
          }
          runExclusive(e3) {
            return this._semaphore.runExclusive(() => e3());
          }
          isLocked() {
            return this._semaphore.isLocked();
          }
          waitForUnlock() {
            return this._semaphore.waitForUnlock();
          }
          release() {
            this._semaphore.isLocked() && this._semaphore.release();
          }
          cancel() {
            return this._semaphore.cancel();
          }
        }
        function c(e3, t3, r2) {
          var n2 = /* @__PURE__ */ new Map();
          e3.forEach(function(e4) {
            n2.set(e4, t3.filter(function(t4) {
              return t4.to && t4.to.parent === e4;
            }).map(function(e5) {
              return e5.from.parent;
            }));
          });
          var o2 = r2 || e3.filter(function(e4) {
            return t4 = e4.outputInterfaces, [Object, Array].includes((t4 || {}).constructor) && !Object.entries(t4 || {}).length;
            var t4;
          }), i2 = { children: o2.map(function(e4) {
            return { n: e4, children: [] };
          }) };
          s(i2, [], n2);
          var a2 = [], c2 = [];
          for (a2.push(i2); a2.length > 0; ) a2.shift().children.forEach(function(e4) {
            c2.push(e4.n), a2.push(e4);
          });
          for (var u2 = []; c2.length > 0; ) {
            var l2 = c2.pop();
            u2.includes(l2) || u2.push(l2);
          }
          return { calculationOrder: u2, rootNodes: o2 };
        }
        function s(t3, r2, n2) {
          var o2, i2;
          try {
            for (var a2 = (0, e2.__values)(t3.children), c2 = a2.next(); !c2.done; c2 = a2.next()) {
              var u2 = c2.value;
              if (r2.includes(u2.n)) throw new Error("Cycle detected");
              r2.unshift(u2.n), u2.children = u2.children.concat(n2.get(u2.n).map(function(e3) {
                return { n: e3, children: new Array() };
              })), s(u2, r2, n2), r2.shift();
            }
          } catch (e3) {
            o2 = { error: e3 };
          } finally {
            try {
              c2 && !c2.done && (i2 = a2.return) && i2.call(a2);
            } finally {
              if (o2) throw o2.error;
            }
          }
        }
        function u(e3, t3) {
          try {
            return c(e3, t3), true;
          } catch (e4) {
            return false;
          }
        }
        var l = function() {
          function r2(e3) {
            void 0 === e3 && (e3 = false), this.type = "EnginePlugin", this.events = { beforeCalculate: new t2.wD(), calculated: new t2.EZ() }, this.hooks = { gatherCalculationData: new t2.p$() }, this.nodeCalculationOrder = [], this.actualRootNodes = [], this.connectionsPerNode = /* @__PURE__ */ new Map(), this.recalculateOrder = false, this.calculateOnChange = false, this.calculationInProgress = false, this.mutex = new a(), this._rootNodes = void 0, this.interfaceTypePlugins = [], this.calculateOnChange = e3;
          }
          return Object.defineProperty(r2.prototype, "rootNodes", { get: function() {
            return this._rootNodes;
          }, set: function(e3) {
            this._rootNodes = e3, this.recalculateOrder = true;
          }, enumerable: false, configurable: true }), r2.prototype.register = function(e3) {
            var t3 = this;
            this.editor = e3, this.editor.plugins.forEach(function(e4) {
              "InterfaceTypePlugin" === e4.type && t3.interfaceTypePlugins.push(e4);
            }), this.editor.events.usePlugin.addListener(this, function(e4) {
              "InterfaceTypePlugin" === e4.type && t3.interfaceTypePlugins.push(e4);
            }), this.editor.events.addNode.addListener(this, function(e4) {
              e4.events.update.addListener(t3, function(e5) {
                (e5.interface && 0 === e5.interface.connectionCount || e5.option) && t3.onChange(false);
              }), t3.onChange(true);
            }), this.editor.events.removeNode.addListener(this, function(e4) {
              e4.events.update.removeListener(t3);
            }), this.editor.events.checkConnection.addListener(this, function(e4) {
              if (!t3.checkConnection(e4.from, e4.to)) return false;
            }), this.editor.events.addConnection.addListener(this, function(e4) {
              t3.editor.connections.filter(function(t4) {
                return t4.id !== e4.id && t4.to === e4.to;
              }).forEach(function(e5) {
                return t3.editor.removeConnection(e5);
              }), t3.onChange(true);
            }), this.editor.events.removeConnection.addListener(this, function() {
              t3.onChange(true);
            });
          }, r2.prototype.calculate = function(t3) {
            return (0, e2.__awaiter)(this, void 0, void 0, function() {
              var r3 = this;
              return (0, e2.__generator)(this, function(n2) {
                switch (n2.label) {
                  case 0:
                    return [4, this.mutex.runExclusive(function() {
                      return (0, e2.__awaiter)(r3, void 0, void 0, function() {
                        return (0, e2.__generator)(this, function(e3) {
                          switch (e3.label) {
                            case 0:
                              return [4, this.internalCalculate(t3)];
                            case 1:
                              return [2, e3.sent()];
                          }
                        });
                      });
                    })];
                  case 1:
                    return [2, n2.sent()];
                }
              });
            });
          }, r2.prototype.calculateOrder = function() {
            this.calculateNodeTree(), this.recalculateOrder = false;
          }, r2.prototype.internalCalculate = function(t3) {
            return (0, e2.__awaiter)(this, void 0, void 0, function() {
              var r3, n2, o2, i2, a2, c2, s2, u2, l2 = this;
              return (0, e2.__generator)(this, function(f) {
                switch (f.label) {
                  case 0:
                    if (this.events.beforeCalculate.emit(t3)) return [2, null];
                    t3 = this.hooks.gatherCalculationData.execute(t3), this.calculationInProgress = true, this.recalculateOrder && this.calculateOrder(), r3 = /* @__PURE__ */ new Map(), f.label = 1;
                  case 1:
                    f.trys.push([1, 6, 7, 8]), n2 = (0, e2.__values)(this.nodeCalculationOrder), o2 = n2.next(), f.label = 2;
                  case 2:
                    return o2.done ? [3, 5] : [4, (i2 = o2.value).calculate(t3)];
                  case 3:
                    a2 = f.sent(), this.actualRootNodes.includes(i2) && r3.set(i2, a2), this.connectionsPerNode.has(i2) && this.connectionsPerNode.get(i2).forEach(function(e3) {
                      var t4 = l2.interfaceTypePlugins.find(function(t5) {
                        return t5.canConvert(e3.from.type, e3.to.type);
                      });
                      e3.to.value = t4 ? t4.convert(e3.from.type, e3.to.type, e3.from.value) : e3.from.value;
                    }), f.label = 4;
                  case 4:
                    return o2 = n2.next(), [3, 2];
                  case 5:
                    return [3, 8];
                  case 6:
                    return c2 = f.sent(), s2 = { error: c2 }, [3, 8];
                  case 7:
                    try {
                      o2 && !o2.done && (u2 = n2.return) && u2.call(n2);
                    } finally {
                      if (s2) throw s2.error;
                    }
                    return [7];
                  case 8:
                    return this.calculationInProgress = false, this.events.calculated.emit(r3), [2, r3];
                }
              });
            });
          }, r2.prototype.checkConnection = function(e3, t3) {
            var r3 = { from: e3, to: t3, id: "dc", destructed: false, isInDanger: false }, n2 = this.editor.connections.concat([r3]);
            return n2.filter(function(e4) {
              return e4.to !== t3;
            }), u(this.editor.nodes, n2);
          }, r2.prototype.onChange = function(e3) {
            this.recalculateOrder = this.recalculateOrder || e3, this.calculateOnChange && !this.calculationInProgress && this.calculate();
          }, r2.prototype.calculateNodeTree = function() {
            var e3 = this, t3 = c(this.editor.nodes, this.editor.connections, this.rootNodes), r3 = t3.calculationOrder, n2 = t3.rootNodes;
            this.nodeCalculationOrder = r3, this.actualRootNodes = n2, this.connectionsPerNode.clear(), this.editor.nodes.forEach(function(t4) {
              e3.connectionsPerNode.set(t4, e3.editor.connections.filter(function(e4) {
                return e4.from.parent === t4;
              }));
            });
          }, r2;
        }();
      })(), exports.Engine = n.Engine, exports.calculateOrder = n.calculateOrder, exports.containsCycle = n.containsCycle, Object.defineProperty(exports, "__esModule", { value: true });
    })();
  }
});
export default require_dist();
/*! Bundled license information:

@baklavajs/plugin-engine/dist/index.cjs:
  (*! For license information please see index.cjs.LICENSE.txt *)
*/
//# sourceMappingURL=@baklavajs_plugin-engine.js.map

<script setup lang="ts">
import { h, ref } from 'vue';

import cron from 'cron-validate';
import { NButton, NInput, useMessage } from 'naive-ui';

import TimeList from './time-list.vue';
import CronExample from './cron-example.vue';

const message = useMessage();

const activeName = ref('seconds');

const columns = ref([
  {
    title: '',
    key: 'title'
  },
  {
    title: '秒',
    key: 'seconds',
    render(row, index) {
      return h(NInput, {
        value: row.seconds,
        onUpdateValue(v) {
          tableData.value[index].seconds = v;
        }
      });
    },
    colSpan: (rowData, rowIndex) => (rowIndex === 1 ? 6 : 1)
  },
  {
    title: '分钟',
    key: 'minutes',
    render(row, index) {
      return h(NInput, {
        value: row.minutes,
        onUpdateValue(v) {
          tableData.value[index].minutes = v;
        }
      });
    }
  },
  {
    title: '小时',
    key: 'hour',
    render(row, index) {
      return h(NInput, {
        value: row.hour,
        onUpdateValue(v) {
          tableData.value[index].hour = v;
        }
      });
    }
  },
  {
    title: '日',
    key: 'day',
    render(row, index) {
      return h(NInput, {
        value: row.day,
        onUpdateValue(v) {
          tableData.value[index].day = v;
        }
      });
    }
  },
  {
    title: '月',
    key: 'month',
    render(row, index) {
      return h(NInput, {
        value: row.month,
        onUpdateValue(v) {
          tableData.value[index].month = v;
        }
      });
    }
  },
  {
    title: '星期',
    key: 'week',
    render(row, index) {
      return h(NInput, {
        value: row.week,
        onUpdateValue(v) {
          tableData.value[index].week = v;
        }
      });
    }
  },
  {
    title: '年',
    key: 'year',
    render(row, index) {
      if (index == 0) {
        return h(NInput, {
          value: row.year,
          onUpdateValue(v) {
            tableData.value[index].year = v;
          }
        });
      }

      return h(
        NButton,
        {
          size: 'small',
          text: true,
          type: 'primary',
          onClick: () => cronToField()
        },
        { default: () => '解析为字段' }
      );
    }
  }
]);

const tableData = ref([
  {
    title: '表达式字段',
    seconds: '*',
    minutes: '*',
    hour: '*',
    day: '*',
    month: '*',
    week: '?',
    year: ''
  },
  {
    title: 'Cron 表达式',
    seconds: '* * * * * ?',
    minutes: '',
    hour: '',
    day: '',
    month: '',
    week: '',
    year: ''
  }
]);

const seconds = ref('0');
const seconds1_1 = ref();
const seconds1_2 = ref();
const seconds2_1 = ref();
const seconds2_2 = ref();
const seconds3_1 = ref();

// 秒改变事件
const secondsChange = () => {
  const secondsValue = ref('*');
  if (seconds.value === '1' && seconds1_1.value && seconds1_2.value) {
    secondsValue.value = `${seconds1_1.value}-${seconds1_2.value}`;
  } else if (seconds.value === '2' && seconds2_1.value && seconds2_2.value) {
    secondsValue.value = `${seconds2_1.value}/${seconds2_2.value}`;
  } else if (seconds.value === '3' && seconds3_1.value) {
    secondsValue.value = seconds3_1.value.join(',');
  }
  tableData.value[0].seconds = secondsValue.value;
  fieldToCron(tableData.value[0]);
};

const minutes = ref('0');
const minutes1_1 = ref();
const minutes1_2 = ref();
const minutes2_1 = ref();
const minutes2_2 = ref();
const minutes3_1 = ref();

// 分改变事件
const minutesChange = () => {
  const minutesValue = ref('*');
  if (minutes.value === '1' && minutes1_1.value && minutes1_2.value) {
    minutesValue.value = `${minutes1_1.value}-${minutes1_2.value}`;
  } else if (minutes.value === '2' && minutes2_1.value && minutes2_2.value) {
    minutesValue.value = `${minutes2_1.value}/${minutes2_2.value}`;
  } else if (minutes.value === '3' && minutes3_1.value) {
    minutesValue.value = minutes3_1.value.join(',');
  }
  tableData.value[0].minutes = minutesValue.value;
  fieldToCron(tableData.value[0]);
};

const hour = ref('0');
const hour1_1 = ref();
const hour1_2 = ref();
const hour2_1 = ref();
const hour2_2 = ref();
const hour3_1 = ref();

// 小时改变事件
const hourChange = () => {
  const hourValue = ref('*');
  if (hour.value === '1' && hour1_1.value && hour1_2.value) {
    hourValue.value = `${hour1_1.value}-${hour1_2.value}`;
  } else if (hour.value === '2' && hour2_1.value && hour2_2.value) {
    hourValue.value = `${hour2_1.value}/${hour2_2.value}`;
  } else if (hour.value === '3' && hour3_1.value) {
    hourValue.value = hour3_1.value.join(',');
  }
  tableData.value[0].hour = hourValue.value;
  fieldToCron(tableData.value[0]);
};

const day = ref('0');
const day1_1 = ref();
const day1_2 = ref();
const day2_1 = ref();
const day2_2 = ref();
const day3_1 = ref();
const day4_1 = ref();

// 日改变事件
const dayChange = () => {
  const dayValue = ref('*');
  if (day.value === '1') {
    dayValue.value = '?';
  } else if (day.value === '2') {
    dayValue.value = 'L';
  } else if (day.value === '3' && day1_1.value && day1_2.value) {
    dayValue.value = `${day1_1.value}-${day1_2.value}`;
  } else if (day.value === '4' && day2_1.value && day2_2.value) {
    dayValue.value = `${day2_1.value}/${day2_2.value}`;
  } else if (day.value === '5' && day3_1.value) {
    dayValue.value = `${day3_1.value}W`;
  } else if (day.value === '6' && day4_1.value) {
    dayValue.value = day4_1.value.join(',');
  }
  tableData.value[0].day = dayValue.value;
  if (day.value !== '1') {
    week.value = '1';
    tableData.value[0].week = '?';
  }
  fieldToCron(tableData.value[0]);
};

const month = ref('0');
const month1_1 = ref();
const month1_2 = ref();
const month2_1 = ref();
const month2_2 = ref();
const month3_1 = ref();

// 月改变事件
const monthChange = () => {
  const monthValue = ref('*');
  if (month.value === '1' && month1_1.value && month1_2.value) {
    monthValue.value = `${month1_1.value}-${month1_2.value}`;
  } else if (month.value === '2' && month2_1.value && month2_2.value) {
    monthValue.value = `${month2_1.value}/${month2_2.value}`;
  } else if (month.value === '3' && month3_1.value) {
    monthValue.value = month3_1.value.join(',');
  }
  tableData.value[0].month = monthValue.value;
  fieldToCron(tableData.value[0]);
};

const weekListData = ref(['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']);
const week = ref('1');
const week1_1 = ref();
const week1_2 = ref();
const week2_1 = ref();
const week2_2 = ref();
const week3_1 = ref();
const week4_1 = ref();

// 周改变事件
const weekChange = () => {
  const weekValue = ref('*');
  if (week.value === '1') {
    weekValue.value = '?';
  } else if (week.value === '2' && week1_1.value && week1_2.value) {
    weekValue.value = `${week1_1.value}-${week1_2.value}`;
  } else if (week.value === '3' && week2_1.value && week2_2.value) {
    weekValue.value = `${week2_1.value}#${week2_2.value}`;
  } else if (week.value === '4' && week3_1.value) {
    weekValue.value = `${week3_1.value}L`;
  } else if (week.value === '5' && week4_1.value) {
    weekValue.value = week4_1.value.join(',');
  }
  tableData.value[0].week = weekValue.value;
  if (week.value !== '1') {
    day.value = '1';
    tableData.value[0].day = '?';
  }
  fieldToCron(tableData.value[0]);
};

const year = ref();
const year1_1 = ref();
const year1_2 = ref();

// 年改变事件
const yearChange = () => {
  const yearValue = ref('');
  if (year.value === '0') {
    yearValue.value = '*';
  } else if (year.value === '1') {
    yearValue.value = '';
  } else if (year.value === '2' && year1_1.value && year1_2.value) {
    yearValue.value = `${year1_1.value}-${year1_2.value}`;
  }
  tableData.value[0].year = yearValue.value;
  fieldToCron(tableData.value[0]);
};

interface SpanMethodProps {
  row: any;
  column: any;
  rowIndex: number;
  columnIndex: number;
}

// 表格合并单元格
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
  if (rowIndex === 1 && columnIndex >= 1 && columnIndex <= 6) {
    return {
      rowspan: 1,
      colspan: 6
    };
  }
};

// 表达式字段 转 Cron 表达式
const fieldToCron = row => {
  const seconds = row.seconds !== '' ? row.seconds : '*';
  const minutes = row.minutes !== '' ? row.minutes : '*';
  const hour = row.hour !== '' ? row.hour : '*';
  const day = row.day !== '' ? row.day : '*';
  const month = row.month !== '' ? row.month : '*';
  const week = row.week !== '' ? row.week : '?';
  const year = row.year !== '' ? row.year : '';

  const cron = ref(`${seconds} ${minutes} ${hour} ${day} ${month} ${week} ${year}`);
  tableData.value[1].seconds = cron.value.trim();
};

// Cron 表达式 转 表达式字段
const cronToField = () => {
  const cronExpression = tableData.value[1].seconds; // 获取 Cron 表达式
  const [seconds, minutes, hour, day, month, week, year] = cronExpression.split(' ');

  // 更新表达式字段的值
  tableData.value[0].seconds = seconds;
  tableData.value[0].minutes = minutes;
  tableData.value[0].hour = hour;
  tableData.value[0].day = day;
  tableData.value[0].month = month;
  tableData.value[0].week = week;
  tableData.value[0].year = year;
};

// 验证 cron 表达式
// https://blog.csdn.net/a2524289/article/details/109177764
// 生成工具：https://cron.qqe2.com/
// cron 表达式转中文描述：https://github.com/bradymholt/cRonstrue
// cronjs 解析器与匹配器(五位)：https://github.com/datasert/cronjs
// cron 表达式验证器：https://github.com/Airfooox/cron-validate
const validate = () => {
  const cronExpression = tableData.value[1].seconds; // 获取 Cron 表达式
  const fields = cronExpression.split(' ');
  const [seconds, minutes, hour, day, month, week, year] = fields;
  let useYears = true;

  if (fields.length === 6) {
    useYears = false;
  } else if (fields.length === 7) {
    useYears = true;
  } else {
    message.error('验证失败');
    return;
  }
  // 验证字段是否为空
  if (!seconds || !minutes || !hour || !day || !month || !week) {
    message.error('验证失败');
    return;
  }

  // 验证日字段和周字段的冲突
  if (day === '?' && week === '?') {
    message.error('验证失败');
    return;
  }
  const result = cron(cronExpression, {
    override: {
      useSeconds: true,
      useYears,
      useBlankDay: true,
      useLastDayOfMonth: true,
      useLastDayOfWeek: true,
      useNearestWeekday: true,
      useNthWeekdayOfMonth: true
    }
  }).isValid();
  if (result) {
    message.success('验证成功');
  } else {
    message.error('验证失败');
  }
};

const dialogVisible1 = ref(false);
const dialogVisible2 = ref(false);

// 获取最近十次运行时间
const getRunDatetime = () => {
  dialogVisible1.value = true;
};

// Cron 表达式示例
const getCronExample = () => {
  dialogVisible2.value = true;
};
</script>

<template>
  <div class="cron-expression-box">
    <NTabs v-model="activeName" type="card">
      <NTabPane label="秒" name="seconds">
        <NRadioGroup v-model:value="seconds" class="!block" @on-update:value="secondsChange">
          <NFlex :wrap="false" align="center">
            <NRadio value="0"></NRadio>
            <div>每秒</div>
          </NFlex>

          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="1"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>周期从</NInputGroupLabel>
              <NInput v-model:value="seconds1_1" @change="secondsChange" />
              <NInputGroupLabel>-</NInputGroupLabel>
              <NInput v-model:value="seconds1_2" @change="secondsChange" />
              <NInputGroupLabel>秒</NInputGroupLabel>
            </NInputGroup>
          </NFlex>

          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="2"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>从</NInputGroupLabel>
              <NInput v-model:value="seconds2_1" @change="secondsChange" />
              <NInputGroupLabel>秒开始，每</NInputGroupLabel>
              <NInput v-model:value="seconds2_2" @change="secondsChange" />
              <NInputGroupLabel>秒执行一次</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="true" class="mt-4">
            <NRadio value="3">指定</NRadio>
            <NCheckboxGroup v-model:value="seconds3_1" class="ml-6" @change="secondsChange">
              <NCheckbox v-for="(num, index) in 60" :value="num" :label="index.toString()" />
            </NCheckboxGroup>
          </NFlex>
        </NRadioGroup>
      </NTabPane>
      <NTabPane label="分" name="minutes">
        <NRadioGroup v-model:value="minutes" class="!block" @change="minutesChange">
          <NFlex :wrap="false" align="center">
            <NRadio value="0"></NRadio>
            <div>每分</div>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="1"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>周期从</NInputGroupLabel>
              <NInput v-model:value="minutes1_1" @change="minutesChange" />
              <NInputGroupLabel>-</NInputGroupLabel>
              <NInput v-model:value="minutes1_2" @change="minutesChange" />
              <NInputGroupLabel>分</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="2"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>从</NInputGroupLabel>
              <NInput v-model:value="minutes2_1" @change="minutesChange" />
              <NInputGroupLabel>分开始，每</NInputGroupLabel>
              <NInput v-model:value="minutes2_2" @change="minutesChange" />
              <NInputGroupLabel>分执行一次</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="true" class="mt-4">
            <NRadio value="3">指定</NRadio>
            <NCheckboxGroup v-model:value="minutes3_1" class="ml-6" @change="minutesChange">
              <NCheckbox v-for="(num, index) in 60" :value="num" :label="index.toString()" />
            </NCheckboxGroup>
          </NFlex>
        </NRadioGroup>
      </NTabPane>
      <NTabPane label="时" name="hour">
        <NRadioGroup v-model:value="hour" class="!block" @change="hourChange">
          <NFlex :wrap="false" align="center">
            <NRadio value="0">每小时</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="1"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>周期从</NInputGroupLabel>
              <NInput v-model:value="hour1_1" @change="hourChange" />
              <NInputGroupLabel>-</NInputGroupLabel>
              <NInput v-model:value="hour1_2" @change="hourChange" />
              <NInputGroupLabel>小时</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="2"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>从</NInputGroupLabel>
              <NInput v-model:value="hour2_1" @change="hourChange" />
              <NInputGroupLabel>小时开始，每</NInputGroupLabel>
              <NInput v-model:value="hour2_2" @change="hourChange" />
              <NInputGroupLabel>小时执行一次</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="true" class="mt-4">
            <NRadio value="3">指定</NRadio>
            <NCheckboxGroup v-model:value="hour3_1" class="ml-6" @change="hourChange">
              <NCheckbox v-for="(num, index) in 24" :value="num" :label="index.toString()" />
            </NCheckboxGroup>
          </NFlex>
        </NRadioGroup>
      </NTabPane>
      <NTabPane label="日" name="day">
        <NRadioGroup v-model:value="day" class="!block" @change="dayChange">
          <NFlex :wrap="false" align="center">
            <NRadio value="0">每天</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="1">不指定</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="2">月最后一天</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="3"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>周期从</NInputGroupLabel>
              <NInput v-model:value="day1_1" @change="dayChange" />
              <NInputGroupLabel>-</NInputGroupLabel>
              <NInput v-model:value="day1_2" @change="dayChange" />
              <NInputGroupLabel>日</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="4"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>从</NInputGroupLabel>
              <NInput v-model:value="day2_1" @change="dayChange" />
              <NInputGroupLabel>日开始，每</NInputGroupLabel>
              <NInput v-model:value="day2_2" @change="dayChange" />
              <NInputGroupLabel>日执行一次</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="5"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>每月</NInputGroupLabel>
              <NInput v-model:value="day3_1" @change="dayChange" />
              <NInputGroupLabel>号最近的那个工作日</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="true" class="mt-4">
            <NRadio value="6">指定</NRadio>
            <NCheckboxGroup v-model:value="day4_1" class="ml-6" @change="dayChange">
              <NCheckbox v-for="num in 31" :value="num" :label="num.toString()" />
            </NCheckboxGroup>
          </NFlex>
        </NRadioGroup>
      </NTabPane>
      <NTabPane label="月" name="month">
        <NRadioGroup v-model:value="month" class="!block" @change="monthChange">
          <NFlex :wrap="false" align="center">
            <NRadio value="0">每月</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="1"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>周期从</NInputGroupLabel>
              <NInput v-model:value="month1_1" @change="monthChange" />
              <NInputGroupLabel>-</NInputGroupLabel>
              <NInput v-model:value="month1_2" @change="monthChange" />
              <NInputGroupLabel>月</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="2"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>从</NInputGroupLabel>
              <NInput v-model:value="month2_1" @change="monthChange" />
              <NInputGroupLabel>月开始，每</NInputGroupLabel>
              <NInput v-model:value="month2_2" @change="monthChange" />
              <NInputGroupLabel>月执行一次</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="true" class="mt-4">
            <NRadio value="3">指定</NRadio>
            <NCheckboxGroup v-model:value="month3_1" class="ml-6" @change="monthChange">
              <NCheckbox v-for="num in 12" :value="num" :label="num.toString()" />
            </NCheckboxGroup>
          </NFlex>
        </NRadioGroup>
      </NTabPane>
      <NTabPane label="周" name="week">
        <NRadioGroup v-model:value="week" class="!block" @change="weekChange">
          <NFlex :wrap="false" align="center">
            <NRadio value="0">每周</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="1">不指定</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="2"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>周期从星期</NInputGroupLabel>
              <NInput v-model:value="week1_1" @change="weekChange" />
              <NInputGroupLabel>- 星期</NInputGroupLabel>
              <NInput v-model:value="week1_2" @change="weekChange" />
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="3"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>第</NInputGroupLabel>
              <NInput v-model:value="week2_1" @change="weekChange" />
              <NInputGroupLabel>星期的星期</NInputGroupLabel>
              <NInput v-model:value="week2_2" @change="weekChange" />
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="4"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>本月最后一个星期</NInputGroupLabel>
              <NInput v-model:value="week3_1" @change="weekChange" />
            </NInputGroup>
          </NFlex>
          <NFlex :wrap="true" class="mt-4">
            <NRadio value="5">指定</NRadio>
            <NCheckboxGroup v-model:value="week4_1" class="ml-6" @change="weekChange">
              <NCheckbox v-for="(item, index) in weekListData" :value="index" :label="item"></NCheckbox>
            </NCheckboxGroup>
          </NFlex>
        </NRadioGroup>
      </NTabPane>
      <NTabPane label="年" name="year">
        <NRadioGroup v-model:value="year" class="!block" @change="yearChange">
          <NFlex :wrap="false" align="center">
            <NRadio value="0">每年</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="1">不指定</NRadio>
          </NFlex>
          <NFlex :wrap="false" align="center" class="mt-4">
            <NRadio value="2"></NRadio>
            <NInputGroup>
              <NInputGroupLabel>周期从</NInputGroupLabel>
              <NInput v-model:value="year1_1" @change="yearChange" />
              <NInputGroupLabel>-</NInputGroupLabel>
              <NInput v-model:value="year1_2" @change="yearChange" />
              <NInputGroupLabel>年</NInputGroupLabel>
            </NInputGroup>
          </NFlex>
        </NRadioGroup>
      </NTabPane>
    </NTabs>
    <div class="mt-5">
      <span class="text-[17px]">生成表达式</span>
      <NDataTable :columns="columns" :data="tableData" :single-line="false" />
      <!-- <n-data-table :columns="columns" :single-line="false" /> -->

      <!--
 <ElTable :data="tableData" style="width: 100%" :span-method="arraySpanMethod" class="mt-2" :border="true">
                <ElTableColumn prop="title" label="" />
                <ElTableColumn prop="seconds" label="秒" align="center">
                    <template #default="{ row, $index }">
                        <n-input v-if="$index === 0" v-model="row.seconds" @change="fieldToCron(row)" />
                        <n-input v-if="$index === 1" v-model="row.seconds" />
                    </template>
</ElTableColumn>
<ElTableColumn prop="minutes" label="分钟" align="center">
    <template #default="{ row, $index }">
                        <n-input v-if="$index === 0" v-model="row.minutes" @change="fieldToCron(row)" />
                        <BaseButton v-if="$index === 1" type="primary" link @click="cronToField">
                            解析为字段
                        </BaseButton>
                    </template>
</ElTableColumn>
<ElTableColumn prop="hour" label="小时" align="center">
    <template #default="{ row, $index }">
                        <n-input v-if="$index === 0" v-model="row.hour" @change="fieldToCron(row)" />
                    </template>
</ElTableColumn>
<ElTableColumn prop="day" label="日" align="center">
    <template #default="{ row, $index }">
                        <n-input v-if="$index === 0" v-model="row.day" @change="fieldToCron(row)" />
                    </template>
</ElTableColumn>
<ElTableColumn prop="month" label="月" align="center">
    <template #default="{ row, $index }">
                        <n-input v-if="$index === 0" v-model="row.month" @change="fieldToCron(row)" />
                    </template>
</ElTableColumn>
<ElTableColumn prop="week" label="星期" align="center">
    <template #default="{ row, $index }">
                        <n-input v-if="$index === 0" v-model="row.week" @change="fieldToCron(row)" />
                    </template>
</ElTableColumn>
<ElTableColumn prop="year" label="年" align="center">
    <template #default="{ row, $index }">
                        <n-input v-if="$index === 0" v-model="row.year" @change="fieldToCron(row)" />
                    </template>
</ElTableColumn>
</ElTable> 
-->

      <NFlex :wrap="false" justify="center" class="mt-6">
        <NButton type="primary" @click="getRunDatetime">获取最近十次运行时间</NButton>
        <NButton type="primary" @click="validate">Cron 表达式验证</NButton>
        <NButton type="primary" @click="getCronExample">Cron 表达式示例</NButton>
      </NFlex>
    </div>
  </div>

  <!--
 <Dialog v-model="dialogVisible1" title="获取最近十次运行时间" width="600px" height="400px" top="13vh">
        
    </Dialog> 
-->

  <NModal
    v-model:show="dialogVisible1"
    preset="card"
    :segmented="{ content: true }"
    title="最近十次运行时间"
    style="width: 600px"
  >
    <TimeList :expression="tableData[1].seconds" />
  </NModal>

  <!--
 <Dialog v-model="dialogVisible2" title="Cron 表达式示例" width="700px" height="620px" top="12vh">

    </Dialog> 
-->
  <NModal
    v-model:show="dialogVisible2"
    preset="card"
    :segmented="{ content: true }"
    title="Cron 表达式示例"
    style="width: 700px"
  >
    <CronExample />
  </NModal>
</template>

<style scoped></style>

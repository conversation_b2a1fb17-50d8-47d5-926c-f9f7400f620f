import asyncio
import base64
import json
import time
from typing import Any, Dict, List, Type
from fastapi import Depends, HTTPException, Request
# from fastapi.responses import StreamingResponse
from starlette.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from utils.sync_utils import SyncRecord, BulkSyncRecord, SyncResponse, SyncService, SyncTableName
from models.daq.terminal import DataPackModel, SeriesModel
from models.system.utils import LogDetailType, LogType
from utils.security import get_password_hash
from fastapi_crud_tortoise import (
    TortoiseCRUDRouter,
    resp_success,
    resp_fail,
    RespModelT,
    convert_to_pydantic,
    UserDataOption,
    TenantDataOption,
)
from .schema import RecordCreateDTO, RecordDTO
from models.daq import RecordModel
from utils.pack import packer

router = TortoiseCRUDRouter(
    schema=RecordDTO,
    create_schema=RecordCreateDTO,
    db_model=RecordModel,
    prefix="records",
    tags=["记录管理"],
    kbatch_create_route=False,
    kupsert_route=False,
    user_data_option=UserDataOption.ALL_ONLY,
    tenant_data_option=TenantDataOption.ALL_ONLY,
)


model_map = {
    SyncTableName.record: RecordModel,
    SyncTableName.series: SeriesModel,
    SyncTableName.data_pack: DataPackModel,
}

sync_service = SyncService(model_map, False)


@router.post("/sync/record", response_model=RespModelT[SyncResponse])
async def sync_record(request: SyncRecord):
    res = await sync_service.process(request)
    return resp_success(data=res.model_dump())

@router.post("/sync/bulk", response_model=RespModelT[List[SyncResponse]])
async def bulk_sync(request: BulkSyncRecord):
    res =await sync_service.process(request)
    return resp_success(data=[r.model_dump() for r in res])


async def get_record_csv(id:str):
    record = await RecordModel.get_or_none(id=id).prefetch_related("series")
    if record:
        series_list = await record.series
        series_title = [series.name for series in series_list]
        comma = ","
        yield comma.join(series_title)
        yield "\r\n"

        # 查询所有series的数据包，按record_time排序
        series_ids = [series.id for series in series_list]
        packs = [await DataPackModel.filter(series_id=series_id).order_by("record_time") for series_id in series_ids]
        for zip_iter in zip(*packs):
            pack_datas = [packer.unpack(pack.pack_data) for pack in zip_iter]
            csv = ""
            for data in zip(*pack_datas):
                row = list(data) 
                csv += ( comma.join(map(str, row)) + "\r\n" )
            yield csv
       

@router.get("/export_csv/{id}", description="stream方式返回csv格式记录")
async def record_csv(
    id: str
):
    csv_generator = get_record_csv(id)
    return StreamingResponse(csv_generator, media_type="text/csv")


async def get_record_json(id:str):
    record = await RecordModel.get_or_none(id=id).prefetch_related("series")
    if record:
        series_list = await record.series
        series_data = {
            str(series.channel): {
                "series": series.name,
                "unit": series.unit
            } for series in series_list
        }

        map_id_channel = {series.id: str(series.channel) for series in series_list}
 
        trigger_time = None
        theory_timestamp_first = None
        # 查询所有series的数据包，按record_time排序
        series_ids = [series.id for series in series_list]
        packs = [await DataPackModel.filter(series_id=series_id).order_by("record_time") for series_id in series_ids]
        for zip_iter in zip(*packs):
            for pack in zip_iter:
                ch = map_id_channel[pack.series_id]
                series_data[ch]["record_time"] = pack.theory_timestamp_s
                series_data[ch]["pack_data"] = base64.b64encode(pack.pack_data).decode('utf-8') 

            if pack:
                if trigger_time is None:
                    trigger_time = time.time()
                    theory_timestamp_first = pack.theory_timestamp_s

                elapsed = time.time() - trigger_time
                expected_elapsed = pack.theory_timestamp_s - theory_timestamp_first
                if elapsed < expected_elapsed:
                    await asyncio.sleep(expected_elapsed - elapsed)
                    
            yield { "data": json.dumps(series_data)} # series_data}
        
    yield "__END__"  # 发送停止事件


@router.get("/record_sse/{id}", description="SSE方式返回记录数据")
async def record_stream(id: str):
    json_generator = get_record_json(id)
    return EventSourceResponse(json_generator)




<script setup lang="ts" name="home">
import { inject, markRaw, nextTick, onActivated, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { ValueBuilderContext, useFs } from '@fast-crud/fast-crud';
import type {
  AddReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  DelReq,
  EditReq,
  UserPageQuery,
  UserPageRes,
} from '@fast-crud/fast-crud';
import { fast_records_api as api } from '@/service/api/record';
import dayjs from 'dayjs';
import { useMessage } from 'naive-ui';

const message = useMessage();

const selectedRowKeys = ref([]);
const selectedRow = ref(null);
function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery): Promise<UserPageRes> => {
    return api.GetList(query);
  };
  const editRequest = async (ctx: EditReq) => {
    const { form, row } = ctx;
    form.id = row.id;
    return api.UpdateObj(form);
  };
  const delRequest = async (ctx: DelReq) => {
    const { row } = ctx;
    return api.DelObj(row.id);
  };

  const addRequest = async (req: AddReq) => {
    const { form } = req;
    return api.AddObj(form);
  };

  // 导出记录
  const on_export_csv = (row) => {
    console.log(row);
    api.export_csv(row.id)
      .then((response) => {
        const url = URL.createObjectURL(response.data);

        const link = document.createElement("a");
        link.href = url;
        link.download = `${row.name}.csv`;
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();

        URL.revokeObjectURL(url);

        message.success('下载成功');

        document.body.removeChild(link);
      })
      .catch((e: any) => {
        console.error("Error downloading CSV:", e);
      });
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest
      },
      settings: {
        viewFormUseCellComponent: true,

        plugins: {
          // 这里使用行选择插件，生成行选择crudOptions配置，最终会与crudOptions合并
          rowSelection: {
            enabled: true,
            order: -2,
            before: true,
            props: {
              multiple: false,
              crossPage: true,
              selectedRowKeys
            }
          }
        }
      },
      table: {
        striped: true,
        rowProps: (row: Record<string, any>) => {
          return {
            onClick: () => {
              selectedRowKeys.value = [row.id];
              selectedRow.value = row;
            }
          };
        },
      },
      actionbar: {
        show: false
      },
      rowHandle: {
        width: 160,
        buttons: {
          edit: {
            show: true,
            order: 1,
          },
          view: {
            show: false,
            order: 2,
          },
          export: {
            text: '导出',
            type: 'primary',
            size: 'small',
            order: 3,
            click(context) {
              on_export_csv(context.row);
            },
          },
          remove: {
            show: true,
            order: 5,
          },
        },
      },
      search: {
        show: false
      },
      form: {
        col: {
          span: 24,
        },
        wrapper: {
          draggable: false
        }
      },
      toolbar: {
        show: false
      },
      columns: {
        name: {
          title: '名称',
          type: 'text',
          search: { show: true },
          column: {
            sorter: 'custom',
          }
        },

        record_time: {
          title: '记录时间',
          type: 'datetime',
          form: {
            show: false
          },
          valueBuilder(context) {
            const { value, row, key } = context;
            if (value) {
              row[key] = dayjs(value).valueOf();
            }
          },
          valueResolve(context) {
            const { value, form, key } = context;
            if (value) {
              form[key] = dayjs(value).format('YYYY-MM-DD');
            }
          },
        },
        record_duration: {
          title: '记录时长',
          type: 'number',
          form: {
            show: false
          }
        },
        description: {
          title: '备注',
          type: 'text',
        },

      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

onMounted(() => {
  crudExpose.doRefresh();
});

onUnmounted(() => { });

</script>

<template>
  <div class="layout-padding">
    <NCard class="h-full" :bordered="false">
      <FsCrud ref="crudRef" v-bind="crudBinding"></FsCrud>
    </NCard>
  </div>
</template>

<style scoped lang="scss"></style>

<template>
	<div :class="[pop ? 'glass' : 'normal']">
		<div ref="panel" class="chart-panel" :class="[pop ? 'popup' : 'normal']">
			<div class="controls-bar">
				<el-button type="primary" size="small" @click="exportCSV(options.title)"
					class="controls-bar-button">导出CSV</el-button>
				<!-- <el-dropdown style="z-index: 1000" :hide-on-click="false" trigger="click" placement="bottom-start"> -->
				<el-dropdown style="z-index: 1000" placement="bottom-start">
					<el-image style="width: 24px; height: 24px" :src="png_colorlens"></el-image>
					<template #dropdown>
						<el-dropdown-menu style="z-index: 1000">
							<el-dropdown-item v-for="(legend, index) in legends" :key="index">
								<div class="menu-item">
									<span style="margin-right: 16px">{{ legend }}</span>
									<el-color-picker v-model="colors[index]" size="mini"></el-color-picker>
								</div>
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<div style="flex-grow: 1;"></div>

				<el-button v-if="pop" icon="Close" size="small" circle style="border: 0; font-size: 16px;"
					@click="on_pop"></el-button>
				<el-button v-else icon="FullScreen" size="small" circle style="border: 0; font-size: 16px;"
					@click="on_pop"></el-button>
			</div>

			<div style="width: 100%; height: 100%;" id="chart-container">
				<div class="free-chart" :class="className" :style="{ width, height }" ref="chartDom"></div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
//按需导入需要用到的 vue函数 和 echarts
import { onMounted, onBeforeUnmount, defineProps, watch, inject, ref, computed, reactive, nextTick } from 'vue';
import { __appendData, __setData, __exportCSV, colorPalette_default } from './chart';
// import png_colorlens from '@/icons/colorlens.png';
import png_colorlens from '@/assets/imgs/icColorLens.png';






// 方案①
// import * as echarts from 'echarts';

// 方案②
let echarts = inject('echarts');

//获取 dom 和 父组件数据 并定义"fastChart"用于初始化图表
let fastChart: echarts.ECharts;

// theme
const chartTheme = computed(() => {
	let isDark = props.options.darkMode;
	return { theme: isDark ? 'dark' : '', bgColor: isDark ? 'transparent' : '', color: isDark ? '#dadada' : '#303133' };
});

let colors = ref(colorPalette_default);

// 测试不同长度对齐
// let legends = ref(['hello', '哈', '哈哈哈哈', '哈哈', '哈哈哈哈哈哈哈哈哈']);

let legends = ref([]);

const chartDom = ref(null);

const props = defineProps({
	className: {
		type: String,
		default: '',
	},
	// width: {
	// 	type: String,
	// 	default: '100%',
	// },
	// height: {
	// 	type: String,
	// 	default: '300px',
	// },
	options: {
		type: Object,
		default: () => {
			return {
				title: '折线图',
				darkMode: false,
				yAxis: {
					type: 'value',
					max: 10,
					min: 0,
				},
				autoYAxis: true,
			};
		},
	},
});

let pop = ref(false);

const on_pop = () => {
	pop.value = !pop.value;
}

const width = ref(0)
const height = ref(0)

const panel = ref(null);
const resizeObserver = new ResizeObserver(entries => {
	for (const entry of entries) {
		console.log('元素大小已变化', entry.target);
		width.value = `${entry.target.clientHeight - 80}px`;
		height.value = `${entry.target.clientHeight - 40}px`;
	}
	// resizeHandler();
});

const watchChartWc = new ResizeObserver(() => {
	cancalDebounce()
})

const handleChange = (value) => {
	console.log(value);
};

//重绘图表函数
const resizeHandler = () => {
	width.value = `${panel.value.clientHeight - 80}px`;
	height.value = `${panel.value.clientHeight - 40}px`;
	nextTick(() => {
		fastChart.resize();
	})
};

//设置防抖，保证无论拖动窗口大小，只执行一次获取浏览器宽高的方法
const debounce = (fun: { (): void; (): void }, delay: number | undefined) => {
	let timer: number | undefined;
	return function () {
		if (timer) {
			clearTimeout(timer);
		}
		timer = setTimeout(() => {
			fun();
		}, delay);
	};
};

const cancalDebounce = debounce(resizeHandler, 50);
//页面成功渲染，开始绘制图表
onMounted(() => {
	//配置为 svg 形式，预防页面缩放而出现模糊问题；图表过于复杂时建议使用 Canvas { renderer: 'svg' }
	fastChart = echarts.init(chartDom.value, chartTheme.value.theme);

	const option = {
		color: colors.value,
		backgroundColor: chartTheme.value.bgColor,
		title: {
			text: props.options.title,
			x: 'left',
			textStyle: { fontSize: '15', color: chartTheme.value.color },
		},
		grid: { top: 70, right: 20, bottom: 30, left: 40 },
		tooltip: { trigger: 'axis' },
		legend: { data: [], right: 0 },
		xAxis: {
			min: -10,
			max: 10,
			axisLine: { onZero: false }
		},
		yAxis: [
			{
				min: -10,
				max: 10,
				name: '值',
				axisLine: { onZero: false },
				splitLine: { show: true, lineStyle: { type: 'dashed', color: '#f5f5f5' } },
			},
		],
		dataZoom: [
			{
				type: 'inside',
				xAxisIndex: 0,
				yAxisIndex: 0,
				filterMode: 'none'
			}
			// {
			// 	type: 'inside',
			// 	start: 0,
			// 	end: 100,
			// },
			// {
			//  type: 'slider',
			// 	start: 0,
			// 	end: 100,
			// },
			// {
			// 	type: 'inside',
			// 	yAxisIndex: 0,
			// 	start: 0,
			// 	end: 100,
			// },
			// {
			//  type: 'slider',
			// 	yAxisIndex: 0,
			// 	start: 0,
			// 	end: 100,
			// },

		],
		series: [],
	};

	fastChart.setOption(option);

	//自适应不同屏幕时改变图表尺寸
	// window.addEventListener('resize', cancalDebounce);
	// watchChartWc.observe(document.getElementById('chart-container'))
	watchChartWc.observe(panel.value)
	// resizeObserver.observe(panel.value);
});

//页面销毁前，销毁事件和实例
onBeforeUnmount(() => {
	// window.removeEventListener('resize', cancalDebounce);
	// resizeObserver.unobserve(panel.value);

	// watchChartWc.unobserve(document.getElementById('chart-container'))
	watchChartWc.unobserve(panel.value)
	fastChart.dispose();
});

//监听图表数据时候变化，重新渲染图表
watch(
	() => [props.options.darkMode, props.options.title],
	() => {
		const option = {
			backgroundColor: chartTheme.value.bgColor,
			title: {
				text: props.options.title,
				textStyle: { color: chartTheme.value.color },
			},
		};

		fastChart?.setOption(option);
	},
	{ deep: true, immediate: true }
);

watch(
	() => [props.options.autoYAxis, props.options.yAxis],
	() => {
		if (props.options.autoYAxis) {
			fastChart?.setOption({
				yAxis: {
					type: 'value',
					max: null,
					min: null,
					interval: null,
				},
			});
		} else {
			fastChart?.setOption({
				yAxis: props.options.yAxis,
			});
		}
	},
	{ deep: true, immediate: true }
);

watch(
	colors,
	() => {
		// legend 图例颜色无变化
		// let option = {
		// 	series: colors.map((v) => {
		// 		return {
		// 			lineStyle: {
		// 				color: v,
		// 			},
		// 		};
		// 	}),
		// };
		// fastChart?.setOption(option);

		let option = {
			color: colors.value,
		};
		fastChart?.setOption(option);
	},
	{ deep: true, immediate: true }
);

const appendData = (data: object, max_len: number = 100000) => {
	legends.value = __appendData(fastChart, data, max_len);
};

const setData = (data: object) => {
	legends.value = __setData(fastChart, data);
};

const exportCSV = (filename) => {
	__exportCSV(fastChart, filename);
};

defineExpose({
	setData,
	appendData,
	exportCSV,
});
</script>

<style>
.el-color-picker__trigger {
	border: none;
}
</style>

<style scoped lang="scss">
.chart-panel {
	padding: 20px !important;
	transition-property: position, width, height, border-radius;
	transition-duration: 0.5s;
	transition-timing-function: ease-in;
	transition-delay: 0;
}

.normal {
	height: 100%;
}

.glass {
	position: fixed !important;
	left: 0 !important;
	right: 0 !important;
	top: 0 !important;
	bottom: 0 !important;
	z-index: 999;
	backdrop-filter: blur(20px);
	color: #fff;
}

.popup {
	background-color: white;
	position: fixed !important;
	left: 30px !important;
	right: 30px !important;
	top: 30px !important;
	bottom: 30px !important;
	z-index: 1000;
	border: 1px solid #f1f2f3;
	box-shadow: 0 2px 12px #0000001a;
}


.menu-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin: 2px 0;
	width: 100%;
	z-index: 1000;
}


.controls-bar {
	display: flex;
	justify-content: flex-start;
	gap: 5px;
	position: absolute;
	top: 16px;
	left: 80px;
	right: 20px;
	z-index: 100;
}

.controls-bar-button {
	margin-right: 5px;
}

.controls-bar-input {
	width: 50px;
}

.free-chart {
	// margin: auto;
}
</style>
"""
Descripttion:
version: 0.x
Author: zhai
Date: 2025-01-05 11:18:02
LastEditors: zhai
LastEditTime: 2025-01-20 21:24:31
"""

from pydantic import BaseModel
from typing import Literal, Optional
from api.v1.operations.schema import OperationsCreateDTO


class MqttMsgDTO(BaseModel):
    sn: str
    payload: dict
    timeout: Optional[int] = 30


class MqttCmdPayloadDTO(BaseModel):
    type: str
    data: dict


class MqttCmdDTO(BaseModel):
    type: Optional[Literal["N", "R"]] = "R"
    sn: list[str]
    payload: MqttCmdPayloadDTO
    timeout: Optional[int] = 30
    operation: Optional[OperationsCreateDTO] = None

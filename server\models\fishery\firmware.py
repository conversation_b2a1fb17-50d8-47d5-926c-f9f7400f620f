from datetime import datetime
from tortoise import Model, fields
from fastapi_crud_tortoise import CrudModel


class FirmwareModel(CrudModel):
    name = fields.CharField(max_length=255)
    version = fields.CharField(max_length=50)
    type = fields.CharField(max_length=255)
    model = fields.CharField(max_length=255)
    file = fields.JSONField(null=True, default=None)
    notes = fields.CharField(max_length=255, null=True)
    status = fields.BooleanField(default=True)

    class Meta:
        table = "firmware"

/**
 * 生成模拟波形数据
 * @param length 数据长度
 * @param xStart x起点值
 * @param xEnd x终点值
 * @param frequency 频率（可选）
 */
export function generateWaveData(
  length: number,
  xStart: number,
  xEnd: number,
  frequency = 50
): { x: number[]; y: number[] } {
  const x: number[] = []
  const y: number[] = []

  const step = (xEnd - xStart) / length
  for (let i = 0; i < length; i++) {
    const xi = xStart + i * step
    const noise = Math.random() * 0.1 - 0.05
    const yi = Math.sin(2 * Math.PI * frequency * (xi / 1000)) + noise
    x.push(Number(xi.toFixed(3)))
    y.push(Number(yi.toFixed(6)))
  }

  return { x, y }
}

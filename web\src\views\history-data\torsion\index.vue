<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue';
import WaveChart from '@/components/waveChart/index.vue';
import { NDropdown, NButton, NTable, NDatePicker, NTabs, NTabPane } from 'naive-ui';
import FFT from 'fft.js/lib/fft';
import { EventSourcePolyfill } from 'event-source-polyfill';
import PulseChart from '@/components/pulseChart/index.vue';
import { localStg } from '@/utils/storage';

function get_token() {
  const token = localStg.get('token');
  return token;
}

// Chart Refs
const InstRef = ref();
const InstFreqRef = ref();
const TorsRef = ref();
const TorsFreqRef = ref();
const AngleRef = ref();
const AngleFreqRef = ref();
const PulseRef = ref();

const InstOptions = reactive({ title: '角速度', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const InstFreqOptions = reactive({ title: '频谱', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true })
const TorsOptions = reactive({ title: '角速度', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const TorsFreqOptions = reactive({ title: '频谱', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const AngleOptions = reactive({ title: '角度', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const AngleFreqOptions = reactive({ title: '频谱', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });
const PulseOptions = reactive({ title: '转速脉冲', darkMode: false, yAxis: { type: 'value', max: 10, min: 0 }, autoYAxis: true });

const fullData = reactive({});
const tableData = ref([]);
const tabKey = ref('inst');
const sectabKey = ref('volt');
const showRightPanel = ref(true);
const showEventPanel = ref(true);

let eventList = ref([
  { time: '2025-07-20 10:00:00', name: '过电压', type: '报警' },
  { time: '2025-07-20 10:05:00', name: '低电流', type: '提示' },
]);
const selectedLine = ref('线路-未选择');

const dropdownOptions = [
  {
    label: '角速度',
    key: 'angle',
    children: [
      { label: '瞬时角速度', key: 'angle-瞬时角速度' },
      { label: '扭振角速度', key: 'angle-扭振角速度' },
      { label: '扭振角度', key: 'angle-扭振角度' }
    ]
  },
  {
    label: '频谱(角速度)',
    key: 'freq',
    children: [
      { label: '瞬时角速度', key: 'freq-频谱(瞬时角速度)' },
      { label: '扭振角速度', key: 'freq-频谱(扭振角速度)' },
      { label: '扭振角度', key: 'freq-频谱(扭振角度)' }
    ]
  },
  {
    label: '转速脉冲',
    key: 'pulse',
    children: [
      { label: '转速脉冲0', key: 'pulse-脉冲(转速脉冲0)' },
      { label: '转速脉冲1', key: 'pulse-脉冲(转速脉冲1)' }
    ]
  }
];
function getGroupKey(name: string): string {
  if (['瞬时角速度', '扭振角速度', '扭振角度'].includes(name)) return 'angle';
  if (['频谱(瞬时角速度)', '频谱(扭振角速度)', '频谱(扭振角度)'].includes(name)) return 'freq';
  if (['脉冲(转速脉冲0)', '脉冲(转速脉冲1)'].includes(name)) return 'pulse';
  return 'unknown';
}
// 外部保存 eventSource 引用
let sseInstance: EventSourcePolyfill | null = null;
let sseGearPulses: EventSourcePolyfill | null = null;
// 时间选择
const dateRange = ref<[number, number] | null>(null);
const setRecent = (type: 'hour' | 'day' | 'week' | 'month') => {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  const oneDay = 24 * 60 * 60 * 1000;
  const ranges = {
    hour: [now - oneHour, now],
    day: [now - oneDay, now],
    week: [now - 7 * oneDay, now],
    month: [now - 30 * oneDay, now],
  };
  dateRange.value = ranges[type];
  handleQuery();
};

// 更新右侧列表
const updateTable = (lineKey) => {
  tableData.value = fullData[lineKey] || [];
};
// 选择列表线路
const handleSelect = (key) => {
  if (!dateRange.value) {
    window.$message?.warning('请先选择时间范围');
    return;
  }
  selectedLine.value = key;
  updateTable(key);
  // to-do-更新事件列表
};

watch(showRightPanel, async () => {
  await nextTick();
  [InstRef, InstFreqRef, TorsRef, TorsFreqRef, AngleRef, AngleFreqRef, PulseRef].forEach(refItem => {
    refItem.value?.resize?.();
  });
});

// 脉冲图图数据
function transformGearPulses(gearPulses) {
  const result = {};

  for (const row of gearPulses) {
    const chanNo = row.ChanNo;
    const tstamp = row.Tstamp;
    const value = row.IsHigh ? 5 : 0;

    if (!result[chanNo]) {
      result[chanNo] = [];
    }

    result[chanNo].push([tstamp, value]);
  }

  return Object.keys(result).map(chanNo => ({
    name: `脉冲(转速脉冲${chanNo})`,
    value: result[chanNo]
  }));
}

// 查询函数
const handleQuery = () => {
  if (!dateRange.value) {
    window.$message?.warning('请先选择时间范围');
    return;
  }
  if (sseInstance) {
    console.log('🛑 页面卸载，手动关闭 SSE');
    sseInstance.close();
    sseInstance = null;
  }
  if (sseGearPulses) {
    sseGearPulses.close();
    sseGearPulses = null;
  }
  const [start, end] = dateRange.value;
  // const recordType = 'Gearphys';
  const startISO = new Date(start).toISOString();
  const endISO = new Date(end).toISOString();
  const token = get_token();
  const base_url = import.meta.env.VITE_SERVICE_BASE_URL;
  const headers = {
    token,
    Authorization: `Bearer ${token}`
  };

  // to-do-再添加一个请求 GearPulses

  sseInstance = new EventSourcePolyfill(`${base_url}/tvs/record_sse/Gearphys/${startISO}/${endISO}`, { headers });

  sseInstance.onopen = () => {
    console.log('📡 Gearphys, SSE连接已建立');
  };

  sseInstance.addEventListener('data', (event) => {
    try {
      const raw = event.data;
      const fixedStr = raw
        .replace(/'/g, '"')
        .replace(/\bTrue\b/g, 'true')
        .replace(/\bFalse\b/g, 'false')
        .replace(/\bNone\b/g, 'null');

      const data = JSON.parse(fixedStr);
      console.log('解析后数据', data.batch);
      const rows = data.batch || [];
      const headers = rows[0]; // 表头
      if (rows.length < 2) return;
      const N = rows.length;

      const channelIndices = {
        InstSpeed: headers.indexOf('InstSpeed'),
        TorsSpeed: headers.indexOf('TorsSpeed'),
        TorsAngle: headers.indexOf('TorsAngle'),
      };
      const lineName = {
        'InstSpeed': '瞬时角速度',
        'TorsSpeed': '扭振角速度',
        'TorsAngle': '扭振角度'
      }

      const x = rows.map(item => {
        const d = new Date(item.Tstamp * 1000);
        const pad = (n: number, len = 2) => String(n).padStart(len, '0');
        return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ` +
          `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}.${pad(d.getMilliseconds(), 3)}`;
      });

      const buildY = (names: string[]) =>
        names.map(name => ({
          name: lineName[name],
          value: rows.map(row => row[channelIndices[name]])
        }));

      const chartDataV = { x, y: buildY(['InstSpeed']) };
      const chartDataI = { x, y: buildY(['TorsSpeed']) };
      const chartDataE = { x, y: buildY(['TorsAngle']) };

      InstRef.value?.appendData(chartDataV, 10000);
      TorsRef.value?.appendData(chartDataI, 10000);
      AngleRef.value?.appendData(chartDataE, 10000);

      // 增加频谱处理逻辑（每组计算一次），提取时间戳（微秒）并计算采样率
      if (rows.length < 2) {
        console.warn('❗️数据不足，跳过频谱计算');
        return;
      }
      const t0 = Number(rows[0].Tstamp);
      const t1 = Number(rows[1].Tstamp);
      const dt_us = t1 - t0;
      const fs = 1e6 / dt_us;  // Hz，采样率

      // 准备信号通道数据
      const getSignal = (ch: keyof typeof channelIndices) =>
        rows.map(row => Number(row[channelIndices[ch]]));

      const signalVA = getSignal('InstSpeed');
      const signalVB = getSignal('TorsSpeed');
      const signalVC = getSignal('TorsAngle');

      const computeFFT = (signal: number[]) => {
        const fft = new FFT(N);
        const out = fft.createComplexArray();
        fft.realTransform(out, signal);
        fft.completeSpectrum(out);
        const mag = [];
        for (let i = 0; i < N / 2; i++) {
          const re = out[2 * i], im = out[2 * i + 1];
          mag.push(Math.sqrt(re * re + im * im));
        }
        return mag;
      };

      // 频率轴（仅 N/2 点）
      // const freq = Array.from({ length: N / 2 }, (_, i) => i * fs / N);
      const freq = Array.from({ length: N / 2 }, (_, i) =>
        parseFloat((i * fs / N).toFixed(2))  // 可改成 .toFixed(0) 只保留整数
      );

      const chartDataFV = {
        x: freq,
        y: [
          { name: '频谱(瞬时角速度)', value: computeFFT(signalVA) }
        ],
      };

      const chartDataFI = {
        x: freq,
        y: [
          { name: '频谱(扭振角速度)', value: computeFFT(signalVB) },
        ],
      };
      const chartDataFA = {
        x: freq,
        y: [
          { name: '频谱(扭振角度)', value: computeFFT(signalVC) },
        ],
      };

      InstFreqRef.value?.setData(chartDataFV);
      TorsFreqRef.value?.setData(chartDataFI);
      AngleFreqRef.value?.setData(chartDataFA);

      // 更新 fullData（用于表格展示）
      const assignFullData = (chartData: typeof chartDataV) => {
        chartData.y.forEach(series => {
          fullData[`${getGroupKey(series.name)}-${series.name}`] = chartData.x.map((xi, i) => ({
            x: xi,
            y: series.value[i]
          }));
        });
      };
      // 分别更新表格数据源
      assignFullData(chartDataV);
      assignFullData(chartDataI);
      assignFullData(chartDataE);
      assignFullData(chartDataFV);
      assignFullData(chartDataFI);
      assignFullData(chartDataFA);

      // 更新右侧表格展示
      updateTable(selectedLine.value);

      if (data.has_more === false) {
        console.log('🔚 数据发送完毕（has_more = false），关闭连接');
        sseInstance?.close();
        sseInstance = null;
        sseGearPulses?.close();
        sseGearPulses = null;
      }
    } catch (err) {
      console.error('❌ JSON 解析失败:', err);
    }
  });

  sseInstance.addEventListener('end', () => {
    console.log('✅ 数据已接收完毕，关闭连接');
    sseInstance?.close();
    sseInstance = null;
  });

  sseGearPulses = new EventSourcePolyfill(`${base_url}/tvs/record_sse/GearPulses/${startISO}/${endISO}`, { headers });

  sseGearPulses.onopen = () => {
    console.log('📡 GearPulses SSE连接已建立');
  };

  sseGearPulses.addEventListener('data', (event) => {
    try {
      const raw = event.data;
      const fixedStr = raw
        .replace(/'/g, '"')
        .replace(/\bTrue\b/g, 'true')
        .replace(/\bFalse\b/g, 'false')
        .replace(/\bNone\b/g, 'null');

      const data = JSON.parse(fixedStr);
      console.log('GearPulses数据:', data.batch);

      const rows = data.batch || [];
      if (rows.length < 2) return;

      // 脉冲图加载
      const pulseChartData = transformGearPulses(rows);
      PulseRef.value?.appendData(pulseChartData, 10000);

      // 填充脉冲表格
      // 单独构造 fullData 所需结构：{ x, y: [...] }
      const xList = pulseChartData[0]?.value.map(item => item[0]) || [];

      // 更新 fullData（用于表格展示）
      const assignFullData = (chartData: typeof chartDataV) => {
        chartData.y.forEach(series => {
          fullData[`${getGroupKey(series.name)}-${series.name}`] = chartData.x.map((xi, i) => ({
            x: xi,
            y: series.value[i]
          }));
        });
      };

      const chartDataPulse = {
        x: xList.map(t => {
          const ms = Math.floor(Number(t) / 1000);
          const d = new Date(ms - 8 * 60 * 60 * 1000);
          const pad = (n: number, len = 2) => String(n).padStart(len, '0');
          return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ` +
            `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}.${pad(d.getMilliseconds(), 3)}`;
        }),
        y: pulseChartData.map(series => ({
          name: series.name,
          value: series.value.map(item => item[1])
        }))
      };

      // 3. ✅ 用于右侧表格展示
      assignFullData(chartDataPulse);

      // 更新右侧表格展示
      updateTable(selectedLine.value);
    } catch (err) {
      console.error('❌ GearPulses 解析失败:', err);
    }
  });

  sseGearPulses.addEventListener('end', () => {
    console.log('✅ GearPulses 数据已接收完毕');
    sseGearPulses?.close();
    sseGearPulses = null;
  });
};
// eventSource.onerror = (err) => {
//   console.error('❌ SSE 错误:', err);
// };
const isExporting = ref(false);
const handleExport = async () => {
  console.log('导出数据:')
  if (!dateRange.value) {
    window.$message?.warning('请先选择时间范围');
    return;
  }
  isExporting.value = true;

  const [start, end] = dateRange.value;
  const startISO = new Date(start).toISOString();
  const endISO = new Date(end).toISOString();

  const recordType = 'GearPhys'; // 🔁 根据你的页面配置修改
  const token = get_token();
  const base_url = import.meta.env.VITE_SERVICE_BASE_URL;

  try {
    const response = await fetch(`${base_url}/tvs/export_csv`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'token': token // 如果后端使用 token 字段做鉴权
      },
      body: JSON.stringify({
        record_type: recordType,
        start_time: startISO,
        end_time: endISO
      })
    });

    if (!response.ok) {
      throw new Error(`导出失败：${response.status}`);
    }

    // 创建 Blob 下载文件
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    const now = new Date();
    const pad = (n: number) => String(n).padStart(2, '0');
    const timestamp = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}_${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(now.getSeconds())}`;

    a.download = `tvs_export_${timestamp}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  } catch (err) {
    console.error('❌ 导出失败:', err);
    window.$message?.error('导出失败');
  } finally {
    isExporting.value = false;
  }
};
onUnmounted(() => {
  if (sseInstance) {
    console.log('🛑 页面卸载，手动关闭 SSE');
    sseInstance.close();
    sseInstance = null;
  }
  if (sseGearPulses) {
    sseGearPulses.close();
    sseGearPulses = null;
  }
});
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <!-- 查询栏 -->
    <div class="flex items-center p-2 gap-2 border-b border-gray-600 bg-black text-white">
      <n-date-picker v-model:value="dateRange" type="datetimerange" size="small" />
      <n-button type="primary" size="small" @click="handleQuery">查询</n-button>
      <n-button size="small" @click="setRecent('hour')">最近一小时</n-button>
      <n-button size="small" @click="setRecent('day')">最近一天</n-button>
      <n-button size="small" @click="setRecent('week')">最近一周</n-button>
      <n-button size="small" @click="setRecent('month')">最近一月</n-button>

      <n-button ghost size="small" @click="handleExport" :disabled="isExporting">{{ isExporting ? '下载中,请稍等...' : '导出数据'
      }}</n-button>
    </div>

    <!-- 图表及右侧面板 -->
    <div class="flex flex-1 overflow-hidden">
      <!-- 图表区 -->
      <div class="flex flex-col flex-1" :style="{
        width:
          showRightPanel && showEventPanel
            ? 'calc(100% - 32rem)'
            : showRightPanel || showEventPanel
              ? 'calc(100% - 16rem)'
              : '100%'
      }">
        <div class="flex h-1/2 border-b border-gray-700">
          <n-tabs v-model:value="tabKey" placement="left" type="line">
            <n-tab-pane name="inst" tab="瞬时角速度">
              <div class="flex h-full">
                <WaveChart ref="InstRef" :options="InstOptions" class="flex-1 border-r" />
                <WaveChart ref="InstFreqRef" :options="InstFreqOptions" class="flex-1" />
              </div>
            </n-tab-pane>
            <n-tab-pane name="tors" tab="扭振角速度">
              <div class="flex h-full">
                <WaveChart ref="TorsRef" :options="TorsOptions" class="flex-1 border-r" />
                <WaveChart ref="TorsFreqRef" :options="TorsFreqOptions" class="flex-1" />
              </div>
            </n-tab-pane>
            <n-tab-pane name="angle" tab="扭振角度">
              <div class="flex h-full">
                <WaveChart ref="AngleRef" :options="AngleOptions" class="flex-1 border-r" />
                <WaveChart ref="AngleFreqRef" :options="AngleFreqOptions" class="flex-1" />
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
        <div class="h-1/2 flex">
          <n-tabs v-model:value="sectabKey" placement="left" type="line">
            <n-tab-pane name="volt" tab="转速脉冲">
              <div class="flex h-full">
                <PulseChart ref="PulseRef" :options="PulseOptions" class="flex-1" />
              </div>
            </n-tab-pane>
          </n-tabs>
        </div>
      </div>

      <!-- 数据表格面板 -->
      <div class="relative h-full">
        <n-tooltip trigger="hover" placement="left">
          <template #trigger>
            <n-button v-show="!showRightPanel" class="expand-icon" size="tiny" text @click="showRightPanel = true"
              type="primary">
              <template #icon>
                <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
              </template>
            </n-button>
          </template>
          展开-数据列表
        </n-tooltip>
        <transition name="slide">
          <div v-show="showRightPanel" class="w-48 h-full border-l border-gray-700 bg-black text-white flex flex-col">
            <div class="flex p-2 gap-1">
              <n-tooltip trigger="hover" placement="right">
                <template #trigger>
                  <n-button quaternary size="small" @click="showRightPanel = false"
                    style="background-color: #2a2a2a; border: 1px solid #888;width: 28px;height:28px;">
                    <template #icon>
                      <img src="@/assets/imgs/TablerChevronRightPipe.svg" class="sicon-white " />
                    </template>
                  </n-button>
                </template>
                收起-数据列表
              </n-tooltip>
              <n-dropdown trigger="click" :options="dropdownOptions" @select="handleSelect">
                <n-button size="small" class="flex-1">选择线路 - {{ selectedLine.split('-')[1] }}</n-button>
              </n-dropdown>
            </div>
            <div class="flex-1 overflow-auto p-2">
              <n-table :bordered="false" :single-line="false" size="small">
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>X</th>
                    <th>Y</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(row, index) in tableData" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ row.x }}</td>
                    <td>{{ row.y }}</td>
                  </tr>
                </tbody>
              </n-table>
            </div>
          </div>
        </transition>
      </div>

      <!-- 事件面板 -->
      <div class="relative h-full">
        <!-- <n-button v-show="!showEventPanel" class="expand-icon" style="top: 260px;" size="tiny" text
          @click="showEventPanel = true" type="primary">
          <template #icon>
            <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
          </template>
        </n-button> -->
        <n-tooltip trigger="hover" placement="left">
          <template #trigger>
            <n-button v-show="!showEventPanel" class="expand-icon" style="top: 200px;" size="tiny" text
              @click="showEventPanel = true" type="primary">
              <template #icon>
                <img src="@/assets/imgs/TablerChevronLeftPipe.svg" class="sicon-white " />
              </template>
            </n-button>
          </template>
          展开-事件列表
        </n-tooltip>

        <transition name="slide">
          <div v-show="showEventPanel" class="w-48 h-full border-l border-gray-700 bg-black text-white flex flex-col">
            <div class="flex p-2 gap-1">
              <n-tooltip trigger="hover" placement="right">
                <template #trigger>
                  <n-button quaternary size="small" @click="showEventPanel = false"
                    style="background-color: #2a2a2a; border: 1px solid #888;width: 28px;height:28px;">
                    <template #icon>
                      <img src="@/assets/imgs/TablerChevronRightPipe.svg" class="sicon-white " />
                    </template>
                  </n-button>
                </template>
                收起-事件列表
              </n-tooltip>
              <span class="text-sm flex-1 text-white">事件列表</span>
            </div>
            <div class="flex-1 overflow-auto p-2">
              <n-table :bordered="false" :single-line="false" size="small">
                <thead>
                  <tr>
                    <th>时间</th>
                    <th>名称</th>
                    <th>类</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, idx) in eventList" :key="idx">
                    <td>{{ item.time }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.type }}</td>
                  </tr>
                </tbody>
              </n-table>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<style scoped>
.expand-icon {
  position: fixed;
  right: 0;
  z-index: 1000;
  width: 28px;
  height: 28px;
  padding: 0;
  background-color: #2a2a2a;
  border: 1px solid #888;
  border-radius: 4px;
}

.sicon-white {
  filter: brightness(0) invert(1);
  width: 16px;
  height: 16px;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

:deep(.n-tabs-tab) {
  padding: 10px 6px 5px 0px !important;
}

:deep(.n-table) {
  font-size: 10px;
}

:deep(.n-tabs-nav) {
  min-width: 78px;
}
</style>

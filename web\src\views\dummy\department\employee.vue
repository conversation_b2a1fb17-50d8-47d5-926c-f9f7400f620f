<!--
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-10 19:30:05
 * @LastEditors: zhai
 * @LastEditTime: 2024-06-10 22:17:59
-->
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { ValueBuilderContext, useCompute, useFs } from '@fast-crud/fast-crud';
import type {
  AddReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  DelReq,
  EditReq,
  UserPageQuery,
  UserPageRes,
  ValueResolveContext
} from '@fast-crud/fast-crud';
import { fast_employee_api as api } from '@/service/api/dummy';
const { asyncCompute, compute } = useCompute();

interface Props {
  department: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  department: null
});

watch(
  () => props.department,
  () => {
    crudExpose.doRefresh();
  }
);

function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery): Promise<UserPageRes> => {
    query.query.department_id = props.department;
    return api.GetList(query);
  };

  const editRequest = async (ctx: EditReq) => {
    const { form, row } = ctx;
    form.id = row.id;
    return api.UpdateObj(form);
  };

  const delRequest = async (ctx: DelReq) => {
    const { row } = ctx;
    return api.DelObj(row.id);
  };

  const addRequest = async (req: AddReq) => {
    const { form } = req;
    form.department_id = props.department;
    return api.AddObj(form);
  };

  return {
    crudOptions: {
      container: {
        // is: 'fs-layout-card'
      },
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest
      },
      rowHandle: {
        width: 150
      },
      search: {
        show: false
      },

      form: {
        wrapper: {
          draggable: false
        }
      },

      columns: {
        id: {
          title: 'ID',
          key: 'id',
          type: 'number',
          column: {
            width: 50
          },
          form: {
            show: false
          }
        },
        name: {
          title: '名称',
          type: 'text',
          search: { show: true },
          column: {
            sorter: 'custom'
          }
        },
        number: {
          title: '工号',
          type: 'text'
        },
        retire: {
          title: '辞退',
          type: 'dict-switch',
          form: {
            value: false
          }
        },
        retire_date: {
          title: '辞退日期',
          type: 'date',
          form: {
            component: {
              show: compute(({ form }) => {
                return form.retire;
              })
            }
          },
          column: {
            component: {
              show: compute(({ row }) => {
                return row.retire;
              })
            }
          }
        }
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

// 页面打开后获取列表数据
onMounted(() => {
  crudExpose.doRefresh();
});
</script>

<template>
  <div class="h-full">
    <NAlert v-if="props.department == null" type="info" closable>从类型列表选择一项后，进行编辑</NAlert>
    <FsCrud v-else ref="crudRef" v-bind="crudBinding"></FsCrud>
  </div>
</template>

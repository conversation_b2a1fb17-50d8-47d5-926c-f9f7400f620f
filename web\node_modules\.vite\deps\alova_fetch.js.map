{"version": 3, "sources": ["../../.pnpm/alova@3.2.6/node_modules/alova/dist/adapter/fetch.esm.js"], "sourcesContent": ["/**\n  * alova 3.2.6 (https://alova.js.org)\n  * Document https://alova.js.org\n  * Copyright 2024 Scott Hu. All Rights Reserved\n  * Licensed under MIT (https://github.com/alovajs/alova/blob/main/LICENSE)\n*/\n\nimport { ObjectCls, JSONStringify, setTimeoutFn, clearTimeoutTimer, promiseReject, newInstance, undefinedValue, isString, isSpecialRequestBody, falseValue, trueValue } from '@alova/shared';\n\nconst isBodyData = (data) => isString(data) || isSpecialRequestBody(data);\nfunction adapterFetch() {\n    return (elements, method) => {\n        const adapterConfig = method.config;\n        const timeout = adapterConfig.timeout || 0;\n        const ctrl = new AbortController();\n        const { data, headers } = elements;\n        const isContentTypeSet = /content-type/i.test(ObjectCls.keys(headers).join());\n        const isDataFormData = data && data.toString() === '[object FormData]';\n        // When the content type is not set and the data is not a form data object, the content type is set to application/json by default.\n        if (!isContentTypeSet && !isDataFormData) {\n            headers['Content-Type'] = 'application/json;charset=UTF-8';\n        }\n        const fetchPromise = fetch(elements.url, {\n            ...adapterConfig,\n            method: elements.type,\n            signal: ctrl.signal,\n            body: isBodyData(data) ? data : JSONStringify(data)\n        });\n        // If the interruption time is set, the request will be interrupted after the specified time.\n        let abortTimer;\n        let isTimeout = falseValue;\n        if (timeout > 0) {\n            abortTimer = setTimeoutFn(() => {\n                isTimeout = trueValue;\n                ctrl.abort();\n            }, timeout);\n        }\n        return {\n            response: () => fetchPromise.then(response => {\n                // Clear interrupt processing after successful request\n                clearTimeoutTimer(abortTimer);\n                // Response's readable can only be read once and needs to be cloned before it can be reused.\n                return response.clone();\n            }, err => promiseReject(isTimeout ? newInstance(Error, 'fetchError: network timeout') : err)),\n            // The then in the Headers function needs to catch exceptions, otherwise the correct error object will not be obtained internally.\n            headers: () => fetchPromise.then(({ headers: responseHeaders }) => responseHeaders, () => ({})),\n            // Due to limitations of the node fetch library, this code cannot be unit tested, but it has passed the test in the browser.\n            /* c8 ignore start */\n            onDownload: async (cb) => {\n                let isAborted = falseValue;\n                const response = await fetchPromise.catch(() => {\n                    isAborted = trueValue;\n                });\n                if (!response)\n                    return;\n                const { headers: responseHeaders, body } = response.clone();\n                const reader = body ? body.getReader() : undefinedValue;\n                const total = Number(responseHeaders.get('Content-Length') || responseHeaders.get('content-length') || 0);\n                if (total <= 0) {\n                    return;\n                }\n                let loaded = 0;\n                if (reader) {\n                    const pump = () => reader.read().then(({ done, value = new Uint8Array() }) => {\n                        if (done || isAborted) {\n                            isAborted && cb(total, 0);\n                        }\n                        else {\n                            loaded += value.byteLength;\n                            cb(total, loaded);\n                            return pump();\n                        }\n                    });\n                    pump();\n                }\n            },\n            onUpload() {\n                // eslint-disable-next-line no-console\n                console.error(\"fetch API does'nt support uploading progress. please consider to change `@alova/adapter-xhr` or `@alova/adapter-axios`\");\n            },\n            /* c8 ignore stop */\n            abort: () => {\n                ctrl.abort();\n                clearTimeoutTimer(abortTimer);\n            }\n        };\n    };\n}\n\nexport { adapterFetch as default };\n"], "mappings": ";;;;;;;;;;;;;;;;AASA,IAAM,aAAa,CAAC,SAAS,SAAS,IAAI,KAAK,qBAAqB,IAAI;AACxE,SAAS,eAAe;AACpB,SAAO,CAAC,UAAU,WAAW;AACzB,UAAM,gBAAgB,OAAO;AAC7B,UAAM,UAAU,cAAc,WAAW;AACzC,UAAM,OAAO,IAAI,gBAAgB;AACjC,UAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,UAAM,mBAAmB,gBAAgB,KAAK,UAAU,KAAK,OAAO,EAAE,KAAK,CAAC;AAC5E,UAAM,iBAAiB,QAAQ,KAAK,SAAS,MAAM;AAEnD,QAAI,CAAC,oBAAoB,CAAC,gBAAgB;AACtC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,UAAM,eAAe,MAAM,SAAS,KAAK;AAAA,MACrC,GAAG;AAAA,MACH,QAAQ,SAAS;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,MAAM,WAAW,IAAI,IAAI,OAAO,cAAc,IAAI;AAAA,IACtD,CAAC;AAED,QAAI;AACJ,QAAI,YAAY;AAChB,QAAI,UAAU,GAAG;AACb,mBAAa,aAAa,MAAM;AAC5B,oBAAY;AACZ,aAAK,MAAM;AAAA,MACf,GAAG,OAAO;AAAA,IACd;AACA,WAAO;AAAA,MACH,UAAU,MAAM,aAAa,KAAK,cAAY;AAE1C,0BAAkB,UAAU;AAE5B,eAAO,SAAS,MAAM;AAAA,MAC1B,GAAG,SAAO,cAAc,YAAY,YAAY,OAAO,6BAA6B,IAAI,GAAG,CAAC;AAAA;AAAA,MAE5F,SAAS,MAAM,aAAa,KAAK,CAAC,EAAE,SAAS,gBAAgB,MAAM,iBAAiB,OAAO,CAAC,EAAE;AAAA;AAAA;AAAA,MAG9F,YAAY,OAAO,OAAO;AACtB,YAAI,YAAY;AAChB,cAAM,WAAW,MAAM,aAAa,MAAM,MAAM;AAC5C,sBAAY;AAAA,QAChB,CAAC;AACD,YAAI,CAAC;AACD;AACJ,cAAM,EAAE,SAAS,iBAAiB,KAAK,IAAI,SAAS,MAAM;AAC1D,cAAM,SAAS,OAAO,KAAK,UAAU,IAAI;AACzC,cAAM,QAAQ,OAAO,gBAAgB,IAAI,gBAAgB,KAAK,gBAAgB,IAAI,gBAAgB,KAAK,CAAC;AACxG,YAAI,SAAS,GAAG;AACZ;AAAA,QACJ;AACA,YAAI,SAAS;AACb,YAAI,QAAQ;AACR,gBAAM,OAAO,MAAM,OAAO,KAAK,EAAE,KAAK,CAAC,EAAE,MAAM,QAAQ,IAAI,WAAW,EAAE,MAAM;AAC1E,gBAAI,QAAQ,WAAW;AACnB,2BAAa,GAAG,OAAO,CAAC;AAAA,YAC5B,OACK;AACD,wBAAU,MAAM;AAChB,iBAAG,OAAO,MAAM;AAChB,qBAAO,KAAK;AAAA,YAChB;AAAA,UACJ,CAAC;AACD,eAAK;AAAA,QACT;AAAA,MACJ;AAAA,MACA,WAAW;AAEP,gBAAQ,MAAM,wHAAwH;AAAA,MAC1I;AAAA;AAAA,MAEA,OAAO,MAAM;AACT,aAAK,MAAM;AACX,0BAAkB,UAAU;AAAA,MAChC;AAAA,IACJ;AAAA,EACJ;AACJ;", "names": []}
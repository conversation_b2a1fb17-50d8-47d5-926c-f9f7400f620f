import sys
import os

from tortoise import Tortoise

# 获取当前文件的绝对路径
curPath = os.path.abspath(os.path.dirname(""))

# 获取上一级文件路径
parentPath = os.path.split(curPath)[0]

# 获取上上级文件路径
grandParentPath = os.path.split(parentPath)[0]

# 将上上级路径加入到搜索路径中
# sys.path.append(grandParentPath)
sys.path.append(curPath)


import asyncio
import atexit
import datetime
from enum import Enum
import json
import random
from apscheduler.jobstores.base import ConflictingIdError
from models.task import SchedulerTask, SchedulerTaskRecord
from settings import APP_SETTINGS, TORTOISE_ORM, REDIS_URL
from task.scheduler import Scheduler
from loguru import logger

# from redis import Redis
from redis.asyncio import Redis

"""
定时任务配置
"""
# 发布/订阅通道，与定时任务程序相互关联，请勿随意更改
TASK_SUBSCRIBE = "task_queue"


class JobOperation(str, Enum):
    ADD = "add_job"
    REMOVE = "remove_job"
    REMOVE_ALL = "remove_all_jobs"
    PAUSE = "pause"
    RESUME = "resume"
    STATUS = "get_job_status"


class JobExecStrategy(str, Enum):
    interval = "interval"
    date = "date"
    cron = "cron"
    once = "once"


class ScheduledTask:

    def __init__(self):
        self.scheduler = None

    def add_job(self, exec_strategy: JobExecStrategy, job_params: dict) -> None:
        """
        添加定时任务
        :param exec_strategy: 执行策略
        :param job_params: 执行参数
        :return:
        """
        job_id = job_params.get("job_id", None)
        error_info = None
        try:
            if exec_strategy == JobExecStrategy.interval:
                self.scheduler.add_interval_job(**job_params)
            elif exec_strategy == JobExecStrategy.cron:
                self.scheduler.add_cron_job(**job_params)
            elif exec_strategy == JobExecStrategy.date:
                self.scheduler.add_date_job(**job_params)
            elif exec_strategy == JobExecStrategy.once:
                # 这种方式会自动执行事件监听器，用于保存执行任务完成后的日志
                job_params["job_id"] = f"{job_id}-temp-{random.randint(1000, 9999)}"
                self.scheduler.add_date_job(**job_params, expression=datetime.datetime.now())
            else:
                raise ValueError("无效的触发器")
        except ConflictingIdError as e:
            # 任务编号已存在，重复添加报错
            error_info = "任务编号已存在"
        except ValueError as e:
            error_info = e.__str__()

        if error_info:
            logger.error(f"任务编号：{job_id}，报错：{error_info}")
            self.error_record(job_id, error_info)
            return False
        return True

    def remove_job(self, job_id: str) -> bool:
        return self.scheduler.remove_job(job_id)

    def remove_all_jobs(self) -> bool:
        return self.scheduler.remove_all_jobs()

    def get_job_status(self, job_id: str) -> str:
        return self.scheduler.get_job_status(job_id)

    def pause(self, job_id: str) -> bool:
        return self.scheduler.pause(job_id)

    def resume(self, job_id: str) -> bool:
        return self.scheduler.resume(job_id)

    async def error_record(self, job_id: str, error_info: str) -> None:
        """
        添加任务失败记录，并且将任务状态改为 False
        :param job_id: 任务编号
        :param error_info: 报错信息
        :return:
        """
        try:
            task = await SchedulerTask.get(id=job_id)
            task.is_active = False
            await task.save()

            result = {
                "job_id": job_id,
                "job_class": task.job_class,
                "name": task.name,
                "group": task.group,
                "exec_strategy": task.exec_strategy,
                "expression": task.expression,
                "start_time": datetime.datetime.now(),
                "end_time": datetime.datetime.now(),
                "process_time": 0,
                "retval": "任务添加失败",
                "exception": error_info,
                "traceback": None,
            }
            await SchedulerTaskRecord.create(**result)
        except ValueError as e:
            logger.error(f"任务编号：{job_id}, 报错：{e}")

    def run(self) -> None:
        """
        启动定时任务
        :return:
        """
        self.scheduler = Scheduler()
        self.scheduler.start()
        print("Scheduler 启动成功")

    def close(self) -> None:
        """
        关闭程序
        :return:
        """
        if self.scheduler:
            self.scheduler.shutdown()

    #####################################################################################################
    # 作为独立进程运行，通过redis消息队列和fast api交互

    async def listen(self) -> None:
        """
        启动监听订阅消息（阻塞）
        :return:
        """

        async with Redis.from_url(REDIS_URL) as redis:

            pubsub = redis.pubsub()
            await pubsub.subscribe(TASK_SUBSCRIBE)

            logger.info("已成功启动程序，等待接收消息...")
            print("已成功启动程序，等待接收消息...")

            # 处理接收到的消息
            async for message in pubsub.listen():
                if message["type"] == "message":
                    data = json.loads(message["data"].decode("utf-8"))

                    msg = data.get("message")
                    reply_id = data.get("reply_id")

                    operation = msg.get("operation")
                    task = msg.get("task")

                    content = f"接收到任务：任务操作方式({operation})，任务详情：{task}"
                    logger.info(content)
                    print(content)

                    result = getattr(self, operation)(**task)
                    print(f"执行结果：{result}")

                    if reply_id:
                        # 将结果返回给客户端
                        await redis.rpush(reply_id, json.dumps(result).encode("utf-8"))

                else:
                    print("意外", message)


async def init_orm():
    await Tortoise.init(
        config=TORTOISE_ORM,
    )
    await Tortoise.generate_schemas()


async def task_main():
    await init_orm()  # 初始化 ORM
    task = ScheduledTask()
    task.run()

    redis_listener_task = asyncio.create_task(task.listen())

    try:
        # 保持事件循环运行
        # await redis_listener_task
        await asyncio.gather(redis_listener_task)

    finally:
        task.close()
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(task_main())

#####################################################################################################


# if __name__ == "__main__":
#     task = ScheduledTask()
#     atexit.register(task.close)

#     task.run()
#     task.listen()


# asyncio.get_event_loop().run_forever()

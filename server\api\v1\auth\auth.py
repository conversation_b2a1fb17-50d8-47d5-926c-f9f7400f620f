from datetime import datetime, timedelta, timezone  # , UTC #3.10

# 3.10
UTC = timezone.utc

from fastapi import APIRouter, Depends
from fastapi_cache import <PERSON>son<PERSON>oder
from fastapi_cache.decorator import cache

from log import log
from api.v1.utils import insert_log
from controllers.user import user_controller
from core.ctx import CTX_USER_ID
from core.dependency import DependAuth, check_token
from models.system import LogDetailType, LogType
from models.system import User, <PERSON>, But<PERSON>, StatusType
from schemas.base import Fail, Success
from schemas.login import CredentialsSchema, JWTOut, JWTPayload
from settings import APP_SETTINGS
from utils.security import create_access_token, get_password_hash, verify_password
from fastapi.security import OAuth2PasswordRequestForm
from schemas.users import UpdatePassword
from fastapi_crud_tortoise import resp_fail

router = APIRouter()


# swagger
@router.post("/token")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    user_obj: User | None = await user_controller.authenticate(
        CredentialsSchema(user_name=form_data.username, password=form_data.password)
    )  # 账号验证, 失败则触发异常返回请求错误
    await user_controller.update_last_login(user_obj.id)
    payload = JWTPayload(
        data={
            "userId": str(user_obj.id),
            "userName": user_obj.user_name,
            "tokenType": "accessToken",
        },
        iat=datetime.now(timezone.utc),
        exp=datetime.now(timezone.utc),
    )
    access_token_payload = payload.model_copy(deep=True)
    access_token_payload.exp += timedelta(minutes=APP_SETTINGS.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(data=access_token_payload)

    return {"access_token": access_token, "token_type": "bearer"}


@router.post("/login", summary="登录")
async def _(credentials: CredentialsSchema):
    user_obj: User | None = await user_controller.authenticate(
        credentials
    )  # 账号验证, 失败则触发异常返回请求错误
    # user_role_code_list = await user_obj.by_user_roles.values_list("role_code", flat=True)
    # all_login_role_codes = ["R_SUPER", "R_ADMIN", "R_CUSTOM"]
    # for user_role_code in user_role_code_list:
    #     if user_role_code in all_login_role_codes:
    #         break
    # else:
    #     log.info(f"用户越权登录, 用户名: {user_obj.user_name}")
    #     return Fail(msg="This user has no permission to login.")

    await user_controller.update_last_login(user_obj.id)
    payload = JWTPayload(
        data={"userId": str(user_obj.id), "userName": user_obj.user_name, "tokenType": "accessToken"},
        iat=datetime.now(UTC),
        exp=datetime.now(UTC),
    )
    access_token_payload = payload.model_copy(deep=True)
    access_token_payload.exp += timedelta(minutes=APP_SETTINGS.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    refresh_token_payload = payload.model_copy(deep=True)
    refresh_token_payload.data["tokenType"] = "refreshToken"
    refresh_token_payload.exp += timedelta(minutes=APP_SETTINGS.JWT_REFRESH_TOKEN_EXPIRE_MINUTES)
    data = JWTOut(
        access_token=create_access_token(data=access_token_payload),
        refresh_token=create_access_token(data=refresh_token_payload),
    )
    log.info(f"用户登录成功, 用户名: {user_obj.user_name}")
    await insert_log(
        log_type=LogType.UserLog, log_detail_type=LogDetailType.UserLoginSuccess, by_user_id=user_obj.id
    )
    return Success(data=data.model_dump(by_alias=True))


@router.post("/refresh-token", summary="刷新认证")
async def _(jwt_token: JWTOut):
    if not jwt_token.refresh_token:
        return Fail(code="4000", msg="The refreshToken is not valid.")
    status, code, data = check_token(jwt_token.refresh_token)
    if not status:
        return Fail(code=code, msg=data)

    user_id = data["data"]["userId"]
    user_obj = await user_controller.get(id=user_id)

    if data["data"]["tokenType"] != "refreshToken":
        return Fail(code="4000", msg="The token is not an refresh token.")

    if user_obj.status_type == StatusType.disable:
        await insert_log(
            log_type=LogType.UserLog, log_detail_type=LogDetailType.UserLoginForbid, by_user_id=user_id
        )
        return Fail(code="4040", msg="This user has been disabled.")

    await user_controller.update_last_login(user_id)
    payload = JWTPayload(
        data={"userId": str(user_obj.id), "userName": user_obj.user_name, "tokenType": "accessToken"},
        iat=datetime.now(UTC),
        exp=datetime.now(UTC),
    )

    access_token_payload = payload.model_copy(deep=True)
    access_token_payload.exp += timedelta(minutes=APP_SETTINGS.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    refresh_token_payload = payload.model_copy(deep=True)
    refresh_token_payload.data["tokenType"] = "refreshToken"
    refresh_token_payload.exp += timedelta(minutes=APP_SETTINGS.JWT_REFRESH_TOKEN_EXPIRE_MINUTES)

    data = JWTOut(
        access_token=create_access_token(data=access_token_payload),
        refresh_token=create_access_token(data=refresh_token_payload),
    )
    await insert_log(
        log_type=LogType.UserLog,
        log_detail_type=LogDetailType.UserAuthRefreshTokenSuccess,
        by_user_id=user_obj.id,
    )
    return Success(data=data.model_dump(by_alias=True))


@cache(expire=60, coder=JsonCoder)
@router.get("/user-info", summary="查看用户信息", dependencies=[DependAuth])
async def _():
    user_id = CTX_USER_ID.get()
    user_obj: User = await user_controller.get(id=user_id)
    data = await user_obj.to_dict(
        exclude_fields=["id", "password", "create_time", "update_time"], lc_case=True
    )

    user_roles: list[Role] = await user_obj.by_user_roles
    user_role_codes = [user_role.role_code for user_role in user_roles]

    user_role_button_codes = (
        [b.button_code for b in await Button.all()]
        if "R_SUPER" in user_role_codes
        else [b.button_code for user_role in user_roles for b in await user_role.by_role_buttons]
    )

    user_role_button_codes = list(set(user_role_button_codes))

    data.update({"userId": user_id, "roles": user_role_codes, "buttons": user_role_button_codes})
    await insert_log(
        log_type=LogType.UserLog, log_detail_type=LogDetailType.UserLoginGetUserInfo, by_user_id=user_obj.id
    )
    return Success(data=data)


@router.post("/updatepassword", summary="更新密码", dependencies=[DependAuth])
async def _(model: UpdatePassword):
    user_id = CTX_USER_ID.get()
    user_obj: User = await user_controller.get(id=user_id)

    verified = verify_password(model.old_password, user_obj.password)
    if not verified:
        return Fail(code="4000", msg="The old password is incorrect.")

    user_obj.password = get_password_hash(password=model.new_password)
    await user_obj.save()

    await insert_log(
        log_type=LogType.UserLog, log_detail_type=LogDetailType.UserUpdatePassword, by_user_id=user_obj.id
    )
    return Success(msg="Password updated successfully")


@router.get("/error", summary="自定义后端错误")  # todo 使用限流器, 每秒最多一次
async def _(code: str, msg: str):
    if code == "9999":
        return Success(code="4040", msg="accessToken已过期")

    return Fail(code=code, msg=f"未知错误, code: {code} msg: {msg}")

# 通过url加入参数tk=<token>，可直接登录该用户并访问url：
# http://localhost:9527/early-warning/overview?tk=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7InVzZXJJZCI6MywidXNlck5hbWUiOiJBZG1pbiIsInRva2VuVHlwZSI6ImFjY2Vzc1Rva2VuIn0sImlhdCI6MTcyOTQyNzAyNCwiZXhwIjoyNTkzNDI3MDI0fQ.KBPUpyapvknV3iYQd8GoyGGIyzXZbHelbvaKNEg3r9k

@router.get("/permanentToken", summary="长期Token", dependencies=[DependAuth])
async def _(days: int):
    user_id = CTX_USER_ID.get()
    user_obj: User = await user_controller.get(id=user_id)

    payload = JWTPayload(
        data={
            "userId": user_obj.id,
            "userName": user_obj.user_name,
            "tokenType": "accessToken",
        },
        iat=datetime.now(timezone.utc),
        exp=datetime.now(timezone.utc),
    )
    access_token_payload = payload.model_copy(deep=True)
    access_token_payload.exp += timedelta(days)
    access_token = create_access_token(data=access_token_payload)

    return {"access_token": access_token, "token_type": "bearer"}

<template>
  <div :style="ledStyle">
    <SevenSegmentDisplay :value="hours" :disable-select="true" />
    <span :style="separatorStyle">{{ separator }}</span>
    <SevenSegmentDisplay :value="minutes" :disable-select="true" />
    <span :style="separatorStyle">{{ separator }}</span>
    <SevenSegmentDisplay :value="seconds" :disable-select="true" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { SevenSegmentDisplay } from 'vue3-seven-segment-display'

const props = defineProps({
  // 时间配置
  time: {
    type: Date,
    default: () => new Date()
  },
  // 样式配置
  styleObj: {
    type: Object,
    default: () => ({
      background: 'rgba(128, 128, 128, 0.2)',
      // boxShadow: '1px 1px 2px rgba(0, 0, 0, 0.5) inset',
      fontSize: '20px',
    })
  },
  // 分隔符配置
  separator: {
    type: String,
    default: '∶'
  },
  // 分隔符样式配置
  separatorStyle: {
    type: Object,
    default: () => ({})
  }
})

const hours = ref('00')
const minutes = ref('00')
const seconds = ref('00')

// 格式化时间显示
const formatTimeValue = (value: number) => {
  return String(value).padStart(2, '0')
}

// 监听时间变化
watch(() => props.time, (newTime) => {
  hours.value = formatTimeValue(newTime.getHours())
  minutes.value = formatTimeValue(newTime.getMinutes())
  seconds.value = formatTimeValue(newTime.getSeconds())
}, { immediate: true })

const ledStyle = computed(() => ({
  whiteSpace: 'nowrap',
  padding: '0.4em 0.6em',
  borderRadius: '2px',
  display: 'inline-block',
  lineHeight: 0,
  ...props.styleObj
}))

const separatorStyle = computed(() => ({
  display: 'inline-block',
  height: '1em',
  lineHeight: '1em',
  verticalAlign: 'top',
  userSelect: 'none',
  ...props.separatorStyle
}))
</script>
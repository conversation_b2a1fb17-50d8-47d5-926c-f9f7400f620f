<template>
	<div :class="[pop ? 'glass' : 'normal']">
		<div ref="panel" class="chart-panel" :class="[pop ? 'popup' : 'normal']">
			<div class="controls-bar">
				<!--<n-button type="primary" size="small" ghost class="rd-0 smallBox" @click="exportCSV(options.title)"
					style="width:40px">导出</n-button>-->
				<!-- <n-switch  v-model="options.autoYAxis" :round="false" size="large" >
					<template #checked>
					自动Y轴
					</template>
<template #unchecked>
					固定Y轴
					</template>
</n-switch> -->

				<n-checkbox v-model:checked="options.autoYAxis"
					class="border w-24 pt-2px pr-5px pb-0px pl-5px smallText smallBox">
					自动Y轴
				</n-checkbox>

				<n-flex :wrap="false" v-if="!options.autoYAxis">
					<n-input-number size="small" class="w-20" v-model:value="options.yAxis.min"
						@update:value="handleChange" :min="-1000" :max="1000" :show-button="false" placeholder="最小值" />
					-
					<n-input-number size="small" class="w-20" v-model:value="options.yAxis.max"
						@update:value="handleChange" :min="-1000" :max="1000" :show-button="false" placeholder="最大值" />
				</n-flex>

				<n-dropdown trigger="click" :options="legends" :render-option="renderDropdownLabel" :show-arrow="true">
					<n-button text>
						<template #icon>
							<icon-emojione:artist-palette />
						</template>
					</n-button>
				</n-dropdown>

			</div>

			<div class="controls-bar-r">
				<n-button v-if="pop" size="small" text @click="on_pop">
					<img src="@/assets/imgs/MaterialSymbolsCloseSmall.svg" class="sicon-white " alt="关闭弹窗">

				</n-button>
				<n-button v-else size="small" text @click="on_pop">
					<template #icon>
						<icon-material-symbols:fullscreen />
					</template>
				</n-button>
			</div>

			<div class="h-full w-full" id="chart-container">
				<div :class="className" :style="{ height, width }" ref="domRef"></div>
			</div>
		</div>
	</div>
</template>


<script setup lang="ts">
//按需导入需要用到的 vue函数 和 echarts
import { onMounted, onBeforeUnmount, defineProps, watch, inject, ref, computed, reactive, nextTick, h, VNode } from 'vue';
import { WaveChart } from './chart';
import { useEcharts } from '@/hooks/common/echarts';
import { DropdownGroupOption, DropdownOption, NColorPicker, NText } from 'naive-ui';


const waveChart = new WaveChart();

// theme
const chartTheme = computed(() => {
	let isDark = props.options.darkMode;
	return { theme: isDark ? 'dark' : '', bgColor: isDark ? 'transparent' : '', color: isDark ? '#dadada' : '#303133' };
});

let colors = ref(waveChart.colorPalette_default);

// 测试不同长度对齐
// let legends = ref(['hello', '哈', '哈哈哈哈', '哈哈', '哈哈哈哈哈哈哈哈哈']);

let legends = ref([]);

const props = defineProps({
	className: {
		type: String,
		default: '',
	},
	width: {
		type: String,
		default: '100%',
	},
	height: {
		type: String,
		default: '300px',
	},
	options: {
		type: Object,
		default: () => {
			return {
				title: '折线图',
				darkMode: false,
				yAxis: {
					type: 'value',
					max: 10,
					min: 0,
				},
				autoYAxis: true,
			};
		},
	},
});

const tempColors = ref<Record<number, string>>({});

const renderDropdownLabel = ({
	node,
	option
}: {
	node: VNode
	option: DropdownOption | DropdownGroupOption
}) => {

	// 初始化临时颜色（如果不存在）
	if (!tempColors.value[option.index]) {
		tempColors.value[option.index] = colors.value[option.index];
	}

	return h(
		'div',
		{
			style: 'display: flex; align-items: center; flex-wrap: nowrap; padding: 6px 10px;',
		},
		[
			h(NText, { style: 'margin-right: 16px;' }, () => option.label),
			h(NColorPicker, {
				style: 'width:26px;',
				showAlpha: false,
				value: tempColors.value[option.index],
				onUpdateValue: (value) => (tempColors.value[option.index] = value),
				renderLabel: (v) => { },
				size: 'small',
				actions: ['confirm'],
				onConfirm: (v) => {
					colors.value[option.index] = v;
				}
			}),
		]
	)
}

const { domRef, updateOptions, setOptions, getOptions, getChart } = useEcharts(() => ({
	color: colors.value,
	backgroundColor: chartTheme.value.bgColor,
	title: {
		text: props.options.title,
		x: 'left',
		textStyle: { fontSize: '12', color: chartTheme.value.color },
	},
	grid: { top: 70, right: 20, bottom: 80, left: 60 },
	tooltip: { trigger: 'axis' },
	// legend: {
	// 	data: [], top: 0, right: 30, type: 'scroll', // ⬅️ 支持滚动 legend，防止溢出
	// 	orient: 'horizontal',
	// 	pageIconColor: '#aaa',
	// 	pageTextStyle: {
	// 		color: '#aaa'
	// 	},
	// },
	legend: {
		data: [],
		type: 'plain',              // 多行展示（非 scroll）
		top: 0,                    // 距顶部距离
		left: 175,                  // ⬅️ legend 左侧距离
		textStyle: {
			color: '#ffffff',         // ⬅️ legend 文字白色
			fontSize: 10
		},
		itemWidth: 16,
		itemHeight: 10,
		// 设置容器高度，溢出后靠样式控制滚动
	},
	xAxis: {
		type: 'category',
		// axisLabel: {
		// 	formatter: function (value: any) {
		// 		const date = new Date(value);
		// 		return date.toLocaleString();
		// 	}
		// },

		boundaryGap: false,
		data: [],
	},
	yAxis: [
		{
			type: 'value',
			name: '值',
			splitLine: { show: true, lineStyle: { type: 'dashed', color: '#f5f5f5' } },
		},
	],
	dataZoom: [
		{
			type: 'inside',
			start: 0,
			end: 100,
		},
		{
			type: 'slider',
			start: 0,
			end: 100,
		},
	],
	series: [],
}));
const emit = defineEmits<{
	(e: 'lineClick', payload: { name: string; componentRef: string }): void;
}>();
const resize = () => {
	const chart = getChart();
	if (chart) chart.resize();
};
onMounted(() => {
	// 关闭loading
	const checkAndUpdate = () => {
		if (getChart() !== null) {
			// 当 getChart 不为 null 时，执行 updateOptions
			updateOptions(opts => {
				opts.xAxis.data = [];
				opts.series = [];
				return opts;
			});
			const chart = getChart();
			// chart.setOption({
			// 	xAxis: {
			// 		type: 'category',
			// 		data: ['a', 'b', 'c']
			// 	},
			// 	series: [
			// 		{
			// 			name: 'Test Line',
			// 			type: 'line',            // ✅ 必须加
			// 			data: [1, 2, 3]
			// 		}
			// 	]
			// });

			chart.on('click', (params) => {
				console.log('📌 点击成功', params);
			});
		} else {
			// 如果 getChart 为 null，继续在 nextTick 中检查，nextTick会导致页面卡死
			setTimeout(checkAndUpdate, 500); // 100ms 后再次检查
		}
	};

	// 开始检查
	checkAndUpdate();
})

const panel = ref(null);
let pop = ref(false);

const on_pop = () => {
	pop.value = !pop.value;
}

const handleChange = (value) => {
	console.log(value);
};

//监听图表数据时候变化，重新渲染图表
watch(
	() => [props.options.darkMode, props.options.title],
	() => {
		const option = {
			backgroundColor: chartTheme.value.bgColor,
			title: {
				text: props.options.title,
				textStyle: { color: chartTheme.value.color },
			},
		};


		if (setOptions)
			setOptions(option);
	},
	{ deep: true, immediate: true }
);

watch(
	() => [props.options.autoYAxis, props.options.yAxis],
	() => {
		if (setOptions == null)
			return;
		if (props.options.autoYAxis) {
			setOptions({
				yAxis: {
					type: 'value',
					max: null,
					min: null,
					interval: null,
				},
			});
		} else {
			setOptions({
				yAxis: props.options.yAxis,
			});
		}
	},
	{ deep: true, immediate: true }
);

watch(
	colors,
	(new_colors) => {
		let options = {
			color: new_colors,
		};

		if (setOptions)
			setOptions(options);
	},
	{ deep: true, immediate: true }
);

const appendData = (data: object, max_len: number = 100000) => {
	let ls = waveChart.__appendData(getChart(), data, max_len);
	// ✅ 处理全 0 的情况
	// const allZero = data.y.every(series => series.value.every(v => v === 0));
	// if (allZero && props.options.autoYAxis) {
	// 	setOptions({
	// 		yAxis: {
	// 			min: -1,
	// 			max: 1
	// 		}
	// 	});
	// }
	legends.value = ls.map((value, index) => {
		return {
			index: index,
			label: value,
		};
	});

};

const setData = (data: object) => {
	let ls = waveChart.__setData(getChart(), data);

	legends.value = ls.map((value, index) => {
		return {
			index: index,
			label: value,
		};
	});
};

const clear = () => {
	let ls = waveChart.__clear(getChart());

	legends.value = ls.map((value, index) => {
		return {
			index: index,
			label: value,
		};
	});
};

const exportCSV = (filename) => {
	waveChart.__exportCSV(getChart(), filename);
};

defineExpose({
	setData,
	appendData,
	clear,
	exportCSV,
	resize,
});
</script>

<style>
.el-color-picker__trigger {
	border: none;
}
</style>

<style scoped lang="scss">
.chart-panel {
	padding: 8px !important;
	transition-property: position, width, height, border-radius;
	transition-duration: 0.5s;
	transition-timing-function: ease-in;
	transition-delay: 0;
	position: relative;
}

.normal {
	height: 100%;
}

.glass {
	position: fixed !important;
	left: 0 !important;
	right: 0 !important;
	top: 0 !important;
	bottom: 0 !important;
	z-index: 999;
	backdrop-filter: blur(20px);
	color: #fff;
}

.popup {
	background-color: black;
	position: absolute !important;

	// relative、fixed、

	left: 30px !important;
	right: 30px !important;
	top: 30px !important;
	bottom: 30px !important;
	z-index: 1000;
	border: 1px solid #f1f2f3;
	box-shadow: 0 2px 12px #0000001a;
}

// .menu-item {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   padding: 8px 12px; /* 增加内边距，提升点击区域 */
// }
.menu-item {
	min-width: 200px;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;

	align-items: center;
	justify-content: space-between;
	margin: 2px 0;
	width: 100%;
	z-index: 1000;
}

.controls-bar {
	display: flex;
	justify-content: flex-start;
	gap: 8px;
	position: absolute;
	top: 6px;
	left: 40px;
	z-index: 100;
}

.controls-bar-r {
	display: flex;
	justify-content: flex-start;
	position: absolute;
	top: 16px;
	right: 20px;
	z-index: 100;
}

.sicon-white {
	filter: brightness(0) invert(1);
	width: 32px;
	height: 32px;
}

.smallBox {
	height: 22px;
	font-size: 11px !important;
	padding: 2px;
	margin: 0px;
}

.smallText {
	width: 70px;
}

:deep(.n-checkbox__label) {
	padding: 0px 3px;
}
</style>
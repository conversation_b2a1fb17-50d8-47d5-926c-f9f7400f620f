from .config import Settings

APP_SETTINGS = Settings()

TORTOISE_ORM = APP_SETTINGS.TORTOISE_ORM
REDIS_URL = APP_SETTINGS.REDIS_URL

# 调试信息
print("BASE_DIR:", APP_SETTINGS.BASE_DIR)

print(f"REDIS_URL: {REDIS_URL}")

print("DB_ENGINE:", APP_SETTINGS.DB_ENGINE)

if APP_SETTINGS.DB_ENGINE == "sqlite":
    print("DB_FILE:", APP_SETTINGS.DB_FILE)
else:
    print("DB_HOST:", APP_SETTINGS.DB_HOST)
    print("DB_PORT:", APP_SETTINGS.DB_PORT)
    print(f"DB_NAME: {APP_SETTINGS.DB_NAME}")

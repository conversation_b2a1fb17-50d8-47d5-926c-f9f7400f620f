<script lang="ts" setup>
import { ref } from 'vue';

import Department from './department.vue';
import Employee from './employee.vue';

const department_id = ref<number | null>(null);

function onSelectType(value: number | null) {
  department_id.value = value;
}
</script>

<template>
  <NFlex vertical class="h-full">
    <NGrid class="flex-grow" cols="1 s:1 m:1 l:2 xl:2 2xl:2" responsive="screen" :x-gap="12">
      <NGi span="1">
        <NCard title="部门" class="h-full" size="small" :segmented="{ content: true }">
          <Department @select-type="onSelectType"></Department>
        </NCard>
      </NGi>
      <NGi span="1">
        <NCard title="员工" class="h-full" size="small" :segmented="{ content: true }">
          <Employee :department="department_id"></Employee>
        </NCard>
      </NGi>
    </NGrid>
  </NFlex>
</template>

<script setup lang="ts">
import { computed, h, onMounted, onUnmounted, reactive, ref } from 'vue';

import { ApiResponse } from '@/service/crud-api';
import { fast_terminals_api, TerminalCard } from '@/service/api/terminals';
import { fast_blocks_api } from '@/service/api/blocks';
import { fast_schemas_api } from '@/service/api/schemas';

import SevenSegmentClock from '@/components/sevenSegClock/index.vue'
import WaveChart from '@/components/waveChart/index.vue';

import dayjs from 'dayjs';
import durationPlugin from 'dayjs/plugin/duration';
import { SelectBaseOption } from 'naive-ui/es/select/src/interface';
import channel204 from './channel204.vue';
import channel7018 from './channel7018.vue';

dayjs.extend(durationPlugin);

import { CardInfo, useIotStore } from '@/store/iot';
import { NIcon, useMessage } from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useThemeStore } from '@/store/modules/theme';
import { MqttRpc } from '@/mqtt/mqtt-rpc';
import { localStg } from '@/utils/storage';
import { valid } from 'cron-validate/lib/result';
import { updateCardInfo } from '@/mqtt';
import { time } from 'console';
import { useEventStore } from '@/store/event';
import { useMQTT } from 'mqtt-vue-hook';
import { decode, encode } from '@msgpack/msgpack';

const eventStore = useEventStore();
const themeStore = useThemeStore();
const message = useMessage();
const mqttHook = useMQTT();

const { SvgIconVNode } = useSvgIcon();

// SvgIconVNode({ icon: 'ant-design:close-outlined', fontSize: 18 }); // iconify
// SvgIconVNode({ localIcon: 'custom-icon' }); // 本地svg图标

const iotStore = useIotStore();

const time_zero = new Date(2025, 1, 1, 0, 0, 0);
const channelData = ref(null);
let channel_last_time = 0;
let timer = null;


let chartRefC = ref();
let chartRefT = ref();
let chartRefP = ref();
let chartRefXY = ref();

let options_c = reactive({
  title: '通道',
  darkMode: false,
  yAxis: {
    type: 'value',
    max: 10,
    min: 0,
  },
  autoYAxis: true,
});

let options_t = reactive({
  title: '时域',
  darkMode: false,
  yAxis: {
    type: 'value',
    max: 10,
    min: 0,
  },
  autoYAxis: true,
});

let options_p = reactive({
  title: '频域',
  darkMode: false,
  yAxis: {
    type: 'value',
    max: 10,
    min: 0,
  },
  autoYAxis: true,
});

let options_xy = reactive({
  title: 'FREE',
  darkMode: false,
  yAxis: {
    type: 'value',
    max: 10,
    min: 0,
  },
  autoYAxis: true,
});


function update_card_status(data) {
  if (data.client_id == cardState.cur_card?.client_id) {
    if (!data.recorder) {
      return;
    }
    if (data.recorder.running) {
      let total_sec = data.recorder.total_sec;
      recState.days = Math.floor(total_sec / 24 / 3600);
      recState.seconds = new Date(time_zero.getTime() + (total_sec - recState.days * 24 * 3600) * 1000);
      recState.name = data.recorder.name;
    }
  }
}

const { fsamplingState, cardState, onCardSlect, getCardStatus, renderCardList, schemaState, run_time, card_running, record_running } = (() => {
  const fsamplingState = reactive({
    val: 2000,
    options: [
      {
        label: '1000',
        value: 1000,
      },
      {
        label: '2000',
        value: 2000,
      },
      {
        label: '4000',
        value: 4000,
      },
      {
        label: '8000',
        value: 8000,
      },
      {
        label: '16000',
        value: 16000,
      },
      {
        label: '32000',
        value: 32000,
      },
      {
        label: '64000',
        value: 64000,
      },
      {
        label: '128000',
        value: 128000,
      }
    ]
  })


  const cardState = reactive({
    card_list: [] as CardInfo[],
    cur_card: null as CardInfo | null,
    cur_card_id: null,
  });


  const renderCardList = (option: SelectOption): VNodeChild => {
    return [
      h(
        NIcon,
        {
          style: {
            marginRight: '10px'
          }
        },
        option.connected ? SvgIconVNode({ icon: "ri:wifi-line", fontSize: 18, color: themeStore.themeColors.primary }) : SvgIconVNode({ icon: "ri:wifi-off-line", fontSize: 18 })
      ),
      option.name as string
    ]
  }


  const getCardStatus = () => {
    let topic = `rpc/terminal/cmd/${cardState.cur_card.client_id}`
    let data = { cmd: 'status', param: {} };
    MqttRpc.Instance.publish(topic, data,
      () => {
      },
      (ret) => {
        if (ret) {
          update_card_status(ret);
          message.success('已获取终端最新状态');
        }
      })
  }


  var last_topic = null;

  const onCardSlect = (val, option) => {
    cardState.cur_card = option;

    if (cardState.cur_card?.connected) {
      getCardStatus();
    }

    if (cardState.cur_card?.client_id) {
      const topic = `terminal/${cardState.cur_card.client_id}/data/channel`;
      if (last_topic != topic) {
        if (last_topic != null) {
          mqttHook.unSubscribe(last_topic);
        }
        last_topic = topic;

        if (topic) {
          mqttHook.subscribe([topic], 1, {}, () => {
            console.log('subscribed!');
          });
        }
      }
    }
  }
  const schemaState = reactive({
    schema_list: [],
    cur_schema_id: null,
    cur_schema: null
  })

  const run_time = computed(() => {
    let t_run = new Date(time_zero);
    if (!cardState.cur_card)
      return t_run;

    if (cardState.cur_card.seconds == null) {
      cardState.cur_card.seconds = 0;
    }

    t_run.setSeconds(t_run.getSeconds() + cardState.cur_card.seconds);
    return t_run;
  });

  const card_running = computed(() => {
    return cardState.cur_card?.connected && cardState.cur_card?.started;
  });

  const record_running = computed(() => {
    return cardState.cur_card?.connected && cardState.cur_card?.recorder?.running;
  });

  return { fsamplingState, cardState, onCardSlect, getCardStatus, renderCardList, schemaState, run_time, card_running, record_running }
})();


const recState = reactive({
  name: "新纪录",
  description: "",
  days: 0,
  seconds: new Date(time_zero.getTime() + 60 * 1000) // 默认1分钟
});

const rec_percent = computed(() => {
  if (record_running.value) {
    let per = Math.min(100, Math.ceil(cardState.cur_card.recorder.record_sec / cardState.cur_card.recorder.total_sec * 100));
    if (per == 100 && cardState.cur_card.recorder.record_sec < cardState.cur_card.recorder.total_sec) {
      per = 99;
    }

    return per;
  }

  return 0;
});




const onCardStart = async () => {
  localStg.set('remote_default_card_id', cardState.cur_card.id);
  localStg.set('remote_default_schema_id', schemaState.cur_schema_id);
  localStg.set('remote_default_fsampling', fsamplingState.val);
  localStg.set('remote_default_channel', channelData.value);

  const channel_data_pure = channelData.value.map(channel => ({
    id: channel.id,
    on: channel.on,
    range: channel.range,
    input_type: channel.input_type,
    Sensitivity: channel.Sensitivity,
    Units: channel.Units,
    show: channel.show,
    save: channel.save
  }));

  // console.log(channel_data_pure)

  let scripts = null;
  let schemas = null;
  if (schemaState.cur_schema) {
    let res = await fast_blocks_api.query({ status: 1 }).catch(err => { });
    if (res) {
      scripts = res.data.data.map(d => d.code);
      schemas = schemaState.cur_schema.schemas;
    }
  }

  let topic = `rpc/terminal/cmd/${cardState.cur_card.client_id}`
  let data = {
    cmd: 'start',
    param: {
      sample_rate: fsamplingState.val,
      channels: channel_data_pure,
      scripts: scripts,
      schemas: schemas
    }
  };

  MqttRpc.Instance.publish(topic, data,
    () => {
      // ElMessage.success('启动命令发送成功');
    },
    (ret) => {
      if (ret) {
        cardState.cur_card.started = true;
        cardState.cur_card.seconds = 0;

        message.success('终端已启动');

        timer = setInterval(() => {
          if (cardState.cur_card?.started) {
            cardState.cur_card.seconds++;
          }

          if (cardState.cur_card?.recorder?.running) {
            cardState.cur_card.recorder.record_sec++;
            if (cardState.cur_card.recorder.record_sec > cardState.cur_card.recorder.total_sec) {
              cardState.cur_card.recorder.running = false;
            }
          }
        }, 1000)

      } else {
        message.error('终端启动失败');
      }
    })

}



const onCardStop = () => {
  let topic = `rpc/terminal/cmd/${cardState.cur_card.client_id}`
  let data = { cmd: 'stop' };

  // mqttHook.publish(topic, encode(data), 1, {}, () => {
  // 	console.log('terminal/set published!');
  // });

  MqttRpc.Instance.publish(topic, data,
    () => {
      // ElMessage.success('停止命令发送成功');
    },
    (ret) => {
      if (ret) {
        cardState.cur_card.started = false;
        if (timer) {
          clearInterval(timer);
          cardState.cur_card.seconds = 0;
          timer = null;
        }
        message.success('终端已停止');
      } else {
        message.error('终端停止失败');
      }
    })
}


const onRecordStart = () => {
  const seconds = (recState.seconds - time_zero) / 1000 + recState.days * 24 * 3600;

  let topic = `rpc/terminal/cmd/${cardState.cur_card.client_id}`
  let data = {
    cmd: 'record_start',
    param: {
      name: recState.name,
      description: recState.description,
      total_sec: seconds,
    }
  };

  MqttRpc.Instance.publish(topic, data,
    () => {
      // message.success('停止命令发送成功');
    },
    (ret) => {
      if (ret) {
        cardState.cur_card.recorder = {
          name: recState.name,
          running: true,
          total_sec: seconds,
          record_sec: 0
        }

        message.success('开始记录');
      } else {
        message.error('启动记录失败');
      }
    })
};

const onRecordStop = () => {
  let topic = `rpc/terminal/cmd/${cardState.cur_card.client_id}`
  let data = { cmd: 'record_stop', param: {} };
  MqttRpc.Instance.publish(topic, data,
    () => {
    },
    (ret) => {
      if (ret) {
        cardState.cur_card.recorder.running = false;
        message.success('停止记录');
      } else {
        message.error('停止记录失败');
      }
    })
};


onMounted(() => {
  eventStore.on('terminal/beat', update_card_status);

  const default_card_id = localStg.get('remote_default_card_id');
  const default_schema_id = localStg.get('remote_default_schema_id');
  const default_fsampling = localStg.get('remote_default_fsampling');
  const default_channel = localStg.get('remote_default_channel');

  fsamplingState.val = default_fsampling || 2000;
  channelData.value = default_channel || null;

  fast_terminals_api.list()
    .then((res: ApiResponse<TerminalCard>) => {
      for (const card of res.data.data) {
        const ret_card = updateCardInfo({ client_id: card.client_id, id: card.id, name: card.name });
        if (ret_card) {
          cardState.card_list.push(ret_card)
        }
      }

      // 默认选择上次的卡
      if (default_card_id) {
        const card = cardState.card_list.find(card => card.id == default_card_id);
        if (card) {
          cardState.cur_card = card;
          cardState.cur_card_id = default_card_id;
          getCardStatus();
        }
      }
    })


  fast_schemas_api.list()
    .then((res) => {
      schemaState.schema_list = res.data.data;

      // 默认选择上次的schema
      if (default_schema_id) {
        const schema = schemaState.schema_list.find(schema => schema.id == default_schema_id);
        if (schema) {
          schemaState.cur_schema = schema;
          schemaState.cur_schema_id = default_schema_id;
        }
      }
    })
    .finally(() => { });

  mqttHook.registerEvent(
    "terminal/+/data/channel",
    (topic, message) => {
      if (!cardState.cur_card) {
        return;
      }

      const match = topic.match(/terminal\/([^\/]*)\//);
      const client_id = match ? match[1] : null;
      if (client_id != cardState.cur_card.client_id) {
        return;
      }
      // ② no webworker
      let max_len = 50000;
      // slideLinechart(state.global.chartWave, message, max_len);
      let sensorData = decode(message);
      console.log(sensorData)
      let cur_time = sensorData.timestamp;

      let chartdata_t = { x: [], y: [] }
      let data_len = 0;
      for (const ch in sensorData.channels) {
        data_len = sensorData.channels[ch].length;
        chartdata_t.y.push({ name: `通道${ch}`, value: sensorData.channels[ch] })
      }

      if (data_len > 0) {
        const step = (cur_time - channel_last_time) / (data_len - 1);
        chartdata_t.x = Array.from({ length: data_len }, (_, index) => channel_last_time + index * step);
        chartRefC.value.appendData(chartdata_t, 10000);
        console.log(chartdata_t.x)
      }

      channel_last_time = cur_time;
    },
    'string_key'
  );


  mqttHook.registerEvent(
    'terminal/+/data/block',
    (topic, message) => {
      if (!cardState.cur_card) {
        return;
      }

      const match = topic.match(/terminal\/([^\/]*)\//);
      const client_id = match ? match[1] : null;
      if (client_id != cardState.cur_card.client_id) {
        return;
      }

      console.log('terminal/data/block');


      // ② no webworker
      let max_len = 50000;
      // slideLinechart(state.global.chartWave, message, max_len);
      let sensorData = decode(message);

      // 时域
      if (sensorData.t.length) {
        var chartdata_t = {}
        chartdata_t.x = sensorData.t[0].value.x;
        chartdata_t.y = [];

        for (const it of sensorData.t) {
          chartdata_t.y.push({ name: it.name, value: it.value.y })
        }

        chartRefT.value.appendData(chartdata_t, 10000);
      }
      // 频域
      if (sensorData.p.length) {
        var chartdata_p = {}
        chartdata_p.x = sensorData.p[0].value.x;
        chartdata_p.y = [];

        for (const it of sensorData.p) {
          chartdata_p.y.push({ name: it.name, value: it.value.y })
        }

        chartRefP.value.setData(chartdata_p);
      }
      // FREE
      if (sensorData.xy.length) {
        chartRefXY.value.appendData(sensorData.xy, 10000);
        // chartRefXY.value.setData(sensorData.xy);
      }


    },
    'string_key'
  );

});

onUnmounted(() => {
  eventStore.off('terminal/beat', update_card_status);
  mqttHook.unRegisterEvent('terminal/+/data/channel', 'string_key');
});

</script>

<template>
  <NFlex vertical class=" pa-10px">
    <NCard title="远程终端" size="small">
      <NForm ref="formRef" :model="form" :rules="rules" label-placement="left" label-width="80px">
        <NGrid :cols="24" :x-gap="24">
          <NFormItemGi :span="6" label="采集卡">
            <NSelect placeholder="Select" @update:value="onCardSlect" :options="cardState.card_list" label-field="name"
              value-field="id" v-model:value="cardState.cur_card_id" :render-label="renderCardList" />
          </NFormItemGi>
          <NFormItemGi :span="6" label="采样率">
            <NSelect :disabled="card_running" placeholder="Select" v-model:value="fsamplingState.val"
              :options="fsamplingState.options" />
          </NFormItemGi>
          <NFormItemGi :span="6" label="数据流">
            <NSelect :disabled="card_running" placeholder="Select" v-model:value="schemaState.cur_schema_id"
              :options="schemaState.schema_list" label-field="name" value-field="id"
              @update:value="(val, option) => schemaState.cur_schema = option" />
          </NFormItemGi>
          <NFormItemGi :span="6" label="" path="selectValue">
            <NFlex :wrap="false" class="flex-items-center">
              <NButton v-if="cardState.cur_card?.started" type="primary" @click="onCardStop()">停止</NButton>
              <NButton v-else type="primary" @click="onCardStart" :disabled="!cardState.cur_card?.connected">启动
              </NButton>
              <SevenSegmentClock :time="run_time" />
            </NFlex>
          </NFormItemGi>
          <NFormItemGi :span="6" label="记录名称">
            <NInput :disabled="record_running" placeholder="记录名称" v-model:value="recState.name" />
          </NFormItemGi>
          <NFormItemGi :span="6" label="记录描述" path="selectValue">
            <NInput :disabled="record_running" placeholder="记录描述" v-model:value="recState.description" />
          </NFormItemGi>
          <NFormItemGi :span="6" label="记录时长" path="selectValue">
            <n-input-number :disabled="record_running" v-model:value="recState.days" :show-button="false"
              class="w-80px mr-5px">
              <template #suffix>
                天
              </template>
            </n-input-number>
            <NTimePicker :disabled="record_running" :actions="['confirm']" v-model:value="recState.seconds"
              format="HH:mm:ss" />
          </NFormItemGi>
          <NFormItemGi :span="6" label="" path="selectValue">
            <NFlex :wrap="false" class="w-full flex-items-center">
              <NButton v-if="record_running" type="primary" @click="onRecordStop()">停录
              </NButton>
              <NButton v-else type="primary" @click="onRecordStart()" :disabled="!card_running">记录
              </NButton>
              <NProgress type="line" :percentage="rec_percent" :height="24" :border-radius="4" :fill-border-radius="0"
                indicator-placement="inside" />
            </NFlex>
          </NFormItemGi>
        </NGrid>
      </NForm>
      <NDivider class="mt-10px mb-0px" />
      <NCollapse arrow-placement="right" :accordion="true" default-expanded-names="1">
        <NCollapseItem title="通道设置" name="1">
          <!-- <channel204 ref="channel204Ref" /> -->
          <channel7018 v-model:ch="channelData" />
        </NCollapseItem>
      </NCollapse>
    </NCard>

    <NCard size="small" class="h-400px">
      <WaveChart ref="chartRefC" height="100%" :options="options_c"></WaveChart>
    </NCard>

    <n-grid x-gap="8" y-gap="8" :cols="2">
      <n-gi>
        <NCard class="h-400px">
          <WaveChart ref="chartRefT" height="100%" :options="options_t"></WaveChart>
        </NCard>
      </n-gi>
      <n-gi>
        <NCard class="h-400px">
          <WaveChart ref="chartRefP" height="100%" :options="options_p"></WaveChart>
        </NCard>
      </n-gi>
      <n-gi>
        <NCard class="h-400px">
          <WaveChart ref="chartRefXY" height="100%" :options="options_xy"></WaveChart>
        </NCard>
      </n-gi>

    </n-grid>

  </NFlex>
</template>
<style scoped>
:deep(.fs-container .header) {
  display: none !important;
}

.clock {
  color: dimgray;
  font-size: 26px;
  font-family: "Orbitron-Regular";
  letter-spacing: 7px;
  white-space: nowrap;
}

.seven-segment-display {
  color: dimgray;
  font-size: 26px;
  font-family: "Orbitron-Regular";
  letter-spacing: 7px;
  white-space: nowrap;
}
</style>
<script setup lang="ts">
import { defineOptions, defineProps, onMounted, watch } from 'vue';
import type { CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { ValueBuilderContext, dict, useFs } from '@fast-crud/fast-crud';


// 使用defineModel，直接定义属性名为ch
const ch = defineModel<Array<Record<string, any>>>('ch');


function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  return {
    crudOptions: {
      container: {
        // is: 'fs-layout-card'
      },
      mode: {
        name: 'local',
        isMergeWhenUpdate: true,
        isAppendWhenAdd: true
      },
      settings: {
        viewFormUseCellComponent: true
      },
      table: {
        striped: true,
        editable: {
          mode: 'free',
          activeDefault: true,
          showAction: false
        }
      },

      rowHandle: {
        show: false
      },
      search: {
        show: false
      },
      toolbar: {
        show: false
      },
      actionbar: {
        show: false
      },
      pagination: {
        show: false
      },
      form: {
        wrapper: {
          draggable: false
        },
        col: {
          span: 24,
        }
      },
      columns: {
        id: {
          title: '通道',
          type: 'number',
          column: { width: 80, align: 'center' },
          // 以下两种方式都可以禁止编辑
          form: {
            show: false
          }
        },
        on: {
          title: '开启',
          type: 'dict-switch',
          column: { width: 80, align: 'center' }
        },
        range: {
          title: '范围',
          type: 'dict-select',
          form: {
            component: {
              clearable: false,
            },
          },
          dict: dict({
            value: 'value',
            label: 'label',
            data: [
              {
                label: '-10V ~ +10V',
                value: 0
              },
              {
                label: '-5V ~ +5V',
                value: 1
              }
            ]
          })
        },
        input_type: {
          title: '输入类型',
          type: 'dict-select',
          form: {
            component: {
              clearable: false,
            },
          },
          dict: dict({
            value: 'value',
            label: 'label',
            data: [
              {
                label: '差分ADC',
                value: 0
              },
              {
                label: '单端ADC',
                value: 1
              },
              {
                label: '单端IEPE',
                value: 2
              }
            ]
          })
        },

        Sensitivity: {
          title: '灵敏度',
          type: 'number'
        },

        Units: {
          title: '单位',
          type: 'dict-select',
          form: {
            component: {
              clearable: false,
            },
          },
          dict: dict({
            value: 'value',
            label: 'label',
            data: [
              {
                label: 'mvolts/g',
                value: 'mvolts/g'
              },
              {
                label: 'volts/g',
                value: 'volts/g'
              },
              {
                label: 'mv/pa',
                value: 'mv/pa'
              },
              {
                label: 'mvolts/N',
                value: 'mvolts/N'
              },
              {
                label: 'mvolts/lb',
                value: 'mvolts/lb'
              }
            ]
          })
        },
        show: {
          title: '显示',
          type: 'dict-switch',
          column: { width: 80, align: 'center' }
        },
        save: {
          title: '保存',
          type: 'dict-switch',
          column: { width: 80, align: 'center' }
        },
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

defineExpose({
  getData: () => crudBinding.value.data
});

// 本地数据
crudBinding.value.data = [
  {
    id: 0,
    on: true,
    range: 0,
    input_type: 2,
    Sensitivity: 1.0,
    Units: 'volts',
    show: true,
    save: true
  },
  {
    id: 1,
    on: true,
    range: 0,
    input_type: 2,
    Sensitivity: 1.0,
    Units: 'volts',
    show: true,
    save: true
  },
  {
    id: 2,
    on: true,
    range: 0,
    input_type: 2,
    Sensitivity: 1.0,
    Units: 'volts',
    show: true,
    save: true
  },
  {
    id: 3,
    on: true,
    range: 0,
    input_type: 2,
    Sensitivity: 1.0,
    Units: 'volts',
    show: true,
    save: true
  },
  {
    id: 4,
    on: true,
    range: 0,
    input_type: 2,
    Sensitivity: 1.0,
    Units: 'volts',
    show: true,
    save: true
  },
  {
    id: 5,
    on: true,
    range: 0,
    input_type: 2,
    Sensitivity: 1.0,
    Units: 'volts',
    show: true,
    save: true
  },
  {
    id: 6,
    on: true,
    range: 0,
    input_type: 2,
    Sensitivity: 1.0,
    Units: 'volts',
    show: true,
    save: true
  },
  {
    id: 7,
    on: true,
    range: 0,
    input_type: 2,
    Sensitivity: 1.0,
    Units: 'volts',
    show: true,
    save: true
  }
];

// 监听props变化，更新本地数据
watch(() => ch.value, (newVal) => {
  if (newVal) {
    crudBinding.value.data = newVal;
  }
}, { immediate: true });

// 监听本地数据变化，更新父组件数据
watch(() => crudBinding.value.data, (newVal) => {
  ch.value = newVal;
}, { deep: true });

onMounted(() => {
  crudExpose.doRefresh();
  crudExpose.editable.enable({ mode: 'free', activeDefault: true });
})

</script>

<template>
  <FsCrud ref="crudRef" v-bind="crudBinding"></FsCrud>
</template>

<style scoped>
/* 在父组件才有效 */
:deep(.fs-container .header) {
  display: none !important;
}
</style>

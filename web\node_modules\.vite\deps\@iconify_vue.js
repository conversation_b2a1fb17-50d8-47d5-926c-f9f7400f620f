import {
  Icon,
  _api,
  addAPIProvider,
  addCollection,
  addIcon,
  calculateSize,
  disableCache,
  enableCache,
  getIcon,
  iconLoaded,
  iconToSVG,
  listIcons,
  loadIcon,
  loadIcons,
  replaceIDs,
  setCustomIconLoader,
  setCustomIconsLoader
} from "./chunk-RNNO2EIX.js";
import "./chunk-7RC5I7IX.js";
import "./chunk-TID6LRNE.js";
import "./chunk-ULBN3QDT.js";
export {
  Icon,
  _api,
  addAPIProvider,
  addCollection,
  addIcon,
  iconToSVG as buildIcon,
  calculateSize,
  disableCache,
  enableCache,
  getIcon,
  iconLoaded as iconExists,
  iconLoaded,
  listIcons,
  loadIcon,
  loadIcons,
  replaceIDs,
  setCustomIconLoader,
  setCustomIconsLoader
};



class PulseChart {
    private y_buf: { [key: string]: [number, number][] } = {};

    // Static color palettes
    public static colorPalette_macarons = [
        '#2ec7c9', '#b6a2de', '#5ab1ef', '#ffb980', '#d87a80',
        '#8d98b3', '#e5cf0d', '#97b552', '#95706d', '#dc69aa',
        '#07a2a4', '#9a7fd1', '#588dd5', '#f5994e', '#c05050',
        '#59678c', '#c9ab00', '#7eb00a', '#6f5553', '#c14089',
    ];

    public static colorPalette_dark = [
        '#4992ff', '#7cffb2', '#fddd60', '#ff6e76', '#58d9f9',
        '#05c091', '#ff8a45', '#8d48e3', '#dd79ff'
    ];

    public static colorPalette_default = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
    ];

    constructor() {
        // Initialize any required properties
    }

    public __appendData(chart: any, data: Array<{ name: string, value: [number, number][] }>, max_len: number = 100000) {
        var legend_data: string[] = [];
        var series_data: any[] = [];

        for (const series of data) {
            const seriesName = series.name;
            legend_data.push(seriesName);
            
            // Initialize series buffer if it doesn't exist
            if (!this.y_buf[seriesName]) {
                this.y_buf[seriesName] = [];
            }
            
            // Add new data points
            this.y_buf[seriesName].push(...series.value);
            
            // Trim to max length if needed
            if (this.y_buf[seriesName].length > max_len) {
                this.y_buf[seriesName] = this.y_buf[seriesName].slice(-max_len);
            }
            
            series_data.push({
                name: series.name,
                type: 'line',
                step: 'start',
                data: this.y_buf[series.name],
                // sampling: 'lttb',
                smooth: false,
                symbol: 'none',
                // showSymbol: false,
                large: true,
                largeThreshold: 1000,
                itemStyle: {
                    lineStyle: {
                        width: 1, //折线宽度
                    },
                },
            });
        }

        // 清理多余的series
        var series_del = false;

        var chart_option = chart.getOption();
        for (var i = chart_option.series.length - 1; i >= 0; i--) {
            // if (!data.y.find(item => item.name == chart_option.series[i].name)) {
            if (!legend_data.includes(chart_option.series[i].name)) {
                series_del = true;
                // chart_series.splice(i, 1);
            }
        }

        if (series_del) {
            chart_option.xAxis.data = this.x_buf;
            chart_option.series = series_data;
            chart.setOption(chart_option, true)
        }
        else {
        chart.setOption({
                xAxis: {
                    data: this.x_buf,
                },
            legend: {
                data: legend_data,
            },
            series: series_data,
            });
        }

        return legend_data;
    };

    public __setData(chart: any, data: Array<{ name: string, value: [number, number][] }>) {
        var legend_data: string[] = [];
        var series_data: any[] = [];
        
        // Clear all existing data
        this.y_buf = {};
        
        for (const series of data) {
            const seriesName = series.name;
            legend_data.push(seriesName);
            
            // Store the data
            this.y_buf[seriesName] = [...series.value];
            
            series_data.push({
                name: seriesName,
                type: 'line',
                step: 'start',
                data: series.value,
                smooth: false,
                symbol: 'none',
                large: true,
                largeThreshold: 1000,
                itemStyle: {
                    lineStyle: {
                        width: 1,
                    },
                },
            });
        }

        // 清理多余的series
        var series_del = false;

        var chart_option = chart.getOption();
        for (var i = chart_option.series.length - 1; i >= 0; i--) {
            // if (!data.y.find(item => item.name == chart_option.series[i].name)) {
            if (!legend_data.includes(chart_option.series[i].name)) {
                series_del = true;
                // chart_series.splice(i, 1);
            }
        }

        if (series_del) {
            chart_option.xAxis.data = data.x;
            chart_option.series = series_data;
            chart.setOption(chart_option, true)
        }
        else {
        chart.setOption({
                xAxis: {
                    data: data.x,
                },
            legend: {
                data: legend_data,
            },
            series: series_data,
            });
        }

        return legend_data;
    };

    public __clear(chart) {
        this.y_buf = {};

        return this.__setData(chart, [])
    };

    public __getChartData(chart) {
        var option = chart.getOption();
        var xAxis = option.xAxis[0]; // x 轴

        var header;
        var seriesData;
        var dataZoomComponent = option.dataZoom[0]; // dataZoom 组件
        if (dataZoomComponent) {
            var startPercent = dataZoomComponent.start;
            var endPercent = dataZoomComponent.end;
            var dataLength = xAxis.data.length;
            var start = Math.floor((startPercent / 100) * dataLength);
            var end = Math.ceil((endPercent / 100) * dataLength);

            var arr = ['time'];
            header = arr.concat(xAxis.data.slice(start, end));

            seriesData = option.series.map(function (serie) {
                var arr = [serie.name];
                return arr.concat(serie.data.slice(start, end));
            });
        } else {
            var arr = ['time'];
            header = arr.concat(xAxis.data);

            seriesData = option.series.map(function (serie) {
                var arr = [serie.name];
                return arr.concat(serie.data);
        });
        }

        var csvContent = header.join(',');

        seriesData.forEach(function (dataArray) {
            var dataString = dataArray.join(',');
            csvContent += '\r\n' + dataString;
        });

        return csvContent;
    };

    public __exportCSV(chart, filename) {
        // 获取导出的数据
        var data = this.__getChartData(chart);
        // 创建 Blob 对象
        var blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
        // 创建下载链接
        var downloadLink = document.createElement('a');
        downloadLink.setAttribute('download', filename);
        downloadLink.setAttribute('href', URL.createObjectURL(blob));
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        // 触发下载链接
        downloadLink.click();
        // 移除下载链接
        document.body.removeChild(downloadLink);
    };

}

export { PulseChart };

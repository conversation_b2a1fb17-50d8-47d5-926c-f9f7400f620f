"""
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-01-12 20:26:42
LastEditors: zhai
LastEditTime: 2025-01-12 22:56:44
"""

from pydantic import BaseModel, ConfigD<PERSON>, <PERSON><PERSON>
from datetime import datetime, date
from typing import List, Optional
from fastapi_crud_tortoise import MODEL_ID_TYPE


class Ref_DictItemDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    category_id: Optional[MODEL_ID_TYPE] = None
    parent_id: Optional[MODEL_ID_TYPE] = None
    code: Optional[str] = None
    value: Optional[str] = None
    ext: Optional[str] = None
    order: Optional[int] = None
    description: Optional[str] = None


class Ref_DictCateDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    code: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


###################################################################################


class DictItemCreateDTO(BaseModel):
    category_id: MODEL_ID_TYPE
    parent_id: Optional[MODEL_ID_TYPE] = None
    code: str
    value: str
    ext: Optional[str] = None
    order: Optional[int] = 0
    description: Optional[str] = None


class DictItemDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    category: Optional[Ref_DictCateDTO] = None
    category_id: Optional[MODEL_ID_TYPE] = None
    parent: Optional[Ref_DictItemDTO] = None
    parent_id: Optional[MODEL_ID_TYPE] = None
    children: Optional[List[Ref_DictItemDTO]] = None
    children_refids: Optional[List[MODEL_ID_TYPE]] = None
    code: Optional[str] = None
    value: Optional[str] = None
    ext: Optional[str] = None
    order: Optional[int] = None
    description: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


###################################################################################


class DictCateCreateDTO(BaseModel):
    code: str
    name: str
    description: Optional[str] = None


class DictCateDTO(BaseModel):
    id: Optional[MODEL_ID_TYPE] = None
    code: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    items: Optional[List[DictItemDTO]] = None
    items_refids: Optional[List[MODEL_ID_TYPE]] = None

    model_config = ConfigDict(from_attributes=True)

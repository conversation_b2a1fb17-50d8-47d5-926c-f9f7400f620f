2025-07-30 08:49:55 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 11:21:53 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 11:22:45 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 11:22:56 - MainProcess | MainThread |  0688990109b07a648000d379f6cd62cc  | auth._:80 - INFO -用户登录成功, 用户名: Super
2025-07-30 11:25:16 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 12:22:13 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x0000017486A1B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001748932A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001748932A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │     └ <function run_path at 0x0000017488FAD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_app\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
           └ <function _run_module_code at 0x0000017488FACDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
    └ <function _run_code at 0x0000017488FAC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>

  File "E:\git\tvs\tvs_app\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000017489F7CDC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x0000017489F7C700>
    └ <uvicorn.server.Server object at 0x000001748C51F3A0>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000017489F7C790>
           │       │   └ <uvicorn.server.Server object at 0x000001748C51F3A0>
           │       └ <function run at 0x0000017489D64550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000174B95E7530>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000017489D65F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000017489DDB130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000017489D679A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000174894DEDD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1768, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 54862), raddr...
    └ <_ProactorSocketTransport closing fd=1768>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 12:22:13 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x0000017486A1B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001748932A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001748932A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │     └ <function run_path at 0x0000017488FAD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_app\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
           └ <function _run_module_code at 0x0000017488FACDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
    └ <function _run_code at 0x0000017488FAC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>

  File "E:\git\tvs\tvs_app\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000017489F7CDC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x0000017489F7C700>
    └ <uvicorn.server.Server object at 0x000001748C51F3A0>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000017489F7C790>
           │       │   └ <uvicorn.server.Server object at 0x000001748C51F3A0>
           │       └ <function run at 0x0000017489D64550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000174B95E7530>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000017489D65F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000017489DDB130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000017489D679A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000174894DEDD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1796, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1796>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 12:24:05 - MainProcess | MainThread |  068899e63a207a2d80009078ca45baf8  | auth._:80 - INFO -用户登录成功, 用户名: Super
2025-07-30 12:24:05 - MainProcess | MainThread |  068899e63a3b71b4800035a75cbc44b7  | auth._:80 - INFO -用户登录成功, 用户名: Super
2025-07-30 12:24:05 - MainProcess | MainThread |  068899e63a4a7d798000f03d2b39531d  | auth._:80 - INFO -用户登录成功, 用户名: Super
2025-07-30 12:24:05 - MainProcess | MainThread |  068899e63a727ffb8000bd76511bbd39  | auth._:80 - INFO -用户登录成功, 用户名: Super
2025-07-30 12:24:09 - MainProcess | MainThread |  068899e69af1758b800064c85f122ad3  | auth._:80 - INFO -用户登录成功, 用户名: Super
2025-07-30 12:26:35 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x0000017486A1B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001748932A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001748932A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │     └ <function run_path at 0x0000017488FAD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_app\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
           └ <function _run_module_code at 0x0000017488FACDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
    └ <function _run_code at 0x0000017488FAC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>

  File "E:\git\tvs\tvs_app\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000017489F7CDC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x0000017489F7C700>
    └ <uvicorn.server.Server object at 0x000001748C51F3A0>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000017489F7C790>
           │       │   └ <uvicorn.server.Server object at 0x000001748C51F3A0>
           │       └ <function run at 0x0000017489D64550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000174B95E7530>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000017489D65F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000017489DDB130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000017489D679A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000174894DEDD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1784, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1784>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 12:34:16 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x0000017486A1B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001748932A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001748932A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │     └ <function run_path at 0x0000017488FAD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_app\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
           └ <function _run_module_code at 0x0000017488FACDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
    └ <function _run_code at 0x0000017488FAC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>

  File "E:\git\tvs\tvs_app\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000017489F7CDC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x0000017489F7C700>
    └ <uvicorn.server.Server object at 0x000001748C51F3A0>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000017489F7C790>
           │       │   └ <uvicorn.server.Server object at 0x000001748C51F3A0>
           │       └ <function run at 0x0000017489D64550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000174B95E7530>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000017489D65F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000017489DDB130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000017489D679A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000174894DEDD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1528, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1528>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 12:40:24 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x0000017486A1B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001748932A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001748932A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │     └ <function run_path at 0x0000017488FAD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_app\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
           └ <function _run_module_code at 0x0000017488FACDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
    └ <function _run_code at 0x0000017488FAC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>

  File "E:\git\tvs\tvs_app\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000017489F7CDC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x0000017489F7C700>
    └ <uvicorn.server.Server object at 0x000001748C51F3A0>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000017489F7C790>
           │       │   └ <uvicorn.server.Server object at 0x000001748C51F3A0>
           │       └ <function run at 0x0000017489D64550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000174B95E7530>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000017489D65F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000017489DDB130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000017489D679A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000174894DEDD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1816, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1816>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 12:40:32 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x0000017486A1B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x0000017486A30660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001748932A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001748932A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │     └ <function run_path at 0x0000017488FAD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_app\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
           └ <function _run_module_code at 0x0000017488FACDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>
    └ <function _run_code at 0x0000017488FAC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x0000017489426810, file "E:\git\tvs\tvs_app\run.py", line 1>

  File "E:\git\tvs\tvs_app\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x0000017489F7CDC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x0000017489F7C700>
    └ <uvicorn.server.Server object at 0x000001748C51F3A0>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000017489F7C790>
           │       │   └ <uvicorn.server.Server object at 0x000001748C51F3A0>
           │       └ <function run at 0x0000017489D64550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000174B95E7530>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000017489D65F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000017489DDB130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000017489D679A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000174894DEDD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1456, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1456>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 12:47:59 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 12:48:23 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 12:50:08 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 12:50:54 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x00000231A8060660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x00000231A804B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x00000231A8060660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x00000231AAA0A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x00000231AAA0A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │     └ <function run_path at 0x00000231AA27D120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_app\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x00000231AAADE340, file "E:\git\tvs\tvs_app\run.py", line 1>
           └ <function _run_module_code at 0x00000231AA27CDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_app\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x00000231AAADE340, file "E:\git\tvs\tvs_app\run.py", line 1>
    └ <function _run_code at 0x00000231AA27C9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x00000231AAADE340, file "E:\git\tvs\tvs_app\run.py", line 1>

  File "E:\git\tvs\tvs_app\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x00000231AB658DC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x00000231AB658700>
    └ <uvicorn.server.Server object at 0x00000231ADBEB2B0>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000231AB658790>
           │       │   └ <uvicorn.server.Server object at 0x00000231ADBEB2B0>
           │       └ <function run at 0x00000231AB434550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x00000231DACA7610>
           │    └ <function BaseEventLoop.run_until_complete at 0x00000231AB435F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000231AB4B3130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000231AB4379A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000231AABAADD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1244, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1244>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 12:57:17 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 13:02:40 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x000001F03430B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001F036C8A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001F036C8A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │     └ <function run_path at 0x000001F0368FD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
           └ <function _run_module_code at 0x000001F0368FCDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
    └ <function _run_code at 0x000001F0368FC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>

  File "E:\git\tvs\tvs_site\server\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x000001F0378D4DC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x000001F0378D4700>
    └ <uvicorn.server.Server object at 0x000001F039E6F160>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001F0378D4790>
           │       │   └ <uvicorn.server.Server object at 0x000001F039E6F160>
           │       └ <function run at 0x000001F0376B4550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001F0681E4AC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001F0376B5F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001F037733130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F0376B79A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001F036E2ADD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=992, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=(...
    └ <_ProactorSocketTransport closing fd=992>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 13:07:11 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x000001F03430B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001F036C8A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001F036C8A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │     └ <function run_path at 0x000001F0368FD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
           └ <function _run_module_code at 0x000001F0368FCDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
    └ <function _run_code at 0x000001F0368FC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>

  File "E:\git\tvs\tvs_site\server\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x000001F0378D4DC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x000001F0378D4700>
    └ <uvicorn.server.Server object at 0x000001F039E6F160>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001F0378D4790>
           │       │   └ <uvicorn.server.Server object at 0x000001F039E6F160>
           │       └ <function run at 0x000001F0376B4550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001F0681E4AC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001F0376B5F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001F037733130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F0376B79A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001F036E2ADD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1744, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1744>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 13:07:18 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x000001F03430B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001F036C8A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001F036C8A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │     └ <function run_path at 0x000001F0368FD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
           └ <function _run_module_code at 0x000001F0368FCDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
    └ <function _run_code at 0x000001F0368FC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>

  File "E:\git\tvs\tvs_site\server\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x000001F0378D4DC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x000001F0378D4700>
    └ <uvicorn.server.Server object at 0x000001F039E6F160>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001F0378D4790>
           │       │   └ <uvicorn.server.Server object at 0x000001F039E6F160>
           │       └ <function run at 0x000001F0376B4550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001F0681E4AC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001F0376B5F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001F037733130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F0376B79A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001F036E2ADD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1076, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1076>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 13:07:21 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x000001F03430B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001F036C8A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001F036C8A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │     └ <function run_path at 0x000001F0368FD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
           └ <function _run_module_code at 0x000001F0368FCDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
    └ <function _run_code at 0x000001F0368FC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>

  File "E:\git\tvs\tvs_site\server\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x000001F0378D4DC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x000001F0378D4700>
    └ <uvicorn.server.Server object at 0x000001F039E6F160>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001F0378D4790>
           │       │   └ <uvicorn.server.Server object at 0x000001F039E6F160>
           │       └ <function run at 0x000001F0376B4550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001F0681E4AC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001F0376B5F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001F037733130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F0376B79A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001F036E2ADD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1780, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1780>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 13:07:22 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x000001F03430B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001F036C8A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001F036C8A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │     └ <function run_path at 0x000001F0368FD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
           └ <function _run_module_code at 0x000001F0368FCDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
    └ <function _run_code at 0x000001F0368FC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>

  File "E:\git\tvs\tvs_site\server\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x000001F0378D4DC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x000001F0378D4700>
    └ <uvicorn.server.Server object at 0x000001F039E6F160>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001F0378D4790>
           │       │   └ <uvicorn.server.Server object at 0x000001F039E6F160>
           │       └ <function run at 0x000001F0376B4550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001F0681E4AC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001F0376B5F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001F037733130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F0376B79A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001F036E2ADD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1832, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1832>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 13:07:39 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x000001F03430B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001F036C8A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001F036C8A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │     └ <function run_path at 0x000001F0368FD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
           └ <function _run_module_code at 0x000001F0368FCDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
    └ <function _run_code at 0x000001F0368FC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>

  File "E:\git\tvs\tvs_site\server\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x000001F0378D4DC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x000001F0378D4700>
    └ <uvicorn.server.Server object at 0x000001F039E6F160>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001F0378D4790>
           │       │   └ <uvicorn.server.Server object at 0x000001F039E6F160>
           │       └ <function run at 0x000001F0376B4550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001F0681E4AC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001F0376B5F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001F037733130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F0376B79A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001F036E2ADD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1924, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1924>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 13:12:18 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x000001F03430B6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x000001F034320660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000001F036C8A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000001F036C8A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │     └ <function run_path at 0x000001F0368FD120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
           └ <function _run_module_code at 0x000001F0368FCDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>
    └ <function _run_code at 0x000001F0368FC9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x000001F036D5E340, file "E:\git\tvs\tvs_site\server\run.py", line 1>

  File "E:\git\tvs\tvs_site\server\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x000001F0378D4DC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x000001F0378D4700>
    └ <uvicorn.server.Server object at 0x000001F039E6F160>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001F0378D4790>
           │       │   └ <uvicorn.server.Server object at 0x000001F039E6F160>
           │       └ <function run at 0x000001F0376B4550>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000001F0681E4AC0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000001F0376B5F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001F037733130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001F0376B79A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001F036E2ADD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1068, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1068>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 13:16:56 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 13:19:59 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 13:20:10 - MainProcess | MainThread |  06889ab838d9769280007a4a030ee4da  | views.event_generator:183 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp >= '2025-07-30 04:10:15' AND Tstamp <= '2025-07-30 04:11:15'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:26:30 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 13:26:37 - MainProcess | MainThread |  06889ad07ec67c6f80003900f385b71b  | views.event_generator:160 - INFO -转换后的查询时间范围: 2025-07-30 04:10:15 到 2025-07-30 04:11:15
2025-07-30 13:26:37 - MainProcess | MainThread |  06889ad07ec67c6f80003900f385b71b  | views.event_generator:199 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp >= '2025-07-30 04:10:15' AND Tstamp <= '2025-07-30 04:11:15'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:32:33 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 13:32:51 - MainProcess | MainThread |  06889ae74ba4787e800084b86908c7da  | views.event_generator:155 - INFO -查询时间范围: 2025-07-30 04:28:58+00:00 到 2025-07-30 04:38:58+00:00
2025-07-30 13:33:06 - MainProcess | MainThread |  06889ae74ba4787e800084b86908c7da  | views.event_generator:194 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp >= '2025-07-30 04:28:58+00:00' AND Tstamp <= '2025-07-30 04:38:58+00:00'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:35:00 - MainProcess | MainThread |    | base_events.default_exception_handler:1758 - ERROR -Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "D:\dev\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │         └ <code object <module> at 0x0000021742F00660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...
           └ <function _run_code at 0x0000021742EEB6D0>

  File "D:\dev\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
         └ <code object <module> at 0x0000021742F00660, file "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\__main__.py", line 71, in <module>
    cli.main()
    │   └ <function main at 0x000002174590A560>
    └ <module 'debugpy.server.cli' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\l...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 501, in main
    run()
    └ <function run_file at 0x000002174590A320>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy/..\debugpy\server\cli.py", line 351, in run_file
    runpy.run_path(target, run_name="__main__")
    │     │        └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │     └ <function run_path at 0x000002174519D120>
    └ <module '_pydevd_bundle.pydevd_runpy' from 'c:\\Users\\<USER>\\.windsurf\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\b...

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 310, in run_path
    return _run_module_code(code, init_globals, run_name, pkg_name=pkg_name, script_name=fname)
           │                │     │             │                  │                     └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
           │                │     │             │                  └ ''
           │                │     │             └ '__main__'
           │                │     └ None
           │                └ <code object <module> at 0x0000021745A07310, file "E:\git\tvs\tvs_site\server\run.py", line 1>
           └ <function _run_module_code at 0x000002174519CDC0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 127, in _run_module_code
    _run_code(code, mod_globals, init_globals, mod_name, mod_spec, pkg_name, script_name)
    │         │     │            │             │         │         │         └ 'E:\\git\\tvs\\tvs_site\\server\\run.py'
    │         │     │            │             │         │         └ ''
    │         │     │            │             │         └ None
    │         │     │            │             └ '__main__'
    │         │     │            └ None
    │         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
    │         └ <code object <module> at 0x0000021745A07310, file "E:\git\tvs\tvs_site\server\run.py", line 1>
    └ <function _run_code at 0x000002174519C9D0>

  File "c:\Users\<USER>\.windsurf\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\libs\debugpy\_vendored\pydevd\_pydevd_bundle\pydevd_runpy.py", line 118, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': '\nDescripttion:\nversion: 0.x\nAuthor: zhai\nDate: 2025-01-04 15:11:53\nLastEditors: zha...
         └ <code object <module> at 0x0000021745A07310, file "E:\git\tvs\tvs_site\server\run.py", line 1>

  File "E:\git\tvs\tvs_site\server\run.py", line 36, in <module>
    uvicorn.run(
    │       └ <function run at 0x000002174653CDC0>
    └ <module 'uvicorn' from 'c:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\fast-soy-iot-50K12LJ7-py3.10\\lib\\si...

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\main.py", line 575, in run
    server.run()
    │      └ <function Server.run at 0x000002174653C700>
    └ <uvicorn.server.Server object at 0x0000021748A87340>

  File "c:\Users\<USER>\AppData\Local\pypoetry\Cache\virtualenvs\fast-soy-iot-50K12LJ7-py3.10\lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002174653C790>
           │       │   └ <uvicorn.server.Server object at 0x0000021748A87340>
           │       └ <function run at 0x0000021746337BE0>
           └ <module 'asyncio' from 'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\dev\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000021776DB4C80>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000021746335F30>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000217463AB130>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000217463379A0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\dev\Python\Python310\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000021745ABEDD0>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

> File "D:\dev\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>

  File "D:\dev\Python\Python310\lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'D:\\dev\\Python\\Python310\\lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1556, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=0, laddr=('127.0.0.1', 9995), raddr=...
    └ <_ProactorSocketTransport closing fd=1556>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-30 13:35:13 - MainProcess | MainThread |  06889af0485f75c18000815efc2bb83e  | views.event_generator:155 - INFO -查询时间范围: 2025-07-30 04:28:58+00:00 到 2025-07-30 04:38:58+00:00
2025-07-30 13:35:13 - MainProcess | MainThread |  06889af0485f75c18000815efc2bb83e  | views.event_generator:194 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp >= '2025-07-30 04:28:58+00:00' AND Tstamp <= '2025-07-30 04:38:58+00:00'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:49:46 - MainProcess | MainThread |  06889b270f7572b98000da3606f3d4ca  | views.event_generator:155 - INFO -查询时间范围: 2025-07-30 12:49:08 到 2025-07-30 12:51:08
2025-07-30 13:49:49 - MainProcess | MainThread |  06889b270f7572b98000da3606f3d4ca  | views.event_generator:194 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp >= '2025-07-30 12:49:08' AND Tstamp <= '2025-07-30 12:51:08'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:49:49 - MainProcess | MainThread |  06889b270f7572b98000da3606f3d4ca  | views.event_generator:194 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp > '2025-07-30 20:49:10.012048' AND Tstamp <= '2025-07-30 12:51:08'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:51:36 - MainProcess | MainThread |  06889b2de4b4794a8000aa8ca01a5466  | views.event_generator:155 - INFO -查询时间范围: 2025-07-30 12:49:08 到 2025-07-30 12:51:08
2025-07-30 13:51:46 - MainProcess | MainThread |  06889b2de4b4794a8000aa8ca01a5466  | views.event_generator:194 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp >= '2025-07-30 12:49:08' AND Tstamp <= '2025-07-30 12:51:08'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:52:10 - MainProcess | MainThread |    | lifespan.check_redis_connection:23 - ERROR -Redis连接失败: Error 22 connecting to 127.0.0.1:6379. 22.
2025-07-30 13:52:16 - MainProcess | MainThread |  06889b30a9fa7b1a8000e79e60010a3b  | views.event_generator:152 - INFO -查询时间范围: 2025-07-30 12:49:08 到 2025-07-30 12:51:08
2025-07-30 13:52:19 - MainProcess | MainThread |  06889b30a9fa7b1a8000e79e60010a3b  | views.event_generator:191 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp >= '2025-07-30 12:49:08' AND Tstamp <= '2025-07-30 12:51:08'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:52:35 - MainProcess | MainThread |  06889b3201dc7ceb800098b0b04afc3c  | views.event_generator:152 - INFO -查询时间范围: 2025-07-30 12:49:08 到 2025-07-30 12:51:08
2025-07-30 13:52:35 - MainProcess | MainThread |  06889b3201dc7ceb800098b0b04afc3c  | views.event_generator:191 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp >= '2025-07-30 12:49:08' AND Tstamp <= '2025-07-30 12:51:08'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        
2025-07-30 13:52:35 - MainProcess | MainThread |  06889b3201dc7ceb800098b0b04afc3c  | views.event_generator:191 - INFO -完整SQL: 
                        SELECT * FROM ElecSignal 
                        WHERE Tstamp > '2025-07-30 20:49:10.012048' AND Tstamp <= '2025-07-30 12:51:08'
                        ORDER BY Tstamp
                        LIMIT '2048'
                        

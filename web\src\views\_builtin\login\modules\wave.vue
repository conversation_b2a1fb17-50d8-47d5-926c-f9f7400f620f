<template>
    <div class="wave-container" ref="container">
      <canvas ref="canvas" class="waves-canvas"></canvas>
    </div>
  </template>
  
  <script setup lang="ts">
  import { onMounted, onUnmounted, ref } from 'vue'
  
  interface WaveOptions {
    timeModifier?: number
    lineWidth?: number
    amplitude?: number
    wavelength?: number
    segmentLength?: number
    strokeStyle?: string | CanvasGradient
  }
  
  interface SineWaveGeneratorOptions {
    el: HTMLCanvasElement
    waves: WaveOptions[]
    initialize?: () => void
    resizeEvent?: () => void
  }
  
  const container = ref<HTMLElement | null>(null)
  const canvas = ref<HTMLCanvasElement | null>(null)
  
  class SineWaveGenerator {
    private ctx: CanvasRenderingContext2D
    private width: number = 0
    private height: number = 0
    private waveWidth: number = 0
    private waveLeft: number = 0
    private time: number = 0
    private animationId: number | null = null
    private dpr: number = window.devicePixelRatio || 1
    private maxAmplitude: number = 0 // 动态振幅
    private waves: WaveOptions[] = []
  
    readonly PI2: number = Math.PI * 2
    readonly HALFPI: number = Math.PI / 2
  
    constructor(options: SineWaveGeneratorOptions & { container: HTMLElement }) {
      const ctx = options.el.getContext('2d')
      if (!ctx) throw new Error('Could not get canvas context')
      this.ctx = ctx
  
      Object.assign(this, options)
      if (!this.waves.length) throw new Error('No waves specified')
  
      this._resizeCanvas()
      const resizeObserver = new ResizeObserver(this._resizeCanvas.bind(this))
      resizeObserver.observe(options.container)
  
      if (typeof options.resizeEvent === 'function') {
        options.resizeEvent.call(this)
      }
  
      if (typeof options.initialize === 'function') {
        options.initialize.call(this)
      }
  
      this.loop()
    }
  
    private _resizeCanvas() {
      const containerWidth = this.container.clientWidth
      const containerHeight = this.container.clientHeight
  
      this.dpr = window.devicePixelRatio || 1
      this.width = this.el.width = containerWidth * this.dpr
      this.height = this.el.height = containerHeight * this.dpr
      this.el.style.width = `${containerWidth}px`
      this.el.style.height = `${containerHeight}px`
  
      // 设置最大振幅为容器高度的40%
      this.maxAmplitude = containerHeight * 0.4
  
      this.waveWidth = this.width * 0.95
      this.waveLeft = this.width * 0.025
    }
  
    private clear() {
      this.ctx.clearRect(0, 0, this.width, this.height)
    }
  
    private update(time?: number) {
      this.time = this.time - 0.007
      const currentTime = time ?? this.time
  
      this.waves.forEach(wave => {
        const timeModifier = wave.timeModifier || 1
        // 计算每个波浪的振幅，确保波浪在容器范围内
        const adjustedAmplitude = wave.amplitude 
          ? Math.max(-this.maxAmplitude, Math.min(wave.amplitude, this.maxAmplitude))
          : this.maxAmplitude
  
        this.drawSine(currentTime * timeModifier, { ...wave, amplitude: adjustedAmplitude })
      })
    }
  
    private ease(percent: number, amplitude: number): number {
      return amplitude * (Math.sin(percent * this.PI2 - this.HALFPI) + 1) * 0.5
    }
  
    private drawSine(time: number, options: WaveOptions = {}) {
      const {
        amplitude = this.maxAmplitude,
        wavelength = 50,
        lineWidth = 2,
        strokeStyle = 'rgba(255, 255, 255, 0.2)',
        segmentLength = 10,
      } = options
  
      let x = time
      const yAxis = this.height / 2
  
      this.ctx.lineWidth = lineWidth * this.dpr
      this.ctx.strokeStyle = strokeStyle
      this.ctx.beginPath()
  
      // Ensure the wave starts from the left side of the canvas, at the center
      this.ctx.moveTo(0, yAxis)
      this.ctx.lineTo(this.waveLeft, yAxis)
  
      for (let i = 0; i < this.waveWidth; i += segmentLength) {
        x = (time * 8) + (-yAxis + i) / wavelength
        const y = Math.sin(x)
        const amp = this.ease(i / this.waveWidth, amplitude)
        this.ctx.lineTo(i + this.waveLeft, amp * y + yAxis)
      }
  
      // Ensure the wave ends at the right side of the canvas, at the center
      this.ctx.lineTo(this.width, yAxis)
      this.ctx.stroke()
    }
  
    private loop() {
      this.clear()
      this.update()
      this.animationId = window.requestAnimationFrame(this.loop.bind(this))
    }
  
    destroy() {
      if (this.animationId) {
        window.cancelAnimationFrame(this.animationId)
      }
    }
  }
  
  let waveGenerator: SineWaveGenerator | null = null
  
  onMounted(() => {
    if (!canvas.value || !container.value) return
  
    waveGenerator = new SineWaveGenerator({
      el: canvas.value,
      container: container.value,
      waves: [
        { lineWidth: 3, amplitude: 150, wavelength: 200, segmentLength: 20 },
        { lineWidth: 2, amplitude: 150, wavelength: 100 },
        { lineWidth: 1, amplitude: -150, wavelength: 50, segmentLength: 10 },
        { lineWidth: 0.5, amplitude: -100, wavelength: 100, segmentLength: 10 }
      ],
      resizeEvent() {
        const gradient = this.ctx.createLinearGradient(0, 0, this.width, 0)
        gradient.addColorStop(0, 'rgba(0, 0, 0, 0)')
        gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.5)')
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)')
  
        this.waves.forEach(wave => wave.strokeStyle = gradient)
      }
    })
  })
  
  onUnmounted(() => {
    if (waveGenerator) {
      waveGenerator.destroy()
    }
  })
  </script>
  
  <style scoped>
  .wave-container {
    width: 100%;
    height: 100%;
    position: relative;
  }
  
  .waves-canvas {
    display: block;
    width: 100%;
    height: 100%;
  }
  </style>
  
{"version": 3, "sources": ["../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/ts-types/index.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/ts-types/common.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/ts-types/gantt-engine.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/ts-types/EVENT_TYPE.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/grid.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/src/env.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/scroll-bar.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/tools/util.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/event/scroll.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/src/gantt-helper.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/timeline-header.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/gantt-node.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/task-bar.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/mark-line.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/frame-border.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/scenegraph/src/scenegraph/graphic/group-contribution-render.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/scenegraph/src/scenegraph/graphic/index.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/task-creation-button.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/scenegraph/dependency-link.js", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/drag-order-line.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/scenegraph/scenegraph.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/tools/isx.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/tools/debounce.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/event/EventHandler.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/tools/pixel-ratio.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/event/event-manager.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/state/gantt-table-sync.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/tools/inertia.js", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/state/state-manager.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/event/EventTarget.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/data/DataSource.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/Gantt.js", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/es/src/tools/index.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/src/vrender.ts", "../../.pnpm/@visactor+vtable-gantt@1.13.1/node_modules/@visactor/vtable-gantt/src/vtable.ts"], "sourcesContent": ["export * from './common';\nexport * from './gantt-engine';\nexport * from './EVENT_TYPE';\nexport * from './events';\n", "/**\n * 当前表格的交互状态：\n * Default 默认展示\n * grabing 拖拽中\n *   -Resize column 改变列宽\n *   -column move 调整列顺序\n *   -drag select 拖拽多选\n * Scrolling 滚动中\n */\nexport enum InteractionState {\n  'default' = 'default',\n  'grabing' = 'grabing',\n  'scrolling' = 'scrolling'\n}\n", "import type { ColumnsDefine, TYPES, ListTableConstructorOptions } from '@visactor/vtable';\nimport type { Group } from '@visactor/vtable/es/vrender';\nimport type { Gantt } from '../Gantt';\nexport type LayoutObjectId = number | string;\n\nexport interface ITimelineDateInfo {\n  days: number;\n  timeScaleCount: number;\n  endDate: Date;\n  startDate: Date;\n  title: string;\n  /** 当期日期属于该日期刻度的第几位。如季度日期中第四季度 返回4。 */\n  dateIndex: number;\n  unit: 'year' | 'month' | 'quarter' | 'week' | 'day' | 'hour' | 'minute' | 'second';\n  step: number;\n}\n\nexport interface ITimelineHeaderStyle {\n  padding?: number | number[];\n  fontSize?: number;\n  fontWeight?: string;\n  color?: string;\n  strokeColor?: string;\n  // backgroundColor?: string;\n  textAlign?: 'center' | 'end' | 'left' | 'right' | 'start'; // 设置单元格内文字的水平对齐方式\n  textOverflow?: string;\n  textBaseline?: 'alphabetic' | 'bottom' | 'middle' | 'top'; // 设置单元格内文字的垂直对齐方式\n  textStick?: boolean;\n}\nexport interface IGrid {\n  backgroundColor?: string;\n  verticalLine?: ILineStyle;\n  horizontalLine?: ILineStyle;\n}\n//#region gantt\nexport interface GanttConstructorOptions {\n  /**\n   * 数据集合\n   */\n  records?: any[];\n\n  /** 左侧任务信息表格相关配置 */\n  taskListTable?: {\n    /** 左侧任务列表信息占用的宽度。如果设置为'auto'表示将所有列完全展示 */\n    tableWidth?: 'auto' | number;\n    /** 左侧任务列表 最小宽度 */\n    minTableWidth?: number;\n    /** 左侧任务列表 最大宽度 */\n    maxTableWidth?: number;\n  } & Omit<\n    //ListTable表格可配置的属性\n    ListTableConstructorOptions,\n    | 'container'\n    | 'records'\n    | 'defaultHeaderRowHeight'\n    | 'defaultRowHeight'\n    | 'overscrollBehavior'\n    | 'rowSeriesNumber'\n    | 'scrollStyle'\n    | 'pixelRatio'\n    | 'title'\n  >;\n  /** 时间刻度 */\n  timelineHeader: {\n    backgroundColor?: string;\n    colWidth?: number;\n    /** 垂直间隔线样式 */\n    verticalLine?: ILineStyle;\n    /** 水平间隔线样式 */\n    horizontalLine?: ILineStyle;\n    scales: ITimelineScale[];\n  };\n\n  /** 任务条相关配置及样式 */\n  taskBar?: {\n    /** 任务开始日期对应的数据字段名 默认按'startDate' */\n    startDateField?: string;\n    /** 任务结束日期对应的数据字段名 默认按'endDate'  */\n    endDateField?: string;\n    /** 任务进度对应的数据字段名 */\n    progressField?: string;\n    /** 任务条展示文字。可以配置固定文本 或者 字符串模版`${fieldName}` */\n    labelText?: ITaskBarLabelText;\n    /** 任务条文字样式 */\n    labelTextStyle?: ITaskBarLabelTextStyle;\n    /** 任务条样式 */\n    barStyle?: ITaskBarStyle;\n    /** 自定义布局渲染 */\n    customLayout?: ITaskBarCustomLayout;\n    /** 任务条是否可调整大小 */\n    resizable?: boolean;\n    /** 任务条是否可移动 */\n    moveable?: boolean;\n    /** 任务条是否可以被拖拽来改变顺序 */\n    dragOrder?: boolean;\n    /** 任务条hover时的样式 */\n    hoverBarStyle?: ITaskBarHoverStyle;\n    /** 任务条选择时的样式 TODO */\n    selectedBarStyle?: ITaskBarSelectedStyle;\n    /** 任务条是否可选择，默认为true */\n    selectable?: boolean;\n    /** 任务条右键菜单 */\n    menu?: {\n      /** 右键菜单。代替原来的option.contextmenu */\n      contextMenuItems?:\n        | TYPES.MenuListItem[]\n        | ((\n            record: string,\n            index: number,\n            /** 当期日期属于该日期刻度的第几位。如季度日期中第四季度 返回4。 */\n            dateIndex: number,\n            startDate: Date,\n            endDate: Date\n          ) => TYPES.MenuListItem[]);\n    };\n    /** 数据没有排期时，可通过创建任务条排期。默认为true */\n    scheduleCreatable?: boolean;\n    /** 针对没有分配日期的任务，可以显示出创建按钮 */\n    scheduleCreation?: {\n      buttonStyle: ILineStyle & {\n        cornerRadius?: number;\n        backgroundColor?: string;\n      };\n      /** 任务条创建按钮的自定义渲染 */\n      customLayout?: ITaskCreationCustomLayout;\n    };\n  };\n  /** 数据条目可唯一标识的字段名,默认为'id' */\n  taskKeyField?: string;\n  /** 任务之间的依赖关系 */\n  dependency?: {\n    links: ITaskLink[];\n    linkLineStyle?: ILineStyle;\n    linkCreatable?: boolean;\n    linkSelectable?: boolean;\n    linkSelectedLineStyle?: ITaskLinkSelectedStyle;\n    /** 创建关联线的操作点 */\n    linkCreatePointStyle?: IPointStyle;\n    /** 创建关联线的操作点响应状态效果 */\n    linkCreatingPointStyle?: IPointStyle;\n    /** 创建关联线的操作线样式 */\n    linkCreatingLineStyle?: ILineStyle;\n  };\n  /** 网格线配置 */\n  grid?: IGrid;\n\n  /** 整个外边框及横纵分割线配置。 */\n  frame?: {\n    outerFrameStyle: IFrameStyle;\n    verticalSplitLine?: ILineStyle;\n    horizontalSplitLine?: ILineStyle;\n    verticalSplitLineMoveable?: boolean;\n    //列调整宽度的直线\n    verticalSplitLineHighlight?: ILineStyle;\n  };\n\n  /** 标记线配置 如果配置为true 会自动给今天做标记 */\n  markLine?: boolean | IMarkLine | IMarkLine[];\n\n  /** 指定整个甘特图的最小日期 */\n  minDate?: string;\n  /** 指定整个甘特图的最大日期 不设置的话用默认规则*/\n  maxDate?: string;\n\n  /** 顶部表头部分默认行高。如果想按表头层级依次配置，请配置到timelineHeader.scale中 */\n  headerRowHeight?: number;\n\n  /** 数据默认行高 */\n  rowHeight?: number;\n\n  /** 行号配置 */\n  rowSeriesNumber?: IRowSeriesNumber;\n\n  /**\n   * 'auto':和浏览器滚动行为一致 表格滚动到顶部/底部时 触发浏览器默认行为;\n   *  设置为 'none' 时, 表格滚动到顶部/底部时, 不再触发父容器滚动\n   * */\n  overscrollBehavior?: 'auto' | 'none';\n\n  scrollStyle?: IScrollStyle;\n\n  pixelRatio?: number;\n  dateFormat?:\n    | 'yyyy-mm-dd'\n    | 'dd-mm-yyyy'\n    | 'mm/dd/yyyy'\n    | 'yyyy/mm/dd'\n    | 'dd/mm/yyyy'\n    | 'yyyy.mm.dd'\n    | 'dd.mm.yyyy'\n    | 'mm.dd.yyyy';\n\n  /** 表格绘制范围外的canvas上填充的颜色 */\n  underlayBackgroundColor?: string;\n  groupBy?: true | string | string[];\n  /** 展示嵌套结构数据时的模式，默认为full。*/\n  tasksShowMode?: TasksShowMode;\n}\n/**\n * IBarLabelText\n * 可以配置固定文本 或者 ${fieldName} 或者自定义函数\n */\nexport type ITaskBarLabelText = string; //| string[] | ((args: any) => string | string[]);\nexport interface ITimelineScale {\n  rowHeight?: number;\n  unit: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'hour' | 'minute' | 'second';\n  step: number;\n  startOfWeek?: 'sunday' | 'monday';\n  customLayout?: IDateCustomLayout;\n  style?: ITimelineHeaderStyle;\n  format?: (date: DateFormatArgumentType) => string;\n}\nexport interface ITaskBarLabelTextStyle {\n  fontFamily?: string;\n  fontSize?: number;\n  color?: string;\n  textAlign?: 'center' | 'end' | 'left' | 'right' | 'start'; // 设置单元格内文字的水平对齐方式\n  textOverflow?: string;\n  textBaseline?: 'alphabetic' | 'bottom' | 'middle' | 'top'; // 设置单元格内文字的垂直对齐方式\n  padding?: number | number[];\n}\nexport interface ITaskBarStyle {\n  /** 任务条的颜色 */\n  barColor?: string;\n  /** 已完成部分任务条的颜色 */\n  completedBarColor?: string;\n  /** 任务条的宽度 */\n  width?: number;\n  /** 任务条的圆角 */\n  cornerRadius?: number;\n  /** 任务条的边框 */\n  borderWidth?: number;\n  /** 边框颜色 */\n  borderColor?: string;\n}\nexport type ILineStyle = {\n  lineColor?: string;\n  lineWidth?: number;\n  lineDash?: number[];\n};\nexport type IPointStyle = {\n  strokeColor?: string;\n  strokeWidth?: number;\n  fillColor?: string;\n  radius?: number;\n};\nexport interface IMarkLine {\n  date: string;\n  style?: ILineStyle;\n  /** 标记线显示在日期列下的位置 默认为'left' */\n  position?: 'left' | 'right' | 'middle';\n  /** 自动将日期范围内 包括改标记线 */\n  scrollToMarkLine?: boolean;\n}\nexport type ITableColumnsDefine = ColumnsDefine;\nexport type IFrameStyle = {\n  borderColor?: string;\n  borderLineWidth?: number;\n  borderLineDash?: number[];\n  cornerRadius?: number;\n};\n\nexport type ITableStyle = TYPES.ThemeStyle;\nexport type IRowSeriesNumber = TYPES.IRowSeriesNumber;\nexport type IScrollStyle = TYPES.ScrollStyle;\nexport type DateFormatArgumentType = {\n  /** 当期日期属于该日期刻度的第几位。如季度日期中第四季度 返回4。 */\n  dateIndex: number;\n  startDate: Date;\n  endDate: Date;\n};\nexport type TaskBarCustomLayoutArgumentType = {\n  width: number;\n  height: number;\n  index: number;\n  startDate: Date;\n  endDate: Date;\n  taskDays: number;\n  progress: number;\n  taskRecord: any;\n  ganttInstance: Gantt;\n};\nexport type ITaskBarCustomLayoutObj = {\n  rootContainer: Group;\n  renderDefaultBar?: boolean; // 默认false\n  renderDefaultResizeIcon?: boolean; // 默认false\n  renderDefaultText?: boolean; // 默认false\n};\nexport type ITaskBarCustomLayout = (args: TaskBarCustomLayoutArgumentType) => ITaskBarCustomLayoutObj; //CustomLayout\n\nexport type DateCustomLayoutArgumentType = {\n  width: number;\n  height: number;\n  index: number;\n  /** 当期日期属于该日期刻度的第几位。如季度日期中第四季度 返回4。 */\n  dateIndex: number;\n  title: string;\n  startDate: Date;\n  endDate: Date;\n  days: number;\n  ganttInstance: Gantt;\n};\nexport type IDateCustomLayoutObj = {\n  rootContainer: Group;\n  renderDefaultText?: boolean; // 默认false\n};\nexport type IDateCustomLayout = (args: DateCustomLayoutArgumentType) => IDateCustomLayoutObj;\n\nexport type TaskCreationCustomLayoutArgumentType = {\n  width: number;\n  height: number;\n  // index: number;\n  ganttInstance: Gantt;\n};\nexport type ITaskCreationCustomLayoutObj = {\n  rootContainer: Group;\n};\nexport type ITaskCreationCustomLayout = (args: TaskCreationCustomLayoutArgumentType) => ITaskCreationCustomLayoutObj;\n\nexport type ITaskLink = {\n  /** 依赖的类型 */\n  type: DependencyType;\n  /** 依赖线的起始任务唯一标识。如果是tree树形结构的数据 设置数组的话 查找性能会更高 */\n  linkedFromTaskKey?: string | number | (string | number)[];\n  /** 依赖的终止目标任务唯一标识。如果是tree树形结构的数据 设置数组的话 查找性能会更高 */\n  linkedToTaskKey?: string | number | (string | number)[];\n};\n\nexport type ITaskLinkSelectedStyle = ILineStyle & {\n  shadowBlur?: number; //阴影宽度\n  shadowOffset?: number; //偏移\n  shadowColor?: string; //阴影颜色\n};\nexport enum DependencyType {\n  FinishToStart = 'finish_to_start',\n  StartToStart = 'start_to_start',\n  FinishToFinish = 'finish_to_finish',\n  StartToFinish = 'start_to_finish'\n}\nexport enum TasksShowMode {\n  /** 每一个任务节点用单独一行来展示，父任务占用一行，子任务分别占用一行。这是默认的显示效果 */\n  Tasks_Separate = 'tasks_separate',\n  /** 省去父任务节点不展示，并把所有子任务的节点都放到同一行来展示。 */\n  Sub_Tasks_Inline = 'sub_tasks_inline',\n  /** 省去父任务节点不展示，且所有子任务的节点分别用一行展示。*/\n  Sub_Tasks_Separate = 'sub_tasks_separate',\n  /** 省去父任务节点不展示，且所有子任务会维持records中的数据顺序布局，并保证节点不重叠展示 */\n  Sub_Tasks_Arrange = 'sub_tasks_arrange',\n  /** 省去父任务节点不展示，且所有子任务会按照日期早晚的属性来布局，并保证节点不重叠的紧凑型展示 */\n  Sub_Tasks_Compact = 'sub_tasks_compact'\n}\nexport type ITaskBarSelectedStyle = {\n  shadowBlur?: number; //阴影宽度\n  shadowOffsetX?: number; //x方向偏移\n  shadowOffsetY?: number; //Y方向偏移\n  shadowColor?: string; //阴影颜色\n  borderColor?: string; //边框颜色\n  borderLineWidth?: number;\n};\nexport type ITaskBarHoverStyle = {\n  /** 任务条的圆角 */\n  cornerRadius?: number;\n  barOverlayColor?: string;\n};\n//#endregion\n", "export interface EVENT_TYPES {\n  /**\n   * 滚动表格事件\n   */\n  SCROLL: 'scroll';\n  /**\n   * 改变日期范围事件\n   */\n  CHANGE_DATE_RANGE: 'change_date_range';\n  /**\n   * 点击任务条事件\n   */\n  CLICK_TASK_BAR: 'click_task_bar';\n  /**\n   * 鼠标移入任务条事件\n   */\n  MOUSEENTER_TASK_BAR: 'mouseenter_task_bar';\n  /**\n   * 鼠标移出任务条事件\n   */\n  MOUSELEAVE_TASK_BAR: 'mouseleave_task_bar';\n  /**\n   * 创建任务排期事件\n   */\n  CREATE_TASK_SCHEDULE: 'create_task_schedule';\n\n  /**\n   * 创建任务依赖关系\n   */\n  CREATE_DEPENDENCY_LINK: 'create_dependency_link';\n\n  /**\n   * 点击依赖关系连接点\n   */\n  CLICK_DEPENDENCY_LINK_POINT: 'click_dependency_link_point';\n}\n/**\n * Table event types\n */\nexport const GANTT_EVENT_TYPE: EVENT_TYPES = {\n  SCROLL: 'scroll',\n  CHANGE_DATE_RANGE: 'change_date_range',\n  CLICK_TASK_BAR: 'click_task_bar',\n  MOUSEENTER_TASK_BAR: 'mouseenter_task_bar',\n  MOUSELEAVE_TASK_BAR: 'mouseleave_task_bar',\n  CREATE_TASK_SCHEDULE: 'create_task_schedule',\n  CREATE_DEPENDENCY_LINK: 'create_dependency_link',\n  CLICK_DEPENDENCY_LINK_POINT: 'click_dependency_link_point'\n} as EVENT_TYPES;\n", "import { Group, createLine } from '@visactor/vtable/es/vrender';\n\nimport type { Scenegraph } from './scenegraph';\nexport class Grid {\n  vertical: boolean;\n  horizontal: boolean;\n  // verticalLineSpace: number;\n  // horizontalLineSpace: number;\n  gridStyle: any;\n  scrollLeft: number;\n  scrollTop: number;\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n  rowHeight: number;\n  rowCount: number;\n  group: Group;\n  verticalLineGroup: Group;\n  horizontalLineGroup: Group;\n  allGridHeight: number;\n  allGridWidth: number;\n  _scene: Scenegraph;\n  constructor(scene: Scenegraph) {\n    this._scene = scene;\n    this.vertical = !!scene._gantt.parsedOptions.grid.verticalLine;\n    this.horizontal = !!scene._gantt.parsedOptions.grid.horizontalLine;\n    this.gridStyle = scene._gantt.parsedOptions.grid;\n    this.scrollLeft = 0;\n    this.scrollTop = 0;\n    this.x = 0;\n    this.y = scene._gantt.getAllHeaderRowsHeight();\n    this.width = scene.tableGroup.attribute.width;\n    this.height = scene.tableGroup.attribute.height - scene.timelineHeader.group.attribute.height;\n    this.rowHeight = scene._gantt.parsedOptions.rowHeight;\n    this.rowCount = scene._gantt.itemCount;\n    this.allGridWidth = scene._gantt.getAllDateColsWidth();\n    this.allGridHeight = scene._gantt.getAllTaskBarsHeight();\n    this.group = new Group({\n      x: this.x,\n      y: this.y,\n      width: this.width,\n      height: this.height,\n      clip: true,\n      fill: this.gridStyle?.backgroundColor\n    });\n    this.group.name = 'grid-container';\n    scene.tableGroup.addChild(this.group);\n\n    this.createVerticalLines();\n\n    this.createHorizontalLines();\n\n    //补充timelineHeader中不好绘制的底部的边线\n    const horizontalSplitLineWidth =\n      scene._gantt.parsedOptions.horizontalSplitLine?.lineWidth ??\n      scene._gantt.parsedOptions.timelineHeaderHorizontalLineStyle?.lineWidth;\n    const bottomLineY = (horizontalSplitLineWidth & 1 ? -0.5 : 0) + horizontalSplitLineWidth / 2; // 原来是(horizontalSplitLineWidth & 1 ? 0.5 : 0)  这里改成-0.5为了和左侧表格的水平分割线对齐\n    const line = createLine({\n      pickable: false,\n      stroke:\n        scene._gantt.parsedOptions.horizontalSplitLine?.lineColor ??\n        scene._gantt.parsedOptions.timelineHeaderHorizontalLineStyle?.lineColor,\n      lineWidth: horizontalSplitLineWidth + (horizontalSplitLineWidth & 1 ? 1 : 0), // 加上后面这个1是为了和左侧表格的水平分割线对齐\n      points: [\n        { x: 0, y: bottomLineY },\n        {\n          x: scene._gantt.getAllDateColsWidth(),\n          y: bottomLineY\n        }\n      ]\n    });\n    line.name = 'timeLine-header-bottom-line';\n    this.group.addChild(line);\n  }\n  createVerticalLines() {\n    if (this.vertical) {\n      this.verticalLineGroup = new Group({\n        x: 0,\n        y: 0,\n        width: this.allGridWidth,\n        height: this.allGridHeight\n      });\n      this.verticalLineGroup.name = 'grid-vertical';\n      this.group.appendChild(this.verticalLineGroup);\n\n      const vLines = [];\n\n      const timelineDates = this._scene._gantt.parsedOptions.reverseSortedTimelineScales[0].timelineDates;\n      const timelineColWidth = this._scene._gantt.parsedOptions.timelineColWidth;\n      for (let i = 0; i < timelineDates?.length - 1; i++) {\n        const x = Math.ceil(timelineColWidth * (i + 1)) + (this.gridStyle?.verticalLine.lineWidth & 1 ? 0.5 : 0);\n        const line = createLine({\n          pickable: false,\n          stroke: this.gridStyle?.verticalLine.lineColor,\n          lineWidth: this.gridStyle?.verticalLine.lineWidth,\n          points: [\n            { x, y: 0 },\n            { x, y: this.allGridHeight }\n          ]\n        });\n        vLines.push(line);\n        this.verticalLineGroup.appendChild(line);\n      }\n    }\n  }\n  createHorizontalLines() {\n    if (this.horizontal) {\n      this.horizontalLineGroup = new Group({\n        x: 0,\n        y: 0,\n        width: this.allGridWidth,\n        height: this.allGridHeight\n      });\n      this.horizontalLineGroup.name = 'grid-horizontal';\n      this.group.appendChild(this.horizontalLineGroup);\n\n      const hLines = [];\n      let y = 0;\n      if (this.gridStyle?.horizontalLine.lineWidth & 1) {\n        y += 0.5;\n      }\n      for (let i = 0; i < this.rowCount - 1; i++) {\n        y = y + this._scene._gantt.getRowHeightByIndex(i); // Math.floor(this.rowHeight);\n        const line = createLine({\n          pickable: false,\n          stroke: this.gridStyle?.horizontalLine.lineColor,\n          lineWidth: this.gridStyle?.horizontalLine.lineWidth,\n          points: [\n            { x: 0, y },\n            { x: this.allGridWidth, y }\n          ]\n        });\n        hLines.push(line);\n        this.horizontalLineGroup.appendChild(line);\n      }\n    }\n  }\n  /** 重新创建网格线场景树结点 */\n  refresh() {\n    this.width = this._scene.tableGroup.attribute.width;\n    this.height = this._scene.tableGroup.attribute.height - this._scene.timelineHeader.group.attribute.height;\n    this.group.setAttributes({\n      width: this.width,\n      height: this.height,\n      y: this._scene._gantt.getAllHeaderRowsHeight()\n    });\n    this.rowCount = this._scene._gantt.itemCount;\n    this.allGridWidth = this._scene._gantt.getAllDateColsWidth();\n    this.allGridHeight = this._scene._gantt.getAllTaskBarsHeight();\n    this.verticalLineGroup?.parent.removeChild(this.verticalLineGroup);\n    this.horizontalLineGroup?.parent.removeChild(this.horizontalLineGroup);\n    this.createVerticalLines();\n    this.createHorizontalLines();\n  }\n  setX(x: number) {\n    this.verticalLineGroup?.setAttribute('x', x);\n    this.horizontalLineGroup?.setAttribute('x', x);\n  }\n  setY(y: number) {\n    this.verticalLineGroup?.setAttribute('y', y);\n    this.horizontalLineGroup?.setAttribute('y', y);\n  }\n  resize() {\n    this.width = this._scene.tableGroup.attribute.width;\n    this.height = this._scene.tableGroup.attribute.height - this._scene.timelineHeader.group.attribute.height;\n    this.group.setAttribute('width', this.width);\n    this.group.setAttribute('height', this.height);\n  }\n}\n", "export type EnvMode = 'browser' | 'node' | 'worker' | 'miniApp' | 'desktop-miniApp';\nexport type LooseFunction = (...args: any) => any;\n\nexport class Env {\n  static _mode: EnvMode;\n  public static get mode() {\n    if (!Env._mode) {\n      Env._mode = defaultMode();\n    }\n    return Env._mode;\n  }\n  public static set mode(mode: EnvMode) {\n    Env._mode = mode;\n  }\n\n  static dpr = 0;\n\n  static CreateCanvas?: LooseFunction;\n\n  static LoadImage?: LooseFunction;\n\n  static RequestAnimationFrame?: LooseFunction;\n\n  static CancelAnimationFrame?: LooseFunction;\n\n  static RegisterCreateCanvas(func: LooseFunction) {\n    Env.CreateCanvas = func;\n  }\n\n  static RegisterLoadImage(func: LooseFunction) {\n    Env.LoadImage = func;\n  }\n\n  static GetCreateCanvasFunc(): LooseFunction | undefined {\n    if (Env.CreateCanvas) {\n      return Env.CreateCanvas;\n    }\n    if (Env.mode === 'worker') {\n      return (width = 200, height = 200) => new OffscreenCanvas(width, height);\n    }\n    return undefined;\n  }\n\n  static RegisterRequestAnimationFrame(func: LooseFunction) {\n    Env.RequestAnimationFrame = func();\n  }\n\n  static GetRequestAnimationFrame() {\n    if (Env.RequestAnimationFrame) {\n      return Env.RequestAnimationFrame;\n    }\n    return undefined;\n  }\n\n  static RegisterCancelAnimationFrame(func: LooseFunction) {\n    Env.CancelAnimationFrame = func();\n  }\n\n  static GetCancelAnimationFrame() {\n    if (Env.CancelAnimationFrame) {\n      return Env.CancelAnimationFrame;\n    }\n    return undefined;\n  }\n}\n\n/**\n *\n * 这个默认的判断方法并不能区分出不同的环境，所以这里采用是否判断\n * 满足条件为 'browser'，不满足则为 'node'\n */\nfunction defaultMode(): EnvMode {\n  let mode: EnvMode = 'browser';\n  try {\n    if ((window as any).type === 'node') {\n      mode = 'node';\n    } else if (typeof window !== 'undefined' && !window.performance) {\n      mode = 'miniApp';\n    } else if (typeof window === 'undefined') {\n      mode = 'node';\n    }\n  } catch (err) {\n    mode = 'node';\n  }\n  return mode;\n}\n", "import { ScrollBar } from '@visactor/vtable/es/vrender';\nimport { isValid } from '@visactor/vutils';\nimport type { Gantt } from '../Gantt';\n/**\n * @description: 创建滚动条组件\n * @return {*}\n */\n\nexport class ScrollBarComponent {\n  hScrollBar: ScrollBar;\n  vScrollBar: ScrollBar;\n  _gantt: Gantt;\n  _clearHorizontalScrollBar: any;\n  _clearVerticalScrollBar: any;\n  constructor(gantt: Gantt) {\n    this._gantt = gantt;\n    this.createScrollBar(gantt.tableNoFrameWidth, gantt.tableNoFrameHeight - gantt.getAllHeaderRowsHeight());\n  }\n\n  createScrollBar(tableWidth: number, tableHeight: number) {\n    const scrollRailColor = this._gantt.parsedOptions.scrollStyle.scrollRailColor;\n    const scrollSliderColor = this._gantt.parsedOptions.scrollStyle.scrollSliderColor;\n    const scrollSliderCornerRadius = this._gantt.parsedOptions.scrollStyle.scrollSliderCornerRadius;\n    const width = this._gantt.parsedOptions.scrollStyle.width;\n\n    let sliderStyle;\n    if (isValid(scrollSliderCornerRadius)) {\n      sliderStyle = {\n        cornerRadius: scrollSliderCornerRadius,\n        fill: scrollSliderColor\n      };\n    } else {\n      sliderStyle = {\n        fill: scrollSliderColor\n      };\n    }\n    const visible = this._gantt.parsedOptions.scrollStyle?.visible as string;\n    const hoverOn = this._gantt.parsedOptions.scrollStyle?.hoverOn as boolean;\n\n    this.hScrollBar = new ScrollBar({\n      direction: 'horizontal',\n      x: -tableWidth * 2,\n      y: -tableHeight * 2,\n      width: tableWidth,\n      height: width,\n      padding: 0,\n      railStyle: {\n        fill: scrollRailColor\n      },\n      sliderStyle,\n      range: [0, 0.1],\n      // scrollRange: [0.4, 0.8]\n      visible: false\n    });\n    // hack方案实现初始化隐藏滚动条，也可以add到stage之后执行hideAll\n    (this.hScrollBar as any).render();\n    this.hScrollBar.hideAll();\n\n    this.vScrollBar = new ScrollBar({\n      direction: 'vertical',\n      x: -tableWidth * 2,\n      y: -tableHeight * 2,\n      width,\n      height: tableHeight,\n      padding: 0,\n      railStyle: {\n        fill: scrollRailColor\n      },\n      sliderStyle,\n      range: [0, 0.1],\n      visible: false\n    });\n    (this.vScrollBar as any).render();\n    this.vScrollBar.hideAll();\n  }\n  refresh() {\n    //\n  }\n  hideVerticalScrollBar() {\n    const visable = this._gantt.parsedOptions.scrollStyle.visible;\n    if (visable !== 'focus' && visable !== 'scrolling') {\n      return;\n    }\n    this.vScrollBar.setAttribute('visible', false);\n    this.vScrollBar.hideAll();\n    this._gantt.scenegraph.updateNextFrame();\n  }\n  showVerticalScrollBar(autoHide?: boolean) {\n    const visable = this._gantt.parsedOptions.scrollStyle.visible;\n    if (visable !== 'focus' && visable !== 'scrolling') {\n      return;\n    }\n    this.vScrollBar.setAttribute('visible', true);\n    this.vScrollBar.showAll();\n    this._gantt.scenegraph.updateNextFrame();\n    if (autoHide) {\n      // 滚轮触发滚动条显示后，异步隐藏\n      clearTimeout(this._clearVerticalScrollBar);\n      this._clearVerticalScrollBar = setTimeout(() => {\n        this.hideVerticalScrollBar();\n      }, 1000);\n    }\n  }\n  hideHorizontalScrollBar() {\n    const visable = this._gantt.parsedOptions.scrollStyle.visible;\n    if (visable !== 'focus' && visable !== 'scrolling') {\n      return;\n    }\n    this.hScrollBar.setAttribute('visible', false);\n    this.hScrollBar.hideAll();\n    this._gantt.scenegraph.updateNextFrame();\n  }\n  showHorizontalScrollBar(autoHide?: boolean) {\n    const visable = this._gantt.parsedOptions.scrollStyle.visible;\n    if (visable !== 'focus' && visable !== 'scrolling') {\n      return;\n    }\n    this.hScrollBar.setAttribute('visible', true);\n    this.hScrollBar.showAll();\n    this._gantt.scenegraph.updateNextFrame();\n    if (autoHide) {\n      // 滚轮触发滚动条显示后，异步隐藏\n      clearTimeout(this._clearHorizontalScrollBar);\n      this._clearHorizontalScrollBar = setTimeout(() => {\n        this.hideHorizontalScrollBar();\n      }, 1000);\n    }\n  }\n  updateVerticalScrollBarPos(topRatio: number) {\n    const range = this.vScrollBar.attribute.range;\n    const size = range[1] - range[0];\n    const range0 = topRatio * (1 - size);\n    this.vScrollBar.setAttribute('range', [range0, range0 + size]);\n    const bounds = this.vScrollBar.AABBBounds && this.vScrollBar.globalAABBBounds;\n    (this.vScrollBar as any)._viewPosition = {\n      x: bounds.x1,\n      y: bounds.y1\n    };\n  }\n  updateHorizontalScrollBarPos(leftRatio: number) {\n    const range = this.hScrollBar.attribute.range;\n    const size = range[1] - range[0];\n    const range0 = leftRatio * (1 - size);\n    this.hScrollBar.setAttribute('range', [range0, range0 + size]);\n    const bounds = this.hScrollBar.AABBBounds && this.hScrollBar.globalAABBBounds;\n    (this.hScrollBar as any)._viewPosition = {\n      x: bounds.x1,\n      y: bounds.y1\n    };\n  }\n\n  /**\n   * @description: 更新滚动条尺寸\n   * @return {*}\n   */\n  updateScrollBar() {\n    const oldHorizontalBarPos = this._gantt.stateManager.scroll.horizontalBarPos;\n    const oldVerticalBarPos = this._gantt.stateManager.scroll.verticalBarPos;\n\n    const scrollStyle = this._gantt.parsedOptions.scrollStyle;\n    const width = scrollStyle?.width as number;\n    const visible = scrollStyle?.visible as string;\n    // const hoverOn = theme.scrollStyle?.hoverOn as boolean;\n    const tableWidth = Math.ceil(this._gantt.scenegraph.tableGroup.attribute.width);\n    const tableHeight = Math.ceil(this._gantt.scenegraph.tableGroup.attribute.height);\n\n    const totalHeight = this._gantt.getAllRowsHeight();\n    const totalWidth = this._gantt.getAllDateColsWidth();\n    const frozenRowsHeight = this._gantt.getAllHeaderRowsHeight();\n    // const frozenColsWidth = this._gantt.getFrozenColsWidth();\n    // const bottomFrozenRowsHeight = this._gantt.getBottomFrozenRowsHeight();\n    // const rightFrozenColsWidth = this._gantt.getRightFrozenColsWidth();\n    if (totalWidth > tableWidth) {\n      const y = Math.min(tableHeight, totalHeight);\n      const rangeEnd = Math.max(0.05, tableWidth / totalWidth);\n\n      const hoverOn = scrollStyle.hoverOn;\n\n      let attrY = 0;\n      if (scrollStyle.barToSide) {\n        attrY =\n          this._gantt.tableNoFrameHeight -\n          (hoverOn ? width : -this._gantt.scenegraph.tableGroup.attribute.y) +\n          this._gantt.tableY;\n      } else {\n        attrY = y - (hoverOn ? width : -this._gantt.scenegraph.tableGroup.attribute.y) + this._gantt.tableY;\n      }\n\n      this.hScrollBar.setAttributes({\n        x: this._gantt.scenegraph.tableGroup.attribute.x,\n        y: attrY,\n        width: tableWidth,\n        range: [0, rangeEnd],\n        visible: visible === 'always'\n      });\n      const bounds = this.hScrollBar.AABBBounds && this.hScrollBar.globalAABBBounds;\n      (this.hScrollBar as any)._viewPosition = {\n        x: bounds.x1,\n        y: bounds.y1\n      };\n      if (visible === 'always') {\n        this.hScrollBar.showAll();\n      }\n    } else {\n      this.hScrollBar.setAttributes({\n        x: -this._gantt.tableNoFrameWidth * 2,\n        y: -this._gantt.tableNoFrameHeight * 2,\n        width: 0,\n        visible: false\n      });\n    }\n\n    if (totalHeight > tableHeight) {\n      const x = Math.min(tableWidth, totalWidth) + this._gantt.scenegraph.tableGroup.attribute.x;\n      const rangeEnd = Math.max(0.05, (tableHeight - frozenRowsHeight) / (totalHeight - frozenRowsHeight));\n\n      let attrX = 0;\n      const hoverOn = this._gantt.parsedOptions.scrollStyle.hoverOn;\n\n      if (this._gantt.parsedOptions.scrollStyle.barToSide) {\n        attrX = this._gantt.tableNoFrameWidth - (hoverOn ? width : -this._gantt.scenegraph.tableGroup.attribute.x);\n      } else {\n        attrX = x - (hoverOn ? width : -this._gantt.scenegraph.tableGroup.attribute.x);\n      }\n\n      this.vScrollBar.setAttributes({\n        x: attrX,\n        y: frozenRowsHeight + (!hoverOn ? this._gantt.scenegraph.tableGroup.attribute.y : 0) + this._gantt.tableY,\n        height: tableHeight - frozenRowsHeight,\n        range: [0, rangeEnd],\n        visible: visible === 'always'\n      });\n      const bounds = this.vScrollBar.AABBBounds && this.vScrollBar.globalAABBBounds;\n      (this.vScrollBar as any)._viewPosition = {\n        x: bounds.x1,\n        y: bounds.y1\n      };\n\n      if (visible === 'always') {\n        this.vScrollBar.showAll();\n      }\n    } else {\n      this.vScrollBar.setAttributes({\n        x: -this._gantt.tableNoFrameWidth * 2,\n        y: -this._gantt.tableNoFrameHeight * 2,\n        height: 0,\n        visible: false\n      });\n    }\n\n    this._gantt.stateManager.setScrollLeft(oldHorizontalBarPos);\n    this._gantt.stateManager.setScrollTop(oldVerticalBarPos);\n  }\n}\n", "import { isValid } from '@visactor/vutils';\n\n/**\n * throttle 保障了首次立即执行 后续触发的回调执行间隔delay时间 区别于throttle2 最后执行时机会提前\n * @param { Function } func 执行函数\n * @param { Interger } time 多长时间内不能第二次执行\n * @returns function 返回经过节流处理的函数\n */\nexport function throttle(func: Function, delay: number) {\n  let timer: any = null;\n  return function (this: any, ...args: any[]) {\n    // let args=arguments 也可以写成这种或...args也是代表我们传过来的实参\n    if (!timer) {\n      func.apply(this, args); //先执行函数,保证第一次立即执行\n      timer = setTimeout(() => {\n        timer = null;\n      }, delay);\n    }\n    // console.log('throttle');\n    // 当我们第一次触发事件，定时器不存在时就执行函数，当我们再次点击时，因为定时器存在，\n    // 所以无法再进入函数调用(无论事件如何执行),那么只能等定时器事件结束，\n    // 我们让timer=null，回到第一次的状态,就又重新开始新的一轮\n  };\n}\n/**\n * throttle节流 间隔delay时间后执行 保障了最后执行时机是在delay之后\n * @param { Function } func 执行函数\n * @param { Interger } time 多长时间内不能第二次执行\n * @returns function 返回经过节流处理的函数\n */\nexport function throttle2(func: Function, delay: number) {\n  let timer: any = null;\n  return function (this: any, ...args: any[]) {\n    // let args=arguments 也可以写成这种或...args也是代表我们传过来的实参\n    if (!timer) {\n      timer = setTimeout(() => {\n        func.apply(this, args);\n        timer = null;\n      }, delay);\n    }\n  };\n}\n// export function parseDateFormat(dateString: string) {\n//   const formats = [\n//     'yyyy-mm-dd',\n//     'dd-mm-yyyy',\n//     'mm/dd/yyyy',\n//     'yyyy/mm/dd',\n//     'dd/mm/yyyy',\n//     'yyyy.mm.dd',\n//     'dd.mm.yyyy',\n//     'mm.dd.yyyy'\n//   ];\n//   const separatorsInDate = dateString.match(/[^\\w]/g);\n//   for (let i = 0; i < formats.length; i++) {\n//     const format = formats[i];\n//     const separators = format.match(/[^\\w]/g);\n//     let isValidFormat = true;\n//     for (let j = 0; j < separators.length; j++) {\n//       const part = separators[j];\n//       if (part !== separatorsInDate[j]) {\n//         isValidFormat = false;\n//         break;\n//       }\n//     }\n//     if (isValidFormat) {\n//       return format;\n//     }\n//   }\n\n//   return null;\n// }\n\n// export function parseDate(dateString, format) {\n//   // 根据解析出的格式将 dateString 解析为日期对象\n//   // 这里只是一个示例，假设解析格式为 \"yyyy-mm-dd\"\n//   const parts = dateString.split('-');\n//   const year = parseInt(parts[0], 10);\n//   const month = parseInt(parts[1], 10) - 1;\n//   const day = parseInt(parts[2], 10);\n//   return new Date(year, month, day);\n// }\nexport function getTodayNearDay(dayOffset: number, format?: string) {\n  const today = new Date();\n  const todayTime = today.getTime();\n  const oneDayTime = 24 * 60 * 60 * 1000;\n  const targetTime = todayTime + dayOffset * oneDayTime;\n  const date = new Date(targetTime);\n  if (format) {\n    const year = date.getFullYear().toString();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    format = format.replace('yyyy', year);\n    format = format.replace('mm', month);\n    format = format.replace('dd', day);\n    return format;\n  }\n  return date;\n}\n\nexport function formatDate(date: Date, format: string) {\n  const year = date.getFullYear().toString();\n  const month = (date.getMonth() + 1).toString().padStart(2, '0');\n  const day = date.getDate().toString().padStart(2, '0');\n\n  format = format.replace('yyyy', year);\n  format = format.replace('mm', month);\n  format = format.replace('dd', day);\n  if (format.length > 10) {\n    const hour = date.getHours().toString().padStart(2, '0');\n    const minute = date.getMinutes().toString().padStart(2, '0');\n    const second = date.getSeconds().toString().padStart(2, '0');\n\n    format = format.replace('hh', hour);\n    format = format.replace('mm', minute);\n    format = format.replace('ss', second);\n  }\n\n  return format;\n}\n\n// 修正后的 validateDate 函数\nfunction validateDate(dateParts: string[], format: string) {\n  // 根据格式确定年、月、日的索引\n  const yearIndex = format.indexOf('yyyy');\n  const monthIndex = format.indexOf('mm');\n  const dayIndex = format.indexOf('dd');\n  const dateYearIndex = yearIndex < monthIndex ? (yearIndex < dayIndex ? 0 : 1) : monthIndex < dayIndex ? 1 : 2;\n  const dateMonthIndex = monthIndex < yearIndex ? (monthIndex < dayIndex ? 0 : 1) : monthIndex < dayIndex ? 1 : 2;\n  const dateDayIndex = dayIndex < yearIndex ? (dayIndex < monthIndex ? 0 : 1) : dayIndex < monthIndex ? 1 : 2;\n  // 解析年、月、日\n  const year = parseInt(dateParts[dateYearIndex], 10);\n  const month = parseInt(dateParts[dateMonthIndex], 10) - 1; // 月份从0开始\n  const day = parseInt(dateParts[dateDayIndex], 10);\n\n  // 检查年份是否有效\n  if (isNaN(year) || year < 1) {\n    return false;\n  }\n\n  // 检查月份是否有效\n  if (isNaN(month) || month < 0 || month > 11) {\n    return false;\n  }\n\n  // 检查日期是否有效\n  // 每个月的天数不同，需要考虑闰年\n  const daysInMonth = [31, isLeapYear(year) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n  if (isNaN(day) || day < 1 || day > daysInMonth[month]) {\n    return false;\n  }\n\n  return true;\n}\n// 修正后的 validateDate 函数\nfunction validateTime(dateParts: string[], format: string) {\n  // 如果格式包含时分秒，则进一步解析和验证\n  if (format.includes('hh') || format.includes('mm') || format.includes('ss')) {\n    const timeIndex = format.indexOf('hh') > -1 ? format.indexOf('hh') : format.indexOf('HH');\n    const hour = parseInt(dateParts[timeIndex], 10);\n    const minute = parseInt(dateParts[timeIndex + 1], 10);\n    const second = dateParts.length > timeIndex + 2 ? parseInt(dateParts[timeIndex + 2], 10) : 0;\n\n    if (isNaN(hour) || hour < 0 || hour > 23) {\n      return false;\n    }\n\n    if (isNaN(minute) || minute < 0 || minute > 59) {\n      return false;\n    }\n\n    if (isNaN(second) || second < 0 || second > 59) {\n      return false;\n    }\n  }\n\n  return true;\n}\n// 辅助函数，用于判断是否为闰年\nfunction isLeapYear(year: number) {\n  // 能被4整除且不能被100整除，或者能被400整除的年份是闰年\n  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;\n}\n\n// // 修正后的 parseDateFormat 函数\n// export function parseDateFormat(dateString: string) {\n//   const formats = [\n//     'yyyy-mm-dd',\n//     'dd-mm-yyyy',\n//     'mm/dd/yyyy',\n//     'yyyy/mm/dd',\n//     'dd/mm/yyyy',\n//     'yyyy.mm.dd',\n//     'mm.dd.yyyy',\n//     'dd.mm.yyyy'\n//   ];\n//   dateString = dateString.replace(/\\s+/g, ''); // 移除空格\n//   for (let i = 0; i < formats.length; i++) {\n//     const format = formats[i];\n//     const dateParts = dateString.split(getSeparator(format));\n//     const isValid = validateDate(dateParts, format);\n//     if (dateParts.length === 3 && isValid) {\n//       return format;\n//     }\n//   }\n//   return null;\n// }\n\n// 修正后的 parseDateFormat 函数\nexport function parseDateFormat(dateString: string) {\n  const formats = [\n    'yyyy-mm-dd',\n    'dd-mm-yyyy',\n    'mm/dd/yyyy',\n    'yyyy/mm/dd',\n    'dd/mm/yyyy',\n    'yyyy.mm.dd',\n    'mm.dd.yyyy',\n    'dd.mm.yyyy'\n  ];\n  const timeFormat = ['hh:mm:ss', 'hh:mm'];\n  dateString = dateString.trim(); // 移除空格\n  const dates = dateString.split(' ');\n  const date = dates[0];\n  const time = dates[1];\n  let dateFormatMatched;\n  let timeFormatMatched;\n  if (date) {\n    for (let i = 0; i < formats.length; i++) {\n      const format = formats[i];\n      const dateParts = date.split(getSeparator(format));\n      const isValid = validateDate(dateParts, format);\n      if (dateParts.length === 3 && isValid) {\n        dateFormatMatched = format;\n        break;\n      }\n    }\n  }\n  if (dateFormatMatched) {\n    if (time) {\n      for (let i = 0; i < timeFormat.length; i++) {\n        const format = timeFormat[i];\n        const timeParts = time.split(getSeparator(format));\n        const formatParts = format.split(getSeparator(format));\n        const isValid = validateTime(timeParts, format);\n        if (isValid && timeParts.length === formatParts.length) {\n          timeFormatMatched = format;\n          break;\n        }\n      }\n    }\n  }\n  if (date && time && dateFormatMatched && timeFormatMatched) {\n    return dateFormatMatched + ' ' + timeFormatMatched;\n  }\n  if (date && !time) {\n    return dateFormatMatched;\n  }\n  return 'yyyy-mm-dd hh:mm:ss';\n}\n\n// 根据日期格式获取分隔符正则表达式\nfunction getSeparator(format: string) {\n  // 找到日期格式中的分隔符\n  const separators = format.match(/[^\\w]/g);\n  if (separators) {\n    // 转义分隔符，以确保正则表达式正确处理特殊字符\n    const escapedSeparators = separators.map(s => '\\\\' + s).join('|');\n    return new RegExp(escapedSeparators, 'g');\n  }\n  return /[^\\w]/;\n}\n\nexport function parseStringTemplate(template: string, data: any) {\n  const result = template.replace(/\\{([^}]+)\\}/g, (match, key) => {\n    const keys = key.split('.');\n    let value = data;\n\n    for (const k of keys) {\n      if (value.hasOwnProperty(k)) {\n        value = value[k];\n      } else {\n        value = match; // 如果找不到对应的字段值，保持原样\n        break;\n      }\n    }\n\n    return value;\n  });\n  return result;\n}\n\nexport function toBoxArray<T>(obj: T | T[]): [T, T, T, T] {\n  if (!Array.isArray(obj)) {\n    return [obj /*top*/, obj /*right*/, obj /*bottom*/, obj /*left*/];\n  }\n  if (obj.length === 3) {\n    return [obj[0] /*top*/, obj[1] /*right*/, obj[2] /*bottom*/, obj[1] /*left*/];\n  }\n  if (obj.length === 2) {\n    return [obj[0] /*top*/, obj[1] /*right*/, obj[0] /*bottom*/, obj[1] /*left*/];\n  }\n  if (obj.length === 1) {\n    return [obj[0] /*top*/, obj[0] /*right*/, obj[0] /*bottom*/, obj[0] /*left*/];\n  }\n  // return obj as [T, T, T, T];//原先这种返回方式，会造成修改引用问题\n  return [obj[0] /*top*/, obj[1] /*right*/, obj[2] /*bottom*/, obj[3] /*left*/];\n}\n\nexport function getWeekNumber(currentDate: Date) {\n  // Calculate the week number within the year\n  const startOfYear = new Date(currentDate.getFullYear(), 0, 1);\n  // 以下代码的第一个 +1 是为了处理currentDate正好是一周当中的第一天0点0分0秒的情况 ；第2个 +1 是天索引从0开始的缘故\n  const weekNumber = Math.ceil(((currentDate.getTime() + 1 - startOfYear.getTime()) / 86400000 + 1) / 7);\n  return weekNumber;\n}\n\nexport function getWeekday(dateString: string | Date, format: 'long' | 'short' = 'long') {\n  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n  const date = new Date(dateString);\n\n  if (format === 'short') {\n    return days[date.getDay()].substr(0, 3);\n  } else if (format === 'long') {\n    return days[date.getDay()];\n  }\n  return 'Invalid format specified. Please use \"short\" or \"long\".';\n}\n/** 判断对象的属性是否可写 */\nexport function isPropertyWritable(obj: any, prop: string | number) {\n  const descriptor = Object.getOwnPropertyDescriptor(obj, prop);\n  if (!descriptor) {\n    // 属性不存在\n    return false;\n  }\n\n  // 检查是否有 setter 方法或 writable 属性为 true\n  return !!descriptor.set || descriptor.writable === true;\n}\n\n/** 创建日期 */\nexport function createDateAtMidnight(dateStr?: string | number | Date, forceMidnight: boolean = false): Date {\n  let date;\n  if (dateStr) {\n    date = new Date(dateStr);\n    if (typeof dateStr === 'string') {\n      if (dateStr.length > 10) {\n        if (forceMidnight) {\n          date.setHours(0, 0, 0, 0);\n        }\n        // 如果 dateStr 是字符串类型且包含时分秒，不需要设置为午夜\n        return date;\n      }\n      date.setHours(0, 0, 0, 0);\n    }\n  } else {\n    date = new Date();\n  }\n  if (forceMidnight) {\n    date.setHours(0, 0, 0, 0);\n  }\n  return date;\n}\nexport function createDateAtLastMinute(dateStr?: string | number | Date, forceSetMinute: boolean = false): Date {\n  let date;\n  if (dateStr) {\n    date = new Date(dateStr);\n    if (typeof dateStr === 'string') {\n      if (dateStr.length > 10) {\n        if (forceSetMinute) {\n          date.setMinutes(59, 59, 999);\n        }\n        // 如果 dateStr 是字符串类型且包含时分秒，不需要设置为午夜\n        return date;\n      }\n      date.setMinutes(59, 59, 999);\n    }\n  } else {\n    date = new Date();\n  }\n  if (forceSetMinute) {\n    date.setMinutes(59, 59, 999);\n  }\n  return date;\n}\n\nexport function createDateAtLastSecond(dateStr?: string | number | Date, forceSetSecond: boolean = false): Date {\n  let date;\n  if (dateStr) {\n    date = new Date(dateStr);\n    if (typeof dateStr === 'string') {\n      if (dateStr.length > 10) {\n        if (forceSetSecond) {\n          date.setSeconds(59, 999);\n        }\n        // 如果 dateStr 是字符串类型且包含时分秒，不需要设置为午夜\n        return date;\n      }\n      date.setSeconds(59, 999);\n    }\n  } else {\n    date = new Date();\n  }\n  if (forceSetSecond) {\n    date.setSeconds(59, 999);\n  }\n  return date;\n}\n\nexport function createDateAtLastMillisecond(\n  dateStr?: string | number | Date,\n  forceSetMillisecond: boolean = false\n): Date {\n  let date;\n  if (dateStr) {\n    date = new Date(dateStr);\n    if (typeof dateStr === 'string') {\n      if (dateStr.length > 10) {\n        if (forceSetMillisecond) {\n          date.setMilliseconds(999);\n        }\n        // 如果 dateStr 是字符串类型且包含时分秒，不需要设置为午夜\n        return date;\n      }\n      date.setMilliseconds(999);\n    }\n  } else {\n    date = new Date();\n  }\n  if (forceSetMillisecond) {\n    date.setMilliseconds(999);\n  }\n  return date;\n}\n/** 创建日期 */\nexport function createDateAtLastHour(dateStr?: string | number | Date, forceLastHour: boolean = false): Date {\n  let date;\n  if (dateStr) {\n    date = new Date(dateStr);\n    if (typeof dateStr === 'string') {\n      if (dateStr.length > 10) {\n        if (forceLastHour) {\n          date.setHours(23, 59, 59, 999);\n        }\n        // 如果 dateStr 是字符串类型且包含时分秒，不需要设置为午夜\n        return date;\n      }\n      date.setHours(23, 59, 59, 999);\n    }\n  } else {\n    date = new Date();\n  }\n  if (forceLastHour) {\n    date.setHours(23, 59, 59, 999);\n  }\n  return date;\n}\n\nexport function getEndDateByTimeUnit(\n  startDate: Date,\n  date: Date,\n  timeScale: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'hour' | 'minute' | 'second',\n  step: number\n) {\n  let endDate = new Date(date);\n\n  switch (timeScale) {\n    case 'second':\n      endDate.setMilliseconds(999);\n      break;\n    case 'minute':\n      endDate.setSeconds(59, 999);\n      break;\n    case 'hour':\n      endDate.setMinutes(59, 59, 999);\n      break;\n    case 'day':\n      endDate.setHours(23, 59, 59, 999);\n      break;\n    case 'week':\n      const day = endDate.getDay();\n      const diffToEndOfWeek = 6 - day;\n      endDate.setDate(endDate.getDate() + diffToEndOfWeek);\n      endDate.setHours(23, 59, 59, 999);\n      break;\n    case 'month':\n      const lastDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0).getDate();\n      endDate.setDate(lastDayOfMonth);\n      endDate.setHours(23, 59, 59, 999);\n      break;\n    case 'quarter':\n      const currentMonth = endDate.getMonth();\n      const endMonthOfQuarter = currentMonth - (currentMonth % 3) + 2;\n      const lastDayOfQuarter = new Date(endDate.getFullYear(), endMonthOfQuarter + 1, 0).getDate();\n      endDate.setMonth(endMonthOfQuarter, lastDayOfQuarter);\n      endDate.setHours(23, 59, 59, 999);\n      break;\n    case 'year':\n      endDate.setMonth(11, 31);\n      endDate.setHours(23, 59, 59, 999);\n      break;\n    default:\n      throw new Error('Invalid time scale');\n  }\n\n  const count = computeCountToTimeScale(endDate, startDate, timeScale, step, 1);\n  const targetCount = Math.ceil(count);\n  if (targetCount > count) {\n    const dif = (targetCount - count) * step;\n    const msInSecond = 1000;\n    const msInMinute = msInSecond * 60;\n    const msInHour = msInMinute * 60;\n    const msInDay = msInHour * 24;\n    const msInWeek = msInDay * 7;\n    const adjusted_date = new Date(endDate.getTime() + 1);\n    switch (timeScale) {\n      case 'second':\n        endDate.setTime(endDate.getTime() + dif * msInSecond);\n        break;\n      case 'minute':\n        endDate.setTime(endDate.getTime() + dif * msInMinute);\n        break;\n      case 'hour':\n        endDate.setTime(endDate.getTime() + dif * msInHour);\n        break;\n      case 'day':\n        endDate.setTime(endDate.getTime() + dif * msInDay);\n        break;\n      case 'week':\n        endDate.setTime(endDate.getTime() + dif * msInWeek);\n        break;\n      case 'month':\n        endDate = new Date(endDate.getFullYear(), endDate.getMonth() + 1 + Math.round(dif), 0);\n        // endDate.setTime(lastDayOfMonth);\n        endDate.setHours(23, 59, 59, 999);\n        break;\n      case 'quarter':\n        const currentMonth = endDate.getMonth();\n        const endMonthOfQuarter = currentMonth - (currentMonth % 3) + 2;\n        endDate = new Date(endDate.getFullYear(), endMonthOfQuarter + Math.round(dif * 3) + 1, 0);\n        endDate.setHours(23, 59, 59, 999);\n        break;\n      case 'year':\n        endDate.setFullYear(endDate.getFullYear() + Math.floor(dif));\n        endDate.setHours(23, 59, 59, 999);\n        break;\n      default:\n        throw new Error('Invalid time scale');\n    }\n  }\n\n  return endDate;\n}\n\n// export function getEndDateByTimeUnit(\n//   startDate: Date,\n//   date: Date,\n//   timeScale: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'hour' | 'minute' | 'second',\n//   step: number\n// ): Date {\n//   const count = computeCountToTimeScale(date, startDate, timeScale, step);\n//   const targetCount = Math.ceil(count);\n//   debugger;\n//   const endDate = new Date(startDate);\n\n//   switch (timeScale) {\n//     case 'second':\n//       endDate.setSeconds(endDate.getSeconds() + targetCount * step);\n//       endDate.setMilliseconds(999);\n//       break;\n//     case 'minute':\n//       endDate.setMinutes(endDate.getMinutes() + targetCount * step);\n//       endDate.setSeconds(59, 999);\n//       break;\n//     case 'hour':\n//       endDate.setHours(endDate.getHours() + targetCount * step);\n//       endDate.setMinutes(59, 59, 999);\n//       break;\n//     case 'day':\n//       endDate.setDate(endDate.getDate() + targetCount * step);\n//       endDate.setHours(23, 59, 59, 999);\n//       break;\n//     case 'week':\n//       endDate.setDate(endDate.getDate() + targetCount * step * 7);\n//       endDate.setHours(23, 59, 59, 999);\n//       break;\n//     case 'month':\n//       endDate.setMonth(endDate.getMonth() + targetCount * step);\n//       const lastDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0).getDate();\n//       endDate.setDate(lastDayOfMonth);\n//       endDate.setHours(23, 59, 59, 999);\n//       break;\n//     case 'quarter':\n//       endDate.setMonth(endDate.getMonth() + targetCount * step * 3);\n//       const lastDayOfQuarter = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0).getDate();\n//       endDate.setDate(lastDayOfQuarter);\n//       endDate.setHours(23, 59, 59, 999);\n//       break;\n//     case 'year':\n//       endDate.setFullYear(endDate.getFullYear() + targetCount * step);\n//       endDate.setMonth(11, 31);\n//       endDate.setHours(23, 59, 59, 999);\n//       break;\n//     default:\n//       throw new Error('Invalid time scale');\n//   }\n\n//   return endDate;\n// }\n\nexport function getStartDateByTimeUnit(\n  date: Date,\n  timeScale: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'hour' | 'minute' | 'second',\n  startOfWeekSetting = 'monday'\n): Date {\n  const startDate = new Date(date);\n\n  switch (timeScale) {\n    case 'second':\n      startDate.setMilliseconds(0);\n      break;\n    case 'minute':\n      startDate.setSeconds(0, 0);\n      break;\n    case 'hour':\n      startDate.setMinutes(0, 0, 0);\n      break;\n    case 'day':\n      startDate.setHours(0, 0, 0, 0);\n      break;\n    case 'week':\n      const day = startDate.getDay();\n      let diffToStartOfWeek = day;\n      if (startOfWeekSetting === 'monday') {\n        diffToStartOfWeek = day === 0 ? -6 : 1 - day; // Adjusting for Sunday as the start of the week\n      } else {\n        diffToStartOfWeek = -day;\n      }\n\n      startDate.setDate(startDate.getDate() + diffToStartOfWeek);\n      startDate.setHours(0, 0, 0, 0);\n      break;\n    case 'month':\n      startDate.setDate(1);\n      startDate.setHours(0, 0, 0, 0);\n      break;\n    case 'quarter':\n      const currentMonth = startDate.getMonth();\n      const startMonthOfQuarter = currentMonth - (currentMonth % 3);\n      startDate.setMonth(startMonthOfQuarter, 1);\n      startDate.setHours(0, 0, 0, 0);\n      break;\n    case 'year':\n      startDate.setMonth(0, 1);\n      startDate.setHours(0, 0, 0, 0);\n      break;\n    default:\n      throw new Error('Invalid time scale');\n  }\n\n  return startDate;\n}\n\nexport function computeCountToTimeScale(\n  date: Date,\n  startDate: Date,\n  timeScale: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'hour' | 'minute' | 'second',\n  step: number,\n  diffMS: number = 0\n): number {\n  const msInSecond = 1000;\n  const msInMinute = msInSecond * 60;\n  const msInHour = msInMinute * 60;\n  const msInDay = msInHour * 24;\n  const msInWeek = msInDay * 7;\n\n  let difference: number;\n  const adjusted_date = new Date(date.getTime() + diffMS);\n  switch (timeScale) {\n    case 'second':\n      difference = (adjusted_date.getTime() - startDate.getTime()) / msInSecond;\n      break;\n    case 'minute':\n      difference = (adjusted_date.getTime() - startDate.getTime()) / msInMinute;\n      break;\n    case 'hour':\n      difference = (adjusted_date.getTime() - startDate.getTime()) / msInHour;\n      break;\n    case 'day':\n      difference = (adjusted_date.getTime() - startDate.getTime()) / msInDay;\n      break;\n    case 'week':\n      difference = (adjusted_date.getTime() - startDate.getTime()) / msInWeek;\n      break;\n    case 'month':\n      difference =\n        (adjusted_date.getFullYear() - startDate.getFullYear()) * 12 +\n        (adjusted_date.getMonth() - startDate.getMonth());\n      difference +=\n        (adjusted_date.getDate() - startDate.getDate()) /\n        new Date(adjusted_date.getFullYear(), adjusted_date.getMonth() + 1, 0).getDate();\n      break;\n    case 'quarter':\n      difference =\n        (adjusted_date.getFullYear() - startDate.getFullYear()) * 4 +\n        Math.floor(adjusted_date.getMonth() / 3) -\n        Math.floor(startDate.getMonth() / 3);\n      difference +=\n        (adjusted_date.getDate() - startDate.getDate()) /\n        (3 * new Date(adjusted_date.getFullYear(), adjusted_date.getMonth() + 1, 0).getDate());\n      break;\n    case 'year':\n      difference = adjusted_date.getFullYear() - startDate.getFullYear();\n      difference += (adjusted_date.getMonth() - startDate.getMonth()) / 12;\n      break;\n    default:\n      throw new Error('Invalid time scale');\n  }\n\n  return difference / step;\n}\n\n/** 暂时没有用上  函数为了解析日期的余数 */\nexport function parseDateToTimeUnit(\n  date: Date,\n  timeUnit: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'hour' | 'minute' | 'second'\n): number {\n  const millisecondsInSecond = 1000;\n  const secondsInMinute = 60;\n  const minutesInHour = 60;\n  const hoursInDay = 24;\n  const daysInWeek = 7;\n  const monthsInYear = 12;\n  const quartersInYear = 4;\n\n  const millisecondsInMinute = millisecondsInSecond * secondsInMinute;\n  const millisecondsInHour = millisecondsInMinute * minutesInHour;\n  const millisecondsInDay = millisecondsInHour * hoursInDay;\n  const millisecondsInWeek = millisecondsInDay * daysInWeek;\n  const millisecondsInMonth = millisecondsInDay * (365.25 / monthsInYear); // 近似值\n  const millisecondsInQuarter = millisecondsInMonth * (monthsInYear / quartersInYear); // 近似值\n  const millisecondsInYear = millisecondsInDay * 365.25; // 近似值\n  const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();\n  switch (timeUnit) {\n    case 'second':\n      return date.getMilliseconds() / millisecondsInSecond;\n    case 'minute':\n      return (date.getSeconds() + date.getMilliseconds() / millisecondsInSecond) / secondsInMinute;\n    case 'hour':\n      return (\n        (date.getMinutes() * secondsInMinute + date.getSeconds() + date.getMilliseconds() / millisecondsInSecond) /\n        (minutesInHour * secondsInMinute)\n      );\n    case 'day':\n      return (\n        (date.getHours() * minutesInHour * secondsInMinute +\n          date.getMinutes() * secondsInMinute +\n          date.getSeconds() +\n          date.getMilliseconds() / millisecondsInSecond) /\n        (hoursInDay * minutesInHour * secondsInMinute)\n      );\n    case 'week':\n      return (\n        (date.getDay() * hoursInDay * minutesInHour * secondsInMinute +\n          date.getHours() * minutesInHour * secondsInMinute +\n          date.getMinutes() * secondsInMinute +\n          date.getSeconds() +\n          date.getMilliseconds() / millisecondsInSecond) /\n        (daysInWeek * hoursInDay * minutesInHour * secondsInMinute)\n      );\n    case 'month':\n      return (\n        ((date.getDate() - 1) * hoursInDay * minutesInHour * secondsInMinute +\n          date.getHours() * minutesInHour * secondsInMinute +\n          date.getMinutes() * secondsInMinute +\n          date.getSeconds() +\n          date.getMilliseconds() / millisecondsInSecond) /\n        (daysInMonth * hoursInDay * minutesInHour * secondsInMinute)\n      );\n    case 'quarter':\n      const monthInQuarter = date.getMonth() % 3;\n      const daysInQuarter = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate() * 3;\n      return (\n        ((monthInQuarter * daysInMonth + date.getDate() - 1) * hoursInDay * minutesInHour * secondsInMinute +\n          date.getHours() * minutesInHour * secondsInMinute +\n          date.getMinutes() * secondsInMinute +\n          date.getSeconds() +\n          date.getMilliseconds() / millisecondsInSecond) /\n        (daysInQuarter * hoursInDay * minutesInHour * secondsInMinute)\n      );\n    case 'year':\n      const daysInYear = isLeapYear(date.getFullYear()) ? 366 : 365;\n      return (\n        ((date.getMonth() * daysInMonth + date.getDate() - 1) * hoursInDay * minutesInHour * secondsInMinute +\n          date.getHours() * minutesInHour * secondsInMinute +\n          date.getMinutes() * secondsInMinute +\n          date.getSeconds() +\n          date.getMilliseconds() / millisecondsInSecond) /\n        (daysInYear * hoursInDay * minutesInHour * secondsInMinute)\n      );\n    default:\n      throw new Error('Invalid time unit');\n  }\n}\n\n// // 示例用法\n// const date = new Date('2024-07-04T17:20:30');\n// const timeUnit = 'hour';\n// const result = parseDateToTimeUnit(date, timeUnit);\n// console.log(result); // 输出相对于小时的时间差值\n", "import type { FederatedPointerEvent } from '@visactor/vtable/es/vrender';\n\nimport type { Gantt } from '../Gantt';\nimport { InteractionState } from '../ts-types';\nimport type { EventManager } from './event-manager';\nimport { throttle } from '../tools/util';\nimport type { StateManager } from '../state/state-manager';\n/**\n *\n * @param event\n * @param state\n * @param isWheelEvent 是否是由鼠标或者触摸板原生滚动事件触发进入？\n */\nexport function handleWhell(event: WheelEvent, state: StateManager, gantt: Gantt, isWheelEvent: boolean = true) {\n  let { deltaX, deltaY } = event;\n  // 如果按住了shift 则进行横向滚动 纵向不滚动\n  if (event.shiftKey && event.deltaY) {\n    //mac电脑按住shift 鼠标滚动deltaX和deltaY是自动互换的，所以此逻辑只针对windows电脑有效及mac触摸板有效\n    deltaX = deltaY;\n    deltaY = 0;\n  }\n  const [optimizedDeltaX, optimizedDeltaY] = optimizeScrollXY(deltaX, deltaY, { horizontal: 1, vertical: 1 });\n  if (optimizedDeltaX || optimizedDeltaY) {\n    // if (state.interactionState !== InteractionState.scrolling) {\n    //   state.updateInteractionState(InteractionState.scrolling);\n    // }\n  }\n\n  if (optimizedDeltaX) {\n    state.setScrollLeft(state.scroll.horizontalBarPos + optimizedDeltaX);\n    gantt.scenegraph.scrollbarComponent.showHorizontalScrollBar(true);\n  }\n  if (optimizedDeltaY) {\n    state.setScrollTop(state.scroll.verticalBarPos + optimizedDeltaY);\n    gantt.scenegraph.scrollbarComponent.showVerticalScrollBar(true);\n  }\n  isWheelEvent && state.resetInteractionState();\n  if (\n    event.cancelable &&\n    (state._gantt.parsedOptions.overscrollBehavior === 'none' ||\n      (Math.abs(deltaY) >= Math.abs(deltaX) && deltaY !== 0 && isVerticalScrollable(deltaY, state)) ||\n      (Math.abs(deltaY) <= Math.abs(deltaX) && deltaX !== 0 && isHorizontalScrollable(deltaX, state)))\n  ) {\n    event.preventDefault();\n  }\n}\n\ninterface ScrollSpeedRatio {\n  horizontal?: number;\n  vertical?: number;\n}\n\n/**\n * 优化滚动方向，对于小角度的滚动，固定为一个方向\n * @param x\n * @param y\n * @param ratio\n */\nfunction optimizeScrollXY(x: number, y: number, ratio: ScrollSpeedRatio): [number, number] {\n  const ANGLE = 2; // 调参 根据斜率来调整xy方向的划分\n  const angle = Math.abs(x / y);\n\n  // 经过滚动优化之后的 x, y\n  const deltaX = angle <= 1 / ANGLE ? 0 : x;\n  const deltaY = angle > ANGLE ? 0 : y;\n\n  return [Math.ceil(deltaX * (ratio.horizontal ?? 0)), Math.ceil(deltaY * (ratio.vertical ?? 0))];\n}\n\nexport function isVerticalScrollable(deltaY: number, state: StateManager) {\n  const totalHeight = state._gantt.getAllRowsHeight() - state._gantt.scenegraph.height;\n  if (totalHeight === 0) {\n    return false;\n  }\n  return !isScrollToTop(deltaY, state) && !isScrollToBottom(deltaY, state);\n}\n\nexport function isHorizontalScrollable(deltaX: number, state: StateManager) {\n  const totalWidth = state._gantt.getAllDateColsWidth() - state._gantt.scenegraph.width;\n  if (totalWidth === 0) {\n    return false;\n  }\n  return !isScrollToLeft(deltaX, state) && !isScrollToRight(deltaX, state);\n}\n\nfunction isScrollToTop(deltaY: number, state: StateManager) {\n  const totalHeight = state._gantt.getAllRowsHeight() - state._gantt.scenegraph.height;\n  return totalHeight !== 0 && deltaY <= 0 && state.scroll.verticalBarPos < 1;\n}\n\nfunction isScrollToBottom(deltaY: number, state: StateManager) {\n  const totalHeight = state._gantt.getAllRowsHeight() - state._gantt.scenegraph.height;\n  return totalHeight !== 0 && deltaY >= 0 && Math.abs(state.scroll.verticalBarPos - totalHeight) < 1;\n}\n\nfunction isScrollToLeft(deltaX: number, state: StateManager) {\n  const totalWidth = state._gantt.getAllDateColsWidth() - state._gantt.scenegraph.width;\n  return totalWidth !== 0 && deltaX <= 0 && state.scroll.horizontalBarPos < 1;\n}\n\nfunction isScrollToRight(deltaX: number, state: StateManager) {\n  const totalWidth = state._gantt.getAllDateColsWidth() - state._gantt.scenegraph.width;\n  return totalWidth !== 0 && deltaX >= 0 && Math.abs(state.scroll.horizontalBarPos - totalWidth) < 1;\n}\n\nexport class InertiaScroll {\n  friction: number;\n  lastTime: number;\n  speedX: number;\n  speedY: number;\n  stateManager: StateManager;\n  runingId: number;\n  scrollHandle: (dx: number, dy: number) => void;\n  constructor(stateManager: StateManager) {\n    this.stateManager = stateManager;\n  }\n  setScrollHandle(scrollHandle: (dx: number, dy: number) => void) {\n    this.scrollHandle = scrollHandle;\n  }\n\n  startInertia(speedX: number, speedY: number, friction: number) {\n    this.lastTime = Date.now();\n    this.speedX = speedX;\n    this.speedY = speedY;\n    this.friction = friction;\n    if (!this.runingId) {\n      this.runingId = requestAnimationFrame(this.inertia.bind(this));\n    }\n  }\n  inertia() {\n    const now = Date.now();\n    const dffTime = now - this.lastTime;\n    let stopped = true;\n    const f = Math.pow(this.friction, dffTime / 16);\n    const newSpeedX = f * this.speedX;\n    const newSpeedY = f * this.speedY;\n    let dx = 0;\n    let dy = 0;\n    if (Math.abs(newSpeedX) > 0.05) {\n      stopped = false;\n      dx = ((this.speedX + newSpeedX) / 2) * dffTime;\n    }\n    if (Math.abs(newSpeedY) > 0.05) {\n      stopped = false;\n      dy = ((this.speedY + newSpeedY) / 2) * dffTime;\n    }\n    this.scrollHandle?.(dx, dy);\n    if (stopped) {\n      this.runingId = null;\n      return;\n    }\n    this.lastTime = now;\n    this.speedX = newSpeedX;\n    this.speedY = newSpeedY;\n\n    this.runingId = requestAnimationFrame(this.inertia.bind(this));\n  }\n  endInertia() {\n    cancelAnimationFrame(this.runingId);\n    this.runingId = null;\n  }\n  isInertiaScrolling() {\n    return !!this.runingId;\n  }\n}\n\nexport function bindScrollBarListener(eventManager: EventManager) {\n  const table = eventManager._gantt;\n  const stateManager = table.stateManager;\n  const scenegraph = table.scenegraph;\n\n  // 监听滚动条组件pointover事件\n  scenegraph.scrollbarComponent.vScrollBar.addEventListener('pointerover', (e: any) => {\n    scenegraph.scrollbarComponent.showVerticalScrollBar();\n  });\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('pointerover', (e: any) => {\n    scenegraph.scrollbarComponent.showHorizontalScrollBar();\n  });\n  scenegraph.scrollbarComponent.vScrollBar.addEventListener('pointerout', (e: any) => {\n    if (stateManager.interactionState === InteractionState.scrolling) {\n      return;\n    }\n    scenegraph.scrollbarComponent.hideVerticalScrollBar();\n  });\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('pointerout', (e: any) => {\n    if (stateManager.interactionState === InteractionState.scrolling) {\n      return;\n    }\n    scenegraph.scrollbarComponent.hideHorizontalScrollBar();\n  });\n  scenegraph.scrollbarComponent.vScrollBar.addEventListener('pointermove', (e: FederatedPointerEvent) => {\n    // scenegraph._gantt.stateManager.updateCursor('default');\n    e.stopPropagation(); //防止冒泡到stage上 检测到挨着列间隔线判断成可拖拽\n  });\n  // scenegraph.scrollbarComponent.vScrollBar.addEventListener('pointerdown', (e: FederatedPointerEvent) => {\n  //   e.stopPropagation(); //防止冒泡到stage上 检测到挨着列间隔线判断成拖拽状态\n  //   if ((scenegraph._gantt as any).hasListeners(TABLE_EVENT_TYPE.MOUSEDOWN_TABLE)) {\n  //     scenegraph._gantt.fireListeners(TABLE_EVENT_TYPE.MOUSEDOWN_TABLE, {\n  //       event: e.nativeEvent\n  //     });\n  //   }\n  // });\n  scenegraph.scrollbarComponent.vScrollBar.addEventListener('scrollDown', (e: FederatedPointerEvent) => {\n    // scenegraph._gantt.eventManager.LastBodyPointerXY = { x: e.x, y: e.y };\n    scenegraph._gantt.eventManager.isDown = true;\n\n    // if ((scenegraph._gantt as any).hasListeners(TABLE_EVENT_TYPE.MOUSEDOWN_TABLE)) {\n    //   scenegraph._gantt.fireListeners(TABLE_EVENT_TYPE.MOUSEDOWN_TABLE, {\n    //     event: e.nativeEvent\n    //   });\n    // }\n  });\n  scenegraph.scrollbarComponent.vScrollBar.addEventListener('pointerup', () => {\n    // stateManager.fastScrolling = false;\n    scenegraph._gantt.eventManager.isDraging = false;\n    if (stateManager.interactionState === InteractionState.scrolling) {\n      stateManager.updateInteractionState(InteractionState.default);\n    }\n  });\n  scenegraph.scrollbarComponent.vScrollBar.addEventListener('pointerupoutside', () => {\n    // stateManager.fastScrolling = false;\n    if (stateManager.interactionState === InteractionState.scrolling) {\n      stateManager.updateInteractionState(InteractionState.default);\n    }\n  });\n  scenegraph.scrollbarComponent.vScrollBar.addEventListener('scrollUp', (e: FederatedPointerEvent) => {\n    scenegraph._gantt.eventManager.isDraging = false;\n  });\n\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('pointermove', (e: FederatedPointerEvent) => {\n    // scenegraph._gantt.stateManager.updateCursor('default');\n    e.stopPropagation(); //防止冒泡到stage上 检测到挨着列间隔线判断成可拖拽\n  });\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('pointerdown', (e: FederatedPointerEvent) => {\n    e.stopPropagation(); //防止冒泡到stage上 检测到挨着列间隔线判断成拖拽状态\n    // if ((scenegraph._gantt as any).hasListeners(TABLE_EVENT_TYPE.MOUSEDOWN_TABLE)) {\n    //   scenegraph._gantt.fireListeners(TABLE_EVENT_TYPE.MOUSEDOWN_TABLE, {\n    //     event: e.nativeEvent\n    //   });\n    // }\n  });\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('scrollDown', (e: FederatedPointerEvent) => {\n    // scenegraph._gantt.eventManager.LastBodyPointerXY = { x: e.x, y: e.y };\n    scenegraph._gantt.eventManager.isDown = true;\n    if (stateManager.interactionState !== InteractionState.scrolling) {\n      stateManager.updateInteractionState(InteractionState.scrolling);\n    }\n\n    // if ((scenegraph._gantt as any).hasListeners(TABLE_EVENT_TYPE.MOUSEDOWN_TABLE)) {\n    //   scenegraph._gantt.fireListeners(TABLE_EVENT_TYPE.MOUSEDOWN_TABLE, {\n    //     event: e.nativeEvent\n    //   });\n    // }\n  });\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('pointerup', () => {\n    // stateManager.fastScrolling = false;\n    // scenegraph._gantt.eventManager.isDraging = false;\n    if (stateManager.interactionState === InteractionState.scrolling) {\n      stateManager.updateInteractionState(InteractionState.default);\n    }\n  });\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('pointerupoutside', () => {\n    // stateManager.fastScrolling = false;\n    if (stateManager.interactionState === InteractionState.scrolling) {\n      stateManager.updateInteractionState(InteractionState.default);\n    }\n  });\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('scrollUp', (e: FederatedPointerEvent) => {\n    scenegraph._gantt.eventManager.isDraging = false;\n  });\n  const throttleVerticalWheel = throttle(stateManager.updateVerticalScrollBar, 20);\n  const throttleHorizontalWheel = throttle(stateManager.updateHorizontalScrollBar, 20);\n\n  // 监听滚动条组件scroll事件\n  scenegraph.scrollbarComponent.vScrollBar.addEventListener('scrollDrag', (e: any) => {\n    if (scenegraph._gantt.eventManager.isDown) {\n      scenegraph._gantt.eventManager.isDraging = true;\n    }\n    // stateManager.fastScrolling = true;\n    if (stateManager.interactionState !== InteractionState.scrolling) {\n      stateManager.updateInteractionState(InteractionState.scrolling);\n    }\n    const ratio = e.detail.value[0] / (1 - e.detail.value[1] + e.detail.value[0]);\n    throttleVerticalWheel(ratio, e);\n  });\n\n  scenegraph.scrollbarComponent.hScrollBar.addEventListener('scrollDrag', (e: any) => {\n    if (scenegraph._gantt.eventManager.isDown) {\n      scenegraph._gantt.eventManager.isDraging = true;\n    }\n    stateManager.fastScrolling = true;\n    if (stateManager.interactionState !== InteractionState.scrolling) {\n      stateManager.updateInteractionState(InteractionState.scrolling);\n    }\n    // stateManager._gantt.scenegraph.proxy.isSkipProgress = true;\n    const ratio = e.detail.value[0] / (1 - e.detail.value[1] + e.detail.value[0]);\n    throttleHorizontalWheel(ratio);\n  });\n}\n", "import { text } from 'stream/consumers';\nimport type { Gantt } from './Gantt';\nimport {\n  TasksShowMode,\n  type IMarkLine,\n  type IScrollStyle,\n  type ITimelineDateInfo,\n  type ITimelineScale\n} from './ts-types';\nimport {\n  createDateAtLastHour,\n  createDateAtLastMillisecond,\n  createDateAtLastMinute,\n  createDateAtLastSecond,\n  createDateAtMidnight,\n  getEndDateByTimeUnit,\n  getStartDateByTimeUnit,\n  getWeekNumber\n} from './tools/util';\n\nconst isNode = typeof window === 'undefined' || typeof window.window === 'undefined';\nexport const DayTimes = 1000 * 60 * 60 * 24;\n/** 通过事件坐标y计算鼠标当前所在所几条任务条上。y是相对于canvas的坐标值，vrender事件返回的e.offset.y */\nexport function getTaskIndexByY(y: number, gantt: Gantt) {\n  const gridY = y - gantt.headerHeight;\n  const taskBarHeight = gantt.stateManager.scroll.verticalBarPos + gridY;\n  const taskBarIndex = Math.floor(taskBarHeight / gantt.parsedOptions.rowHeight);\n  return taskBarIndex;\n}\nexport function getDateIndexByX(x: number, gantt: Gantt) {\n  const totalX = x + gantt.stateManager.scroll.horizontalBarPos;\n  const firstDateColWidth = gantt.getDateColWidth(0);\n  const dateIndex = Math.floor((totalX - firstDateColWidth) / gantt.parsedOptions.timelineColWidth) + 1;\n  return dateIndex;\n}\n\nexport function generateMarkLine(markLine?: boolean | IMarkLine | IMarkLine[]): IMarkLine[] {\n  if (!markLine) {\n    return [];\n  }\n  if (markLine === true) {\n    return [\n      {\n        date: createDateAtMidnight().toLocaleDateString(),\n        scrollToMarkLine: true,\n        position: 'left',\n        style: {\n          lineColor: 'red',\n          lineWidth: 1\n        }\n      }\n    ];\n  } else if (Array.isArray(markLine)) {\n    return markLine.map((item, index) => {\n      return {\n        date: item.date,\n        scrollToMarkLine: item.scrollToMarkLine,\n        position: item.position ?? 'left',\n        style: {\n          lineColor: item.style?.lineColor || 'red',\n          lineWidth: item.style?.lineWidth || 1,\n          lineDash: item.style?.lineDash\n        }\n      };\n    });\n  }\n  return [\n    {\n      date: (markLine as IMarkLine).date,\n      scrollToMarkLine: (markLine as IMarkLine).scrollToMarkLine ?? true,\n      position: (markLine as IMarkLine).position ?? 'left',\n      style: {\n        lineColor: (markLine as IMarkLine).style?.lineColor || 'red',\n        lineWidth: (markLine as IMarkLine).style?.lineWidth || 1,\n        lineDash: (markLine as IMarkLine).style?.lineDash\n      }\n    }\n  ];\n}\n\nexport function getHorizontalScrollBarSize(scrollStyle?: IScrollStyle): number {\n  if (\n    scrollStyle?.hoverOn ||\n    (scrollStyle?.horizontalVisible && scrollStyle?.horizontalVisible === 'none') ||\n    (!scrollStyle?.horizontalVisible && scrollStyle?.visible === 'none')\n  ) {\n    return 0;\n  }\n  return scrollStyle?.width ?? 7;\n}\n\nexport function getVerticalScrollBarSize(scrollStyle?: IScrollStyle): number {\n  if (\n    scrollStyle?.hoverOn ||\n    (scrollStyle?.verticalVisible && scrollStyle?.verticalVisible === 'none') ||\n    (!scrollStyle?.verticalVisible && scrollStyle?.visible === 'none')\n  ) {\n    return 0;\n  }\n  return scrollStyle?.width ?? 7;\n}\n\nexport { isNode };\n\nexport function initOptions(gantt: Gantt) {\n  const options = gantt.options;\n  gantt.parsedOptions.tasksShowMode = options?.tasksShowMode ?? TasksShowMode.Tasks_Separate;\n  gantt.parsedOptions.pixelRatio = options?.pixelRatio ?? 1;\n  gantt.parsedOptions.rowHeight = options?.rowHeight ?? 40;\n  gantt.parsedOptions.timelineColWidth = options?.timelineHeader?.colWidth ?? 60;\n  gantt.parsedOptions.startDateField = options.taskBar?.startDateField ?? 'startDate';\n  gantt.parsedOptions.endDateField = options.taskBar?.endDateField ?? 'endDate';\n  gantt.parsedOptions.progressField = options.taskBar?.progressField ?? 'progress';\n  // gantt.parsedOptions.minDate = options?.minDate\n  //   ? gantt.parsedOptions.timeScaleIncludeHour\n  //     ? createDateAtMidnight(options.minDate)\n  //     : createDateAtMidnight(options.minDate, true)\n  //   : undefined;\n  // gantt.parsedOptions.maxDate = options?.maxDate\n  //   ? gantt.parsedOptions.timeScaleIncludeHour\n  //     ? createDateAtLastHour(options.maxDate)\n  //     : createDateAtLastHour(options.maxDate, true)\n  //   : undefined;\n  const { unit: minTimeUnit, startOfWeek, step } = gantt.parsedOptions.reverseSortedTimelineScales[0];\n  gantt.parsedOptions.minDate = options?.minDate\n    ? getStartDateByTimeUnit(new Date(options.minDate), minTimeUnit, startOfWeek)\n    : undefined;\n  gantt.parsedOptions.maxDate = options?.maxDate\n    ? getEndDateByTimeUnit(gantt.parsedOptions.minDate, new Date(options.maxDate), minTimeUnit, step)\n    : undefined;\n  gantt.parsedOptions._minDateTime = gantt.parsedOptions.minDate?.getTime();\n  gantt.parsedOptions._maxDateTime = gantt.parsedOptions.maxDate?.getTime();\n  gantt.parsedOptions.overscrollBehavior = options?.overscrollBehavior ?? 'auto';\n  gantt.parsedOptions.underlayBackgroundColor = options?.underlayBackgroundColor ?? '#FFF';\n  gantt.parsedOptions.scrollStyle = Object.assign(\n    {},\n    {\n      scrollRailColor: 'rgba(100, 100, 100, 0.2)',\n      scrollSliderColor: 'rgba(100, 100, 100, 0.5)',\n      scrollSliderCornerRadius: 4,\n      width: 10,\n      visible: 'always',\n      hoverOn: true,\n      barToSide: false\n    },\n    options?.scrollStyle\n  );\n\n  gantt.parsedOptions.timelineHeaderHorizontalLineStyle = options?.timelineHeader?.horizontalLine;\n  gantt.parsedOptions.timelineHeaderVerticalLineStyle = options?.timelineHeader?.verticalLine;\n  gantt.parsedOptions.timelineHeaderBackgroundColor = options?.timelineHeader?.backgroundColor;\n  gantt.parsedOptions.timeLineHeaderRowHeights = [];\n  gantt.parsedOptions.timelineHeaderStyles = [];\n  for (let i = 0; i < gantt.parsedOptions.sortedTimelineScales.length ?? 0; i++) {\n    const style = gantt.parsedOptions.sortedTimelineScales[i].style;\n    gantt.parsedOptions.timelineHeaderStyles.push(\n      Object.assign(\n        {\n          fontSize: 20,\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textBaseline: 'middle',\n          color: '#000',\n          backgroundColor: '#fff'\n        },\n        style\n      )\n    );\n\n    gantt.parsedOptions.timeLineHeaderRowHeights.push(\n      gantt.parsedOptions.sortedTimelineScales[i].rowHeight ?? options?.headerRowHeight ?? 40\n    );\n  }\n  gantt.parsedOptions.grid = Object.assign(\n    {},\n    // {\n    //   backgroundColor: '#fff',\n    //   vertical: {\n    //     lineColor: 'red',\n    //     lineWidth: 1\n    //   },\n    //   horizontal: {\n    //     lineColor: 'blue',\n    //     lineWidth: 1\n    //   }\n    // },\n    options?.grid\n  );\n  gantt.parsedOptions.taskBarStyle = Object.assign(\n    {},\n    {\n      barColor: 'blue',\n      /** 已完成部分任务条的颜色 */\n      completedBarColor: 'gray',\n      /** 任务条的宽度 */\n      width: (gantt.parsedOptions.rowHeight * 3) / 4,\n      /** 任务条的圆角 */\n      cornerRadius: 3,\n      /** 任务条的边框 */\n      borderWidth: 0,\n      /** 边框颜色 */\n      // borderColor: 'red',\n      fontFamily: 'Arial',\n      fontSize: 14\n    },\n    options?.taskBar?.barStyle\n  );\n\n  gantt.parsedOptions.dateFormat = options?.dateFormat;\n  gantt.parsedOptions.taskBarHoverStyle = Object.assign(\n    {\n      barOverlayColor: 'rgba(99, 144, 0, 0.4)'\n    },\n    options?.taskBar?.hoverBarStyle\n  );\n  gantt.parsedOptions.taskBarSelectable = options?.taskBar?.selectable ?? true;\n  gantt.parsedOptions.taskBarSelectedStyle = Object.assign(\n    {\n      shadowBlur: 6, //阴影宽度\n      shadowOffsetX: 0, //x方向偏移\n      shadowOffsetY: 0, //Y方向偏移\n      shadowColor: gantt.parsedOptions.taskBarStyle.barColor, //阴影颜色\n      borderColor: gantt.parsedOptions.taskBarStyle.barColor, //边框颜色\n      borderLineWidth: 1 //边框宽度\n    },\n    options?.taskBar?.selectedBarStyle\n  );\n  gantt.parsedOptions.taskBarLabelText = options?.taskBar?.labelText ?? '';\n  gantt.parsedOptions.taskBarMoveable = options?.taskBar?.moveable ?? true;\n  gantt.parsedOptions.taskBarResizable = options?.taskBar?.resizable ?? true;\n  gantt.parsedOptions.taskBarDragOrder = options?.taskBar?.dragOrder ?? true;\n\n  // gantt.parsedOptions.taskBarHoverColor =\n  //   options?.taskBar?.hoverColor === null ? 'rgba(0,0,0,0)' : options?.taskBar?.hoverColor ?? 'rgba(0,0,0,0.1)';\n  gantt.parsedOptions.taskBarLabelStyle = {\n    fontFamily: options?.taskBar?.labelTextStyle?.fontFamily ?? 'Arial',\n    fontSize: options?.taskBar?.labelTextStyle?.fontSize ?? 20,\n    color: options?.taskBar?.labelTextStyle?.color ?? '#F01',\n    textAlign: options?.taskBar?.labelTextStyle?.textAlign ?? 'left',\n    textBaseline: options?.taskBar?.labelTextStyle?.textBaseline ?? 'middle',\n    padding: options?.taskBar?.labelTextStyle?.padding ?? [0, 0, 0, 10],\n    textOverflow: options?.taskBar?.labelTextStyle?.textOverflow\n  };\n  gantt.parsedOptions.taskBarCustomLayout = options?.taskBar?.customLayout;\n  gantt.parsedOptions.taskBarCreatable = options?.taskBar?.scheduleCreatable ?? true;\n  gantt.parsedOptions.taskBarCreationButtonStyle = Object.assign(\n    {\n      lineColor: 'rgb(99, 144, 0)',\n      lineWidth: 1,\n      lineDash: [5, 5],\n      cornerRadius: 4,\n      backgroundColor: '#FFF'\n    },\n    options?.taskBar?.scheduleCreation?.buttonStyle\n  );\n  gantt.parsedOptions.taskBarCreationCustomLayout = options?.taskBar?.scheduleCreation?.customLayout;\n\n  gantt.parsedOptions.outerFrameStyle = Object.assign(\n    {\n      borderColor: '#e1e4e8',\n      borderLineWidth: 1,\n      cornerRadius: 4\n    },\n    options.frame?.outerFrameStyle\n  );\n  gantt.parsedOptions.markLine = generateMarkLine(options?.markLine);\n  if (gantt.parsedOptions.markLine?.length ?? 0) {\n    if (gantt.parsedOptions.markLine?.every(item => item.scrollToMarkLine === undefined)) {\n      gantt.parsedOptions.markLine[0].scrollToMarkLine = true;\n    }\n    if (gantt.parsedOptions.markLine?.find(item => item.scrollToMarkLine)) {\n      gantt.parsedOptions.scrollToMarkLineDate = getStartDateByTimeUnit(\n        new Date(gantt.parsedOptions.markLine?.find(item => item.scrollToMarkLine).date),\n        minTimeUnit,\n        startOfWeek\n      );\n    }\n  }\n  gantt.parsedOptions.verticalSplitLineHighlight = options.frame?.verticalSplitLineHighlight;\n\n  gantt.parsedOptions.verticalSplitLine = Object.assign(\n    {\n      lineColor: gantt.parsedOptions.outerFrameStyle?.borderColor,\n      lineWidth: gantt.parsedOptions.outerFrameStyle?.borderLineWidth\n    },\n    options.frame?.verticalSplitLine\n  );\n  gantt.parsedOptions.horizontalSplitLine = options.frame?.horizontalSplitLine;\n  gantt.parsedOptions.verticalSplitLineMoveable = options.frame?.verticalSplitLineMoveable;\n\n  gantt.parsedOptions.taskKeyField = options.taskKeyField ?? 'id';\n  gantt.parsedOptions.dependencyLinks = options.dependency?.links;\n  gantt.parsedOptions.dependencyLinkCreatable = options.dependency?.linkCreatable ?? false;\n  gantt.parsedOptions.dependencyLinkSelectable = options.dependency?.linkSelectable ?? true;\n  gantt.parsedOptions.dependencyLinkLineStyle = Object.assign(\n    {\n      lineColor: 'red',\n      lineWidth: 1\n    },\n    options.dependency?.linkLineStyle\n  );\n\n  gantt.parsedOptions.dependencyLinkSelectedLineStyle = Object.assign(\n    {\n      shadowBlur: 4, //阴影宽度\n      shadowOffset: 0, //方向偏移\n      shadowColor: gantt.parsedOptions.dependencyLinkLineStyle.lineColor, //阴影颜色\n      lineColor: gantt.parsedOptions.dependencyLinkLineStyle.lineColor,\n      lineWidth: gantt.parsedOptions.dependencyLinkLineStyle.lineWidth\n    },\n    options?.dependency?.linkSelectedLineStyle\n  );\n\n  gantt.parsedOptions.dependencyLinkLineCreatePointStyle = Object.assign(\n    {\n      strokeColor: 'red',\n      fillColor: 'white',\n      radius: 5,\n      strokeWidth: 1\n    },\n    options?.dependency?.linkCreatePointStyle\n  );\n  gantt.parsedOptions.dependencyLinkLineCreatingPointStyle = Object.assign(\n    {\n      strokeColor: 'red',\n      fillColor: 'red',\n      radius: 5,\n      strokeWidth: 1\n    },\n    options?.dependency?.linkCreatingPointStyle\n  );\n  gantt.parsedOptions.dependencyLinkLineCreatingStyle = Object.assign(\n    {\n      lineColor: 'red',\n      lineWidth: 1,\n      lineDash: [5, 5]\n    },\n    options?.dependency?.linkCreatingLineStyle\n  );\n}\nexport function updateOptionsWhenScaleChanged(gantt: Gantt) {\n  const options = gantt.options;\n\n  const { unit: minTimeUnit, startOfWeek, step } = gantt.parsedOptions.reverseSortedTimelineScales[0];\n  gantt.parsedOptions.minDate = getStartDateByTimeUnit(new Date(gantt.parsedOptions.minDate), minTimeUnit, startOfWeek);\n  gantt.parsedOptions.maxDate = getEndDateByTimeUnit(\n    gantt.parsedOptions.minDate,\n    new Date(gantt.parsedOptions.maxDate),\n    minTimeUnit,\n    step\n  );\n  gantt.parsedOptions._minDateTime = gantt.parsedOptions.minDate?.getTime();\n  gantt.parsedOptions._maxDateTime = gantt.parsedOptions.maxDate?.getTime();\n  gantt.parsedOptions.timeLineHeaderRowHeights = [];\n  gantt.parsedOptions.timelineHeaderStyles = [];\n  for (let i = 0; i < gantt.parsedOptions.sortedTimelineScales.length ?? 0; i++) {\n    const style = gantt.parsedOptions.sortedTimelineScales[i].style;\n    gantt.parsedOptions.timelineHeaderStyles.push(\n      Object.assign(\n        {\n          fontSize: 20,\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textBaseline: 'middle',\n          color: '#000',\n          backgroundColor: '#fff'\n        },\n        style\n      )\n    );\n\n    gantt.parsedOptions.timeLineHeaderRowHeights.push(\n      gantt.parsedOptions.sortedTimelineScales[i].rowHeight ?? options?.headerRowHeight ?? 40\n    );\n  }\n}\n\nexport function generateTimeLineDate(currentDate: Date, endDate: Date, scale: ITimelineScale) {\n  const { unit, step, format } = scale;\n  const timelineDates: ITimelineDateInfo[] = [];\n  while (currentDate < endDate) {\n    if (unit === 'day') {\n      const year = currentDate.getFullYear();\n      const month = currentDate.getMonth();\n      const day = currentDate.getDate();\n      const end = createDateAtLastHour(new Date(year, month, day + step - 1), true);\n      if (end.getTime() > endDate.getTime()) {\n        end.setTime(endDate.getTime());\n      }\n      const start = currentDate;\n      const formattedDate = format?.({ dateIndex: day, startDate: start, endDate: end });\n      const columnTitle = formattedDate || day.toString();\n      const dayCellConfig = {\n        days: Math.abs(end.getTime() - currentDate.getTime() + 1) / DayTimes,\n        startDate: start,\n        endDate: end,\n        step,\n        unit: 'day',\n        title: columnTitle,\n        dateIndex: day\n      };\n      timelineDates.push(dayCellConfig);\n      // currentDate.setTime(createDateAtMidnight(currentDate.getTime() + step * 24 * 60 * 60 * 1000, true).getTime());\n      currentDate = new Date(year, month, day + step);\n    } else if (unit === 'month') {\n      const year = currentDate.getFullYear();\n      const month = currentDate.getMonth(); //index 从0 开始\n      const end = createDateAtLastHour(new Date(year, month + step, 0), true);\n      if (end.getTime() > endDate.getTime()) {\n        end.setTime(endDate.getTime());\n      }\n      const start = currentDate;\n      const formattedDate = format?.({ dateIndex: month + 1, startDate: start, endDate: end });\n      const columnTitle = formattedDate || (month + 1).toString();\n      const dayCellConfig = {\n        days: Math.abs(end.getTime() - currentDate.getTime() + 1) / DayTimes,\n        startDate: start,\n        step,\n        unit: 'month',\n        endDate: end,\n        title: columnTitle,\n        dateIndex: month + 1\n      };\n\n      timelineDates.push(dayCellConfig);\n      currentDate = new Date(year, month + step, 1);\n    } else if (unit === 'quarter') {\n      const year = currentDate.getFullYear();\n      const quarter = Math.floor(currentDate.getMonth() / 3); //quarter 从0 开始\n      const end = createDateAtLastHour(new Date(year, (quarter + step) * 3, 0), true);\n      if (end.getTime() > endDate.getTime()) {\n        end.setTime(endDate.getTime());\n      }\n      const start = currentDate;\n      const formattedDate = format?.({ dateIndex: quarter + 1, startDate: start, endDate: end });\n      const columnTitle = formattedDate || (quarter + 1).toString();\n      const dayCellConfig = {\n        days: Math.abs(end.getTime() - currentDate.getTime() + 1) / (1000 * 60 * 60 * 24),\n        startDate: start,\n        step,\n        unit: 'quarter',\n        endDate: end,\n        title: columnTitle,\n        dateIndex: quarter + 1\n      };\n      timelineDates.push(dayCellConfig);\n      currentDate = new Date(year, (quarter + step) * 3, 1);\n    } else if (unit === 'year') {\n      const year = currentDate.getFullYear();\n      const end = createDateAtLastHour(new Date(year + step - 1, 11, 31), true);\n      if (end.getTime() > endDate.getTime()) {\n        end.setTime(endDate.getTime());\n      }\n      const start = currentDate;\n      const formattedDate = format?.({ dateIndex: year, startDate: start, endDate: end });\n      const columnTitle = formattedDate || year.toString();\n      const dayCellConfig = {\n        days: Math.abs(end.getTime() - currentDate.getTime() + 1) / DayTimes,\n        startDate: start,\n        endDate: end,\n        step,\n        unit: 'year',\n        title: columnTitle,\n        dateIndex: year\n      };\n      timelineDates.push(dayCellConfig);\n      currentDate = new Date(year + step, 0, 1);\n    } else if (unit === 'week') {\n      const startOfWeekSetting = scale.startOfWeek ?? 'monday';\n      let dayOfWeek = currentDate.getDay(); // index从0开始\n      if (startOfWeekSetting === 'monday') {\n        dayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Calculate the difference between the current day and the start of the week\n      }\n      const startOfWeek = createDateAtMidnight(currentDate);\n      // const endOfWeek = createDateAtLastHour(startOfWeek.getTime() + (6 - dayOfWeek) * 24 * 60 * 60 * 1000, true); // Calculate the end of the week\n      const dateEnd = createDateAtLastHour(\n        currentDate.getTime() + (7 * step - dayOfWeek) * 24 * 60 * 60 * 1000 - 1,\n        true\n      );\n      if (dateEnd > endDate) {\n        dateEnd.setTime(endDate.getTime());\n      }\n\n      // Calculate the week number within the year\n      // const startOfYear = new Date(currentDate.getFullYear(), 0, 1);\n      // const weekNumber = Math.ceil(((startOfWeek.getTime() - startOfYear.getTime()) / 86400000 + 1) / 7);\n      const weekNumber = getWeekNumber(startOfWeek);\n\n      const columnTitle =\n        format?.({ dateIndex: weekNumber, startDate: startOfWeek, endDate: dateEnd }) || weekNumber.toString();\n\n      const dayCellConfig = {\n        days: (dateEnd.getTime() - startOfWeek.getTime() + 1) / DayTimes,\n        // days: Math.abs(dateEnd.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24),\n        startDate: startOfWeek,\n        endDate: dateEnd,\n        step,\n        unit: 'week',\n        title: columnTitle,\n        dateIndex: weekNumber\n      };\n\n      timelineDates.push(dayCellConfig);\n\n      // Move currentDate to the next week\n      // currentDate.setDate(currentDate.getDate() + (7 - dayOfWeek));\n      currentDate.setTime(\n        createDateAtMidnight(currentDate.getTime() + (7 * step - dayOfWeek) * 24 * 60 * 60 * 1000, true).getTime()\n      );\n    } else if (unit === 'hour') {\n      const year = currentDate.getFullYear();\n      const month = currentDate.getMonth();\n      const day = currentDate.getDate();\n      const hour = currentDate.getHours();\n      const end = createDateAtLastMinute(new Date(year, month, day, hour + step - 1), true);\n      if (end.getTime() > endDate.getTime()) {\n        end.setTime(endDate.getTime());\n      }\n      const start = currentDate;\n      const formattedDate = format?.({ dateIndex: hour, startDate: start, endDate: end });\n      const columnTitle = formattedDate || hour.toString();\n      const dayCellConfig = {\n        days: Math.abs(end.getTime() - currentDate.getTime() + 1) / DayTimes,\n        startDate: start,\n        endDate: end,\n        step,\n        unit: 'hour',\n        title: columnTitle,\n        dateIndex: currentDate.getHours()\n      };\n      timelineDates.push(dayCellConfig);\n      currentDate = new Date(year, month, day, hour + step);\n    } else if (unit === 'minute') {\n      const year = currentDate.getFullYear();\n      const month = currentDate.getMonth();\n      const day = currentDate.getDate();\n      const hour = currentDate.getHours();\n      const minute = currentDate.getMinutes();\n      const end = createDateAtLastSecond(new Date(year, month, day, hour, minute + step - 1), true);\n      if (end.getTime() > endDate.getTime()) {\n        end.setTime(endDate.getTime());\n      }\n      const start = currentDate;\n      const formattedDate = format?.({ dateIndex: minute, startDate: start, endDate: end });\n      const columnTitle = formattedDate || minute.toString();\n      const dayCellConfig = {\n        days: Math.abs(end.getTime() - currentDate.getTime() + 1) / DayTimes,\n        startDate: start,\n        endDate: end,\n        step,\n        unit: 'minute',\n        title: columnTitle,\n        dateIndex: currentDate.getMinutes()\n      };\n      timelineDates.push(dayCellConfig);\n      currentDate = new Date(year, month, day, hour, minute + step);\n    } else if (unit === 'second') {\n      const year = currentDate.getFullYear();\n      const month = currentDate.getMonth();\n      const day = currentDate.getDate();\n      const hour = currentDate.getHours();\n      const minute = currentDate.getMinutes();\n      const second = currentDate.getSeconds();\n      const end = createDateAtLastMillisecond(new Date(year, month, day, hour, minute, second + step - 1), true);\n      if (end.getTime() > endDate.getTime()) {\n        end.setTime(endDate.getTime());\n      }\n      const start = currentDate;\n      const formattedDate = format?.({ dateIndex: second, startDate: start, endDate: end });\n      const columnTitle = formattedDate || second.toString();\n      const dayCellConfig = {\n        days: Math.abs(end.getTime() - currentDate.getTime() + 1) / DayTimes,\n        startDate: start,\n        endDate: end,\n        step,\n        unit: 'second',\n        title: columnTitle,\n        dateIndex: currentDate.getSeconds()\n      };\n      timelineDates.push(dayCellConfig);\n      currentDate = new Date(year, month, day, hour, minute, second + step);\n    }\n  }\n  return timelineDates;\n}\n\n/**\n * @description: 获取对应样式和容器尺寸下的文字位置\n * @return {*}\n */\nexport function getTextPos(\n  padding: number[],\n  textAlign: CanvasTextAlign,\n  textBaseline: CanvasTextBaseline,\n  width: number,\n  height: number\n) {\n  let textX = padding[3] ?? 10;\n  if (textAlign === 'right' || textAlign === 'end') {\n    textX = width - 0 - (padding[1] ?? 10);\n  } else if (textAlign === 'center') {\n    textX = 0 + (width - 0 + (padding[3] ?? 10) - (padding[1] ?? 10)) / 2;\n  }\n  let textY = 0 + (padding[0] ?? 10);\n  if (textBaseline === 'bottom' || textBaseline === 'alphabetic' || textBaseline === 'ideographic') {\n    textY = height - 0 - (padding[2] ?? 10);\n  } else if (textBaseline === 'middle') {\n    textY = 0 + (height - 0 - (padding[0] ?? 10) - (padding[2] ?? 10)) / 2 + (padding[0] ?? 10);\n  }\n\n  return {\n    x: textX,\n    y: textY\n  };\n}\n\nexport function convertProgress(progress: number | string) {\n  // 如果是字符串类型，去掉可能存在的百分号\n  if (typeof progress === 'string') {\n    progress = progress.replace('%', '');\n    // 转换成数字类型\n    progress = parseFloat(progress);\n  }\n\n  // 如果小于或等于1，说明是0.4这种情况，转换成百分比\n  if (progress <= 1) {\n    progress = progress * 100;\n  }\n\n  // 最后转换成整数\n  return Math.round(progress);\n}\n\nexport function createSplitLineAndResizeLine(gantt: Gantt) {\n  // 注释掉水平分割线 改成用左侧表格的body frameStyle和右侧的grid绘制的表头底部线做分割线\n  // if (gantt.parsedOptions.horizontalSplitLine) {\n  //   gantt.horizontalSplitLine = document.createElement('div');\n  //   gantt.horizontalSplitLine.style.position = 'absolute';\n  //   gantt.horizontalSplitLine.style.top =\n  //     gantt.getAllHeaderRowsHeight() + (gantt.parsedOptions.outerFrameStyle?.borderLineWidth ?? 0) + 'px';\n  //   gantt.horizontalSplitLine.style.left = gantt.tableY + 'px';\n  //   gantt.horizontalSplitLine.style.height = (gantt.parsedOptions.horizontalSplitLine.lineWidth ?? 2) + 'px';\n  //   gantt.horizontalSplitLine.style.width =\n  //     gantt.tableNoFrameWidth +\n  //     (gantt.taskListTableInstance?.tableNoFrameWidth ?? 0) +\n  //     +(gantt.taskListTableInstance ? gantt.parsedOptions.verticalSplitLine.lineWidth : 0) +\n  //     'px'; //'100%';\n  //   gantt.horizontalSplitLine.style.backgroundColor = gantt.parsedOptions.horizontalSplitLine.lineColor;\n  //   gantt.horizontalSplitLine.style.zIndex = '100';\n  //   gantt.horizontalSplitLine.style.userSelect = 'none';\n  //   gantt.horizontalSplitLine.style.opacity = '1';\n  //   (gantt.container as HTMLElement).appendChild(gantt.horizontalSplitLine);\n  // }\n  if (gantt.taskListTableInstance) {\n    gantt.verticalSplitResizeLine = document.createElement('div');\n    gantt.verticalSplitResizeLine.style.position = 'absolute';\n    gantt.verticalSplitResizeLine.style.top = gantt.tableY + 'px';\n    gantt.verticalSplitResizeLine.style.left =\n      (gantt.taskTableWidth ? gantt.taskTableWidth - 7 + gantt.parsedOptions.verticalSplitLine.lineWidth / 2 : 0) +\n      'px';\n    gantt.verticalSplitResizeLine.style.width = '14px'; // 注意下面的14 和7 的地方 都是因为这里的宽度是 14\n    gantt.verticalSplitResizeLine.style.height = gantt.drawHeight + 'px'; //'100%';\n    gantt.verticalSplitResizeLine.style.backgroundColor = 'rgba(0,0,0,0)';\n    gantt.verticalSplitResizeLine.style.zIndex = '100';\n    gantt.parsedOptions.verticalSplitLineMoveable && (gantt.verticalSplitResizeLine.style.cursor = 'col-resize');\n    gantt.verticalSplitResizeLine.style.userSelect = 'none';\n    gantt.verticalSplitResizeLine.style.opacity = '1';\n\n    const verticalSplitLine = document.createElement('div');\n    verticalSplitLine.style.position = 'absolute';\n    verticalSplitLine.style.top = '0px';\n    verticalSplitLine.style.left = `${(14 - gantt.parsedOptions.verticalSplitLine.lineWidth) / 2}px`;\n    verticalSplitLine.style.width = gantt.parsedOptions.verticalSplitLine.lineWidth + 'px';\n    verticalSplitLine.style.height = '100%';\n    verticalSplitLine.style.backgroundColor = gantt.parsedOptions.verticalSplitLine.lineColor;\n    verticalSplitLine.style.zIndex = '100';\n    verticalSplitLine.style.userSelect = 'none';\n    verticalSplitLine.style.pointerEvents = 'none';\n    // verticalSplitLine.style.opacity = '0';\n    verticalSplitLine.style.transition = 'background-color 0.3s';\n    gantt.verticalSplitResizeLine.appendChild(verticalSplitLine);\n\n    if (gantt.parsedOptions.verticalSplitLineHighlight) {\n      const highlightLine = document.createElement('div');\n      highlightLine.style.position = 'absolute';\n      highlightLine.style.top = '0px';\n      highlightLine.style.left = `${(14 - gantt.parsedOptions.verticalSplitLineHighlight.lineWidth ?? 2) / 2}px`;\n      highlightLine.style.width = (gantt.parsedOptions.verticalSplitLineHighlight.lineWidth ?? 2) + 'px';\n      highlightLine.style.height = '100%';\n      highlightLine.style.backgroundColor = gantt.parsedOptions.verticalSplitLineHighlight.lineColor;\n      highlightLine.style.zIndex = '100';\n      highlightLine.style.cursor = 'col-resize';\n      highlightLine.style.userSelect = 'none';\n      highlightLine.style.pointerEvents = 'none';\n      highlightLine.style.opacity = '0';\n      highlightLine.style.transition = 'background-color 0.3s';\n      gantt.verticalSplitResizeLine.appendChild(highlightLine);\n    }\n    (gantt.container as HTMLElement).appendChild(gantt.verticalSplitResizeLine);\n  }\n}\n\nexport function updateSplitLineAndResizeLine(gantt: Gantt) {\n  // if (gantt.horizontalSplitLine) {\n  //   gantt.horizontalSplitLine.style.position = 'absolute';\n  //   gantt.horizontalSplitLine.style.top = gantt.getAllHeaderRowsHeight() + 'px';\n  //   gantt.horizontalSplitLine.style.left = gantt.tableY + 'px';\n  //   gantt.horizontalSplitLine.style.height = (gantt.parsedOptions.horizontalSplitLine.lineWidth ?? 2) + 'px';\n  //   gantt.horizontalSplitLine.style.width =\n  //     gantt.tableNoFrameWidth +\n  //     (gantt.taskListTableInstance?.tableNoFrameWidth ?? 0) +\n  //     (gantt.taskListTableInstance ? gantt.parsedOptions.verticalSplitLine.lineWidth : 0) +\n  //     'px'; //'100%';\n  //   gantt.horizontalSplitLine.style.backgroundColor = gantt.parsedOptions.horizontalSplitLine.lineColor;\n  //   gantt.horizontalSplitLine.style.zIndex = '100';\n  //   gantt.horizontalSplitLine.style.userSelect = 'none';\n  //   gantt.horizontalSplitLine.style.opacity = '1';\n  // }\n  if (gantt.verticalSplitResizeLine) {\n    gantt.verticalSplitResizeLine.style.position = 'absolute';\n    gantt.verticalSplitResizeLine.style.top = gantt.tableY + 'px';\n    gantt.verticalSplitResizeLine.style.left = gantt.taskTableWidth\n      ? `${gantt.taskTableWidth - 7 + gantt.parsedOptions.verticalSplitLine.lineWidth / 2}px`\n      : '0px';\n    gantt.verticalSplitResizeLine.style.width = '14px';\n    gantt.verticalSplitResizeLine.style.height = gantt.drawHeight + 'px'; //'100%';\n    gantt.verticalSplitResizeLine.style.backgroundColor = 'rgba(0,0,0,0)';\n    gantt.verticalSplitResizeLine.style.zIndex = '100';\n    gantt.parsedOptions.verticalSplitLineMoveable && (gantt.verticalSplitResizeLine.style.cursor = 'col-resize');\n    gantt.verticalSplitResizeLine.style.userSelect = 'none';\n    gantt.verticalSplitResizeLine.style.opacity = '1';\n\n    const verticalSplitLine = gantt.verticalSplitResizeLine.childNodes[0] as HTMLDivElement;\n    verticalSplitLine.style.position = 'absolute';\n    verticalSplitLine.style.top = '0px';\n    verticalSplitLine.style.left = `${(14 - gantt.parsedOptions.verticalSplitLine.lineWidth) / 2}px`;\n    verticalSplitLine.style.width = gantt.parsedOptions.verticalSplitLine.lineWidth + 'px';\n    verticalSplitLine.style.height = '100%';\n    verticalSplitLine.style.backgroundColor = gantt.parsedOptions.verticalSplitLine.lineColor;\n    verticalSplitLine.style.zIndex = '100';\n    verticalSplitLine.style.userSelect = 'none';\n    verticalSplitLine.style.pointerEvents = 'none';\n    // verticalSplitLine.style.opacity = '0';\n    verticalSplitLine.style.transition = 'background-color 0.3s';\n\n    if (gantt.verticalSplitResizeLine.childNodes[1]) {\n      const highlightLine = gantt.verticalSplitResizeLine.childNodes[1] as HTMLDivElement;\n      highlightLine.style.position = 'absolute';\n      highlightLine.style.top = '0px';\n      highlightLine.style.left = `${(14 - gantt.parsedOptions.verticalSplitLineHighlight.lineWidth ?? 2) / 2}px`;\n      highlightLine.style.width = (gantt.parsedOptions.verticalSplitLineHighlight.lineWidth ?? 2) + 'px';\n      highlightLine.style.height = '100%';\n      highlightLine.style.backgroundColor = gantt.parsedOptions.verticalSplitLineHighlight.lineColor;\n      highlightLine.style.zIndex = '100';\n      highlightLine.style.cursor = 'col-resize';\n      highlightLine.style.userSelect = 'none';\n      highlightLine.style.pointerEvents = 'none';\n      highlightLine.style.opacity = '0';\n      highlightLine.style.transition = 'background-color 0.3s';\n    }\n  }\n}\n\nexport function findRecordByTaskKey(\n  records: any[],\n  taskKeyField: string,\n  taskKey: string | number | (string | number)[],\n  childrenField: string = 'children'\n): { record: any; index: number[] } | undefined {\n  for (let i = 0; i < records.length; i++) {\n    if (\n      (Array.isArray(taskKey) && taskKey.length === 1 && records[i][taskKeyField] === taskKey[0]) ||\n      records[i][taskKeyField] === taskKey\n    ) {\n      return { record: records[i], index: [i] };\n    } else if (records[i][childrenField]?.length) {\n      if (Array.isArray(taskKey) && taskKey[0] === records[i][taskKeyField]) {\n        const result: { record: any; index: number[] } | undefined = findRecordByTaskKey(\n          records[i][childrenField],\n          taskKeyField,\n          taskKey.slice(1)\n        );\n        if (result) {\n          result.index.unshift(i);\n          return result;\n        }\n      } else if (!Array.isArray(taskKey)) {\n        const result: { record: any; index: number[] } | undefined = findRecordByTaskKey(\n          records[i][childrenField],\n          taskKeyField,\n          taskKey\n        );\n        if (result) {\n          result.index.unshift(i);\n          return result;\n        }\n      }\n    }\n  }\n}\n\nexport function clearRecordLinkInfos(records: any[], childrenField: string = 'children') {\n  for (let i = 0; i < records.length; i++) {\n    if (records[i][childrenField]?.length) {\n      clearRecordLinkInfos(records[i][childrenField], childrenField);\n    } else {\n      delete records[i].vtable_gantt_linkedTo;\n      delete records[i].vtable_gantt_linkedFrom;\n    }\n  }\n}\n\nexport function clearRecordShowIndex(records: any[], childrenField: string = 'children') {\n  for (let i = 0; i < records.length; i++) {\n    if (records[i][childrenField]?.length) {\n      clearRecordShowIndex(records[i][childrenField], childrenField);\n    } else {\n      delete records[i].vtable_gantt_showIndex;\n    }\n  }\n}\nexport function getTaskIndexsByTaskY(y: number, gantt: Gantt) {\n  let task_index;\n  let sub_task_index;\n  if (gantt.taskListTableInstance) {\n    const rowInfo = gantt.taskListTableInstance.getTargetRowAt(y + gantt.headerHeight);\n    if (rowInfo) {\n      const { row } = rowInfo;\n      task_index = row - gantt.taskListTableInstance.columnHeaderLevelCount;\n      const beforeRowsHeight = gantt.getRowsHeightByIndex(0, task_index - 1); // 耦合了listTableOption的customComputeRowHeight\n      sub_task_index = Math.floor((y - beforeRowsHeight) / gantt.parsedOptions.rowHeight);\n    }\n  } else {\n    task_index = Math.floor(y / gantt.parsedOptions.rowHeight);\n  }\n  return { task_index, sub_task_index };\n}\n\nexport function computeRowsCountByRecordDateForCompact(gantt: Gantt, record: any) {\n  if (!record.children || record.children.length === 1) {\n    if (record.children?.length === 1) {\n      record.children[0].vtable_gantt_showIndex = 0;\n    }\n    return 1;\n  }\n  // 创建一个浅拷贝并排序子任务，根据开始日期排序\n  const sortedChildren = record.children.slice().sort((a: any, b: any) => {\n    const { startDate: aStartDate } = formatRecordDateConsiderHasHour(gantt, a);\n    const { startDate: bStartDate } = formatRecordDateConsiderHasHour(gantt, b);\n    return aStartDate.getTime() - bStartDate.getTime();\n  });\n  // 用于存储每一行的结束日期\n  const rows = [];\n  for (let i = 0; i <= sortedChildren.length - 1; i++) {\n    const newRecord = sortedChildren[i];\n    const { startDate, endDate } = formatRecordDateConsiderHasHour(gantt, newRecord);\n\n    let placed = false;\n\n    // 尝试将当前任务放入已有的行中\n    for (let j = 0; j < rows.length; j++) {\n      if (startDate.getTime() > rows[j]) {\n        // 如果当前任务的开始日期在该行的结束日期之后，则可以放在这一行\n        rows[j] = endDate.getTime();\n        placed = true;\n        newRecord.vtable_gantt_showIndex = j;\n        break;\n      }\n    }\n\n    // 如果不能放在已有的行中，则需要新开一行\n    if (!placed) {\n      rows.push(endDate.getTime());\n      newRecord.vtable_gantt_showIndex = rows.length - 1;\n    }\n  }\n\n  return rows.length;\n}\n// 检查两个日期范围是否重叠\nfunction isOverlapping(startDate: Date, endDate: Date, rowTasks: any[], gantt: Gantt) {\n  return rowTasks.some(rowTask => {\n    const { startDate: startDate2, endDate: endDate2 } = formatRecordDateConsiderHasHour(gantt, rowTask);\n    return startDate <= endDate2 && startDate2 <= endDate;\n  });\n}\nexport function computeRowsCountByRecordDate(gantt: Gantt, record: any) {\n  if (!record.children || record.children.length === 1) {\n    if (record.children?.length === 1) {\n      record.children[0].vtable_gantt_showIndex = 0;\n    }\n    return 1;\n  }\n\n  // 用于存储每一行的结束日期\n  const rows = [];\n  for (let i = 0; i <= record.children.length - 1; i++) {\n    const newRecord = record.children[i];\n    const { startDate, endDate } = formatRecordDateConsiderHasHour(gantt, newRecord);\n    let placed = false;\n    // 尝试将当前任务放入已有的行中\n    for (let j = 0; j < rows.length; j++) {\n      const rowTasks = record.children.filter((t: any) => t !== newRecord && t.vtable_gantt_showIndex === j);\n      if (!isOverlapping(startDate, endDate, rowTasks, gantt)) {\n        // 如果当前任务的开始日期在该行的结束日期之后，则可以放在这一行\n        rows[j] = endDate.getTime();\n        placed = true;\n        newRecord.vtable_gantt_showIndex = j;\n        break;\n      }\n    }\n\n    // 如果不能放在已有的行中，则需要新开一行\n    if (!placed) {\n      rows.push(endDate.getTime());\n      newRecord.vtable_gantt_showIndex = rows.length - 1;\n    }\n  }\n\n  return rows.length;\n}\nexport function getSubTaskRowIndexByRecordDate(\n  record: any,\n  childIndex: number,\n  startDateField: string,\n  endDateField: string\n) {\n  if (childIndex === 0) {\n    return 0;\n  }\n  // 排序在datasource中已经排过了\n  //  创建一个浅拷贝并排序子任务，根据开始日期排序\n  // const sortedChildren = record.children.slice().sort((a: any, b: any) => {\n  //   return createDateAtMidnight(a[startDateField]).getTime() - createDateAtMidnight(b[startDateField]).getTime();\n  // });\n\n  // 用于存储每一行的结束日期\n  const rows = [];\n  if (record?.children) {\n    for (let i = 0; i <= record.children.length - 1; i++) {\n      const newRecord = record.children[i];\n      const startDate = createDateAtMidnight(newRecord[startDateField]).getTime();\n      const endDate = createDateAtMidnight(newRecord[endDateField]).getTime();\n\n      let placed = false;\n\n      // 尝试将当前任务放入已有的行中\n      for (let j = 0; j < rows.length; j++) {\n        if (startDate > rows[j]) {\n          // 如果当前任务的开始日期在该行的结束日期之后，则可以放在这一行\n          rows[j] = endDate;\n          placed = true;\n          if (i === childIndex) {\n            return j;\n          }\n          break;\n        }\n      }\n      // 如果不能放在已有的行中，则需要新开一行\n      if (!placed) {\n        rows.push(endDate);\n      }\n      if (i === childIndex) {\n        return rows.length - 1;\n      }\n    }\n  }\n\n  return 0;\n}\n\n/**\n * 获取指定index处任务数据的具体信息\n * @param index\n * @returns 当前任务信息\n */\nexport function formatRecordDateConsiderHasHour(\n  gantt: Gantt,\n  record: any\n): {\n  startDate: Date;\n  endDate: Date;\n} {\n  const { timeScaleIncludeHour, startDateField, endDateField } = gantt.parsedOptions;\n  const startDate = record[startDateField];\n  const endDate = record[endDateField];\n  if (timeScaleIncludeHour) {\n    return { startDate: createDateAtMidnight(startDate), endDate: createDateAtLastHour(endDate) };\n  }\n  return { startDate: createDateAtMidnight(startDate, true), endDate: createDateAtLastHour(endDate, true) };\n}\n", "import { isValid } from '@visactor/vutils';\nimport { getTextPos } from '../gantt-helper';\nimport { computeCountToTimeScale, toBoxArray } from '../tools/util';\nimport type { Scenegraph } from './scenegraph';\nimport { Group, Text, createLine } from '@visactor/vtable/es/vrender';\nexport class TimelineHeader {\n  group: Group;\n  _scene: Scenegraph;\n  constructor(scene: Scenegraph) {\n    this._scene = scene;\n    this.initNodes();\n  }\n  initNodes() {\n    const { _scene: scene } = this;\n    const dateHeader = new Group({\n      x: 0,\n      y: 0,\n      width: scene._gantt.getAllDateColsWidth(), //width - 2,\n      height: scene._gantt.getAllHeaderRowsHeight(),\n      clip: true,\n      pickable: false\n      // fill: 'purple',\n      // stroke: 'green',\n      // lineWidth: 2\n    });\n    this.group = dateHeader;\n    dateHeader.name = 'date-header-container';\n    scene.tableGroup.addChild(this.group);\n    const { unit: minUnit, step } = scene._gantt.parsedOptions.reverseSortedTimelineScales[0];\n    let y = 0;\n    for (let i = 0; i < scene._gantt.timeLineHeaderLevel; i++) {\n      const rowHeader = new Group({\n        x: 0,\n        y,\n        width: scene._gantt.getAllDateColsWidth(),\n        height: scene._gantt.parsedOptions.timeLineHeaderRowHeights[i],\n        clip: false\n      });\n      y += rowHeader.attribute.height;\n      rowHeader.name = 'row-header';\n      dateHeader.addChild(rowHeader);\n\n      const { timelineDates, customLayout } = scene._gantt.parsedOptions.sortedTimelineScales[i];\n      for (let j = 0; j < timelineDates?.length; j++) {\n        const { days, endDate, startDate, title, dateIndex, unit } = timelineDates[j];\n        const x = Math.ceil(\n          computeCountToTimeScale(startDate, scene._gantt.parsedOptions.minDate, minUnit, step) *\n            scene._gantt.parsedOptions.timelineColWidth\n        );\n        const right_x = Math.ceil(\n          computeCountToTimeScale(endDate, scene._gantt.parsedOptions.minDate, minUnit, step, 1) *\n            scene._gantt.parsedOptions.timelineColWidth\n        );\n        console.log(\n          unit,\n          x,\n          right_x,\n          computeCountToTimeScale(startDate, scene._gantt.parsedOptions.minDate, minUnit, step),\n          computeCountToTimeScale(endDate, scene._gantt.parsedOptions.minDate, minUnit, step, 1)\n        );\n        const width = right_x - x;\n        const date = new Group({\n          x,\n          y: 0,\n          width,\n          height: rowHeader.attribute.height,\n          clip: false,\n          fill: scene._gantt.parsedOptions.timelineHeaderBackgroundColor\n        });\n        date.name = 'date-header-cell';\n        let rootContainer;\n        let renderDefaultText = true;\n\n        const height = rowHeader.attribute.height;\n        if (customLayout) {\n          let customLayoutObj;\n          if (typeof customLayout === 'function') {\n            const arg = {\n              width,\n              height,\n              index: j,\n              startDate,\n              endDate,\n              days,\n              dateIndex,\n              title,\n              ganttInstance: this._scene._gantt\n            };\n            customLayoutObj = customLayout(arg);\n          } else {\n            customLayoutObj = customLayout;\n          }\n          if (customLayoutObj) {\n            // if (customLayoutObj.rootContainer) {\n            //   customLayoutObj.rootContainer = decodeReactDom(customLayoutObj.rootContainer);\n            // }\n            rootContainer = customLayoutObj.rootContainer;\n            renderDefaultText = customLayoutObj.renderDefaultText ?? false;\n            rootContainer.name = 'task-bar-custom-render';\n          }\n          rootContainer && date.appendChild(rootContainer);\n        }\n        if (renderDefaultText) {\n          const {\n            padding,\n            textAlign,\n            textBaseline,\n            textOverflow,\n            fontSize,\n            fontWeight,\n            color,\n            strokeColor,\n            textStick\n          } = scene._gantt.parsedOptions.timelineHeaderStyles[i];\n\n          const position = getTextPos(toBoxArray(padding), textAlign, textBaseline, width, height);\n          const text = new Text({\n            x: position.x,\n            y: position.y,\n            maxLineWidth: width,\n            heightLimit: height,\n            // clip: true,\n            pickable: true,\n            text: title.toLocaleString(),\n            fontSize: fontSize,\n\n            fontWeight: fontWeight,\n            fill: color,\n            stroke: strokeColor,\n            lineWidth: 2,\n            textAlign,\n            textBaseline,\n            ellipsis:\n              textOverflow === 'clip'\n                ? ''\n                : textOverflow === 'ellipsis'\n                ? '...'\n                : isValid(textOverflow)\n                ? textOverflow\n                : undefined\n          });\n          (text.attribute as any).textStick = textStick;\n          date.appendChild(text);\n          text.name = 'date-header-cell-text';\n        }\n        rowHeader.addChild(date);\n\n        if (j > 0) {\n          const line = createLine({\n            pickable: false,\n            stroke: scene._gantt.parsedOptions.timelineHeaderVerticalLineStyle?.lineColor,\n            lineWidth: scene._gantt.parsedOptions.timelineHeaderVerticalLineStyle?.lineWidth,\n            points: [\n              { x: scene._gantt.parsedOptions.timelineHeaderVerticalLineStyle?.lineWidth & 1 ? 0.5 : 0, y: 0 },\n              {\n                x: scene._gantt.parsedOptions.timelineHeaderVerticalLineStyle?.lineWidth & 1 ? 0.5 : 0,\n                y: rowHeader.attribute.height\n              }\n            ]\n          });\n          date.appendChild(line);\n        }\n      }\n      //创建表头分割线 水平分割线 TODO\n      if (i > 0) {\n        const line = createLine({\n          pickable: false,\n          stroke: scene._gantt.parsedOptions.timelineHeaderHorizontalLineStyle?.lineColor,\n          lineWidth: scene._gantt.parsedOptions.timelineHeaderHorizontalLineStyle?.lineWidth,\n          points: [\n            { x: 0, y: scene._gantt.parsedOptions.timelineHeaderHorizontalLineStyle?.lineWidth & 1 ? 0.5 : 0 },\n            {\n              x: scene._gantt.getAllDateColsWidth(),\n              y: scene._gantt.parsedOptions.timelineHeaderHorizontalLineStyle?.lineWidth & 1 ? 0.5 : 0\n            }\n          ]\n        });\n        rowHeader.addChild(line);\n      }\n    }\n  }\n  setX(x: number) {\n    this.group.setAttribute('x', x);\n  }\n  setY(y: number) {\n    this.group.setAttribute('y', y);\n  }\n  resize() {\n    this.group.setAttribute('width', this.group.attribute?.width ?? 0);\n    this.group.setAttribute('height', this.group.attribute?.height ?? 0);\n  }\n\n  refresh() {\n    this.group?.parent.removeChild(this.group);\n    this.initNodes();\n  }\n}\n", "import type { IRect, IText, IGroupGraphicAttribute } from '@visactor/vtable/es/vrender';\nimport { Group } from '@visactor/vtable/es/vrender';\n\nexport class GanttTaskBarNode extends Group {\n  clipGroupBox: Group;\n  barRect?: IRect;\n  progressRect?: IRect;\n  textLabel?: IText;\n  name: string;\n  task_index: number;\n  sub_task_index?: number;\n  record?: any;\n  constructor(attrs: IGroupGraphicAttribute) {\n    super(attrs);\n  }\n}\n", "import { Group, createText, createRect, Image, Circle, Line, Rect } from '@visactor/vtable/es/vrender';\nimport type { Scenegraph } from './scenegraph';\n// import { Icon } from './icon';\nimport {\n  computeCountToTimeScale,\n  createDateAtLastHour,\n  createDateAtMidnight,\n  parseStringTemplate,\n  toBoxArray\n} from '../tools/util';\nimport { isValid } from '@visactor/vutils';\nimport {\n  computeRowsCountByRecordDate,\n  computeRowsCountByRecordDateForCompact,\n  getSubTaskRowIndexByRecordDate,\n  getTextPos\n} from '../gantt-helper';\nimport { GanttTaskBarNode } from './gantt-node';\nimport { TasksShowMode } from '../ts-types';\n\nconst TASKBAR_HOVER_ICON = `<svg width=\"100\" height=\"200\" xmlns=\"http://www.w3.org/2000/svg\">\n  <line x1=\"30\" y1=\"10\" x2=\"30\" y2=\"190\" stroke=\"black\" stroke-width=\"4\"/>\n  <line x1=\"70\" y1=\"10\" x2=\"70\" y2=\"190\" stroke=\"black\" stroke-width=\"4\"/>\n</svg>`;\nexport const TASKBAR_HOVER_ICON_WIDTH = 10;\n\nexport class TaskBar {\n  group: Group;\n  barContainer: Group;\n  hoverBarGroup: Group;\n  creatingDependencyLine: Line;\n  hoverBarLeftIcon: Image;\n  hoverBarRightIcon: Image;\n  _scene: Scenegraph;\n  width: number;\n  height: number;\n  selectedBorders: Group[] = [];\n  constructor(scene: Scenegraph) {\n    this._scene = scene;\n    // const height = Math.min(scene._gantt.tableNoFrameHeight, scene._gantt.drawHeight);\n    this.width = scene._gantt.tableNoFrameWidth;\n    this.height = scene._gantt.gridHeight;\n    this.group = new Group({\n      x: 0,\n      y: scene._gantt.getAllHeaderRowsHeight(),\n      width: this.width,\n      height: this.height,\n      pickable: false,\n      clip: true\n    });\n    this.group.name = 'task-bar-container';\n    scene.tableGroup.addChild(this.group);\n    this.initBars();\n    this.initHoverBarIcons();\n  }\n\n  initBars() {\n    this.barContainer = new Group({\n      x: 0,\n      y: 0,\n      width: this._scene._gantt.getAllDateColsWidth(),\n      height: this._scene._gantt.getAllTaskBarsHeight(),\n      pickable: false,\n      clip: true\n    });\n    this.group.appendChild(this.barContainer);\n\n    for (let i = 0; i < this._scene._gantt.itemCount; i++) {\n      if (\n        this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline ||\n        this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate ||\n        this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n        this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n      ) {\n        const record = this._scene._gantt.getRecordByIndex(i);\n        if (record.children?.length > 0) {\n          for (let j = 0; j < record.children.length; j++) {\n            const barGroup = this.initBar(i, j, record.children.length);\n            if (barGroup) {\n              this.barContainer.appendChild(barGroup);\n            }\n          }\n        }\n        continue;\n      } else {\n        const barGroup = this.initBar(i);\n        if (barGroup) {\n          this.barContainer.appendChild(barGroup);\n        }\n      }\n    }\n  }\n  initBar(index: number, childIndex?: number, childrenLength?: number) {\n    const taskBarCustomLayout = this._scene._gantt.parsedOptions.taskBarCustomLayout;\n    const { startDate, endDate, taskDays, progress, taskRecord } = this._scene._gantt.getTaskInfoByTaskListIndex(\n      index,\n      childIndex\n    );\n\n    if (taskDays <= 0 || !startDate || !endDate || startDate.getTime() > endDate.getTime()) {\n      return null;\n    }\n    const { unit, step } = this._scene._gantt.parsedOptions.reverseSortedTimelineScales[0];\n    const taskBarSize =\n      computeCountToTimeScale(endDate, startDate, unit, step, 1) * this._scene._gantt.parsedOptions.timelineColWidth;\n    const taskbarHeight = this._scene._gantt.parsedOptions.taskBarStyle.width;\n    const minDate = createDateAtMidnight(this._scene._gantt.parsedOptions.minDate);\n\n    const subTaskShowRowCount =\n      this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate\n        ? childrenLength\n        : this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange\n        ? computeRowsCountByRecordDate(this._scene._gantt, this._scene._gantt.records[index])\n        : this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n        ? computeRowsCountByRecordDateForCompact(this._scene._gantt, this._scene._gantt.records[index])\n        : 1;\n    const oneTaskHeigth = this._scene._gantt.getRowHeightByIndex(index) / subTaskShowRowCount;\n    const x =\n      computeCountToTimeScale(startDate, this._scene._gantt.parsedOptions.minDate, unit, step) *\n      this._scene._gantt.parsedOptions.timelineColWidth;\n    const barGroupBox = new GanttTaskBarNode({\n      x,\n      y:\n        this._scene._gantt.getRowsHeightByIndex(0, index - 1) +\n        (this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate\n          ? childIndex * oneTaskHeigth\n          : this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n            this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n          ? taskRecord.vtable_gantt_showIndex * oneTaskHeigth\n          : 0) +\n        (oneTaskHeigth - taskbarHeight) / 2,\n      width: taskBarSize,\n      // height: this._scene._gantt.parsedOptions.rowHeight,\n      height: taskbarHeight,\n      cornerRadius: this._scene._gantt.parsedOptions.taskBarStyle.cornerRadius,\n      lineWidth: this._scene._gantt.parsedOptions.taskBarStyle.borderWidth * 2,\n      stroke: this._scene._gantt.parsedOptions.taskBarStyle.borderColor\n      // clip: true\n    });\n    barGroupBox.name = 'task-bar';\n    barGroupBox.task_index = index;\n    barGroupBox.sub_task_index = childIndex;\n    barGroupBox.record = taskRecord;\n\n    const barGroup = new Group({\n      x: 0,\n      y: 0,\n      width: taskBarSize,\n      height: taskbarHeight,\n      cornerRadius: this._scene._gantt.parsedOptions.taskBarStyle.cornerRadius,\n      clip: true\n    });\n    barGroup.name = 'task-bar-group';\n    barGroupBox.appendChild(barGroup);\n    barGroupBox.clipGroupBox = barGroup;\n    let rootContainer;\n    let renderDefaultBar = true;\n    let renderDefaultText = true;\n\n    if (taskBarCustomLayout) {\n      let customLayoutObj;\n      if (typeof taskBarCustomLayout === 'function') {\n        const arg = {\n          width: taskBarSize,\n          height: taskbarHeight,\n          index,\n          startDate,\n          endDate,\n          taskDays,\n          progress,\n          taskRecord,\n          ganttInstance: this._scene._gantt\n        };\n        customLayoutObj = taskBarCustomLayout(arg);\n      } else {\n        customLayoutObj = taskBarCustomLayout;\n      }\n      if (customLayoutObj) {\n        // if (customLayoutObj.rootContainer) {\n        //   customLayoutObj.rootContainer = decodeReactDom(customLayoutObj.rootContainer);\n        // }\n        rootContainer = customLayoutObj.rootContainer;\n        renderDefaultBar = customLayoutObj.renderDefaultBar ?? false;\n        renderDefaultText = customLayoutObj.renderDefaultText ?? false;\n        rootContainer.name = 'task-bar-custom-render';\n      }\n    }\n\n    if (renderDefaultBar) {\n      // 创建整个任务条rect\n      const rect = createRect({\n        x: 0,\n        y: 0, //this._scene._gantt.parsedOptions.rowHeight - taskbarHeight) / 2,\n        width: taskBarSize,\n        height: taskbarHeight,\n        fill: this._scene._gantt.parsedOptions.taskBarStyle.barColor,\n        pickable: false\n      });\n      rect.name = 'task-bar-rect';\n      barGroup.appendChild(rect);\n      barGroupBox.barRect = rect;\n      // 创建已完成部分任务条rect\n      const progress_rect = createRect({\n        x: 0,\n        y: 0, //(this._scene._gantt.parsedOptions.rowHeight - taskbarHeight) / 2,\n        width: (taskBarSize * progress) / 100,\n        height: taskbarHeight,\n        fill: this._scene._gantt.parsedOptions.taskBarStyle.completedBarColor,\n        pickable: false\n      });\n      progress_rect.name = 'task-bar-progress-rect';\n      barGroup.appendChild(progress_rect);\n      barGroupBox.progressRect = progress_rect;\n    }\n\n    rootContainer && barGroup.appendChild(rootContainer);\n    if (renderDefaultText) {\n      const { textAlign, textBaseline, fontSize, fontFamily, textOverflow, color, padding } =\n        this._scene._gantt.parsedOptions.taskBarLabelStyle;\n      const position = getTextPos(toBoxArray(padding), textAlign, textBaseline, taskBarSize, taskbarHeight);\n      //创建label 文字\n      const label = createText({\n        // visible: false,\n        // pickable: false,\n        x: position.x, //extAlign === 'center' ? taskBarSize / 2 : textAlign === 'left' ? 10 : taskBarSize - 10,\n        y: position.y, //fontSize / 2,\n        fontSize: fontSize, // 10\n        fill: color,\n        fontFamily: fontFamily,\n        text: parseStringTemplate(this._scene._gantt.parsedOptions.taskBarLabelText as string, taskRecord),\n        maxLineWidth: taskBarSize - TASKBAR_HOVER_ICON_WIDTH,\n        textBaseline,\n        textAlign,\n        ellipsis:\n          textOverflow === 'clip'\n            ? ''\n            : textOverflow === 'ellipsis'\n            ? '...'\n            : isValid(textOverflow)\n            ? textOverflow\n            : undefined,\n        poptip: {\n          position: 'bottom',\n          dx: (taskBarSize - TASKBAR_HOVER_ICON_WIDTH) / 4\n        }\n        // dx: 12 + 4,\n        // dy: this._scene._gantt.barLabelStyle.fontSize / 2\n      });\n      barGroup.appendChild(label);\n      barGroupBox.textLabel = label;\n    }\n    return barGroupBox;\n  }\n  updateTaskBarNode(index: number) {\n    const taskbarGroup = this.getTaskBarNodeByIndex(index);\n    if (taskbarGroup) {\n      this.barContainer.removeChild(taskbarGroup);\n    }\n    const barGroup = this.initBar(index);\n    if (barGroup) {\n      this.barContainer.insertInto(barGroup, index); //TODO\n    }\n  }\n  initHoverBarIcons() {\n    const hoverBarGroup = new Group({\n      x: 0,\n      y: 0,\n      width: 100,\n      height: 100,\n      clip: true,\n      cursor: this._scene._gantt.parsedOptions.taskBarMoveable ? 'grab' : 'default',\n      pickable: false,\n      cornerRadius:\n        this._scene._gantt.parsedOptions.taskBarHoverStyle.cornerRadius ??\n        this._scene._gantt.parsedOptions.taskBarStyle.cornerRadius ??\n        0,\n      fill: this._scene._gantt.parsedOptions.taskBarHoverStyle.barOverlayColor,\n      visibleAll: false\n    });\n    this.hoverBarGroup = hoverBarGroup;\n    hoverBarGroup.name = 'task-bar-hover-shadow';\n    // this.barContainer.appendChild(hoverBarGroup);\n    // 创建左侧的icon\n    if (this._scene._gantt.parsedOptions.taskBarResizable) {\n      const icon = new Image({\n        x: 0,\n        y: 0, //this._scene._gantt.parsedOptions.rowHeight - taskbarHeight) / 2,\n        width: TASKBAR_HOVER_ICON_WIDTH,\n        height: 20,\n        image: TASKBAR_HOVER_ICON,\n        pickable: true,\n        cursor: 'col-resize'\n      });\n      icon.name = 'task-bar-hover-shadow-left-icon';\n      this.hoverBarLeftIcon = icon;\n      hoverBarGroup.appendChild(icon);\n\n      // 创建右侧的icon\n      const rightIcon = new Image({\n        x: 0,\n        y: 0, //this._scene._gantt.parsedOptions.rowHeight - taskbarHeight) / 2,\n        width: TASKBAR_HOVER_ICON_WIDTH,\n        height: 20,\n        image: TASKBAR_HOVER_ICON,\n        pickable: true,\n        cursor: 'col-resize'\n      });\n      rightIcon.name = 'task-bar-hover-shadow-right-icon';\n      this.hoverBarRightIcon = rightIcon;\n      hoverBarGroup.appendChild(rightIcon);\n    }\n  }\n\n  setX(x: number) {\n    this.barContainer.setAttribute('x', x);\n  }\n  setY(y: number) {\n    this.barContainer.setAttribute('y', y);\n  }\n  /** 重新创建任务条节点 */\n  refresh() {\n    this.width = this._scene._gantt.tableNoFrameWidth;\n    this.height = this._scene._gantt.gridHeight;\n    this.group.setAttributes({\n      height: this.height,\n      width: this.width,\n      y: this._scene._gantt.getAllHeaderRowsHeight()\n    });\n    this.barContainer.removeAllChild();\n    this.group.removeChild(this.barContainer);\n    this.initBars();\n  }\n  resize() {\n    this.width = this._scene._gantt.tableNoFrameWidth;\n    this.height = this._scene._gantt.gridHeight;\n    this.group.setAttribute('width', this.width);\n    this.group.setAttribute('height', this.height);\n  }\n\n  showHoverBar(x: number, y: number, width: number, height: number, target?: Group) {\n    if (target && target.name === 'task-bar') {\n      // this.hoverBarGroup.releatedTaskBar = target;\n      target.appendChild(this.hoverBarGroup);\n    }\n    this.hoverBarGroup.setAttribute('x', 0);\n    this.hoverBarGroup.setAttribute('y', 0);\n    this.hoverBarGroup.setAttribute('width', width);\n    this.hoverBarGroup.setAttribute('height', height);\n    this.hoverBarGroup.setAttribute('visibleAll', true);\n    if (this.hoverBarLeftIcon) {\n      this.hoverBarLeftIcon.setAttribute('x', 0);\n      this.hoverBarLeftIcon.setAttribute('y', Math.ceil(height / 10));\n      this.hoverBarLeftIcon.setAttribute('width', TASKBAR_HOVER_ICON_WIDTH);\n      this.hoverBarLeftIcon.setAttribute('height', height - 2 * Math.ceil(height / 10));\n      this.hoverBarRightIcon.setAttribute('x', width - TASKBAR_HOVER_ICON_WIDTH);\n      this.hoverBarRightIcon.setAttribute('y', Math.ceil(height / 10));\n      this.hoverBarRightIcon.setAttribute('width', TASKBAR_HOVER_ICON_WIDTH);\n      this.hoverBarRightIcon.setAttribute('height', height - 2 * Math.ceil(height / 10));\n    }\n  }\n  hideHoverBar() {\n    this.hoverBarGroup.setAttribute('visibleAll', false);\n  }\n\n  createSelectedBorder(\n    x: number,\n    y: number,\n    width: number,\n    height: number,\n    attachedToTaskBarNode: GanttTaskBarNode,\n    showLinkPoint: boolean = false\n  ) {\n    const selectedBorder = new Group({\n      x,\n      y,\n      width,\n      height,\n      lineWidth: this._scene._gantt.parsedOptions.taskBarSelectedStyle.borderLineWidth,\n      pickable: false,\n      cornerRadius: this._scene._gantt.parsedOptions.taskBarStyle.cornerRadius ?? 0,\n      fill: false,\n      stroke: this._scene._gantt.parsedOptions.taskBarSelectedStyle.borderColor,\n      shadowColor: this._scene._gantt.parsedOptions.taskBarSelectedStyle.shadowColor,\n      shadowOffsetX: this._scene._gantt.parsedOptions.taskBarSelectedStyle.shadowOffsetX,\n      shadowOffsetY: this._scene._gantt.parsedOptions.taskBarSelectedStyle.shadowOffsetY,\n      shadowBlur: this._scene._gantt.parsedOptions.taskBarSelectedStyle.shadowBlur,\n      attachedToTaskBarNode: attachedToTaskBarNode,\n      zIndex: 10000\n    });\n    selectedBorder.name = 'task-bar-select-border';\n    this.barContainer.appendChild(selectedBorder);\n    this.selectedBorders.push(selectedBorder);\n\n    if (showLinkPoint) {\n      const linkPointContainer = new Group({\n        x: -10,\n        y: 0,\n        width: 10,\n        height: height,\n        pickable: true\n      });\n      const linkPoint = new Circle({\n        x: 5,\n        y: height / 2,\n        radius: this._scene._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.radius,\n        fill: this._scene._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.fillColor,\n        stroke: this._scene._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.strokeColor,\n        lineWidth: this._scene._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.strokeWidth,\n        pickable: false\n      });\n      linkPointContainer.appendChild(linkPoint);\n      linkPointContainer.name = 'task-bar-link-point-left';\n      selectedBorder.appendChild(linkPointContainer);\n\n      const linkPointContainer1 = new Group({\n        x: width,\n        y: 0,\n        width: 10,\n        height: height,\n        pickable: true\n      });\n      const linkPoint1 = new Circle({\n        x: 5,\n        y: height / 2,\n        radius: this._scene._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.radius,\n        fill: this._scene._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.fillColor,\n        stroke: this._scene._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.strokeColor,\n        lineWidth: this._scene._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.strokeWidth,\n        pickable: false,\n        pickStrokeBuffer: 10\n      });\n      linkPointContainer1.appendChild(linkPoint1);\n      linkPointContainer1.name = 'task-bar-link-point-right';\n      selectedBorder.appendChild(linkPointContainer1);\n    }\n  }\n  removeSelectedBorder() {\n    this.selectedBorders.forEach(border => {\n      border.delete();\n    });\n    this.selectedBorders = [];\n  }\n  removeSecondSelectedBorder() {\n    if (this.selectedBorders.length === 2) {\n      const secondBorder = this.selectedBorders.pop();\n      secondBorder.delete();\n    }\n  }\n  updateCreatingDependencyLine(x1: number, y1: number, x2: number, y2: number) {\n    if (this.creatingDependencyLine) {\n      this.creatingDependencyLine.delete();\n      this.creatingDependencyLine = undefined;\n    }\n    const line = new Line({\n      points: [\n        { x: x1, y: y1 },\n        { x: x2, y: y2 }\n      ],\n      stroke: this._scene._gantt.parsedOptions.dependencyLinkLineCreatingStyle.lineColor,\n      lineWidth: this._scene._gantt.parsedOptions.dependencyLinkLineCreatingStyle.lineWidth,\n      lineDash: this._scene._gantt.parsedOptions.dependencyLinkLineCreatingStyle.lineDash,\n      pickable: false\n    });\n    this.creatingDependencyLine = line;\n    this.selectedBorders[0].appendChild(line);\n  }\n\n  getTaskBarNodeByIndex(index: number, sub_task_index?: number) {\n    let c = this.barContainer.firstChild as Group;\n    if (!c) {\n      return null;\n    }\n    for (let i = 0; i < this.barContainer.childrenCount; i++) {\n      if (\n        c.task_index === index &&\n        (!isValid(sub_task_index) || (isValid(sub_task_index) && c.sub_task_index === sub_task_index))\n      ) {\n        return c;\n      }\n      c = c._next as Group;\n    }\n    return null;\n  }\n}\n", "import { computeCountToTimeScale, createDateAtMidnight } from '../tools/util';\nimport type { IMarkLine } from '../ts-types';\nimport type { Scenegraph } from './scenegraph';\nimport { Group, createLine } from '@visactor/vtable/es/vrender';\n\nexport class MarkLine {\n  _scene: Scenegraph;\n  group: Group;\n  markLine: IMarkLine[];\n  markLIneContainer: Group;\n  markLineContainerWidth: number = 20;\n  height: number;\n  constructor(scene: Scenegraph) {\n    this._scene = scene;\n    this.markLine = scene._gantt.parsedOptions.markLine;\n    this.height =\n      Math.min(scene._gantt.tableNoFrameHeight, scene._gantt.drawHeight) - scene._gantt.getAllHeaderRowsHeight();\n    this.group = new Group({\n      x: 0,\n      y: scene._gantt.getAllHeaderRowsHeight(),\n      width: scene._gantt.tableNoFrameWidth,\n      height: this.height,\n      pickable: false,\n      clip: true\n    });\n    this.group.name = 'mark-line-container';\n    scene.tableGroup.addChild(this.group);\n\n    this.markLIneContainer = new Group({\n      x: 0,\n      y: 0,\n      width: this._scene._gantt.getAllDateColsWidth(),\n      height: this.height,\n      pickable: false,\n      clip: true\n    });\n    this.group.appendChild(this.markLIneContainer);\n    this.initMarkLines();\n  }\n  initMarkLines() {\n    this.markLine.forEach(line => {\n      const style = line.style;\n      const date = this._scene._gantt.parsedOptions.timeScaleIncludeHour\n        ? createDateAtMidnight(line.date)\n        : createDateAtMidnight(line.date, true);\n      const minDate = this._scene._gantt.parsedOptions.minDate;\n      const { unit, step } = this._scene._gantt.parsedOptions.reverseSortedTimelineScales[0];\n      const unitCount = computeCountToTimeScale(date, minDate, unit, step);\n      const dateX =\n        this._scene._gantt.parsedOptions.timelineColWidth *\n        (Math.floor(unitCount) + (line.position === 'right' ? 1 : line.position === 'middle' ? 0.5 : 0));\n      const markLineGroup = new Group({\n        pickable: false,\n        x: dateX - this.markLineContainerWidth / 2,\n        y: 0,\n        width: this.markLineContainerWidth,\n        height: this.height\n      });\n      markLineGroup.name = 'mark-line';\n      this.markLIneContainer.appendChild(markLineGroup);\n      // 创建整个任务条rect\n      const lineObj = createLine({\n        pickable: false,\n        stroke: style.lineColor,\n        lineWidth: style.lineWidth,\n        lineDash: style.lineDash,\n        points: [\n          { x: this.markLineContainerWidth / 2, y: 0 },\n          { x: this.markLineContainerWidth / 2, y: this.height }\n        ]\n      });\n      markLineGroup.appendChild(lineObj);\n    });\n  }\n\n  /** 重新场景场景树节点 */\n  refresh() {\n    this.height =\n      Math.min(this._scene._gantt.tableNoFrameHeight, this._scene._gantt.drawHeight) -\n      this._scene._gantt.getAllHeaderRowsHeight();\n    this.markLIneContainer.removeAllChild();\n    this.group.setAttribute('height', this.height);\n    this.markLIneContainer.setAttribute('height', this.height);\n    this.initMarkLines();\n  }\n  setX(x: number) {\n    this.markLIneContainer.setAttribute('x', x);\n  }\n}\n", "import { createRect } from '@visactor/vtable/es/vrender';\nimport type { IRect, IGroupGraphicAttribute, IRectGraphicAttribute } from '@visactor/vtable/es/vrender';\n\nimport type { Scenegraph } from './scenegraph';\n\nexport class FrameBorder {\n  _scene: Scenegraph;\n  border: IRect;\n  constructor(scene: Scenegraph) {\n    this._scene = scene;\n    this.createFrameBorder();\n  }\n  createFrameBorder() {\n    const justForXYPosition = false;\n    const group = this._scene.tableGroup;\n    const frameStyle = this._scene._gantt.parsedOptions.outerFrameStyle;\n    // const strokeArray = [true, true, true, false];\n    if (!frameStyle) {\n      return;\n    }\n    const { cornerRadius, borderColor, borderLineWidth, borderLineDash } = frameStyle;\n\n    // const hasShadow = false;\n    const groupAttributes: IGroupGraphicAttribute = {};\n    const rectAttributes: IRectGraphicAttribute = {\n      pickable: false\n    };\n\n    // 处理边框\n    if (borderLineWidth) {\n      rectAttributes.stroke = true;\n      rectAttributes.fill = false;\n      rectAttributes.stroke = borderColor; // getStroke(borderColor, strokeArray);\n      rectAttributes.lineWidth = borderLineWidth as number;\n      borderLineDash && (rectAttributes.lineDash = borderLineDash as number[]);\n      rectAttributes.lineCap = 'butt';\n    }\n\n    if (cornerRadius) {\n      if (this._scene._gantt.taskListTableInstance) {\n        rectAttributes.cornerRadius = [\n          0,\n          this._scene._gantt.parsedOptions.outerFrameStyle.cornerRadius ?? 0,\n          this._scene._gantt.parsedOptions.outerFrameStyle.cornerRadius ?? 0,\n          0\n        ];\n        groupAttributes.cornerRadius = [\n          0,\n          this._scene._gantt.parsedOptions.outerFrameStyle.cornerRadius ?? 0,\n          this._scene._gantt.parsedOptions.outerFrameStyle.cornerRadius ?? 0,\n          0\n        ];\n      } else {\n        rectAttributes.cornerRadius = this._scene._gantt.parsedOptions.outerFrameStyle.cornerRadius ?? 0;\n        groupAttributes.cornerRadius = this._scene._gantt.parsedOptions.outerFrameStyle.cornerRadius ?? 0;\n      }\n    }\n\n    group.setAttributes(groupAttributes);\n\n    if (justForXYPosition) {\n      return;\n    }\n\n    if (rectAttributes.stroke) {\n      rectAttributes.x = this._scene._gantt.taskListTableInstance ? -borderLineWidth / 2 : borderLineWidth / 2; //为了可以绘制完整矩形 且左侧的边框不出现在group中\n      rectAttributes.y = borderLineWidth / 2;\n      rectAttributes.pickable = false;\n\n      rectAttributes.width =\n        group.attribute.width +\n        borderLineWidth +\n        (this._scene._gantt.taskListTableInstance ? this._scene._gantt.parsedOptions.verticalSplitLine.lineWidth : 0);\n      rectAttributes.height = group.attribute.height + borderLineWidth / 2 + borderLineWidth / 2;\n      const borderRect = createRect(rectAttributes);\n      borderRect.name = 'border-rect';\n      group.parent.insertAfter(borderRect, group);\n      (group as any).border = borderRect;\n      this.border = borderRect;\n    }\n  }\n  resize() {\n    const { cornerRadius, borderColor, borderLineWidth, borderLineDash } =\n      this._scene._gantt.parsedOptions.outerFrameStyle;\n    this.border?.setAttributes({\n      // x: -borderLineWidth / 2,\n      // y: borderLineWidth / 2,\n      width:\n        this._scene.tableGroup.attribute.width +\n        borderLineWidth +\n        (this._scene._gantt.taskListTableInstance ? this._scene._gantt.parsedOptions.verticalSplitLine.lineWidth : 0),\n      // this._scene.tableGroup.attribute.width +\n      // this.border.attribute.lineWidth +\n      // this._scene._gantt.parsedOptions.verticalSplitLine.lineWidth,\n      height: this._scene._gantt.drawHeight + borderLineWidth\n      // height: this._scene.tableGroup.attribute.height + this.border.attribute.lineWidth\n    });\n  }\n}\n", "import type {\n  IContext2d,\n  IDrawContext,\n  IMarkAttribute,\n  IGraphicAttribute,\n  IThemeAttribute,\n  IGroupGraphicAttribute,\n  IGroup,\n  IGroupRenderContribution\n} from '@visactor/vtable/es/vrender';\nimport { injectable, BaseRenderContributionTime } from '@visactor/vtable/es/vrender';\n\n// const highlightDash: number[] = [];\n\n// SplitGroupContribution处理分段渲染stroke\n// stroke/strokeArrayWidth/strokeArrayColor 为数组时调用\n@injectable()\nexport class DateHeaderGroupBeforeRenderContribution implements IGroupRenderContribution {\n  time: BaseRenderContributionTime = BaseRenderContributionTime.beforeFillStroke;\n  useStyle = true;\n  order = 0;\n  supportedAppName: string = 'vtable';\n  drawShape(\n    group: IGroup,\n    context: IContext2d,\n    x: number,\n    y: number,\n    doFill: boolean,\n    doStroke: boolean,\n    fVisible: boolean,\n    sVisible: boolean,\n    groupAttribute: Required<IGroupGraphicAttribute>,\n    drawContext: IDrawContext,\n    fillCb?: (\n      ctx: IContext2d,\n      markAttribute: Partial<IMarkAttribute & IGraphicAttribute>,\n      themeAttribute: IThemeAttribute\n    ) => boolean,\n    strokeCb?: (\n      ctx: IContext2d,\n      markAttribute: Partial<IMarkAttribute & IGraphicAttribute>,\n      themeAttribute: IThemeAttribute\n    ) => boolean,\n    doFillOrStroke?: { doFill: boolean; doStroke: boolean }\n  ) {\n    if (group.name === 'date-header-cell') {\n      group.forEachChildren((child: any) => {\n        if (child.name === 'date-header-cell-text' && child.attribute.textStick === true) {\n          const text = child;\n          text.setAttribute('dx', 0);\n          const textBounds = text.globalAABBBounds;\n          const stageBound = text.stage.globalAABBBounds;\n          const groupParent = text.parent.globalAABBBounds;\n          const intersectBounds = stageBound.intersect(groupParent);\n          if (stageBound.intersect(groupParent)) {\n            // if (!stageBound.contains(textBounds)) {\n            if (textBounds.width() >= intersectBounds.width() && text.attribute.last_dx) {\n              text.setAttribute('dx', text.attribute.last_dx ?? 0);\n            } else {\n              if (stageBound.x1 >= textBounds.x1) {\n                text.setAttribute('dx', stageBound.x1 - textBounds.x1);\n                text.setAttribute('last_dx', stageBound.x1 - textBounds.x1);\n              } else if (stageBound.x2 <= textBounds.x2) {\n                text.setAttribute('dx', stageBound.x2 - textBounds.x2);\n                text.setAttribute('last_dx', stageBound.x1 - textBounds.x1);\n              }\n            }\n            // }\n          }\n        }\n      });\n    }\n  }\n}\n", "import { ContainerModule, GroupRenderContribution } from '@visactor/vtable/es/vrender';\n\nimport { DateHeaderGroupBeforeRenderContribution } from './group-contribution-render';\n\nexport default new ContainerModule((bind, unbind, isBound, rebind) => {\n  // text 渲染器注入contributions\n  // bind(TextStickBeforeRenderContribution).toSelf().inSingletonScope();\n  // bind(TextRenderContribution).toService(TextStickBeforeRenderContribution);\n\n  // group 渲染器注入contributions\n  bind(DateHeaderGroupBeforeRenderContribution).toSelf().inSingletonScope();\n  bind(GroupRenderContribution).toService(DateHeaderGroupBeforeRenderContribution);\n});\n", "import { createLine, Group } from '@visactor/vtable/es/vrender';\nimport type { IRect, IGroupGraphicAttribute, IRectGraphicAttribute, ILine } from '@visactor/vtable/es/vrender';\n\nimport type { Scenegraph } from './scenegraph';\n\nexport class TaskCreationButton {\n  _scene: Scenegraph;\n  group: Group;\n  lineVertical: ILine;\n  lineHorizontal: ILine;\n  constructor(scene: Scenegraph) {\n    this._scene = scene;\n    this.createAddButton();\n  }\n  createAddButton() {\n    if (this._scene._gantt.parsedOptions.taskBarCreationCustomLayout) {\n      this.group = new Group({\n        x: 0,\n        y: 0,\n        width: 100,\n        height: 100\n      });\n    } else {\n      this.group = new Group({\n        x: 0,\n        y: 0,\n        width: 100,\n        height: 100,\n        lineDash: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.lineDash,\n        cursor: 'pointer',\n        lineWidth: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.lineWidth,\n        stroke: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.lineColor,\n        cornerRadius: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.cornerRadius ?? 0,\n        fill: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.backgroundColor\n      });\n\n      this.lineVertical = createLine({\n        pickable: false,\n        stroke: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.lineColor,\n        lineWidth: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.lineWidth,\n        points: [\n          { x: 50, y: 0 },\n          { x: 50, y: 100 }\n        ]\n      });\n      this.group.appendChild(this.lineVertical);\n      this.lineHorizontal = createLine({\n        pickable: false,\n        stroke: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.lineColor,\n        lineWidth: this._scene._gantt.parsedOptions.taskBarCreationButtonStyle.lineWidth,\n        points: [\n          { x: 0, y: 50 },\n          { x: 100, y: 50 }\n        ]\n      });\n      this.group.appendChild(this.lineHorizontal);\n    }\n    this.group.name = 'task-creation-button';\n    this._scene.taskBar.group.addChild(this.group);\n  }\n  show(x: number, y: number, width: number, height: number) {\n    if (this._scene._gantt.parsedOptions.taskBarCreationCustomLayout) {\n      this.group.appendChild(\n        this._scene._gantt.parsedOptions.taskBarCreationCustomLayout({\n          width,\n          height,\n          ganttInstance: this._scene._gantt\n        }).rootContainer\n      );\n    } else {\n      const lineSize = Math.min(width, height) / 6;\n      this.lineHorizontal.setAttribute('points', [\n        { x: (width - lineSize * 4) / 2, y: height / 2 },\n        { x: (width - lineSize * 4) / 2 + lineSize * 4, y: height / 2 }\n      ]);\n\n      this.lineVertical.setAttribute('points', [\n        { x: width / 2, y: (height - lineSize * 4) / 2 },\n        { x: width / 2, y: (height - lineSize * 4) / 2 + lineSize * 4 }\n      ]);\n    }\n    this.group.setAttribute('x', x);\n    this.group.setAttribute('y', y);\n    this.group.setAttribute('width', width);\n    this.group.setAttribute('height', height);\n    this.group.setAttribute('visibleAll', true);\n  }\n  hide() {\n    this.group.setAttribute('visibleAll', false);\n    if (this._scene._gantt.parsedOptions.taskBarCreationCustomLayout) {\n      this.group.removeAllChild();\n    }\n  }\n}\n", "import { Group, createLine, Polygon } from \"@visactor/vtable/es/vrender\";\n\nimport { computeCountToTimeScale, createDateAtMidnight } from \"../tools/util\";\n\nimport { clearRecordLinkInfos, findRecordByTaskKey } from \"../gantt-helper\";\n\nimport { DependencyType, TasksShowMode } from \"../ts-types\";\n\nexport class DependencyLink {\n    constructor(scene) {\n        this._scene = scene, this.width = scene._gantt.tableNoFrameWidth, this.height = scene._gantt.gridHeight, \n        this.group = new Group({\n            x: 0,\n            y: scene._gantt.getAllHeaderRowsHeight(),\n            width: this.width,\n            height: this.height,\n            pickable: !1,\n            clip: !0\n        }), this.group.name = \"dependency-link-container\", scene.tableGroup.addChild(this.group), \n        this.initLinkLines();\n    }\n    initLinkLines() {\n        var _a, _b, _c;\n        if (clearRecordLinkInfos(this._scene._gantt.records), this.linkLinesContainer = new Group({\n            x: 0,\n            y: 0,\n            width: this._scene._gantt.getAllDateColsWidth(),\n            height: this._scene._gantt.getAllTaskBarsHeight(),\n            pickable: !1,\n            clip: !0\n        }), this.group.appendChild(this.linkLinesContainer), null === (_a = this._scene._gantt.records) || void 0 === _a ? void 0 : _a.length) for (let i = 0; null !== (_c = i < (null === (_b = this._scene._gantt.parsedOptions.dependencyLinks) || void 0 === _b ? void 0 : _b.length)) && void 0 !== _c && _c; i++) this.initLinkLine(i);\n    }\n    initLinkLine(index) {\n        const {taskKeyField: taskKeyField, dependencyLinks: dependencyLinks} = this._scene._gantt.parsedOptions, link = dependencyLinks[index], {linkedToTaskKey: linkedToTaskKey, linkedFromTaskKey: linkedFromTaskKey, type: type} = link, linkedToTaskRecord = findRecordByTaskKey(this._scene._gantt.records, taskKeyField, linkedToTaskKey), linkedFromTaskRecord = findRecordByTaskKey(this._scene._gantt.records, taskKeyField, linkedFromTaskKey);\n        if (!linkedToTaskRecord || !linkedFromTaskRecord) return;\n        let linkedToTaskStartDate, linkedToTaskEndDate, linkedToTaskTaskDays, linkedFromTaskStartDate, linkedFromTaskEndDate, linkedFromTaskTaskDays, linkedToTaskShowIndex, linkedFromTaskShowIndex;\n        if (linkedToTaskRecord.record.vtable_gantt_linkedTo || (linkedToTaskRecord.record.vtable_gantt_linkedTo = []), \n        linkedToTaskRecord.record.vtable_gantt_linkedTo.push(link), linkedFromTaskRecord.record.vtable_gantt_linkedFrom || (linkedFromTaskRecord.record.vtable_gantt_linkedFrom = []), \n        linkedFromTaskRecord.record.vtable_gantt_linkedFrom.push(link), this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline) linkedFromTaskShowIndex = linkedFromTaskRecord.index[0], \n        linkedToTaskShowIndex = linkedToTaskRecord.index[0], ({startDate: linkedToTaskStartDate, endDate: linkedToTaskEndDate, taskDays: linkedToTaskTaskDays} = this._scene._gantt.getTaskInfoByTaskListIndex(linkedToTaskRecord.index[0], linkedToTaskRecord.index[1])), \n        ({startDate: linkedFromTaskStartDate, endDate: linkedFromTaskEndDate, taskDays: linkedFromTaskTaskDays} = this._scene._gantt.getTaskInfoByTaskListIndex(linkedFromTaskRecord.index[0], linkedFromTaskRecord.index[1])); else if (this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate || this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange || this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact) {\n            linkedFromTaskShowIndex = this._scene._gantt.getRowsHeightByIndex(0, linkedFromTaskRecord.index[0] - 1) / this._scene._gantt.parsedOptions.rowHeight + (this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange || this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact ? linkedFromTaskRecord.record.vtable_gantt_showIndex : linkedFromTaskRecord.index[1]);\n            linkedToTaskShowIndex = this._scene._gantt.getRowsHeightByIndex(0, linkedToTaskRecord.index[0] - 1) / this._scene._gantt.parsedOptions.rowHeight + (this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange || this._scene._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact ? linkedToTaskRecord.record.vtable_gantt_showIndex : linkedToTaskRecord.index[1]), \n            ({startDate: linkedToTaskStartDate, endDate: linkedToTaskEndDate, taskDays: linkedToTaskTaskDays} = this._scene._gantt.getTaskInfoByTaskListIndex(linkedToTaskRecord.index[0], linkedToTaskRecord.index[1])), \n            ({startDate: linkedFromTaskStartDate, endDate: linkedFromTaskEndDate, taskDays: linkedFromTaskTaskDays} = this._scene._gantt.getTaskInfoByTaskListIndex(linkedFromTaskRecord.index[0], linkedFromTaskRecord.index[1]));\n        } else linkedFromTaskShowIndex = this._scene._gantt.getTaskShowIndexByRecordIndex(linkedFromTaskRecord.index), \n        linkedToTaskShowIndex = this._scene._gantt.getTaskShowIndexByRecordIndex(linkedToTaskRecord.index), \n        ({startDate: linkedToTaskStartDate, endDate: linkedToTaskEndDate, taskDays: linkedToTaskTaskDays} = this._scene._gantt.getTaskInfoByTaskListIndex(linkedToTaskShowIndex)), \n        ({startDate: linkedFromTaskStartDate, endDate: linkedFromTaskEndDate, taskDays: linkedFromTaskTaskDays} = this._scene._gantt.getTaskInfoByTaskListIndex(linkedFromTaskShowIndex));\n        if (!linkedFromTaskTaskDays || !linkedToTaskTaskDays) return;\n        createDateAtMidnight(this._scene._gantt.parsedOptions.minDate);\n        const {arrowPoints: arrowPoints, linePoints: linePoints} = generateLinkLinePoints(type, linkedFromTaskStartDate, linkedFromTaskEndDate, linkedFromTaskShowIndex, linkedFromTaskTaskDays, linkedToTaskStartDate, linkedToTaskEndDate, linkedToTaskShowIndex, linkedToTaskTaskDays, this._scene._gantt), lineStyle = this._scene._gantt.parsedOptions.dependencyLinkLineStyle, lineObj = createLine({\n            pickable: !0,\n            stroke: lineStyle.lineColor,\n            lineWidth: lineStyle.lineWidth,\n            lineDash: lineStyle.lineDash,\n            points: linePoints,\n            pickStrokeBuffer: 3,\n            vtable_link: link\n        });\n        this.linkLinesContainer.appendChild(lineObj), link.vtable_gantt_linkLineNode = lineObj;\n        const arrow = new Polygon({\n            fill: lineStyle.lineColor,\n            points: arrowPoints\n        });\n        this.linkLinesContainer.appendChild(arrow), link.vtable_gantt_linkArrowNode = arrow;\n    }\n    setX(x) {\n        this.linkLinesContainer.setAttribute(\"x\", x);\n    }\n    setY(y) {\n        this.linkLinesContainer.setAttribute(\"y\", y);\n    }\n    refresh() {\n        this.width = this._scene._gantt.tableNoFrameWidth, this.height = this._scene._gantt.gridHeight, \n        this.group.setAttributes({\n            height: this.height,\n            width: this.width,\n            y: this._scene._gantt.getAllHeaderRowsHeight()\n        }), this.linkLinesContainer.removeAllChild(), this.group.removeChild(this.linkLinesContainer), \n        this.initLinkLines();\n    }\n    resize() {\n        this.width = this._scene._gantt.tableNoFrameWidth, this.height = this._scene._gantt.gridHeight, \n        this.group.setAttribute(\"width\", this.width), this.group.setAttribute(\"height\", this.height);\n    }\n    createSelectedLinkLine(selectedLink) {\n        const lineNode = selectedLink.vtable_gantt_linkLineNode, arrowNode = selectedLink.vtable_gantt_linkArrowNode, selectedLineNodelineNode = lineNode.clone(), selectedLinkArrowNode = arrowNode.clone();\n        selectedLineNodelineNode.setAttribute(\"stroke\", this._scene._gantt.parsedOptions.dependencyLinkSelectedLineStyle.lineColor), \n        selectedLineNodelineNode.setAttribute(\"lineWidth\", this._scene._gantt.parsedOptions.dependencyLinkSelectedLineStyle.lineWidth), \n        selectedLineNodelineNode.setAttribute(\"shadowColor\", this._scene._gantt.parsedOptions.dependencyLinkSelectedLineStyle.shadowColor), \n        selectedLineNodelineNode.setAttribute(\"shadowOffsetX\", this._scene._gantt.parsedOptions.dependencyLinkSelectedLineStyle.shadowOffset), \n        selectedLineNodelineNode.setAttribute(\"shadowOffsetY\", this._scene._gantt.parsedOptions.dependencyLinkSelectedLineStyle.shadowOffset), \n        selectedLineNodelineNode.setAttribute(\"shadowBlur\", this._scene._gantt.parsedOptions.dependencyLinkSelectedLineStyle.shadowBlur), \n        this.linkLinesContainer.appendChild(selectedLineNodelineNode), selectedLinkArrowNode.setAttribute(\"fill\", this._scene._gantt.parsedOptions.dependencyLinkSelectedLineStyle.lineColor), \n        this.linkLinesContainer.appendChild(selectedLinkArrowNode), this.selectedEffectNodes = [ selectedLineNodelineNode, selectedLinkArrowNode ];\n    }\n    removeSelectedLinkLine() {\n        var _a;\n        null === (_a = this.selectedEffectNodes) || void 0 === _a || _a.forEach((node => {\n            node.delete();\n        })), this.selectedEffectNodes = [];\n    }\n    deleteLink(link) {\n        const linkLineNode = link.vtable_gantt_linkLineNode, lineArrowNode = link.vtable_gantt_linkArrowNode;\n        linkLineNode.delete(), lineArrowNode.delete();\n    }\n}\n\nexport function generateLinkLinePoints(type, linkedFromTaskStartDate, linkedFromTaskEndDate, linkedFromTaskRecordRowIndex, linkedFromTaskTaskDays, linkedToTaskStartDate, linkedToTaskEndDate, linkedToTaskRecordRowIndex, linkedToTaskTaskDays, gantt) {\n    const {unit: unit, step: step} = gantt.parsedOptions.reverseSortedTimelineScales[0], {minDate: minDate, rowHeight: rowHeight, timelineColWidth: timelineColWidth} = gantt.parsedOptions;\n    let startDate, endDate, linePoints = [], arrowPoints = [];\n    if (type === DependencyType.FinishToStart) {\n        startDate = linkedFromTaskStartDate, endDate = linkedToTaskStartDate;\n        const linkFromPointX = computeCountToTimeScale(linkedFromTaskEndDate, minDate, unit, step, 1) * timelineColWidth, linkToPointX = computeCountToTimeScale(linkedToTaskStartDate, minDate, unit, step) * timelineColWidth;\n        linePoints = [ {\n            x: linkFromPointX,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5)\n        }, {\n            x: linkFromPointX + 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5)\n        }, {\n            x: linkFromPointX + 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + (linkedFromTaskRecordRowIndex > linkedToTaskRecordRowIndex ? 0 : 1))\n        }, {\n            x: linkToPointX - 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + (linkedFromTaskRecordRowIndex > linkedToTaskRecordRowIndex ? 0 : 1))\n        }, {\n            x: linkToPointX - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        }, {\n            x: linkToPointX,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        } ], linkFromPointX + 20 <= linkToPointX - 20 && linePoints.splice(2, 3, {\n            x: linkFromPointX + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        });\n        const lastPoint = linePoints[linePoints.length - 1];\n        arrowPoints = [ {\n            x: lastPoint.x,\n            y: lastPoint.y\n        }, {\n            x: lastPoint.x - 10,\n            y: lastPoint.y - 5\n        }, {\n            x: lastPoint.x - 10,\n            y: lastPoint.y + 5\n        }, {\n            x: lastPoint.x,\n            y: lastPoint.y\n        } ];\n    } else if (type === DependencyType.StartToFinish) {\n        startDate = linkedFromTaskStartDate, endDate = linkedToTaskStartDate;\n        const linkFromPointX = computeCountToTimeScale(linkedFromTaskStartDate, minDate, unit, step) * timelineColWidth, linkToPointX = computeCountToTimeScale(linkedToTaskEndDate, minDate, unit, step, 1) * timelineColWidth;\n        linePoints = [ {\n            x: linkFromPointX,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5)\n        }, {\n            x: linkFromPointX - 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5)\n        }, {\n            x: linkFromPointX - 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + (linkedFromTaskRecordRowIndex > linkedToTaskRecordRowIndex ? 0 : 1))\n        }, {\n            x: linkToPointX + 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + (linkedFromTaskRecordRowIndex > linkedToTaskRecordRowIndex ? 0 : 1))\n        }, {\n            x: linkToPointX + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        }, {\n            x: linkToPointX,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        } ], linkFromPointX - 20 >= linkToPointX + 20 && linePoints.splice(2, 3, {\n            x: linkFromPointX - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        });\n        const lastPoint = linePoints[linePoints.length - 1];\n        arrowPoints = [ {\n            x: lastPoint.x,\n            y: lastPoint.y\n        }, {\n            x: lastPoint.x + 10,\n            y: lastPoint.y - 5\n        }, {\n            x: lastPoint.x + 10,\n            y: lastPoint.y + 5\n        }, {\n            x: lastPoint.x,\n            y: lastPoint.y\n        } ];\n    } else if (type === DependencyType.StartToStart) {\n        startDate = linkedFromTaskStartDate, endDate = linkedToTaskStartDate;\n        const linkFromPointX = computeCountToTimeScale(linkedFromTaskStartDate, minDate, unit, step) * timelineColWidth, linkToPointX = computeCountToTimeScale(linkedToTaskStartDate, minDate, unit, step) * timelineColWidth;\n        linePoints = [ {\n            x: linkFromPointX,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5)\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkFromPointX : Math.min(linkFromPointX, linkToPointX)) - 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5)\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkFromPointX : Math.min(linkFromPointX, linkToPointX)) - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? 1 : .5))\n        }, {\n            x: linkToPointX - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? 1 : .5))\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkToPointX : Math.min(linkFromPointX, linkToPointX)) - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        }, {\n            x: linkToPointX,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        } ];\n        const lastPoint = linePoints[linePoints.length - 1];\n        arrowPoints = [ {\n            x: lastPoint.x,\n            y: lastPoint.y\n        }, {\n            x: lastPoint.x - 10,\n            y: lastPoint.y - 5\n        }, {\n            x: lastPoint.x - 10,\n            y: lastPoint.y + 5\n        }, {\n            x: lastPoint.x,\n            y: lastPoint.y\n        } ];\n    } else if (type === DependencyType.FinishToFinish) {\n        startDate = linkedFromTaskStartDate, endDate = linkedToTaskStartDate;\n        const linkFromPointX = computeCountToTimeScale(linkedFromTaskEndDate, minDate, unit, step, 1) * timelineColWidth, linkToPointX = computeCountToTimeScale(linkedToTaskEndDate, minDate, unit, step, 1) * timelineColWidth;\n        linePoints = [ {\n            x: linkFromPointX,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5)\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkFromPointX : Math.max(linkFromPointX, linkToPointX)) + 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5)\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkFromPointX : Math.max(linkFromPointX, linkToPointX)) + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? 1 : .5))\n        }, {\n            x: linkToPointX + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? 1 : .5))\n        }, {\n            x: linkToPointX + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        }, {\n            x: linkToPointX,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5)\n        } ];\n        const lastPoint = linePoints[linePoints.length - 1];\n        arrowPoints = [ {\n            x: lastPoint.x,\n            y: lastPoint.y\n        }, {\n            x: lastPoint.x + 10,\n            y: lastPoint.y - 5\n        }, {\n            x: lastPoint.x + 10,\n            y: lastPoint.y + 5\n        }, {\n            x: lastPoint.x,\n            y: lastPoint.y\n        } ];\n    }\n    return {\n        linePoints: linePoints,\n        arrowPoints: arrowPoints\n    };\n}\n\nexport function updateLinkLinePoints(type, linkedFromTaskStartDate, linkedFromTaskEndDate, linkedFromTaskRecordRowIndex, linkedFromTaskTaskDays, linkedFromMovedTaskBarNode, fromNodeDiffY, linkedToTaskStartDate, linkedToTaskEndDate, linkedToTaskRecordRowIndex, linkedToTaskTaskDays, linkedToMovedTaskBarNode, toNodeDiffY, gantt) {\n    const {unit: unit, step: step} = gantt.parsedOptions.reverseSortedTimelineScales[0], {minDate: minDate, rowHeight: rowHeight, timelineColWidth: timelineColWidth} = gantt.parsedOptions;\n    let startDate, endDate, linePoints = [], arrowPoints = [];\n    if (type === DependencyType.FinishToStart) {\n        startDate = linkedFromTaskStartDate, endDate = linkedToTaskStartDate;\n        const linkFromPointX = linkedFromMovedTaskBarNode ? linkedFromMovedTaskBarNode.attribute.x + linkedFromMovedTaskBarNode.attribute.width : computeCountToTimeScale(linkedFromTaskEndDate, minDate, unit, step, 1) * timelineColWidth, linkToPointX = linkedToMovedTaskBarNode ? linkedToMovedTaskBarNode.attribute.x : computeCountToTimeScale(linkedToTaskStartDate, minDate, unit, step) * timelineColWidth;\n        linePoints = [ {\n            x: linkFromPointX,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5) + fromNodeDiffY\n        }, {\n            x: linkFromPointX + 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5) + fromNodeDiffY\n        }, {\n            x: linkFromPointX + 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + (linkedFromTaskRecordRowIndex > linkedToTaskRecordRowIndex ? 0 : 1)) + fromNodeDiffY\n        }, {\n            x: linkToPointX - 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + (linkedFromTaskRecordRowIndex > linkedToTaskRecordRowIndex ? 0 : 1)) + fromNodeDiffY\n        }, {\n            x: linkToPointX - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        }, {\n            x: linkToPointX,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        } ], linkFromPointX + 20 <= linkToPointX - 20 && linePoints.splice(2, 3, {\n            x: linkFromPointX + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        });\n        const lastPoint = linePoints[linePoints.length - 1];\n        arrowPoints = [ {\n            x: lastPoint.x,\n            y: lastPoint.y\n        }, {\n            x: lastPoint.x - 10,\n            y: lastPoint.y - 5\n        }, {\n            x: lastPoint.x - 10,\n            y: lastPoint.y + 5\n        }, {\n            x: lastPoint.x,\n            y: lastPoint.y\n        } ];\n    } else if (type === DependencyType.StartToFinish) {\n        startDate = linkedFromTaskStartDate, endDate = linkedToTaskStartDate;\n        const linkFromPointX = linkedFromMovedTaskBarNode ? linkedFromMovedTaskBarNode.attribute.x : computeCountToTimeScale(linkedFromTaskStartDate, minDate, unit, step) * timelineColWidth, linkToPointX = linkedToMovedTaskBarNode ? linkedToMovedTaskBarNode.attribute.x + linkedToMovedTaskBarNode.attribute.width : computeCountToTimeScale(linkedToTaskEndDate, minDate, unit, step, 1) * timelineColWidth;\n        linePoints = [ {\n            x: linkFromPointX,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5) + fromNodeDiffY\n        }, {\n            x: linkFromPointX - 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5) + fromNodeDiffY\n        }, {\n            x: linkFromPointX - 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + (linkedFromTaskRecordRowIndex > linkedToTaskRecordRowIndex ? 0 : 1)) + fromNodeDiffY\n        }, {\n            x: linkToPointX + 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + (linkedFromTaskRecordRowIndex > linkedToTaskRecordRowIndex ? 0 : 1)) + fromNodeDiffY\n        }, {\n            x: linkToPointX + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        }, {\n            x: linkToPointX,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        } ], linkFromPointX - 20 >= linkToPointX + 20 && linePoints.splice(2, 3, {\n            x: linkFromPointX - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        });\n        const lastPoint = linePoints[linePoints.length - 1];\n        arrowPoints = [ {\n            x: lastPoint.x,\n            y: lastPoint.y\n        }, {\n            x: lastPoint.x + 10,\n            y: lastPoint.y - 5\n        }, {\n            x: lastPoint.x + 10,\n            y: lastPoint.y + 5\n        }, {\n            x: lastPoint.x,\n            y: lastPoint.y\n        } ];\n    } else if (type === DependencyType.StartToStart) {\n        startDate = linkedFromTaskStartDate, endDate = linkedToTaskStartDate;\n        const linkFromPointX = linkedFromMovedTaskBarNode ? linkedFromMovedTaskBarNode.attribute.x : computeCountToTimeScale(linkedFromTaskStartDate, minDate, unit, step) * timelineColWidth, linkToPointX = linkedToMovedTaskBarNode ? linkedToMovedTaskBarNode.attribute.x : computeCountToTimeScale(linkedToTaskStartDate, minDate, unit, step) * timelineColWidth;\n        linePoints = [ {\n            x: linkFromPointX,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5) + fromNodeDiffY\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkFromPointX : Math.min(linkFromPointX, linkToPointX)) - 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5) + fromNodeDiffY\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkFromPointX : Math.min(linkFromPointX, linkToPointX)) - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? 1 : .5)) + toNodeDiffY\n        }, {\n            x: linkToPointX - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? 1 : .5)) + toNodeDiffY\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkToPointX : Math.min(linkFromPointX, linkToPointX)) - 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        }, {\n            x: linkToPointX,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        } ];\n        const lastPoint = linePoints[linePoints.length - 1];\n        arrowPoints = [ {\n            x: lastPoint.x,\n            y: lastPoint.y\n        }, {\n            x: lastPoint.x - 10,\n            y: lastPoint.y - 5\n        }, {\n            x: lastPoint.x - 10,\n            y: lastPoint.y + 5\n        }, {\n            x: lastPoint.x,\n            y: lastPoint.y\n        } ];\n    } else if (type === DependencyType.FinishToFinish) {\n        startDate = linkedFromTaskStartDate, endDate = linkedToTaskStartDate;\n        const linkFromPointX = linkedFromMovedTaskBarNode ? linkedFromMovedTaskBarNode.attribute.x + linkedFromMovedTaskBarNode.attribute.width : computeCountToTimeScale(linkedFromTaskEndDate, minDate, unit, step, 1) * timelineColWidth, linkToPointX = linkedToMovedTaskBarNode ? linkedToMovedTaskBarNode.attribute.x + linkedToMovedTaskBarNode.attribute.width : computeCountToTimeScale(linkedToTaskEndDate, minDate, unit, step, 1) * timelineColWidth;\n        linePoints = [ {\n            x: linkFromPointX,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5) + fromNodeDiffY\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkFromPointX : Math.max(linkFromPointX, linkToPointX)) + 20,\n            y: rowHeight * (linkedFromTaskRecordRowIndex + .5) + fromNodeDiffY\n        }, {\n            x: (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? linkFromPointX : Math.max(linkFromPointX, linkToPointX)) + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? 1 : .5)) + toNodeDiffY\n        }, {\n            x: linkToPointX + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + (linkedFromTaskRecordRowIndex === linkedToTaskRecordRowIndex ? 1 : .5)) + toNodeDiffY\n        }, {\n            x: linkToPointX + 20,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        }, {\n            x: linkToPointX,\n            y: rowHeight * (linkedToTaskRecordRowIndex + .5) + toNodeDiffY\n        } ];\n        const lastPoint = linePoints[linePoints.length - 1];\n        arrowPoints = [ {\n            x: lastPoint.x,\n            y: lastPoint.y\n        }, {\n            x: lastPoint.x + 10,\n            y: lastPoint.y - 5\n        }, {\n            x: lastPoint.x + 10,\n            y: lastPoint.y + 5\n        }, {\n            x: lastPoint.x,\n            y: lastPoint.y\n        } ];\n    }\n    return {\n        linePoints: linePoints,\n        arrowPoints: arrowPoints\n    };\n}", "import { createDateAtMidnight } from '../tools/util';\nimport type { IMarkLine } from '../ts-types';\nimport type { Scenegraph } from './scenegraph';\nimport type { Line } from '@visactor/vtable/es/vrender';\nimport { Group, createLine } from '@visactor/vtable/es/vrender';\n\nexport class DragOrderLine {\n  _scene: Scenegraph;\n\n  dragLineContainer: Group;\n  dragLine: Line;\n  constructor(scene: Scenegraph) {\n    this._scene = scene;\n    // this.width = scene._gantt.tableNoFrameWidth;\n    this.dragLineContainer = new Group({\n      x: 0,\n      y: 0,\n      width: scene._gantt.tableNoFrameWidth,\n      height: Math.min(scene._gantt.tableNoFrameHeight, scene._gantt.drawHeight),\n      pickable: false,\n      clip: true,\n      visible: false\n    });\n    this.dragLineContainer.name = 'drag-order-line-container';\n    scene.tableGroup.addChild(this.dragLineContainer);\n    this.initDragLine();\n  }\n  initDragLine() {\n    if (this._scene._gantt.taskListTableInstance) {\n      const style = this._scene._gantt.taskListTableInstance.theme.dragHeaderSplitLine;\n      // 创建拖拽位置高亮线条\n      const lineObj = createLine({\n        pickable: false,\n        stroke: style.lineColor,\n        lineWidth: style.lineWidth,\n        // lineDash: style.lineDash,\n        points: []\n      });\n      this.dragLine = lineObj;\n      this.dragLineContainer.appendChild(lineObj);\n    }\n  }\n\n  /** 重新场景场景树节点 */\n  showDragLine(y: number) {\n    this.dragLineContainer.showAll();\n    this.dragLine.setAttribute('points', [\n      {\n        x: 0,\n        y: y\n      },\n      {\n        x: this._scene._gantt.tableNoFrameWidth,\n        y: y\n      }\n    ]);\n  }\n  hideDragLine() {\n    this.dragLineContainer.hideAll();\n  }\n}\n", "import type { Stage } from '@visactor/vtable/es/vrender';\nimport { container, Group, vglobal, createStage } from '@visactor/vtable/es/vrender';\nimport { Grid } from './grid';\nimport type { Gantt } from '../Gantt';\nimport { Env } from '../env';\nimport { ScrollBarComponent } from './scroll-bar';\nimport { bindScrollBarListener } from '../event/scroll';\nimport { TimelineHeader } from './timeline-header';\nimport { TaskBar } from './task-bar';\nimport { MarkLine } from './mark-line';\nimport { FrameBorder } from './frame-border';\nimport { findRecordByTaskKey, getTaskIndexByY, getTaskIndexsByTaskY } from '../gantt-helper';\nimport graphicContribution from './graphic';\nimport { TaskCreationButton } from './task-creation-button';\nimport { DependencyLink, updateLinkLinePoints } from './dependency-link';\nimport { DragOrderLine } from './drag-order-line';\nimport type { GanttTaskBarNode } from './gantt-node';\nimport { TasksShowMode } from '../ts-types';\ncontainer.load(graphicContribution);\nexport class Scenegraph {\n  dateStepWidth: number;\n  rowHeight: number;\n  _scales: {}[];\n  timelineHeader: TimelineHeader;\n  grid: Grid;\n  dependencyLink: DependencyLink;\n  taskBar: TaskBar;\n  _gantt: Gantt;\n  tableGroup: Group;\n  scrollbarComponent: ScrollBarComponent;\n  markLine: MarkLine;\n  dragOrderLine: DragOrderLine;\n  frameBorder: FrameBorder;\n  taskCreationButton: TaskCreationButton;\n  stage: Stage;\n  tableGroupWidth: number;\n  tableGroupHeight: number;\n  constructor(gantt: Gantt) {\n    this._gantt = gantt;\n    this.tableGroupWidth = gantt.tableNoFrameWidth;\n    this.tableGroupHeight = Math.min(gantt.tableNoFrameHeight, gantt.drawHeight);\n    let width;\n    let height;\n    if (Env.mode === 'node') {\n      // vglobal.setEnv('node', gantt.options.modeParams);\n      // width = table.canvasWidth;\n      // height = table.canvasHeight;\n    } else {\n      vglobal.setEnv('browser');\n      width = gantt.canvas.width;\n      height = gantt.canvas.height;\n    }\n    this.stage = createStage({\n      canvas: gantt.canvas,\n      width,\n      height,\n      disableDirtyBounds: false,\n      background: gantt.parsedOptions.underlayBackgroundColor,\n      // dpr: gantt.internalProps.pixelRatio,\n      enableLayout: true,\n      autoRender: false,\n      context: {\n        appName: 'vtable'\n      },\n      pluginList: ['poptipForText']\n      // afterRender: () => {\n      // this._gantt.fireListeners('after_render', null);\n      // }\n    });\n    (this.stage as any).gantt = this._gantt;\n    (this.stage as any).table = this._gantt; // 为了使用bindDebugTool\n    this.stage.defaultLayer.setTheme({\n      group: {\n        boundsPadding: 0,\n        strokeBoundsBuffer: 0,\n        lineJoin: 'round'\n      },\n      text: {\n        ignoreBuf: true\n      }\n    });\n    this.initSceneGraph();\n  }\n\n  initSceneGraph() {\n    const scene = this;\n\n    scene.tableGroup = new Group({\n      x: scene._gantt.tableX,\n      y: scene._gantt.tableY,\n      width: this.tableGroupWidth,\n      height: this.tableGroupHeight,\n      clip: true,\n      pickable: false\n    });\n    scene.stage.defaultLayer.add(scene.tableGroup);\n    scene.tableGroup.name = 'table';\n    // 初始化顶部时间线表头部分\n    scene.timelineHeader = new TimelineHeader(scene);\n\n    // 初始化网格线组件\n    scene.grid = new Grid(scene);\n    // 初始化任务条线组件\n    scene.dependencyLink = new DependencyLink(scene);\n    // 初始化任务条线组件\n    scene.taskBar = new TaskBar(scene);\n\n    // 初始化标记线组件\n    scene.markLine = new MarkLine(scene);\n\n    // 初始化边框\n    scene.frameBorder = new FrameBorder(scene);\n\n    // 初始化滚动条组件\n    scene.scrollbarComponent = new ScrollBarComponent(scene._gantt);\n    scene.stage.defaultLayer.addChild(scene.scrollbarComponent.hScrollBar);\n    scene.stage.defaultLayer.addChild(scene.scrollbarComponent.vScrollBar);\n\n    //初始化换位交互标记线\n    scene.dragOrderLine = new DragOrderLine(scene);\n  }\n\n  afterCreateSceneGraph() {\n    this.scrollbarComponent.updateScrollBar();\n    bindScrollBarListener(this._gantt.eventManager);\n  }\n\n  refreshAll() {\n    this.tableGroupHeight = Math.min(this._gantt.tableNoFrameHeight, this._gantt.drawHeight);\n    this.tableGroupWidth = this._gantt.tableNoFrameWidth;\n    this.tableGroup.setAttribute('height', this.tableGroupHeight);\n    this.tableGroup.setAttribute('width', this.tableGroupWidth);\n    this.timelineHeader.refresh();\n    this.grid.refresh();\n    this.taskBar.refresh();\n    this.dependencyLink.refresh();\n    this.markLine.refresh();\n    this.dependencyLink.refresh();\n    this.frameBorder.resize();\n    this.scrollbarComponent.updateScrollBar();\n    this.updateNextFrame();\n  }\n\n  refreshTaskBars() {\n    // this.timelineHeader.refresh();\n    // this.grid.refresh();\n    this.dependencyLink.refresh();\n    this.taskBar.refresh();\n    // this.markLine.refresh();\n    // this.frameBorder.refresh();\n    // this.scrollbarComponent.refresh();\n    this.updateNextFrame();\n  }\n  refreshTaskBarsAndGrid() {\n    this._gantt.verticalSplitResizeLine.style.height = this._gantt.drawHeight + 'px'; //'100%';\n    this.tableGroupHeight = Math.min(this._gantt.tableNoFrameHeight, this._gantt.drawHeight);\n    this.tableGroup.setAttribute('height', this.tableGroupHeight);\n    // this.timelineHeader.refresh();\n    this.grid.refresh();\n    this.taskBar.refresh();\n    this.dependencyLink.refresh();\n    this.markLine.refresh();\n    this.frameBorder.resize();\n    this.scrollbarComponent.updateScrollBar();\n    this.updateNextFrame();\n  }\n\n  updateTableSize() {\n    this.tableGroupHeight = Math.min(this._gantt.tableNoFrameHeight, this._gantt.drawHeight);\n    this.tableGroupWidth = this._gantt.tableNoFrameWidth;\n    this.tableGroup.setAttributes({\n      x: this._gantt.tableX,\n      y: this._gantt.tableY,\n      width: this._gantt.tableNoFrameWidth,\n      height: this.tableGroupHeight\n    } as any);\n    this.grid.resize();\n    this.taskBar.resize();\n    this.markLine.refresh();\n    this.frameBorder.resize();\n  }\n\n  /**\n   * @description: 绘制场景树\n   * @param {any} element\n   * @param {CellRange} visibleCoord\n   * @return {*}\n   */\n  renderSceneGraph() {\n    this.stage.render();\n  }\n\n  /**\n   * @description: 触发下一帧渲染\n   * @return {*}\n   */\n  updateNextFrame() {\n    this.stage.renderNextFrame();\n  }\n  get width(): number {\n    return this.tableGroup.attribute?.width ?? 0;\n  }\n\n  get height(): number {\n    return this.tableGroup.attribute?.height ?? 0;\n  }\n\n  get x(): number {\n    return this.tableGroup.attribute?.x ?? 0;\n  }\n\n  get y(): number {\n    return this.tableGroup.attribute?.y ?? 0;\n  }\n\n  /**\n   * @description: 设置表格的x位置，滚动中使用\n   * @param {number} x\n   * @return {*}\n   */\n  setX(x: number, isEnd = false) {\n    this.timelineHeader.setX(x);\n    this.grid.setX(x);\n    this.taskBar.setX(x);\n    this.dependencyLink.setX(x);\n    this.markLine.setX(x);\n    this.updateNextFrame();\n    // this._gantt.scenegraph.proxy.setX(-x, isEnd);\n  }\n\n  /**\n   * @description: 更新表格的y位置，滚动中使用\n   * @param {number} y\n   * @return {*}\n   */\n  setY(y: number, isEnd = false) {\n    // this._gantt.scenegraph.proxy.setY(-y, isEnd);\n    this.grid.setY(y);\n    this.taskBar.setY(y);\n    this.dependencyLink.setY(y);\n    this.updateNextFrame();\n  }\n\n  setPixelRatio(pixelRatio: number) {\n    // this.stage.setDpr(pixelRatio);\n    // 这里因为本时刻部分节点有更新bounds标记，直接render回导致开启DirtyBounds，无法完整重绘画布；\n    // 所以这里先关闭DirtyBounds，等待下一帧再开启\n    this.stage.disableDirtyBounds();\n    this.stage.window.setDpr(pixelRatio);\n    this.stage.render();\n    this.stage.enableDirtyBounds();\n  }\n\n  resize() {\n    this.updateTableSize();\n    // this.updateBorderSizeAndPosition();\n    this.scrollbarComponent.updateScrollBar();\n    this.updateNextFrame();\n  }\n  release() {\n    this.stage.release();\n  }\n\n  showTaskCreationButton(x: number, y: number, dateIndex: number) {\n    if (!this.taskCreationButton) {\n      this.taskCreationButton = new TaskCreationButton(this._gantt.scenegraph);\n    }\n    this.taskCreationButton.show(x, y, this._gantt.getDateColWidth(dateIndex), this._gantt.parsedOptions.rowHeight);\n    this.updateNextFrame();\n  }\n\n  hideTaskCreationButton() {\n    if (this.taskCreationButton) {\n      this.taskCreationButton.hide();\n      this.updateNextFrame();\n    }\n  }\n  refreshRecordLinkNodes(taskIndex: number, sub_task_index: number, target: GanttTaskBarNode, dy: number = 0) {\n    const gantt: Gantt = this._gantt;\n    const record = gantt.getRecordByIndex(taskIndex, sub_task_index);\n    const vtable_gantt_linkedTo = record.vtable_gantt_linkedTo;\n    const vtable_gantt_linkedFrom = record.vtable_gantt_linkedFrom;\n    for (let i = 0; i < vtable_gantt_linkedTo?.length; i++) {\n      const link = vtable_gantt_linkedTo[i];\n      const linkLineNode = link.vtable_gantt_linkLineNode;\n      const lineArrowNode = link.vtable_gantt_linkArrowNode;\n\n      const { linkedToTaskKey, linkedFromTaskKey, type } = link;\n      const { taskKeyField, minDate } = gantt.parsedOptions;\n      const linkedFromTaskRecord = findRecordByTaskKey(gantt.records, taskKeyField, linkedFromTaskKey);\n      const linkedToTaskRecord = findRecordByTaskKey(gantt.records, taskKeyField, linkedToTaskKey);\n      // const { startDate: linkedToTaskStartDate, endDate: linkedToTaskEndDate } =\n      //   gantt.getTaskInfoByTaskListIndex(taskIndex);\n      // const taskShowIndex = gantt.getTaskShowIndexByRecordIndex(linkedFromTaskRecord.index);\n      // const { startDate: linkedFromTaskStartDate, endDate: linkedFromTaskEndDate } =\n      //   gantt.getTaskInfoByTaskListIndex(taskShowIndex);\n\n      let linkedToTaskStartDate;\n      let linkedToTaskEndDate;\n      let linkedToTaskTaskDays;\n      // let linkedToTaskTaskRecord;\n      let linkedFromTaskStartDate;\n      let linkedFromTaskEndDate;\n      let linkedFromTaskTaskDays;\n      // let linkedFromTaskTaskRecord;\n\n      let linkedToTaskShowIndex;\n      let linkedFromTaskShowIndex;\n      let diffY: number;\n      if (gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline) {\n        const new_indexs = getTaskIndexsByTaskY(target.attribute.y + dy, gantt);\n        linkedFromTaskShowIndex = linkedFromTaskRecord.index[0];\n        // linkedToTaskShowIndex = linkedToTaskRecord.index[0];\n        const beforeRowCountLinkedTo =\n          gantt.getRowsHeightByIndex(0, new_indexs.task_index - 1) / gantt.parsedOptions.rowHeight; // 耦合了listTableOption的customComputeRowHeight\n        linkedToTaskShowIndex = beforeRowCountLinkedTo;\n        ({\n          startDate: linkedToTaskStartDate,\n          endDate: linkedToTaskEndDate,\n          taskDays: linkedToTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedToTaskRecord.index[0], linkedToTaskRecord.index[1]));\n        ({\n          startDate: linkedFromTaskStartDate,\n          endDate: linkedFromTaskEndDate,\n          taskDays: linkedFromTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedFromTaskRecord.index[0], linkedFromTaskRecord.index[1]));\n\n        const taskbarHeight = gantt.parsedOptions.taskBarStyle.width;\n        diffY = target.attribute.y + taskbarHeight / 2 - (linkedToTaskShowIndex + 0.5) * gantt.parsedOptions.rowHeight;\n      } else if (\n        gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate ||\n        gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n        gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n      ) {\n        const new_indexs = getTaskIndexsByTaskY(target.attribute.y + dy, gantt);\n        const beforeRowCountLinkedFrom =\n          gantt.getRowsHeightByIndex(0, linkedFromTaskRecord.index[0] - 1) / gantt.parsedOptions.rowHeight; // 耦合了listTableOption的customComputeRowHeight\n        linkedFromTaskShowIndex =\n          beforeRowCountLinkedFrom +\n          (gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n          gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n            ? linkedFromTaskRecord.record.vtable_gantt_showIndex\n            : linkedFromTaskRecord.index[1]);\n        // const beforeRowCountLinkedTo =\n        //   gantt.getRowsHeightByIndex(0, linkedToTaskRecord.index[0] - 1) / gantt.parsedOptions.rowHeight; // 耦合了listTableOption的customComputeRowHeight\n        // linkedToTaskShowIndex = beforeRowCountLinkedTo + linkedToTaskRecord.index[1];\n        const beforeRowCountLinkedTo =\n          gantt.getRowsHeightByIndex(0, new_indexs.task_index - 1) / gantt.parsedOptions.rowHeight; // 耦合了listTableOption的customComputeRowHeight\n        linkedToTaskShowIndex =\n          beforeRowCountLinkedTo +\n          (gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n          gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n            ? linkedToTaskRecord.record.vtable_gantt_showIndex\n            : new_indexs.sub_task_index);\n\n        ({\n          startDate: linkedToTaskStartDate,\n          endDate: linkedToTaskEndDate,\n          taskDays: linkedToTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedToTaskRecord.index[0], linkedToTaskRecord.index[1]));\n        ({\n          startDate: linkedFromTaskStartDate,\n          endDate: linkedFromTaskEndDate,\n          taskDays: linkedFromTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedFromTaskRecord.index[0], linkedFromTaskRecord.index[1]));\n\n        const taskbarHeight = gantt.parsedOptions.taskBarStyle.width;\n        diffY = target.attribute.y + taskbarHeight / 2 - (linkedToTaskShowIndex + 0.5) * gantt.parsedOptions.rowHeight;\n      } else {\n        linkedFromTaskShowIndex = gantt.getTaskShowIndexByRecordIndex(linkedFromTaskRecord.index);\n        linkedToTaskShowIndex = gantt.getTaskShowIndexByRecordIndex(linkedToTaskRecord.index);\n        if (linkedFromTaskShowIndex === -1 || linkedToTaskShowIndex === -1) {\n          continue;\n        }\n        ({\n          startDate: linkedToTaskStartDate,\n          endDate: linkedToTaskEndDate,\n          taskDays: linkedToTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedToTaskShowIndex));\n        ({\n          startDate: linkedFromTaskStartDate,\n          endDate: linkedFromTaskEndDate,\n          taskDays: linkedFromTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedFromTaskShowIndex));\n      }\n      const { linePoints, arrowPoints } = updateLinkLinePoints(\n        type,\n        linkedFromTaskStartDate,\n        linkedFromTaskEndDate,\n        linkedFromTaskShowIndex,\n        linkedFromTaskTaskDays,\n        null,\n        0,\n        linkedToTaskStartDate,\n        linkedToTaskEndDate,\n        linkedToTaskShowIndex,\n        linkedToTaskTaskDays,\n        target,\n        diffY ?? 0,\n        this._gantt\n      );\n      linkLineNode.setAttribute('points', linePoints);\n      lineArrowNode.setAttribute('points', arrowPoints);\n    }\n\n    for (let i = 0; i < vtable_gantt_linkedFrom?.length; i++) {\n      const link = vtable_gantt_linkedFrom[i];\n      const linkLineNode = link.vtable_gantt_linkLineNode;\n      const lineArrowNode = link.vtable_gantt_linkArrowNode;\n\n      const { linkedToTaskKey, linkedFromTaskKey, type } = link;\n      const { taskKeyField, minDate } = gantt.parsedOptions;\n      const linkedToTaskRecord = findRecordByTaskKey(gantt.records, taskKeyField, linkedToTaskKey);\n      const linkedFromTaskRecord = findRecordByTaskKey(gantt.records, taskKeyField, linkedFromTaskKey);\n      // const { startDate: linkedFromTaskStartDate, endDate: linkedFromTaskEndDate } =\n      //   gantt.getTaskInfoByTaskListIndex(taskIndex);\n      // const taskShowIndex = gantt.getTaskShowIndexByRecordIndex(linkedToTaskRecord.index);\n      // const { startDate: linkedToTaskStartDate, endDate: linkedToTaskEndDate } =\n      //   gantt.getTaskInfoByTaskListIndex(taskShowIndex);\n\n      let linkedToTaskStartDate;\n      let linkedToTaskEndDate;\n      let linkedToTaskTaskDays;\n      let linkedFromTaskStartDate;\n      let linkedFromTaskEndDate;\n      let linkedFromTaskTaskDays;\n\n      let linkedToTaskShowIndex;\n      let linkedFromTaskShowIndex;\n      let diffY: number;\n      if (gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline) {\n        const new_indexs = getTaskIndexsByTaskY(target.attribute.y + dy, gantt);\n        const beforeRowCountLinkedFrom =\n          gantt.getRowsHeightByIndex(0, new_indexs.task_index - 1) / gantt.parsedOptions.rowHeight; // 耦合了listTableOption的customComputeRowHeight\n        linkedFromTaskShowIndex = beforeRowCountLinkedFrom;\n\n        // linkedFromTaskShowIndex = linkedFromTaskRecord.index[0];\n        linkedToTaskShowIndex = linkedToTaskRecord.index[0];\n        ({\n          startDate: linkedToTaskStartDate,\n          endDate: linkedToTaskEndDate,\n          taskDays: linkedToTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedToTaskRecord.index[0], linkedToTaskRecord.index[1]));\n        ({\n          startDate: linkedFromTaskStartDate,\n          endDate: linkedFromTaskEndDate,\n          taskDays: linkedFromTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedFromTaskRecord.index[0], linkedFromTaskRecord.index[1]));\n        const taskbarHeight = gantt.parsedOptions.taskBarStyle.width;\n        diffY =\n          target.attribute.y + taskbarHeight / 2 - (linkedFromTaskShowIndex + 0.5) * gantt.parsedOptions.rowHeight;\n      } else if (\n        gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate ||\n        gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n        gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n      ) {\n        const new_indexs = getTaskIndexsByTaskY(target.attribute.y + dy, gantt);\n        // const beforeRowCountLinkedFrom =\n        //   gantt.getRowsHeightByIndex(0, linkedFromTaskRecord.index[0] - 1) / gantt.parsedOptions.rowHeight; // 耦合了listTableOption的customComputeRowHeight\n        // linkedFromTaskShowIndex = beforeRowCountLinkedFrom + linkedFromTaskRecord.index[1];\n        const beforeRowCountLinkedFrom =\n          gantt.getRowsHeightByIndex(0, new_indexs.task_index - 1) / gantt.parsedOptions.rowHeight; // 耦合了listTableOption的customComputeRowHeight\n        linkedFromTaskShowIndex =\n          beforeRowCountLinkedFrom +\n          (gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n          gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n            ? linkedFromTaskRecord.record.vtable_gantt_showIndex\n            : new_indexs.sub_task_index);\n\n        const beforeRowCountLinkedTo =\n          gantt.getRowsHeightByIndex(0, linkedToTaskRecord.index[0] - 1) / gantt.parsedOptions.rowHeight; // 耦合了listTableOption的customComputeRowHeight\n        linkedToTaskShowIndex =\n          beforeRowCountLinkedTo +\n          (gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n          gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n            ? linkedToTaskRecord.record.vtable_gantt_showIndex\n            : linkedToTaskRecord.index[1]);\n        ({\n          startDate: linkedToTaskStartDate,\n          endDate: linkedToTaskEndDate,\n          taskDays: linkedToTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedToTaskRecord.index[0], linkedToTaskRecord.index[1]));\n        ({\n          startDate: linkedFromTaskStartDate,\n          endDate: linkedFromTaskEndDate,\n          taskDays: linkedFromTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedFromTaskRecord.index[0], linkedFromTaskRecord.index[1]));\n        const taskbarHeight = gantt.parsedOptions.taskBarStyle.width;\n        diffY =\n          target.attribute.y + taskbarHeight / 2 - (linkedFromTaskShowIndex + 0.5) * gantt.parsedOptions.rowHeight;\n      } else {\n        linkedFromTaskShowIndex = gantt.getTaskShowIndexByRecordIndex(linkedFromTaskRecord.index);\n        linkedToTaskShowIndex = gantt.getTaskShowIndexByRecordIndex(linkedToTaskRecord.index);\n        if (linkedFromTaskShowIndex === -1 || linkedToTaskShowIndex === -1) {\n          continue;\n        }\n        ({\n          startDate: linkedToTaskStartDate,\n          endDate: linkedToTaskEndDate,\n          taskDays: linkedToTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedToTaskShowIndex));\n        ({\n          startDate: linkedFromTaskStartDate,\n          endDate: linkedFromTaskEndDate,\n          taskDays: linkedFromTaskTaskDays\n        } = gantt.getTaskInfoByTaskListIndex(linkedFromTaskShowIndex));\n      }\n      const { linePoints, arrowPoints } = updateLinkLinePoints(\n        type,\n        linkedFromTaskStartDate,\n        linkedFromTaskEndDate,\n        linkedFromTaskShowIndex,\n        linkedFromTaskTaskDays,\n\n        target,\n        diffY ?? 0,\n        linkedToTaskStartDate,\n        linkedToTaskEndDate,\n        linkedToTaskShowIndex,\n        linkedToTaskTaskDays,\n        null,\n        0,\n        this._gantt\n      );\n\n      linkLineNode.setAttribute('points', linePoints);\n      lineArrowNode.setAttribute('points', arrowPoints);\n    }\n  }\n}\n", "export const judgeType = (value: any) => {\n  switch (Object.prototype.toString.call(value)) {\n    case '[object Object]':\n      return 'object';\n    case '[object Function]':\n      return 'function';\n    case '[object Array]':\n      return 'array';\n    case '[object String]':\n      return 'string';\n    case '[object Number]':\n      return 'number';\n    case '[object RegExp]':\n      return 'regExp';\n    case '[object Boolean]':\n      return 'boolean';\n    case '[object Symbol]':\n      return 'symbol';\n    case '[object Date]':\n      return 'date';\n    case '[object Undefined]':\n      return 'undefined';\n    case '[object Null]':\n      return 'null';\n    case '[object Error]':\n      return 'error';\n    case '[object HTMLDocument]':\n      return 'document';\n    case '[object global]':\n      return 'global'; // window 是全局对象 global 的引用\n    default:\n      return null;\n  }\n};\n\nexport const isIt = (v: any, type: string): boolean => judgeType(v) === type;\n\nexport const isObject = (v: any): boolean => isIt(v, 'object');\nexport const isFunction = (v: any): boolean => isIt(v, 'function');\nexport const isArray = (v: any): boolean => isIt(v, 'array');\nexport const isString = (v: any): boolean => isIt(v, 'string');\nexport const isNumber = (v: any): boolean => isIt(v, 'number');\nexport const isRegExp = (v: any): boolean => isIt(v, 'regExp');\nexport const isBoolean = (v: any): boolean => isIt(v, 'boolean');\nexport const isSymbol = (v: any): boolean => isIt(v, 'symbol');\nexport const isDate = (v: any): boolean => isIt(v, 'date');\nexport const isUndefined = (v: any): boolean => isIt(v, 'undefined');\nexport const isNull = (v: any): boolean => isIt(v, 'null');\nexport const isError = (v: any): boolean => isIt(v, 'error');\nexport const isDocument = (v: any): boolean => isIt(v, 'document');\nexport const isGlobal = (v: any): boolean => isIt(v, 'global');\n", "import { isObject } from './isx';\n\nexport function debounce(func: Function, wait?: number, options?: any) {\n  let lastArgs: any;\n  let lastThis: any;\n  let maxWait: number; // 最长等待时间\n  let result: any; // 存储 func 函数的返回值\n  let timerId: number | undefined; // 定时器 id\n  let lastCallTime: number; // 最近一次 执行 debounced 函数时的时间\n\n  // 最近一次执行 func 时的时间戳\n  let lastInvokeTime = 0;\n  // options 是否 传入了 maxWait\n  let maxing = false;\n  // 是否在延迟开始前调用函数\n  let leading = false;\n  // 是否在延迟结束后调用函数\n  let trailing = true;\n\n  const useRAF = !wait && wait !== 0 && typeof requestAnimationFrame === 'function';\n\n  if (typeof func !== 'function') {\n    throw new TypeError('Expected a function');\n  }\n\n  wait = +(wait as number) || 0;\n\n  if (isObject(options)) {\n    leading = !!options.leading;\n\n    maxing = 'maxWait' in options;\n    if (maxing) {\n      maxWait = Math.max(+options.maxWait || 0, wait);\n    }\n\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time: number) {\n    const args = lastArgs;\n    const thisArg = lastThis;\n\n    lastThis = undefined;\n    lastArgs = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  // 开启定时器\n  function startTimer(pendingFunc: any, wait: number) {\n    if (useRAF) {\n      return requestAnimationFrame(pendingFunc);\n    }\n    return setTimeout(pendingFunc, wait);\n  }\n\n  // 清除定时器\n  // function cancelTimer(id: number) {\n  //   if (useRAF) {\n  //     return cancelAnimationFrame(id);\n  //   }\n  //   clearTimeout(id);\n  // }\n\n  // 在延迟开始前调用\n  function leadingEdge(time: number) {\n    // 记录 函数被调用时 的时间戳\n    lastInvokeTime = time;\n    //@ts-ignore\n    timerId = startTimer(timerExpired, wait);\n    return leading ? invokeFunc(time) : result;\n  }\n\n  // 在延迟结束后调用\n  function trailingEdge(time: number) {\n    timerId = undefined;\n\n    // lastArgs 在 debounced 函数执行时赋值\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n\n    // 重置参数和作用域\n    lastThis = undefined;\n    lastArgs = undefined;\n    return result;\n  }\n\n  function remainingWait(time: number) {\n    // 计算 time 与最近一次调用 debounced 函数的时间差\n    const timeSinceLastCall = time - lastCallTime;\n    // 计算 time 与最近一次调用 func 函数的时间差\n    const timeSinceLastInvoke = time - lastInvokeTime;\n    // 用 wait 减去已经等待的时间\n    const timeWaiting = wait && -timeSinceLastCall;\n\n    return maxing ? Math.min(timeWaiting as number, maxWait - timeSinceLastInvoke) : timeWaiting;\n  }\n\n  // 是否可以执行函数\n  function shouldInvoke(time: number) {\n    // 计算 time 与最近一次调用 debounced 函数的时间差\n    const timeSinceLastCall = time - lastCallTime;\n    // 计算 time 与最近一次调用 func 函数的时间差\n    const timeSinceLastInvoke = time - lastInvokeTime;\n\n    return (\n      // 是不是第一次执行 debouned 函数\n      lastCallTime === undefined ||\n      timeSinceLastCall >= (wait as number) ||\n      timeSinceLastCall < 0 ||\n      (maxing && timeSinceLastInvoke >= maxWait)\n    );\n  }\n\n  // 封装执行函数，用于 wait 延迟结束后执行\n  function timerExpired() {\n    const time = Date.now();\n    // 根据时间来判断是否可以执行 func 函数\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // 重新计算时间，重新建一个定时器\n    // @ts-ignore\n    timerId = startTimer(timerExpired, remainingWait(time));\n  }\n\n  function debounced(this: any, ...args: any) {\n    const time = Date.now();\n    const isInvoking = shouldInvoke(time);\n\n    lastArgs = args;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      // 第一次执行时\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // @ts-ignore\n        timerId = startTimer(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n\n    // 因为 trailingEdge 函数内部会执行 timerId = undefined\n    // trailingEdge 函数执行之后，又触发了 debounced\n    if (timerId === undefined) {\n      // @ts-ignore\n      timerId = startTimer(timerExpired, wait);\n    }\n\n    return result;\n  }\n\n  return debounced;\n}\n", "// import type { AnyFunction, EventListenerId } from '../ts-types';\nimport type { EventTarget as CustomEventTarget } from './EventTarget';\nimport { debounce } from '../tools/debounce';\nimport { Env } from '../env';\nexport type EventListenerId = any; //TODO 类型\nlet idCount = 1;\ntype EventHandlerTarget = EventTarget | CustomEventTarget;\ntype Listener = any; // AnyFunction; TODO 类型\ntype EventListenerObject = {\n  target: EventHandlerTarget;\n  type: string;\n  listener: Listener;\n  options: any[];\n};\n\nexport type ResizeObserverCallBack = ({\n  width,\n  height,\n  windowSizeNotChange\n}: {\n  width: number;\n  height: number;\n  windowSizeNotChange: boolean;\n}) => void;\n\nexport class ResizeObserver {\n  resizeTime = 100;\n  element: HTMLElement;\n  cb: ResizeObserverCallBack;\n  observer?: MutationObserver;\n  lastSize: {\n    width: number;\n    height: number;\n  } = {\n    width: 0,\n    height: 0\n  };\n  callBackDebounce: () => void;\n\n  constructor(element: HTMLElement, cb: ResizeObserverCallBack, resizeTime?: number) {\n    this.element = element;\n    this.cb = cb;\n    this.lastSize = this.getSize();\n    if (resizeTime) {\n      this.resizeTime = Math.max(resizeTime, 16);\n    }\n\n    this.callBackDebounce = debounce(this.callBack, this.resizeTime);\n    //TODO: 这个地方的 addEventListener resize 应该更改到下面的else逻辑中，兼容ResizeObserver不存在的情况\n    window?.addEventListener('resize', this.onResize);\n    // 优先使用 ResizeObserver\n    if ('ResizeObserver' in window) {\n      // @ts-ignore\n      const ResizeObserverWindow: any = window.ResizeObserver;\n      this.observer = new ResizeObserverWindow(this.mutationResize);\n      this.observer?.observe(this.element);\n    } else if ('MutationObserver' in window) {\n      this.observer = new MutationObserver(this.mutationResize);\n      this.observer.observe(this.element, {\n        attributes: true,\n        attributeFilter: ['style']\n      });\n    }\n  }\n\n  mutationResize = () => {\n    this.onResize();\n  };\n\n  disConnect() {\n    window.removeEventListener('resize', this.onResize);\n    if (this.observer) {\n      this.observer.disconnect();\n      this.observer = undefined;\n    }\n  }\n\n  callBack = () => {\n    const newSize = this.getSize();\n    let windowSizeNotChange = false;\n    if (newSize.width === this.lastSize.width && newSize.height === this.lastSize.height) {\n      windowSizeNotChange = true;\n    }\n    this.lastSize = newSize;\n    this.cb && this.cb({ ...this.lastSize, windowSizeNotChange });\n  };\n\n  setSize(size: { width: number; height: number }) {\n    this.lastSize = size;\n  }\n\n  private onResize = () => {\n    // if (this.checkSize()) {\n    this.callBackDebounce();\n    // }\n  };\n\n  private checkSize() {\n    const newSize = this.getSize();\n    if (newSize.width === this.lastSize.width && newSize.height === this.lastSize.height) {\n      return false;\n    }\n    return true;\n  }\n\n  getSize() {\n    if (!this.element) {\n      return {\n        ...this.lastSize\n      };\n    }\n    return {\n      width: Math.floor(this.element.clientWidth),\n      height: Math.floor(this.element.clientHeight)\n    };\n  }\n}\n\nexport class EventHandler {\n  resizeTime?: number;\n\n  private listeners: {\n    [key: string]: EventListenerObject;\n  } = {};\n\n  private reseizeListeners: {\n    [key: string]: ResizeObserver;\n  } = {};\n\n  on(\n    target: HTMLElement | Window | EventHandlerTarget,\n    type: string,\n    listener: Listener,\n    ...options: any[]\n  ): EventListenerId {\n    if (Env.mode === 'node') {\n      return -1;\n    }\n    const id = idCount++;\n    if (target?.addEventListener) {\n      if (type !== 'resize' || (target as Window) === window) {\n        (target as EventTarget)?.addEventListener(type, listener, ...(options as []));\n      } else {\n        const resizeObserver = new ResizeObserver(target as HTMLElement, listener, this.resizeTime);\n        this.reseizeListeners[id] = resizeObserver;\n      }\n    }\n    const obj = { target, type, listener, options };\n    this.listeners[id] = obj;\n    return id;\n  }\n  once(\n    target: EventHandlerTarget,\n    type: string,\n    listener: Listener,\n    ...options: (boolean | AddEventListenerOptions)[]\n  ): EventListenerId {\n    if (Env.mode === 'node') {\n      return -1;\n    }\n    const id = this.on(\n      target,\n      type,\n      (...args: any[]) => {\n        this.off(id);\n        listener(...args);\n      },\n      ...options\n    );\n    return id;\n  }\n  off(id: EventListenerId | null | undefined): void {\n    if (Env.mode === 'node') {\n      return;\n    }\n    if (id == null) {\n      return;\n    }\n    const obj = this.listeners?.[id];\n    if (!obj) {\n      return;\n    }\n    delete this.listeners[id];\n    if (obj.target.removeEventListener) {\n      obj.target.removeEventListener(obj.type, obj.listener, ...(obj.options as []));\n    }\n  }\n  fire(target: EventTarget, type: string, ...args: any[]): void {\n    if (Env.mode === 'node') {\n      return;\n    }\n    for (const key in this.listeners) {\n      const listener = this.listeners[key];\n      if (listener.target === target && listener.type === type) {\n        listener.listener.call(listener.target, ...args);\n      }\n    }\n  }\n  hasListener(target: EventTarget, type: string): boolean {\n    if (Env.mode === 'node') {\n      return false;\n    }\n    let result = false;\n    for (const key in this.listeners) {\n      const listener = this.listeners[key];\n      if (listener.target === target && listener.type === type) {\n        result = true;\n      }\n    }\n\n    return result;\n  }\n  clear(): void {\n    if (Env.mode === 'node') {\n      return;\n    }\n    for (const key in this.listeners) {\n      const listener = this.listeners[key];\n      if (listener.target.removeEventListener) {\n        listener.target.removeEventListener(listener.type, listener.listener, ...(listener.options as []));\n      }\n    }\n\n    for (const key in this.reseizeListeners) {\n      const resizeObserver: ResizeObserver = this.reseizeListeners[key];\n      resizeObserver?.disConnect();\n    }\n\n    this.listeners = {};\n  }\n  release(): void {\n    if (Env.mode === 'node') {\n      return;\n    }\n    this.clear();\n    (this as any).listeners = {};\n  }\n}\n", "import { isNode } from '../gantt-helper';\n\nexport let defaultPixelRatio = 1;\n/*\n * @Description: 设置像素比\n */\nexport function getPixelRatio(): number {\n  if (isNode) {\n    defaultPixelRatio = 1;\n  } else {\n    defaultPixelRatio = Math.ceil(window.devicePixelRatio || 1);\n    if (defaultPixelRatio > 1 && defaultPixelRatio % 2 !== 0) {\n      // 非整数倍的像素比，向上取整\n      defaultPixelRatio += 1;\n    }\n  }\n  return defaultPixelRatio;\n}\ngetPixelRatio();\n", "import { vglobal } from '@visactor/vtable/es/vrender';\nimport type { Circle, FederatedPointerEvent } from '@visactor/vtable/es/vrender';\nimport type { Gantt } from '../Gantt';\nimport { EventHandler } from '../event/EventHandler';\nimport { handleWhell } from '../event/scroll';\nimport { formatDate, parseDateFormat, throttle } from '../tools/util';\nimport { GANTT_EVENT_TYPE, InteractionState, TasksShowMode } from '../ts-types';\nimport { isValid } from '@visactor/vutils';\nimport { getPixelRatio } from '../tools/pixel-ratio';\nimport { DayTimes, getDateIndexByX, getTaskIndexByY } from '../gantt-helper';\nimport type { GanttTaskBarNode } from '../scenegraph/gantt-node';\n\nexport class EventManager {\n  _gantt: Gantt;\n  _eventHandler: EventHandler;\n  isDown: boolean = false;\n  isDraging: boolean = false;\n  lastDragPointerXYOnWindow: { x: number; y: number };\n  //报错已绑定过的事件 后续清除绑定\n  globalEventListeners: { name: string; env: 'document' | 'body' | 'window'; callback: (e?: any) => void }[] = [];\n  poniterState: 'down' | 'draging' | 'up';\n  // lastDragPointerXYOnResizeLine: { x: number; y: number };\n  constructor(gantt: Gantt) {\n    this._gantt = gantt;\n    this._eventHandler = new EventHandler();\n    this.bindEvent();\n  }\n  release() {\n    this._eventHandler.release();\n    // remove global event listerner\n    this.globalEventListeners.forEach(item => {\n      if (item.env === 'document') {\n        document.removeEventListener(item.name, item.callback);\n      } else if (item.env === 'body') {\n        document.body.removeEventListener(item.name, item.callback);\n      } else if (item.env === 'window') {\n        window.removeEventListener(item.name, item.callback);\n      }\n    });\n    this.globalEventListeners = [];\n  }\n  // 绑定DOM事件\n  bindEvent() {\n    bindTableGroupListener(this);\n    bindContainerDomListener(this);\n    // bindScrollBarListener(this);\n  }\n}\nfunction bindTableGroupListener(event: EventManager) {\n  const scene = event._gantt.scenegraph;\n  const gantt = event._gantt;\n  const stateManager = gantt.stateManager;\n\n  scene.tableGroup.addEventListener('pointerdown', (e: FederatedPointerEvent) => {\n    if (e.button !== 0) {\n      // 只处理左键\n      return;\n    }\n    let downBarNode;\n    let downCreationButtomNode;\n    let downDependencyLineNode;\n    let downLeftLinkPointNode;\n    let downRightLinkPointNode;\n    let depedencyLink;\n\n    e.detailPath.find((pathNode: any) => {\n      if (pathNode.name === 'task-bar') {\n        // || pathNode.name === 'task-bar-hover-shadow';\n        downBarNode = pathNode;\n        return true;\n      } else if (pathNode.name === 'task-creation-button') {\n        downCreationButtomNode = pathNode;\n        return true;\n      } else if (pathNode.name === 'task-bar-link-point-left') {\n        // || pathNode.name === 'task-bar-hover-shadow';\n        downLeftLinkPointNode = pathNode;\n        return true;\n      } else if (pathNode.name === 'task-bar-link-point-right') {\n        // || pathNode.name === 'task-bar-hover-shadow';\n        downRightLinkPointNode = pathNode;\n        return true;\n      } else if (pathNode.attribute.vtable_link) {\n        downDependencyLineNode = pathNode;\n        depedencyLink = pathNode.attribute.vtable_link;\n        return true;\n      }\n      return false;\n    });\n    if (downBarNode) {\n      if (e.target.name === 'task-bar-hover-shadow-left-icon') {\n        stateManager.startResizeTaskBar(\n          downBarNode,\n          (e.nativeEvent as any).x,\n          (e.nativeEvent as any).y,\n          e.offset.y,\n          'left'\n        );\n        stateManager.updateInteractionState(InteractionState.grabing);\n      } else if (e.target.name === 'task-bar-hover-shadow-right-icon') {\n        stateManager.startResizeTaskBar(\n          downBarNode,\n          (e.nativeEvent as any).x,\n          (e.nativeEvent as any).y,\n          e.offset.y,\n          'right'\n        );\n        stateManager.updateInteractionState(InteractionState.grabing);\n      } else if (gantt.parsedOptions.taskBarMoveable) {\n        stateManager.startMoveTaskBar(downBarNode, (e.nativeEvent as any).x, (e.nativeEvent as any).y, e.offset.y);\n        stateManager.updateInteractionState(InteractionState.grabing);\n      }\n    } else if (downLeftLinkPointNode) {\n      stateManager.startCreateDependencyLine(\n        downLeftLinkPointNode,\n        (e.nativeEvent as any).x,\n        (e.nativeEvent as any).y,\n        e.offset.y,\n        'left'\n      );\n      stateManager.updateInteractionState(InteractionState.grabing);\n    } else if (downRightLinkPointNode) {\n      stateManager.startCreateDependencyLine(\n        downRightLinkPointNode,\n        (e.nativeEvent as any).x,\n        (e.nativeEvent as any).y,\n        e.offset.y,\n        'right'\n      );\n      stateManager.updateInteractionState(InteractionState.grabing);\n    }\n  });\n\n  scene.tableGroup.addEventListener('pointermove', (e: FederatedPointerEvent) => {\n    if (stateManager.interactionState === InteractionState.default) {\n      const taskBarTarget = e.detailPath.find((pathNode: any) => {\n        return pathNode.name === 'task-bar'; // || pathNode.name === 'task-bar-hover-shadow';\n      });\n      if (taskBarTarget) {\n        if (scene._gantt.stateManager.hoverTaskBar.target !== (taskBarTarget as any as GanttTaskBarNode)) {\n          scene._gantt.stateManager.hoverTaskBar.target = taskBarTarget as any as GanttTaskBarNode;\n          stateManager.showTaskBarHover();\n          if (scene._gantt.hasListeners(GANTT_EVENT_TYPE.MOUSEENTER_TASK_BAR)) {\n            // const taskIndex = getTaskIndexByY(e.offset.y, scene._gantt);\n            const taskIndex = taskBarTarget.task_index;\n            const sub_task_index = taskBarTarget.sub_task_index;\n            const record = scene._gantt.getRecordByIndex(taskIndex, sub_task_index);\n            scene._gantt.fireListeners(GANTT_EVENT_TYPE.MOUSEENTER_TASK_BAR, {\n              federatedEvent: e,\n              event: e.nativeEvent,\n              index: taskIndex,\n              sub_task_index,\n              record\n            });\n          }\n        }\n      } else {\n        if (scene._gantt.stateManager.hoverTaskBar.target) {\n          if (scene._gantt.hasListeners(GANTT_EVENT_TYPE.MOUSELEAVE_TASK_BAR)) {\n            // const taskIndex = getTaskIndexByY(e.offset.y, scene._gantt);\n            const taskIndex = scene._gantt.stateManager.hoverTaskBar.target.task_index;\n            const sub_task_index = scene._gantt.stateManager.hoverTaskBar.target.sub_task_index;\n            const record = scene._gantt.getRecordByIndex(taskIndex, sub_task_index);\n            scene._gantt.fireListeners(GANTT_EVENT_TYPE.MOUSELEAVE_TASK_BAR, {\n              federatedEvent: e,\n              event: e.nativeEvent,\n              index: taskIndex,\n              sub_task_index,\n              record\n            });\n          }\n          stateManager.hideTaskBarHover(e);\n        }\n        //#region hover到某一个任务 检查有没有日期安排，没有的话显示创建按钮\n        if (\n          gantt.parsedOptions.tasksShowMode !== TasksShowMode.Sub_Tasks_Inline &&\n          gantt.parsedOptions.tasksShowMode !== TasksShowMode.Sub_Tasks_Separate &&\n          gantt.parsedOptions.tasksShowMode !== TasksShowMode.Sub_Tasks_Arrange &&\n          gantt.parsedOptions.tasksShowMode !== TasksShowMode.Sub_Tasks_Compact &&\n          gantt.parsedOptions.taskBarCreatable\n        ) {\n          const taskIndex = getTaskIndexByY(e.offset.y, gantt);\n          const recordTaskInfo = gantt.getTaskInfoByTaskListIndex(taskIndex);\n          if (!recordTaskInfo.taskDays && recordTaskInfo.taskRecord && !recordTaskInfo.taskRecord.vtableMerge) {\n            const dateIndex = getDateIndexByX(e.offset.x, gantt);\n            const showX =\n              (dateIndex >= 1 ? gantt.getDateColsWidth(0, dateIndex - 1) : 0) -\n              gantt.stateManager.scroll.horizontalBarPos;\n            const showY = taskIndex * gantt.parsedOptions.rowHeight - gantt.stateManager.scroll.verticalBarPos;\n            //    -\n            // (gantt.stateManager.scroll.horizontalBarPos % gantt.parsedOptions.rowHeight);\n            // const date = getDateByX(e.offset.x, gantt);\n            gantt.scenegraph.showTaskCreationButton(showX, showY, dateIndex);\n            return;\n          }\n        }\n        //#endregion\n      }\n      gantt.scenegraph.hideTaskCreationButton();\n    } else if (stateManager.interactionState === InteractionState.grabing) {\n      let downBarNode;\n      let downCreationButtomNode;\n      let downDependencyLineNode;\n      let downLeftLinkPointNode;\n      let downRightLinkPointNode;\n      let depedencyLink;\n\n      e.detailPath.find((pathNode: any) => {\n        if (pathNode.name === 'task-bar') {\n          // || pathNode.name === 'task-bar-hover-shadow';\n          downBarNode = pathNode;\n          return true;\n        } else if (pathNode.name === 'task-creation-button') {\n          downCreationButtomNode = pathNode;\n          return true;\n        } else if (pathNode.name === 'task-bar-link-point-left') {\n          // || pathNode.name === 'task-bar-hover-shadow';\n          downLeftLinkPointNode = pathNode;\n          return true;\n        } else if (pathNode.name === 'task-bar-link-point-right') {\n          // || pathNode.name === 'task-bar-hover-shadow';\n          downRightLinkPointNode = pathNode;\n          return true;\n        } else if (pathNode.attribute.vtable_link) {\n          downDependencyLineNode = pathNode;\n          depedencyLink = pathNode.attribute.vtable_link;\n          return true;\n        }\n        return false;\n      });\n      downBarNode =\n        downBarNode ??\n        downLeftLinkPointNode?.parent?.attribute.attachedToTaskBarNode ??\n        downRightLinkPointNode?.parent?.attribute.attachedToTaskBarNode;\n      if (scene._gantt.stateManager.isCreatingDependencyLine() && !downBarNode) {\n        //如果正在创建依赖链，但是鼠标没有一定到目标taskBar上\n        stateManager.hideSecondTaskBarSelectedBorder();\n      } else if (\n        scene._gantt.stateManager.isCreatingDependencyLine() &&\n        downLeftLinkPointNode &&\n        scene._gantt.stateManager.selectedTaskBar.target !== (downBarNode as any as GanttTaskBarNode)\n      ) {\n        //如果正在创建依赖链，鼠标在另一个taskBar的左侧定位点上\n        // 这时候需要高亮左侧定位点\n        stateManager.highlightLinkPointNode(downLeftLinkPointNode);\n        stateManager.creatingDenpendencyLink.lastHighLightLinkPoint = downLeftLinkPointNode;\n        stateManager.creatingDenpendencyLink.secondTaskBarPosition = 'left';\n      } else if (\n        scene._gantt.stateManager.isCreatingDependencyLine() &&\n        downRightLinkPointNode &&\n        scene._gantt.stateManager.selectedTaskBar.target !== (downBarNode as any as GanttTaskBarNode)\n      ) {\n        //如果正在创建依赖链，鼠标在另一个taskBar的右侧定位点上\n        // 这时候需要高亮右侧定位点\n        stateManager.highlightLinkPointNode(downRightLinkPointNode);\n        stateManager.creatingDenpendencyLink.lastHighLightLinkPoint = downRightLinkPointNode;\n        stateManager.creatingDenpendencyLink.secondTaskBarPosition = 'right';\n      } else if (\n        scene._gantt.stateManager.isCreatingDependencyLine() &&\n        downBarNode &&\n        scene._gantt.stateManager.selectedTaskBar.target !== (downBarNode as any as GanttTaskBarNode)\n      ) {\n        // 如果正在创建依赖链，鼠标在另一个taskBar上\n        stateManager.unhighlightLinkPointNode(stateManager.creatingDenpendencyLink.lastHighLightLinkPoint);\n        if (\n          !stateManager.creatingDenpendencyLink.secondTaskBarNode ||\n          stateManager.creatingDenpendencyLink.secondTaskBarNode !== (downBarNode as any as GanttTaskBarNode)\n        ) {\n          if (\n            stateManager.creatingDenpendencyLink.secondTaskBarNode &&\n            stateManager.creatingDenpendencyLink.secondTaskBarNode !== (downBarNode as any as GanttTaskBarNode)\n          ) {\n            stateManager.hideSecondTaskBarSelectedBorder();\n          }\n          stateManager.creatingDenpendencyLink.secondTaskBarNode = downBarNode as any as GanttTaskBarNode;\n          stateManager.showSecondTaskBarSelectedBorder();\n        } else {\n        }\n      }\n    }\n  });\n  scene.tableGroup.addEventListener('pointerup', (e: FederatedPointerEvent) => {\n    let isClickBar = false;\n    let isClickCreationButtom = false;\n    let isClickDependencyLine = false;\n    let isClickLeftLinkPoint = false;\n    let isClickRightLinkPoint = false;\n    let depedencyLink;\n\n    const taskBarTarget = e.detailPath.find((pathNode: any) => {\n      if (pathNode.name === 'task-bar') {\n        // || pathNode.name === 'task-bar-hover-shadow';\n        isClickBar = true;\n        return true;\n      } else if (pathNode.name === 'task-creation-button') {\n        isClickCreationButtom = true;\n        return false;\n      } else if (pathNode.name === 'task-bar-link-point-left') {\n        // || pathNode.name === 'task-bar-hover-shadow';\n        isClickLeftLinkPoint = true;\n        return false;\n      } else if (pathNode.name === 'task-bar-link-point-right') {\n        // || pathNode.name === 'task-bar-hover-shadow';\n        isClickRightLinkPoint = true;\n        return false;\n      } else if (pathNode.attribute.vtable_link) {\n        isClickDependencyLine = true;\n        depedencyLink = pathNode.attribute.vtable_link;\n        return false;\n      }\n      return false;\n    });\n    if (isClickBar && scene._gantt.parsedOptions.taskBarSelectable && event.poniterState === 'down') {\n      stateManager.hideDependencyLinkSelectedLine();\n      stateManager.showTaskBarSelectedBorder(taskBarTarget);\n      if (gantt.hasListeners(GANTT_EVENT_TYPE.CLICK_TASK_BAR)) {\n        // const taskIndex = getTaskIndexByY(e.offset.y, gantt);\n        const taskIndex = taskBarTarget.task_index;\n        const sub_task_index = taskBarTarget.sub_task_index;\n        const record = gantt.getRecordByIndex(taskIndex, sub_task_index);\n        gantt.fireListeners(GANTT_EVENT_TYPE.CLICK_TASK_BAR, {\n          federatedEvent: e,\n          event: e.nativeEvent,\n          index: taskIndex,\n          sub_task_index,\n          record\n        });\n      }\n    } else if (isClickCreationButtom && event.poniterState === 'down') {\n      stateManager.hideDependencyLinkSelectedLine();\n      stateManager.hideTaskBarSelectedBorder();\n      const taskIndex = getTaskIndexByY(e.offset.y, gantt);\n      const recordTaskInfo = gantt.getTaskInfoByTaskListIndex(taskIndex);\n      if (recordTaskInfo.taskRecord) {\n        // const minTimeUnit = gantt.parsedOptions.reverseSortedTimelineScales[0].unit;\n        const dateFormat =\n          gantt.parsedOptions.dateFormat ??\n          (gantt.parsedOptions.timeScaleIncludeHour ? 'yyyy-mm-dd hh:mm:ss' : 'yyyy-mm-dd');\n        const dateIndex = getDateIndexByX(e.offset.x, gantt);\n        const dateRange = gantt.getDateRangeByIndex(dateIndex);\n        recordTaskInfo.taskRecord[gantt.parsedOptions.startDateField] = formatDate(dateRange.startDate, dateFormat);\n        recordTaskInfo.taskRecord[gantt.parsedOptions.endDateField] = formatDate(dateRange.endDate, dateFormat);\n\n        gantt.scenegraph.hideTaskCreationButton();\n        gantt.updateTaskRecord(recordTaskInfo.taskRecord, taskIndex);\n        if (gantt.hasListeners(GANTT_EVENT_TYPE.CREATE_TASK_SCHEDULE)) {\n          gantt.fireListeners(GANTT_EVENT_TYPE.CREATE_TASK_SCHEDULE, {\n            federatedEvent: e,\n            event: e.nativeEvent,\n            index: taskIndex,\n            startDate: recordTaskInfo.taskRecord[gantt.parsedOptions.startDateField],\n            endDate: recordTaskInfo.taskRecord[gantt.parsedOptions.endDateField],\n            record: recordTaskInfo.taskRecord\n          });\n        }\n      }\n    } else if (\n      isClickDependencyLine &&\n      scene._gantt.parsedOptions.dependencyLinkSelectable &&\n      event.poniterState === 'down'\n    ) {\n      stateManager.hideDependencyLinkSelectedLine();\n      stateManager.hideTaskBarSelectedBorder();\n      scene._gantt.stateManager.selectedDenpendencyLink.link = depedencyLink;\n      stateManager.showDependencyLinkSelectedLine();\n    } else if ((isClickLeftLinkPoint || isClickRightLinkPoint) && event.poniterState === 'down') {\n      if (gantt.hasListeners(GANTT_EVENT_TYPE.CLICK_DEPENDENCY_LINK_POINT)) {\n        const taskIndex = getTaskIndexByY(e.offset.y, gantt);\n        const record = gantt.getRecordByIndex(taskIndex);\n        gantt.fireListeners(GANTT_EVENT_TYPE.CLICK_DEPENDENCY_LINK_POINT, {\n          event: e.nativeEvent,\n          index: taskIndex,\n          point: isClickLeftLinkPoint ? 'start' : 'end',\n          record\n        });\n      }\n      stateManager.hideTaskBarSelectedBorder();\n    } else if (isClickLeftLinkPoint && event.poniterState === 'draging') {\n      if (stateManager.isCreatingDependencyLine()) {\n        const link = stateManager.endCreateDependencyLine(e.offset.y);\n        if (gantt.hasListeners(GANTT_EVENT_TYPE.CREATE_DEPENDENCY_LINK)) {\n          gantt.fireListeners(GANTT_EVENT_TYPE.CREATE_DEPENDENCY_LINK, {\n            federatedEvent: e,\n            event: e.nativeEvent,\n            link\n          });\n        }\n      }\n    } else if (isClickRightLinkPoint && event.poniterState === 'draging') {\n      if (stateManager.isCreatingDependencyLine()) {\n        const link = stateManager.endCreateDependencyLine(e.offset.y);\n        if (gantt.hasListeners(GANTT_EVENT_TYPE.CREATE_DEPENDENCY_LINK)) {\n          gantt.fireListeners(GANTT_EVENT_TYPE.CREATE_DEPENDENCY_LINK, {\n            federatedEvent: e,\n            event: e.nativeEvent,\n            link\n          });\n        }\n      }\n    } else {\n      stateManager.hideDependencyLinkSelectedLine();\n      stateManager.hideTaskBarSelectedBorder();\n    }\n  });\n\n  scene.tableGroup.addEventListener('pointerenter', (e: FederatedPointerEvent) => {\n    if (\n      (gantt.parsedOptions.scrollStyle.horizontalVisible &&\n        gantt.parsedOptions.scrollStyle.horizontalVisible === 'focus') ||\n      (!gantt.parsedOptions.scrollStyle.horizontalVisible && gantt.parsedOptions.scrollStyle.visible === 'focus')\n    ) {\n      scene.scrollbarComponent.showHorizontalScrollBar();\n    }\n    if (\n      (gantt.parsedOptions.scrollStyle.verticalVisible &&\n        gantt.parsedOptions.scrollStyle.verticalVisible === 'focus') ||\n      (!gantt.parsedOptions.scrollStyle.verticalVisible && gantt.parsedOptions.scrollStyle.visible === 'focus')\n    ) {\n      scene.scrollbarComponent.showVerticalScrollBar();\n    }\n  });\n\n  scene.tableGroup.addEventListener('pointerleave', (e: FederatedPointerEvent) => {\n    if (\n      (gantt.parsedOptions.scrollStyle.horizontalVisible &&\n        gantt.parsedOptions.scrollStyle.horizontalVisible === 'focus') ||\n      (!gantt.parsedOptions.scrollStyle.horizontalVisible && gantt.parsedOptions.scrollStyle.visible === 'focus')\n    ) {\n      scene.scrollbarComponent.hideHorizontalScrollBar();\n    }\n    if (\n      (gantt.parsedOptions.scrollStyle.verticalVisible &&\n        gantt.parsedOptions.scrollStyle.verticalVisible === 'focus') ||\n      (!gantt.parsedOptions.scrollStyle.verticalVisible && gantt.parsedOptions.scrollStyle.visible === 'focus')\n    ) {\n      scene.scrollbarComponent.hideVerticalScrollBar();\n    }\n  });\n}\n\nfunction bindContainerDomListener(eventManager: EventManager) {\n  const gantt = eventManager._gantt;\n  const scene = eventManager._gantt.scenegraph;\n  const stateManager = gantt.stateManager;\n  const handler = eventManager._eventHandler;\n  handler.on(gantt.getElement(), 'wheel', (e: WheelEvent) => {\n    handleWhell(e, stateManager, eventManager._gantt);\n  });\n\n  handler.on(gantt.getContainer(), 'resize', (e: any) => {\n    // if (table.canvasSizeSeted) {\n    //   return;\n    // }\n    if (e.width === 0 && e.height === 0) {\n      // 临时绕行解决因为display设置为none产生的问题\n      return;\n    }\n    if (!isValid(gantt.options.pixelRatio)) {\n      gantt.setPixelRatio(getPixelRatio());\n    }\n    if (!e.windowSizeNotChange) {\n      gantt._resize();\n    }\n  });\n  if (gantt.taskListTableInstance && gantt.parsedOptions.verticalSplitLineMoveable) {\n    handler.on(gantt.verticalSplitResizeLine, 'mousedown', (e: MouseEvent) => {\n      console.log('resizeLine mousedown');\n      stateManager.updateInteractionState(InteractionState.grabing);\n      stateManager.startResizeTableWidth(e);\n    });\n  }\n  if (gantt.parsedOptions.verticalSplitLineHighlight) {\n    // 添加鼠标悬停时的高亮效果\n    gantt.verticalSplitResizeLine &&\n      handler.on(gantt.verticalSplitResizeLine, 'mouseover', (e: MouseEvent) => {\n        (gantt.verticalSplitResizeLine.childNodes[1] as HTMLDivElement).style.opacity = '1';\n      });\n\n    // 添加鼠标移出时恢复初始样式\n    gantt.verticalSplitResizeLine &&\n      handler.on(gantt.verticalSplitResizeLine, 'mouseout', (e: MouseEvent) => {\n        (gantt.verticalSplitResizeLine.childNodes[1] as HTMLDivElement).style.opacity = '0';\n      });\n  }\n  const globalMousedownCallback = (e: FederatedPointerEvent) => {\n    gantt.eventManager.lastDragPointerXYOnWindow = { x: e.x, y: e.y };\n    gantt.eventManager.poniterState = 'down';\n  };\n  eventManager.globalEventListeners.push({\n    name: 'mousedown',\n    env: 'document',\n    callback: globalMousedownCallback\n  });\n  vglobal.addEventListener('mousedown', globalMousedownCallback);\n  const globalMousemoveCallback = (e: FederatedPointerEvent) => {\n    if (gantt.eventManager.poniterState === 'down') {\n      const x1 = gantt.eventManager.lastDragPointerXYOnWindow.x ?? e.x;\n      const x2 = e.x;\n      const dx = x2 - x1;\n      const y1 = gantt.eventManager.lastDragPointerXYOnWindow.y ?? e.y;\n      const y2 = e.y;\n      const dy = y2 - y1;\n      if (Math.abs(dx) >= 1 || Math.abs(dy) >= 1) {\n        gantt.eventManager.poniterState = 'draging';\n      }\n    }\n    if (stateManager.interactionState === InteractionState.grabing && gantt.eventManager.poniterState === 'draging') {\n      const lastX = gantt.eventManager.lastDragPointerXYOnWindow?.x ?? e.x;\n      const lastY = gantt.eventManager.lastDragPointerXYOnWindow?.y ?? e.y;\n      if (Math.abs(lastX - e.x) >= 1 || Math.abs(lastY - e.y) >= 1) {\n        if (stateManager.isResizingTableWidth()) {\n          stateManager.hideDependencyLinkSelectedLine();\n          stateManager.hideTaskBarSelectedBorder();\n          stateManager.dealResizeTableWidth(e);\n        } else if (stateManager.isMoveingTaskBar()) {\n          stateManager.hideDependencyLinkSelectedLine();\n          stateManager.hideTaskBarSelectedBorder();\n          stateManager.dealTaskBarMove(e);\n        } else if (stateManager.isResizingTaskBar()) {\n          stateManager.hideDependencyLinkSelectedLine();\n          stateManager.hideTaskBarSelectedBorder();\n          stateManager.dealTaskBarResize(e);\n        } else if (stateManager.isCreatingDependencyLine()) {\n          // stateManager.hideDependencyLinkSelectedLine();\n          stateManager.dealCreateDependencyLine(e);\n        }\n        gantt.eventManager.lastDragPointerXYOnWindow = { x: e.x, y: e.y };\n      }\n    }\n  };\n  eventManager.globalEventListeners.push({\n    name: 'mousemove',\n    env: 'document',\n    callback: globalMousemoveCallback\n  });\n  vglobal.addEventListener('mousemove', globalMousemoveCallback);\n  const globalMouseupCallback = (e: MouseEvent) => {\n    if (stateManager.interactionState === 'grabing') {\n      stateManager.updateInteractionState(InteractionState.default);\n      if (stateManager.isResizingTableWidth()) {\n        stateManager.endResizeTableWidth();\n      } else if (stateManager.isMoveingTaskBar()) {\n        stateManager.endMoveTaskBar();\n      } else if (stateManager.isResizingTaskBar()) {\n        stateManager.endResizeTaskBar(e.x);\n      }\n    }\n    gantt.eventManager.lastDragPointerXYOnWindow = undefined;\n    gantt.eventManager.poniterState = 'up';\n  };\n  eventManager.globalEventListeners.push({\n    name: 'mouseup',\n    env: 'document',\n    callback: globalMouseupCallback\n  });\n  vglobal.addEventListener('mouseup', globalMouseupCallback);\n}\n", "import type { Gantt } from '../Gantt';\nimport { updateSplitLineAndResizeLine } from '../gantt-helper';\nimport { TasksShowMode } from '../ts-types';\n\nexport function syncScrollStateToTable(gantt: Gantt) {\n  const { scroll } = gantt.stateManager;\n  const { verticalBarPos } = scroll;\n  gantt.taskListTableInstance.stateManager.setScrollTop(verticalBarPos, undefined, false);\n}\n\nexport function syncScrollStateFromTable(gantt: Gantt) {\n  if (gantt.taskListTableInstance) {\n    gantt.taskListTableInstance?.on('scroll', (args: any) => {\n      if (args.scrollDirection === 'vertical') {\n        const { scroll } = gantt.taskListTableInstance.stateManager;\n        const { verticalBarPos } = scroll;\n        gantt.stateManager.setScrollTop(verticalBarPos, false);\n      }\n    });\n  }\n}\nexport function syncEditCellFromTable(gantt: Gantt) {\n  gantt.taskListTableInstance?.on('change_cell_value', (args: any) => {\n    const { col, row, rawValue, changedValue } = args;\n    gantt._refreshTaskBar(row - gantt.taskListTableInstance.columnHeaderLevelCount);\n    // const record = gantt.getRecordByIndex(row - gantt.taskListTableInstance.columnHeaderLevelCount);\n    // debugger;\n  });\n}\n\nexport function syncTreeChangeFromTable(gantt: Gantt) {\n  gantt.taskListTableInstance?.on('tree_hierarchy_state_change', (args: any) => {\n    gantt._syncPropsFromTable();\n\n    gantt.scenegraph.refreshTaskBarsAndGrid();\n    const left = gantt.stateManager.scroll.horizontalBarPos;\n    const top = gantt.stateManager.scroll.verticalBarPos;\n    gantt.scenegraph.setX(-left);\n    gantt.scenegraph.setY(-top);\n  });\n}\nexport function syncSortFromTable(gantt: Gantt) {\n  gantt.taskListTableInstance?.on('after_sort', (args: any) => {\n    gantt.scenegraph.refreshTaskBars();\n    const left = gantt.stateManager.scroll.horizontalBarPos;\n    const top = gantt.stateManager.scroll.verticalBarPos;\n    gantt.scenegraph.setX(-left);\n    gantt.scenegraph.setY(-top);\n  });\n}\nexport function syncDragOrderFromTable(gantt: Gantt) {\n  gantt.taskListTableInstance?.on('change_header_position', (args: any) => {\n    if (\n      gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n      gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact ||\n      gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate\n    ) {\n      gantt.scenegraph.refreshTaskBarsAndGrid();\n    } else {\n      gantt.scenegraph.refreshTaskBars();\n    }\n    gantt.scenegraph.dragOrderLine.hideDragLine();\n    const left = gantt.stateManager.scroll.horizontalBarPos;\n    const top = gantt.stateManager.scroll.verticalBarPos;\n    gantt.scenegraph.setX(-left);\n    gantt.scenegraph.setY(-top);\n  });\n  gantt.taskListTableInstance?.on('change_header_position_start', (args: any) => {\n    const { col, row, x, y, backX, lineX, backY, lineY, event } = args;\n\n    gantt.scenegraph.dragOrderLine.showDragLine(lineY);\n    gantt.scenegraph.updateNextFrame();\n  });\n  gantt.taskListTableInstance?.on('changing_header_position', (args: any) => {\n    const { col, row, x, y, backX, lineX, backY, lineY, event } = args;\n    gantt.scenegraph.dragOrderLine.showDragLine(lineY);\n    gantt.scenegraph.updateNextFrame();\n  });\n  gantt.taskListTableInstance?.on('change_header_position_fail', (args: any) => {\n    gantt.scenegraph.dragOrderLine.hideDragLine();\n    gantt.scenegraph.updateNextFrame();\n  });\n}\n\nexport function syncTableWidthFromTable(gantt: Gantt) {\n  gantt.taskListTableInstance?.on('resize_column', (args: any) => {\n    const oldTaskTableWidth: number = gantt.taskTableWidth;\n\n    gantt.taskTableWidth =\n      gantt.taskListTableInstance.getAllColsWidth() + gantt.parsedOptions.outerFrameStyle.borderLineWidth;\n    if (gantt.options?.taskListTable?.maxTableWidth) {\n      gantt.taskTableWidth = Math.min(gantt.options?.taskListTable?.maxTableWidth, gantt.taskTableWidth);\n    }\n    if (gantt.options?.taskListTable?.minTableWidth) {\n      gantt.taskTableWidth = Math.max(gantt.options?.taskListTable?.minTableWidth, gantt.taskTableWidth);\n    }\n    if (oldTaskTableWidth === gantt.taskTableWidth) {\n      return;\n    }\n    gantt.element.style.left = gantt.taskTableWidth ? `${gantt.taskTableWidth}px` : '0px';\n    gantt.taskListTableInstance.setCanvasSize(\n      gantt.taskTableWidth,\n      gantt.tableNoFrameHeight + gantt.parsedOptions.outerFrameStyle.borderLineWidth * 2\n    );\n    gantt._updateSize();\n    updateSplitLineAndResizeLine(gantt);\n  });\n}\n", "export class Inertia {\n    constructor() {}\n    setScrollHandle(scrollHandle) {\n        this.scrollHandle = scrollHandle;\n    }\n    startInertia(speedX, speedY, friction) {\n        this.stopped = !1, this.lastTime = Date.now(), this.speedX = speedX, this.speedY = speedY, \n        this.friction = friction, this.runingId || (this.runingId = requestAnimationFrame(this.inertia.bind(this)));\n    }\n    inertia() {\n        var _a;\n        if (this.stopped) return;\n        const now = Date.now(), dffTime = now - this.lastTime;\n        let stopped = !0;\n        const f = Math.pow(this.friction, dffTime / 16), newSpeedX = f * this.speedX, newSpeedY = f * this.speedY;\n        let dx = 0, dy = 0;\n        Math.abs(newSpeedX) > .05 && (stopped = !1, dx = (this.speedX + newSpeedX) / 2 * dffTime), \n        Math.abs(newSpeedY) > .05 && (stopped = !1, dy = (this.speedY + newSpeedY) / 2 * dffTime), \n        null === (_a = this.scrollHandle) || void 0 === _a || _a.call(this, dx, dy), stopped ? this.runingId = null : (this.lastTime = now, \n        this.speedX = newSpeedX, this.speedY = newSpeedY, this.runingId = requestAnimationFrame(this.inertia.bind(this)));\n    }\n    endInertia() {\n        cancelAnimationFrame(this.runingId), this.runingId = null, this.stopped = !0;\n    }\n    isInertiaScrolling() {\n        return !!this.runingId;\n    }\n}", "import { clone, cloneDeep, isValid } from '@visactor/vutils';\nimport type { Gantt } from '../Gantt';\nimport type { ITaskLink } from '../ts-types';\nimport { InteractionState, GANTT_EVENT_TYPE, DependencyType, TasksShowMode } from '../ts-types';\nimport type { Group, FederatedPointerEvent, Polygon, Line, Circle } from '@visactor/vtable/es/vrender';\nimport {\n  syncEditCellFromTable,\n  syncScrollStateFromTable,\n  syncScrollStateToTable,\n  syncDragOrderFromTable,\n  syncTreeChangeFromTable,\n  syncSortFromTable,\n  syncTableWidthFromTable\n} from './gantt-table-sync';\nimport { clearRecordShowIndex, findRecordByTaskKey, getDateIndexByX, getTaskIndexsByTaskY } from '../gantt-helper';\nimport { debounce } from '../tools/debounce';\nimport type { GanttTaskBarNode } from '../scenegraph/gantt-node';\nimport { TASKBAR_HOVER_ICON_WIDTH } from '../scenegraph/task-bar';\nimport { Inertia } from '../tools/inertia';\nimport { createDateAtMidnight } from '../tools/util';\nexport class StateManager {\n  _gantt: Gantt;\n\n  scroll: {\n    horizontalBarPos: number;\n    verticalBarPos: number;\n  };\n  fastScrolling: boolean;\n  /**\n   * Default 默认展示\n   * grabing 拖拽中\n   *   -Resize column 改变列宽\n   *   -column move 调整列顺序\n   *   -drag select 拖拽多选\n   * Scrolling 滚动中\n   */\n  interactionState: InteractionState = InteractionState.default;\n\n  moveTaskBar: {\n    /** x坐标是相对table内坐标 */\n    startX: number;\n    startY: number;\n    deltaX: number;\n    deltaY: number;\n    targetStartX: number;\n    targetStartY: number;\n    startOffsetY: number;\n    moving: boolean;\n    target: GanttTaskBarNode;\n    moveTaskBarXSpeed: number;\n    moveTaskBarXInertia: Inertia;\n  };\n\n  hoverTaskBar: {\n    /** x坐标是相对table内坐标 */\n    startX: number;\n    targetStartX: number;\n    target: GanttTaskBarNode;\n  };\n  resizeTaskBar: {\n    /** x坐标是相对table内坐标 */\n    startX: number;\n    startY: number;\n    /** 刚开始时 任务条节点的offsetX */\n    startOffsetY: number;\n    targetStartX: number;\n    targetEndX: number;\n    target: GanttTaskBarNode;\n    resizing: boolean;\n    onIconName: string;\n  };\n  selectedTaskBar: {\n    target: GanttTaskBarNode;\n  };\n  resizeTableWidth: {\n    /** x坐标是相对table内坐标 */\n    lastX: number;\n    resizing: boolean;\n  };\n\n  selectedDenpendencyLink: {\n    link: ITaskLink & { vtable_gantt_linkArrowNode: Polygon; vtable_gantt_linkLineNode: Line };\n  };\n  creatingDenpendencyLink: {\n    /** x坐标是相对table内坐标 */\n    startX: number;\n    startY: number;\n    startOffsetY: number;\n    targetStartX: number;\n    startClickedPoint: Group;\n    creating: boolean;\n    firstTaskBarPosition: 'left' | 'right';\n    secondTaskBarPosition: 'left' | 'right';\n    secondTaskBarNode: GanttTaskBarNode;\n    lastHighLightLinkPoint: Group;\n  };\n  // 供滚动重置为default使用\n  resetInteractionState = debounce(() => {\n    this.updateInteractionState(InteractionState.default);\n  }, 100);\n  constructor(gantt: Gantt) {\n    this._gantt = gantt;\n    this.scroll = {\n      horizontalBarPos: 0,\n      verticalBarPos: 0\n    };\n    this.moveTaskBar = {\n      targetStartX: null,\n      targetStartY: null,\n      deltaX: 0,\n      deltaY: 0,\n      startOffsetY: null,\n      startX: null,\n      startY: null,\n      moving: false,\n      target: null,\n      moveTaskBarXSpeed: 0,\n      moveTaskBarXInertia: new Inertia()\n    };\n\n    this.hoverTaskBar = {\n      targetStartX: null,\n      startX: null,\n      target: null\n    };\n\n    this.selectedTaskBar = {\n      target: null\n    };\n    this.resizeTaskBar = {\n      startOffsetY: null,\n      targetStartX: null,\n      targetEndX: null,\n      startX: null,\n      startY: null,\n      target: null,\n      resizing: false,\n      onIconName: ''\n    };\n    this.resizeTableWidth = {\n      lastX: null,\n      resizing: false\n    };\n    this.selectedDenpendencyLink = {\n      link: null\n    };\n    this.creatingDenpendencyLink = {\n      startClickedPoint: null,\n      startX: null,\n      startY: null,\n      startOffsetY: null,\n      targetStartX: null,\n      creating: false,\n      secondTaskBarNode: null\n    };\n\n    this.updateVerticalScrollBar = this.updateVerticalScrollBar.bind(this);\n    this.updateHorizontalScrollBar = this.updateHorizontalScrollBar.bind(this);\n\n    syncScrollStateFromTable(this._gantt);\n    syncEditCellFromTable(this._gantt);\n    syncDragOrderFromTable(this._gantt);\n    syncTreeChangeFromTable(this._gantt);\n    syncSortFromTable(this._gantt);\n    if (this._gantt.options.taskListTable?.tableWidth === 'auto' || this._gantt.taskTableWidth === -1) {\n      syncTableWidthFromTable(this._gantt);\n    }\n  }\n\n  setScrollTop(top: number, triggerEvent: boolean = true) {\n    // 矫正top值范围\n    const totalHeight = this._gantt.getAllRowsHeight();\n    top = Math.max(0, Math.min(top, totalHeight - this._gantt.scenegraph.height));\n    top = Math.ceil(top);\n    const oldVerticalBarPos = this.scroll.verticalBarPos;\n    // this._gantt.stateManager.updateSelectPos(-1, -1);\n    this.scroll.verticalBarPos = top;\n    if (!isValid(this.scroll.verticalBarPos) || isNaN(this.scroll.verticalBarPos)) {\n      this.scroll.verticalBarPos = 0;\n    }\n    // 设置scenegraph坐标\n    this._gantt.scenegraph.setY(-top);\n\n    // 更新scrollbar位置\n    const yRatio = top / (totalHeight - this._gantt.scenegraph.height);\n    this._gantt.scenegraph.scrollbarComponent.updateVerticalScrollBarPos(yRatio);\n\n    if (oldVerticalBarPos !== top && triggerEvent) {\n      syncScrollStateToTable(this._gantt);\n      this._gantt.fireListeners(GANTT_EVENT_TYPE.SCROLL, {\n        scrollTop: this.scroll.verticalBarPos,\n        scrollLeft: this.scroll.horizontalBarPos,\n        // scrollHeight: this._gantt.theme.scrollStyle?.width,\n        // scrollWidth: this._gantt.theme.scrollStyle?.width,\n        // viewHeight: this._gantt.tableNoFrameHeight,\n        // viewWidth: this._gantt.tableNoFrameWidth,\n        scrollDirection: 'vertical',\n        scrollRatioY: yRatio\n      });\n    }\n  }\n  get scrollLeft() {\n    return this.scroll.horizontalBarPos;\n  }\n  get scrollTop() {\n    return this.scroll.verticalBarPos;\n  }\n  setScrollLeft(left: number, triggerEvent: boolean = true) {\n    // 矫正left值范围\n    const totalWidth = this._gantt.getAllDateColsWidth();\n\n    left = Math.max(0, Math.min(left, totalWidth - this._gantt.scenegraph.width));\n    left = Math.ceil(left);\n    // 滚动期间清空选中清空\n    // if (left !== this.scroll.horizontalBarPos) {\n    //   this.updateHoverPos(-1, -1);\n    // }\n    // this._gantt.stateManager.updateSelectPos(-1, -1);\n    const oldHorizontalBarPos = this.scroll.horizontalBarPos;\n    this.scroll.horizontalBarPos = left;\n    if (!isValid(this.scroll.horizontalBarPos) || isNaN(this.scroll.horizontalBarPos)) {\n      this.scroll.horizontalBarPos = 0;\n    }\n\n    // 设置scenegraph坐标\n    this._gantt.scenegraph.setX(-left);\n\n    // 更新scrollbar位置\n    const xRatio = left / (totalWidth - this._gantt.scenegraph.width);\n    this._gantt.scenegraph.scrollbarComponent.updateHorizontalScrollBarPos(xRatio);\n\n    if (oldHorizontalBarPos !== left && triggerEvent) {\n      this._gantt.fireListeners(GANTT_EVENT_TYPE.SCROLL, {\n        scrollTop: this.scroll.verticalBarPos,\n        scrollLeft: this.scroll.horizontalBarPos,\n        // scrollHeight: this._gantt.theme.scrollStyle?.width,\n        // scrollWidth: this._gantt.theme.scrollStyle?.width,\n        // viewHeight: this._gantt.tableNoFrameHeight,\n        // viewWidth: this._gantt.tableNoFrameWidth,\n        scrollDirection: 'horizontal',\n        scrollRatioX: xRatio\n      });\n    }\n  }\n\n  updateInteractionState(mode: InteractionState) {\n    if (this.interactionState === mode) {\n      return;\n    }\n    const oldState = this.interactionState;\n    this.interactionState = mode;\n    // 处理mode 更新后逻辑\n    if (oldState === InteractionState.scrolling && mode === InteractionState.default) {\n      // this.table.scenegraph.stage.disableDirtyBounds();\n      // this.table.scenegraph.stage.render();\n      // this.table.scenegraph.stage.enableDirtyBounds();\n    }\n  }\n\n  updateVerticalScrollBar(yRatio: number) {\n    const totalHeight = this._gantt.getAllRowsHeight();\n    const oldVerticalBarPos = this.scroll.verticalBarPos;\n    this.scroll.verticalBarPos = Math.ceil(yRatio * (totalHeight - this._gantt.scenegraph.height));\n    if (!isValid(this.scroll.verticalBarPos) || isNaN(this.scroll.verticalBarPos)) {\n      this.scroll.verticalBarPos = 0;\n    }\n    this._gantt.scenegraph.setY(-this.scroll.verticalBarPos, yRatio === 1);\n    syncScrollStateToTable(this._gantt);\n    this._gantt.fireListeners(GANTT_EVENT_TYPE.SCROLL, {\n      scrollTop: this.scroll.verticalBarPos,\n      scrollLeft: this.scroll.horizontalBarPos,\n      // scrollHeight: this.table.theme.scrollStyle?.width,\n      // scrollWidth: this.table.theme.scrollStyle?.width,\n      // viewHeight: this.table.tableNoFrameHeight,\n      // viewWidth: this.table.tableNoFrameWidth,\n      scrollDirection: 'vertical',\n      scrollRatioY: yRatio\n    });\n  }\n  updateHorizontalScrollBar(xRatio: number) {\n    const totalWidth = this._gantt.getAllDateColsWidth();\n    const oldHorizontalBarPos = this.scroll.horizontalBarPos;\n    this.scroll.horizontalBarPos = Math.ceil(xRatio * (totalWidth - this._gantt.scenegraph.width));\n    if (!isValid(this.scroll.horizontalBarPos) || isNaN(this.scroll.horizontalBarPos)) {\n      this.scroll.horizontalBarPos = 0;\n    }\n    this._gantt.scenegraph.setX(-this.scroll.horizontalBarPos, xRatio === 1);\n    this._gantt.fireListeners(GANTT_EVENT_TYPE.SCROLL, {\n      scrollTop: this.scroll.verticalBarPos,\n      scrollLeft: this.scroll.horizontalBarPos,\n      // scrollHeight: this.table.theme.scrollStyle?.width,\n      // scrollWidth: this.table.theme.scrollStyle?.width,\n      // viewHeight: this.table.tableNoFrameHeight,\n      // viewWidth: this.table.tableNoFrameWidth,\n      scrollDirection: 'horizontal',\n      scrollRatioY: xRatio\n    });\n    // this.scroll.horizontalBarPos -= this._gantt.scenegraph.proxy.deltaX;\n    // this._gantt.scenegraph.proxy.deltaX = 0;\n\n    // this._gantt.fireListeners(TABLE_EVENT_TYPE.SCROLL, {\n    //   scrollTop: this.scroll.verticalBarPos,\n    //   scrollLeft: this.scroll.horizontalBarPos,\n    //   scrollHeight: this._gantt.theme.scrollStyle?.width,\n    //   scrollWidth: this._gantt.theme.scrollStyle?.width,\n    //   viewHeight: this._gantt.tableNoFrameHeight,\n    //   viewWidth: this._gantt.tableNoFrameWidth,\n    //   scrollDirection: 'horizontal',\n    //   scrollRatioX: xRatio\n    // });\n\n    // if (oldHorizontalBarPos !== this.scroll.horizontalBarPos) {\n    //   this.checkHorizontalScrollBarEnd();\n    // }\n  }\n\n  startMoveTaskBar(target: GanttTaskBarNode, x: number, y: number, offsetY: number) {\n    if (target.name === 'task-bar-hover-shadow') {\n      target = target.parent;\n    }\n    this.moveTaskBar.moving = true;\n    this.moveTaskBar.target = target;\n    this.moveTaskBar.targetStartX = target.attribute.x;\n    this.moveTaskBar.targetStartY = target.attribute.y;\n    this.moveTaskBar.startX = x;\n    this.moveTaskBar.startY = y;\n    this.moveTaskBar.startOffsetY = offsetY;\n    target.setAttribute('zIndex', 10000);\n  }\n\n  isMoveingTaskBar() {\n    return this.moveTaskBar.moving;\n  }\n  endMoveTaskBar() {\n    if (this.moveTaskBar.moveTaskBarXInertia.isInertiaScrolling()) {\n      this.moveTaskBar.moveTaskBarXInertia.endInertia();\n    }\n\n    const deltaX = this.moveTaskBar.deltaX;\n    const deltaY = this.moveTaskBar.deltaY;\n    const target = this.moveTaskBar.target;\n    if (Math.abs(deltaX) >= 1 || Math.abs(deltaY) >= 1) {\n      const taskIndex = target.task_index;\n      const sub_task_index = target.sub_task_index;\n      const { startDate: oldStartDate, endDate: oldEndDate } = this._gantt.getTaskInfoByTaskListIndex(\n        taskIndex,\n        sub_task_index\n      );\n\n      const targetEndY =\n        this.moveTaskBar.targetStartY +\n        this._gantt.parsedOptions.rowHeight * Math.round(deltaY / this._gantt.parsedOptions.rowHeight);\n\n      const startDateColIndex = getDateIndexByX(\n        this.moveTaskBar.target.attribute.x - this._gantt.stateManager.scroll.horizontalBarPos,\n        this._gantt\n      );\n      const timelineStartDate =\n        this._gantt.parsedOptions.reverseSortedTimelineScales[0].timelineDates[startDateColIndex];\n      const newStartDate = timelineStartDate.startDate;\n      // const endDateColIndex = getDateIndexByX(\n      //   this.moveTaskBar.target.attribute.x +\n      //     this.moveTaskBar.target.attribute.width -\n      //     this._gantt.stateManager.scroll.horizontalBarPos,\n      //   this._gantt\n      // );\n      // const timelineEndDate = this._gantt.parsedOptions.reverseSortedTimelineScales[0].timelineDates[endDateColIndex];\n      const newEndDate = new Date(\n        newStartDate.getTime() +\n          (createDateAtMidnight(oldEndDate).getTime() - createDateAtMidnight(oldStartDate).getTime())\n      );\n      // 判断横向拖动 更新数据的date\n      let dateChanged: 'left' | 'right';\n      if (createDateAtMidnight(oldStartDate).getTime() !== newStartDate.getTime()) {\n        dateChanged = createDateAtMidnight(oldStartDate).getTime() > newStartDate.getTime() ? 'left' : 'right';\n        // this._gantt._updateDateToTaskRecord('move', days, taskIndex, sub_task_index);\n        this._gantt._updateStartEndDateToTaskRecord(newStartDate, newEndDate, taskIndex, sub_task_index);\n        const newRecord = this._gantt.getRecordByIndex(taskIndex, sub_task_index);\n\n        if (this._gantt.hasListeners(GANTT_EVENT_TYPE.CHANGE_DATE_RANGE)) {\n          this._gantt.fireListeners(GANTT_EVENT_TYPE.CHANGE_DATE_RANGE, {\n            startDate: newRecord[this._gantt.parsedOptions.startDateField],\n            endDate: newRecord[this._gantt.parsedOptions.endDateField],\n            oldStartDate,\n            oldEndDate,\n            index: taskIndex,\n            record: newRecord\n          });\n        }\n      }\n      if (\n        this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n        this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n      ) {\n        const indexs = getTaskIndexsByTaskY(targetEndY, this._gantt);\n        this._gantt._dragOrderTaskRecord(\n          target.task_index,\n          target.sub_task_index,\n          indexs.task_index,\n          indexs.sub_task_index\n        );\n        clearRecordShowIndex(this._gantt.records);\n        this._gantt.taskListTableInstance.renderWithRecreateCells();\n        this._gantt._syncPropsFromTable();\n        this._gantt.scenegraph.refreshTaskBarsAndGrid();\n      } else {\n        // 判断纵向拖动 处理数据的位置\n        if (\n          this._gantt.parsedOptions.tasksShowMode !== TasksShowMode.Tasks_Separate &&\n          Math.abs(Math.round(deltaY / this._gantt.parsedOptions.rowHeight)) >= 1\n        ) {\n          const indexs = getTaskIndexsByTaskY(targetEndY, this._gantt);\n          this._gantt._dragOrderTaskRecord(\n            target.task_index,\n            target.sub_task_index,\n            indexs.task_index,\n            indexs.sub_task_index\n          );\n          if (this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate) {\n            this._gantt.taskListTableInstance.renderWithRecreateCells();\n            this._gantt.scenegraph.refreshTaskBarsAndGrid();\n          } else {\n            this._gantt.scenegraph.taskBar.refresh();\n            this._gantt.scenegraph.dependencyLink.refresh();\n          }\n          // target = this._gantt.scenegraph.taskBar.getTaskBarNodeByIndex(indexs.task_index, indexs.sub_task_index);\n        } else {\n          const newX = startDateColIndex >= 1 ? this._gantt.getDateColsWidth(0, startDateColIndex - 1) : 0;\n          resizeOrMoveTaskBar(\n            target,\n            newX - (target as Group).attribute.x,\n            targetEndY - (target as Group).attribute.y,\n            null,\n            this\n          );\n\n          // 为了确保拖拽后 保持startDate日期晚的显示在上层不被盖住 这里需要重新排序一下\n          if (dateChanged === 'right') {\n            let insertAfterNode = target;\n            while (\n              (insertAfterNode as Group).nextSibling &&\n              (insertAfterNode as Group).nextSibling.attribute.y === (target as Group).attribute.y &&\n              (insertAfterNode as Group).nextSibling.record[this._gantt.parsedOptions.startDateField] <=\n                target.record[this._gantt.parsedOptions.startDateField]\n            ) {\n              insertAfterNode = (insertAfterNode as Group).nextSibling;\n            }\n            if (insertAfterNode !== target) {\n              (insertAfterNode as Group).parent.insertAfter(target, insertAfterNode);\n            }\n          } else if (dateChanged === 'left') {\n            let insertBeforeNode = target;\n            while (\n              (insertBeforeNode as Group).previousSibling &&\n              (insertBeforeNode as Group).previousSibling.attribute.y === (target as Group).attribute.y &&\n              (insertBeforeNode as Group).previousSibling.record[this._gantt.parsedOptions.startDateField] >=\n                target.record[this._gantt.parsedOptions.startDateField]\n            ) {\n              insertBeforeNode = (insertBeforeNode as Group).previousSibling;\n            }\n            if (insertBeforeNode !== target) {\n              (insertBeforeNode as Group).parent.insertBefore(target, insertBeforeNode);\n            }\n          }\n        }\n      }\n      this._gantt.scenegraph.updateNextFrame();\n    }\n    this.moveTaskBar.moving = false;\n    if (this.selectedTaskBar.target !== target) {\n      target.setAttribute('zIndex', 0);\n    }\n    this.moveTaskBar.target = null;\n    this.moveTaskBar.deltaX = 0;\n    this.moveTaskBar.deltaY = 0;\n    this.moveTaskBar.moveTaskBarXSpeed = 0;\n  }\n  dealTaskBarMove(e: FederatedPointerEvent) {\n    const target = this.moveTaskBar.target;\n    target.setAttribute('zIndex', 10000);\n    // const taskIndex = getTaskIndexByY(this.moveTaskBar.startOffsetY, this._gantt);\n    const x1 = this._gantt.eventManager.lastDragPointerXYOnWindow.x;\n    const x2 = e.x;\n    const dx = x2 - x1;\n    const y1 = this._gantt.eventManager.lastDragPointerXYOnWindow.y;\n    const y2 = e.y;\n    const dy = y2 - y1;\n\n    this.moveTaskBar.deltaX += dx;\n    this.moveTaskBar.deltaY += dy;\n    // target.setAttribute('x', target.attribute.x + dx);\n    resizeOrMoveTaskBar(target, dx, dy, null, this);\n\n    // 处理向左拖拽任务条时，整体向左滚动\n    if (target.attribute.x <= this._gantt.stateManager.scrollLeft && dx < 0) {\n      this.moveTaskBar.moveTaskBarXSpeed = -this._gantt.parsedOptions.timelineColWidth / 100;\n\n      this.moveTaskBar.moveTaskBarXInertia.startInertia(this.moveTaskBar.moveTaskBarXSpeed, 0, 1);\n      this.moveTaskBar.moveTaskBarXInertia.setScrollHandle((dx: number, dy: number) => {\n        this.moveTaskBar.deltaX += dx;\n        this.moveTaskBar.deltaY += dy;\n        resizeOrMoveTaskBar(target, dx, dy, null, this);\n\n        this._gantt.stateManager.setScrollLeft(target.attribute.x);\n        if (this._gantt.stateManager.scrollLeft === 0) {\n          this.moveTaskBar.moveTaskBarXInertia.endInertia();\n        }\n      });\n    } else if (\n      target.attribute.x + target.attribute.width >=\n        this._gantt.stateManager.scrollLeft + this._gantt.tableNoFrameWidth &&\n      dx > 0\n    ) {\n      // 处理向右拖拽任务条时，整体向右滚动\n      this.moveTaskBar.moveTaskBarXSpeed = this._gantt.parsedOptions.timelineColWidth / 100;\n\n      this.moveTaskBar.moveTaskBarXInertia.startInertia(this.moveTaskBar.moveTaskBarXSpeed, 0, 1);\n      this.moveTaskBar.moveTaskBarXInertia.setScrollHandle((dx: number, dy: number) => {\n        this.moveTaskBar.deltaX += dx;\n        this.moveTaskBar.deltaY += dy;\n        resizeOrMoveTaskBar(target, dx, dy, null, this);\n\n        this._gantt.stateManager.setScrollLeft(\n          target.attribute.x + target.attribute.width - this._gantt.tableNoFrameWidth\n        );\n        if (this._gantt.stateManager.scrollLeft === this._gantt.getAllDateColsWidth() - this._gantt.tableNoFrameWidth) {\n          this.moveTaskBar.moveTaskBarXInertia.endInertia();\n        }\n      });\n    } else if (this.moveTaskBar.moveTaskBarXInertia.isInertiaScrolling()) {\n      this.moveTaskBar.moveTaskBarXInertia.endInertia();\n    } else {\n      this.moveTaskBar.moveTaskBarXSpeed = 0;\n    }\n\n    this._gantt.scenegraph.updateNextFrame();\n\n    //\n  }\n  //#region 调整拖拽任务条的大小\n  startResizeTaskBar(target: Group, x: number, y: number, startOffsetY: number, onIconName: string) {\n    // if (target.name === 'task-bar-hover-shadow') {\n    // target = target.parent.parent;\n    // }\n    this.resizeTaskBar.onIconName = onIconName;\n    this.resizeTaskBar.resizing = true;\n    this.resizeTaskBar.target = target;\n    this.resizeTaskBar.targetStartX = target.attribute.x;\n    this.resizeTaskBar.targetEndX = target.attribute.x + target.attribute.width;\n    this.resizeTaskBar.startX = x;\n    this.resizeTaskBar.startY = y;\n    this.resizeTaskBar.startOffsetY = startOffsetY;\n  }\n  isResizingTaskBar() {\n    return this.resizeTaskBar.resizing;\n  }\n  endResizeTaskBar(x: number) {\n    const direction = this._gantt.stateManager.resizeTaskBar.onIconName;\n    const deltaX = x - this.resizeTaskBar.startX;\n    if (Math.abs(deltaX) >= 1) {\n      // let diff_days =\n      //   Math.round(deltaX / this._gantt.parsedOptions.timelineColWidth) * this._gantt.getMinScaleUnitToDays();\n\n      const colIndex = getDateIndexByX(\n        (direction === 'left'\n          ? this.resizeTaskBar.target.attribute.x\n          : this.resizeTaskBar.target.attribute.x + this.resizeTaskBar.target.attribute.width) -\n          this._gantt.stateManager.scroll.horizontalBarPos,\n        this._gantt\n      );\n      const timelineDate = this._gantt.parsedOptions.reverseSortedTimelineScales[0].timelineDates[colIndex];\n      const targetDate = direction === 'left' ? timelineDate.startDate : timelineDate.endDate;\n      // diff_days = direction === 'left' ? -diff_days : diff_days;\n\n      const taskBarGroup = this.resizeTaskBar.target;\n      const clipGroupBox = taskBarGroup.clipGroupBox;\n      const rect = this.resizeTaskBar.target.barRect;\n      const progressRect = this.resizeTaskBar.target.progressRect;\n      const taskIndex = this.resizeTaskBar.target.task_index;\n      const sub_task_index = this.resizeTaskBar.target.sub_task_index;\n\n      const {\n        taskDays,\n        progress,\n        startDate: oldStartDate,\n        endDate: oldEndDate\n      } = this._gantt.getTaskInfoByTaskListIndex(taskIndex, sub_task_index);\n\n      let dateChanged = false;\n      if (direction === 'left') {\n        this._gantt._updateStartDateToTaskRecord(targetDate, taskIndex, sub_task_index);\n        targetDate.getTime() !== new Date(oldStartDate).getTime() && (dateChanged = true);\n      } else {\n        this._gantt._updateEndDateToTaskRecord(targetDate, taskIndex, sub_task_index);\n        targetDate.getTime() !== new Date(oldEndDate).getTime() && (dateChanged = true);\n      }\n      if (\n        this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n        this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n      ) {\n        this._gantt.taskListTableInstance.renderWithRecreateCells();\n        this._gantt._syncPropsFromTable();\n        this._gantt.scenegraph.refreshTaskBarsAndGrid();\n      } else {\n        if (direction === 'left') {\n          const newX = colIndex >= 1 ? this._gantt.getDateColsWidth(0, colIndex - 1) : 0;\n          taskBarGroup.setAttribute('x', newX);\n          taskBarGroup.setAttribute('width', this.resizeTaskBar.targetEndX - newX);\n        } else if (direction === 'right') {\n          const newEndX = this._gantt.getDateColsWidth(0, colIndex);\n          taskBarGroup.setAttribute('width', newEndX - this.resizeTaskBar.targetStartX);\n        }\n        clipGroupBox.setAttribute('width', taskBarGroup.attribute.width);\n        rect?.setAttribute('width', taskBarGroup.attribute.width);\n        progressRect?.setAttribute('width', (progress / 100) * taskBarGroup.attribute.width);\n        this._gantt.scenegraph.refreshRecordLinkNodes(taskIndex, sub_task_index, taskBarGroup, 0); //更新关联线\n        this.showTaskBarHover();\n        reCreateCustomNode(this._gantt, taskBarGroup, taskIndex, sub_task_index);\n        taskBarGroup.setAttribute('zIndex', 0);\n      }\n      this.resizeTaskBar.resizing = false;\n      this.resizeTaskBar.target = null;\n\n      if (dateChanged && this._gantt.hasListeners(GANTT_EVENT_TYPE.CHANGE_DATE_RANGE)) {\n        const newRecord = this._gantt.getRecordByIndex(taskIndex, sub_task_index);\n        this._gantt.fireListeners(GANTT_EVENT_TYPE.CHANGE_DATE_RANGE, {\n          startDate: newRecord[this._gantt.parsedOptions.startDateField],\n          endDate: newRecord[this._gantt.parsedOptions.endDateField],\n          oldStartDate,\n          oldEndDate,\n          index: taskIndex,\n          record: newRecord\n        });\n      }\n      this._gantt.scenegraph.updateNextFrame();\n    }\n  }\n  dealTaskBarResize(e: FederatedPointerEvent) {\n    const x1 = this._gantt.eventManager.lastDragPointerXYOnWindow.x;\n    const x2 = e.x;\n    const dx = x2 - x1;\n    // debugger;\n    const taskBarGroup = this._gantt.stateManager.resizeTaskBar.target;\n    taskBarGroup.setAttribute('zIndex', 10000);\n    const clipGroupBox = taskBarGroup.clipGroupBox;\n    const rect = taskBarGroup.barRect;\n    const progressRect = taskBarGroup.progressRect;\n    const textLabel = taskBarGroup.textLabel;\n\n    const progressField = this._gantt.parsedOptions.progressField;\n    // const taskIndex = getTaskIndexByY(this.resizeTaskBar.startOffsetY, this._gantt);\n    const taskIndex = taskBarGroup.task_index;\n    const sub_task_index = taskBarGroup.sub_task_index;\n    const taskRecord = this._gantt.getRecordByIndex(taskIndex, sub_task_index);\n    const progress = taskRecord[progressField];\n\n    let diffWidth = this._gantt.stateManager.resizeTaskBar.onIconName === 'left' ? -dx : dx;\n    let taskBarSize = taskBarGroup.attribute.width + diffWidth;\n    if (diffWidth < 0 && taskBarSize <= this._gantt.parsedOptions.timelineColWidth) {\n      diffWidth = this._gantt.parsedOptions.timelineColWidth - taskBarGroup.attribute.width;\n      taskBarSize += diffWidth;\n    }\n    // taskBarGroup.setAttribute('width', taskBarSize);\n    // if (this._gantt.stateManager.resizeTaskBar.onIconName === 'left') {\n    //   taskBarGroup.setAttribute('x', taskBarGroup.attribute.x - diffWidth);\n    // }\n    resizeOrMoveTaskBar(\n      taskBarGroup,\n      this._gantt.stateManager.resizeTaskBar.onIconName === 'left' ? -diffWidth : 0,\n      0,\n      taskBarSize,\n      this\n    );\n    clipGroupBox.setAttribute('width', taskBarGroup.attribute.width);\n    rect?.setAttribute('width', taskBarGroup.attribute.width);\n    progressRect?.setAttribute('width', (progress / 100) * taskBarGroup.attribute.width);\n\n    textLabel?.setAttribute('maxLineWidth', taskBarSize - TASKBAR_HOVER_ICON_WIDTH * 2);\n\n    this.showTaskBarHover();\n\n    reCreateCustomNode(this._gantt, taskBarGroup, taskIndex, sub_task_index);\n    this._gantt.scenegraph.updateNextFrame();\n    //\n  }\n  //#endregion\n  //#region 生成关联线的交互处理\n  startCreateDependencyLine(target: Group, x: number, y: number, startOffsetY: number, position: 'left' | 'right') {\n    // if (target.name === 'task-bar-hover-shadow') {\n    // target = target.parent.parent;\n    // }\n    this.creatingDenpendencyLink.creating = true;\n    this.creatingDenpendencyLink.startClickedPoint = target;\n    this.creatingDenpendencyLink.startX = x;\n    this.creatingDenpendencyLink.startY = y;\n    this.creatingDenpendencyLink.startOffsetY = startOffsetY;\n    this.creatingDenpendencyLink.firstTaskBarPosition = position;\n    this.highlightLinkPointNode(target);\n  }\n  isCreatingDependencyLine() {\n    return this.creatingDenpendencyLink.creating;\n  }\n  endCreateDependencyLine(offsetY: number) {\n    const taskKeyField = this._gantt.parsedOptions.taskKeyField;\n    // const fromTaskIndex = getTaskIndexByY(this.creatingDenpendencyLink.startOffsetY, this._gantt);\n    const fromTaskIndex = this.selectedTaskBar.target.task_index;\n    const from_sub_task_id = this.selectedTaskBar.target.sub_task_index;\n    // const toTaskIndex = getTaskIndexByY(offsetY, this._gantt);\n    const toTaskIndex = this.creatingDenpendencyLink.secondTaskBarNode.task_index;\n    const to_sub_task_id = this.creatingDenpendencyLink.secondTaskBarNode.sub_task_index;\n    const fromRecord = this._gantt.getRecordByIndex(fromTaskIndex, from_sub_task_id);\n    const linkedFromTaskKey = fromRecord[taskKeyField];\n    const toRecord = this._gantt.getRecordByIndex(toTaskIndex, to_sub_task_id);\n    const linkedToTaskKey = toRecord[taskKeyField];\n    const link = {\n      linkedFromTaskKey,\n      linkedToTaskKey,\n      type:\n        this.creatingDenpendencyLink.firstTaskBarPosition === 'left' &&\n        this.creatingDenpendencyLink.secondTaskBarPosition === 'left'\n          ? DependencyType.StartToStart\n          : this.creatingDenpendencyLink.firstTaskBarPosition === 'right' &&\n            this.creatingDenpendencyLink.secondTaskBarPosition === 'left'\n          ? DependencyType.FinishToStart\n          : this.creatingDenpendencyLink.firstTaskBarPosition === 'right' &&\n            this.creatingDenpendencyLink.secondTaskBarPosition === 'right'\n          ? DependencyType.FinishToFinish\n          : DependencyType.StartToFinish\n    };\n    this._gantt.addLink(link);\n    // const oldRecord = this._gantt.getRecordByIndex(fromTaskIndex);\n    this.hideTaskBarSelectedBorder();\n    this._gantt.scenegraph.updateNextFrame();\n    this.creatingDenpendencyLink.creating = false;\n    return link;\n  }\n  dealCreateDependencyLine(e: FederatedPointerEvent) {\n    const x1 = this.creatingDenpendencyLink.startX;\n    const y1 = this.creatingDenpendencyLink.startY;\n    const x2 = e.x;\n    const y2 = e.y;\n    const dx = x2 - x1;\n    const dy = y2 - y1;\n    // debugger;\n    const startClickedPoint = this.creatingDenpendencyLink.startClickedPoint;\n    const x = startClickedPoint.attribute.x + startClickedPoint.attribute.width / 2;\n    const y = startClickedPoint.attribute.y + startClickedPoint.attribute.height / 2;\n    this._gantt.scenegraph.taskBar.updateCreatingDependencyLine(x, y, x + dx, y + dy);\n    this._gantt.scenegraph.updateNextFrame();\n    //\n  }\n  //#endregion\n  //#region 调整左侧任务列表表格整体的宽度\n  startResizeTableWidth(e: MouseEvent) {\n    this.resizeTableWidth.resizing = true;\n    this.resizeTableWidth.lastX = e.pageX;\n    //this.resizeTableWidth.startWidth = this._gantt.tableNoFrameWidth;\n  }\n  isResizingTableWidth() {\n    return this.resizeTableWidth.resizing;\n  }\n  endResizeTableWidth() {\n    this.resizeTableWidth.resizing = false;\n  }\n\n  dealResizeTableWidth(e: MouseEvent) {\n    if (!this.resizeTableWidth.resizing) {\n      return;\n    }\n    const deltaX = e.pageX - this.resizeTableWidth.lastX;\n    if (Math.abs(deltaX) >= 1) {\n      const startWidth = this._gantt.taskTableWidth;\n      let width = startWidth + deltaX;\n      const maxWidth = Math.min(\n        this._gantt.taskListTableInstance.getAllColsWidth() + this._gantt.parsedOptions.outerFrameStyle.borderLineWidth,\n        this._gantt.options.taskListTable.maxTableWidth ?? 100000\n      );\n      const minWidth = Math.max(\n        this._gantt.parsedOptions.outerFrameStyle.borderLineWidth,\n        this._gantt.options.taskListTable.minTableWidth ?? 0\n      );\n      if (deltaX > 0 && width > maxWidth) {\n        width = maxWidth;\n      }\n      if (deltaX < 0 && width < minWidth) {\n        width = minWidth;\n      }\n      this._gantt.taskTableWidth = width;\n      this._gantt.element.style.left = this._gantt.taskTableWidth ? `${this._gantt.taskTableWidth}px` : '0px';\n      this._gantt.verticalSplitResizeLine.style.left = this._gantt.taskTableWidth\n        ? `${this._gantt.taskTableWidth - 7}px`\n        : '0px';\n      this._gantt._resize();\n      this.resizeTableWidth.lastX = e.pageX;\n    }\n  }\n  //#endregion\n\n  showTaskBarHover() {\n    const target = this._gantt.stateManager.hoverTaskBar.target;\n    if (target) {\n      const x = target.attribute.x;\n      const y = target.attribute.y;\n      const width = target.attribute.width;\n      const height = target.attribute.height;\n      this._gantt.scenegraph.taskBar.showHoverBar(x, y, width, height, target);\n      this._gantt.scenegraph.updateNextFrame();\n    }\n  }\n  hideTaskBarHover(e: FederatedPointerEvent) {\n    this._gantt.stateManager.hoverTaskBar.target = null;\n    this._gantt.scenegraph.taskBar.hideHoverBar();\n    this._gantt.scenegraph.updateNextFrame();\n  }\n\n  showTaskBarSelectedBorder(target: GanttTaskBarNode) {\n    this._gantt.stateManager.selectedTaskBar.target?.setAttribute('zIndex', 0);\n    this._gantt.stateManager.selectedTaskBar.target = target as any as GanttTaskBarNode;\n    const linkCreatable = this._gantt.parsedOptions.dependencyLinkCreatable;\n    target.setAttribute('zIndex', 10000);\n    const x = target.attribute.x;\n    const y = target.attribute.y;\n    const width = target.attribute.width;\n    const height = target.attribute.height;\n    this._gantt.scenegraph.taskBar.createSelectedBorder(x, y, width, height, target, linkCreatable);\n    this._gantt.scenegraph.updateNextFrame();\n  }\n\n  hideTaskBarSelectedBorder() {\n    this._gantt.stateManager.selectedTaskBar.target?.setAttribute('zIndex', 0);\n    this._gantt.stateManager.selectedTaskBar.target = null;\n    this._gantt.scenegraph.taskBar.removeSelectedBorder();\n    this._gantt.scenegraph.updateNextFrame();\n  }\n  showSecondTaskBarSelectedBorder() {\n    const target = this._gantt.stateManager.creatingDenpendencyLink.secondTaskBarNode;\n    const x = target.attribute.x;\n    const y = target.attribute.y;\n    const width = target.attribute.width;\n    const height = target.attribute.height;\n    this._gantt.scenegraph.taskBar.createSelectedBorder(x, y, width, height, target, true);\n    this._gantt.scenegraph.updateNextFrame();\n  }\n  hideSecondTaskBarSelectedBorder() {\n    this._gantt.stateManager.creatingDenpendencyLink.secondTaskBarNode = null;\n    this._gantt.scenegraph.taskBar.removeSecondSelectedBorder();\n    this._gantt.scenegraph.updateNextFrame();\n  }\n  showDependencyLinkSelectedLine() {\n    const link = this._gantt.stateManager.selectedDenpendencyLink.link;\n    this._gantt.scenegraph.dependencyLink.createSelectedLinkLine(link);\n\n    const { taskKeyField, dependencyLinks } = this._gantt.parsedOptions;\n    const { linkedToTaskKey, linkedFromTaskKey, type } = link;\n    let linkFrom_index;\n    let linkFrom_sub_task_index;\n    let linkTo_index;\n    let linkTo_sub_task_index;\n    const linkedToTaskRecord = findRecordByTaskKey(this._gantt.records, taskKeyField, linkedToTaskKey);\n    const linkedFromTaskRecord = findRecordByTaskKey(this._gantt.records, taskKeyField, linkedFromTaskKey);\n    if (\n      this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline ||\n      this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate ||\n      this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n      this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n    ) {\n      linkFrom_index = linkedFromTaskRecord.index[0];\n      linkFrom_sub_task_index = linkedFromTaskRecord.index[1];\n      linkTo_index = linkedToTaskRecord.index[0];\n      linkTo_sub_task_index = linkedToTaskRecord.index[1];\n    } else {\n      linkFrom_index = this._gantt.getTaskShowIndexByRecordIndex(linkedFromTaskRecord.index);\n      linkTo_index = this._gantt.getTaskShowIndexByRecordIndex(linkedToTaskRecord.index);\n    }\n    const fromTaskNode = this._gantt.scenegraph.taskBar.getTaskBarNodeByIndex(\n      linkFrom_index,\n      linkFrom_sub_task_index\n    ) as GanttTaskBarNode;\n    this._gantt.scenegraph.taskBar.createSelectedBorder(\n      fromTaskNode.attribute.x,\n      fromTaskNode.attribute.y,\n      fromTaskNode.attribute.width,\n      fromTaskNode.attribute.height,\n      fromTaskNode,\n      false\n    );\n    const toTaskNode = this._gantt.scenegraph.taskBar.getTaskBarNodeByIndex(\n      linkTo_index,\n      linkTo_sub_task_index\n    ) as GanttTaskBarNode;\n    this._gantt.scenegraph.taskBar.createSelectedBorder(\n      toTaskNode.attribute.x,\n      toTaskNode.attribute.y,\n      toTaskNode.attribute.width,\n      toTaskNode.attribute.height,\n      toTaskNode,\n      false\n    );\n\n    this._gantt.scenegraph.updateNextFrame();\n  }\n  hideDependencyLinkSelectedLine() {\n    this._gantt.stateManager.selectedDenpendencyLink.link = null;\n    this._gantt.scenegraph.dependencyLink.removeSelectedLinkLine();\n    this._gantt.scenegraph.taskBar.removeSelectedBorder();\n    this._gantt.scenegraph.updateNextFrame();\n  }\n  highlightLinkPointNode(linkPointGroup: Group) {\n    if (linkPointGroup?.children.length > 0) {\n      const circle = linkPointGroup.children[0];\n      circle.setAttribute('fill', this._gantt.parsedOptions.dependencyLinkLineCreatingPointStyle.fillColor);\n      circle.setAttribute('stroke', this._gantt.parsedOptions.dependencyLinkLineCreatingPointStyle.strokeColor);\n      circle.setAttribute('radius', this._gantt.parsedOptions.dependencyLinkLineCreatingPointStyle.radius);\n      circle.setAttribute('lineWidth', this._gantt.parsedOptions.dependencyLinkLineCreatingPointStyle.strokeWidth);\n      this._gantt.scenegraph.updateNextFrame();\n    }\n  }\n  unhighlightLinkPointNode(linkPointGroup: Group) {\n    if (linkPointGroup?.children.length > 0) {\n      const circle = linkPointGroup.children[0];\n      circle.setAttribute('fill', this._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.fillColor);\n      circle.setAttribute('stroke', this._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.strokeColor);\n      circle.setAttribute('radius', this._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.radius);\n      circle.setAttribute('lineWidth', this._gantt.parsedOptions.dependencyLinkLineCreatePointStyle.strokeWidth);\n      this._gantt.scenegraph.updateNextFrame();\n    }\n  }\n}\n\nfunction reCreateCustomNode(gantt: Gantt, taskBarGroup: Group, taskIndex: number, sub_task_index?: number) {\n  const taskBarCustomLayout = gantt.parsedOptions.taskBarCustomLayout;\n  if (taskBarCustomLayout) {\n    let customLayoutObj;\n    if (typeof taskBarCustomLayout === 'function') {\n      const { startDate, endDate, taskDays, progress, taskRecord } = gantt.getTaskInfoByTaskListIndex(\n        taskIndex,\n        sub_task_index\n      );\n      const arg = {\n        width: taskBarGroup.attribute.width,\n        height: taskBarGroup.attribute.height,\n        index: taskIndex,\n        startDate,\n        endDate,\n        taskDays,\n        progress,\n        taskRecord,\n        ganttInstance: gantt\n      };\n      customLayoutObj = taskBarCustomLayout(arg);\n    } else {\n      customLayoutObj = taskBarCustomLayout;\n    }\n    if (customLayoutObj) {\n      const rootContainer = customLayoutObj.rootContainer;\n      rootContainer.name = 'task-bar-custom-render';\n      const barGroup = taskBarGroup.children.find((node: any) => node.name === 'task-bar-group');\n      if (barGroup) {\n        const oldCustomIndex = barGroup.children.findIndex((node: any) => {\n          return node.name === 'task-bar-custom-render';\n        });\n        const oldCustomNode = barGroup.children[oldCustomIndex] as Group;\n        if (oldCustomNode) {\n          barGroup.removeChild(oldCustomNode);\n          barGroup.insertInto(rootContainer, oldCustomIndex);\n        }\n      }\n    }\n  }\n}\n\nfunction resizeOrMoveTaskBar(target: GanttTaskBarNode, dx: number, dy: number, newWidth: number, state: StateManager) {\n  // const taskIndex = getTaskIndexByY(state.moveTaskBar.startOffsetY, state._gantt);\n  const taskIndex = target.task_index;\n  const sub_task_index = target.sub_task_index;\n  if (dx) {\n    target.setAttribute('x', target.attribute.x + dx);\n  }\n  if (state._gantt.parsedOptions.tasksShowMode !== TasksShowMode.Tasks_Separate) {\n    if (dy) {\n      target.setAttribute('y', target.attribute.y + dy);\n    }\n  } else {\n    dy = 0;\n  }\n  if (newWidth) {\n    target.setAttribute('width', newWidth);\n  }\n  state._gantt.scenegraph.refreshRecordLinkNodes(taskIndex, sub_task_index, target, dy);\n}\n", "/* Adapted from cheetah-grid by yosuke ota\n *url:https://github.com/future-architect/cheetah-grid/blob/master/packages/cheetah-grid/src/js/core/EventTarget.ts\n *License: https://github.com/future-architect/cheetah-grid/blob/master/LICENSE\n * @license\n */\nimport type {\n  TableEventListener,\n  EventListenerId,\n  TableEventHandlersEventArgumentMap,\n  TableEventHandlersReturnMap\n} from '../ts-types';\nimport { isValid } from '@visactor/vutils';\n\nlet idCount = 1;\n\nexport class EventTarget {\n  private listenersData: {\n    listeners: { [TYPE in keyof TableEventHandlersEventArgumentMap]?: TableEventListener<TYPE>[] };\n    listenerData: {\n      [id: number]: {\n        type: string;\n        listener: TableEventListener<keyof TableEventHandlersEventArgumentMap>;\n        remove: () => void;\n      };\n    };\n  } = {\n    listeners: {},\n    listenerData: {}\n  };\n\n  /**\n   * 监听事件\n   * @param type 事件类型\n   * @param listener 事件监听器\n   * @returns 事件监听器id\n   */\n  on<TYPE extends keyof TableEventHandlersEventArgumentMap>(\n    type: TYPE,\n    listener: TableEventListener<TYPE>\n  ): EventListenerId {\n    const list: TableEventListener<TYPE>[] =\n      this.listenersData.listeners[type] || (this.listenersData.listeners[type] = []);\n    list.push(listener);\n\n    const id = idCount++;\n    this.listenersData.listenerData[id] = {\n      type,\n      listener,\n      remove: (): void => {\n        delete this.listenersData.listenerData[id];\n        const index = list.indexOf(listener);\n        list.splice(index, 1);\n        if (!this.listenersData.listeners[type].length) {\n          delete this.listenersData.listeners[type];\n        }\n      }\n    };\n    return id;\n  }\n\n  off(type: string, listener: TableEventListener<keyof TableEventHandlersEventArgumentMap>): void;\n  off(id: EventListenerId): void;\n  off(\n    idOrType: EventListenerId | string,\n    listener?: TableEventListener<keyof TableEventHandlersEventArgumentMap>\n  ): void {\n    if (listener) {\n      const type = idOrType as string;\n      this.removeEventListener(type, listener);\n    } else {\n      const id = idOrType as EventListenerId;\n      if (!this.listenersData) {\n        return;\n      }\n      this.listenersData.listenerData[id]?.remove();\n    }\n  }\n\n  addEventListener<TYPE extends keyof TableEventHandlersEventArgumentMap>(\n    type: TYPE,\n    listener: TableEventListener<TYPE>,\n    option?: any\n  ): void {\n    this.on(type, listener);\n  }\n\n  removeEventListener(type: string, listener: TableEventListener<keyof TableEventHandlersEventArgumentMap>): void {\n    if (!this.listenersData) {\n      return;\n    }\n    for (const key in this.listenersData.listenerData) {\n      const listenerData = this.listenersData.listenerData[key];\n      if (listenerData.type === type && listenerData.listener === listener) {\n        this.off(key as unknown as number);\n      }\n    }\n  }\n\n  hasListeners(type: string): boolean {\n    if (!this.listenersData) {\n      return false;\n    }\n    return !!this.listenersData.listeners[type];\n  }\n\n  // fireListeners(type: string, ...args: any[]): any {\n  //   if (!this.listenersData) {\n  //     return [];\n  //   }\n  //   const list = this.listenersData.listeners[type];\n  //   if (!list) {\n  //     return [];\n  //   }\n  //   return list.map(listener => listener.call(this, ...args)).filter(r => isValid(r));\n  // }\n  fireListeners<TYPE extends keyof TableEventHandlersEventArgumentMap>(\n    type: TYPE,\n    event: TableEventHandlersEventArgumentMap[TYPE]\n  ): TableEventHandlersReturnMap[TYPE][] {\n    if (!this.listenersData) {\n      return [];\n    }\n    const list = this.listenersData.listeners[type];\n    if (!list) {\n      return [];\n    }\n    return list.map(listener => listener.call(this, event)).filter(r => isValid(r));\n  }\n  release(): void {\n    delete this.listenersData;\n  }\n}\n", "import type { Gantt } from '../Gantt';\nimport {\n  createDateAtLastHour,\n  createDateAtLastMinute,\n  createDateAtMidnight,\n  getEndDateByTimeUnit,\n  getStartDateByTimeUnit\n} from '../tools/util';\nimport { TasksShowMode } from '../ts-types';\nimport { isValid } from '@visactor/vutils';\nexport class DataSource {\n  records: any[];\n  minDate: Date;\n  maxDate: Date;\n  _gantt: Gantt;\n  constructor(_gantt: Gantt) {\n    this._gantt = _gantt;\n    this.records = _gantt.records;\n    this.minDate = _gantt.parsedOptions.minDate;\n    this.maxDate = _gantt.parsedOptions.maxDate;\n    this.processRecords();\n  }\n  processRecords() {\n    const needMinDate = !this.minDate;\n    const needMaxDate = !this.maxDate;\n\n    let minDate = Number.MAX_SAFE_INTEGER;\n    let maxDate = Number.MIN_SAFE_INTEGER;\n    if (\n      (needMinDate ||\n        needMaxDate ||\n        this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline ||\n        this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact) &&\n      this.records.length\n    ) {\n      for (let i = 0; i < this.records.length; i++) {\n        const record = this.records[i];\n        if (needMinDate && record[this._gantt.parsedOptions.startDateField]) {\n          const recordMinDate = createDateAtMidnight(record[this._gantt.parsedOptions.startDateField]);\n          minDate = Math.min(minDate, recordMinDate.getTime());\n        }\n        if (needMaxDate && record[this._gantt.parsedOptions.endDateField]) {\n          const recordMaxDate = createDateAtMidnight(record[this._gantt.parsedOptions.endDateField]);\n          maxDate = Math.max(maxDate, recordMaxDate.getTime());\n        }\n\n        if (\n          this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline ||\n          this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact\n        ) {\n          // 将子任务按开始时间升序排列\n          record.children &&\n            record.children.sort((a: any, b: any) => {\n              return (\n                createDateAtMidnight(a[this._gantt.parsedOptions.startDateField]).getTime() -\n                createDateAtMidnight(b[this._gantt.parsedOptions.startDateField]).getTime()\n              );\n            });\n        }\n      }\n\n      needMinDate && (this.minDate = createDateAtMidnight(minDate));\n      needMaxDate && (this.maxDate = createDateAtMidnight(maxDate));\n\n      const { unit: minTimeUnit, startOfWeek, step } = this._gantt.parsedOptions.reverseSortedTimelineScales[0];\n      if (needMinDate) {\n        this._gantt.parsedOptions.minDate = getStartDateByTimeUnit(new Date(minDate), minTimeUnit, startOfWeek);\n        this._gantt.parsedOptions._minDateTime = this._gantt.parsedOptions.minDate.getTime();\n      }\n      if (needMaxDate) {\n        this._gantt.parsedOptions.maxDate = getEndDateByTimeUnit(\n          this._gantt.parsedOptions.minDate,\n          new Date(maxDate),\n          minTimeUnit,\n          step\n        );\n        this._gantt.parsedOptions._maxDateTime = this._gantt.parsedOptions.maxDate.getTime();\n      }\n    }\n  }\n  adjustOrder(\n    source_index: number,\n    source_sub_task_index: number,\n    target_index: number,\n    target_sub_task_index: number\n  ) {\n    if (\n      (this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ||\n        this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact) &&\n      source_index === target_index\n    ) {\n      return;\n    }\n    if (this._gantt.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline) {\n      if (\n        isValid(source_sub_task_index) &&\n        isValid(target_sub_task_index) &&\n        isValid(source_index) &&\n        isValid(target_index)\n      ) {\n        const sub_task_record = this.records[source_index].children[source_sub_task_index];\n        this.records[source_index].children.splice(source_sub_task_index, 1);\n        if (!this.records[target_index].children) {\n          this.records[target_index].children = [];\n        }\n        this.records[target_index].children.splice(target_sub_task_index, 0, sub_task_record);\n      }\n      this.records[target_index]?.children?.sort((a: any, b: any) => {\n        return (\n          createDateAtMidnight(a[this._gantt.parsedOptions.startDateField]).getTime() -\n          createDateAtMidnight(b[this._gantt.parsedOptions.startDateField]).getTime()\n        );\n      });\n    } else {\n      if (\n        isValid(source_sub_task_index) &&\n        isValid(target_sub_task_index) &&\n        isValid(source_index) &&\n        isValid(target_index)\n      ) {\n        const sub_task_record = this.records[source_index].children[source_sub_task_index];\n        this.records[source_index].children.splice(source_sub_task_index, 1);\n        if (!this.records[target_index].children) {\n          this.records[target_index].children = [];\n        }\n        this.records[target_index].children.splice(target_sub_task_index, 0, sub_task_record);\n      }\n    }\n  }\n  setRecords(records: any[]) {\n    this.records = records;\n    this.processRecords();\n  }\n}\n", "import { Scenegraph } from \"./scenegraph/scenegraph\";\n\nimport { Env } from \"./env\";\n\nimport { TasksShowMode } from \"./ts-types\";\n\nimport { themes, ListTable } from \"@visactor/vtable\";\n\nimport { EventManager } from \"./event/event-manager\";\n\nimport { StateManager } from \"./state/state-manager\";\n\nimport { computeRowsCountByRecordDate, computeRowsCountByRecordDateForCompact, convertProgress, createSplitLineAndResizeLine, generateTimeLineDate, getHorizontalScrollBarSize, getVerticalScrollBarSize, initOptions, updateOptionsWhenScaleChanged, updateSplitLineAndResizeLine } from \"./gantt-helper\";\n\nimport { EventTarget } from \"./event/EventTarget\";\n\nimport { computeCountToTimeScale, createDateAtLastHour, createDateAtLastMillisecond, createDateAtMidnight, formatDate, isPropertyWritable, parseDateFormat } from \"./tools/util\";\n\nimport { DataSource } from \"./data/DataSource\";\n\nimport { isValid } from \"@visactor/vutils\";\n\nexport function createRootElement(padding, className = \"vtable-gantt\") {\n    var _a, _b;\n    const element = document.createElement(\"div\");\n    element.setAttribute(\"tabindex\", \"0\"), element.classList.add(className), element.style.outline = \"none\", \n    element.style.margin = `${padding.top}px ${padding.right}px ${padding.bottom}px ${padding.left}px`;\n    const width = (element.offsetWidth || (null === (_a = element.parentElement) || void 0 === _a ? void 0 : _a.offsetWidth) || 1) - 1, height = (element.offsetHeight || (null === (_b = element.parentElement) || void 0 === _b ? void 0 : _b.offsetHeight) || 1) - 1;\n    return element.style.width = width && width - padding.left - padding.right + \"px\" || \"0px\", \n    element.style.height = height && height - padding.top - padding.bottom + \"px\" || \"0px\", \n    element;\n}\n\nexport class Gantt extends EventTarget {\n    constructor(container, options) {\n        var _a, _b, _c, _d, _e;\n        super(), this.parsedOptions = {}, this.container = container, this.options = options, \n        this.taskTableWidth = \"number\" == typeof (null === (_a = null == options ? void 0 : options.taskListTable) || void 0 === _a ? void 0 : _a.tableWidth) ? null === (_b = null == options ? void 0 : options.taskListTable) || void 0 === _b ? void 0 : _b.tableWidth : -1, \n        this.taskTableColumns = null !== (_d = null === (_c = null == options ? void 0 : options.taskListTable) || void 0 === _c ? void 0 : _c.columns) && void 0 !== _d ? _d : [], \n        this.records = null !== (_e = null == options ? void 0 : options.records) && void 0 !== _e ? _e : [], \n        this._sortScales(), initOptions(this), this.data = new DataSource(this), this._generateTimeLineDateMap(), \n        this.timeLineHeaderLevel = this.parsedOptions.sortedTimelineScales.length, this.element = createRootElement({\n            top: 0,\n            right: 0,\n            left: 0,\n            bottom: 0\n        }, \"vtable-gantt\"), this.element.style.left = this.taskTableWidth ? `${this.taskTableWidth}px` : \"0px\", \n        this.canvas = document.createElement(\"canvas\"), this.element.appendChild(this.canvas), \n        this.context = this.canvas.getContext(\"2d\"), container ? (container.appendChild(this.element), \n        this._updateSize()) : this._updateSize(), this._generateListTable(), this._syncPropsFromTable(), \n        createSplitLineAndResizeLine(this), this.scenegraph = new Scenegraph(this), this.stateManager = new StateManager(this), \n        this.eventManager = new EventManager(this), this.scenegraph.afterCreateSceneGraph(), \n        this._scrollToMarkLine();\n    }\n    renderTaskBarsTable() {\n        this.scenegraph.updateNextFrame();\n    }\n    _updateSize() {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n        let widthP = 0, heightP = 0;\n        if (\"browser\" === Env.mode) {\n            const element = this.getElement();\n            let widthWithoutPadding = 0, heightWithoutPadding = 0;\n            if (element.parentElement) {\n                const computedStyle = element.parentElement.style || window.getComputedStyle(element.parentElement);\n                widthWithoutPadding = element.parentElement.offsetWidth - parseInt(computedStyle.paddingLeft || \"0px\", 10) - parseInt(computedStyle.paddingRight || \"0px\", 10), \n                heightWithoutPadding = element.parentElement.offsetHeight - parseInt(computedStyle.paddingTop || \"0px\", 10) - parseInt(computedStyle.paddingBottom || \"0px\", 20);\n            }\n            const width1 = (null != widthWithoutPadding ? widthWithoutPadding : 1) - 1 - this.taskTableWidth, height1 = (null != heightWithoutPadding ? heightWithoutPadding : 1) - 1;\n            element.style.width = width1 && `${width1}px` || \"0px\", element.style.height = height1 && `${height1}px` || \"0px\";\n            const {canvas: canvas} = this;\n            widthP = null !== (_b = null === (_a = canvas.parentElement) || void 0 === _a ? void 0 : _a.offsetWidth) && void 0 !== _b ? _b : 1, \n            heightP = null !== (_d = null === (_c = canvas.parentElement) || void 0 === _c ? void 0 : _c.offsetHeight) && void 0 !== _d ? _d : 1, \n            (null === (_e = null == this ? void 0 : this.scenegraph) || void 0 === _e ? void 0 : _e.stage) ? this.scenegraph.stage.resize(widthP, heightP) : (canvas.style.width = \"\", \n            canvas.style.height = \"\", canvas.width = widthP, canvas.height = heightP, canvas.style.width = `${widthP}px`, \n            canvas.style.height = `${heightP}px`);\n        } else Env.mode;\n        const width = Math.floor(widthP - getVerticalScrollBarSize(this.parsedOptions.scrollStyle)), height = Math.floor(heightP - getHorizontalScrollBarSize(this.parsedOptions.scrollStyle));\n        if (this.tableNoFrameWidth = widthP, this.tableNoFrameHeight = Math.floor(heightP), \n        this.parsedOptions.outerFrameStyle) {\n            const lineWidth = null === (_f = this.parsedOptions.outerFrameStyle) || void 0 === _f ? void 0 : _f.borderLineWidth;\n            this.tableX = this.taskTableColumns.length >= 1 || (null === (_g = this.options) || void 0 === _g ? void 0 : _g.rowSeriesNumber) ? null !== (_h = this.parsedOptions.verticalSplitLine.lineWidth) && void 0 !== _h ? _h : 0 : lineWidth, \n            this.tableY = lineWidth, this.tableNoFrameWidth = Math.min(width - lineWidth - this.tableX, this.getAllDateColsWidth()), \n            this.tableNoFrameHeight = height - 2 * lineWidth;\n        }\n    }\n    _generateListTable() {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n        if (this.taskTableColumns.length >= 1 || (null === (_a = this.options) || void 0 === _a ? void 0 : _a.rowSeriesNumber)) {\n            const listTableOption = this._generateListTableOptions();\n            if (this.taskListTableInstance = new ListTable(this.container, listTableOption), \n            \"auto\" !== (null === (_c = null === (_b = this.options) || void 0 === _b ? void 0 : _b.taskListTable) || void 0 === _c ? void 0 : _c.tableWidth) && -1 !== this.taskTableWidth || (this.taskTableWidth = this.taskListTableInstance.getAllColsWidth() + this.parsedOptions.outerFrameStyle.borderLineWidth, \n            (null === (_e = null === (_d = this.options) || void 0 === _d ? void 0 : _d.taskListTable) || void 0 === _e ? void 0 : _e.maxTableWidth) && (this.taskTableWidth = Math.min(null === (_g = null === (_f = this.options) || void 0 === _f ? void 0 : _f.taskListTable) || void 0 === _g ? void 0 : _g.maxTableWidth, this.taskTableWidth)), \n            (null === (_j = null === (_h = this.options) || void 0 === _h ? void 0 : _h.taskListTable) || void 0 === _j ? void 0 : _j.minTableWidth) && (this.taskTableWidth = Math.max(null === (_l = null === (_k = this.options) || void 0 === _k ? void 0 : _k.taskListTable) || void 0 === _l ? void 0 : _l.minTableWidth, this.taskTableWidth)), \n            this.element.style.left = this.taskTableWidth ? `${this.taskTableWidth}px` : \"0px\", \n            this.taskListTableInstance.setCanvasSize(this.taskTableWidth, this.tableNoFrameHeight + 2 * this.parsedOptions.outerFrameStyle.borderLineWidth), \n            this._updateSize()), this.taskListTableInstance.columnHeaderLevelCount > 1) if (this.taskListTableInstance.columnHeaderLevelCount === this.parsedOptions.timeLineHeaderRowHeights.length) for (let i = 0; i < this.taskListTableInstance.columnHeaderLevelCount; i++) this.taskListTableInstance.setRowHeight(i, this.parsedOptions.timeLineHeaderRowHeights[i]); else {\n                const newRowHeight = this.getAllHeaderRowsHeight() / this.taskListTableInstance.columnHeaderLevelCount;\n                for (let i = 0; i < this.taskListTableInstance.columnHeaderLevelCount; i++) this.taskListTableInstance.setRowHeight(i, newRowHeight);\n            }\n        }\n    }\n    _generateListTableOptions() {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29, _30, _31, _32, _33, _34, _35, _36, _37, _38, _39, _40, _41;\n        const listTable_options = {}, needPutInListTableKeys = [ \"container\", \"records\", \"rowSeriesNumber\", \"overscrollBehavior\", \"pixelRatio\" ];\n        for (const key in this.options) needPutInListTableKeys.indexOf(key) >= 0 && (listTable_options[key] = this.options[key]);\n        for (const key in this.options.taskListTable) {\n            if (listTable_options[key] = this.options.taskListTable[key], \"columns\" === key && (listTable_options[key][listTable_options[key].length - 1].disableColumnResize = !0, \n            this.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Inline || this.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate || this.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange || this.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact)) for (let i = 0; i < listTable_options.columns.length; i++) listTable_options.columns[i].tree && (listTable_options.columns[i].tree = !1);\n            \"hierarchyExpandLevel\" !== key || this.parsedOptions.tasksShowMode !== TasksShowMode.Sub_Tasks_Inline && this.parsedOptions.tasksShowMode !== TasksShowMode.Sub_Tasks_Separate && this.parsedOptions.tasksShowMode !== TasksShowMode.Sub_Tasks_Arrange && this.parsedOptions.tasksShowMode !== TasksShowMode.Sub_Tasks_Compact || delete listTable_options[key];\n        }\n        if (null === (_a = this.options.taskListTable) || void 0 === _a ? void 0 : _a.theme) if (listTable_options.theme = null === (_b = this.options.taskListTable) || void 0 === _b ? void 0 : _b.theme, \n        listTable_options.theme.bodyStyle && !isPropertyWritable(listTable_options.theme, \"bodyStyle\")) {\n            listTable_options.theme = (null === (_c = this.options.taskListTable) || void 0 === _c ? void 0 : _c.theme).extends((null === (_d = this.options.taskListTable) || void 0 === _d ? void 0 : _d.theme).getExtendTheme());\n            const extendThemeOption = listTable_options.theme.getExtendTheme();\n            listTable_options.theme.clearBodyStyleCache(), (null === (_e = listTable_options.theme.headerStyle) || void 0 === _e ? void 0 : _e.bgColor) || (extendThemeOption.headerStyle ? extendThemeOption.headerStyle.bgColor || (extendThemeOption.headerStyle.bgColor = this.parsedOptions.timelineHeaderBackgroundColor) : extendThemeOption.headerStyle = {\n                bgColor: this.parsedOptions.timelineHeaderBackgroundColor\n            }), extendThemeOption.bodyStyle ? extendThemeOption.bodyStyle.frameStyle = {\n                borderLineWidth: [ null !== (_g = null === (_f = this.parsedOptions.horizontalSplitLine) || void 0 === _f ? void 0 : _f.lineWidth) && void 0 !== _g ? _g : 0, 0, 0, 0 ],\n                borderColor: null === (_h = this.parsedOptions.horizontalSplitLine) || void 0 === _h ? void 0 : _h.lineColor\n            } : extendThemeOption.bodyStyle = {\n                frameStyle: {\n                    borderLineWidth: [ null !== (_k = null === (_j = this.parsedOptions.horizontalSplitLine) || void 0 === _j ? void 0 : _j.lineWidth) && void 0 !== _k ? _k : 0, 0, 0, 0 ],\n                    borderColor: null === (_l = this.parsedOptions.horizontalSplitLine) || void 0 === _l ? void 0 : _l.lineColor\n                }\n            }, extendThemeOption.cellInnerBorder = !1, extendThemeOption.frameStyle = Object.assign({}, this.parsedOptions.outerFrameStyle, {\n                shadowBlur: 0,\n                cornerRadius: [ null !== (_o = null === (_m = this.parsedOptions.outerFrameStyle) || void 0 === _m ? void 0 : _m.cornerRadius) && void 0 !== _o ? _o : 0, 0, 0, null !== (_q = null === (_p = this.parsedOptions.outerFrameStyle) || void 0 === _p ? void 0 : _p.cornerRadius) && void 0 !== _q ? _q : 0 ],\n                borderLineWidth: [ null !== (_s = null === (_r = this.parsedOptions.outerFrameStyle) || void 0 === _r ? void 0 : _r.borderLineWidth) && void 0 !== _s ? _s : 0, 0, null !== (_u = null === (_t = this.parsedOptions.outerFrameStyle) || void 0 === _t ? void 0 : _t.borderLineWidth) && void 0 !== _u ? _u : 0, null !== (_w = null === (_v = this.parsedOptions.outerFrameStyle) || void 0 === _v ? void 0 : _v.borderLineWidth) && void 0 !== _w ? _w : 0 ]\n            }), extendThemeOption.scrollStyle = Object.assign({}, null === (_y = null === (_x = this.options.taskListTable) || void 0 === _x ? void 0 : _x.theme) || void 0 === _y ? void 0 : _y.scrollStyle, this.parsedOptions.scrollStyle, {\n                verticalVisible: \"none\"\n            }), extendThemeOption.columnResize = Object.assign({\n                labelColor: \"rgba(0,0,0,0)\",\n                labelBackgroundFill: \"rgba(0,0,0,0)\"\n            }, null == extendThemeOption ? void 0 : extendThemeOption.columnResize), extendThemeOption.underlayBackgroundColor || (extendThemeOption.underlayBackgroundColor = this.parsedOptions.underlayBackgroundColor);\n        } else listTable_options.theme.headerStyle ? listTable_options.theme.headerStyle.bgColor || (listTable_options.theme.headerStyle.bgColor = this.parsedOptions.timelineHeaderBackgroundColor) : listTable_options.theme.headerStyle = {\n            bgColor: this.parsedOptions.timelineHeaderBackgroundColor\n        }, listTable_options.theme.headerStyle = Object.assign({}, themes.DEFAULT.headerStyle, {\n            bgColor: this.parsedOptions.timelineHeaderBackgroundColor\n        }, null === (_0 = null === (_z = this.options.taskListTable) || void 0 === _z ? void 0 : _z.theme) || void 0 === _0 ? void 0 : _0.headerStyle), \n        listTable_options.theme.bodyStyle = Object.assign({}, themes.DEFAULT.bodyStyle, null === (_2 = null === (_1 = this.options.taskListTable) || void 0 === _1 ? void 0 : _1.theme) || void 0 === _2 ? void 0 : _2.bodyStyle, {\n            frameStyle: {\n                borderLineWidth: [ null !== (_4 = null === (_3 = this.parsedOptions.horizontalSplitLine) || void 0 === _3 ? void 0 : _3.lineWidth) && void 0 !== _4 ? _4 : 0, 0, 0, 0 ],\n                borderColor: null === (_5 = this.parsedOptions.horizontalSplitLine) || void 0 === _5 ? void 0 : _5.lineColor\n            }\n        }), listTable_options.theme.cellInnerBorder = !1, listTable_options.theme.frameStyle = Object.assign({}, this.parsedOptions.outerFrameStyle, {\n            cornerRadius: [ null !== (_7 = null === (_6 = this.parsedOptions.outerFrameStyle) || void 0 === _6 ? void 0 : _6.cornerRadius) && void 0 !== _7 ? _7 : 0, 0, 0, null !== (_9 = null === (_8 = this.parsedOptions.outerFrameStyle) || void 0 === _8 ? void 0 : _8.cornerRadius) && void 0 !== _9 ? _9 : 0 ],\n            borderLineWidth: [ null !== (_11 = null === (_10 = this.parsedOptions.outerFrameStyle) || void 0 === _10 ? void 0 : _10.borderLineWidth) && void 0 !== _11 ? _11 : 0, 0, null !== (_13 = null === (_12 = this.parsedOptions.outerFrameStyle) || void 0 === _12 ? void 0 : _12.borderLineWidth) && void 0 !== _13 ? _13 : 0, null !== (_15 = null === (_14 = this.parsedOptions.outerFrameStyle) || void 0 === _14 ? void 0 : _14.borderLineWidth) && void 0 !== _15 ? _15 : 0 ]\n        }), listTable_options.theme.scrollStyle = Object.assign({}, null === (_17 = null === (_16 = this.options.taskListTable) || void 0 === _16 ? void 0 : _16.theme) || void 0 === _17 ? void 0 : _17.scrollStyle, this.parsedOptions.scrollStyle, {\n            verticalVisible: \"none\"\n        }), listTable_options.theme.columnResize = Object.assign({\n            labelColor: \"rgba(0,0,0,0)\",\n            labelBackgroundFill: \"rgba(0,0,0,0)\"\n        }, null === (_19 = null === (_18 = this.options.taskListTable) || void 0 === _18 ? void 0 : _18.theme) || void 0 === _19 ? void 0 : _19.columnResize), \n        listTable_options.theme.underlayBackgroundColor || (listTable_options.theme.underlayBackgroundColor = this.parsedOptions.underlayBackgroundColor); else listTable_options.theme = {\n            scrollStyle: Object.assign({}, null === (_21 = null === (_20 = this.options.taskListTable) || void 0 === _20 ? void 0 : _20.theme) || void 0 === _21 ? void 0 : _21.scrollStyle, this.parsedOptions.scrollStyle, {\n                verticalVisible: \"none\"\n            }),\n            headerStyle: Object.assign({}, themes.DEFAULT.headerStyle, {\n                bgColor: this.parsedOptions.timelineHeaderBackgroundColor\n            }, null === (_23 = null === (_22 = this.options.taskListTable) || void 0 === _22 ? void 0 : _22.theme) || void 0 === _23 ? void 0 : _23.headerStyle),\n            bodyStyle: Object.assign({}, themes.DEFAULT.bodyStyle, null === (_25 = null === (_24 = this.options.taskListTable) || void 0 === _24 ? void 0 : _24.theme) || void 0 === _25 ? void 0 : _25.bodyStyle, {\n                frameStyle: {\n                    borderLineWidth: [ null !== (_27 = null === (_26 = this.parsedOptions.horizontalSplitLine) || void 0 === _26 ? void 0 : _26.lineWidth) && void 0 !== _27 ? _27 : 0, 0, 0, 0 ],\n                    borderColor: null === (_28 = this.parsedOptions.horizontalSplitLine) || void 0 === _28 ? void 0 : _28.lineColor\n                }\n            }),\n            cellInnerBorder: !1,\n            frameStyle: Object.assign({}, this.parsedOptions.outerFrameStyle, {\n                cornerRadius: [ null !== (_30 = null === (_29 = this.parsedOptions.outerFrameStyle) || void 0 === _29 ? void 0 : _29.cornerRadius) && void 0 !== _30 ? _30 : 0, 0, 0, null !== (_32 = null === (_31 = this.parsedOptions.outerFrameStyle) || void 0 === _31 ? void 0 : _31.cornerRadius) && void 0 !== _32 ? _32 : 0 ],\n                borderLineWidth: [ null !== (_34 = null === (_33 = this.parsedOptions.outerFrameStyle) || void 0 === _33 ? void 0 : _33.borderLineWidth) && void 0 !== _34 ? _34 : 0, 0, null !== (_36 = null === (_35 = this.parsedOptions.outerFrameStyle) || void 0 === _35 ? void 0 : _35.borderLineWidth) && void 0 !== _36 ? _36 : 0, null !== (_38 = null === (_37 = this.parsedOptions.outerFrameStyle) || void 0 === _37 ? void 0 : _37.borderLineWidth) && void 0 !== _38 ? _38 : 0 ]\n            }),\n            columnResize: Object.assign({\n                labelColor: \"rgba(0,0,0,0)\",\n                labelBackgroundFill: \"rgba(0,0,0,0)\"\n            }, null === (_40 = null === (_39 = this.options.taskListTable) || void 0 === _39 ? void 0 : _39.theme) || void 0 === _40 ? void 0 : _40.columnResize),\n            underlayBackgroundColor: this.parsedOptions.underlayBackgroundColor\n        };\n        return listTable_options.canvasWidth = this.taskTableWidth, listTable_options.canvasHeight = this.canvas.height, \n        listTable_options.defaultHeaderRowHeight = this.getAllHeaderRowsHeight(), this.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Separate ? (listTable_options.customComputeRowHeight = args => {\n            var _a;\n            const {row: row, table: table} = args;\n            return ((null === (_a = table.getRecordByRowCol(0, row).children) || void 0 === _a ? void 0 : _a.length) || 1) * this.parsedOptions.rowHeight;\n        }, listTable_options.defaultRowHeight = \"auto\") : this.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Compact ? (listTable_options.customComputeRowHeight = args => {\n            const {row: row, table: table} = args, record = table.getRecordByRowCol(0, row);\n            return computeRowsCountByRecordDateForCompact(this, record) * this.parsedOptions.rowHeight;\n        }, listTable_options.defaultRowHeight = \"auto\") : this.parsedOptions.tasksShowMode === TasksShowMode.Sub_Tasks_Arrange ? (listTable_options.customComputeRowHeight = args => {\n            const {row: row, table: table} = args, record = table.getRecordByRowCol(0, row);\n            return computeRowsCountByRecordDate(this, record) * this.parsedOptions.rowHeight;\n        }, listTable_options.defaultRowHeight = \"auto\") : listTable_options.defaultRowHeight = null !== (_41 = this.options.rowHeight) && void 0 !== _41 ? _41 : 40, \n        listTable_options.clearDOM = !1, listTable_options;\n    }\n    getElement() {\n        return this.element;\n    }\n    getContainer() {\n        return this.element.parentElement;\n    }\n    _sortScales() {\n        const {timelineHeader: timelineHeader} = this.options;\n        if (timelineHeader) {\n            const timelineScales = timelineHeader.scales, sortOrder = [ \"year\", \"quarter\", \"month\", \"week\", \"day\", \"hour\", \"minute\", \"second\" ];\n            1 === timelineScales.length && (\"hour\" !== timelineScales[0].unit && \"minute\" !== timelineScales[0].unit && \"second\" !== timelineScales[0].unit || (this.parsedOptions.timeScaleIncludeHour = !0));\n            const orderedScales = timelineScales.slice().sort(((a, b) => {\n                \"hour\" !== a.unit && \"minute\" !== a.unit && \"second\" !== a.unit || (this.parsedOptions.timeScaleIncludeHour = !0);\n                const indexA = sortOrder.indexOf(a.unit), indexB = sortOrder.indexOf(b.unit);\n                return -1 === indexA ? 1 : -1 === indexB ? -1 : indexA - indexB;\n            })), reverseOrderedScales = timelineScales.slice().sort(((a, b) => {\n                const indexA = sortOrder.indexOf(a.unit), indexB = sortOrder.indexOf(b.unit);\n                return -1 === indexA ? 1 : -1 === indexB ? -1 : indexB - indexA;\n            }));\n            this.parsedOptions.sortedTimelineScales = orderedScales, this.parsedOptions.reverseSortedTimelineScales = reverseOrderedScales;\n        }\n    }\n    _generateTimeLineDateMap() {\n        if (this.parsedOptions.minDate && this.parsedOptions.maxDate) for (const scale of this.parsedOptions.reverseSortedTimelineScales) scale.timelineDates = generateTimeLineDate(new Date(this.parsedOptions.minDate), this.parsedOptions.maxDate, scale);\n    }\n    getRowHeightByIndex(index) {\n        return this.taskListTableInstance ? this.taskListTableInstance.getRowHeight(index + this.taskListTableInstance.columnHeaderLevelCount) : this.parsedOptions.rowHeight;\n    }\n    getRowsHeightByIndex(startIndex, endIndex) {\n        return this.taskListTableInstance ? this.taskListTableInstance.getRowsHeight(startIndex + this.taskListTableInstance.columnHeaderLevelCount, endIndex + this.taskListTableInstance.columnHeaderLevelCount) : this.parsedOptions.rowHeight * (endIndex - startIndex + 1);\n    }\n    getAllRowsHeight() {\n        return this.taskListTableInstance ? this.taskListTableInstance.getAllRowsHeight() : this.getAllHeaderRowsHeight() + this.itemCount * this.parsedOptions.rowHeight;\n    }\n    getAllHeaderRowsHeight() {\n        return this.parsedOptions.timeLineHeaderRowHeights.reduce(((acc, curr, index) => acc + curr), 0);\n    }\n    getAllDateColsWidth() {\n        var _a, _b;\n        return this.parsedOptions.timelineColWidth * (null !== (_b = null === (_a = this.parsedOptions.reverseSortedTimelineScales[0].timelineDates) || void 0 === _a ? void 0 : _a.length) && void 0 !== _b ? _b : 0);\n    }\n    getAllTaskBarsHeight() {\n        return this.taskListTableInstance ? this.taskListTableInstance.getRowsHeight(this.taskListTableInstance.columnHeaderLevelCount, this.taskListTableInstance.rowCount - 1) : this.itemCount * this.parsedOptions.rowHeight;\n    }\n    getTaskShowIndexByRecordIndex(index) {\n        return this.taskListTableInstance.getBodyRowIndexByRecordIndex(index);\n    }\n    getRecordByIndex(taskShowIndex, sub_task_index) {\n        var _a, _b;\n        return isValid(sub_task_index) ? null === (_b = null === (_a = this.records[taskShowIndex]) || void 0 === _a ? void 0 : _a.children) || void 0 === _b ? void 0 : _b[sub_task_index] : this.taskListTableInstance ? this.taskListTableInstance.getRecordByRowCol(0, taskShowIndex + this.taskListTableInstance.columnHeaderLevelCount) : this.records[taskShowIndex];\n    }\n    _refreshTaskBar(taskShowIndex) {\n        this.scenegraph.taskBar.updateTaskBarNode(taskShowIndex), this.scenegraph.refreshRecordLinkNodes(taskShowIndex, void 0, this.scenegraph.taskBar.getTaskBarNodeByIndex(taskShowIndex)), \n        this.scenegraph.updateNextFrame();\n    }\n    _updateRecordToListTable(record, index) {\n        const indexs = this.taskListTableInstance.getRecordIndexByCell(0, index + this.taskListTableInstance.columnHeaderLevelCount);\n        this.taskListTableInstance.updateRecords([ record ], [ indexs ]);\n    }\n    getTaskInfoByTaskListIndex(taskShowIndex, sub_task_index) {\n        const taskRecord = this.getRecordByIndex(taskShowIndex, sub_task_index), startDateField = this.parsedOptions.startDateField, endDateField = this.parsedOptions.endDateField, progressField = this.parsedOptions.progressField, rawDateStartDateTime = createDateAtMidnight(null == taskRecord ? void 0 : taskRecord[startDateField]).getTime(), rawDateEndDateTime = createDateAtMidnight(null == taskRecord ? void 0 : taskRecord[endDateField]).getTime();\n        if (rawDateEndDateTime < this.parsedOptions._minDateTime || rawDateStartDateTime > this.parsedOptions._maxDateTime || !(null == taskRecord ? void 0 : taskRecord[startDateField]) || !(null == taskRecord ? void 0 : taskRecord[endDateField])) return {\n            taskDays: 0,\n            progress: 0,\n            startDate: null,\n            endDate: null,\n            taskRecord: taskRecord\n        };\n        const progress = convertProgress(taskRecord[progressField]);\n        let startDate, endDate;\n        this.parsedOptions.timeScaleIncludeHour ? (startDate = createDateAtMidnight(Math.min(Math.max(this.parsedOptions._minDateTime, rawDateStartDateTime), this.parsedOptions._maxDateTime)), \n        endDate = createDateAtLastMillisecond(Math.max(Math.min(this.parsedOptions._maxDateTime, rawDateEndDateTime), this.parsedOptions._minDateTime), !0)) : (startDate = createDateAtMidnight(Math.min(Math.max(this.parsedOptions._minDateTime, rawDateStartDateTime), this.parsedOptions._maxDateTime), !0), \n        endDate = createDateAtLastHour(Math.max(Math.min(this.parsedOptions._maxDateTime, rawDateEndDateTime), this.parsedOptions._minDateTime), !0));\n        return {\n            taskRecord: taskRecord,\n            taskDays: (endDate.getTime() - startDate.getTime() + 1) / 864e5,\n            startDate: startDate,\n            endDate: endDate,\n            progress: progress\n        };\n    }\n    _updateStartDateToTaskRecord(startDate, index, sub_task_index) {\n        var _a;\n        const taskRecord = this.getRecordByIndex(index, sub_task_index), startDateField = this.parsedOptions.startDateField, dateFormat = null !== (_a = this.parsedOptions.dateFormat) && void 0 !== _a ? _a : parseDateFormat(taskRecord[startDateField]), newStartDate = formatDate(startDate, dateFormat);\n        taskRecord[startDateField] = newStartDate, isValid(sub_task_index) || this._updateRecordToListTable(taskRecord, index);\n    }\n    _updateEndDateToTaskRecord(endDate, index, sub_task_index) {\n        var _a;\n        const taskRecord = this.getRecordByIndex(index, sub_task_index), endDateField = this.parsedOptions.endDateField, dateFormat = null !== (_a = this.parsedOptions.dateFormat) && void 0 !== _a ? _a : parseDateFormat(taskRecord[endDateField]), newEndDate = formatDate(endDate, dateFormat);\n        taskRecord[endDateField] = newEndDate, isValid(sub_task_index) || this._updateRecordToListTable(taskRecord, index);\n    }\n    _updateStartEndDateToTaskRecord(startDate, endDate, index, sub_task_index) {\n        var _a;\n        const taskRecord = this.getRecordByIndex(index, sub_task_index), startDateField = this.parsedOptions.startDateField, endDateField = this.parsedOptions.endDateField, dateFormat = null !== (_a = this.parsedOptions.dateFormat) && void 0 !== _a ? _a : parseDateFormat(taskRecord[startDateField]), newStartDate = formatDate(startDate, dateFormat);\n        taskRecord[startDateField] = newStartDate;\n        const newEndDate = formatDate(endDate, dateFormat);\n        taskRecord[endDateField] = newEndDate, isValid(sub_task_index) || this._updateRecordToListTable(taskRecord, index);\n    }\n    _dragOrderTaskRecord(source_index, source_sub_task_index, target_index, target_sub_task_index) {\n        this.data.adjustOrder(source_index, source_sub_task_index, target_index, target_sub_task_index);\n    }\n    updateTaskRecord(record, index) {\n        this._updateRecordToListTable(record, index), this._refreshTaskBar(index);\n    }\n    setPixelRatio(pixelRatio) {\n        var _a;\n        null === (_a = this.taskListTableInstance) || void 0 === _a || _a.setPixelRatio(pixelRatio), \n        this.parsedOptions.pixelRatio = pixelRatio, this.scenegraph.setPixelRatio(pixelRatio);\n    }\n    _resize() {\n        var _a;\n        this._updateSize(), null === (_a = this.taskListTableInstance) || void 0 === _a || _a.setCanvasSize(this.taskTableWidth, this.tableNoFrameHeight + 2 * this.parsedOptions.outerFrameStyle.borderLineWidth), \n        this._syncPropsFromTable(), this.scenegraph.resize(), updateSplitLineAndResizeLine(this);\n    }\n    _syncPropsFromTable() {\n        this.itemCount = this.taskListTableInstance ? this.taskListTableInstance.rowCount - this.taskListTableInstance.columnHeaderLevelCount : this.records.length, \n        this.headerHeight = this.getAllHeaderRowsHeight(), this.drawHeight = Math.min(this.getAllRowsHeight(), this.tableNoFrameHeight), \n        this.gridHeight = this.drawHeight - this.headerHeight;\n    }\n    getContext() {\n        return this.context;\n    }\n    release() {\n        var _a, _b;\n        null === (_a = super.release) || void 0 === _a || _a.call(this), this.eventManager.release(), \n        null === (_b = this.taskListTableInstance) || void 0 === _b || _b.release();\n        const {parentElement: parentElement} = this.element;\n        parentElement && (parentElement.removeChild(this.element), this.verticalSplitResizeLine && parentElement.removeChild(this.verticalSplitResizeLine), \n        this.horizontalSplitLine && parentElement.removeChild(this.horizontalSplitLine)), \n        this.scenegraph = null;\n    }\n    setRecords(records) {\n        this.records = records, this.data.setRecords(records), this.taskListTableInstance.setRecords(records), \n        this._syncPropsFromTable(), this._generateTimeLineDateMap(), this.timeLineHeaderLevel = this.parsedOptions.sortedTimelineScales.length, \n        this._updateSize(), this.scenegraph.refreshAll(), this.verticalSplitResizeLine.style.height = this.drawHeight + \"px\";\n        const left = this.stateManager.scroll.horizontalBarPos, top = this.stateManager.scroll.verticalBarPos;\n        this.scenegraph.setX(-left), this.scenegraph.setY(-top);\n    }\n    updateScales(scales) {\n        if (this.options.timelineHeader.scales = scales, this._sortScales(), updateOptionsWhenScaleChanged(this), \n        this._generateTimeLineDateMap(), this.timeLineHeaderLevel = this.parsedOptions.sortedTimelineScales.length, \n        this.scenegraph.refreshAll(), updateSplitLineAndResizeLine(this), this.taskListTableInstance) if (this.taskListTableInstance.columnHeaderLevelCount === this.parsedOptions.timeLineHeaderRowHeights.length) for (let i = 0; i < this.taskListTableInstance.columnHeaderLevelCount; i++) this.taskListTableInstance.setRowHeight(i, this.parsedOptions.timeLineHeaderRowHeights[i]); else {\n            const newRowHeight = this.getAllHeaderRowsHeight() / this.taskListTableInstance.columnHeaderLevelCount;\n            for (let i = 0; i < this.taskListTableInstance.columnHeaderLevelCount; i++) this.taskListTableInstance.setRowHeight(i, newRowHeight);\n        }\n    }\n    _scrollToMarkLine() {\n        if (this.parsedOptions.scrollToMarkLineDate && this.parsedOptions.minDate) {\n            const minDate = this.parsedOptions.minDate, {unit: unit, step: step} = this.parsedOptions.reverseSortedTimelineScales[0], left = computeCountToTimeScale(this.parsedOptions.scrollToMarkLineDate, minDate, unit, step) * this.parsedOptions.timelineColWidth - this.tableNoFrameWidth / 2;\n            this.stateManager.setScrollLeft(left);\n        }\n    }\n    addLink(link) {\n        this.parsedOptions.dependencyLinks.push(link), this.scenegraph.dependencyLink.initLinkLine(this.parsedOptions.dependencyLinks.length - 1), \n        this.scenegraph.updateNextFrame();\n    }\n    deleteLink(link) {\n        const index = this.parsedOptions.dependencyLinks.findIndex((item => item.type === link.type && item.linkedFromTaskKey === link.linkedFromTaskKey && item.linkedToTaskKey === link.linkedToTaskKey));\n        if (-1 !== index) {\n            const link = this.parsedOptions.dependencyLinks[index];\n            this.parsedOptions.dependencyLinks.splice(index, 1), this.scenegraph.dependencyLink.deleteLink(link), \n            this.scenegraph.updateNextFrame();\n        }\n    }\n    get scrollTop() {\n        return this.stateManager.scrollTop;\n    }\n    set scrollTop(value) {\n        this.stateManager.setScrollTop(value);\n    }\n    get scrollLeft() {\n        return this.stateManager.scrollLeft;\n    }\n    set scrollLeft(value) {\n        this.stateManager.setScrollLeft(value);\n    }\n    getTaskBarRelativeRect(index) {\n        const taskBarNode = this.scenegraph.taskBar.getTaskBarNodeByIndex(index);\n        return {\n            left: taskBarNode.attribute.x + this.taskListTableInstance.tableNoFrameWidth + this.taskListTableInstance.tableX + this.tableX - this.scrollLeft,\n            top: taskBarNode.attribute.y + this.tableY + this.headerHeight - this.scrollTop,\n            width: taskBarNode.attribute.width,\n            height: taskBarNode.attribute.height\n        };\n    }\n    getMinScaleUnitToDays() {\n        var _a;\n        const minScale = this.parsedOptions.reverseSortedTimelineScales[0], minScaleUnit = minScale.unit, minScaleStep = null !== (_a = minScale.step) && void 0 !== _a ? _a : 1;\n        return \"day\" === minScaleUnit ? minScaleStep : \"week\" === minScaleUnit ? 7 * minScaleStep : \"month\" === minScaleUnit ? 30 * minScaleStep : \"quarter\" === minScaleUnit ? 90 * minScaleStep : \"year\" === minScaleUnit ? 365 * minScaleStep : \"hour\" === minScaleUnit ? 1 / 24 * minScaleStep : \"minute\" === minScaleUnit ? 1 / 24 / 60 * minScaleStep : \"second\" === minScaleUnit ? 1 / 24 / 60 / 60 * minScaleStep : 1;\n    }\n    getDateColWidth(dateIndex) {\n        return this.parsedOptions.timelineColWidth;\n    }\n    getDateColsWidth(startDateIndex, endDateIndex) {\n        return (endDateIndex - startDateIndex + 1) * this.parsedOptions.timelineColWidth;\n    }\n    getDateRangeByIndex(index) {\n        const minScale = this.parsedOptions.reverseSortedTimelineScales[0];\n        return {\n            startDate: minScale.timelineDates[index].startDate,\n            endDate: minScale.timelineDates[index].endDate\n        };\n    }\n    parseTimeFormat(date) {\n        return parseDateFormat(date);\n    }\n}", "import { formatDate, getWeekNumber, getTodayNearDay, getWeekday } from './util';\n\nexport { formatDate, getWeekNumber, getTodayNearDay, getWeekday };\n", "export { Group, Text, Image, Arc, Area, Circle, Line, Path, Rect, RichText } from '@visactor/vtable/es/vrender';\n", "export { register, themes, CustomLayout, TYPES } from '@visactor/vtable';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,oBAAA;SAAAA,mBAAA;;;;;;;;ACSA,IAAY;UACVC,mBAAA;AACA,EAAAA,kBAAA,UAAA,WAAAA,kBAAqB,UAAA,WAAAA,kBAAA,YAAA;uBACrB,mBAAA,CAAA,EAAA;;;ACiUF,IAAY;UACVC,iBAAA;AACA,EAAAA,gBAAA,gBAAA,mBAA+BA,gBAAA,eAAA,kBAC/BA,gBAAA,iBAAA,oBAAAA,gBAAmC,gBAAA;qBACnC,iBAAA,CAAA,EAAA;AAEF,IAAY;UAEVC,gBAAA;AAEA,EAAAA,eAAA,iBAAA,kBAAAA,eAAqC,mBAAA,oBAErCA,eAAA,qBAAA,sBAAAA,eAAyC,oBAAA,qBAEzCA,eAAA,oBAAA;oBAEA,gBAAA,CAAA,EAAA;;;ACtTK,IAAM,mBAAgC;EAC3C,QAAQ;EACR,mBAAmB;EACnB,gBAAgB;EAChB,qBAAqB;EACrB,qBAAqB;EACrB,sBAAsB;EACtB,wBAAwB;EACxB,6BAA6B;;;;ACxB7B,IAAA,OAAA,MAAY;;AACV,QAAI,IAAC,IAAM,IAAG,IAAM,IAAA,IAAA;AACpB,SAAK,SAAQ,OAAK,KAAM,WAAO,CAAA,CAAA,MAAc,OAAK,cAAa,KAAA,cAC/D,KAAK,aAAa,CAAC,CAAC,MAAM,OAAO,cAAc,KAAK,gBAAe,KAAA,YAAA,MAAA,OAAA,cAAA,MACnE,KAAK,aAAY,GAAA,KAAM,YAAO,GAAA,KAAc,IAAK,GAAA,KAAA,IAAA,MAAA,OAAA,uBAAA,GACjD,KAAK,QAAA,MAAc,WAAC,UAAA,OAAA,KAAA,SAAA,MAAA,WAAA,UAAA,SAAA,MAAA,eAAA,MAAA,UAAA,QACpB,KAAK,YAAY,MAAE,OAAA,cAAA,WAAA,KAAA,WAAA,MAAA,OAAA,WACnB,KAAK,eAAM,MAAA,OAAA,oBAAA,GAAA,KAAA,gBAAA,MAAA,OAAA,qBAAA,GACX,KAAK,QAAI,IAAM,MAAO;MAClB,GAAC,KAAK;MACN,GAAC,KAAM;MACP,OAAC,KAAS;MACV,QAAC,KAAW;MACZ,MAAC;MACD,MAAC,UAAa,KAAG,KAAM,cAAO,WAAoB,KAAG,SAAA,GAAA;IACzD,CAAA,GAAI,KAAC,MAAQ,OAAS,kBAAC,MAAA,WAAA,SAAA,KAAA,KAAA,GAAA,KAAA,oBAAA,QACpB,sBAAQ;UACR,2BAAQ,UAAA,KAAA,UAAA,KAAA,MAAA,OAAA,cAAA,wBAAA,WAAA,KAAA,SAAA,GAAA,cAAA,WAAA,KAAA,KAAA,UAAA,KAAA,MAAA,OAAA,cAAA,sCAAA,WAAA,KAAA,SAAA,GAAA,WAAA,eAAA,IAAA,2BAAA,OAAA,KAAA,2BAAA,GAAA,OAAA,WAAA;MACT,UAAO;MACP,QAAQ,UAAK,KAAM,UAAA,KAAA,MAAA,OAAA,cAAA,wBAAA,WAAA,KAAA,SAAA,GAAA,cAAA,WAAA,KAAA,KAAA,UAAA,KAAA,MAAA,OAAA,cAAA,sCAAA,WAAA,KAAA,SAAA,GAAA;MACnB,WAAU,4BAAA,IAAA,2BAAA,IAAA;MACV,QAAM,CAAA;QACL,GAAA;QACE,GAAM;MACX,GAAM;QAED,GAAA,MAAA,OAAmB,oBAAG;QAEtB,GAAA;MAGL,CAAM;IAGN,CAAA;AACA,SAAA,OAAU,+BAAc,KAAA,MAAA,SAAA,IAAA;;wBAGpB;QAEF,IAAA,IAAA;QACA,KAAA,UAAQ;WACN,oBAAW,IAAa,MAAA;QACxB,GAAA;;eAEK,KAAA;gBACJ,KAAA;OACF,GAAA,KAAA,kBAAA,OAAA,iBAAA,KAAA,MAAA,YAAA,KAAA,iBAAA;AACA,YAAA,SAAA,CAAA,GAAA,gBAAA,KAAA,OAAA,OAAA,cAAA,4BAAA,CAAA,EAAA,eAAA,mBAAA,KAAA,OAAA,OAAA,cAAA;AACC,eAAQ,IAAA,GAAA,KAAA,QAAA,gBAA8B,SAAA,cAAA,UAAA,GAAA,KAAA;AACrC,cAAM,IAAA,KAAS,KAAM,oBAAA,IAAA,EAAA,KAAA,KAAA,UAAA,KAAA,KAAA,cAAA,WAAA,KAAA,SAAA,GAAA,aAAA,aAAA,MAAA,IAAA,OAAA,WAAA;UAC3B,UAAA;UACD,QAAmB,UAAA,KAAA,KAAA,cAAA,WAAA,KAAA,SAAA,GAAA,aAAA;;UACR,QAAU,CAAA;YACZ;YACC,GAAA;UACA,GAAA;YACG;YACC,GAAA,KAAK;UACZ,CAAA;QACC,CAAC;AACD,eAAO,KAAA,IAAA,GAAY,KAAK,kBAAkB,YAAC,IAAA;MAE/C;;;0BAIgB;YACd,IAAA;aACA,YAAa;iCACI,IAAA,MAAA;;;eAGf,KAAQ;gBACN,KAAK;mCACQ,OAAA,mBAAe,KAAA,MAAA,YAAA,KAAA,mBAAA;qBAC7B,CAAA;cACA;WACH,UAAW,KAAK,KAAE,cAAA,WAAA,KAAA,SAAA,GAAA,eAAA,eAAA,KAAA;eACb,IAAA,GAAA,IAAA,KAAA,WAAkB,GAAY,KAAK;AACzC,aAAA,KAAA,OAAA,OAAA,oBAAA,CAAA;AACF,cAAA,OAAA,WAAA;UACF,UAAA;UACD,QAAqB,UAAA,KAAA,KAAA,cAAA,WAAA,KAAA,SAAA,GAAA,eAAA;;UACV,QAAU,CAAE;YACd,GAAA;YACC;UACA,GAAA;YACG,GAAI,KAAC;YACJ;UACP,CAAA;QACC,CAAC;AACD,eAAO,KAAA,IAAA,GAAY,KAAK,oBAAoB,YAAC,IAAA;MAEjD;;;;YAIC;SACD,QAAS,KAAK,OAAM,WAAK,UAAiB,OAAE,KAAA,SAAA,KAAA,OAAA,WAAA,UAAA,SAAA,KAAA,OAAA,eAAA,MAAA,UAAA,mBACzC,cAAY;aACb,KAAM;cACJ,KAAA;cACA,OAAM,OAAE,uBAAc;wBACtB,KAAW,OAAA,OAAK,WAAS,KAAA,eAAA,KAAA,OAAA,OAAE,oBAAwB,wBAC3C,KAAA,OAAA,OAAA,qBAAA,GAAA,UAAA,KAAA,KAAA,sBAAA,WAAA,MAAA,GAAA,OAAA,YAAA,KAAA,iBAAA,uBACH,wBAAQ,WAAA,MAAA,GAAA,OAAA,YAAA,KAAA,mBAAA,4BACF,GAAC,KAAA,sBAAiB;;;YAG/B;cACA,KAAK,KAAA,sBAAoB,WAAkB,MAAA,GAAA,aAAA,KAAA,CAAA,aAC5C,KAAA,KAAA,wBAAA,WAAA,MAAA,GAAA,aAAA,KAAA,CAAA;;EAEL,KAAC,GAAA;AAED,QAAO,IAAA;wFACL,UAAU,KAAG,KAAK,wBAAkB,WAAgB,MAAA,GAAA,aAAA,KAAA,CAAA;;WAE/C;SACH,QAAO,KAAK,OAAK,WAAA,UAAA,OAAA,KAAA,SAAA,KAAA,OAAA,WAAA,UAAA,SAAA,KAAA,OAAA,eAAA,MAAA,UAAA,aACjB,MAAM,aAAa,SAAA,KAAA,KAAA,GAAA,KAAA,MAAA,aAAA,UAAA,KAAA,MAAA;;;;;AC7InB,IAAO,MAAP,MAAO,KAAG;EAEP,WAAW,OAAI;AACpB,WAAK,KAAI,UAAO,KAAA,QAAA,YAAA,IAAA,KAAA;;aAEf,KAAA,MAAA;AACD,SAAA,QAAW;EACb;EACO,OAAM,qBAAuB,MAAA;AAClC,SAAI,eAAa;EACnB;EAYA,OAAO,kBAAA,MAAqB;AAC1B,SAAI,YAAY;EAClB;EAEA,OAAO,sBAAqC;AAC1C,WAAI,KAAA,eAAiB,KAAA,eAAA,aAAA,KAAA,OAAA,CAAA,QAAA,KAAA,SAAA,QAAA,IAAA,gBAAA,OAAA,MAAA,IAAA;EACvB;EAEA,OAAO,8BAAmB,MAAA;AACxB,SAAI,wBAAkB,KAAA;;SAErB,2BAAA;AACD,QAAI,KAAI,sBAAmB,QAAA,KAAA;;SAE1B,6BAAA,MAAA;AACD,SAAA,uBAAiB,KAAA;EACnB;EAEA,OAAO,0BAAA;AACL,QAAI,KAAA,qBAAwB,QAAO,KAAA;EACrC;;uBAIe;aACZ;MACD;AACD,eAAA,OAAA,OAAA,OAAA,SAAA,eAAA,OAAA,UAAA,OAAA,cAAA,eAAA,OAAA,WAAA,OAAA,UAAA,OAAA;EAED,SAAO,KAAA;AACL,WAAI;EACN;AAEA,SAAO;;UAIL;;;IC/CK,2BAAe;cACf,OAAA;AACN,SAAA,SAAA,OAAA,KAAA,gBAAA,MAAA,mBAAA,MAAA,qBAAA,MAAA,uBAAA,CAAA;EAED;;AACE,QAAA,IAAM;AACN,UAAM,kBAAiB,KAAG,OAAK,cAAO,YAAc,iBAAY,oBAAkB,KAAA,OAAA,cAAA,YAAA,mBAAA,2BAAA,KAAA,OAAA,cAAA,YAAA,0BAAA,QAAA,KAAA,OAAA,cAAA,YAAA;AAClF,QAAA;AACA,kBAAc,gBAAK,wBAAqB,IAAA;MAEpC,cAAY;MACZ,MAAA;QACF;YACE;;cAEA,KAAA,KAAA,OAAA,cAAA,gBAAA,WAAA,MAAA,GAAA,mBACH,KAAA,KAAA,OAAA,cAAA,gBAAA,WAAA,MAAA,GAAA;SAAM,aAAA,IAAA,UAAA;MACL,WAAW;aACT,CAAI;SACJ,IAAA,CAAA;MACH,OAAA;MACD,QAAM;MACN,SAAa;MAET,WAAW;QACb,MAAS;MACT;MACA;MACA,OAAO,CAAA,GAAA,GAAA;MACP,SAAQ;QACR,KAAA,WAAU,OAAA,GAAA,KAAA,WAAA,QAAA,GAAA,KAAA,aAAA,IAAA,UAAA;MACV,WAAW;aACT,CAAI;SACL,IAAA,CAAA;MACD;MACA,QAAQ;MAER,SAAS;MACR,WAAA;QAEG,MAAA;MACF;MAEA;MACF,OAAA,CAAS,GAAE,GAAA;MACX,SAAI;QACJ,KAAI,WAAW,OAAI,GAAA,KAAA,WAAA,QAAA;;YAEnB;EAAA;0BACU;UACV,UAAW,KAAA,OAAA,cAAA,YAAA;gBACL,WAAE,gBAAe,YAAA,KAAA,WAAA,aAAA,WAAA,KAAA,QACtB,WAAA,QAAA,GAAA,KAAA,OAAA,WAAA,gBAAA;;wBAEa,UAAC;UACf,UAAS,KAAK,OAAA,cAAA,YAAA;gBACb,WAAA,gBAAA,YAAA,KAAA,WAAA,aAAA,WAAA,IAAA,GACF,KAAK,WAAmB,QAAQ,GAAC,KAAA,OAAA,WAAA,gBAAA,GAAA,aAAA,aAAA,KAAA,uBAAA,GAClC,KAAK,0BAAqB,WAAA,MAAA;AAC3B,WAAA,sBAAA;IACD,GAAO,GAAA;EAEP;EACA,0BAAqB;AACnB,UAAM,UAAU,KAAK,OAAO,cAAc,YAAY;AACtD,gBAAW,WAAY,gBAAgB,YAAW,KAAE,WAAA,aAAA,WAAA,KAAA,QAClD,WAAO,QAAA,GAAA,KAAA,OAAA,WAAA,gBAAA;;0BAEO,UAAa;AAC7B,UAAK,UAAW,KAAA,OAAU,cAAA,YAAA;AAC1B,gBAAY,WAAW,gBAAe,YAAG,KAAA,WAAA,aAAA,WAAA,IAAA,GAC1C,KAAA,WAAA,QAAA,GAAA,KAAA,OAAA,WAAA,gBAAA,GAAA,aAAA,aAAA,KAAA,yBAAA,GACD,KAAA,4BAAwC,WAAA,MAAA;AACtC,WAAM,wBAAsB;IAC5B,GAAI,GAAA;;6BAEH,UAAA;AACD,UAAK,QAAA,KAAW,WAAa,UAAW,OAAM,OAAA,MAAA,CAAA,IAAA,MAAA,CAAA,GAAA,SAAA,YAAA,IAAA;AAC9C,SAAK,WAAW,aAAU,SAAA,CAAA,QAAA,SAAA,IAAA,CAAA;AAC1B,UAAK,SAAO,KAAA,WAAW,cAAkB,KAAA,WAAA;AACzC,SAAI,WAAU,gBAAA;MAEZ,GAAA,OAAA;MACA,GAAA,OAAK;;;+BAGN,WAAA;AACF,UAAA,QAAA,KAAA,WAAA,UAAA,OAAA,OAAA,MAAA,CAAA,IAAA,MAAA,CAAA,GAAA,SAAA,aAAA,IAAA;AACD,SAAA,WAAA,aAAuB,SAAA,CAAA,QAAA,SAAA,IAAA,CAAA;AACrB,UAAM,SAAO,KAAO,WAAQ,cAAc,KAAA,WAAmB;AAC7D,SAAI,WAAY,gBAAW;MACzB,GAAA,OAAO;MACR,GAAA,OAAA;IACD;;oBAEY;AACb,UAAA,sBAAA,KAAA,OAAA,aAAA,OAAA,kBAAA,oBAAA,KAAA,OAAA,aAAA,OAAA,gBAAA,cAAA,KAAA,OAAA,cAAA,aAAA,QAAA,QAAA,cAAA,SAAA,YAAA,OAAA,UAAA,QAAA,cAAA,SAAA,YAAA,SAAA,aAAA,KAAA,KAAA,KAAA,OAAA,WAAA,WAAA,UAAA,KAAA,GAAA,cAAA,KAAA,KAAA,KAAA,OAAA,WAAA,WAAA,UAAA,MAAA,GAAA,cAAA,KAAA,OAAA,iBAAA,GAAA,aAAA,KAAA,OAAA,oBAAA,GAAA,mBAAA,KAAA,OAAA,uBAAA;AACD,QAAA,aAAA,YAA0C;AACxC,YAAM,IAAO,KAAG,IAAK,aAAO,WAAc,GAAA,WAAmB,KAAC,IAAA,MAAA,aAAA,UAAA,GAAA,UAAA,YAAA;AAC1D,UAAA,QAAY;AACd,cAAO,YAAA,YAAA,KAAA,OAAA,sBAAA,UAAA,QAAA,CAAA,KAAA,OAAA,WAAA,WAAA,UAAA,KAAA,KAAA,OAAA,SAAA,KAAA,UAAA,QAAA,CAAA,KAAA,OAAA,WAAA,WAAA,UAAA,KAAA,KAAA,OAAA,QACR,KAAA,WAAA,cAAA;QACI,GAAA,KAAW,OAAA,WAAa,WAAgB,UAAC;QACzC,GAAA;QACA,OAAO;QACR,OAAU,CAAA,GAAA,QAAA;QAEZ,SAAa,aAAK;MAClB,CAAA;YACE,SAAK,KAAA,WAAA,cAA0B,KAAA,WAAA;AACjC,WAAG,WAAM,gBAAA;QACV,GAAA,OAAA;QACF,GAAA,OAAA;MACD,GAAA,aAAA,WAA2C,KAAA,WAAA,QAAA;IACzC,MAAM,MAAK,WAAQ,cAAW;MAC9B,GAAM,IAAI,CAAA,KAAG,OAAW;MACxB,GAAM,IAAA,CAAM,KAAG,OAAQ;MACnB,OAAC;MACL,SAAY;IACX,CAAA;QACC,cAAY,aAAA;AACZ,YAAG,IAAO,KAAE,IAAA,YAAA,UAAA,IAAA,KAAA,OAAA,WAAA,WAAA,UAAA,GAAA,WAAA,KAAA,IAAA,OAAA,cAAA,qBAAA,cAAA,iBAAA;AACZ,UAAA,QAAA;AACH,YAAA,UAAA,KAAA,OAAA,cAAA,YAAA;AACD,cAAA,KAAA,OAA4B,cAAkB,YAAA,YAAA,KAAA,OAAA,qBAAA,UAAA,QAAA,CAAA,KAAA,OAAA,WAAA,WAAA,UAAA,KAAA,KAAA,UAAA,QAAA,CAAA,KAAA,OAAA,WAAA,WAAA,UAAA,IAC5C,KAAM,WAAa,cAAW;QACxB,GAAI;QACJ,GAAA,oBAAuB,UAAS,IAAA,KAAA,OAAA,WAAA,WAAA,UAAA,KAAA,KAAA,OAAA;QACjC,QAAW,cAAa;QACvB,OAAS,CAAA,GAAK,QAAA;QACd,SAAmB,aAAa;MACpC,CAAC;AACD,YAAG,SAAS,KAAA,WAAA,cAAA,KAAA,WAAA;AACZ,WAAA,WAAA,gBAAA;QACH,GAAA,OAAA;QAMD,GAAe,OAAA;MACb,GAAM,aAAA,WAAsB,KAAK,WAAO,QAAa;IACrD,MAAM,MAAA,WAAiB,cAAc;MAErC,GAAM,IAAA,CAAA,KAAW,OAAO;MACxB,GAAM,IAAK,CAAA,KAAG,OAAA;MACd,QAAM;MAEN,SAAM;IACN,CAAA;AAEA,SAAA,OAAM,aAAmB,cAAO,mBAAmB,GAAA,KAAA,OAAA,aAAA,aAAA,iBAAA;;;;;AC9JjD,SAAU,SAAS,MAAgB,OAAa;AACpD,MAAI,QAAa;AACjB,SAAO,YAAqB,MAAc;AAExC,cAAU,KAAE,MAAA,MAAA,IAAA,GAAA,QAAA,WAAA,MAAA;AACV,cAAK;OACL,KAAK;;;SAwBF,gBAAO,WAAA,QAAA;QACX,aAAA,oBAAA,QAAA,QAAA,GAAA,OAAA,IAAA,KAAA,YAAA,QAAA,SAAA;AACH,MAAE,QAAA;AACH,UAAA,OAAA,KAAA,YAAA,EAAA,SAAA,GAAA,SAAA,KAAA,SAAA,IAAA,GAAA,SAAA,EAAA,SAAA,GAAA,GAAA,GAAA,MAAA,KAAA,QAAA,EAAA,SAAA,EAAA,SAAA,GAAA,GAAA;AAyCK,WAAA,UAAU,UAAgB,SAAmB,OAAe,QAAA,QAAA,IAAA,GAAA,QAAA,MAAA,KAAA,GAAA,QAAA,MAAA,GAAA;EAChE;AACA,SAAM;;AAGN,SAAU,WAAY,MAAA,QAAY;AAClC,QAAI,OAAQ,KAAA,YAAA,EAAA,SAAA,GAAA,SAAA,KAAA,SAAA,IAAA,GAAA,SAAA,EAAA,SAAA,GAAA,GAAA,GAAA,MAAA,KAAA,QAAA,EAAA,SAAA,EAAA,SAAA,GAAA,GAAA;OACV,UAAU,UAAQ,SAAW,OAAG,QAAW,QAAA,IAAA,GAAA,QAAA,MAAA,KAAA,GAAA,QAAA,MAAA,GAAA,GAAA,SAAA,IAAA;AAC3C,UAAM,OAAK,KAAI,SAAK,EAAQ,SAAQ,EAAA,SAAW,GAAA,GAAQ,GAAE,SAAO,KAAA,WAAA,EAAA,SAAA,EAAA,SAAA,GAAA,GAAA,GAAA,SAAA,KAAA,WAAA,EAAA,SAAA,EAAA,SAAA,GAAA,GAAA;AAChE,cAAS,UAAQ,SAAU,OAAQ,QAAG,MAAY,IAAI,GAAC,QAAA,MAAA,MAAA,GAAA,QAAA,MAAA,MAAA;;SAEvD;;SAGD,aAAA,WAAA,QAAA;AACD,QAAA,YAAY,OAAA,QAAA,MAAA,GAAA,aAAA,OAAA,QAAA,IAAA,GAAA,WAAA,OAAA,QAAA,IAAA,GAAA,iBAAA,aAAA,YAAA,aAAA,WAAA,IAAA,IAAA,aAAA,WAAA,IAAA,GAAA,eAAA,WAAA,YAAA,WAAA,aAAA,IAAA,IAAA,WAAA,aAAA,IAAA,GAAA,OAAA,SAAA,UAAA,YAAA,aAAA,YAAA,WAAA,IAAA,IAAA,aAAA,WAAA,IAAA,CAAA,GAAA,EAAA,GAAA,QAAA,SAAA,UAAA,cAAA,GAAA,EAAA,IAAA,GAAA,MAAA,SAAA,UAAA,YAAA,GAAA,EAAA;AACb,MAAA,MAAA,IAAA,KAAA,OAAA,EAAA,QAAA;AAED,MAAM,MAAA,KAAU,KAAA,QAAqB,KAAE,QAAc,GAAA,QAAA;AACnD,QAAM,cAAY,CAAA,IAAA,WAAc,IAAQ,IAAG,KAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,EAAA;AAC3C,SAAM,EAAA,MAAS,GAAA,KAAK,MAAQ,KAAM,MAAE,YAAW,KAAU;;SAIzD,aAAgB,WAAY,QAAS;AACrC,MAAA,OAAS,SAAO,IAAQ,KAAI,OAAO,SAAA,IAAA,KAAA,OAAA,SAAA,IAAA,GAAA;AAC/B,UAAM,YAAY,OAAE,QAAA,IAAA,IAAA,KAAA,OAAA,QAAA,IAAA,IAAA,OAAA,QAAA,IAAA,GAAA,OAAA,SAAA,UAAA,SAAA,GAAA,EAAA,GAAA,SAAA,SAAA,UAAA,YAAA,CAAA,GAAA,EAAA,GAAA,SAAA,UAAA,SAAA,YAAA,IAAA,SAAA,UAAA,YAAA,CAAA,GAAA,EAAA,IAAA;AACtB,QAAA,MAAU,IAAG,KAAK,OAAQ,KAAG,OAAQ,GAAG,QAAS;AACjD,QAAA,MAAM,MAAS,KAAK,SAAU,KAAG,SAAW,GAAA,QAAU;AACtD,QAAA,MAAM,MAAS,KAAK,SAAU,KAAG,SAAW,GAAA,QAAU;;SAGtD;;SAIF,WAAc,MAAA;AACf,SAAA,OAAA,KAAA,KAAA,OAAA,OAAA,KAAA,OAAA,OAAA;AAGD;AAGE,SAAM,gBAAoB,YAAc;AACxC,QAAM,UAAQ,CAAG,cAAe,cAAM,cAAA,cAAA,cAAA,cAAA,cAAA,YAAA,GAAA,aAAA,CAAA,YAAA,OAAA,GAAA,SAAA,aAAA,WAAA,KAAA,GAAA,MAAA,GAAA,GAAA,OAAA,MAAA,CAAA,GAAA,OAAA,MAAA,CAAA;AACtC,MAAA,mBAAsB;AACtB,MAAA,KAAM,UAAA,IAAiB,GAAA,IAAA,QAAa,QAAU,KAAG;AACjD,UAAM,SAAY,QAAG,CAAA,GAAQ,YAAc,KAAE,MAAQ,aAAc,MAAM,CAAC,GAAG,UAAS,aAAc,WAAQ,MAAA;AAE5G,QAAM,MAAO,UAAS,UAAU,SAAA;AAC1B,0BAAiB;AACjB;IAGF;;MAEH,qBAAA,KAAA,UAAA,IAAA,GAAA,IAAA,WAAA,QAAA,KAAA;AAGG,UAAM,SAAM,WAAa,CAAA,GAAI,YAAY,KAAA,MAAA,aAAA,MAAA,CAAA,GAAA,cAAA,OAAA,MAAA,aAAA,MAAA,CAAA;AAC3C,QAAA,aAAa,WAAA,MAAA,KAAA,UAAA,WAAA,YAAA,QAAA;AACd,0BAAA;AAIK;IACF;;SAEH,QAAA,QAAA,qBAAA,oBAAA,oBAAA,MAAA,oBAAA,QAAA,CAAA,OAAA,oBAAA;;AAKH,SAAS,aAAa,QAAA;AAEpB,QAAI,aAAe,OAAM,MAAI,QAAO;MAClC,YAAM;AACN,UAAM,oBAAgB,WAAU,IAAU,OAAK,OAAC,CAAA,EAAA,KAAA,GAAA;AAChD,WAAM,IAAA,OAAS,mBAAmB,GAAA;;SAGlC;;SAII,oBAAuB,UAAQ,MAAS;kBACnC,QAAM,gBAAA,CAAA,OAAA,QAAA;UACd,OAAA,IAAA,MAAA,GAAA;AAED,QAAI,QAAM;eACD,KAAK,MAAC;AACd,UAAA,CAAA,MAAA,eAAA,CAAA,GAAA;AACF,gBAAA;AAEW;MACb;AAEQ,cAAW,MAAY,CAAA;IAE9B;AACD,WAAA;EA2BD,CAAM;;SAGF,WAAY,KAAA;SACZ,MAAA,QAAY,GAAA,IAAA,MAAA,IAAA,SAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,IAAA,CAAA,CAAA,IAAA,MAAA,IAAA,SAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,IAAA,CAAA,CAAA,IAAA,MAAA,IAAA,SAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,IAAA,CAAA,CAAA,IAAA,CAAA,KAAA,KAAA,KAAA,GAAA;;SAGZ,cAAY,aAAA;QACZ,cAAY,IAAA,KAAA,YAAA,YAAA,GAAA,GAAA,CAAA;SACZ,KAAA,OAAY,YAAA,QAAA,IAAA,IAAA,YAAA,QAAA,KAAA,QAAA,KAAA,CAAA;;AAGd,SAAU,WAAa,YAAQ,SAAA,QAAA;AAC/B,QAAM,OAAK,CAAG,UAAW,UAAU,WAAC,aAAA,YAAA,UAAA,UAAA,GAAA,OAAA,IAAA,KAAA,UAAA;AACpC,SAAM,YAAa,SAAG,KAAA,KAAA,OAAA,CAAA,EAAA,OAAA,GAAA,CAAA,IAAA,WAAA,SAAA,KAAA,KAAA,OAAA,CAAA,IAAA;;AAGtB,SAAI,mBAAkB,KAAA,MAAA;AACtB,QAAI,aAAM,OAAA,yBAAA,KAAA,IAAA;SACR,CAAA,CAAK,eAAe,CAAA,CAAA,WAAQ,OAAW,SAAE,WAAA;;SAGvC,qBAAgB,SAAa,gBAAmB,OAAA;;eAE9C;eACA,IAAM,KAAA,OAAA,GAAA,YAAA,OAAA,SAAA;UACP,QAAA,SAAA,GAAA,QAAA,iBAAA,KAAA,SAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AACF,WAAA,SAAA,GAAA,GAAA,GAAA,CAAA;IACF;EACD,MAAI,QAAA,oBAAA;SACF,iBAAU,KAAA,SAAA,GAAA,GAAA,GAAA,CAAA,GAAA;;SAGN,uBAAuB,SAAM,iBAAoB,OAAE;;eAEnD;eACA,IAAI,KAAO,OAAI,GAAA,YAAgB,OAAK,SAAY;kBAC9C,SAAA,GAAiB,QAAG,kBAAO,KAAA,WAAA,IAAA,IAAA,GAAA;WAE5B,WAAA,IAAA,IAAA,GAAA;;QAEJ,QAAA,oBAAA;SACF,kBAAA,KAAA,WAAA,IAAA,IAAA,GAAA,GAAA;;AAGA,SAAA,uBAAA,SAAA,iBAAA,OAAA;AACD,MAAI;MACF,SAAO;AACR,QAAA,OAAA,IAAA,KAAA,OAAA,GAAA,YAAA,OAAA,SAAA;AACM,UAAA,QAAA,SAAsB,GAAA,QAAA,kBAAA,KAAA,WAAA,IAAA,GAAA,GAAA;AAC9B,WAAA,WAAA,IAAA,GAAA;IAGD;EAEE,MAAM,QAAA,oBAAU;AAChB,SAAI,kBAAY,KAAA,WAAA,IAAA,GAAA,GAAA;;AAIf,SAAA,4BAAA,SAAA,sBAAA,OAAA;AACD,MAAA;AACD,MAAA,SAAA;AAEK,QAAA,OAAU,IAAA,KAAA,OAAmB,GAAC,YAA2B,OAAA,SAAA;AACvD,UAAM,QAAG,SAAS,GAAQ,QAAA,uBAA+B,KAAA,gBAAA,GAAA,GAC7D;AACI,WAAK,gBAAQ,GAAA;IAEjB;gBACM,oBAAM;gCACS,KAAA,gBAAA,GAAA,GAAA;;SAEjB,qBAAc,SAAA,gBAAA,OAAA;;eAEf;QACF,OAAA,IAAA,KAAA,OAAA,GAAA,YAAA,OAAA,SAAA;AAED,UAAO,QAAM,SAAA,GAAA,QAAA,iBAAA,KAAA,SAAA,IAAA,IAAA,IAAA,GAAA,GACZ;AACI,WAAM,SAAC,IAAA,IAAA,IAAA,GAAA;IACf;EAED,MAAM,QAAU,oBAAA;AACd,SAAK,iBAAkB,KAAE,SAAA,IAAA,IAAA,IAAA,GAAA,GAAA;;AAGzB,SAAQ,qBAAc,WAAA,MAAA,WAAA,MAAA;MACpB,UAAQ,IAAM,KAAa,IAAG;UAC/B,WAAA;IACD,KAAO;AACL,cAAQ,gBAAkC,GAAG;AAC9C;SAEC;AACD,cAAA,WAAA,IAAA,GAAA;AAED;IAGI,KAAA;AAEJ,cAAM,WAAc,IAAI,IAAK,GAAA;AAE7B;IAED,KAAA;AAEK,cAAU,SAAA,IAAW,IAAA,IAAyB,GAAE;AACpD;IAGA,KAAI;AACF,YAAA,kBAAuB,IAAI,QAAQ,OAAK;AACzC,cAAA,QAAA,QAAA,QAAA,IAAA,eAAA,GAAA,QAAA,SAAA,IAAA,IAAA,IAAA,GAAA;;IAEA,KAAA;AACD,YAAO,iBAAA,IAAA,KAAA,QAAA,YAAA,GAAA,QAA0D,SAAA,IAAA,GAAA,CAAA,EAAA,QAAA;AAClE,cAAA,QAAA,cAAA,GAAA,QAAA,SAAA,IAAA,IAAA,IAAA,GAAA;AAEK;IAEJ,KAAK;AAEH,YAAA,eAAa,QAAA,SAAA,GAAA,oBAAA,eAAA,eAAA,IAAA,GAAA,mBAAA,IAAA,KAAA,QAAA,YAAA,GAAA,oBAAA,GAAA,CAAA,EAAA,QAAA;AACd,cAAA,SAAA,mBAAA,gBAAA,GAAA,QAAA,SAAA,IAAA,IAAA,IAAA,GAAA;AAGD;IAII,KAAA;AACA,cAAK,SAAA,IAAA,EAAA,GAAA,QAAA,SAAA,IAAA,IAAA,IAAA,GAAA;AACL;;YAGA,IAAI,MAAQ,oBAAa;;wCAEA,SAAK,WAAA,WAAA,MAAA,CAAA,GAAA,cAAA,KAAA,KAAA,KAAA;oBAC3B,OAAA;iBAED,cAAY,SAAA,MAAA,aAAA,KAAA,aAAA,KAAA,YAAA,WAAA,KAAA,YAAA,UAAA,KAAA,UAAA,WAAA,IAAA;aACb,QAAA,QAAA,IAAA,CAAA;YACG,WAAW;MAChB,KAAA;AACF,gBAAA,QAAA,QAAA,QAAA,IAAA,MAAA,UAAA;AAAM;MAEN,KAAA;AACG,gBAAA,QAAe,QAAA,QAAA,IAAA,MAAA,UAAA;AACb;MAEN,KAAW;AACZ,gBAAA,QAAA,QAAA,QAAA,IAAA,MAAA,QAAA;AACK;MAEA,KAAO;AACL,gBAAO,QAAK,QAAS,QAAA,IAAA,MAAA,OAAA;AACrB;;gBAGE,QAAK,QAAW,QAAQ,IAAI,MAAC,QAAA;;WAIhC;AACD,kBAAK,IAAA,KAAa,QAAS,YAAE,GAAA,QAAA,SAAA,IAAA,IAAA,KAAA,MAAA,GAAA,GAAA,CAAA,GAC9B,QAAA,SAAA,IAAA,IAAA,IAAA,GAAA;AACF;MACC,KAAO;AACR,cAAA,eAAA,QAAA,SAAA,GAAA,oBAAA,eAAA,eAAA,IAAA;AACG,kBAAc,IAAE,KAAA,QAAA,YAAA,GAAA,oBAAA,KAAA,MAAA,IAAA,GAAA,IAAA,GAAA,CAAA,GACd,QAAC,SAAe,IAAI,IAAK,IAAA,GAAA;AAC9B;MAEF,KAAA;AAEK,gBAAU,YAAA,QAAuB,YAAkC,IAAA,KAAA,MAA0B,GAAK,CAAA,GAAA,QAAA,SAAA,IAAA,IAAA,IAAA,GAAA;AAC9F;MAEN;AACI,cAAA,IAAO,MAAO,oBAAe;;;;;SAO9B,uBAAA,MAAA,WAAA,qBAAA,UAAA;oBACI,IAAA,KAAa,IAAK;UACxB,WAAA;IACF,KAAA;gBAAM,gBAAA,CAAA;AACL;IAEF,KAAI;AACF,gBAAK,WAAe,GAAI,CAAC;AAC1B;IAEF,KAAA;AAEK,gBAAU,WAAA,GAAA,GAAA,CAAA;AAIV;SAEF;AACA,gBAAI,SAAc,GAAA,GAAK,GAAA,CAAA;;;kBAIlB,UAAA,OAAA;8BAEW;0BACb,aAAA,qBAAA,MAAA,MAAA,KAAA,IAAA,MAAA,CAAA,eACI,QAAA,UAAmB,QAAE,IAAA,iBAAA,GAAA,UAAA,SAAA,GAAA,GAAA,GAAA,CAAA;;SAEvB;AACL,gBAAO,QAAW,CAAA,GAAA,UAAA,SAAA,GAAA,GAAA,GAAA,CAAA;AACnB;SAEC;AACD,YAAA,eAAA,UAAA,SAAA,GAAA,sBAAA,eAAA,eAAA;AACD,gBAAY,SAAA,qBAAA,CAAA,GAAA,UAAA,SAAA,GAAA,GAAA,GAAA,CAAA;AACb;IAGC,KAAI;AACA,gBAAS,SAAA,GAAA,CAAA,GAAA,UAAA,SAAA,GAAA,GAAA,GAAA,CAAA;AACX;;gBAGI,MAAI,oBAAe;;;;SAMjB,wBAAsB,MAAK,WAAA,WAAA,MAAA,SAAA,GAAA;;QAElC,gBAAA,IAAA,KAAA,KAAA,QAAA,IAAA,MAAA;UAAM,WAAA;SACL;AACD,oBAAA,cAAA,QAAA,IAAA,UAAA,QAAA,KAAA;AACG;IAEH,KAAA;AACD,oBAAY,cAAA,QAAA,IAAA,UAAA,QAAA,KAAA;AACb;IAQC,KAAI;AAEJ,oBAAiB,cAAE,QAAA,IAAA,UAAA,QAAA,KAAA;AACjB;;AAGA,oBAAa,cAAA,QAAA,IAAA,UAAA,QAAA,KAAA;;SAGb;oBACU,cAAe,QAAS,IAAA,UAAA,QAAA,KAAA;;;mBAI1B,MAAA,cAAA,YAAA,IAAA,UAAA,YAAA,MAAA,cAAA,SAAA,IAAA,UAAA,SAAA,IACR,eAAW,cAAA,QAAA,IAAA,UAAA,QAAA,KAAA,IAAA,KAAA,cAAA,YAAA,GAAA,cAAA,SAAA,IAAA,GAAA,CAAA,EAAA,QAAA;;;mBAID,KAAA,cAAmB,YAAO,IAAA,UAAA,YAAA,KAAA,KAAA,MAAA,cAAA,SAAA,IAAA,CAAA,IAAA,KAAA,MAAA,UAAA,SAAA,IAAA,CAAA,kBAC5B,cAAA,QAAA,IAAA,UAAA,QAAA,MAAA,IAAA,IAAA,KAAA,cAAA,YAAA,GAAA,cAAA,SAAA,IAAA,GAAA,CAAA,EAAA,QAAA;AACR;;mBAGU,cAAe,YAAW,IAAA,UAAA,YAAA,GAAA,eAAA,cAAA,SAAA,IAAA,UAAA,SAAA,KAAA;;;YAIlC,IAAM,MAAA,oBAAoB;;sBAElB;;;;AC9dZ,SAAU,YAAY,OAAM,OAAQ,OAAA,eAAA,MAAA;MAElC,EAAA,QAAgB,OAAA,IAAA;QAChB,YAAW,MAAA,WAAA,SAAA,QAAA,SAAA;QACZ,CAAA,iBAAA,eAAA,IAAA,iBAAA,QAAA,QAAA;IACD,YAAO;IACH,UAAA;GAIH;AAED,sBAAmB,MAAE,cAAA,MAAA,OAAA,mBAAA,eAAA,SACnB,WAAM,mBAA0B,wBAAoB,IAAA,IAAA,oBAAiB,MAAA,aAAA,MAAA,OAAA,iBAAA,eAAA,SACrE,WAAM,mBAAW,sBAAmB,IAAA,IAAwB,gBAAM,MAAA,sBAAA,SACnE,eAAA,WAAA,MAAA,OAAA,cAAA,sBAAA,KAAA,IAAA,MAAA,KAAA,KAAA,IAAA,MAAA,KAAA,MAAA,UAAA,qBAAA,QAAA,KAAA,KAAA,KAAA,IAAA,MAAA,KAAA,KAAA,IAAA,MAAA,KAAA,MAAA,UAAA,uBAAA,QAAA,KAAA,MAAA,MAAA,eAAA;;SAGC,iBAAiB,GAAA,GAAA,OAAA;MAClB,IAAA;AACD,QAAA,QAAY,KAAI,IAAM,IAAA,CAAA,GAAA,SAAA,SAAwB,MAAA,IAAA,GAAA,SAAA,QAAA,IAAA,IAAA;AAC9C,SACE,CAAK,KAAC,KAAA,UAAU,UAAA,KAAA,MAAA,eAAA,WAAA,KAAA,KAAA,EAAA,GAAA,KAAA,KAAA,UAAA,UAAA,KAAA,MAAA,aAAA,WAAA,KAAA,KAAA,EAAA,CAAA;;SAGb,qBAAwB,QAAK,OAAO;SAEvC,KAAM,MAAA,OAAc,iBAAG,IAAA,MAAA,OAAA,WAAA,WAAA,CAAA,cAAA,QAAA,KAAA,KAAA,CAAA,iBAAA,QAAA,KAAA;;AAe3B,SAAS,uBAA8D,QAAA,OAAA;;;SAKrE,cAAe,QAAU,OAAS;AAClC,SAAM,MAAM,MAAG,OAAQ,iBAAc,IAAA,MAAA,OAAA,WAAA,UAAA,UAAA,KAAA,MAAA,OAAA,iBAAA;;AAKvC,SAAM,iBAAU,QAAA,OAAqB;AACnC,QAAM,cAAc,MAAM,OAAO,iBAAgB,IAAK,MAAM,OAAO,WAAW;AAC9E,SAAI,MAAA,eAAmB,UAAA,KAAA,KAAA,IAAA,MAAA,OAAA,iBAAA,WAAA,IAAA;;SAGvB,eAAQ,QAAoB,OAAO;AACpC,SAAA,MAAA,MAAA,OAAA,oBAAA,IAAA,MAAA,OAAA,WAAA,SAAA,UAAA,KAAA,MAAA,OAAA,mBAAA;AAED;SAEM,gBAAgB,QAAE,OAAA;QACpB,aAAa,MAAA,OAAA,oBAAA,IAAA,MAAA,OAAA,WAAA;SACd,MAAA,cAAA,UAAA,KAAA,KAAA,IAAA,MAAA,OAAA,mBAAA,UAAA,IAAA;;SA8CE,sBAAA,cAAA;AACH,QAAC,QAAA,aAAA,QAAA,eAAA,MAAA,cAAA,aAAA,MAAA;AACD,aAAO,mBAAA,WAAA,iBAAA,eAAA,OAAA;;MACL,WAAY,mBAAW,WAAA,iBAAA,eAAA,OAAA;AACvB,eAAM,mBAAqB,wBAAS;MACpC,WAAW,mBAAQ,WAAA,iBAAA,cAAA,OAAA;AACnB,iBAAU,qBAAwB,iBAAc,aAAA,WAAA,mBAAA,sBAAA;MAChD,WAAM,mBAAqB,WAAO,iBAAA,cAAA,OAAA;AAClC,iBAAM,qBAA4B,iBAAA,aAAA,WAAA,mBAAA,wBAAA;MAClC,WAAW,mBAAA,WAAA,iBAAA,eAAA,OAAA;AACX,MAAA,gBAAW;MACX,WAAY,mBAAkB,WAAE,iBAAA,cAAA,OAAA;eACvB,OAAG,aAAM,SAAA;iBACT,mBAAc,WAAe,iBAAW,aAAA,MAAA;eAChD,OAAA,aAAA,YAAA,OAAA,aAAA,qBAAA,iBAAA,aAAA,aAAA,uBAAA,iBAAA,OAAA;MACD,WAAY,mBAAkB,WAAE,iBAAA,oBAAA,MAAA;iBACvB,qBAAS,iBAAA,aAAA,aAAA,uBAAA,iBAAA,OAAA;iBACT,mBAAc,WAAe,iBAAW,YAAA,OAAA;eAChD,OAAA,aAAA,YAAA;MACD,WAAK,mBAAY,WAAA,iBAAA,eAAA,OAAO;AACxB,MAAA,gBAAa;iBACN,mBAAgB,WAAA,iBAAA,eAAA,OAAA;sBACd;MACR,WAAA,mBAAA,WAAA,iBAAA,cAAA,OAAA;AACD,eAAK,OAAW,aAAI,SAAA,MAAA,aAAA,qBAAA,iBAAA,aAAA,aAAA,uBAAA,iBAAA,SAAA;MACpB,WAAW,mBAAa,WAAA,iBAAA,aAAA,MAAA;AACxB,iBAAW,qBAAa,iBAAA,aAAA,aAAA,uBAAA,iBAAA,OAAA;MAExB,WAAK,mBAAW,WAA0B,iBAAc,oBAAO,MAAA;AAChE,iBAAA,qBAAA,iBAAA,aAAA,aAAA,uBAAA,iBAAA,OAAA;EACD,CAAA,GAAA,WAAU,mBAAA,WAAA,iBAAA,YAAA,OAAA;AACR,eAAA,OAAA,aAA0B,YAAU;;AAEtC,QAAC,wBAAA,SAAA,aAAA,yBAAA,EAAA,GAAA,0BAAA,SAAA,aAAA,2BAAA,EAAA;AACD,aAAA,mBAAkB,WAAA,iBAAA,cAAA,OAAA;AAChB,eAAS,OAAK,aAAS,WAAA,WAAA,OAAA,aAAA,YAAA,OACxB,aAAA,qBAAA,iBAAA,aAAA,aAAA,uBAAA,iBAAA,SAAA;AACF,UAAA,QAAA,EAAA,OAAA,MAAA,CAAA,KAAA,IAAA,EAAA,OAAA,MAAA,CAAA,IAAA,EAAA,OAAA,MAAA,CAAA;AAEK,0BAAU,OAAqB,CAAC;EACpC,CAAA,GAAA,WAAc,mBAAoB,WAAA,iBAAA,cAAA,OAAA;AAClC,eAAM,OAAe,aAAM,WAAa,WAAA,OAAA,aAAA,YAAA,OACxC,aAAgB,gBAAS,MAAW,aAAA,qBAAA,iBAAA,aAAA,aAAA,uBAAA,iBAAA,SAAA;AAGpC,UAAU,QAAC,EAAA,OAAA,MAAmB,CAAA,KAAA,IAAW,EAAA,OAAA,MAAiB,CAAA,IAAA,EAAA,OAAa,MAAW,CAAE;AAClF,4BAAW,KAAkB;EAC/B,CAAE;;;;ACvJJ,IAAM,SAAA,eAAU,OAA2B,UAAY,WAAA,OAAA;AAIrD,SAAO,gBAAa,GAAA,OAAA;AACrB,QAAA,QAAA,IAAA,MAAA,cAAA,gBAAA,MAAA,aAAA,OAAA,iBAAA;AACD,SAAM,KAAU,MAAA,gBAA2B,MAAY,cAAA,SAAA;;AAGrD,SAAM,gBAAsB,GAAE,OAAM;AACpC,QAAA,SAAO,IAAU,MAAA,aAAA,OAAA,kBAAA,oBAAA,MAAA,gBAAA,CAAA;AAClB,SAAA,KAAA,OAAA,SAAA,qBAAA,MAAA,cAAA,gBAAA,IAAA;AAED;AACE,SAAK,iBAAU,UAAA;MACb,IAAA,IAAO,IAAG,IAAA;SACX,WAAA,SAAA,WAAA,CAAA;IACG,MAAA,qBAAmB,EAAA,mBAAA;IACrB,kBAAO;cACL;;iBAEE;iBACA;;qBAEE,QAAW,IAAK,SAAA,IAAA,CAAA,MAAA,UAAA;kBAChBC,KAAAC;;YAEH,KAAA;MACD,kBAAA,KAAA;MACH,UAAA,UAAAC,MAAA,KAAA,aAAA,WAAAA,MAAAA,MAAA;MAAM,OAAU;QACR,YAAa,UAAOC,MAAO,KAAE,UAAA,WAAAA,MAAA,SAAAA,IAAA,cAAA;;QAClC,UAAO,UAAAF,MAAA,KAAA,UAAA,WAAAA,MAAA,SAAAA,IAAA;;;;mBAIE;sBACL,UAAW,KAAA,SAAU,qBAAA,WAAA,MAAA;wBACrB,KAAW,SAAI,aAAM,WAAA,KAAA,KAAA;;kBAEtB,UAAA,KAAA,SAAA,UAAA,WAAA,KAAA,SAAA,GAAA,cAAA;kBACD,UAAA,KAAA,SAAA,UAAA,WAAA,KAAA,SAAA,GAAA,cAAA;MACD,UAAA,UAAA,KAAA,SAAA,UAAA,WAAA,KAAA,SAAA,GAAA;IACJ;EACD,CAAA,IAAA,CAAO;;SAGH,2BAAmB,aAAuB;;UAE1C,QAAO,cAAA,SAAA,YAAA,aAAA,QAAA,cAAA,SAAA,YAAA,sBAAA,YAAA,QAAA,cAAA,SAAA,YAAA,sBAAA,EAAA,QAAA,cAAA,SAAA,YAAA,sBAAA,YAAA,QAAA,cAAA,SAAA,YAAA,WAAA,IAAA,UAAA,KAAA,QAAA,cAAA,SAAA,YAAA,UAAA,WAAA,KAAA,KAAA;;SAGL,yBAAkC,aAAK;;UAE1C,QAAA,cAAA,SAAA,YAAA,aAAA,QAAA,cAAA,SAAA,YAAA,oBAAA,YAAA,QAAA,cAAA,SAAA,YAAA,oBAAA,EAAA,QAAA,cAAA,SAAA,YAAA,oBAAA,YAAA,QAAA,cAAA,SAAA,YAAA,WAAA,IAAA,UAAA,KAAA,QAAA,cAAA,SAAA,YAAA,UAAA,WAAA,KAAA,KAAA;;AAKH,SACE,YAAW,OAAX;MACA,IAAC,IAAA,IAAA,IAAW,IAAA,IAAA,IAAX,IAAA,IAAA,IAAW,IAAA,IAAA,IAAA,IAAA,IAAX,IAAA,IAAA,IAAa,IAAA,IAAA,IAAA,IAAA,IAAiB,IAAI,IAAA,IAAA,IAAW,IAAA,IAAA,IAAX,IAAA,IAAA,IAAW,IAAA,KAAA,KAAA,KAAA,KAAX,KAAA,KAAa,KAAA,KAAA,KAAA,KAAiB,KAAK,KAAO,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA,KAAA;QAC3E,UAAA,MAAW;QAEb,cAAS,gBAAA,UAAA,KAAA,QAAA,UAAA,SAAA,QAAA,kBAAA,WAAA,KAAA,KAAA,cAAA,sBACV,cAAA,aAAA,UAAA,KAAA,QAAA,UAAA,SAAA,QAAA,eAAA,WAAA,KAAA,KAAA,GACD,MAAA,cAAO,YAAW,UAAX,KAAA,QAAW,UAAA,SAAX,QAAW,cAAO,WAAA,KAAA,KAAA,IAC1B,MAAA,cAAA,mBAAA,UAAA,KAAA,UAAA,KAAA,QAAA,UAAA,SAAA,QAAA,mBAAA,WAAA,KAAA,SAAA,GAAA,aAAA,WAAA,KAAA,KAAA,IAED,MAAM,cAAU,iBAAyB,UAAA,KAA0B,UAAA,KAAA,QAAA,YAAA,WAAA,KAAA,SAAA,GAAA,mBAAA,WAAA,KAAA,KAAA,+KACjE,MACE,cAAW,gBAAX,UAAW,KAAA,UAAA,KAAX,QAAW,YAAS,WAAA,KAAA,SAAA,GAAA,kBAAA,WAAA,KAAA,KAAA;QACnB,EAAA,MAAA,aAAW,aAAA,KAAA,IAAX,MAAA,cAAa,4BAA8B,CAAA;QAC1C,cAAW,WAAX,QAAA,UAAW,SAAA,QAAX,WAAa,uBAAmB,IAAA,KAAW,QAAA,OAAX,GAAA,aAAW,WAAA,IAAA,cAE7C,cAAS,WAAA,QAAA,UAAA,SAAA,QAAA,WAAA,qBAAA,MAAA,cAAA,SAAA,IAAA,KAAA,QAAA,OAAA,GAAA,aAAA,IAAA,IAAA,cACV,cAAA,eAAA,UAAA,KAAA,MAAA,cAAA,YAAA,WAAA,KAAA,SAAA,GAAA,QAAA,GACD,MAAA,cAAO,eAAW,UAAX,KAAW,MAAA,cAAA,YAAA,WAAO,KAAA,SAAA,GAAA,QAAA,GAC1B,MAAA,cAAA,qBAAA,UAAA,KAAA,QAAA,UAAA,SAAA,QAAA,uBAAA,WAAA,KAAA,KAAA,QAED,MAAS,cAAS,0BAAA,UAAA,KAAA,QAAA,UAAA,SAAA,QAAA,4BAAA,WAAA,KAAA,KAAA,QAElB,MAAM,cAAU,cAAwB,OAAA,OAAA,CAAA,GAAA;;IACtC,mBAAsB;IACtB,0BAAoB;IACpB,OAAM;IACN,SAAM;IACN,SAAM;IACN,WAAM;EACN,GAAA,QAAM,UAAc,SAAA,QAAe,WAAA,GAAA,MAAQ,cAAO,oCAAE,UAAY,KAAA,QAAA,UAAA,SAAA,QAAI,mBAAU,WAAA,KAAA,SAAA,GAAA,gBAC9E,MAAM,cAAc,kCAAgB,UAAe,KAAA,QAAA,UAAA,SAAA,QAAA,mBAAe,WAAA,KAAA,SAAA,GAAA,cAWlE,MAAM,cAAQ,gCAAmC,UAAM,KAAA,QAAc,UAAA,SAAA,QAA+B,mBAAA,WAAA,KAAA,SAAA,GAAA,iBACpG,MAAM,cAAc,2BAAiB,CAAA,GAAP,MAAA,cAAO,uBAAE,CAAA;WACnC,IAAA,GAAA,UAAA,KAAuB,IAAI,MAAK,cAAgB,qBAAe,WAAY,WAAA,MAAA,IAAA,KAAA;AAC7E,UAAE,QAAU,MAAA,cAAA,qBAAA,CAAA,EAAA;AACd,UAAM,cAAc,qBAAiB,KAAA,OAAP,OAAO;MACjC,UAAA;MACA,YAAU;MACR,WAAa;MACb,cAAc;MACd,OAAA;MACA,iBAAc;IACpB,GAAM,KAAA,CAAA,GAAA,MAAc,cAAc,yBAEhC,KAAA,UAAA,KAAA,UAAA,KAAA,MAAA,cAAA,qBAAA,CAAA,EAAA,cAAA,WAAA,KAAA,KAAA,QAAA,UAAA,SAAA,QAAA,oBAAA,WAAA,KAAA,KAAA,EAAA;;QAEE,cAAA,OAAmB,OAAA,OAAA,CAAA,GAAA,QAA0B,UAAA,SAAA,QAAA,IAAA,SAC7C,cAAA,eAA2B,OAAA,OAAA,CAAA,GAAA;IAC3B,UAAS;IACT,mBAAiB;IACjB,OAAO,IAAE,MAAI,cAAA,YAAA;IACb,cAAW;IAEb,aAAO;IAGT,YAAM;IACN,UAAM;EACN,GAAA,UAAM,KAAA,QAAc,UAAA,SAAA,QAAgC,YAAO,WAAA,KAAP,SAAO,GAAA,QAAA,GAC3D,MAAM,cAAc,aAAA,QAAA,UAA8B,SAAA,QAAA,YAClD,MAAM,cAAc,oBAAoB,OAAM,OAAA;IAC9C,iBAAgB;eACR,KAAQ,QAAM,UAAA,SAAc,QAAA,YAAwB,WAAM,KAAA,SAAA,GAAA,aAAA,SAChE,cAAM,oBAAc,UAClB,KAAA,UACE,KAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,KAAA,SAAA,GAAA,eAAA,WAAA,MAAA,wBACc,uBAAA,OAAA,OAAA;gBACZ;mBACW;mBACX;iBACO,MAAM,cAAA,aAAA;iBACb,MAAe,cAAQ,aAAA;qBAI3B;eAEI,KAAA,QAAc,UAAA,SAAA,QAA6B,YAC/C,WAAM,KAAA,SAAc,GAAA,gBAAuB,SAE9C,cAAA,mBAAA,UAAA,KAAA,UAAA,KAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,KAAA,SAAA,GAAA,cAAA,WAAA,KAAA,KAAA,IACD,MAAM,cAAc,kBAAc,UAahC,KAAO,UAAA,KAAP,QAAO,UAAA,SAAP,QAAS,YACT,WAAA,KAAA,SAAA,GAAA,aAAA,WAAA,MAAA,IACF,MAAM,cAAc,mBAAe,UACjC,KACA,UAAA,KAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,KAAA,SAAA,GAAA,cAAA,WAAA,MAAA,UACE,cAAgB,mBAAA,UAAA,KAAA,UAAA,KAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,KAAA,SAAA,GAAA,cAAA,WAAA,MAAA,UAEhB,cAAA,oBAAyB;IAEzB,YAAQ,UAAM,MAAc,UAAS,MAAK,UAAI,MAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,mBAAA,WAAA,MAAA,SAAA,IAAA,eAAA,WAAA,MAAA,MAAA;IAE9C,UAAA,UAAe,MAAA,UAAA,MAAA,UAAA,MAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,mBAAA,WAAA,MAAA,SAAA,IAAA,aAAA,WAAA,MAAA,MAAA;IAEf,OAAA,UAAc,MAAA,UAAA,MAAA,UAAA,MAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,mBAAA,WAAA,MAAA,SAAA,IAAA,UAAA,WAAA,MAAA,MAAA;IAGd,WAAU,UAAS,MAAA,UAAA,MAAA,UAAA,MAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,mBAAA,WAAA,MAAA,SAAA,IAAA,cAAA,WAAA,MAAA,MAAA;IACnB,cAAY,UAAA,MAAA,UAAA,MAAA,UAAA,MAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,mBAAA,WAAA,MAAA,SAAA,IAAA,iBAAA,WAAA,MAAA,MAAA;IAEd,SAAA,UAAO,MAAP,UAAO,MAAA,UAAA,MAAP,QAAS,UAAO,SAAA,QAAA,YAAA,WAAE,MACnB,SAAC,IAAA,mBAAA,WAAA,MAAA,SAAA,IAAA,YAAA,WAAA,MAAA,MAAA,CAAA,GAAA,GAAA,GAAA,EAAA;IAEF,cAAM,UAAc,MAAa,UAAO,MAAA,QAAP,UAAO,SAAA,QAAP,YAAS,WAAW,MAAA,SAAA,IAAA,mBAAA,WAAA,MAAA,SAAA,IAAA;EACrD,GAAA,MAAM,cAAc,sBAAoB,UACtC,MAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,oBACE,cAAe,mBAAE,UAAuB,MAAA,UAAA,MAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,sBAAA,WAAA,OAAA,WAE1C,cAAO,6BAAA,OAAA,OAAP;IAEF,WAAM;IACN,WAAM;IAEF,UAAU,CAAE,GAAC,CAAA;IACb,cAAa;IACb,iBAAgB;eAChB,MAAa,UAAM,MAAA,QAAc,UAAa,SAAQ,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,qBAAA,WAAA,MAAA,SAAA,IAAA,WAAA,SACtD,cAAa,8BAAiC,UAAQ,MAAA,UAAA,MAAA,QAAA,UAAA,SAAA,QAAA,YAAA,WAAA,MAAA,SAAA,IAAA,qBAAA,WAAA,MAAA,SAAA,IAAA,oBACtD,cAAe,kBAAG,OAAA,OAAA;IAEpB,aAAO;IAET,iBAAoB;IACpB,cAAM;EACN,GAAA,UAAM,MAAa,QAAC,UAAgB,WAAG,MAAA,SAAO,IAAA,eAAA,GAC9C,MAAM,cAAc,WAAA,iBAAmB,QAAA,UAAO,SAAP,QAAO,QAAA,GAI9C,UAAM,MAAA,UAAc,MAAA,MAAiB,cAAG,aAAA,WAAA,MAAA,SAAA,IAAA,WAAA,WAAA,OAAA,SAAA,UAAA,MAAA,MAAA,cAAA,aAAA,WAAA,MAAA,SAAA,IAAA,MAAA,UAAA,WAAA,KAAA,gBAAA,OAAA,MAAA,cAAA,SAAA,CAAA,EAAA,mBAAA,kBACtC,MAAY,MAAA,cAAA,aAAO,WAAP,MAAO,SAAA,IAAA,KAAA,UAAA,KAAE,gBAAO,OAAA,MAAA,cAAA,uBAAgB,uBAAA,IAAA,KAAA,UAAE,MAAU,MAAA,cAAA,aAAA,WAAW,MAAA,SAAA,IAAA,KAAA,UAAA,KAAA,gBAAA,EAAA,IAAA,GAAA,aAAA,WAAA,WACnE,cAAU,6BAAO,UAAP,MAAO,QAAA,UAAA,WAAE,MAAO,SAAA,IAAA,kCAC1B,cAAO,oBAAO,OAAA,OAAP;IACP,WAAW,UAAA,MAAA,MAAA,cAAO,oBAAA,WAAA,MAAP,SAAS,IAAO;IAC3B,WAAA,UAAc,MAAA,MAAA,cAAO,oBAAA,WAAA,MAAA,SAAE,IAAA;eACd,MAAA,QAAA,UAAA,WAAO,MAAP,SAAO,IAAA,iBAAP,SACT,cAAc,sBAAO,UAAP,MAAA,QAAO,UAAA,WAAP,MAAS,SAAO,IAAA,2BAC9B,cAAA,4BAAA,UAAA,MAAA,QAAA,UAAA,WAAA,MAAA,SAAA,IAAA,2BACF,MAAM,cAAc,eAAA,UAAsB,MAAA,QAAO,iBAAA,WAAA,MAAA,MAAP,MAC1C,MAAM,cAAc,kBAAgB,UAAG,MAAA,QAAO,eAAP,WAAO,MAAA,SAAP,IAAA,OACvC,MAAM,cAAc,0BAA0B,UAAU,MAAM,UAC5D,MAAA,QAAA,eAAA,WAAA,MAAA,SAAA,IAAA,kBAAA,WAAA,OAAA,WACE,cAAW,2BAAiB,UAAA,MAAA,UAAA,MAAA,QAAA,eAAA,WAAA,MAAA,SAAA,IAAA,mBAAA,WAAA,OAAA,WAC5B,cAAY,0BAAA,OAAA,OAAA;IACZ,WAAW;IACX,WAAA;eACA,MAAe,QAAQ,eAAA,WAAA,MAAA,SAAA,IAAA,aAAA,SAEzB,cAAA,kCAAO,OAAA,OAAP;IAEF,YAAM;IAEN,cAAM;IAEF,aAAa,MAAA,cAAS,wBAAA;IACtB,WAAA,MAAiB,cAAC,wBAAA;IAClB,WAAA,MAAe,cAAA,wBAAA;KAEjB,UAAA,MAAQ,QAAK,UAAA,SAAA,QAAA,eAAE,WAAA,MACf,SAAA,IAAA,qBAAA,GACF,MAAM,cAAc,qCAAmC,OAAA,OAAP;IAC5C,aAAA;IACF,WAAI;YACF;iBACD;eACG,MAAA,QAAM,UAAc,SAAQ,QAAA,eAAA,WAAA,MAAA,SAAO,IAAO,oBAAM,uBAC5C,uCAAqC,OAAA,OAAsB;iBAKlE;IACF,WAAA;IACD,QAAM;IAEN,aAAM;eAEO,MAAE,QAAK,UAAC,SAAc,QAAA,eAAe,WAAA,MAAA,SAAA,IAAA,sBAAa,SAC3D,cAAW,kCAAoB,OAAe,OAAA;IAEhD,WAAA;IAEF,WAAM;IACN,UAAM,CAAA,GAAa,CAAC;EAEpB,GAAA,UAAM,MAAa,QAAC,UAAe,SAAA,QAAQ,eAAY,WAAA,MAAA,SAAA,IAAI,qBAAK;;AAGhE,SAAM,8BAAc,OAA2B;AAC/C,MAAA,IAAM,IAAA,IAAA,IAAc;QAEhB,UAAW,MAAK,SAAA,EAAA,MAAA,aAAA,aAAA,KAAA,IAAA,MAAA,cAAA,4BAAA,CAAA;QAChB,cAAY,UAAA,uBAAA,IAAA,KAAA,MAAA,cAAA,OAAA,GAAA,aAAA,WAAA,SAEd,cAAQ,UAAU,qBAAA,MAAA,cAAA,SAAE,IAAA,KACpB,MAAA,cAAA,OAAA,GAAA,aAAA,IAAA,GAEF,MAAM,cAAc,eAAA,UAAA,KAAA,MAAkC,cACpD,YAAA,WAAA,KAAA,SAAA,GAAA,QAAA,SACE,cAAa,eAAA,UAAA,KAAA,MAAA,cAAA,YAAA,WAAA,KAAA,SAAA,GAAA,QAAA,SACb,cAAe,2BAAA,CAAA,GAAA,MAAA,cAAA,uBAAA,CAAA;WACf,IAAA,GAAW,UAAQ,KAAA,IAAA,MAAc,cAAA,qBAAiC,WAAA,WAAA,MAAA,IAAA,KAAA;AAClE,UAAA,QAAW,MAAM,cAAc,qBAAwB,CAAA,EAAA;AACvD,UAAA,cAAiB,qBAAc,KAAA,OAAA,OAAwB;MAEzD,UAAO;MAGH,YAAc;MAEhB,WAAa;MACb,cAAkB;MAClB,OAAS;MACT,iBAAc;IAEhB,GAAA,KAAA,CAAA,GAAA,MAAO,cAAP,yBAAA,KAAA,UAAS,KAAU,UAAA,KAAA,MAAA,cAAA,qBAAE,CAAA,EAAA,cACrB,WAAA,KAAA,KAAA,QAAA,UAAA,SAAA,QAAA,oBAAA,WAAA,KAAA,KAAA,EAAA;EACF;;SAIY,qBAAC,aAAA,SAAA,OAAA;MACT;QAEF,EAAA,MAAO,MAAP,OAAO,IAAA,OAAA,gBAAE,CAAA;AAEX,SAAM,cAAc,UAAA,KAAA,UAAA,MAA+B;AAE/C,UAAA,OAAW,YAAK,YAAA,GAAA,QAAA,YAAA,SAAA,GAAA,MAAA,YAAA,QAAA,GAAA,MAAA,qBAAA,IAAA,KAAA,MAAA,OAAA,MAAA,OAAA,CAAA,GAAA,IAAA;AAChB,QAAA,QAAY,IAAA,QAAA,QAAA,KAAA,IAAA,QAAA,QAAA,QAAA,CAAA;AACZ,UAAA,QAAc,aAAE,eAAA,QAAA,SAAA,SAAA,OAAA;MAElB,WAAO;MAEV,WAAA;MACK,SAAU;;MACR,MAAO,KAAG,IAAM,IAAA,QAAQ,IAAA,YAAA,QAAA,IAAA,CAAA,IAAA;MAEtB,WAAM;MACR,SAAA;MACA;MAMA,MAAA;MACA,OAAA;MACA,WAAa;IACnB;AACA,kBAAgB,KAAC,aAAS,GAAA,cAAc,IAAA,KAAA,MAAqB,OAAM,MAAA,IAAA;aAC3D,YAAa,MAAC;AACpB,UAAM,OAAA,YAAc,YAAA,GAAoB,QACtC,YACE,SAAA,GAAA,MAAA,qBAAA,IAAA,KAAA,MAAA,QAAA,MAAA,CAAA,GAAA,IAAA;QACE,QAAQ,IAAI,QAAA,QAAA,KAAA,IAAA,QAAA,QAAA,QAAA,CAAA;UACZ,QAAU,aAAQ,eAAA,QAAA,SAAA,SAAA,OAAA;MAClB,WAAW,QAAQ;MACnB,WAAA;MACA,SAAO;YACP,QAAA,GAAe,SAAQ,GAAA,gBAAA;MAEzB,MAEH,KAAC,IAAA,IAAA,QAAA,IAAA,YAAA,QAAA,IAAA,CAAA,IAAA;MAEF,WAAM;MAGP;MACF,MAAA;MAEK,SAAU;;MACN,WAAU,QAAQ;IAC1B;AACA,kBAAkB,KAAG,aAAS,GAAA,cAAA,IAAA,KAAA,MAAA,QAAA,MAAA,CAAA;aACxB,cAAgB,MAAA;UAClB,OAAM,YAAO,YAAY,GAAW,UAAG,KAAA,MAAA,YAAA,SAAA,IAAA,CAAA,GAAA,MAAA,qBAAA,IAAA,KAAA,MAAA,KAAA,UAAA,OAAA,CAAA,GAAA,IAAA;QACvC,QAAM,IAAK,QAAG,QAAY,KAAA,IAAW,QAAA,QAAA,QAAA,CAAA;UACrC,QAAS,aAAc,eAAW,QAAA,SAAA,SAAA,OAAA;MAClC,WAAS,UAAG;MACZ,WAAQ;eACF;YACL,UAAA,GAAA,SAAA,GAAA,gBAAA;MACD,MAAM,KAAK,IAAG,IAAA,QAAY,IAAA,YAAA,QAAA,IAAA,CAAA,IAAA;MAC1B,WAAM;MACN;MACA,MAAM;eACA;aACJ;iBACO,UAAK;;kBAEN,KAAK,aAAA,GAAA,cAAA,IAAA,KAAA,MAAA,KAAA,UAAA,OAAA,CAAA;wBACJ,MAAA;iBACP,YAAc,YAAA,GAAA,MAAA,qBAAA,IAAA,KAAA,OAAA,OAAA,GAAA,IAAA,EAAA,GAAA,IAAA;gBACd,IAAA,QAAA,QAAA,KAAA,IAAA,QAAA,QAAA,QAAA,CAAA;UACF,QAAA,aAAmB,eAAe,QAAA,SAAA,SAAA,OAAA;MAElC,WAAW;MACZ,WAAA;eAAc;WACb,KAAM,SAAO,GAAA,gBAAuB;MACpC,MAAM,KAAK,IAAG,IAAA,QAAY,IAAA,YAAW,QAAA,IAAA,CAAA,IAAA;MACrC,WAAS;MACT,SAAQ;;YAEP;MACD,OAAM;MACN,WAAM;;kBAEA,KAAA,aAAgB,GAAA,cAAA,IAAA,KAAA,OAAA,MAAA,GAAA,CAAA;wBACV,MAAK;+BACC,UAAA,KAAA,MAAA,gBAAA,WAAA,KAAA,KAAA;oBACZ,YAAA,OAAA;iBACA,uBAAS,YAAA,MAAA,YAAA,IAAA,YAAA;wBACD,qBAAA,WAAA,GAAA,UAAA,qBAAA,YAAA,QAAA,IAAA,MAAA,IAAA,OAAA,aAAA,KAAA,KAAA,MAAA,GAAA,IAAA;cACZ,WAAO,QAAW,QAAA,QAAA,QAAA,CAAA;uBACP,cAAS,WAAA,GAAA,eAAA,QAAA,SAAA,SAAA,OAAA;iBACpB;MAEF,WAAA;MACA,SAAA;KACD,MAAA,WAAA,SAAA,GAAA,gBAAA;aAAU,QAAS,QAAS,IAAE,YAAA,QAAA,IAAA,KAAA;MAC7B,WAAU;MACV,SAAM;MACN;MACA,MAAI;aACC;iBACJ;;kBAEK,KAAA,aAAsB,GAAA,YAAN,QAAM,qBAAN,YAAW,QAAW,IAAA,MAAW,IAAE,OAAS,aAAS,KAAS,KAAM,KAAC,IAAA,EAAA,QAAA,CAAA;aAC3F,WAAM,MAAc;UACpB,OAAM,YAAa,YAAG,GAAA,QAAA,YAAA,SAAA,GAAA,MAAA,YAAA,QAAA,GAAA,OAAA,YAAA,SAAA,GAAA,MAAA,uBAAA,IAAA,KAAA,MAAA,OAAA,KAAA,OAAA,OAAA,CAAA,GAAA,IAAA;gBAChB,IAAE,QAAS,QAAI,KAAS,IAAG,QAAA,QAAY,QAAc,CAAA;kBACzD,aAAgB,eAAA,QAAA,SAAA,SAAA,OAAA;iBACZ;iBACE;eACN;gBACA,SAAO,GAAA,gBAAW;YAClB,KAAA,IAAW,IAAA,QAAW,IAAA,YAAA,QAAA,IAAA,CAAA,IAAA;iBACtB;MACF,SAAA;MACA;MACD,MAAA;aAAU;MACT,WAAU,YAAG,SAAY;;kBAEjB,KAAO,aAAa,GAAA,cAAW,IAAA,KAAA,MAAA,OAAA,KAAA,OAAA,IAAA;0BACzB,MAAA;UACb,OAAA,YAAA,YAAA,GAAA,QAAA,YAAA,SAAA,GAAA,MAAA,YAAA,QAAA,GAAA,OAAA,YAAA,SAAA,GAAA,SAAA,YAAA,WAAA,GAAA,MAAA,uBAAA,IAAA,KAAA,MAAA,OAAA,KAAA,MAAA,SAAA,OAAA,CAAA,GAAA,IAAA;QACD,QAAM,IAAK,QAAG,QAAY,KAAA,IAAA,QAAA,QAAA,QAAA,CAAA;UAC1B,QAAM,aAAgB,eAAM,QAAN,SAAM,SAAA,OAAN;MACtB,WAAM;MACN,WAAM;eACA;kBACJ,SAAW,GAAK,gBAAA;YAChB,KAAO,IAAE,IAAG,QAAA,IAAA,YAAA,QAAA,IAAA,CAAA,IAAA;iBACR;eACA;;YAEJ;aACA;MACF,WAAA,YAAmB,WAAe;;kBAEnC,KAAA,aAAA,GAAA,cAAA,IAAA,KAAA,MAAA,OAAA,KAAA,MAAA,SAAA,IAAA;aAAM,aAAa,MAAQ;UAC1B,OAAM,YAAA,YAAqB,GAAA,QAAM,YAAW,SAAA,GAAA,MAAA,YAAI,QAAS,GAAA,OAAA,YAAA,SAAA,GAAA,SAAA,YAAA,WAAA,GAAA,SAAA,YAAA,WAAA,GAAA,MAAA,4BAAA,IAAA,KAAA,MAAA,OAAA,KAAA,MAAA,QAAA,SAAA,OAAA,CAAA,GAAA,IAAA;QACzD,QAAI,IAAA,QAAY,QAAY,KAAA,IAAS,QAAA,QAAA,QAAA,CAAA;UACrC,QAAI,aAAkB,eAAe,QAAA,SAAA,SAAA,OAAA;iBACnC;iBACD;MACD,SAAM;WAEN,OAAM,SAAU,GAAA,gBACd;MAGF,MAAI,KAAO,IAAG,IAAA,QAAS,IAAA,YAAA,QAAA,IAAA,CAAA,IAAA;iBACd;eACR;MAKD;MAEA,MAAM;MAGN,OAAM;iBACG,YAAQ,WAAY;;kBAG3B,KAAS,aAAO,GAAA,cAAA,IAAA,KAAA,MAAA,OAAA,KAAA,MAAA,QAAA,SAAA,IAAA;;;;SAKhB,WAAA,SAAA,WAAA,cAAA,OAAA,QAAA;UAEF,IAAA,IAAA,IAAA,IAAc,IAAK,IAAA,IAAA;cAInB,UAAY,KAAO,QACjB,CAAA,MAAA,WAAqB,KAAA,KAAY;cAEpC,aAAA,UAAA,YAAA,QAAA,QAAA,KAAA,UAAA,KAAA,QAAA,CAAA,MAAA,WAAA,KAAA,KAAA,MAAA,aAAA,cAAA,QAAA,KAAA,QAAA,KAAA,UAAA,KAAA,QAAA,CAAA,MAAA,WAAA,KAAA,KAAA,OAAA,UAAA,KAAA,QAAA,CAAA,MAAA,WAAA,KAAA,KAAA,OAAA;cAAM,KAAQ,UAAK,KAAQ,QAAA,CAAA,MAAA,WAAA,KAAA,KAAA;sBAChB,gBAAe,iBAAc,gBAAA,kBAAA,eAAA,QAAA,SAAA,KAAA,UAAA,KAAA,QAAA,CAAA,MAAA,WAAA,KAAA,KAAA,MAAA,aAAA,iBAAA,QAAA,KAAA,SAAA,KAAA,UAAA,KAAA,QAAA,CAAA,MAAA,WAAA,KAAA,KAAA,OAAA,UAAA,KAAA,QAAA,CAAA,MAAA,WAAA,KAAA,KAAA,OAAA,KAAA,UAAA,KAAA,QAAA,CAAA,MAAA,WAAA,KAAA,KAAA;;;;;SAOtC,gBAAA,UAAA;qBACU,OAAG,aAAY,WAAA,SAAA,QAAA,KAAA,EAAA,GAAA,WAAA,WAAA,QAAA,gBAC1B,MAAM,YAAgB,MAAM,KAAA,MAAA,QAAA;;SAG1B,6BAA4B,OAAG;;mCAEnB;oCACR,SAAA,cAAA,KAAA,GAAA,MAAA,wBAAA,MAAA,WAAA,0CACQ,MAAA,MAAA,MAAA,SAAA,MAAA,MAAA,wBAAA,MAAA,QAAA,MAAA,iBAAA,MAAA,iBAAA,IAAA,MAAA,cAAA,kBAAA,YAAA,IAAA,KAAA,oCACM,MAAA,QAAA,QAAA,MAAA,wBAAA,MAAA,SAAA,MAAA,aAAA,oCACI,MAAC,kBAAU,iBAAA,MAAA,wBAAA,MAAA,SAAA,aACjC,cAAA,8BAAA,MAAA,wBAAA,MAAA,SAAA,qBACF,wBAAmB,MAAa,aAAE,QAAA,MAAA,wBAAA,MAAA,UAAA;UAClC,oBAAsB,SAAO,cAAgB,KAAG;QACjD,kBAAA,MAAA,WAAA,YAAA,kBAAA,MAAA,MAAA,yBAAmB,MAAA,QAAU,KAAA,MAAA,cAAA,kBAAA,aAAA,IAAA,wBACf,MAAA,QAAY,MAAA,cAAc,kBAAA,YAAA,wBACzB,MAAA,SAAY,QAAW,kBAAA,MAAA,kBAAA,MAAA,cAAA,kBAAA,6BACzB,MAAA,SAAY,OAAU,kBAAA,MAAA,aAAA,0BACrB,MAAA,gBAAuB,QAAA,kBAAA,MAAA,aAAA,+BACpC,wBAA0B,YAAW,iBAAG,GAAA,MAAA,cAAA,4BAAA;AACxC,YAAM,gBAAM,SAAA,cAAgC,KAAM;AAClD,oBAAQ,MAAY,WAAQ,YAAW,cAAA,MAAA,MAAA,OAAA,cAAA,MAAA,QAAA,UAAA,KAAA,KAAA,MAAA,cAAA,2BAAA,cAAA,WAAA,KAAA,KAAA,KAAA,IAAA,oBACjC,MAAQ,SAAQ,UAAW,KAAA,MAAA,cAAA,2BAAA,cAAA,WAAA,KAAA,KAAA,KAAA,oBAChC,MAAA,SAAA,QAAA,cAAA,MAAA,kBAAA,MAAA,cAAA,2BAAA,WACD,cAAc,MAAA,SAAY,OAAA,cAAA,MAAA,SAAA,cAAA,cAAA,MAAA,aAAA,QAC1B,cAAM,MAAa,gBAAS,QAAN,cAAM,MAAA,UAAN,KAAW,cAAW,MAAQ,aAAW,yBAC/D,MAAM,wBAAc,YAAiB,aAAkB;;oBAE/C,YAAa,MAAO,uBAAiB;;;SAI3C,6BAAc,OAAA;;qCAES;UACvB,wBAAA,MAAA,WAAA,YAAA,MAAA,wBAAA,MAAA,MAAA,MAAA,SAAA,YACF,wBAAmB,MAAa,OAAE,MAAA,iBAAA,MAAA,iBAAA,IAAA,MAAA,cAAA,kBAAA,YAAA,IAAA,OAAA,aAClC,wBAAuB,MAAM,QAAU,QAAQ,MAAM,wBAAS,MAAA,SAAA,MAAA,aAAA,YAC/D,wBAAA,MAAA,kBAAA,iBAAA,MAAA,wBAAA,MAAA,SAAA,aAAM,cAAa,8BAAU,MAAA,wBAAA,MAAA,SAAA,qBAC5B,wBAAyB,MAAA,aAAc,QAAA,MAAA,wBAAA,MAAA,UAAA;UACvC,oBAAc,MAAY,wBAAW,WAAA,CAAA;QACrC,kBAAY,MAAY,WAAU,YAAA,kBAAA,MAAA,MAAA,yBACrB,MAAA,QAAY,KAAQ,MAAG,cAAA,kBAAA,aAAA,IAAA,wBACxB,MAAG,QAAY,MAAA,cAAa,kBAAA,YAAA,wBAC5B,MAAG,SAAY,QAAU,kBAAG,MAAA,kBAAA,MAAA,cAAA,kBAAA,6BAC5B,MAAA,SAAA,OAAA,kBAAyC,MAAO,aAAa,0BACjE,MAAY,gBAAe,QAAI,kBAAA,MAAA,aAAA,uDACjB,WAAW,CAAA,GAAA;YAChC,gBAAA,MAAA,wBAAA,WAAA,CAAA;AACD,oBAAc,MAAA,WAAY,YAAA,cAAA,MAAA,MAAA,OAAA,cAAA,MAAA,QAAA,UAAA,KAAA,KAAA,MAAA,cAAA,2BAAA,cAAA,WAAA,KAAA,KAAA,KAAA,IAAA,MAC1B,cAAM,MAAa,SAAS,UAAA,KAAN,MAAM,cAAA,2BAAgB,cAAQ,WAAkB,KAAA,KAAS,KAAM,MACrF,cAAM,MAAc,SAAA,QAAiB,cAAO,MAAW,kBAAA,MAAA,cAAA,2BAAA,WACvD,cAAM,MAAa,SAAG,OAAA,cAAA,MAAA,SAAA,cAAA,cAAA,MAAA,aAAA,sBACV,MAAK,gBAAgB,QAAA,cAAqB,MAAQ,UAAQ,KAAA,cAAA,MAAA,aAAA;;;;SAKpE,oBAAkB,SAAA,cAAA,SAAA,gBAAA,YAAA;;WAEnB,IAAC,GAAA,IAAA,QAAA,QAAA,KAAA;QACF,MAAA,QAAc,OAAK,KAAA,MAAa,QAAE,UAAA,QAAA,CAAA,EAAA,YAAA,MAAA,QAAA,CAAA,KAAA,QAAA,CAAA,EAAA,YAAA,MAAA,QAAA,QAAA;MAClC,QAAA,QAAc,CAAA;MACf,OAAA,CAAA,CAAA;IACF;AACD,QAAO,UAAA,KAAc,QAAA,CAAA,EAAA,aAAA,MAAA,WAAA,KAAA,SAAA,GAAA;AAAA,UAAA,MAAA,QAAA,OAAA,KAAA,QAAA,CAAA,MAAA,QAAA,CAAA,EAAA,YAAA,GAAA;AACtB,cAAA,SAAA,oBAAA,QAAA,CAAA,EAAA,aAAA,GAAA,cAAA,QAAA,MAAA,CAAA,CAAA;AAMK,YAAU,OAAA,QACd,OACA,MAAA,QACA,CAAA,GAAA;;AAII,cAAQ,SAAA,oBAAU,QAAA,CAAA,EAAA,aAAO,GAAA,cAAA,OAAA;AACzB,YAAA,OAAc,QAAO,OAAI,MAAS,QAAU,CAAA,GAAE;MAChD;;;;AAGD,SAAA,qBAAA,SAAA,gBAAA,YAAA;AACD,MAAI;AACJ,WAAI,IAAA,GAAY,IAAA,QAAK,QAAY,IAAA,EAAA,UAAiB,KAAA,QAAY,CAAI,EAAA,aAAY,MAAK,WAAe,KAAA,SAAA,GAAA,UAAA,qBAAA,QAAA,CAAA,EAAA,aAAA,GAAA,aAAA,KAAA,OAAA,QAAA,CAAA,EAAA,8BAChG,QAAQ,CAAA,EAAM;;SAEN,qBAAmB,SAAA,gBAAU,YAAA;MACtC;AAED,WAAO,IAAA,GAAA,IAAA,QAAA,QAAA,IAAA,EAAA,UAAA,KAAA,QAAA,CAAA,EAAA,aAAA,MAAA,WAAA,KAAA,SAAA,GAAA,UAAA,qBAAA,QAAA,CAAA,EAAA,aAAA,GAAA,aAAA,IAAA,OAAA,QAAA,CAAA,EAAA;;AAGL,SAAA,qBAAA,GAAA,OAAA;AACH,MAAA,YAAA;AAED,MAAM,MAAA,uBAA0B;AAE1B,UAAA,UAAe,MAAK,sBAAU,eAAA,IAAA,MAAA,YAAA;AAChC,QAAA,SAAW;AAEX,YAAQ,EAAG,IAAA,IAAW;AACvB,mBAAA,MAAA,MAAA,sBAAA;AAGG,YAAQ,mBAAO,MAAA,qBAAA,GAAA,aAAA,CAAA;AACjB,uBAAmB,KAAO,OAAA,IAAA,oBAAA,MAAA,cAAA,SAAA;IAC3B;EAGD,MAAO,cAAW,KAAA,MAAU,IAAA,MAAA,cAAA,SAAA;AAC7B,SAAA;IAEK;;EAoBJ;;SAGQ,uCAAyC,OAAO,QAAQ;MAC9D;cACG,YAAM,MAAe,OAAO,SAAC,OAAc,QAAO,OAAM,UAAa,KAAC,OAAA,aAAkB,WAAiB,KAAC,SAAA,GAAA,YAAA,OAAA,SAAA,CAAA,EAAA,yBAAA;QAE7G,iBAAM,OAAA,SAA6B,MAAM,EAAA,KAAG,CAAM,GAAC,MAAA;AACnD,UAAM,EAAA,WAAA,WAAA,IAAwB,gCAAkC,OAAK,CAAA,GAAA,EAAA,WAAA,WAAA,IAAA,gCAAA,OAAA,CAAA;AACrE,WAAM,WAAA,QAAA,IAAwB,WAAM,QAAA;MACpC,OAAM,CAAA;WACD,IAAC,GAAA,KAAA,eAAc,SAAA,GAAA,KAA6B;AACjD,UAAM,YAAA,eAAwB,CAAA,GAAM,EAAA,WAAoB,QAAA,IAAA,gCAAA,OAAA,SAAA;AACxD,QAAA,SAAM;AAEN,aAAM,IAAA,GAAA,IAAA,KAAiB,QAAG,IAAS,KAAA,UAAc,QAAO,IAAA,KAAA,CAAA,GAAA;AACxD,WAAA,CAAA,IAAA,QAAkB,QAAM,GAAQ,SAAG,MAAW,UAAA,yBAAA;AAC9C;IACA;AACA,eAAA,KAAA,KAAkB,QAAW,QAAQ,CAAC,GAAA,UAAa,yBAAmB,KAAS,SAAQ;;SAEvF,KAAA;;SAGA,cAAA,WAAwB,SAAA,UAAgB,OAAO;SAE/C,SAAA,KAAkB,aAAM;AACxB,UAAM,EAAA,WAAA,YAAwB,SAAA,SAAY,IAAA,gCAAmB,OAAA,OAAA;AAE7D,WAAI,aAAM,YAAc,cAAA;;;SAItB,6BAA+B,OAAE,QAAS;;cAE1C,YAAc,MAAM,OAAS,SAAO,OAAA,QAAA,OAAA,UAAA,KAAA,OAAA,aAAA,WAAA,KAAA,SAAA,GAAA,YAAA,OAAA,SAAA,CAAA,EAAA,yBAAA;eAEpC,CAAA;WACA,IAAA,GAAA,KAAa,OAAO,SAAS,SAAA,GAAY,KAAC;UAC1C,YAAc,OAAM,SAAU,CAAA,GAAG,EAAA,WAAO,QAAA,IAAA,gCAAA,OAAA,SAAA;QACxC,SAAA;aACA,IAAA,GAAA,IAAc,KAAM,QAAO,KAAO;AAClC,UAAA,CAAA,cAAmB,WAAW,SAAG,OAAA,SAAA,OAAwB,OAAA,MAAA,aAAA,EAAA,2BAAA,CAAA,GAAA,KAAA,GAAA;AACzD,aAAM,CAAA,IAAA,QAAA,QAAwB,GAAA,SAAY,MAAA,UAAc,yBAAC;AAC1D;MACA;IACF;AACF,eAAA,KAAA,KAAA,QAAA,QAAA,CAAA,GAAA,UAAA,yBAAA,KAAA,SAAA;EAED;;;SAkCI,gCAAsC,OAAA,QAAc;QACpD,EAAA,sBAAwC,gBAAA,aAAA,IAAA,MAAA,eAAA,YAAA,OAAA,cAAA,GAAA,UAAA,OAAA,YAAA;SACxC,uBAAwB;IACxB,WAAA,qBAA8B,SAAS;IACvC,SAAA,qBAAwB,OAAa;MACrC;IAEA,WAAA,qBAAwB,WAAa,IAAA;IAErC,SAAS,qBAAC,SAAwB,IAAA;;;;;AC9tBnC,IAAA,iBAAA,MAAA;EACD,YAAS,OAAA;;;cAED;QACJ,IAAI,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;UACH,EAAE,QAAC,MAAA,IAAA,MAAA,aAAA,IAAA,MAAA;MACJ,GAAA;MACA,GAAA;MACA,OAAM,MAAI,OAAA,oBAAA;MACV,QAAQ,MAAE,OAAK,uBAAA;MAId,MAAA;MACC,UAAS;IACb,CAAA;AACA,SAAK,QAAC,YAAmB,WAAW,OAAE,yBAAA,MAAA,WAAA,SAAA,KAAA,KAAA;AACtC,UAAM,EAAA,MAAM,SAAS,KAAW,IAAA,MAAM,OAAO,cAAc,4BAA+B,CAAA;AAC1F,QAAI,IAAI;AACR,aAAS,IAAI,GAAG,IAAI,MAAM,OAAO,qBAAqB,KAAK;AACzD,YAAM,YAAY,IAAI,MAAM;QAC1B,GAAG;QACH;QACA,OAAO,MAAM,OAAO,oBAAmB;QACvC,QAAQ,MAAM,OAAO,cAAc,yBAAyB,CAAC;QAC7D,MAAM;OACP;AACD,WAAK,UAAU,UAAU,QAAO,UAAA,OAAA,cAAA,WAAA,SAAA,SAAA;AAChC,YAAA,EAAA,eAA8B,aAAA,IAAA,MAAA,OAAA,cAAA,qBAAA,CAAA;AAC9B,eAAA,IAAW,GAAA,KAAS,QAAS,gBAAE,SAAA,cAAA,SAAA,KAAA;AAE/B,cAAQ,EAAA,MAAe,SAAiB,WAAa,OAAc,WAAwB,KAAA,IAAA,cAAA,CAAA,GAAA,IAAA,KAAA,KAAA,wBAAA,WAAA,MAAA,OAAA,cAAA,SAAA,SAAA,IAAA,IAAA,MAAA,OAAA,cAAA,gBAAA,GAAA,QAAA,KAAA,KAAA,wBAAA,SAAA,MAAA,OAAA,cAAA,SAAA,SAAA,MAAA,CAAA,IAAA,MAAA,OAAA,cAAA,gBAAA,IAAA,GAAA,OAAA,IAAA,MAAA;UACtF;UACH,GAAM;UACN;UAEI,QAAM,UAAO,UAAc;UAE/B,MAAM;UAEF,MAAM,MAAM,OAAC,cAAc;QAE/B,CAAA;AAOA,YAAA;AACA,aAAA,OAAU;YACR,oBAAC;cACA,SAAG,UAAA,UAAA;YACJ,cAAK;AACL,cAAA;AACA,cAAI,cAAO,OAAA,cAAA;AACP,8BAAe,aAAc;cAChC;cACS;cACR,OAAc;cACd;cAEQ;cACR;cACE;cACA;cACF,eAAY,KAAA,OAAA;;mCAEJ;8BACE,gBAAA,gBAAA,eAAA,oBAAA,UAAA,KAAA,gBAAA,sBAAA,WAAA,MAAA,kBACR,OAAS,2BAAA,iBAAA,KAAA,YAAA,aAAA;;+BAEL;2BACK,WAAA,cAAA,cAAA,UAAA,YAAA,OAAA,aAAA,UAAA,IAAA,MAAA,OAAA,cAAA,qBAAA,CAAA,GAAA,WAAA,WAAA,WAAA,OAAA,GAAA,WAAA,cAAA,OAAA,MAAA,GAAA,OAAA,IAAA,KAAA;wBACJ;wBACL;0BACA;YACF,aAAA;YACD,UAAA;kBAAM,MAAA,eAAA;YACL;YACD;YACG,MAAA;YAIF,QAAA;YACA,WAAA;YACA;YACD;YACD,UAAa,WAAS,eAAY,KAAA,eAAe,eAAA,QAAA,gBAAA,YAAA,IAAA,eAAA;UAClD,CAAA;AACG,eAAA,UAAA,YAAmB,WAAA,KAAA,YAAA,IAAA,GAAA,KAAA,OAAA;;YAarB,UAAM,SAAW,IAAA,GAAA,IAAW,GAAA;AAC5B,gBAAM,OAAO,WAAS;YACpB,UAAG;YACH,QAAG,UAAU,KAAA,MAAA,OAAA,cAAA,oCAAA,WAAA,KAAA,SAAA,GAAA;YACb,WAAA,UAAmB,KAAA,MAAA,OAAA,cAAA,oCAAA,WAAA,KAAA,SAAA,GAAA;YACnB,QAAA,CAAA;cAEA,GAAA,KAAU,UAAI,KAAA,MAAA,OAAA,cAAA,oCAAA,WAAA,KAAA,SAAA,GAAA,aAAA,MAAA;cACV,GAAE;YACN,GAAA;cAEA,GAAA,KAAY,UAAU,KAAA,MAAA,OAAA,cAAA,oCAAA,WAAA,KAAA,SAAA,GAAA,aAAA,MAAA;cAClB,GAAE,UAAK,UAAA;YACX,CAAA;;eAEA,YAAS,IAAA;;;;qBAKJ,WAAC;;4BAEA,KAAQ,MAAA,OAAa,cAAA,sCAAA,WAAA,KAAA,SAAA,GAAA;+BACrB,KAAA,MAAY,OAAA,cAAA,sCAAA,WAAA,KAAA,SAAA,GAAA;;YAEjB,GAAA;YACE,GAAC,KAAA,UAAkB,KAAY,MAAA,OAAU,cAAA,sCAAA,WAAA,KAAA,SAAA,GAAA,aAAA,MAAA;UAC9C,GAAA;YACI,GAAC,MAAO,OAAA,oBAAwB;YACrC,GAAA,KAAA,UAAA,KAAA,MAAA,OAAA,cAAA,sCAAA,WAAA,KAAA,SAAA,GAAA,aAAA,MAAA;UACD,CAAA;QAEA,CAAA;kBACQ,SAAO,IAAA;;;;;4BAKN,KAAE,CAAA;;;4BAGH,KAAG,CAAA;;;oBAGN;4BACE,SAAgB,UAAE,KAAA,UAAA,KAAA,KAAA,MAAA,cAAA,WAAA,KAAA,SAAA,GAAA,UAAA,WAAA,KAAA,KAAA,CAAA,cACxB,aAAA,UAAA,UAAA,KAAA,UAAA,KAAA,KAAA,MAAA,cAAA,WAAA,KAAA,SAAA,GAAA,WAAA,WAAA,KAAA,KAAA,CAAA;;YAGH;;mBAEI,KAAQ,UAAO,WAAA,MAAA,GAAA,OAAA,YAAA,KAAA,KAAA,kBACf;;;;;AC3JR,IAAA,mBAAA,cAAyC,MAAA;cACjC,OAAO;AACd,UAAA,KAAA;EACF;;;;ICsBC,qBAA6B;IACtB,2BAAe;IAGf,gBAAM;cACN,OAAQ;SACV,kBAAG,CAAA,GAAA,KAAA,SAAA,OAAA,KAAA,QAAA,MAAA,OAAA,wBACH,SAAQ,MAAO,OAAA,YAAA,KAAwB,QAAA,IAAA,MAAA;MACxC,GAAA;MACA,GAAA,MAAQ,OAAK,uBAAM;MACnB,OAAA,KAAU;MACV,QAAM,KAAI;MACT,UAAA;MACC,MAAM;IACV,CAAA,GAAA,KAAM,MAAA,OAAW,sBAAqB,MAAA,WAAA,SAAA,KAAA,KAAA,GACtC,KAAK,SAAQ,GAAG,KAAA,kBAAA;;EAElB,WAAC;AAED,QAAQ;;MACF,GAAC;MACH,GAAG;MACH,OAAI,KAAA,OAAA,OAAA,oBAAA;MACJ,QAAO,KAAK,OAAO,OAAO,qBAAqB;MAC/C,UAAQ;MACR,MAAA;QACA,KAAI,MAAM,YAAA,KAAA,YAAA;aACT,IAAA,GAAA,IAAA,KAAA,OAAA,OAAA,WAAA,IAAA,KAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,oBAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,sBAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,qBAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,mBAAA;AACC,YAAM,WAAC,KAAY,QAAK,CAAA;AAE5B,kBAAgB,KAAI,aAAY,YAAO,QAAc;WACnD;YAEE,SAAW,KAAC,OAAO,OAAA,iBAA2B,CAAA;WAC9C,UAAW,KAAC,OAAO,aAAc,WAAa,KAAK,SAAA,GAAa,UAAC,EAAA,UAAiB,IAAA,GAAA,IAAA,OAAA,SAAA,QAAA,KAAA;AAClF,cAAK,WAAO,KAAO,QAAA,GAAc,GAAA,OAAA,SAAkB,MAAA;AAEnD,oBAAY,KAAG,aAAY,YAAO,QAAgB;;;;6BAI1C,gBAAU;;gCAEb,KAAA,OAAA,OAAA,cAAA,qBAAA,EAAA,WAAA,SAAA,UAAA,UAAA,WAAA,IAAA,KAAA,OAAA,OAAA,2BAAA,OAAA,UAAA;oBACF,KAAA,CAAA,aAAA,CAAA,WAAA,UAAA,QAAA,IAAA,QAAA,QAAA,EAAA,QAAA;kBACF,KAAA,IAAA,KAAA,OAAA,OAAA,cAAA,4BAAA,CAAA,GAAA,cAAA,wBAAA,SAAA,WAAA,MAAA,MAAA,CAAA,IAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,gBAAA,KAAA,OAAA,OAAA,cAAA,aAAA,OAAA,uBAAA,qBAAA,KAAA,OAAA,OAAA,cAAA,OAAA,eACD,OAAS,cAAA,kBAAA,cAAA,qBAAA,iBAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,oBAAA,6BAAA,KAAA,OAAA,QAAA,KAAA,OAAA,OAAA,QAAA,KAAA,CAAA,IAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,oBAAA,uCAAA,KAAA,OAAA,QAAA,KAAA,OAAA,OAAA,QAAA,KAAA,CAAA,IAAA,IAAA,gBAAA,KAAA,OAAA,OAAA,oBAAA,KAAA,IAAA,qBAAA,IAAA,wBAAA,WAAA,KAAA,OAAA,OAAA,cAAA,SAAA,MAAA,IAAA,IAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,IAAA,iBAAA;;cACJ,OAAA,OAAA,qBAAA,GAAA,QAAA,CAAA,KAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,qBAAA,aAAA,gBAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,qBAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,cAAA,oBAAA,WAAA,yBAAA,gBAAA,MAAA,gBAAA,iBAAA;aACL;cACI;oBACG,KAAA,OAAa,OAAA,cAAqB,aAAC;iBACzC,IAAA,KAAA,OAAA,OAAA,cAAA,aAAA;cACF,KAAA,OAAA,OAAA,cAAA,aAAA;KACF;AACF,gBAAA,OAAA,YAAA,YAAA,aAAA,OAAA,YAAA,iBAAA,YACD,YAAuB,SAAmB;;MACxC,GAAM;MACN,GAAM;MAKF,OAAA;MACF,QAAO;MACR,cAAA,KAAA,OAAA,OAAA,cAAA,aAAA;MACD,MAAQ;IACR,CAAA;AAEA,QAAA;AACA,aAAM,OAAU,kBAAA,YAA0B,YAAc,QAAA,GAAa,YAAU,eAAA;AAE/E,QAAA,mBAAM,MAAmB,oBACJ;QACjB,qBAAgB;AAChB,UAAE;UACF,cAAE,OAAA,qBAAwC;AAC1C,0BAAc,oBAAqB;UACnC,OAAE;UACF,QAAI;UACF;UAEJ;UACK;UACD;UACH;UAEM;UACJ,eAAkB,KAAC,OAAA;;+BAEJ;0BACP,gBAAc,gBAAc,eAAkB,mBAAc,UAAiB,KAAA,gBAAA,qBAAA,WAAA,MAAA,wBAClF,UAAW,KAAA,gBAAyB,sBAAa,WAAA,MAAA,kBACjD,OAAE;;QAER,kBAAkB;AAElB,YAAM,OAAE,WAAa;QACrB,GAAA;QACA,GAAA;QACA,OAAQ;QAEP,QAAA;QACH,MAAY,KAAO,OAAA,OAAW,cAAA,aAAA;QAC9B,UAAY;MACZ,CAAA;AACA,WAAA,OAAY,iBAAoB,SAAA,YAAA,IAAA,GAAA,YAAA,UAAA;AAEhC,YAAM,gBAAoB,WAAC;QACrB,GAAA;QACA,GAAA;QACJ,OAAO,cAAW,WAAA;QAClB,QAAQ;QACR,MAAA,KAAc,OAAK,OAAO,cAAO,aAAc;QAC3C,UAAM;MACT,CAAA;AACH,oBAAgB,OAAA,0BAAiB,SAAA,YAAA,aAAA,GACjC,YAAY,eAAY;IACxB;AACA,QAAI,iBAAc,SAAA,YAAA,aAAA,GAAA,mBAAA;AACd,YAAA,EAAA,WAAwB,cAAA,UAAA,YAAA,cAAA,OAAA,QAAA,IAAA,KAAA,OAAA,OAAA,cAAA,mBAAA,WAAA,WAAA,WAAA,OAAA,GAAA,WAAA,cAAA,aAAA,aAAA,GAAA,QAAA,WAAA;QACxB,GAAA,SAAA;QAEA,GAAA,SAAA;QACE;QACA,MAAA;QACF;cACE,oBAAkB,KAAA,OAAA,OAAA,cAAA,kBAAA,UAAA;sBACV,cAAa;;;kBAGrB,WAAO,eAAA,KAAA,eAAA,eAAA,QAAA,gBAAA,YAAA,IAAA,eAAA;gBACP;UACA,UAAQ;UACR,KAAA,cAAU,MAAA;;;eAGZ,YAAe,KAAG,GAAA,YAAmB,YAAM;;;;oBAG5C,OAAA;UACD,eAAI,KAAiB,sBAAA,KAAA;oBAInB,KAAa,aAAG,YAAgB,YAAc;qBAC9C,KAAA,QAAmB,KAAA;gBACnB,KAAA,aAAoB,WAAA,UAAgB,KAAA;;sBAErC;QACF,IAAA;AAED,UAAI,gBAAkB,IAAA,MAAA;MAEpB,GAAA;;aAEK;cACH;YACA;cACI,KAAE,OAAK,OAAO,cAAO,kBAA2B,SAAQ;gBAC5D;oBACC,UAAA,KAAA,UAAA,KAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,iBAAA,WAAA,KAAA,KAAA,KAAA,OAAA,OAAA,cAAA,aAAA,iBAAA,WAAA,KAAA,KAAA;MACH,MAAK,KAAI,OAAG,OAAA,cAAgB,kBAAA;MAC5B,YAAS;;QAGT,KAAA,gBAAmB,eAAc,cAAA,OAAA,qCAC3B,OAAA,cAAA,kBAAA;YACH,OAAG,IAAA,MAAA;QACJ,GAAA;QACA,GAAA;QACA,OAAM;QACN,QAAQ;QACP,OAAA;QACH,UAAc;QACd,QAAS;MACT,CAAA;AACD,WAAA,OAAA,mCAAA,KAAA,mBAAA,MAAA,cAAA,YAAA,IAAA;AAED,YAAA,YAAiB,IAAS,MAAA;QACtB,GAAA;QACF,GAAM;QAEN,OAAM;QAEN,QAAW;QAGT,OAAG;QACH,UAAG;QACH,QAAQ;;gBAER,OAAY,oCAAU,KAAA,oBAAA,yBAChB,YAAA,SAAyB;;;;sBAK7B,aAAY,KAAK,CAAA;;;sBAGb,aAAK,KAAA,CAAA;;;6BAGL,OAAS,mBAAA,KAAA,SAAA,KAAA,OAAA,OAAA,uBACf,cAAQ;cACN,KAAA;kBACI;cACL,OAAA,OAAA,uBAAA;aAGA,aAAA,eAAA,GAAA,KAAA,MAAA,YAAA,KAAA,YAAA,QACH,SAAS;;WAEV;AACD,SAAA,QAAO,KAAW,OAAC,OAAA,mBAAA,KAAA,SAAA,KAAA,OAAA,OAAA,YACpB,KAAA,MAAA,aAAA,SAAA,KAAA,KAAA,GAAA,KAAA,MAAA,aAAA,UAAA,KAAA,MAAA;EACD;eACQ,GAAA,GAAA,OAAe,QAAK,QAAA;AAC1B,cAAI,eAAc,OAAA,QAAA,OAAA,YAAA,KAAA,aAAA,QAChB,cAAK,aAAa,KAAY,CAAA,GAAA,KAAY,cAAE,aAAA,KAAA,CAAA,QAC7C,cAAA,aAAA,SAAA,KAAA,GAAA,KAAA,cAAA,aAAA,UAAA,MAAA,GACD,KAAA,cAAiB,aAAa,cAAO,IAAA,GAAA,KAAA,qBAAA,KAAA,iBAAA,aAAA,KAAA,CAAA,GACrC,KAAI,iBAAU,aAAA,KAAA,KAAA,KAAA,SAAA,EAAA,CAAA,GAAA,KAAA,iBAAA,aAAA,SAAA,EAAA,QACZ,iBAAkB,aAAW,UAAU,SAAO,IAAA,KAAA,KAAA,SAAA,EAAA,CAAA,QAC/C,kBAAA,aAAA,KAAA,QAAA,EAAA,GAAA,KAAA,kBAAA,aAAA,KAAA,KAAA,KAAA,SAAA,EAAA,CAAA,GACF,KAAA,kBAAA,aAAA,SAAA,EAAA,GAAA,KAAA,kBAAA,aAAA,UAAA,SAAA,IAAA,KAAA,KAAA,SAAA,EAAA,CAAA;EACD;;AACE,SAAA,cAAmB,aAAa,cAAA,KAAA;;uBAE1B,GAAA,GAAA,OAAA,QAAA,uBAAA,gBAAA,OAAA;QACJ;UACA,iBAAW,IAAA,MAAA;MACX;MACA;MACA;MACA;MAIA,WAAW,KAAA,OAAO,OAAO,cAAc,qBAAkB;MACzD,UAAU;MACT,cAAA,UAAA,KAAA,KAAA,OAAA,OAAA,cAAA,aAAA,iBAAA,WAAA,KAAA,KAAA;MACC,MAAC;MACL,QAAA,KAAc,OAAO,OAAA,cAAwB,qBAAA;MAGzC,aAAY,KAAM,OAAC,OAAa,cAAC,qBAAkB;MACrD,eAAa,KAAI,OAAM,OAAA,cAAA,qBAAA;qBACjB,KAAA,OAAA,OAAA,cAAA,qBAAA;kBACA,KAAA,OAAA,OAAA,cAAA,qBAAA;;cAEJ;;uBAEU,OAAI,0BAAA,KAAA,aAAA,YAAA,cAAA,wBACN,KAAA,cAAY,GAAA,eAAA;YACnB,qBAAA,IAAA,MAAA;QACC,GAAC;QACD,GAAC;QACL,OAAA;QAGA;QACE,UAAI;UACJ,YAAI,IAAA,OAAA;QACJ,GAAA;QACA,GAAA,SAAU;QACV,QAAO,KAAA,OAAA,OAAkB,cAAA,mCAAA;QACzB,MAAA,KAAU,OAAI,OAAA,cAAA,mCAAA;QACd,QAAQ,KAAA,OAAY,OAAA,cAAA,mCAAA;QACnB,WAAA,KAAA,OAAA,OAAA,cAAA,mCAAA;QACH,UAAc;MACd,CAAA;AACA,yBAAc,YAAY,SAAW,GAAA,mBAAA,OAAA,4BACtC,eAAA,YAAA,kBAAA;AACF,YAAA,sBAAA,IAAA,MAAA;QAEa,GAAA;QACP,GAAA;QACN,OAAA;QACa;QACP,UAAa;MACnB,CAAA,GAAA,aAAA,IAAA,OAAA;QAEM,GAAA;QACA,GAAK,SAAQ;QACb,QAAS,KAAK,OAAO,OAAO,cAAW,mCAAA;QACvC,MAAM,KAAA,OAAc,OAAA,cAAA,mCAAA;QACvB,QAAY,KAAC,OAAM,OAAA,cAAA,mCAAA;QACnB,WAAY,KAAK,OAAA,OAAA,cAAA,mCAAA;QACd,UAAW;QACb,kBAAA;MACC,CAAC;AACD,0BAAmB,YAAK,UAAc,GAAA,oBAAA,OAAA,6BACtC,eAAY,YAAA,mBAAA;IACjB;EACD;yBACoB;AAClB,SAAK,gBAAc,QAAO,YAAO;AAC7B,aAAO,OAAA;IACX,CAAA,GAAK,KAAK,kBAAc,CAAA;EAC1B;EAEA,6BAAkD;AAChD,QAAI,MAAM,KAAI,gBAAgB,QAAA;AAE5B,WAAA,gBAAmB,IAAK,EAAA,OAAA;;;+BAGP,IAAA,IAAa,IAAK,IAAG;AACxC,SAAK,2BAA2B,KAAA,uBAAgB,OAAA,GAAA,KAAA,yBAAA;AAChD,UAAK,OAAA,IAAA,KAAc;MACf,QAAC,CAAA;QACG,GAAC;QACH,GAAC;MACL,GAAA;QACI,GAAC;QACD,GAAC;MACL,CAAA;MACA,QAAK,KAAA,OAAA,OAAkB,cAAkB,gCAAwB;MACjE,WAAK,KAAA,OAAkB,OAAA,cAAoB,gCAA4B;MACvE,UAAK,KAAA,OAAiB,OAAC,cAAa,gCAAuC;MAC5E,UAAA;IACF,CAAA;AACD,SAAA,yBAAY,MAAA,KAAA,gBAAA,CAAA,EAAA,YAAA,IAAA;;EAEZ,sBAAC,OAAA,gBAAA;AAED,QAAA,IAAA,KAAA,aAGE;;AAKA,aAAM,IAAA,GAAA,IAAc,KAAG,aAAU,eAAA,KAAA;AAC/B,UAAC,EAAA,eAAA,UAAA,CAAA,gBAAA,cAAA,KAAA,gBAAA,cAAA,KAAA,EAAA,mBAAA,gBAAA,QAAA;AACD,UAAC,EAAA;;WAED;;;;;IC9WJ,iBAAA;cAGO,OAAS;AACd,SAAK,yBAAwB,IAAA,KAAA,SAAc,OAAS,KAAA,WAAA,MAAA,OAAA,cAAA,UACpD,KAAK,SAAM,KAAA,IAAA,MAAA,OAAA,oBAAA,MAAA,OAAA,UAAA,IAAA,MAAA,OAAA,uBAAA,QACT,QAAS,IAAA,MAAM;MACb,GAAC;MACH,GAAG,MAAC,OAAA,uBAAA;MACJ,OAAG,MAAM,OAAO;MAChB,QAAO,KAAM;MACb,UAAQ;MACR,MAAA;QACA,KAAI,MAAM,OAAA,uBAAA,MAAA,WAAA,SAAA,KAAA,KAAA,QACT,oBAAA,IAAA,MAAA;MACC,GAAC;MACL,GAAM;MAEF,OAAC,KAAA,OAAiB,OAAO,oBAAM;MACjC,QAAI,KAAA;MACJ,UAAI;MACJ,MAAK;QACL,KAAA,MAAQ,YAAW,KAAA,iBAAA,GAAA,KAAA,cAAA;;kBAEb;SACL,SAAA,QAAA,UAAA;AACC,YAAM,QAAC,KAAY,OAAK,OAAA,KAAA,OAAmB,OAAA,cAAA,uBAAA,qBAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,MAAA,IAAA,GAAA,UAAA,KAAA,OAAA,OAAA,cAAA,SAAA,EAAA,MAAA,KAAA,IAAA,KAAA,OAAA,OAAA,cAAA,4BAAA,CAAA,GAAA,YAAA,wBAAA,MAAA,SAAA,MAAA,IAAA,GAAA,QAAA,KAAA,OAAA,OAAA,cAAA,oBAAA,KAAA,MAAA,SAAA,KAAA,YAAA,KAAA,WAAA,IAAA,aAAA,KAAA,WAAA,MAAA,KAAA,gBAAA,IAAA,MAAA;QAC1C,UAAa;QACnB,GAAA,QAAA,KAAA,yBAAA;QACD,GAAa;QACN,OAAS,KAAA;QACZ,QAAW,KAAG;MACd,CAAA;oBACI,OAAA,aAA0B,KAAK,kBAAA,YAAA,aAAA;YAC/B,UAAA,WAAqB;QACzB,UAAa;QACb,QAAY,MAAM;QAClB,WAAe,MAAG;QAClB,UACE,MAAK;QACL,QAAM,CAAA;UACF,GAAA,KAAA,yBAA0B;UAC9B,GAAA;QACA,GAAG;UACC,GAAA,KAAA,yBAAA;UACJ,GAAO,KAAK;QACZ,CAAA;OACD;AACD,oBAAc,YAAO,OAAY;;;;kBAKzB,KAAE,IAAM,KAAA,OAAS,OAAA,oBAAA,KAAA,OAAA,OAAA,UAAA,IAAA,KAAA,OAAA,OAAA,uBAAA,0BACZ,eAAe,GAAA,KAAA,MAAA,aAAA,UAAA,KAAA,MAAA,0BACX,aAAS,UAAA,KAAA,MAAA,GAAA,KAAA,cAAA;;;2BAGZ,aAAA,KAAA,CAAA;;;;;AC5DlB,IAAA,cAAA,MAA6B;cACtB,OAAS;AACd,SAAK,SAAA,OAAA,KAAoB,kBAAA;EAC3B;EACA,oBAAiB;;AACf,UAAM,QAAA,KAAA,OAAoB,YAAM,aAAA,KAAA,OAAA,OAAA,cAAA;AAChC,QAAA,CAAA,WAAc;AACd,UAAM,EAAA,cAAyB,aAAqB,iBAAgB,eAAA,IAAA,YAAA,kBAAA,CAAA,GAAA,iBAAA;MAEhE,UAAC;;QAEJ,oBAAA,eAAA,SAAA,MAAA,eAAA,OAAA,OAAA,eAAA,SAAA,aACD,eAAQ,YAAc,iBAAa,mBAAiB,eAAmB,WAAW,iBAGlF,eAAM,UAA0C,SAAG,iBAAA,KAAA,OAAA,OAAA,yBAAA,eAAA,eAAA,CAAA,GAAA,UAAA,KAAA,KAAA,OAAA,OAAA,cAAA,gBAAA,iBAAA,WAAA,KAAA,KAAA,GAAA,UAAA,KAAA,KAAA,OAAA,OAAA,cAAA,gBAAA,iBAAA,WAAA,KAAA,KAAA,GAAA,CAAA,GACnD,gBAAM,eAAwC,CAAA,GAAA,UAAA,KAAA,KAAA,OAAA,OAAA,cAAA,gBAAA,iBAAA,WAAA,KAAA,KAAA,GAAA,UAAA,KAAA,KAAA,OAAA,OAAA,cAAA,gBAAA,iBAAA,WAAA,KAAA,KAAA,GAAA,CAAA,MAAA,eAAA,eAAA,UAAA,KAAA,KAAA,OAAA,OAAA,cAAA,gBAAA,iBAAA,WAAA,KAAA,KAAA,mBAClC,eAAK,UAAA,KAAA,KAAA,OAAA,OAAA,cAAA,gBAAA,iBAAA,WAAA,KAAA,KAAA,WACf,cAAA,eAAA,GAAA,eAAA,QAAA;AAGE,qBAAe,IAAE,KAAA,OAAA,OAAA,wBAAA,CAAA,kBAAA,IAAA,kBAAA,GACnB,eAAe,IAAA,kBAAc,GAAA,eAAA,WAAA,OAAA,eAAA,QAAA,MAAA,UAAA,QAAA,mBAAA,KAAA,OAAA,OAAA,wBAAA,KAAA,OAAA,OAAA,cAAA,kBAAA,YAAA,IAC7B,eAAe,SAAO,MAAM,UAAA,SAAA,kBAAA,IAAA,kBAAA;AAC5B,YAAA,aAAe,WAAS,cAAY;AACpC,iBAAA,OAAe,eAAY,MAAA,OAA0B,YAAA,YAAA,KAAA,GAAA,MAAA,SAAA,YACrD,KAAA,SAAc;;;WAIZ;QACF;0BAC6B,aAAG,iBAAA,eAAA,IAAA,KAAA,OAAA,OAAA,cAAA;mBAC3B,KAAA,WAAA,WAAA,MAAA,GAAA,cAAA;kBACD,OAAK,WAAO,UAAO,QAAc,mBAAgB,KAAA,OAAY,OAAA,wBAAI,KAAC,OAAA,OAAA,cAAA,kBAAA,YAAA;cAClE,KAAA,OAAK,OAAO,aAAO;;;;;;;;;;;;IC1BtB,0CAAA,MAAA;gBACD;AACJ,SAAA,OAAA,2BAAgB,kBAAA,KAAA,WAAA,MAAA,KAAA,QAAA,GAChB,KAAA,mBAAU;;EAqDZ,UAAC,OAAA,SAAA,GAAA,GAAA,QAAA,UAAA,UAAA,UAAA,gBAAA,aAAA,QAAA,UAAA,gBAAA;AAnDC,2BAGW,MAET,QACA,MAAA,gBAEA,WACA;AAcI,UAAA;AACF,UAAA,4BAAqC,MAAA,QAAA,SAAA,MAAA,UAAA,WAAA;;AACnC,aAAI,aAAU,MAAK,CAAA;cACjB,aAAa,KAAM,kBAAA,aAAA,KAAA,MAAA,kBAAA,cAAA,KAAA,OAAA,kBAAA,kBAAA,WAAA,UAAA,WAAA;mBACd,UAAY,WAAU,MAAA,WAAA,MAAA,KAAA,gBAAA,MAAA,KAAA,KAAA,UAAA,UAAA,KAAA,aAAA,MAAA,UAAA,KAAA,KAAA,UAAA,YAAA,WAAA,KAAA,KAAA,CAAA,IAAA,WAAA,MAAA,WAAA,MAAA,KAAA,aAAA,MAAA,WAAA,KAAA,WAAA,EAAA,QAC3B,aAAM,WAAkB,WAAA,KAAiB,WAAA,EAAA,KAAA,WAAA,MAAA,WAAA,OAAA,KAAA,aAAA,MAAA,WAAA,KAAA,WAAA,EAAA,QACzC,aAAM,WAAkB,WAAM,KAAA,WAAiB,EAAA;;;;;0CAO5C,WAAA,CAAA,WAAA,CAAA,GAAA,uCAAA;;;IC/CX,kBAAK,IAAA,gBAAyB,CAAA,MAAS,QAAC,SAAA,WAAA;AACvC,OAAA,uCAAA,EAAA,OAAA,EAAA,iBAAA,GAAA,KAAA,uBAAA,EAAA,UAAA,uCAAA;;;;ACFD,IAAA,qBAAA,MAA6B;cACtB,OAAS;AACd,SAAK,SAAA,OAAe,KAAG,gBAAA;EACzB;EACA,kBAAe;;AACb,SAAI,OAAK,OAAO,cAAO,8BAAc,KAA6B,QAAA,IAAA,MAAA;MAChE,GAAA;;aAEK;cACH;eACA,QAAW,IAAA,MAAA;SACV;MACJ,GAAA;aAAM;MACL,QAAK;gBACC,KAAA,OAAA,OAAA,cAAA,2BAAA;cACA;iBACG,KAAG,OAAA,OAAA,cAAA,2BAAA;cACV,KAAQ,OAAG,OAAA,cAAA,2BAAA;oBACD,UAAK,KAAO,KAAO,OAAA,OAAc,cAAA,2BAAmC,iBAAA,WAAA,KAAA,KAAA;YAC9E,KAAM,OAAE,OAAS,cAAA,2BAAA;aACjB,eAAgB,WAAO;gBACjB;cACN,KAAA,OAAc,OAAA,cAAY,2BAAqB;iBACzC,KAAK,OAAO,OAAO,cAAc,2BAA2B;cACjE,CAAA;QAEC,GAAC;QACH,GAAA;;QAEA,GAAA;QACA,GAAA;;mBAEK,YAAY,KAAE,YAAA,GAAA,KAAA,iBAAA,WAAA;gBAClB;cACA,KAAA,OAAA,OAAA,cAAA,2BAAA;MACH,WAAW,KAAA,OAAY,OAAK,cAAc,2BAAA;MAC1C,QAAK,CAAA;QACH,GAAA;QACA,GAAA;;QAEA,GAAA;;;aAGC,MAAA,YAAA,KAAA,cAAA,IAAA,KAAA,MAAA,OAAA,6BACF,OAAE,QAAA,MAAA,SAAA,KAAA,KAAA;;OAEJ,GAAA,GAAA,OAAA,QAAA;AACD,QAAI,KAAC,OAAU,OAAG,cAAA,4BAAuB,MAAA,MAAA,YAAA,KAAA,OAAA,OAAA,cAAA,4BAAA;MACrC;MACL;MACe,eAA0B,KAAc,OAAA;IACtD,CAAA,EAAA,aAAgB;SAAO;AACrB,YAAK,WAAM,KAAW,IACpB,OAAK,MAAO,IAAO;WACjB,eAAK,aAAA,UAAA,CAAA;QACL,IAAA,QAAM,IAAA,YAAA;QACN,GAAA,SAAA;SACC;QAEN,IAAA,QAAA,IAAA,YAAA,IAAA,IAAA;QAAM,GAAA,SAAA;MACL,CAAA,CAAA,GAAM,KAAA,aAAgB,aAAW,UAAY,CAAA;QACzC,GAAC,QAAA;QACH,IAAG,SAAQ,IAAG,YAAa;;QAE1B,GAAA,QAAA;QAEC,IAAC,SAAY,IAAC,YAAa,IAAA,IAAU;;;SAGxC,MAAE,aAAA,KAAA,CAAA,GAAA,KAAA,MAAA,aAAA,KAAA,CAAA,GAAA,KAAA,MAAA,aAAA,SAAA,KAAA,QACJ,MAAA,aAAA,UAAA,MAAA,GAAA,KAAA,MAAA,aAAA,cAAA,IAAA;;SAED;AACA,SAAK,MAAM,aAAa,cAAc,KAAE,GAAA,KAAA,OAAA,OAAA,cAAA,+BAAA,KAAA,MAAA,eAAA;;;;;AC3ErC,IAAM,iBAAN,MAAqB;AAAA,EACxB,YAAY,OAAO;AACf,SAAK,SAAS,OAAO,KAAK,QAAQ,MAAM,OAAO,mBAAmB,KAAK,SAAS,MAAM,OAAO,YAC7F,KAAK,QAAQ,IAAI,MAAM;AAAA,MACnB,GAAG;AAAA,MACH,GAAG,MAAM,OAAO,uBAAuB;AAAA,MACvC,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,UAAU;AAAA,MACV,MAAM;AAAA,IACV,CAAC,GAAG,KAAK,MAAM,OAAO,6BAA6B,MAAM,WAAW,SAAS,KAAK,KAAK,GACvF,KAAK,cAAc;AAAA,EACvB;AAAA,EACA,gBAAgB;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,qBAAqB,KAAK,OAAO,OAAO,OAAO,GAAG,KAAK,qBAAqB,IAAI,MAAM;AAAA,MACtF,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,KAAK,OAAO,OAAO,oBAAoB;AAAA,MAC9C,QAAQ,KAAK,OAAO,OAAO,qBAAqB;AAAA,MAChD,UAAU;AAAA,MACV,MAAM;AAAA,IACV,CAAC,GAAG,KAAK,MAAM,YAAY,KAAK,kBAAkB,GAAG,UAAU,KAAK,KAAK,OAAO,OAAO,YAAY,WAAW,KAAK,SAAS,GAAG,OAAQ,UAAS,IAAI,GAAG,UAAU,KAAK,KAAK,UAAU,KAAK,KAAK,OAAO,OAAO,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG,YAAY,WAAW,MAAM,IAAI,IAAK,MAAK,aAAa,CAAC;AAAA,EACxU;AAAA,EACA,aAAa,OAAO;AAChB,UAAM,EAAC,cAA4B,gBAAgC,IAAI,KAAK,OAAO,OAAO,eAAe,OAAO,gBAAgB,KAAK,GAAG,EAAC,iBAAkC,mBAAsC,KAAU,IAAI,MAAM,qBAAqB,oBAAoB,KAAK,OAAO,OAAO,SAAS,cAAc,eAAe,GAAG,uBAAuB,oBAAoB,KAAK,OAAO,OAAO,SAAS,cAAc,iBAAiB;AAChb,QAAI,CAAC,sBAAsB,CAAC,qBAAsB;AAClD,QAAI,uBAAuB,qBAAqB,sBAAsB,yBAAyB,uBAAuB,wBAAwB,uBAAuB;AACrK,QAAI,mBAAmB,OAAO,0BAA0B,mBAAmB,OAAO,wBAAwB,CAAC,IAC3G,mBAAmB,OAAO,sBAAsB,KAAK,IAAI,GAAG,qBAAqB,OAAO,4BAA4B,qBAAqB,OAAO,0BAA0B,CAAC,IAC3K,qBAAqB,OAAO,wBAAwB,KAAK,IAAI,GAAG,KAAK,OAAO,OAAO,cAAc,kBAAkB,cAAc,iBAAkB,2BAA0B,qBAAqB,MAAM,CAAC,GACzM,wBAAwB,mBAAmB,MAAM,CAAC,GAAI,EAAC,WAAW,uBAAuB,SAAS,qBAAqB,UAAU,qBAAoB,IAAI,KAAK,OAAO,OAAO,2BAA2B,mBAAmB,MAAM,CAAC,GAAG,mBAAmB,MAAM,CAAC,CAAC,GAC9P,EAAC,WAAW,yBAAyB,SAAS,uBAAuB,UAAU,uBAAsB,IAAI,KAAK,OAAO,OAAO,2BAA2B,qBAAqB,MAAM,CAAC,GAAG,qBAAqB,MAAM,CAAC,CAAC;AAAA,aAAa,KAAK,OAAO,OAAO,cAAc,kBAAkB,cAAc,sBAAsB,KAAK,OAAO,OAAO,cAAc,kBAAkB,cAAc,qBAAqB,KAAK,OAAO,OAAO,cAAc,kBAAkB,cAAc,mBAAmB;AAC9d,gCAA0B,KAAK,OAAO,OAAO,qBAAqB,GAAG,qBAAqB,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,OAAO,cAAc,aAAa,KAAK,OAAO,OAAO,cAAc,kBAAkB,cAAc,qBAAqB,KAAK,OAAO,OAAO,cAAc,kBAAkB,cAAc,oBAAoB,qBAAqB,OAAO,yBAAyB,qBAAqB,MAAM,CAAC;AACpZ,8BAAwB,KAAK,OAAO,OAAO,qBAAqB,GAAG,mBAAmB,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,OAAO,cAAc,aAAa,KAAK,OAAO,OAAO,cAAc,kBAAkB,cAAc,qBAAqB,KAAK,OAAO,OAAO,cAAc,kBAAkB,cAAc,oBAAoB,mBAAmB,OAAO,yBAAyB,mBAAmB,MAAM,CAAC,IAC3Y,EAAC,WAAW,uBAAuB,SAAS,qBAAqB,UAAU,qBAAoB,IAAI,KAAK,OAAO,OAAO,2BAA2B,mBAAmB,MAAM,CAAC,GAAG,mBAAmB,MAAM,CAAC,CAAC,GACzM,EAAC,WAAW,yBAAyB,SAAS,uBAAuB,UAAU,uBAAsB,IAAI,KAAK,OAAO,OAAO,2BAA2B,qBAAqB,MAAM,CAAC,GAAG,qBAAqB,MAAM,CAAC,CAAC;AAAA,IACxN,MAAO,2BAA0B,KAAK,OAAO,OAAO,8BAA8B,qBAAqB,KAAK,GAC5G,wBAAwB,KAAK,OAAO,OAAO,8BAA8B,mBAAmB,KAAK,GAChG,EAAC,WAAW,uBAAuB,SAAS,qBAAqB,UAAU,qBAAoB,IAAI,KAAK,OAAO,OAAO,2BAA2B,qBAAqB,GACtK,EAAC,WAAW,yBAAyB,SAAS,uBAAuB,UAAU,uBAAsB,IAAI,KAAK,OAAO,OAAO,2BAA2B,uBAAuB;AAC/K,QAAI,CAAC,0BAA0B,CAAC,qBAAsB;AACtD,yBAAqB,KAAK,OAAO,OAAO,cAAc,OAAO;AAC7D,UAAM,EAAC,aAA0B,WAAsB,IAAI,uBAAuB,MAAM,yBAAyB,uBAAuB,yBAAyB,wBAAwB,uBAAuB,qBAAqB,uBAAuB,sBAAsB,KAAK,OAAO,MAAM,GAAG,YAAY,KAAK,OAAO,OAAO,cAAc,yBAAyB,UAAU,WAAW;AAAA,MAC9X,UAAU;AAAA,MACV,QAAQ,UAAU;AAAA,MAClB,WAAW,UAAU;AAAA,MACrB,UAAU,UAAU;AAAA,MACpB,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,aAAa;AAAA,IACjB,CAAC;AACD,SAAK,mBAAmB,YAAY,OAAO,GAAG,KAAK,4BAA4B;AAC/E,UAAM,QAAQ,IAAI,QAAQ;AAAA,MACtB,MAAM,UAAU;AAAA,MAChB,QAAQ;AAAA,IACZ,CAAC;AACD,SAAK,mBAAmB,YAAY,KAAK,GAAG,KAAK,6BAA6B;AAAA,EAClF;AAAA,EACA,KAAK,GAAG;AACJ,SAAK,mBAAmB,aAAa,KAAK,CAAC;AAAA,EAC/C;AAAA,EACA,KAAK,GAAG;AACJ,SAAK,mBAAmB,aAAa,KAAK,CAAC;AAAA,EAC/C;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,KAAK,OAAO,OAAO,mBAAmB,KAAK,SAAS,KAAK,OAAO,OAAO,YACpF,KAAK,MAAM,cAAc;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,GAAG,KAAK,OAAO,OAAO,uBAAuB;AAAA,IACjD,CAAC,GAAG,KAAK,mBAAmB,eAAe,GAAG,KAAK,MAAM,YAAY,KAAK,kBAAkB,GAC5F,KAAK,cAAc;AAAA,EACvB;AAAA,EACA,SAAS;AACL,SAAK,QAAQ,KAAK,OAAO,OAAO,mBAAmB,KAAK,SAAS,KAAK,OAAO,OAAO,YACpF,KAAK,MAAM,aAAa,SAAS,KAAK,KAAK,GAAG,KAAK,MAAM,aAAa,UAAU,KAAK,MAAM;AAAA,EAC/F;AAAA,EACA,uBAAuB,cAAc;AACjC,UAAM,WAAW,aAAa,2BAA2B,YAAY,aAAa,4BAA4B,2BAA2B,SAAS,MAAM,GAAG,wBAAwB,UAAU,MAAM;AACnM,6BAAyB,aAAa,UAAU,KAAK,OAAO,OAAO,cAAc,gCAAgC,SAAS,GAC1H,yBAAyB,aAAa,aAAa,KAAK,OAAO,OAAO,cAAc,gCAAgC,SAAS,GAC7H,yBAAyB,aAAa,eAAe,KAAK,OAAO,OAAO,cAAc,gCAAgC,WAAW,GACjI,yBAAyB,aAAa,iBAAiB,KAAK,OAAO,OAAO,cAAc,gCAAgC,YAAY,GACpI,yBAAyB,aAAa,iBAAiB,KAAK,OAAO,OAAO,cAAc,gCAAgC,YAAY,GACpI,yBAAyB,aAAa,cAAc,KAAK,OAAO,OAAO,cAAc,gCAAgC,UAAU,GAC/H,KAAK,mBAAmB,YAAY,wBAAwB,GAAG,sBAAsB,aAAa,QAAQ,KAAK,OAAO,OAAO,cAAc,gCAAgC,SAAS,GACpL,KAAK,mBAAmB,YAAY,qBAAqB,GAAG,KAAK,sBAAsB,CAAE,0BAA0B,qBAAsB;AAAA,EAC7I;AAAA,EACA,yBAAyB;AACrB,QAAI;AACJ,cAAU,KAAK,KAAK,wBAAwB,WAAW,MAAM,GAAG,QAAS,UAAQ;AAC7E,WAAK,OAAO;AAAA,IAChB,CAAE,GAAG,KAAK,sBAAsB,CAAC;AAAA,EACrC;AAAA,EACA,WAAW,MAAM;AACb,UAAM,eAAe,KAAK,2BAA2B,gBAAgB,KAAK;AAC1E,iBAAa,OAAO,GAAG,cAAc,OAAO;AAAA,EAChD;AACJ;AAEO,SAAS,uBAAuB,MAAM,yBAAyB,uBAAuB,8BAA8B,wBAAwB,uBAAuB,qBAAqB,4BAA4B,sBAAsB,OAAO;AACpP,QAAM,EAAC,MAAY,KAAU,IAAI,MAAM,cAAc,4BAA4B,CAAC,GAAG,EAAC,SAAkB,WAAsB,iBAAkC,IAAI,MAAM;AAC1K,MAAI,WAAW,SAAS,aAAa,CAAC,GAAG,cAAc,CAAC;AACxD,MAAI,SAAS,eAAe,eAAe;AACvC,gBAAY,yBAAyB,UAAU;AAC/C,UAAM,iBAAiB,wBAAwB,uBAAuB,SAAS,MAAM,MAAM,CAAC,IAAI,kBAAkB,eAAe,wBAAwB,uBAAuB,SAAS,MAAM,IAAI,IAAI;AACvM,iBAAa,CAAE;AAAA,MACX,GAAG;AAAA,MACH,GAAG,aAAa,+BAA+B;AAAA,IACnD,GAAG;AAAA,MACC,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,+BAA+B;AAAA,IACnD,GAAG;AAAA,MACC,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,gCAAgC,+BAA+B,6BAA6B,IAAI;AAAA,IACpH,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,gCAAgC,+BAA+B,6BAA6B,IAAI;AAAA,IACpH,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,6BAA6B;AAAA,IACjD,GAAG;AAAA,MACC,GAAG;AAAA,MACH,GAAG,aAAa,6BAA6B;AAAA,IACjD,CAAE,GAAG,iBAAiB,MAAM,eAAe,MAAM,WAAW,OAAO,GAAG,GAAG;AAAA,MACrE,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,6BAA6B;AAAA,IACjD,CAAC;AACD,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,kBAAc,CAAE;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,CAAE;AAAA,EACN,WAAW,SAAS,eAAe,eAAe;AAC9C,gBAAY,yBAAyB,UAAU;AAC/C,UAAM,iBAAiB,wBAAwB,yBAAyB,SAAS,MAAM,IAAI,IAAI,kBAAkB,eAAe,wBAAwB,qBAAqB,SAAS,MAAM,MAAM,CAAC,IAAI;AACvM,iBAAa,CAAE;AAAA,MACX,GAAG;AAAA,MACH,GAAG,aAAa,+BAA+B;AAAA,IACnD,GAAG;AAAA,MACC,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,+BAA+B;AAAA,IACnD,GAAG;AAAA,MACC,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,gCAAgC,+BAA+B,6BAA6B,IAAI;AAAA,IACpH,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,gCAAgC,+BAA+B,6BAA6B,IAAI;AAAA,IACpH,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,6BAA6B;AAAA,IACjD,GAAG;AAAA,MACC,GAAG;AAAA,MACH,GAAG,aAAa,6BAA6B;AAAA,IACjD,CAAE,GAAG,iBAAiB,MAAM,eAAe,MAAM,WAAW,OAAO,GAAG,GAAG;AAAA,MACrE,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,6BAA6B;AAAA,IACjD,CAAC;AACD,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,kBAAc,CAAE;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,CAAE;AAAA,EACN,WAAW,SAAS,eAAe,cAAc;AAC7C,gBAAY,yBAAyB,UAAU;AAC/C,UAAM,iBAAiB,wBAAwB,yBAAyB,SAAS,MAAM,IAAI,IAAI,kBAAkB,eAAe,wBAAwB,uBAAuB,SAAS,MAAM,IAAI,IAAI;AACtM,iBAAa,CAAE;AAAA,MACX,GAAG;AAAA,MACH,GAAG,aAAa,+BAA+B;AAAA,IACnD,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,iBAAiB,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC7H,GAAG,aAAa,+BAA+B;AAAA,IACnD,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,iBAAiB,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC7H,GAAG,aAAa,8BAA8B,iCAAiC,6BAA6B,IAAI;AAAA,IACpH,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,8BAA8B,iCAAiC,6BAA6B,IAAI;AAAA,IACpH,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,eAAe,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC3H,GAAG,aAAa,6BAA6B;AAAA,IACjD,GAAG;AAAA,MACC,GAAG;AAAA,MACH,GAAG,aAAa,6BAA6B;AAAA,IACjD,CAAE;AACF,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,kBAAc,CAAE;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,CAAE;AAAA,EACN,WAAW,SAAS,eAAe,gBAAgB;AAC/C,gBAAY,yBAAyB,UAAU;AAC/C,UAAM,iBAAiB,wBAAwB,uBAAuB,SAAS,MAAM,MAAM,CAAC,IAAI,kBAAkB,eAAe,wBAAwB,qBAAqB,SAAS,MAAM,MAAM,CAAC,IAAI;AACxM,iBAAa,CAAE;AAAA,MACX,GAAG;AAAA,MACH,GAAG,aAAa,+BAA+B;AAAA,IACnD,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,iBAAiB,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC7H,GAAG,aAAa,+BAA+B;AAAA,IACnD,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,iBAAiB,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC7H,GAAG,aAAa,8BAA8B,iCAAiC,6BAA6B,IAAI;AAAA,IACpH,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,8BAA8B,iCAAiC,6BAA6B,IAAI;AAAA,IACpH,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,6BAA6B;AAAA,IACjD,GAAG;AAAA,MACC,GAAG;AAAA,MACH,GAAG,aAAa,6BAA6B;AAAA,IACjD,CAAE;AACF,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,kBAAc,CAAE;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,CAAE;AAAA,EACN;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEO,SAAS,qBAAqB,MAAM,yBAAyB,uBAAuB,8BAA8B,wBAAwB,4BAA4B,eAAe,uBAAuB,qBAAqB,4BAA4B,sBAAsB,0BAA0B,aAAa,OAAO;AACpU,QAAM,EAAC,MAAY,KAAU,IAAI,MAAM,cAAc,4BAA4B,CAAC,GAAG,EAAC,SAAkB,WAAsB,iBAAkC,IAAI,MAAM;AAC1K,MAAI,WAAW,SAAS,aAAa,CAAC,GAAG,cAAc,CAAC;AACxD,MAAI,SAAS,eAAe,eAAe;AACvC,gBAAY,yBAAyB,UAAU;AAC/C,UAAM,iBAAiB,6BAA6B,2BAA2B,UAAU,IAAI,2BAA2B,UAAU,QAAQ,wBAAwB,uBAAuB,SAAS,MAAM,MAAM,CAAC,IAAI,kBAAkB,eAAe,2BAA2B,yBAAyB,UAAU,IAAI,wBAAwB,uBAAuB,SAAS,MAAM,IAAI,IAAI;AAC5X,iBAAa,CAAE;AAAA,MACX,GAAG;AAAA,MACH,GAAG,aAAa,+BAA+B,OAAM;AAAA,IACzD,GAAG;AAAA,MACC,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,+BAA+B,OAAM;AAAA,IACzD,GAAG;AAAA,MACC,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,gCAAgC,+BAA+B,6BAA6B,IAAI,MAAM;AAAA,IAC1H,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,gCAAgC,+BAA+B,6BAA6B,IAAI,MAAM;AAAA,IAC1H,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,GAAG;AAAA,MACC,GAAG;AAAA,MACH,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,CAAE,GAAG,iBAAiB,MAAM,eAAe,MAAM,WAAW,OAAO,GAAG,GAAG;AAAA,MACrE,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,CAAC;AACD,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,kBAAc,CAAE;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,CAAE;AAAA,EACN,WAAW,SAAS,eAAe,eAAe;AAC9C,gBAAY,yBAAyB,UAAU;AAC/C,UAAM,iBAAiB,6BAA6B,2BAA2B,UAAU,IAAI,wBAAwB,yBAAyB,SAAS,MAAM,IAAI,IAAI,kBAAkB,eAAe,2BAA2B,yBAAyB,UAAU,IAAI,yBAAyB,UAAU,QAAQ,wBAAwB,qBAAqB,SAAS,MAAM,MAAM,CAAC,IAAI;AAC1X,iBAAa,CAAE;AAAA,MACX,GAAG;AAAA,MACH,GAAG,aAAa,+BAA+B,OAAM;AAAA,IACzD,GAAG;AAAA,MACC,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,+BAA+B,OAAM;AAAA,IACzD,GAAG;AAAA,MACC,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,gCAAgC,+BAA+B,6BAA6B,IAAI,MAAM;AAAA,IAC1H,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,gCAAgC,+BAA+B,6BAA6B,IAAI,MAAM;AAAA,IAC1H,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,GAAG;AAAA,MACC,GAAG;AAAA,MACH,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,CAAE,GAAG,iBAAiB,MAAM,eAAe,MAAM,WAAW,OAAO,GAAG,GAAG;AAAA,MACrE,GAAG,iBAAiB;AAAA,MACpB,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,CAAC;AACD,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,kBAAc,CAAE;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,CAAE;AAAA,EACN,WAAW,SAAS,eAAe,cAAc;AAC7C,gBAAY,yBAAyB,UAAU;AAC/C,UAAM,iBAAiB,6BAA6B,2BAA2B,UAAU,IAAI,wBAAwB,yBAAyB,SAAS,MAAM,IAAI,IAAI,kBAAkB,eAAe,2BAA2B,yBAAyB,UAAU,IAAI,wBAAwB,uBAAuB,SAAS,MAAM,IAAI,IAAI;AAC9U,iBAAa,CAAE;AAAA,MACX,GAAG;AAAA,MACH,GAAG,aAAa,+BAA+B,OAAM;AAAA,IACzD,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,iBAAiB,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC7H,GAAG,aAAa,+BAA+B,OAAM;AAAA,IACzD,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,iBAAiB,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC7H,GAAG,aAAa,8BAA8B,iCAAiC,6BAA6B,IAAI,QAAO;AAAA,IAC3H,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,8BAA8B,iCAAiC,6BAA6B,IAAI,QAAO;AAAA,IAC3H,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,eAAe,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC3H,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,GAAG;AAAA,MACC,GAAG;AAAA,MACH,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,CAAE;AACF,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,kBAAc,CAAE;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,CAAE;AAAA,EACN,WAAW,SAAS,eAAe,gBAAgB;AAC/C,gBAAY,yBAAyB,UAAU;AAC/C,UAAM,iBAAiB,6BAA6B,2BAA2B,UAAU,IAAI,2BAA2B,UAAU,QAAQ,wBAAwB,uBAAuB,SAAS,MAAM,MAAM,CAAC,IAAI,kBAAkB,eAAe,2BAA2B,yBAAyB,UAAU,IAAI,yBAAyB,UAAU,QAAQ,wBAAwB,qBAAqB,SAAS,MAAM,MAAM,CAAC,IAAI;AACxa,iBAAa,CAAE;AAAA,MACX,GAAG;AAAA,MACH,GAAG,aAAa,+BAA+B,OAAM;AAAA,IACzD,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,iBAAiB,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC7H,GAAG,aAAa,+BAA+B,OAAM;AAAA,IACzD,GAAG;AAAA,MACC,IAAI,iCAAiC,6BAA6B,iBAAiB,KAAK,IAAI,gBAAgB,YAAY,KAAK;AAAA,MAC7H,GAAG,aAAa,8BAA8B,iCAAiC,6BAA6B,IAAI,QAAO;AAAA,IAC3H,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,8BAA8B,iCAAiC,6BAA6B,IAAI,QAAO;AAAA,IAC3H,GAAG;AAAA,MACC,GAAG,eAAe;AAAA,MAClB,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,GAAG;AAAA,MACC,GAAG;AAAA,MACH,GAAG,aAAa,6BAA6B,OAAM;AAAA,IACvD,CAAE;AACF,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,kBAAc,CAAE;AAAA,MACZ,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU,IAAI;AAAA,MACjB,GAAG,UAAU,IAAI;AAAA,IACrB,GAAG;AAAA,MACC,GAAG,UAAU;AAAA,MACb,GAAG,UAAU;AAAA,IACjB,CAAE;AAAA,EACN;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;;;AChaE,IAAA,gBAAA,MAA6B;cACtB,OAAS;AAEd,SAAK,SAAA,OAAA,KAAoB,oBAAU,IAAA,MAAA;MACjC,GAAG;MACH,GAAG;MACH,OAAO,MAAM,OAAO;MACpB,QAAQ,KAAK,IAAI,MAAM,OAAO,oBAAoB,MAAM,OAAO,UAAU;MACzE,UAAU;MACV,MAAM;MACN,SAAS;KACV,GAAE,KAAA,kBAAA,OAAA,6BAAA,MAAA,WAAA,SAAA,KAAA,iBAAA,GACH,KAAK,aAAA;;iBAEA;AACN,QAAA,KAAA,OAAA,OAAA,uBAAA;AACD,YAAY,QAAA,KAAA,OAAA,OAAA,sBAAA,MAAA,qBAAA,UAAA,WAAA;QACF,UAAQ;QACd,QAAW,MAAO;QAElB,WAAa,MAAG;QACd,QAAQ,CAAA;;WAER,WAAW,SAAM,KAAS,kBAAA,YAAA,OAAA;;;eAIvB,GAAA;SACL,kBAAK,QAAkB,GAAA,KAAY,SAAS,aAAA,UAAA,CAAA;MAC7C,GAAA;MACF;IAGD,GAAA;MACM,GAAC,KAAA,OAAA,OAAkB;MACnB;;;iBAGG;SACJ,kBAAA,QAAA;;;;;UCEH,KAAK,eAAoB;IAEvB,mBAAK;cACL,OAAM;QACN,OAAA;SACA,SAAU,OAAO,KAAC,kBAAc,MAAA,mBAAuB,KAAA,mBAAA,KAAA,IAAA,MAAA,oBAAA,MAAA,UAAA,cAEvD,IAAA,SAAkB,QAAA,OAAA,SAAA,GAAA,QAAA,MAAA,OAAA,OAAA,SAAA,MAAA,OAAA,cAClB,QAAA,YAAiB;MACjB,QAAO,MAAE;;;MAGT,oBAAa;MAIZ,YAAA,MAAA,cAAA;MACE,cAAoB;MACpB,YAAoB;MACrB,SAAO;QACT,SAAO;;kBAEL,CAAA,eAAqB;aACrB,MAAA,QAAU,KAAO,QAAA,KAAA,MAAA,QAAA,KAAA,QAAA,KAAA,MAAA,aAAA,SAAA;aAClB;QACG,eAAE;QACJ,oBAAe;QAChB,UAAA;MACA;MACC,MAAC;QACN,WAAA;MAED;IACE,CAAA,GAAA,KAAM,eAAa;;mBAGR;SACR,aAAQ,IAAO,MAAM;MACtB,GAAA,KAAO,OAAK;MACZ,GAAA,KAAQ,OAAK;MACb,OAAM,KAAI;MACV,QAAQ,KAAE;MACT,MAAA;MACH,UAAY;IACZ,CAAA,GAAA,KAAM,MAAA,aAAkB,IAAQ,KAAA,UAAA,GAAA,KAAA,WAAA,OAAA,SAEhC,KAAK,iBAAe,IAAG,eAAkB,IAAC,GAAM,KAAC,OAAA,IAAA,KAAA,IAAA,GAAA,KAAA,iBAAA,IAAA,eAAA,IAAA,GAGjD,KAAK,UAAQ,IAAI,QAAU,IAAE,GAAA,KAAA,WAAA,IAAA,SAAA,IAAA,GAAA,KAAA,cAAA,IAAA,YAAA,IAAA,GAE7B,KAAK,qBAAkB,IAAI,mBAAsB,KAAA,MAAA,GAAA,KAAA,MAAA,aAAA,SAAA,KAAA,mBAAA,UAAA,GAEjD,KAAK,MAAC,aAAc,SAAa,KAAE,mBAAA,UAAA,GAAA,KAAA,gBAAA,IAAA,cAAA,IAAA;;0BAMf;AAGpB,SAAK,mBAAmB,gBAAO,GAAA,sBAAiC,KAAA,OAAA,YAAA;;eAE1D;AAGN,SAAK,mBAAiB,KAAI,IAAA,KAAA,OAAmB,oBAAE,KAAA,OAAA,UAAA,GAChD,KAAA,kBAAA,KAAA,OAAA,mBAAA,KAAA,WAAA,aAAA,UAAA,KAAA,gBAAA,GAED,KAAA,WAAA,aAAqB,SAAA,KAAA,eAAA,GAAA,KAAA,eAAA,QAAA,GACnB,KAAK,KAAA,QAAA,GAAA,KAAmB,QAAA,QAAkB,GAAA,KAAA,eAAA,QAAA,GAAA,KAAA,SAAA,QAAA,GAC1C,KAAA,eAAA,QAA2B,GAAA,KAAO,YAAc,OAAA,GAAA,KAAA,mBAAA,gBAAA,GACjD,KAAA,gBAAA;EAED;oBACO;AACL,SAAK,eAAe,QAAQ,GAAA,KAAO,QAAA,QAAiB,GAAC,KAAA,gBAAA;;2BAErC;AAChB,SAAK,OAAA,wBAAyB,MAAA,SAAA,KAAA,OAAA,aAAA,MAC9B,KAAK,mBAAe,KAAA,IAAA,KAAA,OAAA,oBAAA,KAAA,OAAA,UAAA,GACpB,KAAK,WAAQ,aAAU,UAAA,KAAA,gBAAA,GAAA,KAAA,KAAA,QAAA,GACvB,KAAK,QAAA,QAAe,GAAA,KAAS,eAAC,QAAA,GAAA,KAAA,SAAA,QAAA,GAC9B,KAAK,YAAS,OAAU,GAAA,KAAA,mBAAA,gBAAA,GAAA,KAAA,gBAAA;;oBAEnB;AACL,SAAK,mBAAmB,KAAA,IAAA,KAAA,OAAkB,oBAAA,KAAA,OAAA,UAAA,GAC1C,KAAK,kBAAkB,KAAA,OAAA,mBAAA,KAAA,WAAA,cAAA;MACxB,GAAA,KAAA,OAAA;MAED,GAAA,KAAe,OAAA;MAGT,OAAC,KAAA,OAAe;MAChB,QAAQ,KAAC;IAIb,CAAA,GAAI,KAAC,KAAA,OAAe,GAAG,KAAA,QAAA,OAAA,GAAA,KAAA,SAAA,QAAA,GAAA,KAAA,YAAA,OAAA;EACzB;EACA,mBAAA;AACE,SAAK,MAAM,OAAC;;oBAEP;AAEL,SAAK,MAAK,gBAAU;;MAEpB,QAAK;AACL,QAAI,IAAC;AACL,WAAK,UAAY,KAAA,UAAS,KAAA,KAAA,WAAA,cAAA,WAAA,KAAA,SAAA,GAAA,UAAA,WAAA,KAAA,KAAA;;MAE1B,SAAK;AACN,QAAA,IAAA;AAED,WAAA,UAAe,KAAA,UAAA,KAAA,KAAA,WAAA,cAAA,WAAA,KAAA,SAAA,GAAA,WAAA,WAAA,KAAA,KAAA;;MAEb,IAAI;AACJ,QAAI,IAAC;WACA,UAAK,KAAO,UAAM,KAAA,KAAA,WAAA,cAAA,WAAA,KAAA,SAAA,GAAA,MAAA,WAAA,KAAA,KAAA;;UAErB;QACA,IAAA;WACQ,UAAA,KAAA,UAAA,KAAA,KAAA,WAAA,cAAA,WAAA,KAAA,SAAA,GAAA,MAAA,WAAA,KAAA,KAAA;;OAEV,GAAI,QAAQ,OAAC;AACb,SAAK,eAAS,KAAU,CAAA,GAAA,KAAA,KAAA,KAAA,CAAA,GAAA,KAAA,QAAA,KAAA,CAAA,GAAA,KAAA,eAAA,KAAA,CAAA,GACxB,KAAK,SAAA,KAAY,CAAA,GAAM,KAAG,gBAAA;EAC5B;EAQA,KAAA,GAAA,QAAgB,OAAA;AACd,SAAK,KAAK,KAAC,CAAA,GAAQ,KAAC,QAAA,KAAA,CAAA,GAAA,KAAA,eAAA,KAAA,CAAA,GAAA,KAAA,gBAAA;EACtB;EAMA,cAAA,YAAe;AACb,SAAK,MAAM,mBAAkB,GAAA,KAAA,MAAA,OAAA,OAAA,UAAA,GAAA,KAAA,MAAA,OAAA,GAC9B,KAAA,MAAA,kBAAA;EACD;;AACE,SAAA,gBAAO,GAAK,KAAA,mBAAoB,gBAAA,GAAA,KAAA,gBAAE;EACpC;EAEA,UAAU;;;EAEV,uBAAC,GAAA,GAAA,WAAA;AAEG,SAAC,uBAAA,KAAA,qBAAA,IAAA,mBAAA,KAAA,OAAA,UAAA,qHACH,KAAA,gBAAO;EACT;EAEA,yBAAK;;;EAEL,uBAAC,WAAA,gBAAA,QAAA,KAAA,GAAA;AAOG,UAAY,QAAQ,KAAK,QAAA,SAAA,MAAA,iBAAA,WAAA,cAAA,GAAA,wBAAA,OAAA,uBAAA,0BAAA,OAAA;AAC3B,aAAK,IAAA,GAAA,KAAe,QAAQ,wBAAA,SAAA,sBAAA,SAAA,KAAA;AACxB,YAAM,OAAO,sBAAC,CAAA,GAAA,eAAA,KAAA,2BAAA,gBAAA,KAAA,4BAAA,EAAA,iBAAA,mBAAA,KAAA,IAAA,MAAA,EAAA,cAAA,QAAA,IAAA,MAAA,eAAA,uBAAA,oBAAA,MAAA,SAAA,cAAA,iBAAA,GAAA,qBAAA,oBAAA,MAAA,SAAA,cAAA,eAAA;AACd,UAAC,uBAAgB,qBAAA,sBAAA,yBAAA,uBAAA,wBAAA,uBAAA,yBAAA;AACjB,UAAC,MAAA,cAAuB,kBAAA,cAAA,kBAAA;AACvB,cAAS,aAAQ,qBAAA,OAAA,UAAA,IAAA,IAAA,KAAA;AACjB,kCAAkB,qBAAA,MAAA,CAAA;AAExB,gCAAA,MAAA,qBAAA,GAAA,WAAA,aAAA,CAAA,IAAA,MAAA,cAAA,WAOoB,EAAA,WAAQ,uBAAA,SAAA,qBAAA,UAAA,qBAAA,IAAA,MAAA,2BAAA,mBAAA,MAAA,CAAA,GAAA,mBAAA,MAAA,CAAA,CAAA,GAElB,EAAC,WAAQ,yBAAA,SAAA,uBAAA,UAAA,uBAAA,IAAA,MAAA,2BAAA,qBAAA,MAAA,CAAA,GAAA,qBAAA,MAAA,CAAA,CAAA;AACb,cAAQ,gBAAQ,MAAA,cAAA,aAAA;AAChB,gBAAA,OAAe,UAAQ,IAAA,gBAAA,KAAA,wBAAA,OAAA,MAAA,cAAA;MACxB,WAAC,MAAe,cAAG,kBAAA,cAAA,sBAAA,MAAA,cAAA,kBAAA,cAAA,qBAAA,MAAA,cAAA,kBAAA,cAAA,mBAAA;AACxB,cAAA,aAAA,qBAAA,OAAA,UAAA,IAAA,IAAA,KAAA;AAED,kCAAgC,MAAA,qBAAA,GAAA,qBAAA,MAAA,CAAA,IAAA,CAAA,IAAA,MAAA,cAAA,aAAA,MAAA,cAAA,kBAAA,cAAA,qBAAA,MAAA,cAAA,kBAAA,cAAA,oBAAA,qBAAA,OAAA,yBAAA,qBAAA,MAAA,CAAA;AAIzB,gCAA2B,MAAA,qBAAA,GAAA,WAAA,aAAA,CAAA,IAAA,MAAA,cAAA,aAAA,MAAA,cAAA,kBAAA,cAAA,qBAAA,MAAA,cAAA,kBAAA,cAAA,oBAAA,mBAAA,OAAA,yBAAA,WAAA,iBAC3B,EAAK,WAAQ,uBAAmB,SAAA,qBAAA,UAAA,qBAAA,IAAA,MAAA,2BAAA,mBAAA,MAAA,CAAA,GAAA,mBAAA,MAAA,CAAA,CAAA,GAChC,EAAK,WAAU,yBAAA,SAAA,uBAAA,UAAA,uBAAA,IAAA,MAAA,2BAAA,qBAAA,MAAA,CAAA,GAAA,qBAAA,MAAA,CAAA,CAAA;AACf,cAAM,gBAAmB,MAAC,cAAA,aAAA;AAChC,gBAAA,OAAA,UAAA,IAAA,gBAAA,KAAA,wBAAA,OAAA,MAAA,cAAA;MAEK,OAAA;AACC,YAAA,0BAAkB,MAAA,8BAAA,qBAAA,KAAA,GAElB,wBAAmB,MAAA,8BAAkB,mBAAA,KAAA,GACrC,OAAA,2BAAkB,OAAA,sBAAA;AACxB,SAAA,EAAA,WAAA,uBAAA,SAAA,qBAAA,UAAA,qBAAA,IAAA,MAAA,2BAAA,qBAAA,IACM,EAAA,WAAA,yBAAA,SAAA,uBAAA,UAAA,uBAAA,IAAA,MAAA,2BAAA,uBAAA;MACD;AACL,YAAA,EAAA,YAAA,YAAA,IAAA,qBAAA,MAAA,yBAAA,uBAAA,yBAAA,wBAAA,MAAA,GAAA,uBAAA,qBAAA,uBAAA,sBAAA,QAAA,QAAA,QAAA,QAAA,GAAA,KAAA,MAAA;AAED,mBAAA,aAA6C,UAAiB,UAAA,GAAA,cAAA,aAAA,UAAA,WAAA;IAC5D;aACO,IAAA,GAAA,KAAA,QAAkB,0BAA0B,SAAW,wBAAa,SAAA,KAAA;AAC1E,YAAA,OAAA,wBAAA,CAAA,GAAA,eAAA,KAAA,2BAAA,gBAAA,KAAA,4BAAA,EAAA,iBAAA,mBAAA,KAAA,IAAA,MAAA,EAAA,cAAA,QAAA,IAAA,MAAA,eAAA,qBAAA,oBAAA,MAAA,SAAA,cAAA,eAAA,GAAA,uBAAA,oBAAA,MAAA,SAAA,cAAA,iBAAA;AACG,UAAC,uBAAyB,qBAAiB,sBAA0B,yBAAc,uBAAyB,wBAAA,uBAAA,yBAAA;AAC5G,UAAC,MAAA,cAAkB,kBAAA,cAAA,kBAAA;AACxB,cAAA,aAAA,qBAAA,OAAA,UAAA,IAAA,IAAA,KAAA;AAED,kCAAsB,MAAA,qBAAA,GAAA,WAAA,aAAA,CAAA,IAAA,MAAA,cAAA,WACZ,wBAAqB,mBAAA,MAAA,CAAA,GAAA,EAAA,WAAA,uBAAA,SAAA,qBAAA,UAAA,qBAAA,IAAA,MAAA,2BAAA,mBAAA,MAAA,CAAA,GAAA,mBAAA,MAAA,CAAA,CAAA,GACtB,EAAA,WAAA,yBAA0B,SAAA,uBAAA,UAAA,uBAAA,IAAA,MAAA,2BAAA,qBAAA,MAAA,CAAA,GAAA,qBAAA,MAAA,CAAA,CAAA;AAC3B,cAAC,gBAAkB,MAAA,cAAA,aAAA;AACxB,gBAAA,OAAA,UAAA,IAAA,gBAAA,KAAA,0BAAA,OAAA,MAAA,cAAA;MACF,WAAA,MAAA,cAAA,kBAAA,cAAA,sBAAA,MAAA,cAAA,kBAAA,cAAA,qBAAA,MAAA,cAAA,kBAAA,cAAA,mBAAA;AACD,cAAA,aAAuB,qBAA2C,OAA0B,UAAc,IAAA,IAAA,KAAA;AAClG,kCAA2B,MAAA,qBAAA,GAAA,WAAA,aAAA,CAAA,IAAA,MAAA,cAAA,aAAA,MAAA,cAAA,kBAAA,cAAA,qBAAA,MAAA,cAAA,kBAAA,cAAA,oBAAA,qBAAA,OAAA,yBAAA,WAAA;AAC3B,gCAAe,MAAiB,qBAAW,GAAA,mBAAgB,MAAA,CAAA,IAAA,CAAA,IAAA,MAAA,cAAA,aAAA,MAAA,cAAA,kBAAA,cAAA,qBAAA,MAAA,cAAA,kBAAA,cAAA,oBAAA,mBAAA,OAAA,yBAAA,mBAAA,MAAA,CAAA,IAC3D,EAAA,WAAA,uBAA+B,SAAA,qBAAsB,UAAA,qBAAA,IAAA,MAAA,2BAAA,mBAAA,MAAA,CAAA,GAAA,mBAAA,MAAA,CAAA,CAAA,GACrD,EAAA,WAAA,yBAAiC,SAAA,uBAAwB,UAAA,uBAAA,IAAA,MAAA,2BAAA,qBAAA,MAAA,CAAA,GAAA,qBAAA,MAAA,CAAA,CAAA;AAC1D,cAAS,gBAAM,MAAA,cAAqB,aAArB;AAClB,gBAAU,OAAG,UAAA,IAAA,gBAAyB,KAAA,0BAAA,OAAA,MAAA,cAAA;MACtC,OAAM;AACN,YAAM,0BAAqB,MAAA,8BAA2B,qBAAA,KAAA,GAEtD,wBAAyB,MAAA,8BAAiC,mBAAA,KAAA,GAC1D,OAAQ,2BAA0B,OAAM,sBAAc;AACtD,SAAA,EAAM,WAAA,uBAAuB,SAAoB,qBAAe,UAAc,qBAAmB,IAAA,MAAA,2BAAA,qBAAA,IACjG,EAAM,WAAA,yBAAqB,SAAoB,uBAA2B,UAAE,uBAAiB,IAAA,MAAA,2BAAA,uBAAA;MAO7F;AACA,YAAI,EAAA,YAAoB,YAAA,IAAA,qBAAA,MAAA,yBAAA,uBAAA,yBAAA,wBAAA,QAAA,QAAA,QAAA,QAAA,GAAA,uBAAA,qBAAA,uBAAA,sBAAA,MAAA,GAAA,KAAA,MAAA;AACxB,mBAAI,aAAqB,UAAA,UAAA,GAAA,cAAA,aAAA,UAAA,WAAA;;;;;;AC3SxB,IAAM,YAAY,WAAa;AACpC,UAAQ,OAAO,UAAU,SAAS,KAAK,KAAK,GAAG;SAC7C;aACE;;AAGF,aAAK;SAEL;aACE;;AAGF,aAAK;SAEL;aACE;;AAGF,aAAK;SAEL;aACE;;AAGF,aAAK;SAEL;aACE;;AAGF,aAAA;IAED,KAAA;AACD,aAAA;IAII,KAAC;AACA,aAAM;IAEP,KAAC;AACA,aAAM;IAEP,KAAC;AACA,aAAM;IAEP;AACC,aAAM;EACb;AACA;;;;;AC9CE,SAAiB,SAAC,MAAA,MAAA,SAAA;AAClB,MAAI,UAAc,UAAA,SAAA,QAAA,SAAA,cAAA,iBAAA,GAAA,SAAA,OAAA,UAAA,OAAA,WAAA;AAClB,QAAI,SAAgB,CAAA,QAAA,MAAA,QAAA,cAAA,OAAA;AACpB,MAAI,cAAY,OAAA,KAAA,OAAA,IAAA,UAAA,qBAAA;AAChB,WAAI,WAA4B,MAAA;AAC5B,UAAA,OAAqB,UAAA,UAAA;AAGrB,WAAA,WAAkB,QAAC,WAAA,QAAA,iBAAA,MAAA,SAAA,KAAA,MAAA,SAAA,IAAA,GAEnB;EAEJ;AAEA,WAAI,WAAgB,aAAAG,OAAA;AAEpB,WAAM,SAAc,sBAAkB,WAAO,IAAA,WAAqB,aAAeA,KAAC;EAElF;WACE,aAAU,MAAU;AACrB,UAAA,oBAAA,OAAA;AAEG,WAAK,WAAqB,gBAAA,qBAAA,QAAA,oBAAA,KAAA,UAAA,OAAA,kBAAA;EAE9B;WACE,eAAoB;AAEpB,UAAM,OAAG,KAAS,IAAI;AACtB,QAAI,aAAQ,IAAA,EAAA,QAAA,SAAAC,OAAA;AACV,aAAO,UAAQ,QAAK,YAAe,WAAa,WAAAA,KAAA,KAAA,WAAA,QACjD,WAAA,QAAA;IAED,EAAA,IAAA;AACD,cAAA,WAAA,cAAA,SAAAA,OAAA;AAED,YAAS,sBAAuBA,QAAA,gBAAA,cAAA,QAAA,EAAAA,QAAA;AAC9B,aAAU,SAAG,KAAS,IAAA,aAAA,UAAA,mBAAA,IAAA;IACtB,EAAA,IAAM,CAAA;;SAGN,OAAQ,CAAG,QAAA,GAAU,SAAA,OAAA,MAAA,UAAA,CAAA,CAAA,QAAA,SAAA,SAAA,aAAA,oBACrB,UAAiB,KAAK,IAAA,CAAA,QAAA,WAAA,GAAA,IAAA,IAAA,WAAA,cAAA,UAAA,CAAA,CAAA,QAAA,WAAA,uBAChB,MAAQ;AACd,UAAA,OAAa,KAAC,IAAA,GAAA,aAAA,aAAA,IAAA;AACf,QAAA,WAAA,MAAA,WAAA,MAAA,eAAA,MAAA,YAAA;AAGD,UAAS,WAAW,QAAgB,QAAc,SAAAA,OAAA;AAC5C,eAAQ,iBAAAA,OAAA,UAAA,WAAA,cAAA,IAAA,GAAA,UAAA,WAAAA,KAAA,IAAA;MACV,EAAA,YAAO;AACR,UAAA,OAAA,QAAA,UAAA,WAAA,cAAA,IAAA,GAAA,WAAA,YAAA;IACD;AACD,WAAA,WAAA,YAAA,UAAA,WAAA,cAAA,IAAA,IAAA;EAWD;;;;IC3BA,UAAA;IAbA,uBAAa;cAIb,SAGI,IAAA,YAAA;QACF;QACA,KAAA,aAAS,KAAA,KAAA,WAAA;MACT,OAAA;MA6BF,QAAA;YACM,iBAAY,MAAA;AAChB,WAAA,SAAA;IAUF,GAAA,KAAA,WAAc,MAAE;AACd,YAAM,UAAU,KAAK,QAAO;AAC5B,UAAI,sBAAsB;AAC1B,cAAI,UAAa,KAAK,SAAK,SAAc,QAAI,WAAc,KAAK,SAAK,WAAe,sBAAE,YACpF,WAAA,SAAmB,KAAO,MAAC,KAAA,GAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,KAAA,QAAA,GAAA;QAC5B;MACD,CAAA,CAAA;YACI,WAAW,MAAG;AAClB,WAAA,iBAAA;IAMM,GAAA,KAAA,UAAW,SAAK,KAAA,KAAA,IAAA,KAAA,WAAA,KAAA,QAAA,GAAA,eAAA,KAAA,aAAA,KAAA,IAAA,YAAA,EAAA,SAEtB,mBAAK,SAAmB,KAAA,UAAA,KAAA,UAAA,GAAA,SAAA,UAAA,WAAA,UAAA,OAAA,iBAAA,UAAA,KAAA,QAAA,GAE1B,oBAAE,QAAA;AAvDI,YAAC,uBAAkB,OAAA;AACnB,WAAG,WAAM,IAAA,qBAAA,KAAA,cAAA,GAAA,UAAA,KAAA,KAAA,aAAA,WAAA,MAAA,GAAA,QAAA,KAAA,OAAA;IACb,MAAK,uBAAuB,WAAG,KAAA,WAAA,IAAA,iBAAA,KAAA,cAAA,GAC/B,KAAI,SAAU,QAAE,KAAA,SAAA;MACd,YAAK;MACN,iBAAA,CAAA,OAAA;IAED,CAAA;;eAII;WAEF,oBAAM,UAA4B,KAAO,QAAA,GAAA,KAAe,aAAA,KAAA,SAAA,WAAA,QACxD,WAAK;;UAEN,MAAA;SAAM,WAAI;;cAEL;oBACF,KAAY,QAAI;mBAChB,UAAiB,KAAC,SAAQ,SAAA,QAAA,WAAA,KAAA,SAAA;;YAE7B;AACF,WAAA,KAAA,UAAA;MAMD,OAAU,KAAA,MAAA,KAAA,QAAA,WAAA;MACR,QAAO,KAAA,MAAA,KAAoB,QAAQ,YAAO;IAC1C,IAAI,OAAK,OAAQ,CAAE,GAAA,KAAA,QAAA;;;AAIpB,IAAA,eAAA,MAAA;EAYD,cAA+C;AAC7C,SAAK,YAAW,CAAA,GAAK,KAAA,mBAAA,CAAA;EACvB;EAQQ,GAAA,QAAS,MAAA,aAAA,SAAA;AACf,QAAA,WAAa,IAAG,KAAK,QAAU;AAC/B,UAAI,KAAO;QACT,QAAO,SAAM,SAAA,OAAA,iBAAA,KAAA,aAAA,QAAA,WAAA,OAAA,SAAA,UAAA,OAAA,iBAAA,MAAA,UAAA,GAAA,OAAA;SAAA;AACd,YAAA,iBAAA,IAAA,eAAA,QAAA,UAAA,KAAA,UAAA;AACD,WAAO,iBAAK,EAAA,IAAA;IACb;AAED,UAAO,MAAA;MACD;MACF;MAGD;MACD;;WAEE,KAAQ,UAAU,EAAC,IAAK,KAAA;;EAE5B,KAAC,QAAA,MAAA,aAAA,SAAA;AACF,QAAA,WAAA,IAAA,KAAA,QAAA;AAEK,UAAO,KAAA,KAAA,GAAY,QAAA,MAAA,IAAA,SAAA;AAAzB,WAAA,IAAA,EAAA,GAAA,SAAA,GAAA,IAAA;IAGU,GAAA,GAAA,OAAS;AAIT,WAAA;EAgHV;EA5GE,IACE,IAAA;AAKA,QAAI;QACF,WAAU,IAAA,KAAA;QACX,QAAA,GAAA;AACD,UAAM,MAAK,UAAU,KAAA,KAAA,cAAA,WAAA,KAAA,SAAA,GAAA,EAAA;AACrB,YAAI,OAAM,KAAA,UAAA,EAAA,GAAA,IAAA,OAAA,uBAAE,IAAA,OAAkB,oBAAA,IAAA,MAAA,IAAA,UAAA,GAAA,IAAA,OAAA;;eAEzB,SAAsB,MAAA;mBACxB,IAAA,KAAA,YAAA,OAAA,KAAA,WAAA;YAAM,WAAA,KAAA,UAAA,GAAA;eACL,WAAM,UAAiB,SAAI,SAAe,QAAuB,SAAU,SAAK,KAAU,SAAE,QAAA,GAAA,IAAA;;;cAG/F,QAAA,MAAA;AACD,QAAA,WAAc,IAAA,KAAQ,QAAM;AAC5B,QAAI,SAAC;AACL,eAAU,OAAA,KAAA,WAAA;AACX,YAAA,WAAA,KAAA,UAAA,GAAA;AAEC,eACY,WACM,UACf,SAA8C,SAAA,SAAA,SAAA;IAEjD;WACE;;UAEF;QAII,WAAW,IAAE,MAAA;AACb,iBAAS,OAAS,KAAA,WAAA;AAEjB,cACJ,WAAC,KAAA,UAAA,GAAA;AACK,iBAAG,OAAA,uBAAA,SAAA,OAAA,oBAAA,SAAA,MAAA,SAAA,UAAA,GAAA,SAAA,OAAA;MACX;AACyC,iBAAA,OAAA,KAAA,kBAAA;;AAChC,gBAAI,kBAAa,eAAA,WAAA;MACvB;AACD,WAAA,YAAA,CAAA;IACD;;YAEC;AACD,eAAS,IAAG,SAAK,KAAA,MAAS,GAAA,KAAA,YAAA,CAAA;;;;;AC5KxB,IAAA,oBAAuB;SAEzB,gBAAsB;SACvB,SAAA,oBAAA,KAAA,oBAAA,KAAA,KAAA,OAAA,oBAAA,CAAA,uBAAM,KAAA,oBAAA,KAAA,MAAA,qBAAA;;cAKJ;;;ACYF,IAAA,eAAA,MAAA;EACD,YAAO,OAAA;AACL,SAAK,SAAA,OAAa,KAAC,YAAU,OAAA,KAAA,uBAAA,CAAA,GAAA,KAAA,SAAA,OAE7B,KAAK,gBAAA,IAAoB,gBAAc,KAAE,UAAA;;;SAGtC,cAAA,QAAA,GAAA,KAAA,qBAAA,QAAA,UAAA;qBAAe,KAAG,MAAK,SAAQ,oBAAA,KAAA,MAAA,KAAA,QAAA,IAAA,WAAA,KAAA,MAAA,SAAA,KAAA,oBAAA,KAAA,MAAA,KAAA,QAAA,IAAA,aAAA,KAAA,OAAA,OAAA,oBAAA,KAAA,MAAA,KAAA,QAAA;aAC9B,uBAAc,CAAA;;;2BAEP,IAAA,GAAA,yBAAoC,IAAQ;;;SAIxD,uBAAA,OAAA;AAED,QAAA,QAAS,MAAA,OAAA,YAAA,QAAA,MAAA,QAAA,eAAA,MAAA;QACP,WAAA,iBAA6B,eAAA,OAAA;AAC7B,QAAA,MAAA,EAAA,OAAA;AAED,QAAA,aAAA,wBAAA,wBAAA,uBAAA,wBAAA;AACF,MAAA,WAAA,KAAA,cAAA,eAAA,SAAA,QAAA,cAAA,UACD,QAAS,2BAA0C,SAAA,QAAA,yBAAA,UACjD,QAAM,+BAAgC,SAAA,QAAA,wBAAA,UACtC,QAAM,gCAAqB,SAAA,QAAA,yBAAA,UAC3B,QAAM,CAAA,CAAA,SAAe,UAAM,gBAAa,yBAAA,UAAA,gBAAA,SAAA,UAAA,aAExC,KAAM,GAAA,cAAW,sCAA6D,EAAA,OAAA,QAAA,aAAA,mBAAA,aAAA,EAAA,YAAA,GAAA,EAAA,YAAA,GAAA,EAAA,OAAA,GAAA,MAAA,GAC5E,aAAY,uBAAQ,iBAAA,OAAA,KAAA,uCAAA,EAAA,OAAA,QAAA,aAAA,mBAAA,aAAA,EAAA,YAAA,GAAA,EAAA,YAAA,GAAA,EAAA,OAAA,GAAA,OAAA,gBAEX,uBAAA,iBAAA,OAAA,KAAA,MAAA,cAAA,oBAAA,aAAA,iBAAA,aAAA,EAAA,YAAA,GAAA,EAAA,YAAA,GAAA,EAAA,OAAA,CAAA,gBACR,uBAAA,iBAAA,OAAA,KAAA,yBAAA,aAAA,0BAAA,uBAAA,EAAA,YAAA,GAAA,EAAA,YAAA,GAAA,EAAA,OAAA,GAAA,MAAA,GACD,aAAI,uBAAY,iBAAA,OAAA,KAAA,2BAAA,aAAA,0BAAA,wBAAA,EAAA,YAAA,GAAA,EAAA,YAAA,GAAA,EAAA,OAAA,GAAA,OAAA,GAChB,aAAI,uBAAuB,iBAAA,OAAA;MAC3B,MAAI,WAAA,iBAAuB,eAAA,OAAA;AAC3B,QAAI,IAAA,IAAA;AACJ,QAAI,aAAA,qBAAuB,iBAAA,SAAA;AACvB,YAAA,gBAAc,EAAA,WAAA,KAAA,cAAA,eAAA,SAAA,IAAA;AAEhB,UAAA,eAAiB;AACb,YAAA,MAAS,OAAI,aAAiB,aAAA,WAAA,kBAAA,MAAA,OAAA,aAAA,aAAA,SAAA,eAEhC,aAAW,iBAAY,GAAA,MAAA,OAAA,aAAA,iBAAA,mBAAA,IAAA;AACvB,gBAAO,YAAK,cAAA,YAAA,iBAAA,cAAA,gBAAA,SAAA,MAAA,OAAA,iBAAA,WAAA,cAAA;AACb,gBAAA,OAAA,cAAA,iBAAA,qBAAA;YAAU,gBAAkB;YAC3B,OAAA,EAAA;YACO,OAAK;YACb;YAAU;UAET,CAAA;QACA;aACD;YAAM,MAAI,OAAS,aAAS,aAAA,QAA2B;AAEtD,cAAA,MAAA,OAAA,aAAkC,iBAAA,mBAAA,GAAA;AAC3B,kBAAK,YAAA,MAAA,OAAA,aAAA,aAAA,OAAA,YAAA,iBAAA,MAAA,OAAA,aAAA,aAAA,OAAA,gBAAA,SAAA,MAAA,OAAA,iBAAA,WAAA,cAAA;AACb,kBAAA,OAAA,cAAA,iBAAA,qBAAA;cAAU,gBAAmB;cAC5B,OAAA,EAAA;cACA,OAAgB;cACJ;cACb;YACW,CAAC;UACZ;AACC,uBAAa,iBAAA,CAAA;QACX;AACF,YAAA,MAAA,cAAa,kBACX,cACG,oBACoB,MACrB,cACF,kBACA,cAAA,sBAAA,MAAA,cAAA,kBAAA,cAAA,qBAAA,MAAA,cAAA,kBAAA,cAAA,qBAAA,MAAA,cAAA,kBAAA;AACF,gBAAA,YAAa,gBAAuB,EAAA,OAAA,GAAA,KAAiB,GAAA,iBAAS,MAAA,2BAAA,SAAA;AAC/D,cAAA,CAAA,eAAA,YAAA,eAAA,cAAA,CAAA,eAAA,WAAA,aAAA;AAAY,kBAAO,YAAS,gBAAA,EAAA,OAAA,GAAA,KAAoC,GAAA,SAAA,aAAA,IAAA,MAAA,iBAAA,GAAA,YAAA,CAAA,IAAA,KAAA,MAAA,aAAA,OAAA,kBAAA,QAAA,YAAA,MAAA,cAAA,YAAA,MAAA,aAAA,OAAA;AAC/D,mBAAa,KAAA,MAAA,WACX,uBACwB,OACrB,OAAA,SACD;UAGJ;QACD;;YACC,WAAa,uBAAiB;eAC9B,aAAa,qBAAuB,iBAAiB,SAAS;UAC/D,aAAA,wBAAA,wBAAA,uBAAA,wBAAA;AACF,QAAA,WAAA,KAAA,cAAA,eAAA,SAAA,QAAA,cAAA,kBAAU,2BAAuB,SAAA,QAAA,yBAAA,UAChC,QAAA,+BAAa,SACX,QAAA,wBACwB,UAK1B,QAAA,gCAAoC,SAAA,QAAiB,yBAAS,UAC/D,QAAA,CAAA,CAAA,SAAA,UAAA,gBAAA,yBAAA,UAAA,gBAAA,SAAA,UAAA,kBAAM,GAAI,cAAA,UAAwB,KAAA,QAAA,cAAA,cAAA,UAAA,KAAA,QAAA,wBAAA,SAAA,sBAAA,WAAA,WAAA,KAAA,SAAA,GAAA,UAAA,0BAAA,WAAA,KAAA,KAAA,UAAA,KAAA,QAAA,yBAAA,SAAA,uBAAA,WAAA,WAAA,KAAA,SAAA,GAAA,UAAA,uBACjC,MAAA,OAAa,aAAA,yBACX,KAAA,CAAA,cACG,aACA,gCAEI,IACP,MAAA,OAAA,aAAA,yBAAA,KAAA,yBAAA,MAAA,OAAA,aAAA,gBAAA,WAAA,eAAA,aAAA,uBAAA,qBAAA,GACF,aAAa,wBAAuB,yBAA0B,uBAC/D,aAAA,wBAAA,wBAAA,UAAA,MAAA,OAAA,aAAA,yBAAA,KAAA,0BAAA,MAAA,OAAA,aAAA,gBAAA,WAAA,eAAA,aAAA,uBAAA,sBAAA,GACA,aAAA,wBAAA,yBAAA,wBAEG,aAAW,wBAAiB,wBAA4C,WAAA,MAAA,OAAA,aAAA,yBAAA,KAAA,eAAA,MAAA,OAAA,aAAA,gBAAA,WAAA,gBAAA,aAAA,yBAAA,aAAA,wBAAA,sBAAA,4TACxE,aAAa,wBAAqB,oBAAiB,aAAS,aAAA,gCAAA;;uBAErD,iBAAkB,aAAW,OAAA;QACtC;QACA,eAAI,aAAe,OAAA,wBAAA,OAAA,wBAAA,OAAA,uBAAA,OAAA,wBAAA;0BACP,EAAM,WAAC,KAAa,cAAa,eAAY,SAA2C,QAAA,aAAA,yCACnF,SAAa,QAAY,wBAAmD,MAAA,SAAA,+BAAA,SAAA,QAAA,uBAAA,+CAC5E,SAAmB,QAAA,wBAAA,0BAC5B,UAAM,gBAAoB,wBAAiB,MAAA,gBAAsB,SAAA,UAAA;sBAGnE,MAAM,OAAA,cAAiB,qBAAc,WAAe,MAAA,cAAA;uBACpD,+BAA4B,GAAA,aAAiB,0BAA2B,aAAA,sBAClE,iBAAO,cAAc,GAAA;0BACzB,cAAiB,YAAA,iBAAA,cAAA,gBAAA,SAAA,MAAA,iBAAA,WAAA,cAAA;4BACT,iBAAY,gBAAA;0BACb;mBACP;;;;SAIL;;eACI,yBAAA,WAAA,MAAA,cAAA;mBACI,+BAAqB,GAAa,aAAQ,0BAAA;wBACvC,gBAAO,EAAA,OAAa,GAAA,KAAA,GAAgB,iBAAC,MAAsB,2BAAA,SAAA;yBAE7D,YAAY;2BACZ,UAAc,KAAG,MAAM,cAAO,eAAyB,WAAQ,KAAA,KAAA,MAAe,cAAA,uBAAA,wBAAA,cAAA,YAAA,gBAAA,EAAA,OAAA,GAAA,KAAA,GAAA,YAAA,MAAA,oBAAA,SAAA;uBAC9E,WAAS,MAAM,cAAO,cAAiB,IAAW,WAAA,UAAgB,WAAA,UAAA,kBAClE,WAAO,MAAA,cAAc,YAAiB,IAAA,WAAqB,UAAA,SAAA,UAAA,oBAC/D,uBAAiB,GAAA,MAAA,iBAAA,eAAA,YAAA,SAAA,sBACV,iBAAa,oBAAA,KAAA,MAAA,cAAA,iBAAA,sBAAA;0BACb;mBACP;;qBAEC,eAAA,WAAA,MAAA,cAAA,cAAA;mBACJ,eAAA,WAAA,MAAA,cAAA,YAAA;UACD,QAAA,eAAa;SACd;;wCAIqB,MAAA,OAAa,cAAkB,4BAAmB,WAAA,MAAA,aAAA,cAAA,+BAAA,gBACtE,0BAAoB,GAAA,MAAa,OAAK,aAAc,wBAAiB,OAAA,4BACrE,+BAAoB;cAAkB,wBAA+B,0BAAA,WAAA,MAAA,cAAA;gBACrE,aAAM,iBAAc,2BACpB,GAAA;cACA,YAAM,gBAAY,EAAA,OAAkB,GAAM,KAAI,GAAA,SAAO,MAAA,iBAAA,SAAA;cACrD,cAAM,iBAAuB,6BAA2B;UACxD,OAAK,EAAA;iBACH;iBACA,uBACY,UAAQ;;;;mBAOpB,0BAAO;uCACR,cAAA,MAAA,cAAA;uBACF,yBAAA,GAAA;AAEF,cAAA,OAAA,aAAA,wBAAA,EAAA,OAAA,CAAA;AACD,cAAM,aAAW,iBAAsB,sBAAG,KAAA,MAAA,cAAA,iBAAA,wBAAA;UAC3C,gBAAA;UAAU,OAAA,EAAY;UACjB;QACA,CAAA;MACJ;eACI,yBAAsB,cAAA,MAAA,cAAA;AAC1B,UAAI,aAAA,yBAAuB,GAAA;AACvB,cAAA,OAAa,aAAC,wBAAA,EAAA,OAAA,CAAA;AAEhB,cAAA,aAAiB,iBAAiB,sBAAA,KAAA,MAAA,cAAA,iBAAA,wBAAA;UAC9B,gBAAa;UAEf,OAAA,EAAA;UACA;SACD;;wBACC,+BAAkC,GAAA,aAAA,0BAAA;uBAClC,iBAAY,gBAAA,OAAA;yBACb,YAAA,qBAAA,YAAA,MAAA,cAAA,YAAA,qBAAA,CAAA,MAAA,cAAA,YAAA,qBAAA,YAAA,MAAA,cAAA,YAAA,YAAA,MAAA,mBAAA,wBAAA,wBAAU,YAAa,mBAAK,YAA4B,MAAA,cAAA,YAAA,mBAAA,CAAA,MAAA,cAAA,YAAA,mBAAA,YAAA,MAAA,cAAA,YAAA,YAAA,MAAA,mBAAA,sBAAA;uBAEvD,iBAAqB,gBAAY,OAAA;yBAC1B,YAAK,qBAAA,YAAA,MAAA,cAAA,YAAA,qBAAA,CAAA,MAAA,cAAA,YAAA,qBAAA,YAAA,MAAA,cAAA,YAAA,YAAA,MAAA,mBAAA,wBAAA,wBACb,YAAA,mBAAA,YAAA,MAAA,cAAA,YAAA,mBAAA,CAAA,MAAA,cAAA,YAAA,mBAAA,YAAA,MAAA,cAAA,YAAA,YAAA,MAAA,mBAAA,sBAAA;;;kCAIA,cAAA;6BAAU,QAAS,gBAAqB,aAAE,OAAA,iCACzC,UAAA,aAAyB;mBACzB,WAAA,GAAa,SAAY,OAAA;gBACzB,GAAA,cAAY,aAAA,MAAA;cACb,GAAA,MAAA,aAAA,GAAA,UAAA,OAAA;YACD,SAAO,MAAM,EAAA,WAAA,gBAAA,MAAA,QAAA,UAAA,KAAA,MAAA,cAAA,cAAA,CAAA,4BACZ,MAAA,QAAA;YACH,yBAAW,MAAA,cAAA,6BAAA,QAAA,GAAA,MAAA,yBAAA,aAAA,OAAA;iBACT,uBAAW,iBAAA,OAAA,GAAX,aAAW,sBACX,CAAA;YAEF,cAAiB,+BAAa,MAAwB,2BAAoB,QAAA,GAAA,MAAA,yBAAA,aAAA,OAAA;kCAE3D,WAAA,CAAA,EAAA,MAAA,UAAkC;YAChD,2BAAA,QAAA,GAAA,MAAA,yBAAA,YAAA,OAAA;kCACc,WAAa,CAAA,EAAA,MAAA,UAAA;;kCAEb,OAAY;uBAIzB,4BAAoC;WACpC;WACA;aACD,aAAA,eAAA;;eAEC,qBAAsB,KAAA;;;cAMtB;cACA,iBAAa,aAAA,uBAAwB;kCACtC,OAAA;gBAAM,IACL,IAAM,IAAA;mBACN,MAAW,aAAA,cAAA;YACX,KAAM,UAAO,KAAA,MAAa,aAAe,0BACzC,MAAA,WAAA,KAAA,KAAA,EAAA,GAAA,KAAA,EAAA,IAAA,IAAA,KAAA,UAAA,KAAA,MAAA,aAAA,0BAAA,MAAA,WAAA,KAAA,KAAA,EAAA,GAAA,KAAA,EAAA,IAAA;YAEA,IAAA,EAAA,KAAa,KAAA,KAAA,IAAA,EAAA,KAAA,OAAyB,MAAA,aAAa,eAAwB;;qBAGzE,qBAAa,iBAAwB,WAAiB,cAAM,MAC5D,aAAA,cAAA;oBAEE,UAAY,KAAC,UAAA,KAAA,MAAwB,aAAA,8BAAiB,WAAA,KAAA,SAAA,GAAA,MAAA,WAAA,KAAA,KAAA,EAAA,GAAA,QAAA,UAAA,KAAA,UAAA,KAAA,MAAA,aAAA,8BAAA,WAAA,KAAA,SAAA,GAAA,MAAA,WAAA,KAAA,KAAA,EAAA;wBACtD,EAAA,CAAA,KAAa,KAAA,KAAA,IAAA,QAAuB,EAAC,CAAA,KAAA,OAAA,aAAuB,qBAC5D,KAAA,aAAA,+BAAA,gBACA,0BAAa,GAAA,aAAA,qBAAkC,CAAA,KAAA,aAAA,iBAAA,KAAA,aAAA,+BAAA,gBAChD,0BAAA,GAAA,aAAA,gBAAA,CAAA,KAAA,aAAA,kBAAA,KAAA,aAAA,+BAAA,gBACD,0BAAa,GAAA,aAAwB,kBAAoB,CAAA,KAAuC,aAAA,yBAAA,KAAA,aAAA,yBAAA,CAAA,sBAChG,4BAAa;WACd,EAAA;aAAM;;;;AAIb,eAAG,qBAAA,KAAA;IACH,MAAM;;IACJ,UAAI;MACJ,QAAI,iBAAqB,aAAS,uBAAA;QAClC,wBAAyB,OAAG;AAC5B,kBAAI,aAAuB,qBAAM,aAAA,uBAAA,iBAAA,OAAA,GACjC,aAAI,qBAA8B,IAAA,aAAA,oBAAA,IAAA,aAAA,iBAAA,IAAA,aAAA,eAAA,IAAA,aAAA,kBAAA,KAAA,aAAA,iBAAA,EAAA,CAAA,IAClC,MAAI,aAAc,4BAAA,QAAA,MAAA,aAAA,eAAA;;eAGZ,qBAAkB,KAAA;;;cAIrB;+BAAmB,WAAS,qBAAwB;;;;AC/RzD,SAAQ,uBAA0B,OAAA;AAClC,QAAM,EAAA,OAAA,IAAA,MAAsB,cAAa,EAAA,eAA6B,IAAS;AAChF,QAAA,sBAAA,aAAA,aAAA,gBAAA,QAAA,KAAA;AAED;AACE,SAAU,yBAAuB,OAAA;MAC/B;kCAC0B,UAAK,KAAU,MAAE,0BAAA,WAAA,MAAA,GAAA,GAAA,UAAA,UAAA;uBAC/B,KAAQ,iBAAS;YACzB,EAAA,OAAQ,IAAA,MAAmB,sBAAO,cAAA,EAAA,eAAA,IAAA;YAClC,aAAM,aAAa,gBAAa,KAAc;;;;AAKhD,SAAU,sBAAsB,OAAY;;AAChD,YAAA,KAAM,MAAA,0BAAqB,WAAA,MAAA,GAAA,GAAA,qBAAK,UAAsB;AACpD,UAAM,EAAA,KAAU,KAAU,UAAmB,aAAK,IAAA;AAClD,UAAM,gBAAgB,MAAM,MAAM,sBAAsB,sBAAsB;EAGhF,CAAE;AACJ;;AAGE,MAAA;YACQ,KAAA,MAAA,0BAAsB,WAAA,MAAA,GAAA,GAAA,+BAAA,UAAA;AAE5B,UAAM,oBAAW,GAAA,MAAA,WAAyB,uBAAA;AAC1C,UAAM,OAAO,MAAM,aAAa,OAAO,kBAAiB,MAAA,MAAA,aAAA,OAAA;AACxD,UAAM,WAAW,KAAC,CAAA,IAAA,GAAY,MAAC,WAAO,KAAc,CAAC,GAAA;;;AAIxD,SAAA,kBAAA,OAAA;AACD,MAAM;;AACJ,UAAA,WAAM,gBAAqB;AACzB,UAAM,OAAA,MAAW,aAAe,OAAG,kBAAA,MAAA,MAAA,aAAA,OAAA;AACnC,UAAM,WAAO,KAAM,CAAA,IAAA,GAAA,MAAa,WAAO,KAAA,CAAA,GAAiB;;;AAIvD,SAAA,uBAAA,OAAA;AACJ,MAAA,IAAA,IAAA,IAAA;AACD,YAAM,KAAU,MAAA,0BAAmC,WAAA,MAAA,GAAA,GAAA,0BAAA,UAAA;+SACjD,MAAA,WAAM,cAAqB,aAAA;AACzB,UACE,OAAM,MAAA,aAAc,OAAa,kBAAmB,MAAA,MAAA,aAAiB,OAAA;UACrE,WAAM,KAAA,CAAa,IAAC,GAAA,MAAA,WAAkB,KAAA,CAAA,GAAc;gBAC9C,KAAA,MAAA,0BAAgC,WAAc,MAAA,GAAA,GAAA,gCACpD,UAAA;UACA,EAAA,KAAM,KAAW,GAAA,GAAA,OAAyB,OAAA,OAAA,OAAA,MAAA,IAAA;UAC3C,WAAA,cAAA,aAAA,KAAA,GAAA,MAAA,WAAA,gBAAA;gBAAM,KAAA,MAAA,0BAAA,WAAA,MAAA,GAAA,GAAA,4BAAA,UAAA;UACL,EAAA,KAAM,KAAW,GAAA,GAAkB,OAAA,OAAA,OAAA,OAAA,MAAA,IAAA;UACpC,WAAA,cAAA,aAAA,KAAA,GAAA,MAAA,WAAA,gBAAA;MACD,UAAM,KAAU,MAAC,0BAA6B,WAAA,MAAA,GAAA,GAAA,+BAAA,UAAA;AAC9C,UAAM,WAAO,cAAkB,aAAQ,GAAA,MAAA,WAAiB,gBAAA;;;AAIvD,SAAA,wBAAA,OAAA;AACH,MAAA;YACQ,KAAK,MAAK,0BAA2B,WAAS,MAAO,GAAG,GAAI,iBAAC,UAAA;AAEnE,QAAAC,KAAM,IAAA,IAAU,IAAC,IAAA,IAAA,IAAc;AAC/B,UAAM,oBAAW,MAAe;AAC/B,UAAA,iBAAA,MAAA,sBAAA,gBAAA,IAAA,MAAA,cAAA,gBAAA,kBACH,UAAM,KAAA,UAAAA,MAAqB,MAAA,YAAA,WAAAA,MAAA,SAAKA,IAAA,kBAAA,WAA6B,KAAa,SAAA,GAAA,mBAAA,MAAA,iBAAA,KAAA,IAAA,UAAA,KAAA,UAAA,KAAA,MAAA,YAAA,WAAA,KAAA,SAAA,GAAA,kBAAA,WAAA,KAAA,SAAA,GAAA,eAAA,MAAA,cAAA,KACxE,UAAW,KAAK,UAAQ,KAAO,MAAK,YAAS,WAAc,KAAG,SAAK,GAAA,kBAAA,WAAA,KAAA,SAAA,GAAA,mBAAA,MAAA,iBAAA,KAAA,IAAA,UAAA,KAAA,UAAA,KAAA,MAAA,YAAA,WAAA,KAAA,SAAA,GAAA,kBAAA,WAAA,KAAA,SAAA,GAAA,eAAA,MAAA,cAAA,IACnE,sBAAiB,MAAA,mBAA2B,MAAO,QAAA,MAAA,OAAA,MAAA,iBAAA,GAAA,MAAA,cAAA,OAAA,OACnD,MAAM,sBAAW,cAAkB,MAAA,gBAAA,MAAA,qBAAA,IAAA,MAAA,cAAA,gBAAA,eAAA,GAClC,MAAA,YAAA,GAAA,6BAAA,KAAA;EACH,CAAA;;;;AC9EK,IAAM,UAAN,MAAc;AAAA,EACjB,cAAc;AAAA,EAAC;AAAA,EACf,gBAAgB,cAAc;AAC1B,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,aAAa,QAAQ,QAAQ,UAAU;AACnC,SAAK,UAAU,OAAI,KAAK,WAAW,KAAK,IAAI,GAAG,KAAK,SAAS,QAAQ,KAAK,SAAS,QACnF,KAAK,WAAW,UAAU,KAAK,aAAa,KAAK,WAAW,sBAAsB,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,EAC7G;AAAA,EACA,UAAU;AACN,QAAI;AACJ,QAAI,KAAK,QAAS;AAClB,UAAM,MAAM,KAAK,IAAI,GAAG,UAAU,MAAM,KAAK;AAC7C,QAAI,UAAU;AACd,UAAM,IAAI,KAAK,IAAI,KAAK,UAAU,UAAU,EAAE,GAAG,YAAY,IAAI,KAAK,QAAQ,YAAY,IAAI,KAAK;AACnG,QAAI,KAAK,GAAG,KAAK;AACjB,SAAK,IAAI,SAAS,IAAI,SAAQ,UAAU,OAAI,MAAM,KAAK,SAAS,aAAa,IAAI,UACjF,KAAK,IAAI,SAAS,IAAI,SAAQ,UAAU,OAAI,MAAM,KAAK,SAAS,aAAa,IAAI,UACjF,UAAU,KAAK,KAAK,iBAAiB,WAAW,MAAM,GAAG,KAAK,MAAM,IAAI,EAAE,GAAG,UAAU,KAAK,WAAW,QAAQ,KAAK,WAAW,KAC/H,KAAK,SAAS,WAAW,KAAK,SAAS,WAAW,KAAK,WAAW,sBAAsB,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,EACnH;AAAA,EACA,aAAa;AACT,yBAAqB,KAAK,QAAQ,GAAG,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,EAC9E;AAAA,EACA,qBAAqB;AACjB,WAAO,CAAC,CAAC,KAAK;AAAA,EAClB;AACJ;;;IC2ES,qBAAS;cACZ,OAAA;QACA;SACA,mBAAA,iBAAA,SAAA,KAAA,wBAAA,SAAA,MAAA;AACE,WAAC,uBAAc,iBAAA,OAAA;OACjB,GAAA,GAAA,KAAA,SAAkB,OAAA,KAAA,SAAA;MAClB,kBAAkB;MAClB,gBAAS;YACT,cAAS;MACT,cAAc;MACd,cAAY;MACZ,QAAQ;MACR,QAAQ;MACR,cAAY;MACZ,QAAA;MACA,QAAA;MACA,QAAA;MAEE,QAAC;MACH,mBAAkB;MAClB,qBAAY,IAAA;YACZ,eAAY;MACZ,cAAA;MAEE,QAAC;MACH,QAAQ;OACR,KAAA,kBAAA;MACE,QAAC;YACH,gBAAkB;MAClB,cAAc;MACd,cAAY;MACZ,YAAY;MACZ,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,UAAU;MACV,YAAA;IACF,GAAA,KAAK,mBAAmB;MACtB,OAAO;MACP,UAAU;OACV,KAAA,0BAAA;MACE,MAAC;YACC,0BAAM;MACV,mBAAA;MACE,QAAC;MACH,QAAA;MACA,cAAY;MACZ,cAAY;MACZ,UAAA;MACA,mBAAkB;YAClB,0BAAe,KAAA,wBAAA,KAAA,IAAA,GAAA,KAAA,4BAAA,KAAA,0BAAA,KAAA,IAAA,4BACI,KAAI,MAAA,GAAA,sBAAA,KAAA,MAAA,GAAA,uBAAA,KAAA,MAAA,2BACvB,KAAA,MAAA,GAAA,kBAAA,KAAA,MAAA,GAAA,YAAA,UAAA,KAAA,KAAA,OAAA,QAAA,kBAAA,WAAA,KAAA,SAAA,GAAA,eAAA,OAAA,KAAA,OAAA,kBAAA,wBAAA,KAAA,MAAA;;eAGG,KAAA,eAAA,MAA4B;AAEjC,UAAA,cAAA,KAAyB,OAAK,iBAAQ;AACtC,UAAA,KAAA,IAAA,GAAA,KAAsB,IAAK,KAAA,cAAQ,KAAA,OAAA,WAAA,MAAA,CAAA,GAAA,MAAA,KAAA,KAAA,GAAA;AACnC,UAAA,oBAAuB,KAAK,OAAQ;AACpC,SAAA,OAAA,iBAA6B,KAAA,gBAAQ,KAAA,OAAA,cAAA,KAAA,CAAA,MAAA,KAAA,OAAA,cAAA,MAAA,KAAA,OAAA,iBAAA,IACrC,KAAA,OAAA,WAAuB,KAAA,CAAM,GAAE;AAC/B,UAAI,SAAI,OAAO,cAAS,KAAA,OAAa,WAAA;SACnC,OAAA,WAAA,mBAAqC,2BAAA,MAAA,GAAA,sBAAA,OAAA,iBAAA,uBAAA,KAAA,MAAA,QACtC,OAAA,cAAA,iBAAA,QAAA;MACF,WAAA,KAAA,OAAA;MAED,YAA0B,KAAA,OAAA;MAExB,iBAAoB;MACjB,cAAe;IAClB,CAAA;;MAGA,aAAY;AACZ,WAAK,KAAO,OAAM;;kBAEjB;AAED,WAAK,KAAO,OAAA;;gBAIP,MAAO,eAAW,MAAA;AAEvB,UAAI,aAAA,KAAiB,OAAQ,oBAAkB;WAC7C,KAAA,IAAA,GAAA,KAAA,IAAuB,MAAK,aAAQ,KAAA,OAAA,WAAA,KAAA,CAAA,GAAA,OAAA,KAAA,KAAA,IAAA;UACpC,sBAAY,KAAc,OAAA;gBACxB,mBAAuB,MAAA,gBAAc,KAAA,OAAA,gBAAA,KAAA,CAAA,MAAA,KAAA,OAAA,gBAAA,MAAA,KAAA,OAAA,mBAAA,gBACrC,WAAY,KAAK,CAAA,IAAO;mBAKxB,QAAe,aAAY,KAAA,OAAA,WAAA;gBAC3B,WAAc,mBAAM,6BAAA,MAAA,2BACnB,QAAA,gBAAA,KAAA,OAAA,cAAA,iBAAA,QAAA;MACJ,WAAA,KAAA,OAAA;MACF,YAAA,KAAA,OAAA;MACG,iBAAU;MACZ,cAAkB;IACnB,CAAA;EACD;yBACqB,MAAA;AACpB,QAAA,KAAA,qBAAA,KAAA;AACD,UAAA,WAA4B,KAAA;AAE1B,SAAA,mBAAwB,MAAM,aAAC,iBAAsB,aAAA,iBAAA;;0BAGpC,QAAM;AAMvB,UAAM,cAAA,KAAmB,OAAO,iBAAQ;AACxC,SAAK,OAAO;AACZ,SAAK,OAAO,iBAAa,KAAA,KAAA,UAAqB,cAAiB,KAAC,OAAA,WAAmB,OAAA,mBAC7E,KAAC,OAAO,cAAgB,KAAK,CAAA,MAAA,KAAA,OAAA,cAAA,MAAA,KAAA,OAAA,iBAAA,SAClC,OAAA,WAAA,KAAA,CAAA,KAAA,OAAA,gBAAA,MAAA,MAAA,GAAA,uBAAA,KAAA,MAAA,GAGD,KAAK,OAAO,cAAW,iBAAY,QAAA;MAGnC,WAAe,KAAI,OAAI;MACnB,YAAQ,KAAA,OAAW;MAEnB,iBAAA;MACF,cAAY;;;4BAOV,QAAiB;uBACjB,KAAc,OAAM,oBAAA;SACrB,OAAE;SACJ,OAAA,mBAAA,KAAA,KAAA,UAAA,aAAA,KAAA,OAAA,WAAA,MAAA,GACF,gBAAA,KAAA,OAAA,gBAAA,KAAA,CAAA,MAAA,KAAA,OAAA,gBAAA,MAAA,KAAA,OAAA,mBAAA,IAED,KAAA,OAAA,WAA6C,KAAA,CAAA,KAAA,OAAA,kBAAA,MAAA,MAAA,GAAA,KAAA,OAAA,cAAA,iBAAA,QAAA;MACvC,WAAK,KAAA,OAAgB;MACvB,YAAO,KAAA,OAAA;MACR,iBAAA;MACD,cAAiB;IACjB,CAAA;;mBAMC,QAAA,GAAA,GAAA,SAAA;AACF,gCAAA,OAAA,SAAA,SAAA,OAAA,SAAA,KAAA,YAAA,SAAA,MAED,KAAA,YAAA,SAAsC,QAAA,KAAA,YAAA,eAAA,OAAA,UAAA,GACpC,KAAA,YAAiB,eAAe,OAAA,UAAgB,GAAG,KAAA,YAAA,SAAA,GACnD,KAAA,YAAM,SAAoB,GAAA,KAAK,YAAO,eAAe,SAAA,OAAA,aAAA,UAAA,GAAA;;qBAExC;WACX,KAAK,YAAO;;mBAEF;AACZ,SAAA,YAAA,oBAAoC,mBAAA,KAAA,KAAA,YAAA,oBAAA,WAAA;AACpC,UAAK,SAAO,KAAA,YAAc,QAAA,SAAuB,KAAE,YAAA,QAAA,SAAA,KAAA,YAAA;QACjD,KAAA,IAAS,MAAM,KAAC,KAAO,KAAA,IAAA,MAAc,KAAA,GAAA;AACrC,YAAA,YAAiB,OAAO,YAAA,iBAAgB,OAAA,gBAAA,EAAA,WAAA,cAAA,SAAA,WAAA,IAAA,KAAA,OAAA,2BAAA,WAAA,cAAA,GAAA,aAAA,KAAA,YAAA,eAAA,KAAA,OAAA,cAAA,YAAA,KAAA,MAAA,SAAA,KAAA,OAAA,cAAA,SAAA,GAAA,oBAAA,gBAAA,KAAA,YAAA,OAAA,UAAA,IAAA,KAAA,OAAA,aAAA,OAAA,kBAAA,KAAA,MAAA,GAAA,eAAA,KAAA,OAAA,cAAA,4BAAA,CAAA,EAAA,cAAA,iBAAA,EAAA,WAAA,aAAA,IAAA,KAAA,aAAA,QAAA,KAAA,qBAAA,UAAA,EAAA,QAAA,IAAA,qBAAA,YAAA,EAAA,QAAA,EAAA;AAKxC,UAAA;AACA,UAAA,qBAAoB,YAAA,EAAA,QAAA,MAAA,aAAA,QAAA,GAAA;AACnB,sBAAA,qBAAA,YAAA,EAAA,QAAA,IAAA,aAAA,QAAA,IAAA,SAAA,SACJ,KAAA,OAAA,gCAAA,cAAA,YAAA,WAAA,cAAA;AACD,cAAA,YAA0B,KAAc,OAAA,iBAAA,WAAA,cAAA;AAChC,aAAA,OAAa,aAAY,iBAAmB,iBAAG,KAAA,KAAA,OAAA,cAAA,iBAAA,mBAAA;UAC/C,WAAA,UAA2B,KAAA,OAAO,cAAiB,cAAA;UAC7C,SAAA,UAAmB,KAAK,OAAK,cAAU,YAAkB;UACzD;UACL;UACN,OAAA;UACW,QAAA;QACP,CAAA;MACH;AACA,UAAA,KAAA,OAAgB,cAAQ,kBAAgB,cAAA,qBAAA,KAAA,OAAA,cAAA,kBAAA,cAAA,mBAAA;AAKxC,cAAA,SAAiB,qBAAY,YAAA,KAAA,MAAA;AAC7B,aAAA,OAAc,qBAAM,OAAA,YAAA,OAAA,gBAAA,OAAA,YAAA,OAAA,cAAA,GACnB,qBAAA,KAAA,OAAA,OAAA,GAAA,KAAA,OAAA,sBAAA,wBAAA,GAkBJ,KAAA,OAAA,oBAAA,GAAA,KAAA,OAAA,WAAA,uBAAA;MAED,WAAiB,KAAwB,OAAwB,cAAe,kBAAA,cAAA,kBAAA,KAAA,IAAA,KAAA,MAAA,SAAA,KAAA,OAAA,cAAA,SAAA,CAAA,KAAA,GAAA;AAC1E,cAAO,SAAS,qBAAuB,YAAE,KAAA,MAAA;AAC3C,aAAS,OAAO,qBAAO,OAAA,YAAA,OAAA,gBAAA,OAAA,YAAA,OAAA,cAAA,GACxB,KAAA,OAAA,cAAA,kBAAA,cAAA,sBAAA,KAAA,OAAA,sBAAA,wBAAA,GACI,KAAA,OAAY,WAAc,uBAAA,MAAA,KAAA,OAAA,WAAA,QAAA,QAAA,GAC1B,KAAA,OAAY,WAAS,eAAO,QAAA;MAC7B,OAAC;AACA,YAAA,oBAA2B,SAAO,qBAAY,IAAA,KAAA,OAAA,iBAAA,GAAA,oBAAA,CAAA,IAAA,KAAA,OAAA,UAAA,GAAA,aAAA,OAAA,UAAA,GAAA,MAAA,IAAA,GAC9C,YAAY,aAAW;AACvB,cAAW,kBAAY;AACvB,iBAAY,gBAAe,eAAQ,gBAAA,YAAA,UAAA,MAAA,OAAA,UAAA,KAAA,gBAAA,YAAA,OAAA,KAAA,OAAA,cAAA,cAAA,KAAA,OAAA,OAAA,KAAA,OAAA,cAAA,cAAA,IAAA,mBAAA,gBAAA;AACjC,8BAAuB,UAAO,gBAAA,OAAA,YAAA,QAAA,eAAA;QACtC,WAAA,WAAA,aAAA;AAEe,cAAA,mBAAA;AACF,iBAAA,iBAAmB,mBAAA,iBAAA,gBAAA,UAAA,MAAA,OAAA,UAAA,KAAA,iBAAA,gBAAA,OAAA,KAAA,OAAA,cAAA,cAAA,KAAA,OAAA,OAAA,KAAA,OAAA,cAAA,cAAA,IAAA,oBAAA,iBAAA;AAChC,+BAAA,UAAA,iBAAA,OAAA,aAAA,QAAA,gBAAA;QACD;MACM;AACF,WAAK,OAAA,WAAY,gBAAoB;;AAGvC,SAAA,YAAe,SAAK,OAAA,KAAY,gBAAO,WAAA,UAAA,OAAA,aAAA,UAAA,CAAA,GACvC,KAAA,YAAe,SAAK,MAAY,KAAA,YAAO,SAAA,GAAA,KAAA,YAAA,SAAA,GACvC,KAAA,YAAe,oBAAiB;;kBAExB,GAAA;UACN,SAAM,KAAA,YAAiB;WACvB,aAAQ,UAAW,GAAA;UAKnB,KAAM,KAAA,OACJ,aAAK,0BAAwB,GAAA,KAAA,EAAA,IAAA,IAAA,KAAA,KAAA,OAAA,aAAA,0BAAA,GAAA,KAAA,EAAA,IAAA;qBACxB,UAAO,IAAA,KAAc,YAAY,UAAW,IAAA,oBAAqB,QAAA,IAAc,IAAA,MAAW,IAAA,UAEjG,UAAM,KAAA,KAAiB,OAAG,aACxB,cAAK,KAAY,KAAO,KAAA,YAAkB,oBAAoB,CAAC,KAAA,OAAO,cACtE,mBACA,UACF,YAAM,oBACC,aAAO,KAAa,YAAC,mBAA8B,GAAC,CAAA,QAC3D,YAAM,oBAAe,gBAA4B,CAAAC,KAAAC,QAAA;AAQjD,WAAA,YAAgB,UAAWD,KACzB,KAAA,YAAoB,UAAEC,KAAA,oBAAA,QAAAD,KAAAC,KAAA,MAAA,IAAA,QACnB,OAAA,aAAoB,cAAa,OAAO,UAAK,CAAA,GAAA,MAAA,KAAqB,OAAA,aAAc,cACnF,KAAA,YAAA,oBAAA,WAAA;UAEF,OAAI,UAA8B,IAAA,OAAA,UAAA,SAAA,KAAA,OAAA,aAAA,aAAA,KAAA,OAAA,qBAAA,KAAA,KAAA,KAAA,YAAA,oBAAA,KAAA,OAAA,cAAA,mBAAA,UAClC,YAAI,oBAAqB,aAAqB,KAAE,YAAK,mBAAwB,GAAA,CAAA,oBAC3E,oBAAc,gBAAqB,CAAYD,KAAEC,QAAS;WAE1D,YAAY,UAAAD,KAAA,KAAA,YAAgC,UAAAC,KAAc,oBAAY,QAAWD,KAAAC,KAAA,MAAgB,IAAA,QACjG,OAAM,aAAY,cAAY,OAAA,UAAiB,IAAS,OAAE,UAAe,QAAC,KAAA,OAAA,iBAAA,QAE1E,OAAQ,aAAQ,eAAa,KAAA,OAAiB,oBAAoB,IAAA,KAAA,OAAA,qBAAA,KAAA,YAAA,oBAAA,WAAA;eAChE,YAAY,oBAAc,mBAAiB,IAAA,KAAiB,YAAE,oBAAA,WAAA,IAAA,KAAA,YAAA,oBAAA,0BAC5D,gBAAqB;;6BAErB,GAAA,GAAY,cAAA,YAAA;uBACZ,aAAU,YAAA,KAAA,cAAA,WAAA,MAAA,KAAA,cAAA,SAAA,2BACV,eAAgB,OAAA,UAAA,GAAA,KAAA,cAAA,aAAA,OAAA,UAAA,IAAA,OAAA,UAAA,0BAChB,SAAQ,GAAA,KAAS,cAAA,SAAA,GAAA,KAAA,cAAA,eAAA;;sBAEpB;WACF,KAAA,cAAA;;mBAGM,GAAA;sBAEC,KAAS,OAAA,aAAoB,cAAa,YAAa,SAAA,IAAA,KAAA,cAAA;aAC7D,IAAK,MAAM,KAAC,GAAA;YAMZ,WAAA,iBAA0B,WAAe,YAAC,KAAA,cAAA,OAAA,UAAA,IAAA,KAAA,cAAA,OAAA,UAAA,IAAA,KAAA,cAAA,OAAA,UAAA,SAAA,KAAA,OAAA,aAAA,OAAA,kBAAA,KAAA,MAAA,GAAA,eAAA,KAAA,OAAA,cAAA,4BAAA,CAAA,EAAA,cAAA,QAAA,GAAA,aAAA,WAAA,YAAA,aAAA,YAAA,aAAA,SAAA,eAAA,KAAA,cAAA,QAAA,eAAA,aAAA,cAAA,OAAA,KAAA,cAAA,OAAA,SAAA,eAAA,KAAA,cAAA,OAAA,cAAA,YAAA,KAAA,cAAA,OAAA,YAAA,iBAAA,KAAA,cAAA,OAAA,gBAAA,EAAA,UAAA,UAAA,WAAA,cAAA,SAAA,WAAA,IAAA,KAAA,OAAA,2BAAA,WAAA,cAAA;UAC1C,cAAY;UACZ,WAAW,aAAC,KAAA,OAAsB,6BAAA,YAAA,WAAA,cAAA,cAC7B,QAAO,MAAA,IAAW,KAAA,YAAA,EAAA,QAAyB,MAAA,cAAA,UAAA,KAAA,OAAA,2BAAA,YAAA,WAAA,cAAA,cACjD,QAAA,MAAA,IAAA,KAAA,UAAA,EAAA,QAAA,MAAA,cAAA,aAAM,OAAA,cAAA,kBAAA,cAAA,qBAAA,KAAA,OAAA,cAAA,kBAAA,cAAA,kBAAA,MAAA,OAAA,sBAAA,wBAAA,QAEL,OACM,oBAAQ,GAAc,KAAA,OAAa,WAAK,uBAA4B;WAAA;YACxE,WAAS,WAAW;AAEpB,gBAAM,OAAM,YAAG,IAAA,KAAA,OAAqB,iBAAiB,GAAQ,WAAA,CAAA,IAAA;AAC7D,uBAAY,aAAA,KAAA,IACV,GAAA,aAAiB,aACV,SAAc,KACrB,cAAiB,aACV,IAAA;mBAEL,YAAY,WAAc;gBAC5B,UAAY,KAAA,OAAA,iBAAsB,GAAA,QAAA;uBAC7B,aAAiB,SAAC,UAAA,KAAsB,cAAG,YAAA;;qBAC3C,aAAA,SAAA,aAAA,UAAA,KAAA,GAAA,QAAA,QAAA,KAAA,aAAA,SAAA,aAAA,UAAA,KAAA,WACL,gBAAY,aAAW,aAAkB,SAAA,WAAA,MAAA,aAAA,UAAA,KAAA,eACrC,WAAQ,uBAA0B,WAAU,gBAAA,cAAA,CAAA,QACjD,iBAAA,GAAA,mBAAA,KAAA,QAAA,cAAA,WAAA,cAAA,gBAEF,aAAA,UAAA,CAAA;;eACC,cAAa,WAAA,OAAiB,KAAK,cAAc,SAAC,MAAA,eAAoB,KAAA,OAAqB,aAAM,iBAAA,iBAAA,GAAA;cACjG,YAAA,KAAmB,OACX,iBACmB,WAAW,cACtB;aAMhB,OAAI,cAAgB,iBAAS,mBAAA;qBACvB,UAAA,KAAkB,OAAO,cAAA,cAAA;mBAC7B,UACG,KAAA,OAA0B,cAAW,YAAA;;;;kBAKtC;;;6BAGC,gBAAiC;;;uBAE/B;oBACL,OAAI,aAAgB,0BAAU,GAAA,KAAA,EAAA,IAAA,IAAA,eAAA,KAAA,OAAA,aAAA,cAAA;8BAE3B,UAAA,GAA0B;yBAC1B,aAAA,cAA2B,OAAgB,aAAW,SAAuB,eAAW,aAAA,cAAA,YAAA,aAAA,WAAA,gBAAA,KAAA,OAAA,cAAA,eAAA,YAAA,aAAA,YAAA,iBAAA,aAAA,gBAAA,WAAA,KAAA,OAAA,iBAAA,WAAA,cAAA,EAAA,aAAA;+BACxF,KAAA,OAA2B,aAAe,cAAa,aAAO,CAAA,KAAa,IAAC,cAAe,aAAA,UAAA,QAAA;oCACnF,KAAM,OAAM,cAAO,qBAA6B,YACzD,KAAA,OAAA,cAAA,mBAAA,aAAA,UAAA,kCACA,oBAAoB,cAA2B,WAAgB,KAAA,OAAA,aAAA,cAAA,aAAA,CAAA,YAAA,GAAA,GAAA,aAAA,IAAA,6BAChE,SAAA,aAAA,UAAA,KAAA,GAAA,QAAA,QAAA,KAAA,aAAA,SAAA,aAAA,UAAA,KAAA,2BACG,aAAgB,aAAa,SAAA,WAAA,MAAA,aAAA,UAAA,KAAA,wBAC9B,UAAA,aAAkC,gBAAa,cAAQ,IAAA,wBAAkB,yBAC3E,GAAA,mBAAA,KAAA,QAAA,cAAA,WAAA,cAAA,0BACF,gBAAA;;4BAEJ,QAAA,GAAA,GAAA,cAAA,UAAA;SACD,wBAAuB,WAAA,MAAiB,KAAC,wBAAA,oBAAA,aAC1C,wBAAA,SAAA,GAAA,KAAA,wBAAA,SAAA,GACD,KAAK,wBAAqB,eAAM,cAAA,KAAA,wBAAA,uBAAA,UAChC,KAAI,uBAAqB,MAAM;;6BAE9B;AACD,WAAK,KAAA,wBAA0B;;0BAEd,SAAW;AAC5B,UAAK,eAAY,KAAA,OAAA,cAAsB,cAAA,gBAAA,KAAA,gBAAA,OAAA,YAAA,mBAAA,KAAA,gBAAA,OAAA,gBAAA,cAAA,KAAA,wBAAA,kBAAA,YAAA,iBAAA,KAAA,wBAAA,kBAAA,gBAAA,OAAA;MACxC,mBAAA,KAAA,OAAA,iBAAA,eAAA,gBAAA,EAAA,YAAA;MACD,iBAAwC,KAAA,OAAA,iBAAA,aAAA,cAAA,EAAA,YAAA;MACtC,MAAM,WAAc,KAAA,wBAAmB,wBAAA,WAAA,KAAA,wBAAA,wBAAA,eAAA,eAAA,YAAA,KAAA,wBAAA,wBAAA,WAAA,KAAA,wBAAA,wBAAA,eAAA,gBAAA,YAAA,KAAA,wBAAA,wBAAA,YAAA,KAAA,wBAAA,wBAAA,eAAA,iBAAA,eAAA;IACvC;AAEA,WAAM,KAAK,OAAK,QAAO,IAAA,GAAA,KAAa,0BAA4B,GAAA,KAAA,OAAA,WAAA,gBAAA,GAChE,KAAA,wBAAe,WAAA,OAAA;;2BAEC,GAAO;AACvB,UAAM,KAAK,KAAI,wBAAA,QAAA,KAAA,KAAA,wBAAA,QAAA,KAAA,EAAA,IAAA,IAAA,KAAA,EAAA,IAAA,IAAA,oBAAA,KAAA,wBAAA,mBAAA,IAAA,kBAAA,UAAA,IAAA,kBAAA,UAAA,QAAA,GAAA,IAAA,kBAAA,UAAA,IAAA,kBAAA,UAAA,SAAA;AACf,SAAA,OAAW,WAAQ,QAAA,6BAAA,GAAA,GAAA,IAAA,IAAA,IAAA,EAAA,GAEnB,KAAK,OAAA,WAAkB,gBAAO;;wBAG9B,GAAoB;AAGpB,SAAI,iBAAkB,WAAS,MAAM,KAAC,iBAAa,QAAgB,EAAA;;yBAG5D;WACL,KAAK,iBAAY;;wBAEV;0BACL,WAAoB;;uBAGX,GAAA;;cAER,iBAAA,SAAA;UACD,SAAC,EAAA,QAAA,KAAA,iBAAA;QACJ,KAAA,IAAA,MAAA,KAAA,GAAA;UAAM,QACE,KAAA,OAAW,iBAAU;AAC1B,YAAK,WAAO,KAAA,IAAa,KAAA,OAAa,sBAAY,gBAAiB,IAAA,KAAA,OAAA,cAAA,gBAAA,iBAAA,UAAA,KAAA,KAAA,OAAA,QAAA,cAAA,kBAAA,WAAA,KAAA,KAAA,GAAA,GAAA,WAAA,KAAA,IAAA,KAAA,OAAA,cAAA,gBAAA,iBAAA,UAAA,KAAA,KAAA,OAAA,QAAA,cAAA,kBAAA,WAAA,KAAA,KAAA,CAAA;AACrE,eACA,KAAA,QAAA,aAAA,QAAA,WAAA,SAAA,KAAA,QAAA,aAAA,QAAA,WAEA,KAAK,OAAA,iBAAY,OAAiB,KAAO,OAAO,QAAC,MAAa,OAAC,KAAA,OAAgB,iBAAO,GAAA,KAAA,OAAA,cAAA,OAAA,OAEtF,KAAK,OAAA,wBAA+B,MAAC,OAAY,KAAK,OAAC,iBAAY,KAAA,OAAuB,iBAAE,IAAA,OAAA,OAC5F,KAAK,OAAA,QAAY,GAAA,KAAA,iBAAoB,QAAgB,EAAC;;;qBAGpD;mBAEK,KAAO,OAAA,aAAa,aACjB;gBAEJ;gBACF,OAAK,UAAY,GAAA,IAAA,OAAA,UAAoB,GAAA,QAAa,OAAA,UAAA,OAAA,SAAA,OAAA,UAAA;WACnD,OAAA,WAAA,QAAA,aAAA,GAAA,GAAA,OAAA,QAAA,MAAA,GAAA,KAAA,OAAA,WAAA,gBAAA;;;mBAEU,GAAC;SACd,OAAK,aAAY,aAAA,SAAoB,MAAY,KAAC,OAAA,WAAA,QAAA,aAAA,QACnD,OAAA,WAAA,gBAAA;;4BACkB,QAAA;QAClB;AAED,cAAK,KAAO,KAAA,OAAW,aAAiB,gBAAC,WAAA,WAAA,MAAA,GAAA,aAAA,UAAA,CAAA,GAG1C,KAAA,OAAA,aAAA,gBAAA,SAAA;AAED,UAAA,gBAAgC,KAAsB,OAAE,cAAsB;AAI5E,WAAK,aAAc,UAAU,GAAG;AAChC,UAAK,IAAA,OAAA,UAAsB,GAAG,IAAI,OAAC,UAAA,GAAA,QAAA,OAAA,UAAA,OAAA,SAAA,OAAA,UAAA;AACnC,SAAK,OAAA,WAAc,QAAS,qBAAO,GAAA,GAAA,OAAA,QAAA,QAAA,aAAA,GACnC,KAAK,OAAA,WAAc,gBAAe;;8BAEf;AACnB,QAAI;AACJ,cAAK,KAAA,KAAc,OAAA,aAAe,gBAAa,WAAA,WAAA,MAAA,GAAA,aAAA,UAAA,CAAA,GAChD,KAAA,OAAA,aAAA,gBAAA,SAAA,MAAA,KAAA,OAAA,WAAA,QAAA,qBAAA,GACD,KAAA,OAAA,WAAiB,gBAAA;;EAEjB,kCAAC;AACD,UAAA,SAA0B,KAAA,OAAA,aAAA,wBAAA,mBAAA,IAAA,OAAA,UAAA,GAAA,IAAA,OAAA,UAAA,GAAA,QAAA,OAAA,UAAA,OAAA,SAAA,OAAA,UAAA;AACxB,SAAA,OAAM,WAAiB,QAAO,qBAAa,GAAA,GAAc,OAAA,QAAW,QAAA,IAAA,GACpE,KAAA,OAAY,WAAW,gBAAe;;oCAKnB;gBAEX,aAAK,wBAAgC,oBAAA,MAAA,KAAA,OAAA,WAAA,QAAA,2BAAA,eACrC,WAAK,gBAAqB;;mCAIP;UACzB,OAAM,KAAA,OAAa,aAAc,wBAAsB;SAGvD,OAAM,WAAY,eAAQ,uBAAqB,IAAA;UAC/C,EAAA,cAAqB,gBAA0B,IAAA,KAAA,OAAA,eAAA,EAAA,iBAAA,mBAAA,KAAA,IAAA;QAC/C,gBAAa,yBAA0B,cAAQ;UAC/C,qBAAqB,oBAAmB,KAAO,OAAA,SAAa,cAAA,eAAA,GAAA,uBAAA,oBAAA,KAAA,OAAA,SAAA,cAAA,iBAAA;SAC5D,OAAM,cAAgB,kBAAe,cAAiB,oBAAC,KAAA,OAAA,cAAA,kBAAA,cAAA,sBAAA,KAAA,OAAA,cAAA,kBAAA,cAAA,qBAAA,KAAA,OAAA,cAAA,kBAAA,cAAA,qBAAA,iBAAA,qBAAA,MAAA,CAAA,6BACnC,qBAAsB,MAAO,CAAA,GAAA,eAAe,mBAAA,MAAA,CAAA,2BAI9D,mBACW,MAAA,CAAA,MAAY,iBACd,KACV,OAAO,8BAAQ,qBAAsC,KAAA,kBAElD,KAAW,OAAQ,8BAAC,mBAAA,KAAA;UACxB,eAAa,KAAK,OAAQ,WAAA,QAAA,sBAAA,gBAAA,uBAAA;gBACpB,WAAQ,QAAA,qBAA6B,aAAY,UAAW,GAAA,aAAgB,UAAA,GAAA,aAAA,UAAA,OAAA,aAAA,UAAA,QAAA,cAAA,KAAA;uBACrE,KAAA,OAAS,WAAc,QAAA,sBAA4B,cAAc,qBAAM;SACnF,OAAA,WAAA,QAAA,qBAAA,WAAA,UAAA,GAAA,WAAA,UAAA,GAAA,WAAA,UAAA,OAAA,WAAA,UAAA,QAAA,YAAA,KAAA,eAAM,WAAA,gBAAA;;mCAEe;SACrB,OAAA,aAAA,wBAAA,OAAA,MAAA,KAAA,OAAA,WAAA,eAAA,uBAAA,QACD,OACM,WAAQ,QAAA,qBAAgC,GAAA,KAAA,OAAc,WAAA,gBAAiB;;yBAGhE,gBAAC;iBACP,iBAAO,SAAmB,eAAG,SAAA,UAAA,GAAA;YAClC,SAAW,eAAY,SAAA,CAAA;aACxB,aAAA,QAAA,KAAA,OAAA,cAAA,qCAAA,SAAA,UAAM,aAAA,UAAA,KAAA,OAAA,cAAA,qCAAA,WAAA,UACL,aAAa,UAAa,KAAA,OAAA,cAAA,qCAAA,MAAA,uBACd,aAAe,KAAI,OAAK,cAAO,qCAAsC,WAAA,eAC/E,WAAa,gBAAkB;;;2BAEtB,gBAAc;iBACvB,iBAAgB,SAAW,eAAC,SAAoB,UAAU,GAAA;qBAC1D,eAAa,SAAa,CAAA;aAC3B,aAAA,QAAA,KAAA,OAAA,cAAA,mCAAA,SAAA,UACD,aAAa,UAAa,KAAA,OAAS,cAAa,mCAAiB,WAAA,UACjE,aAAI,UAAA,KAAA,OAAA,cAAE,mCAAmC,MAAS,UAClD,aAAY,aAAZ,KAAA,OAAY,cAAA,mCAAe,WAAU,QACrC,OAAK,WAAO,gBAAW;;;;4BAKpB,OAAc,cAAiB,WAAA,gBAAA;8BACjB,MAAM,cAAQ;2BAElB;;sBAER,OAAO,qBAAc;yBACH,SAAY,UAAc,UAAe,WAAA,IAAA,MAAA,2BAAA,WAAA,cAAA;wBACrD,oBAAqB;eAC9B,aAAY,UAAA;gBACZ,aAAU,UAAA;eACV;;;QAGH;QACG;QACL;QACF,eAAA;MACD,CAAA;IACE,MAAM,mBAAiB;AACvB,QAAA,iBAAe;AACf,YAAQ,gBAAW,gBAAA;AAEnB,oBAAkB,OAAO;AACzB,YAAA,WAAa,aAAqB,SAAS,KAAA,UAAA,qBAAA,KAAA,IAAA;AAC3C,UAAM,UAAY;AACZ,cAAO,iBAAa,SAAQ,SAAA,UAAA,UAAA,6BAAA,KAAA,IAAA,GAAA,gBAAA,SAAA,SAAA,cAAA;AAC5B,0BAAe,SAAa,YAAa,aAAA,GAAA,SAAA,WAAA,eAAA,cAAA;MAC/C;IAEA;;;SAKA,oBAAiB,QAAW,IAAA,IAAA,UAAe,OAAA;QAE3C,YAAa,OAAQ,YAAO,iBAAa,OAAc;QACvD,OAAI,aAAc,KAAA,OAAa,UAAU,IAAK,EAAG,GAAA,MAAU,OAAA,cAAA,kBAAA,cAAA,iBAAA,MAAA,OAAA,aAAA,KAAA,OAAA,UAAA,IAAA,EAAA,IAAA,KAAA,eACvD,OAAS,aAAQ,SAAe,QAAK,GAAO,MAAA,OAAa,WAAC,uBAAkB,WAAA,gBAAA,QAAA,EAAA;;;;ACnoBpF,IAAAC,WAAa;IACH,oBAAA;gBAUN;SACA,gBAAgB;MAChB,WAAA,CAAA;MAuGH,cAAA,CAAA;IA9FG;;WAKK,UAAK;AAEV,UAAM,OAAK,KAAO,cAAG,UAAA,IAAA,MAAA,KAAA,cAAA,UAAA,IAAA,IAAA,CAAA;AACrB,SAAK,KAAA,QAAa;UAChB,KAAIA;WACJ,KAAQ,cAAA,aAAA,EAAA,IAAA;MACR;;cAEE,MAAM;AACN,eAAK,KAAO,cAAU,aAAA,EAAA;AACtB,cAAK,QAAK,KAAA,QAAc,QAAU;aAChC,OAAO,OAAK,CAAA,GAAA,KAAA,cAAwB,UAAM,IAAA,EAAA,UAAA,OAAA,KAAA,cAAA,UAAA,IAAA;;;;MAIhD,UAAU,UAAA;AACX,QAAA;AAKC,QAAA,UACA;;AAEI,WAAA,oBAAU,MAAA,QAAA;WACZ;AACA,YAAK,KAAA;AACN,UAAA,CAAA,KAAA,cAAA;gBAAM,KAAA,KAAA,cAAA,aAAA,EAAA,MAAA,WAAA,MAAA,GAAA,OAAA;;;mBAGH,MAAO,UAAA,QAAA;SACR,GAAA,MAAA,QAAA;;sBAEF,MAAA,UAAA;AACF,QAAA,KAAA,cAAA,YAAA,OAAA,KAAA,cAAA,cAAA;AAED,YAAA,eAEE,KACA,cAAY,aAAA,GAAA;AAER,mBAAU,SAAU,QAAA,aAAA,aAAA,YAAA,KAAA,IAAA,GAAA;IACzB;EAED;eACW,MAAC;WACR,CAAA,CAAA,KAAO,iBAAA,CAAA,CAAA,KAAA,cAAA,UAAA,IAAA;;gBAEJ,MAAS,OAAS;QACrB,CAAA,KAAM,cAAe,QAAK,CAAA;UAC1B,OAAI,KAAA,cAAsB,UAAQ,IAAA;kBAC3B,KAAI,IAA0B,cAAA,SAAA,KAAA,MAAA,KAAA,CAAA,EAAA,OAAA,OAAA,gBAAA,CAAA,CAAA,IAAA,CAAA;;YAEtC;AACF,WAAA,KAAA;EAED;;;;ICjFO,mBAAU;cACV,QAAU;AACf,SAAK,SAAO,QAAU,KAAA,UAAc,OAAQ,SAAA,KAAA,UAAA,OAAA,cAAA,SAC5C,KAAK,UAAA,OAAiB,cAAA,SAAA,KAAA,eAAA;EACxB;EACA,iBAAc;AACZ,UAAM,cAAc,CAAC,KAAK,SAAQ,cAAA,CAAA,KAAA;AAClC,QAAA,UAAM,OAAe,kBAAa,UAAA,OAAA;AAElC,SAAI,eAAgB,eAAC,KAAiB,OAAA,cAAA,kBAAA,cAAA,oBAAA,KAAA,OAAA,cAAA,kBAAA,cAAA,sBAAA,KAAA,QAAA,QAAA;AAClC,eAAO,IAAG,GAAM,IAAC,KAAA,QAAgB,QAAC,KAAA;AAEnC,cAAA,SAAW,KAAA,QAAA,CAAA;AACV,YAAA,eAAW,OAAA,KAAA,OAAA,cAAA,cAAA,GAAA;AACN,gBAAO,gBAAc,qBAAkB,OAAc,KAAA,OAAA,cAAgB,cAAA,CAAA;AACrE,oBAAO,KAAA,IAAc,SAAA,cAAkB,QAAc,CAAA;QACxD;AAEJ,YAAK,eAAe,OAAK,KAAQ,OAAS,cAAI,YAAA,GAAA;AAC5C,gBAAM,gBAAc,qBAAW,OAAA,KAAA,OAAA,cAAA,YAAA,CAAA;AAC3B,oBAAA,KAAe,IAAA,SAAY,cAAO,QAAc,CAAA;;aAElD,OAAO,cAAY,kBAAS,cAAyB,oBAAA,KAAA,OAAA,cAAA,kBAAA,cAAA,qBAAA,OAAA,YAAA,OAAA,SAAA,KAAA,CAAA,GAAA,MAAA,qBAAA,EAAA,KAAA,OAAA,cAAA,cAAA,CAAA,EAAA,QAAA,IAAA,qBAAA,EAAA,KAAA,OAAA,cAAA,cAAA,CAAA,EAAA,QAAA,CAAA;;sBAEnD,KAAW,UAAW,qBAAY,OAAc,IAAA,gBAAe,KAAA,UAAA,qBAAA,OAAA;oBACjE,aAAM,aAAqC,KAAW,IAAC,KAAO,OAAA,cAAc,4BAAe,CAAA;sBACpF,KAAG,OAAS,cAAS,UAAc,uBAAW,IAAA,KAAA,OAAA,GAAA,aAAA,WAAA,QACtD,OAAA,cAAA,eAAA,KAAA,OAAA,cAAA,QAAA,QAAA,oBAGM,KAAO,OAAA,cAAc,UAAkB,qBAAc,KAAA,OAAgB,cAAA,SAAA,IAAA,KAAA,OAAA,GAAA,aAAA,IAAA,eACtE,cAAQ,eAAc,KAAA,OAAkB,cAAc,QAAA,QAAiB;;;4BAKvE,uBACE,cAA4B,uBAAqB;;oBAGrD,cAAG,kBAAA,cAAA,qBAAA,KAAA,OAAA,cAAA,kBAAA,cAAA,qBAAA,iBAAA;AAAA,UAAA,KAAA,OAAA,cAAA,kBAAA,cAAA,kBAAA;4BACN,qBAAA,KAAA,gBAAA,qBAAA,KAAA,gBAAA,YAAA,KAAA,gBAAA,YAAA,GAAA;AACF,gBAAA,kBAAA,KAAA,QAAA,YAAA,EAAA,SAAA,qBAAA;AAED,eAAA,QAAgB,YAAY,EAAG,SAAA,OAAA,uBAA+B,CAAA,GAAA,KAAA,QAAA,YAAA,EAAA,aAAA,KAAA,QAAA,YAAA,EAAA,WAAA,CAAA,IAC9D,KAAA,QAAgB,YAAY,EAAG,SAAA,OAAA,uBAA+B,GAAA,eAAA;QAE9D;AACA,kBAAI,KAAW,UAAE,KAAA,KAAA,QAAA,YAAA,MAAA,WAAA,KAAA,SAAA,GAAA,aAAA,WAAA,MAAA,GAAA,KAAA,CAAA,GAAA,MAAA,qBAAA,EAAA,KAAA,OAAA,cAAA,cAAA,CAAA,EAAA,QAAA,IAAA,qBAAA,EAAA,KAAA,OAAA,cAAA,cAAA,CAAA,EAAA,QAAA,CAAA;iBACf,gBAAW,qBAAe,KAAU,gBAAA,qBAA2B,KAAK,gBAAU,YAAa,KAAA,gBAAa,YAAA,GAAA;cACxG,kBAAY,KAAa,QAAC,YAAmB,EAAC,SAAO,qBAAsB;aAC5E,QAAA,YAAA,EAAA,SAAA,OAAA,uBAAA,CAAA,GAAA,KAAA,QAAA,YAAA,EAAA,aAAA,KAAA,QAAA,YAAA,EAAA,WAAA,CAAA,IACD,KAAI,QAAA,YAAa,EAAA,SAAA,OAAA,uBAAA,GAAA,eAAA;;;;aAQhB,SAAA;SACF,UAAA,SAAA,KAAA,eAAA;EACH;;;;ACzDK,SAAS,kBAAkB,SAAS,YAAY,gBAAgB;AACnE,MAAI,IAAI;AACR,QAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,UAAQ,aAAa,YAAY,GAAG,GAAG,QAAQ,UAAU,IAAI,SAAS,GAAG,QAAQ,MAAM,UAAU,QACjG,QAAQ,MAAM,SAAS,GAAG,QAAQ,GAAG,MAAM,QAAQ,KAAK,MAAM,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAC9F,QAAM,SAAS,QAAQ,gBAAgB,UAAU,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,gBAAgB,KAAK,GAAG,UAAU,QAAQ,iBAAiB,UAAU,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,iBAAiB,KAAK;AAClQ,SAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,OACrF,QAAQ,MAAM,SAAS,UAAU,SAAS,QAAQ,MAAM,QAAQ,SAAS,QAAQ,OACjF;AACJ;AAEO,IAAM,QAAN,cAAoB,YAAY;AAAA,EACnC,YAAYC,YAAW,SAAS;AAC5B,QAAI,IAAI,IAAI,IAAI,IAAI;AACpB,UAAM,GAAG,KAAK,gBAAgB,CAAC,GAAG,KAAK,YAAYA,YAAW,KAAK,UAAU,SAC7E,KAAK,iBAAiB,YAAY,QAAQ,UAAU,KAAK,QAAQ,UAAU,SAAS,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,cAAc,UAAU,KAAK,QAAQ,UAAU,SAAS,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,aAAa,IACrQ,KAAK,mBAAmB,UAAU,KAAK,UAAU,KAAK,QAAQ,UAAU,SAAS,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,YAAY,WAAW,KAAK,KAAK,CAAC,GACzK,KAAK,UAAU,UAAU,KAAK,QAAQ,UAAU,SAAS,QAAQ,YAAY,WAAW,KAAK,KAAK,CAAC,GACnG,KAAK,YAAY,GAAG,YAAY,IAAI,GAAG,KAAK,OAAO,IAAI,WAAW,IAAI,GAAG,KAAK,yBAAyB,GACvG,KAAK,sBAAsB,KAAK,cAAc,qBAAqB,QAAQ,KAAK,UAAU,kBAAkB;AAAA,MACxG,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,IACZ,GAAG,cAAc,GAAG,KAAK,QAAQ,MAAM,OAAO,KAAK,iBAAiB,GAAG,KAAK,cAAc,OAAO,OACjG,KAAK,SAAS,SAAS,cAAc,QAAQ,GAAG,KAAK,QAAQ,YAAY,KAAK,MAAM,GACpF,KAAK,UAAU,KAAK,OAAO,WAAW,IAAI,GAAGA,cAAaA,WAAU,YAAY,KAAK,OAAO,GAC5F,KAAK,YAAY,KAAK,KAAK,YAAY,GAAG,KAAK,mBAAmB,GAAG,KAAK,oBAAoB,GAC9F,6BAA6B,IAAI,GAAG,KAAK,aAAa,IAAI,WAAW,IAAI,GAAG,KAAK,eAAe,IAAI,aAAa,IAAI,GACrH,KAAK,eAAe,IAAI,aAAa,IAAI,GAAG,KAAK,WAAW,sBAAsB,GAClF,KAAK,kBAAkB;AAAA,EAC3B;AAAA,EACA,sBAAsB;AAClB,SAAK,WAAW,gBAAgB;AAAA,EACpC;AAAA,EACA,cAAc;AACV,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChC,QAAI,SAAS,GAAG,UAAU;AAC1B,QAAI,cAAc,IAAI,MAAM;AACxB,YAAM,UAAU,KAAK,WAAW;AAChC,UAAI,sBAAsB,GAAG,uBAAuB;AACpD,UAAI,QAAQ,eAAe;AACvB,cAAM,gBAAgB,QAAQ,cAAc,SAAS,OAAO,iBAAiB,QAAQ,aAAa;AAClG,8BAAsB,QAAQ,cAAc,cAAc,SAAS,cAAc,eAAe,OAAO,EAAE,IAAI,SAAS,cAAc,gBAAgB,OAAO,EAAE,GAC7J,uBAAuB,QAAQ,cAAc,eAAe,SAAS,cAAc,cAAc,OAAO,EAAE,IAAI,SAAS,cAAc,iBAAiB,OAAO,EAAE;AAAA,MACnK;AACA,YAAM,UAAU,QAAQ,sBAAsB,sBAAsB,KAAK,IAAI,KAAK,gBAAgB,WAAW,QAAQ,uBAAuB,uBAAuB,KAAK;AACxK,cAAQ,MAAM,QAAQ,UAAU,GAAG,MAAM,QAAQ,OAAO,QAAQ,MAAM,SAAS,WAAW,GAAG,OAAO,QAAQ;AAC5G,YAAM,EAAC,OAAc,IAAI;AACzB,eAAS,UAAU,KAAK,UAAU,KAAK,OAAO,kBAAkB,WAAW,KAAK,SAAS,GAAG,gBAAgB,WAAW,KAAK,KAAK,GACjI,UAAU,UAAU,KAAK,UAAU,KAAK,OAAO,kBAAkB,WAAW,KAAK,SAAS,GAAG,iBAAiB,WAAW,KAAK,KAAK,IAClI,UAAU,KAAK,QAAQ,OAAO,SAAS,KAAK,eAAe,WAAW,KAAK,SAAS,GAAG,SAAS,KAAK,WAAW,MAAM,OAAO,QAAQ,OAAO,KAAK,OAAO,MAAM,QAAQ,IACvK,OAAO,MAAM,SAAS,IAAI,OAAO,QAAQ,QAAQ,OAAO,SAAS,SAAS,OAAO,MAAM,QAAQ,GAAG,MAAM,MACxG,OAAO,MAAM,SAAS,GAAG,OAAO;AAAA,IACpC,MAAO,KAAI;AACX,UAAM,QAAQ,KAAK,MAAM,SAAS,yBAAyB,KAAK,cAAc,WAAW,CAAC,GAAG,SAAS,KAAK,MAAM,UAAU,2BAA2B,KAAK,cAAc,WAAW,CAAC;AACrL,QAAI,KAAK,oBAAoB,QAAQ,KAAK,qBAAqB,KAAK,MAAM,OAAO,GACjF,KAAK,cAAc,iBAAiB;AAChC,YAAM,YAAY,UAAU,KAAK,KAAK,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG;AACpG,WAAK,SAAS,KAAK,iBAAiB,UAAU,MAAM,UAAU,KAAK,KAAK,YAAY,WAAW,KAAK,SAAS,GAAG,mBAAmB,UAAU,KAAK,KAAK,cAAc,kBAAkB,cAAc,WAAW,KAAK,KAAK,IAAI,WAC9N,KAAK,SAAS,WAAW,KAAK,oBAAoB,KAAK,IAAI,QAAQ,YAAY,KAAK,QAAQ,KAAK,oBAAoB,CAAC,GACtH,KAAK,qBAAqB,SAAS,IAAI;AAAA,IAC3C;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC5C,QAAI,KAAK,iBAAiB,UAAU,MAAM,UAAU,KAAK,KAAK,YAAY,WAAW,KAAK,SAAS,GAAG,kBAAkB;AACpH,YAAM,kBAAkB,KAAK,0BAA0B;AACvD,UAAI,KAAK,wBAAwB,IAAI,aAAU,KAAK,WAAW,eAAe,GAC9E,YAAY,UAAU,KAAK,UAAU,KAAK,KAAK,YAAY,WAAW,KAAK,SAAS,GAAG,kBAAkB,WAAW,KAAK,SAAS,GAAG,eAAe,OAAO,KAAK,mBAAmB,KAAK,iBAAiB,KAAK,sBAAsB,gBAAgB,IAAI,KAAK,cAAc,gBAAgB,kBAC1R,UAAU,KAAK,UAAU,KAAK,KAAK,YAAY,WAAW,KAAK,SAAS,GAAG,kBAAkB,WAAW,KAAK,SAAS,GAAG,mBAAmB,KAAK,iBAAiB,KAAK,IAAI,UAAU,KAAK,UAAU,KAAK,KAAK,YAAY,WAAW,KAAK,SAAS,GAAG,kBAAkB,WAAW,KAAK,SAAS,GAAG,eAAe,KAAK,cAAc,KACtU,UAAU,KAAK,UAAU,KAAK,KAAK,YAAY,WAAW,KAAK,SAAS,GAAG,kBAAkB,WAAW,KAAK,SAAS,GAAG,mBAAmB,KAAK,iBAAiB,KAAK,IAAI,UAAU,KAAK,UAAU,KAAK,KAAK,YAAY,WAAW,KAAK,SAAS,GAAG,kBAAkB,WAAW,KAAK,SAAS,GAAG,eAAe,KAAK,cAAc,IACvU,KAAK,QAAQ,MAAM,OAAO,KAAK,iBAAiB,GAAG,KAAK,cAAc,OAAO,OAC7E,KAAK,sBAAsB,cAAc,KAAK,gBAAgB,KAAK,qBAAqB,IAAI,KAAK,cAAc,gBAAgB,eAAe,GAC9I,KAAK,YAAY,IAAI,KAAK,sBAAsB,yBAAyB,EAAG,KAAI,KAAK,sBAAsB,2BAA2B,KAAK,cAAc,yBAAyB,OAAQ,UAAS,IAAI,GAAG,IAAI,KAAK,sBAAsB,wBAAwB,IAAK,MAAK,sBAAsB,aAAa,GAAG,KAAK,cAAc,yBAAyB,CAAC,CAAC;AAAA,WAAQ;AACnW,cAAM,eAAe,KAAK,uBAAuB,IAAI,KAAK,sBAAsB;AAChF,iBAAS,IAAI,GAAG,IAAI,KAAK,sBAAsB,wBAAwB,IAAK,MAAK,sBAAsB,aAAa,GAAG,YAAY;AAAA,MACvI;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,4BAA4B;AACxB,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACvS,UAAM,oBAAoB,CAAC,GAAG,yBAAyB,CAAE,aAAa,WAAW,mBAAmB,sBAAsB,YAAa;AACvI,eAAW,OAAO,KAAK,QAAS,wBAAuB,QAAQ,GAAG,KAAK,MAAM,kBAAkB,GAAG,IAAI,KAAK,QAAQ,GAAG;AACtH,eAAW,OAAO,KAAK,QAAQ,eAAe;AAC1C,UAAI,kBAAkB,GAAG,IAAI,KAAK,QAAQ,cAAc,GAAG,GAAG,cAAc,QAAQ,kBAAkB,GAAG,EAAE,kBAAkB,GAAG,EAAE,SAAS,CAAC,EAAE,sBAAsB,MACpK,KAAK,cAAc,kBAAkB,cAAc,oBAAoB,KAAK,cAAc,kBAAkB,cAAc,sBAAsB,KAAK,cAAc,kBAAkB,cAAc,qBAAqB,KAAK,cAAc,kBAAkB,cAAc,mBAAoB,UAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,QAAQ,IAAK,mBAAkB,QAAQ,CAAC,EAAE,SAAS,kBAAkB,QAAQ,CAAC,EAAE,OAAO;AACpa,iCAA2B,OAAO,KAAK,cAAc,kBAAkB,cAAc,oBAAoB,KAAK,cAAc,kBAAkB,cAAc,sBAAsB,KAAK,cAAc,kBAAkB,cAAc,qBAAqB,KAAK,cAAc,kBAAkB,cAAc,qBAAqB,OAAO,kBAAkB,GAAG;AAAA,IAClW;AACA,QAAI,UAAU,KAAK,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,MAAO,KAAI,kBAAkB,QAAQ,UAAU,KAAK,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,OAC7L,kBAAkB,MAAM,aAAa,CAAC,mBAAmB,kBAAkB,OAAO,WAAW,GAAG;AAC5F,wBAAkB,SAAS,UAAU,KAAK,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,OAAO,SAAS,UAAU,KAAK,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,OAAO,eAAe,CAAC;AACtN,YAAM,oBAAoB,kBAAkB,MAAM,eAAe;AACjE,wBAAkB,MAAM,oBAAoB,IAAI,UAAU,KAAK,kBAAkB,MAAM,gBAAgB,WAAW,KAAK,SAAS,GAAG,aAAa,kBAAkB,cAAc,kBAAkB,YAAY,YAAY,kBAAkB,YAAY,UAAU,KAAK,cAAc,iCAAiC,kBAAkB,cAAc;AAAA,QAClV,SAAS,KAAK,cAAc;AAAA,MAChC,IAAI,kBAAkB,YAAY,kBAAkB,UAAU,aAAa;AAAA,QACvE,iBAAiB,CAAE,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,wBAAwB,WAAW,KAAK,SAAS,GAAG,cAAc,WAAW,KAAK,KAAK,GAAG,GAAG,GAAG,CAAE;AAAA,QACtK,aAAa,UAAU,KAAK,KAAK,cAAc,wBAAwB,WAAW,KAAK,SAAS,GAAG;AAAA,MACvG,IAAI,kBAAkB,YAAY;AAAA,QAC9B,YAAY;AAAA,UACR,iBAAiB,CAAE,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,wBAAwB,WAAW,KAAK,SAAS,GAAG,cAAc,WAAW,KAAK,KAAK,GAAG,GAAG,GAAG,CAAE;AAAA,UACtK,aAAa,UAAU,KAAK,KAAK,cAAc,wBAAwB,WAAW,KAAK,SAAS,GAAG;AAAA,QACvG;AAAA,MACJ,GAAG,kBAAkB,kBAAkB,OAAI,kBAAkB,aAAa,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,iBAAiB;AAAA,QAC5H,YAAY;AAAA,QACZ,cAAc,CAAE,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG,iBAAiB,WAAW,KAAK,KAAK,GAAG,GAAG,GAAG,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG,iBAAiB,WAAW,KAAK,KAAK,CAAE;AAAA,QACzS,iBAAiB,CAAE,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG,oBAAoB,WAAW,KAAK,KAAK,GAAG,GAAG,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG,oBAAoB,WAAW,KAAK,KAAK,GAAG,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG,oBAAoB,WAAW,KAAK,KAAK,CAAE;AAAA,MAChc,CAAC,GAAG,kBAAkB,cAAc,OAAO,OAAO,CAAC,GAAG,UAAU,KAAK,UAAU,KAAK,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,UAAU,WAAW,KAAK,SAAS,GAAG,aAAa,KAAK,cAAc,aAAa;AAAA,QAC9N,iBAAiB;AAAA,MACrB,CAAC,GAAG,kBAAkB,eAAe,OAAO,OAAO;AAAA,QAC/C,YAAY;AAAA,QACZ,qBAAqB;AAAA,MACzB,GAAG,QAAQ,oBAAoB,SAAS,kBAAkB,YAAY,GAAG,kBAAkB,4BAA4B,kBAAkB,0BAA0B,KAAK,cAAc;AAAA,IAC1L,MAAO,mBAAkB,MAAM,cAAc,kBAAkB,MAAM,YAAY,YAAY,kBAAkB,MAAM,YAAY,UAAU,KAAK,cAAc,iCAAiC,kBAAkB,MAAM,cAAc;AAAA,MACjO,SAAS,KAAK,cAAc;AAAA,IAChC,GAAG,kBAAkB,MAAM,cAAc,OAAO,OAAO,CAAC,GAAG,eAAO,QAAQ,aAAa;AAAA,MACnF,SAAS,KAAK,cAAc;AAAA,IAChC,GAAG,UAAU,KAAK,UAAU,KAAK,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,UAAU,WAAW,KAAK,SAAS,GAAG,WAAW,GAC7I,kBAAkB,MAAM,YAAY,OAAO,OAAO,CAAC,GAAG,eAAO,QAAQ,WAAW,UAAU,KAAK,UAAU,KAAK,KAAK,QAAQ,kBAAkB,WAAW,KAAK,SAAS,GAAG,UAAU,WAAW,KAAK,SAAS,GAAG,WAAW;AAAA,MACtN,YAAY;AAAA,QACR,iBAAiB,CAAE,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,wBAAwB,WAAW,KAAK,SAAS,GAAG,cAAc,WAAW,KAAK,KAAK,GAAG,GAAG,GAAG,CAAE;AAAA,QACtK,aAAa,UAAU,KAAK,KAAK,cAAc,wBAAwB,WAAW,KAAK,SAAS,GAAG;AAAA,MACvG;AAAA,IACJ,CAAC,GAAG,kBAAkB,MAAM,kBAAkB,OAAI,kBAAkB,MAAM,aAAa,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,iBAAiB;AAAA,MACzI,cAAc,CAAE,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG,iBAAiB,WAAW,KAAK,KAAK,GAAG,GAAG,GAAG,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,oBAAoB,WAAW,KAAK,SAAS,GAAG,iBAAiB,WAAW,KAAK,KAAK,CAAE;AAAA,MACzS,iBAAiB,CAAE,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,oBAAoB,WAAW,MAAM,SAAS,IAAI,oBAAoB,WAAW,MAAM,MAAM,GAAG,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,oBAAoB,WAAW,MAAM,SAAS,IAAI,oBAAoB,WAAW,MAAM,MAAM,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,oBAAoB,WAAW,MAAM,SAAS,IAAI,oBAAoB,WAAW,MAAM,MAAM,CAAE;AAAA,IACld,CAAC,GAAG,kBAAkB,MAAM,cAAc,OAAO,OAAO,CAAC,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,QAAQ,kBAAkB,WAAW,MAAM,SAAS,IAAI,UAAU,WAAW,MAAM,SAAS,IAAI,aAAa,KAAK,cAAc,aAAa;AAAA,MAC1O,iBAAiB;AAAA,IACrB,CAAC,GAAG,kBAAkB,MAAM,eAAe,OAAO,OAAO;AAAA,MACrD,YAAY;AAAA,MACZ,qBAAqB;AAAA,IACzB,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,QAAQ,kBAAkB,WAAW,MAAM,SAAS,IAAI,UAAU,WAAW,MAAM,SAAS,IAAI,YAAY,GACpJ,kBAAkB,MAAM,4BAA4B,kBAAkB,MAAM,0BAA0B,KAAK,cAAc;AAAA,QAA+B,mBAAkB,QAAQ;AAAA,MAC9K,aAAa,OAAO,OAAO,CAAC,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,QAAQ,kBAAkB,WAAW,MAAM,SAAS,IAAI,UAAU,WAAW,MAAM,SAAS,IAAI,aAAa,KAAK,cAAc,aAAa;AAAA,QAC7M,iBAAiB;AAAA,MACrB,CAAC;AAAA,MACD,aAAa,OAAO,OAAO,CAAC,GAAG,eAAO,QAAQ,aAAa;AAAA,QACvD,SAAS,KAAK,cAAc;AAAA,MAChC,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,QAAQ,kBAAkB,WAAW,MAAM,SAAS,IAAI,UAAU,WAAW,MAAM,SAAS,IAAI,WAAW;AAAA,MACnJ,WAAW,OAAO,OAAO,CAAC,GAAG,eAAO,QAAQ,WAAW,UAAU,MAAM,UAAU,MAAM,KAAK,QAAQ,kBAAkB,WAAW,MAAM,SAAS,IAAI,UAAU,WAAW,MAAM,SAAS,IAAI,WAAW;AAAA,QACnM,YAAY;AAAA,UACR,iBAAiB,CAAE,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,wBAAwB,WAAW,MAAM,SAAS,IAAI,cAAc,WAAW,MAAM,MAAM,GAAG,GAAG,GAAG,CAAE;AAAA,UAC5K,aAAa,UAAU,MAAM,KAAK,cAAc,wBAAwB,WAAW,MAAM,SAAS,IAAI;AAAA,QAC1G;AAAA,MACJ,CAAC;AAAA,MACD,iBAAiB;AAAA,MACjB,YAAY,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,iBAAiB;AAAA,QAC9D,cAAc,CAAE,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,oBAAoB,WAAW,MAAM,SAAS,IAAI,iBAAiB,WAAW,MAAM,MAAM,GAAG,GAAG,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,oBAAoB,WAAW,MAAM,SAAS,IAAI,iBAAiB,WAAW,MAAM,MAAM,CAAE;AAAA,QACrT,iBAAiB,CAAE,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,oBAAoB,WAAW,MAAM,SAAS,IAAI,oBAAoB,WAAW,MAAM,MAAM,GAAG,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,oBAAoB,WAAW,MAAM,SAAS,IAAI,oBAAoB,WAAW,MAAM,MAAM,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,cAAc,oBAAoB,WAAW,MAAM,SAAS,IAAI,oBAAoB,WAAW,MAAM,MAAM,CAAE;AAAA,MACld,CAAC;AAAA,MACD,cAAc,OAAO,OAAO;AAAA,QACxB,YAAY;AAAA,QACZ,qBAAqB;AAAA,MACzB,GAAG,UAAU,MAAM,UAAU,MAAM,KAAK,QAAQ,kBAAkB,WAAW,MAAM,SAAS,IAAI,UAAU,WAAW,MAAM,SAAS,IAAI,YAAY;AAAA,MACpJ,yBAAyB,KAAK,cAAc;AAAA,IAChD;AACA,WAAO,kBAAkB,cAAc,KAAK,gBAAgB,kBAAkB,eAAe,KAAK,OAAO,QACzG,kBAAkB,yBAAyB,KAAK,uBAAuB,GAAG,KAAK,cAAc,kBAAkB,cAAc,sBAAsB,kBAAkB,yBAAyB,UAAQ;AAClM,UAAIC;AACJ,YAAM,EAAC,KAAU,MAAY,IAAI;AACjC,eAAS,UAAUA,MAAK,MAAM,kBAAkB,GAAG,GAAG,EAAE,aAAa,WAAWA,MAAK,SAASA,IAAG,WAAW,KAAK,KAAK,cAAc;AAAA,IACxI,GAAG,kBAAkB,mBAAmB,UAAU,KAAK,cAAc,kBAAkB,cAAc,qBAAqB,kBAAkB,yBAAyB,UAAQ;AACzK,YAAM,EAAC,KAAU,MAAY,IAAI,MAAM,SAAS,MAAM,kBAAkB,GAAG,GAAG;AAC9E,aAAO,uCAAuC,MAAM,MAAM,IAAI,KAAK,cAAc;AAAA,IACrF,GAAG,kBAAkB,mBAAmB,UAAU,KAAK,cAAc,kBAAkB,cAAc,qBAAqB,kBAAkB,yBAAyB,UAAQ;AACzK,YAAM,EAAC,KAAU,MAAY,IAAI,MAAM,SAAS,MAAM,kBAAkB,GAAG,GAAG;AAC9E,aAAO,6BAA6B,MAAM,MAAM,IAAI,KAAK,cAAc;AAAA,IAC3E,GAAG,kBAAkB,mBAAmB,UAAU,kBAAkB,mBAAmB,UAAU,MAAM,KAAK,QAAQ,cAAc,WAAW,MAAM,MAAM,IACzJ,kBAAkB,WAAW,OAAI;AAAA,EACrC;AAAA,EACA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,eAAe;AACX,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA,EACA,cAAc;AACV,UAAM,EAAC,eAA8B,IAAI,KAAK;AAC9C,QAAI,gBAAgB;AAChB,YAAM,iBAAiB,eAAe,QAAQ,YAAY,CAAE,QAAQ,WAAW,SAAS,QAAQ,OAAO,QAAQ,UAAU,QAAS;AAClI,YAAM,eAAe,WAAW,WAAW,eAAe,CAAC,EAAE,QAAQ,aAAa,eAAe,CAAC,EAAE,QAAQ,aAAa,eAAe,CAAC,EAAE,SAAS,KAAK,cAAc,uBAAuB;AAC9L,YAAM,gBAAgB,eAAe,MAAM,EAAE,KAAM,CAAC,GAAG,MAAM;AACzD,mBAAW,EAAE,QAAQ,aAAa,EAAE,QAAQ,aAAa,EAAE,SAAS,KAAK,cAAc,uBAAuB;AAC9G,cAAM,SAAS,UAAU,QAAQ,EAAE,IAAI,GAAG,SAAS,UAAU,QAAQ,EAAE,IAAI;AAC3E,eAAO,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK,SAAS;AAAA,MAC7D,CAAE,GAAG,uBAAuB,eAAe,MAAM,EAAE,KAAM,CAAC,GAAG,MAAM;AAC/D,cAAM,SAAS,UAAU,QAAQ,EAAE,IAAI,GAAG,SAAS,UAAU,QAAQ,EAAE,IAAI;AAC3E,eAAO,OAAO,SAAS,IAAI,OAAO,SAAS,KAAK,SAAS;AAAA,MAC7D,CAAE;AACF,WAAK,cAAc,uBAAuB,eAAe,KAAK,cAAc,8BAA8B;AAAA,IAC9G;AAAA,EACJ;AAAA,EACA,2BAA2B;AACvB,QAAI,KAAK,cAAc,WAAW,KAAK,cAAc,QAAS,YAAW,SAAS,KAAK,cAAc,4BAA6B,OAAM,gBAAgB,qBAAqB,IAAI,KAAK,KAAK,cAAc,OAAO,GAAG,KAAK,cAAc,SAAS,KAAK;AAAA,EACxP;AAAA,EACA,oBAAoB,OAAO;AACvB,WAAO,KAAK,wBAAwB,KAAK,sBAAsB,aAAa,QAAQ,KAAK,sBAAsB,sBAAsB,IAAI,KAAK,cAAc;AAAA,EAChK;AAAA,EACA,qBAAqB,YAAY,UAAU;AACvC,WAAO,KAAK,wBAAwB,KAAK,sBAAsB,cAAc,aAAa,KAAK,sBAAsB,wBAAwB,WAAW,KAAK,sBAAsB,sBAAsB,IAAI,KAAK,cAAc,aAAa,WAAW,aAAa;AAAA,EACzQ;AAAA,EACA,mBAAmB;AACf,WAAO,KAAK,wBAAwB,KAAK,sBAAsB,iBAAiB,IAAI,KAAK,uBAAuB,IAAI,KAAK,YAAY,KAAK,cAAc;AAAA,EAC5J;AAAA,EACA,yBAAyB;AACrB,WAAO,KAAK,cAAc,yBAAyB,OAAQ,CAAC,KAAK,MAAM,UAAU,MAAM,MAAO,CAAC;AAAA,EACnG;AAAA,EACA,sBAAsB;AAClB,QAAI,IAAI;AACR,WAAO,KAAK,cAAc,oBAAoB,UAAU,KAAK,UAAU,KAAK,KAAK,cAAc,4BAA4B,CAAC,EAAE,kBAAkB,WAAW,KAAK,SAAS,GAAG,WAAW,WAAW,KAAK,KAAK;AAAA,EAChN;AAAA,EACA,uBAAuB;AACnB,WAAO,KAAK,wBAAwB,KAAK,sBAAsB,cAAc,KAAK,sBAAsB,wBAAwB,KAAK,sBAAsB,WAAW,CAAC,IAAI,KAAK,YAAY,KAAK,cAAc;AAAA,EACnN;AAAA,EACA,8BAA8B,OAAO;AACjC,WAAO,KAAK,sBAAsB,6BAA6B,KAAK;AAAA,EACxE;AAAA,EACA,iBAAiB,eAAe,gBAAgB;AAC5C,QAAI,IAAI;AACR,WAAO,gBAAQ,cAAc,IAAI,UAAU,KAAK,UAAU,KAAK,KAAK,QAAQ,aAAa,MAAM,WAAW,KAAK,SAAS,GAAG,aAAa,WAAW,KAAK,SAAS,GAAG,cAAc,IAAI,KAAK,wBAAwB,KAAK,sBAAsB,kBAAkB,GAAG,gBAAgB,KAAK,sBAAsB,sBAAsB,IAAI,KAAK,QAAQ,aAAa;AAAA,EACtW;AAAA,EACA,gBAAgB,eAAe;AAC3B,SAAK,WAAW,QAAQ,kBAAkB,aAAa,GAAG,KAAK,WAAW,uBAAuB,eAAe,QAAQ,KAAK,WAAW,QAAQ,sBAAsB,aAAa,CAAC,GACpL,KAAK,WAAW,gBAAgB;AAAA,EACpC;AAAA,EACA,yBAAyB,QAAQ,OAAO;AACpC,UAAM,SAAS,KAAK,sBAAsB,qBAAqB,GAAG,QAAQ,KAAK,sBAAsB,sBAAsB;AAC3H,SAAK,sBAAsB,cAAc,CAAE,MAAO,GAAG,CAAE,MAAO,CAAC;AAAA,EACnE;AAAA,EACA,2BAA2B,eAAe,gBAAgB;AACtD,UAAM,aAAa,KAAK,iBAAiB,eAAe,cAAc,GAAG,iBAAiB,KAAK,cAAc,gBAAgB,eAAe,KAAK,cAAc,cAAc,gBAAgB,KAAK,cAAc,eAAe,uBAAuB,qBAAqB,QAAQ,aAAa,SAAS,WAAW,cAAc,CAAC,EAAE,QAAQ,GAAG,qBAAqB,qBAAqB,QAAQ,aAAa,SAAS,WAAW,YAAY,CAAC,EAAE,QAAQ;AAC1b,QAAI,qBAAqB,KAAK,cAAc,gBAAgB,uBAAuB,KAAK,cAAc,gBAAgB,EAAE,QAAQ,aAAa,SAAS,WAAW,cAAc,MAAM,EAAE,QAAQ,aAAa,SAAS,WAAW,YAAY,GAAI,QAAO;AAAA,MACnP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,SAAS;AAAA,MACT;AAAA,IACJ;AACA,UAAM,WAAW,gBAAgB,WAAW,aAAa,CAAC;AAC1D,QAAI,WAAW;AACf,SAAK,cAAc,wBAAwB,YAAY,qBAAqB,KAAK,IAAI,KAAK,IAAI,KAAK,cAAc,cAAc,oBAAoB,GAAG,KAAK,cAAc,YAAY,CAAC,GACtL,UAAU,4BAA4B,KAAK,IAAI,KAAK,IAAI,KAAK,cAAc,cAAc,kBAAkB,GAAG,KAAK,cAAc,YAAY,GAAG,IAAE,MAAM,YAAY,qBAAqB,KAAK,IAAI,KAAK,IAAI,KAAK,cAAc,cAAc,oBAAoB,GAAG,KAAK,cAAc,YAAY,GAAG,IAAE,GACvS,UAAU,qBAAqB,KAAK,IAAI,KAAK,IAAI,KAAK,cAAc,cAAc,kBAAkB,GAAG,KAAK,cAAc,YAAY,GAAG,IAAE;AAC3I,WAAO;AAAA,MACH;AAAA,MACA,WAAW,QAAQ,QAAQ,IAAI,UAAU,QAAQ,IAAI,KAAK;AAAA,MAC1D;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,6BAA6B,WAAW,OAAO,gBAAgB;AAC3D,QAAI;AACJ,UAAM,aAAa,KAAK,iBAAiB,OAAO,cAAc,GAAG,iBAAiB,KAAK,cAAc,gBAAgB,aAAa,UAAU,KAAK,KAAK,cAAc,eAAe,WAAW,KAAK,KAAK,gBAAgB,WAAW,cAAc,CAAC,GAAG,eAAe,WAAW,WAAW,UAAU;AACpS,eAAW,cAAc,IAAI,cAAc,gBAAQ,cAAc,KAAK,KAAK,yBAAyB,YAAY,KAAK;AAAA,EACzH;AAAA,EACA,2BAA2B,SAAS,OAAO,gBAAgB;AACvD,QAAI;AACJ,UAAM,aAAa,KAAK,iBAAiB,OAAO,cAAc,GAAG,eAAe,KAAK,cAAc,cAAc,aAAa,UAAU,KAAK,KAAK,cAAc,eAAe,WAAW,KAAK,KAAK,gBAAgB,WAAW,YAAY,CAAC,GAAG,aAAa,WAAW,SAAS,UAAU;AAC1R,eAAW,YAAY,IAAI,YAAY,gBAAQ,cAAc,KAAK,KAAK,yBAAyB,YAAY,KAAK;AAAA,EACrH;AAAA,EACA,gCAAgC,WAAW,SAAS,OAAO,gBAAgB;AACvE,QAAI;AACJ,UAAM,aAAa,KAAK,iBAAiB,OAAO,cAAc,GAAG,iBAAiB,KAAK,cAAc,gBAAgB,eAAe,KAAK,cAAc,cAAc,aAAa,UAAU,KAAK,KAAK,cAAc,eAAe,WAAW,KAAK,KAAK,gBAAgB,WAAW,cAAc,CAAC,GAAG,eAAe,WAAW,WAAW,UAAU;AACpV,eAAW,cAAc,IAAI;AAC7B,UAAM,aAAa,WAAW,SAAS,UAAU;AACjD,eAAW,YAAY,IAAI,YAAY,gBAAQ,cAAc,KAAK,KAAK,yBAAyB,YAAY,KAAK;AAAA,EACrH;AAAA,EACA,qBAAqB,cAAc,uBAAuB,cAAc,uBAAuB;AAC3F,SAAK,KAAK,YAAY,cAAc,uBAAuB,cAAc,qBAAqB;AAAA,EAClG;AAAA,EACA,iBAAiB,QAAQ,OAAO;AAC5B,SAAK,yBAAyB,QAAQ,KAAK,GAAG,KAAK,gBAAgB,KAAK;AAAA,EAC5E;AAAA,EACA,cAAc,YAAY;AACtB,QAAI;AACJ,cAAU,KAAK,KAAK,0BAA0B,WAAW,MAAM,GAAG,cAAc,UAAU,GAC1F,KAAK,cAAc,aAAa,YAAY,KAAK,WAAW,cAAc,UAAU;AAAA,EACxF;AAAA,EACA,UAAU;AACN,QAAI;AACJ,SAAK,YAAY,GAAG,UAAU,KAAK,KAAK,0BAA0B,WAAW,MAAM,GAAG,cAAc,KAAK,gBAAgB,KAAK,qBAAqB,IAAI,KAAK,cAAc,gBAAgB,eAAe,GACzM,KAAK,oBAAoB,GAAG,KAAK,WAAW,OAAO,GAAG,6BAA6B,IAAI;AAAA,EAC3F;AAAA,EACA,sBAAsB;AAClB,SAAK,YAAY,KAAK,wBAAwB,KAAK,sBAAsB,WAAW,KAAK,sBAAsB,yBAAyB,KAAK,QAAQ,QACrJ,KAAK,eAAe,KAAK,uBAAuB,GAAG,KAAK,aAAa,KAAK,IAAI,KAAK,iBAAiB,GAAG,KAAK,kBAAkB,GAC9H,KAAK,aAAa,KAAK,aAAa,KAAK;AAAA,EAC7C;AAAA,EACA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,UAAU;AACN,QAAI,IAAI;AACR,cAAU,KAAK,MAAM,YAAY,WAAW,MAAM,GAAG,KAAK,IAAI,GAAG,KAAK,aAAa,QAAQ,GAC3F,UAAU,KAAK,KAAK,0BAA0B,WAAW,MAAM,GAAG,QAAQ;AAC1E,UAAM,EAAC,cAA4B,IAAI,KAAK;AAC5C,sBAAkB,cAAc,YAAY,KAAK,OAAO,GAAG,KAAK,2BAA2B,cAAc,YAAY,KAAK,uBAAuB,GACjJ,KAAK,uBAAuB,cAAc,YAAY,KAAK,mBAAmB,IAC9E,KAAK,aAAa;AAAA,EACtB;AAAA,EACA,WAAW,SAAS;AAChB,SAAK,UAAU,SAAS,KAAK,KAAK,WAAW,OAAO,GAAG,KAAK,sBAAsB,WAAW,OAAO,GACpG,KAAK,oBAAoB,GAAG,KAAK,yBAAyB,GAAG,KAAK,sBAAsB,KAAK,cAAc,qBAAqB,QAChI,KAAK,YAAY,GAAG,KAAK,WAAW,WAAW,GAAG,KAAK,wBAAwB,MAAM,SAAS,KAAK,aAAa;AAChH,UAAM,OAAO,KAAK,aAAa,OAAO,kBAAkB,MAAM,KAAK,aAAa,OAAO;AACvF,SAAK,WAAW,KAAK,CAAC,IAAI,GAAG,KAAK,WAAW,KAAK,CAAC,GAAG;AAAA,EAC1D;AAAA,EACA,aAAa,QAAQ;AACjB,QAAI,KAAK,QAAQ,eAAe,SAAS,QAAQ,KAAK,YAAY,GAAG,8BAA8B,IAAI,GACvG,KAAK,yBAAyB,GAAG,KAAK,sBAAsB,KAAK,cAAc,qBAAqB,QACpG,KAAK,WAAW,WAAW,GAAG,6BAA6B,IAAI,GAAG,KAAK,sBAAuB,KAAI,KAAK,sBAAsB,2BAA2B,KAAK,cAAc,yBAAyB,OAAQ,UAAS,IAAI,GAAG,IAAI,KAAK,sBAAsB,wBAAwB,IAAK,MAAK,sBAAsB,aAAa,GAAG,KAAK,cAAc,yBAAyB,CAAC,CAAC;AAAA,SAAQ;AACrX,YAAM,eAAe,KAAK,uBAAuB,IAAI,KAAK,sBAAsB;AAChF,eAAS,IAAI,GAAG,IAAI,KAAK,sBAAsB,wBAAwB,IAAK,MAAK,sBAAsB,aAAa,GAAG,YAAY;AAAA,IACvI;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,QAAI,KAAK,cAAc,wBAAwB,KAAK,cAAc,SAAS;AACvE,YAAM,UAAU,KAAK,cAAc,SAAS,EAAC,MAAY,KAAU,IAAI,KAAK,cAAc,4BAA4B,CAAC,GAAG,OAAO,wBAAwB,KAAK,cAAc,sBAAsB,SAAS,MAAM,IAAI,IAAI,KAAK,cAAc,mBAAmB,KAAK,oBAAoB;AACxR,WAAK,aAAa,cAAc,IAAI;AAAA,IACxC;AAAA,EACJ;AAAA,EACA,QAAQ,MAAM;AACV,SAAK,cAAc,gBAAgB,KAAK,IAAI,GAAG,KAAK,WAAW,eAAe,aAAa,KAAK,cAAc,gBAAgB,SAAS,CAAC,GACxI,KAAK,WAAW,gBAAgB;AAAA,EACpC;AAAA,EACA,WAAW,MAAM;AACb,UAAM,QAAQ,KAAK,cAAc,gBAAgB,UAAW,UAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,sBAAsB,KAAK,qBAAqB,KAAK,oBAAoB,KAAK,eAAgB;AAClM,QAAI,OAAO,OAAO;AACd,YAAMC,QAAO,KAAK,cAAc,gBAAgB,KAAK;AACrD,WAAK,cAAc,gBAAgB,OAAO,OAAO,CAAC,GAAG,KAAK,WAAW,eAAe,WAAWA,KAAI,GACnG,KAAK,WAAW,gBAAgB;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK,aAAa;AAAA,EAC7B;AAAA,EACA,IAAI,UAAU,OAAO;AACjB,SAAK,aAAa,aAAa,KAAK;AAAA,EACxC;AAAA,EACA,IAAI,aAAa;AACb,WAAO,KAAK,aAAa;AAAA,EAC7B;AAAA,EACA,IAAI,WAAW,OAAO;AAClB,SAAK,aAAa,cAAc,KAAK;AAAA,EACzC;AAAA,EACA,uBAAuB,OAAO;AAC1B,UAAM,cAAc,KAAK,WAAW,QAAQ,sBAAsB,KAAK;AACvE,WAAO;AAAA,MACH,MAAM,YAAY,UAAU,IAAI,KAAK,sBAAsB,oBAAoB,KAAK,sBAAsB,SAAS,KAAK,SAAS,KAAK;AAAA,MACtI,KAAK,YAAY,UAAU,IAAI,KAAK,SAAS,KAAK,eAAe,KAAK;AAAA,MACtE,OAAO,YAAY,UAAU;AAAA,MAC7B,QAAQ,YAAY,UAAU;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,wBAAwB;AACpB,QAAI;AACJ,UAAM,WAAW,KAAK,cAAc,4BAA4B,CAAC,GAAG,eAAe,SAAS,MAAM,eAAe,UAAU,KAAK,SAAS,SAAS,WAAW,KAAK,KAAK;AACvK,WAAO,UAAU,eAAe,eAAe,WAAW,eAAe,IAAI,eAAe,YAAY,eAAe,KAAK,eAAe,cAAc,eAAe,KAAK,eAAe,WAAW,eAAe,MAAM,eAAe,WAAW,eAAe,IAAI,KAAK,eAAe,aAAa,eAAe,IAAI,KAAK,KAAK,eAAe,aAAa,eAAe,IAAI,KAAK,KAAK,KAAK,eAAe;AAAA,EACxZ;AAAA,EACA,gBAAgB,WAAW;AACvB,WAAO,KAAK,cAAc;AAAA,EAC9B;AAAA,EACA,iBAAiB,gBAAgB,cAAc;AAC3C,YAAQ,eAAe,iBAAiB,KAAK,KAAK,cAAc;AAAA,EACpE;AAAA,EACA,oBAAoB,OAAO;AACvB,UAAM,WAAW,KAAK,cAAc,4BAA4B,CAAC;AACjE,WAAO;AAAA,MACH,WAAW,SAAS,cAAc,KAAK,EAAE;AAAA,MACzC,SAAS,SAAS,cAAc,KAAK,EAAE;AAAA,IAC3C;AAAA,EACJ;AAAA,EACA,gBAAgB,MAAM;AAClB,WAAO,gBAAgB,IAAI;AAAA,EAC/B;AACJ;;;AC9YA;;;;;;;;;ACAA;;;;;;;;;;;;;;;ACAA;;;;;;;A", "names": ["ts_types_exports", "InteractionState", "DependencyType", "TasksShowMode", "_c", "_d", "_a", "_b", "wait", "time", "_a", "dx", "dy", "idCount", "container", "_a", "link"]}
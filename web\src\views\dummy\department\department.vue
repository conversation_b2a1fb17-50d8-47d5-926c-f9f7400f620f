<!--
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-10 19:30:05
 * @LastEditors: zhai
 * @LastEditTime: 2025-01-18 11:23:21
-->
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ValueBuilderContext, useFs } from '@fast-crud/fast-crud';
import type {
  AddReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  DelReq,
  EditReq,
  UserPageQuery,
  UserPageRes,
  ValueResolveContext
} from '@fast-crud/fast-crud';
import { fast_department_api as api } from '@/service/api/dummy';

const selectedRowKeys = ref([]);

const emit = defineEmits<{
  (e: 'select-type', value: number | null): void;
}>();

function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery): Promise<UserPageRes> => {
    return api.GetList(query);
  };
  const editRequest = async (ctx: EditReq) => {
    const { form, row } = ctx;
    form.id = row.id;
    return api.UpdateObj(form);
  };
  const delRequest = async (ctx: DelReq) => {
    const { row } = ctx;
    return api.DelObj(row.id);
  };

  const addRequest = async (req: AddReq) => {
    const { form } = req;
    return api.AddObj(form);
  };

  return {
    crudOptions: {
      container: {
        // is: 'fs-layout-card'
      },
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest
      },
      settings: {
        viewFormUseCellComponent: true,

        plugins: {
          // 这里使用行选择插件，生成行选择crudOptions配置，最终会与crudOptions合并
          rowSelection: {
            enabled: true,
            order: -2,
            before: true,
            // handle: (pluginProps,useCrudProps)=>CrudOptions,
            props: {
              multiple: false,
              crossPage: true,
              selectedRowKeys,
              onSelectedChanged(selected) {
                if (selected?.length > 0) {
                  emit('select-type', selected[0]);
                } else {
                  emit('select-type', null);
                }

                console.log('已选择变化：', selected);
              }
            }
          }
        }
      },
      table: {
        striped: true
      },
      rowHandle: {
        width: 150
      },
      search: {
        show: false
      },
      form: {
        wrapper: {
          draggable: false
        }
      },
      toolbar: {
        buttons: {
          search: {},
          compact: {
            show: false
          }
        }
      },
      columns: {
        // sel: {
        //   type: 'selection',
        //   multiple: false,
        // },

        _index: {
          title: '序号',
          form: { show: false },
          column: {
            align: 'center',
            width: '55px',
            columnSetDisabled: true, // 禁止在列设置中选择
            formatter: context => {
              // 计算序号,你可以自定义计算规则，此处为翻页累加
              const index = context.index ?? 1;
              const pagination = crudBinding.value.pagination;
              return ((pagination.currentPage ?? 1) - 1) * pagination.pageSize + index + 1;
            }
          }
        },

        // id: {
        //   title: 'ID',
        //   key: 'id',
        //   type: 'number',
        //   column: {
        //     width: 50
        //   },
        //   form: {
        //     show: false
        //   }
        // },

        name: {
          title: '部门名称',
          type: 'text',
          search: { show: true },
          column: {
            sorter: 'custom'
          }
        },

        factor: {
          title: '效能系数',
          type: 'number'
        }
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

// 页面打开后获取列表数据
onMounted(() => {
  crudExpose.doRefresh();
});
</script>

<template>
  <div class="h-full">
    <FsCrud ref="crudRef" v-bind="crudBinding"></FsCrud>
  </div>
</template>

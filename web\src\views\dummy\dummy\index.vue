<!--
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-10 19:30:05
 * @LastEditors: zhai
 * @LastEditTime: 2025-01-19 19:33:23
-->

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ValueBuilderContext, dict, useFs } from '@fast-crud/fast-crud';
import type {
  AddReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  DelReq,
  EditReq,
  UserPageQuery,
  UserPageRes,
  ValueResolveContext
} from '@fast-crud/fast-crud';
import { useDialog, useMessage } from 'naive-ui';
import dayjs from 'dayjs';
import { fast_dummy_api as api } from '@/service/api/dummy';

const selectedRowKeys = ref([]);

function createCrudOptions({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery): Promise<UserPageRes> => {
    return api.GetList(query);
  };
  const editRequest = async (ctx: EditReq) => {
    const { form, row } = ctx;
    form.id = row.id;
    return api.UpdateObj(form);
  };
  const delRequest = async (ctx: DelReq) => {
    const { row } = ctx;
    return api.DelObj(row.id);
  };
  const addRequest = async (req: AddReq) => {
    const { form } = req;
    return api.AddObj(form);
  };

  return {
    crudOptions: {
      container: {
        // is: 'fs-layout-card'
      },
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest
      },
      settings: {
        viewFormUseCellComponent: true,
        plugins: {
          // 这里使用行选择插件，生成行选择crudOptions配置，最终会与crudOptions合并
          rowSelection: {
            enabled: true,
            order: -2,
            before: true,
            // handle: (pluginProps, useCrudProps)=>CrudOptions,
            props: {
              multiple: true,
              crossPage: true,
              selectedRowKeys,
              onSelectedChanged(selected) {
                console.log('已选择变化：', selected);
              }
            }
          }
        }
      },
      table: {
        striped: true
      },
      rowHandle: {
        width: 150
      },
      search: {
        show: false
      },
      form: {
        wrapper: {
          draggable: false
        }
      },
      toolbar: {
        buttons: {
          search: {},
          compact: {
            show: false
          }
        }
      },
      columns: {
        // sel: {
        //   type: 'selection',
        //   multiple: false,
        // },
        // id: {
        //   title: 'ID',
        //   key: 'id',
        //   type: 'number',
        //   column: {
        //     width: 50
        //   },
        //   form: {
        //     show: false
        //   }
        // },

        _index: {
          title: '序号',
          form: { show: false },
          column: {
            align: 'center',
            width: '55px',
            columnSetDisabled: true, // 禁止在列设置中选择
            formatter: context => {
              // 计算序号,你可以自定义计算规则，此处为翻页累加
              const index = context.index ?? 1;
              const pagination = crudBinding.value.pagination;
              return ((pagination.currentPage ?? 1) - 1) * pagination.pageSize + index + 1;
            }
          }
        },
        name: {
          title: '姓名',
          type: 'text',
          search: { show: true },
          column: {
            sorter: 'custom'
          },
          form: {
            rule: [{ required: true, message: '请输入姓名' }]
          }
        },
        age: {
          title: '年龄',
          type: 'number',
          form: {
            rule: [{ required: true, message: '请输入年龄' }]
          }
        },
        salary: {
          title: '薪资',
          type: 'number',
          form: {
            rule: [{ required: true, message: '请输入薪资' }]
          }
        },
        birthdate: {
          title: '生日',
          type: 'date',
          form: {
            rule: [{ required: true, message: '请输入生日' }]
          },
          valueBuilder(context) {
            const { value, row, key } = context;
            if (value) {
              row[key] = dayjs(value).valueOf();
            }
          },
          valueResolve(context) {
            const { value, form, key } = context;
            if (value) {
              form[key] = dayjs(value).format('YYYY-MM-DD');
            }
          }
        },
        created_at: {
          title: '创建时间',
          type: 'datetime',
          form: {
            rule: [{ required: true, message: '请输入创建时间' }]
          },
          valueBuilder(context) {
            const { value, row, key } = context;
            if (value) {
              row[key] = dayjs(value).valueOf();
            }
          },
          valueResolve(context) {
            const { value, form, key } = context;
            if (value) {
              form[key] = dayjs(value).format('YYYY-MM-DD');
            }
          }
        },
        is_active: {
          title: '状态',
          type: 'dict-switch',
          form: {
            value: true
          }
        },
        notes: {
          title: '备注',
          type: 'editor-wang5',
          form: {
            col: {
              // 横跨两列
              // span: 24
            },
            component: {
              style: {
                height: '200px'
              }
            }
          }
        },
        json_data: {
          title: 'json数据',
          type: 'json',
          form: {
            col: {
              // 横跨两列
              // span: 24
            },
            component: {
              style: {
                height: '300px'
              }
            }
          }
        },
        file: {
          title: '附件',
          type: 'file-uploader',
          form: {
            component: {
              uploader: {
                type: 'form'
              }
            }
          }
        }
      }
    }
  };
}

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

const message = useMessage();
const dialog = useDialog();

const handleBatchDelete = async () => {
  if (selectedRowKeys.value?.length > 0) {
    await dialog.info({
      title: '确认',
      content: `确定要批量删除这${selectedRowKeys.value.length}条记录吗`,
      positiveText: '确定',
      negativeText: '取消',
      async onPositiveClick() {
        await api.BatchDelete(selectedRowKeys.value);
        message.info('删除成功');
        selectedRowKeys.value = [];
        await crudExpose.doRefresh();
      }
    });
  } else {
    message.error('请先勾选记录');
  }
};

// 页面打开后获取列表数据
onMounted(() => {
  crudExpose.doRefresh();
  // eslint-disable-next-line prettier/prettier
});
</script>

<template>
  <div class="h-full">
    <FsCrud ref="crudRef" v-bind="crudBinding">
      <!-- 批量删除 -->
      <template #pagination-left>
        <FsButton icon="ion:trash-outline" @click="handleBatchDelete" />
      </template>
    </FsCrud>
  </div>
</template>

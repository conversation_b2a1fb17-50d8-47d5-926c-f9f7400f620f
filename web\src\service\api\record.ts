/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2024-06-21 19:42:39
 * @LastEditors: zhai
 * @LastEditTime: 2024-06-21 20:01:57
 */
import { request } from '@/service/request';
import { CrudApi } from '../crud-api';
import { FastCrudApi } from '../fast-crud-api';

/// ///////////////////////////////////////////////////////////////////

export class FastRecordsApi extends FastCrudApi<FastRecordsApi> {
  constructor() {
    super('records');
  }


  async export_csv(id?: string) {
    return request({
      url: `/${this.prefix}/export_csv/${id}`,
      method: 'GET',
      responseType: "blob"
    });
  }


}

export const fast_records_api = FastRecordsApi.instance();

{"version": 3, "sources": ["../../.pnpm/@baklavajs+core@1.10.2/node_modules/@baklavajs/core/dist/index.cjs"], "sourcesContent": ["/*! For license information please see index.cjs.LICENSE.txt */\n(()=>{\"use strict\";var e={163:(e,t,n)=>{n.r(t),n.d(t,{__assign:()=>i,__asyncDelegator:()=>g,__asyncGenerator:()=>_,__asyncValues:()=>O,__await:()=>b,__awaiter:()=>f,__classPrivateFieldGet:()=>P,__classPrivateFieldSet:()=>j,__createBinding:()=>l,__decorate:()=>s,__exportStar:()=>d,__extends:()=>r,__generator:()=>p,__importDefault:()=>E,__importStar:()=>x,__makeTemplateObject:()=>I,__metadata:()=>u,__param:()=>c,__read:()=>v,__rest:()=>a,__spread:()=>y,__spreadArray:()=>w,__spreadArrays:()=>m,__values:()=>h});var o=function(e,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},o(e,t)};function r(e,t){if(\"function\"!=typeof t&&null!==t)throw new TypeError(\"Class extends value \"+String(t)+\" is not a constructor or null\");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var i=function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)};function a(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&\"function\"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}function s(e,t,n,o){var r,i=arguments.length,a=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if(\"object\"==typeof Reflect&&\"function\"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(i<3?r(a):i>3?r(t,n,a):r(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a}function c(e,t){return function(n,o){t(n,o,e)}}function u(e,t){if(\"object\"==typeof Reflect&&\"function\"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function f(e,t,n,o){return new(n||(n=Promise))((function(r,i){function a(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((o=o.apply(e,t||[])).next())}))}function p(e,t){var n,o,r,i,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},\"function\"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(n)throw new TypeError(\"Generator is already executing.\");for(;a;)try{if(n=1,o&&(r=2&i[0]?o.return:i[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,i[1])).done)return r;switch(o=0,r&&(i=[2&i[0],r.value]),i[0]){case 0:case 1:r=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,o=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!r||i[1]>r[0]&&i[1]<r[3])){a.label=i[1];break}if(6===i[0]&&a.label<r[1]){a.label=r[1],r=i;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(i);break}r[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],o=0}finally{n=r=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}var l=Object.create?function(e,t,n,o){void 0===o&&(o=n),Object.defineProperty(e,o,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]};function d(e,t){for(var n in e)\"default\"===n||Object.prototype.hasOwnProperty.call(t,n)||l(t,e,n)}function h(e){var t=\"function\"==typeof Symbol&&Symbol.iterator,n=t&&e[t],o=0;if(n)return n.call(e);if(e&&\"number\"==typeof e.length)return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?\"Object is not iterable.\":\"Symbol.iterator is not defined.\")}function v(e,t){var n=\"function\"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var o,r,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(o=i.next()).done;)a.push(o.value)}catch(e){r={error:e}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return a}function y(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(v(arguments[t]));return e}function m(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),r=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,s=i.length;a<s;a++,r++)o[r]=i[a];return o}function w(e,t,n){if(n||2===arguments.length)for(var o,r=0,i=t.length;r<i;r++)!o&&r in t||(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}function b(e){return this instanceof b?(this.v=e,this):new b(e)}function _(e,t,n){if(!Symbol.asyncIterator)throw new TypeError(\"Symbol.asyncIterator is not defined.\");var o,r=n.apply(e,t||[]),i=[];return o={},a(\"next\"),a(\"throw\"),a(\"return\"),o[Symbol.asyncIterator]=function(){return this},o;function a(e){r[e]&&(o[e]=function(t){return new Promise((function(n,o){i.push([e,t,n,o])>1||s(e,t)}))})}function s(e,t){try{(n=r[e](t)).value instanceof b?Promise.resolve(n.value.v).then(c,u):f(i[0][2],n)}catch(e){f(i[0][3],e)}var n}function c(e){s(\"next\",e)}function u(e){s(\"throw\",e)}function f(e,t){e(t),i.shift(),i.length&&s(i[0][0],i[0][1])}}function g(e){var t,n;return t={},o(\"next\"),o(\"throw\",(function(e){throw e})),o(\"return\"),t[Symbol.iterator]=function(){return this},t;function o(o,r){t[o]=e[o]?function(t){return(n=!n)?{value:b(e[o](t)),done:\"return\"===o}:r?r(t):t}:r}}function O(e){if(!Symbol.asyncIterator)throw new TypeError(\"Symbol.asyncIterator is not defined.\");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=h(e),t={},o(\"next\"),o(\"throw\"),o(\"return\"),t[Symbol.asyncIterator]=function(){return this},t);function o(n){t[n]=e[n]&&function(t){return new Promise((function(o,r){!function(e,t,n,o){Promise.resolve(o).then((function(t){e({value:t,done:n})}),t)}(o,r,(t=e[n](t)).done,t.value)}))}}}function I(e,t){return Object.defineProperty?Object.defineProperty(e,\"raw\",{value:t}):e.raw=t,e}var C=Object.create?function(e,t){Object.defineProperty(e,\"default\",{enumerable:!0,value:t})}:function(e,t){e.default=t};function x(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)\"default\"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&l(t,e,n);return C(t,e),t}function E(e){return e&&e.__esModule?e:{default:e}}function P(e,t,n,o){if(\"a\"===n&&!o)throw new TypeError(\"Private accessor was defined without a getter\");if(\"function\"==typeof t?e!==t||!o:!t.has(e))throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");return\"m\"===n?o:\"a\"===n?o.call(e):o?o.value:t.get(e)}function j(e,t,n,o,r){if(\"m\"===o)throw new TypeError(\"Private method is not writable\");if(\"a\"===o&&!r)throw new TypeError(\"Private accessor was defined without a setter\");if(\"function\"==typeof t?e!==t||!r:!t.has(e))throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");return\"a\"===o?r.call(e,n):r?r.value=n:t.set(e,n),n}},749:(e,t,n)=>{t.p$=t.wD=t.EZ=void 0;var o=n(163),r=function(){function e(){this.listeners=new Map}return e.prototype.addListener=function(e,t){this.listeners.set(e,t)},e.prototype.removeListener=function(e){this.listeners.has(e)&&this.listeners.delete(e)},e.prototype.emit=function(e){this.listeners.forEach((function(t){return t(e)}))},e}();t.EZ=r;var i=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o.__extends(t,e),t.prototype.emit=function(e){var t,n;try{for(var r=o.__values(Array.from(this.listeners.values())),i=r.next();!i.done;i=r.next())if(!1===(0,i.value)(e))return!0}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}return!1},t}(r);t.wD=i;var a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o.__extends(t,e),t.prototype.execute=function(e){var t,n,r=e;try{for(var i=o.__values(this.taps),a=i.next();!a.done;a=i.next())r=(0,a.value)(r)}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return r},t}(function(){function e(){this.tapMap=new Map,this.taps=[]}return e.prototype.tap=function(e,t){this.tapMap.has(e)&&this.untap(e),this.tapMap.set(e,t),this.taps.push(t)},e.prototype.untap=function(e){if(this.tapMap.has(e)){var t=this.tapMap.get(e);this.tapMap.delete(e);var n=this.taps.indexOf(t);n>=0&&this.taps.splice(n,1)}},e}());t.p$=a}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})};var o={};(()=>{n.r(o),n.d(o,{Connection:()=>i,DummyConnection:()=>a,Editor:()=>c,Node:()=>p,NodeBuilder:()=>d,NodeInterface:()=>u,NodeOption:()=>f});var e=0;function t(){return Date.now().toString()+(e++).toString()}var r=n(749),i=function(){function e(e,n){if(this.isInDanger=!1,this.destructed=!1,this.events={destruct:new r.EZ},!e||!n)throw new Error(\"Cannot initialize connection with null/undefined for 'from' or 'to' values\");this.id=t(),this.from=e,this.to=n,this.from.connectionCount++,this.to.connectionCount++}return e.prototype.destruct=function(){this.events.destruct.emit(),this.from.connectionCount--,this.to.connectionCount--,this.destructed=!0},e}(),a=function(e,n){if(!e||!n)throw new Error(\"Cannot initialize connection with null/undefined for 'from' or 'to' values\");this.id=t(),this.from=e,this.to=n},s=n(163),c=function(){function e(){this._plugins=new Set,this._nodes=[],this._connections=[],this._nodeTypes=new Map,this._nodeCategories=new Map([[\"default\",[]]]),this._loading=!1,this.events={beforeRegisterNodeType:new r.wD,registerNodeType:new r.EZ,beforeAddNode:new r.wD,addNode:new r.EZ,beforeRemoveNode:new r.wD,removeNode:new r.EZ,beforeAddConnection:new r.wD,addConnection:new r.EZ,checkConnection:new r.wD,beforeRemoveConnection:new r.wD,removeConnection:new r.EZ,beforeUsePlugin:new r.wD,usePlugin:new r.EZ},this.hooks={save:new r.p$,load:new r.p$}}return Object.defineProperty(e.prototype,\"nodes\",{get:function(){return this._nodes},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"connections\",{get:function(){return this._connections},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"nodeTypes\",{get:function(){return this._nodeTypes},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"nodeCategories\",{get:function(){return this._nodeCategories},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"plugins\",{get:function(){return this._plugins},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"loading\",{get:function(){return this._loading},enumerable:!1,configurable:!0}),e.prototype.registerNodeType=function(e,t,n){void 0===n&&(n=\"default\"),this.events.beforeRegisterNodeType.emit({typeName:e,type:t,category:n})||(this._nodeTypes.set(e,t),this.nodeCategories.has(n)||this._nodeCategories.set(n,[]),this.nodeCategories.get(n).push(e),this.events.registerNodeType.emit({typeName:e,type:t,category:n}))},e.prototype.addNode=function(e){if(!this.events.beforeAddNode.emit(e))return e.registerEditor(this),this._nodes.push(e),this.events.addNode.emit(e),e},e.prototype.removeNode=function(e){var t=this;if(this.nodes.includes(e)){if(this.events.beforeRemoveNode.emit(e))return;this.connections.filter((function(t){return t.from.parent===e||t.to.parent===e})).forEach((function(e){return t.removeConnection(e)})),this._nodes.splice(this.nodes.indexOf(e),1),this.events.removeNode.emit(e)}},e.prototype.addConnection=function(e,t){var n=this.checkConnection(e,t);if(n&&!this.events.beforeAddConnection.emit({from:e,to:t})){var o=new i(n.from,n.to);return this._connections.push(o),this.events.addConnection.emit(o),o}},e.prototype.removeConnection=function(e){if(this.connections.includes(e)){if(this.events.beforeRemoveConnection.emit(e))return;e.destruct(),this._connections.splice(this.connections.indexOf(e),1),this.events.removeConnection.emit(e)}},e.prototype.checkConnection=function(e,t){if(!e||!t)return!1;if(e.parent===t.parent)return!1;if(e.isInput&&!t.isInput){var n=e;e=t,t=n}return!(e.isInput||!t.isInput)&&!this.connections.some((function(n){return n.from===e&&n.to===t}))&&!this.events.checkConnection.emit({from:e,to:t})&&new a(e,t)},e.prototype.load=function(e){var t,n,o,r;try{this._loading=!0;for(var i=[],a=this.connections.length-1;a>=0;a--)this.removeConnection(this.connections[a]);for(a=this.nodes.length-1;a>=0;a--)this.removeNode(this.nodes[a]);try{for(var c=(0,s.__values)(e.nodes),u=c.next();!u.done;u=c.next()){var f=u.value,p=this.nodeTypes.get(f.type);if(p){var l=new p;this.addNode(l),l.load(f)}else i.push(\"Node type \".concat(f.type,\" is not registered\"))}}catch(e){t={error:e}}finally{try{u&&!u.done&&(n=c.return)&&n.call(c)}finally{if(t)throw t.error}}try{for(var d=(0,s.__values)(e.connections),h=d.next();!h.done;h=d.next()){var v=h.value,y=this.findNodeInterface(v.from),m=this.findNodeInterface(v.to);if(y)if(m){var w=this.addConnection(y,m);w?w.id=v.id:i.push(\"Unable to create connection from \".concat(v.from,\" to \").concat(v.to))}else i.push(\"Could not find interface with id \".concat(v.to));else i.push(\"Could not find interface with id \".concat(v.from))}}catch(e){o={error:e}}finally{try{h&&!h.done&&(r=d.return)&&r.call(d)}finally{if(o)throw o.error}}return this.hooks.load.execute(e),i.forEach((function(e){return console.warn(e)})),i}finally{this._loading=!1}},e.prototype.save=function(){var e={nodes:this.nodes.map((function(e){return e.save()})),connections:this.connections.map((function(e){return{id:e.id,from:e.from.id,to:e.to.id}}))};return this.hooks.save.execute(e)},e.prototype.use=function(e){return!this.events.beforeUsePlugin.emit(e)&&(this._plugins.add(e),e.register(this),this.events.usePlugin.emit(e),!0)},e.prototype.generateId=function(e){return void 0===e&&(e=\"\"),e+t()},e.prototype.findNodeInterface=function(e){var t,n,o,r;try{for(var i=(0,s.__values)(this.nodes),a=i.next();!a.done;a=i.next()){var c=a.value;try{for(var u=(o=void 0,(0,s.__values)(c.interfaces.keys())),f=u.next();!f.done;f=u.next()){var p=f.value;if(c.interfaces.get(p).id===e)return c.interfaces.get(p)}}catch(e){o={error:e}}finally{try{f&&!f.done&&(r=u.return)&&r.call(u)}finally{if(o)throw o.error}}}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e}(),u=function(){function e(e,n){this.events={setConnectionCount:new r.EZ,beforeSetValue:new r.wD,setValue:new r.EZ,updated:new r.EZ},this.hooks={load:new r.p$,save:new r.p$},this._connectionCount=0,this._value=null,this.parent=e,this.isInput=n,this.id=\"ni_\"+t()}return Object.defineProperty(e.prototype,\"connectionCount\",{get:function(){return this._connectionCount},set:function(e){this._connectionCount=e,this.events.setConnectionCount.emit(e)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"value\",{get:function(){return this._value},set:function(e){this.events.beforeSetValue.emit(e)||(this._value=e,this.events.setValue.emit(e))},enumerable:!1,configurable:!0}),e.prototype.load=function(e){this.id=e.id,this.value=e.value,this.hooks.load.execute(e)},e.prototype.save=function(){var e={id:this.id,value:this.value};return this.hooks.save.execute(e)},e}(),f=function(){function e(e,t,n){this.events={beforeSetValue:new r.wD,setValue:new r.EZ,updated:new r.EZ},this.optionComponent=e,this.sidebarComponent=n,this._value=t}return Object.defineProperty(e.prototype,\"value\",{get:function(){return this._value},set:function(e){this.events.beforeSetValue.emit(e)||(this._value=e,this.events.setValue.emit(e))},enumerable:!1,configurable:!0}),e}(),p=function(){function e(){this.id=\"node_\"+t(),this.interfaces=new Map,this.options=new Map,this.state={},this.events={beforeAddInterface:new r.wD,addInterface:new r.EZ,beforeRemoveInterface:new r.wD,removeInterface:new r.EZ,beforeAddOption:new r.wD,addOption:new r.EZ,beforeRemoveOption:new r.wD,removeOption:new r.EZ,update:new r.EZ},this.hooks={load:new r.p$,save:new r.p$}}return Object.defineProperty(e.prototype,\"inputInterfaces\",{get:function(){var e={};return this.interfaces.forEach((function(t,n){t.isInput&&(e[n]=t)})),e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,\"outputInterfaces\",{get:function(){var e={};return this.interfaces.forEach((function(t,n){t.isInput||(e[n]=t)})),e},enumerable:!1,configurable:!0}),e.prototype.load=function(e){var t=this;this.id=e.id,this.name=e.name,this.state=e.state,e.options.forEach((function(e){var n=(0,s.__read)(e,2),o=n[0],r=n[1];t.options.has(o)&&(t.options.get(o).value=r)})),e.interfaces.forEach((function(e){var n=(0,s.__read)(e,2),o=n[0],r=n[1];t.interfaces.has(o)&&t.interfaces.get(o).load(r)})),this.hooks.load.execute(e)},e.prototype.save=function(){var e={type:this.type,id:this.id,name:this.name,options:Array.from(this.options.entries()).map((function(e){var t=(0,s.__read)(e,2);return[t[0],t[1].value]})),state:this.state,interfaces:Array.from(this.interfaces.entries()).map((function(e){var t=(0,s.__read)(e,2);return[t[0],t[1].save()]}))};return this.hooks.save.execute(e)},e.prototype.calculate=function(e){},e.prototype.addInputInterface=function(e,t,n,o){var r=this;if(void 0===n&&(n=null),!this.events.beforeAddInterface.emit({name:e,isInput:!0,option:t,defaultValue:n})){var i=this.addInterface(!0,e,t);return i.events.setValue.addListener(this,(function(){return r.events.update.emit({name:e,interface:i})})),i.value=n,Object.entries(o||{}).forEach((function(e){var t=(0,s.__read)(e,2),n=t[0],o=t[1];i[n]=o})),this.events.addInterface.emit(i),i}},e.prototype.addOutputInterface=function(e,t){if(!this.events.beforeAddInterface.emit({name:e,isInput:!1})){var n=this.addInterface(!1,e);return Object.entries(t||{}).forEach((function(e){var t=(0,s.__read)(e,2),o=t[0],r=t[1];n[o]=r})),this.events.addInterface.emit(n),n}},e.prototype.removeInterface=function(e){var t=this,n=this.getInterface(e);if(n){if(this.events.beforeRemoveInterface.emit(n))return;if(n.connectionCount>0){if(!this.editorInstance)throw new Error(\"Interface is connected, but no editor instance is specified. Unable to delete interface\");this.editorInstance.connections.filter((function(e){return e.from===n||e.to===n})).forEach((function(e){t.editorInstance.removeConnection(e)}))}this.interfaces.delete(e),this.events.removeInterface.emit(n)}},e.prototype.addOption=function(e,t,n,o,r){var i=this;if(void 0===n&&(n=null),!this.events.beforeAddOption.emit({name:e,component:t,defaultValue:n,sidebarComponent:o})){var a=new f(t,n,o);return Object.entries(r||{}).forEach((function(e){var t=(0,s.__read)(e,2),n=t[0],o=t[1];a[n]=o})),a.events.setValue.addListener(this,(function(){i.events.update.emit({name:e,option:a})})),this.options.set(e,a),this.events.addOption.emit({name:e,option:a}),a}},e.prototype.removeOption=function(e){if(this.options.has(e)){var t=this.options.get(e);if(this.events.beforeRemoveOption.emit({name:e,option:t}))return;this.options.delete(e),this.events.removeOption.emit({name:e,option:t})}},e.prototype.getInterface=function(e){if(!this.interfaces.has(e))throw new Error(\"No interface named '\".concat(e,\"'\"));return this.interfaces.get(e)},e.prototype.getOptionValue=function(e){if(!this.options.has(e))throw new Error(\"No option named '\".concat(e,\"'\"));return this.options.get(e).value},e.prototype.setOptionValue=function(e,t){if(!this.options.has(e))throw new Error(\"No option named '\".concat(e,\"'\"));this.options.get(e).value=t},e.prototype.registerEditor=function(e){this.editorInstance=e},e.prototype.addInterface=function(e,t,n){var o=new u(this,e);return o.option=n,this.interfaces.set(t,o),o},e}();function l(e){return\"function\"==typeof e?e():e}var d=function(){function e(e,t){this.type=\"\",this.name=\"\",this.intfs=[],this.options=new Map,this.type=e,this.name=e,this.additionalProperties=t}return e.prototype.build=function(){return e=this.type,t=this.name,n=this.additionalProperties,o=this.intfs,r=this.options,i=this.calcFunction,function(a){function c(){var i,c,u=a.call(this)||this;u.type=e,u.name=t,n&&Object.assign(u,n);try{for(var f=(0,s.__values)(o),p=f.next();!p.done;p=f.next()){var d=p.value;d.isInput?u.addInputInterface(d.name,d.option,l(d.defaultValue),d.additionalProperties):u.addOutputInterface(d.name,d.additionalProperties)}}catch(e){i={error:e}}finally{try{p&&!p.done&&(c=f.return)&&c.call(f)}finally{if(i)throw i.error}}return Array.from(r.entries()).forEach((function(e){var t=(0,s.__read)(e,2),n=t[0],o=t[1];u.addOption(n,o.optionComponent,l(o.value),o.sidebarComponent,o.additionalProperties)})),u}return(0,s.__extends)(c,a),c.prototype.calculate=function(e){if(i)return i.call(this,this,e)},c}(p);var e,t,n,o,r,i},e.prototype.setName=function(e){return this.name=e,this},e.prototype.addInputInterface=function(e,t,n,o){return void 0===n&&(n=null),this.checkDefaultValue(n),this.intfs.push({isInput:!0,name:e,option:t,defaultValue:n,additionalProperties:o}),this},e.prototype.addOutputInterface=function(e,t){return this.intfs.push({isInput:!1,name:e,additionalProperties:t}),this},e.prototype.addOption=function(e,t,n,o,r){return void 0===n&&(n=null),this.checkDefaultValue(n),this.options.set(e,{value:n,optionComponent:t,sidebarComponent:o,additionalProperties:r}),this},e.prototype.onCalculate=function(e){return this.calcFunction=e,this},e.prototype.checkDefaultValue=function(e){if(\"object\"==typeof e&&null!==e)throw new Error(\"If the default value is an object, provide a generator function instead of the object\")},e}()})(),exports.Connection=o.Connection,exports.DummyConnection=o.DummyConnection,exports.Editor=o.Editor,exports.Node=o.Node,exports.NodeBuilder=o.NodeBuilder,exports.NodeInterface=o.NodeInterface,exports.NodeOption=o.NodeOption,Object.defineProperty(exports,\"__esModule\",{value:!0})})();"], "mappings": ";;;;;AAAA;AAAA;AACA,KAAC,MAAI;AAAC;AAAa,UAAI,IAAE,EAAC,KAAI,CAACA,IAAEC,IAAEC,OAAI;AAAC,QAAAA,GAAE,EAAED,EAAC,GAAEC,GAAE,EAAED,IAAE,EAAC,UAAS,MAAI,GAAE,kBAAiB,MAAI,GAAE,kBAAiB,MAAI,GAAE,eAAc,MAAI,GAAE,SAAQ,MAAI,GAAE,WAAU,MAAI,GAAE,wBAAuB,MAAI,GAAE,wBAAuB,MAAI,GAAE,iBAAgB,MAAI,GAAE,YAAW,MAAI,GAAE,cAAa,MAAI,GAAE,WAAU,MAAI,GAAE,aAAY,MAAI,GAAE,iBAAgB,MAAI,GAAE,cAAa,MAAI,GAAE,sBAAqB,MAAI,GAAE,YAAW,MAAI,GAAE,SAAQ,MAAI,GAAE,QAAO,MAAI,GAAE,QAAO,MAAI,GAAE,UAAS,MAAI,GAAE,eAAc,MAAI,GAAE,gBAAe,MAAI,GAAE,UAAS,MAAI,EAAC,CAAC;AAAE,YAAIE,KAAE,SAASH,IAAEC,IAAE;AAAC,iBAAOE,KAAE,OAAO,kBAAgB,EAAC,WAAU,CAAC,EAAC,aAAY,SAAO,SAASH,IAAEC,IAAE;AAAC,YAAAD,GAAE,YAAUC;AAAA,UAAC,KAAG,SAASD,IAAEC,IAAE;AAAC,qBAAQC,MAAKD,GAAE,QAAO,UAAU,eAAe,KAAKA,IAAEC,EAAC,MAAIF,GAAEE,EAAC,IAAED,GAAEC,EAAC;AAAA,UAAE,GAAEC,GAAEH,IAAEC,EAAC;AAAA,QAAC;AAAE,iBAAS,EAAED,IAAEC,IAAE;AAAC,cAAG,cAAY,OAAOA,MAAG,SAAOA,GAAE,OAAM,IAAI,UAAU,yBAAuB,OAAOA,EAAC,IAAE,+BAA+B;AAAE,mBAASC,KAAG;AAAC,iBAAK,cAAYF;AAAA,UAAC;AAAC,UAAAG,GAAEH,IAAEC,EAAC,GAAED,GAAE,YAAU,SAAOC,KAAE,OAAO,OAAOA,EAAC,KAAGC,GAAE,YAAUD,GAAE,WAAU,IAAIC;AAAA,QAAE;AAAC,YAAI,IAAE,WAAU;AAAC,iBAAO,IAAE,OAAO,UAAQ,SAASF,IAAE;AAAC,qBAAQC,IAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED,KAAI,UAAQE,MAAKH,KAAE,UAAUC,EAAC,EAAE,QAAO,UAAU,eAAe,KAAKD,IAAEG,EAAC,MAAIJ,GAAEI,EAAC,IAAEH,GAAEG,EAAC;AAAG,mBAAOJ;AAAA,UAAC,GAAE,EAAE,MAAM,MAAK,SAAS;AAAA,QAAC;AAAE,iBAAS,EAAEA,IAAEC,IAAE;AAAC,cAAIC,KAAE,CAAC;AAAE,mBAAQC,MAAKH,GAAE,QAAO,UAAU,eAAe,KAAKA,IAAEG,EAAC,KAAGF,GAAE,QAAQE,EAAC,IAAE,MAAID,GAAEC,EAAC,IAAEH,GAAEG,EAAC;AAAG,cAAG,QAAMH,MAAG,cAAY,OAAO,OAAO,uBAAsB;AAAC,gBAAII,KAAE;AAAE,iBAAID,KAAE,OAAO,sBAAsBH,EAAC,GAAEI,KAAED,GAAE,QAAOC,KAAI,CAAAH,GAAE,QAAQE,GAAEC,EAAC,CAAC,IAAE,KAAG,OAAO,UAAU,qBAAqB,KAAKJ,IAAEG,GAAEC,EAAC,CAAC,MAAIF,GAAEC,GAAEC,EAAC,CAAC,IAAEJ,GAAEG,GAAEC,EAAC,CAAC;AAAA,UAAE;AAAC,iBAAOF;AAAA,QAAC;AAAC,iBAAS,EAAEF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,IAAEC,KAAE,UAAU,QAAOC,KAAED,KAAE,IAAEJ,KAAE,SAAOE,KAAEA,KAAE,OAAO,yBAAyBF,IAAEC,EAAC,IAAEC;AAAE,cAAG,YAAU,OAAO,WAAS,cAAY,OAAO,QAAQ,SAAS,CAAAG,KAAE,QAAQ,SAASN,IAAEC,IAAEC,IAAEC,EAAC;AAAA,cAAO,UAAQI,KAAEP,GAAE,SAAO,GAAEO,MAAG,GAAEA,KAAI,EAACH,KAAEJ,GAAEO,EAAC,OAAKD,MAAGD,KAAE,IAAED,GAAEE,EAAC,IAAED,KAAE,IAAED,GAAEH,IAAEC,IAAEI,EAAC,IAAEF,GAAEH,IAAEC,EAAC,MAAII;AAAG,iBAAOD,KAAE,KAAGC,MAAG,OAAO,eAAeL,IAAEC,IAAEI,EAAC,GAAEA;AAAA,QAAC;AAAC,iBAAS,EAAEN,IAAEC,IAAE;AAAC,iBAAO,SAASC,IAAEC,IAAE;AAAC,YAAAF,GAAEC,IAAEC,IAAEH,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAE;AAAC,cAAG,YAAU,OAAO,WAAS,cAAY,OAAO,QAAQ,SAAS,QAAO,QAAQ,SAASD,IAAEC,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAO,KAAID,OAAIA,KAAE,UAAW,SAASE,IAAEC,IAAE;AAAC,qBAASC,GAAEN,IAAE;AAAC,kBAAG;AAAC,gBAAAQ,GAAEL,GAAE,KAAKH,EAAC,CAAC;AAAA,cAAC,SAAOA,IAAE;AAAC,gBAAAK,GAAEL,EAAC;AAAA,cAAC;AAAA,YAAC;AAAC,qBAASO,GAAEP,IAAE;AAAC,kBAAG;AAAC,gBAAAQ,GAAEL,GAAE,MAAMH,EAAC,CAAC;AAAA,cAAC,SAAOA,IAAE;AAAC,gBAAAK,GAAEL,EAAC;AAAA,cAAC;AAAA,YAAC;AAAC,qBAASQ,GAAER,IAAE;AAAC,kBAAIC;AAAE,cAAAD,GAAE,OAAKI,GAAEJ,GAAE,KAAK,KAAGC,KAAED,GAAE,OAAMC,cAAaC,KAAED,KAAE,IAAIC,GAAG,SAASF,IAAE;AAAC,gBAAAA,GAAEC,EAAC;AAAA,cAAC,CAAE,GAAG,KAAKK,IAAEC,EAAC;AAAA,YAAC;AAAC,YAAAC,IAAGL,KAAEA,GAAE,MAAMH,IAAEC,MAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,UAAC,CAAE;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAE;AAAC,cAAIC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,EAAC,OAAM,GAAE,MAAK,WAAU;AAAC,gBAAG,IAAEF,GAAE,CAAC,EAAE,OAAMA,GAAE,CAAC;AAAE,mBAAOA,GAAE,CAAC;AAAA,UAAC,GAAE,MAAK,CAAC,GAAE,KAAI,CAAC,EAAC;AAAE,iBAAOC,KAAE,EAAC,MAAKE,GAAE,CAAC,GAAE,OAAMA,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,GAAE,cAAY,OAAO,WAASF,GAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,mBAAO;AAAA,UAAI,IAAGA;AAAE,mBAASE,GAAEF,IAAE;AAAC,mBAAO,SAASE,IAAE;AAAC,qBAAO,SAASF,IAAE;AAAC,oBAAGH,GAAE,OAAM,IAAI,UAAU,iCAAiC;AAAE,uBAAKI,KAAG,KAAG;AAAC,sBAAGJ,KAAE,GAAEC,OAAIC,KAAE,IAAEC,GAAE,CAAC,IAAEF,GAAE,SAAOE,GAAE,CAAC,IAAEF,GAAE,WAASC,KAAED,GAAE,WAASC,GAAE,KAAKD,EAAC,GAAE,KAAGA,GAAE,SAAO,EAAEC,KAAEA,GAAE,KAAKD,IAAEE,GAAE,CAAC,CAAC,GAAG,KAAK,QAAOD;AAAE,0BAAOD,KAAE,GAAEC,OAAIC,KAAE,CAAC,IAAEA,GAAE,CAAC,GAAED,GAAE,KAAK,IAAGC,GAAE,CAAC,GAAE;AAAA,oBAAC,KAAK;AAAA,oBAAE,KAAK;AAAE,sBAAAD,KAAEC;AAAE;AAAA,oBAAM,KAAK;AAAE,6BAAOC,GAAE,SAAQ,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,MAAE;AAAA,oBAAE,KAAK;AAAE,sBAAAC,GAAE,SAAQH,KAAEE,GAAE,CAAC,GAAEA,KAAE,CAAC,CAAC;AAAE;AAAA,oBAAS,KAAK;AAAE,sBAAAA,KAAEC,GAAE,IAAI,IAAI,GAAEA,GAAE,KAAK,IAAI;AAAE;AAAA,oBAAS;AAAQ,0BAAG,GAAGF,MAAGA,KAAEE,GAAE,MAAM,SAAO,KAAGF,GAAEA,GAAE,SAAO,CAAC,MAAI,MAAIC,GAAE,CAAC,KAAG,MAAIA,GAAE,CAAC,IAAG;AAAC,wBAAAC,KAAE;AAAE;AAAA,sBAAQ;AAAC,0BAAG,MAAID,GAAE,CAAC,MAAI,CAACD,MAAGC,GAAE,CAAC,IAAED,GAAE,CAAC,KAAGC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAG;AAAC,wBAAAE,GAAE,QAAMD,GAAE,CAAC;AAAE;AAAA,sBAAK;AAAC,0BAAG,MAAIA,GAAE,CAAC,KAAGC,GAAE,QAAMF,GAAE,CAAC,GAAE;AAAC,wBAAAE,GAAE,QAAMF,GAAE,CAAC,GAAEA,KAAEC;AAAE;AAAA,sBAAK;AAAC,0BAAGD,MAAGE,GAAE,QAAMF,GAAE,CAAC,GAAE;AAAC,wBAAAE,GAAE,QAAMF,GAAE,CAAC,GAAEE,GAAE,IAAI,KAAKD,EAAC;AAAE;AAAA,sBAAK;AAAC,sBAAAD,GAAE,CAAC,KAAGE,GAAE,IAAI,IAAI,GAAEA,GAAE,KAAK,IAAI;AAAE;AAAA,kBAAQ;AAAC,kBAAAD,KAAEJ,GAAE,KAAKD,IAAEM,EAAC;AAAA,gBAAC,SAAON,IAAE;AAAC,kBAAAK,KAAE,CAAC,GAAEL,EAAC,GAAEG,KAAE;AAAA,gBAAC,UAAC;AAAQ,kBAAAD,KAAEE,KAAE;AAAA,gBAAC;AAAC,oBAAG,IAAEC,GAAE,CAAC,EAAE,OAAMA,GAAE,CAAC;AAAE,uBAAM,EAAC,OAAMA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,QAAO,MAAK,KAAE;AAAA,cAAC,EAAE,CAACA,IAAEE,EAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAC,YAAI,IAAE,OAAO,SAAO,SAASP,IAAEC,IAAEC,IAAEC,IAAE;AAAC,qBAASA,OAAIA,KAAED,KAAG,OAAO,eAAeF,IAAEG,IAAE,EAAC,YAAW,MAAG,KAAI,WAAU;AAAC,mBAAOF,GAAEC,EAAC;AAAA,UAAC,EAAC,CAAC;AAAA,QAAC,IAAE,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,qBAASA,OAAIA,KAAED,KAAGF,GAAEG,EAAC,IAAEF,GAAEC,EAAC;AAAA,QAAC;AAAE,iBAAS,EAAEF,IAAEC,IAAE;AAAC,mBAAQC,MAAKF,GAAE,eAAYE,MAAG,OAAO,UAAU,eAAe,KAAKD,IAAEC,EAAC,KAAG,EAAED,IAAED,IAAEE,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEF,IAAE;AAAC,cAAIC,KAAE,cAAY,OAAO,UAAQ,OAAO,UAASC,KAAED,MAAGD,GAAEC,EAAC,GAAEE,KAAE;AAAE,cAAGD,GAAE,QAAOA,GAAE,KAAKF,EAAC;AAAE,cAAGA,MAAG,YAAU,OAAOA,GAAE,OAAO,QAAM,EAAC,MAAK,WAAU;AAAC,mBAAOA,MAAGG,MAAGH,GAAE,WAASA,KAAE,SAAQ,EAAC,OAAMA,MAAGA,GAAEG,IAAG,GAAE,MAAK,CAACH,GAAC;AAAA,UAAC,EAAC;AAAE,gBAAM,IAAI,UAAUC,KAAE,4BAA0B,iCAAiC;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAE;AAAC,cAAIC,KAAE,cAAY,OAAO,UAAQF,GAAE,OAAO,QAAQ;AAAE,cAAG,CAACE,GAAE,QAAOF;AAAE,cAAIG,IAAEC,IAAEC,KAAEH,GAAE,KAAKF,EAAC,GAAEM,KAAE,CAAC;AAAE,cAAG;AAAC,oBAAM,WAASL,MAAGA,OAAK,MAAI,EAAEE,KAAEE,GAAE,KAAK,GAAG,OAAM,CAAAC,GAAE,KAAKH,GAAE,KAAK;AAAA,UAAC,SAAOH,IAAE;AAAC,YAAAI,KAAE,EAAC,OAAMJ,GAAC;AAAA,UAAC,UAAC;AAAQ,gBAAG;AAAC,cAAAG,MAAG,CAACA,GAAE,SAAOD,KAAEG,GAAE,WAASH,GAAE,KAAKG,EAAC;AAAA,YAAC,UAAC;AAAQ,kBAAGD,GAAE,OAAMA,GAAE;AAAA,YAAK;AAAA,UAAC;AAAC,iBAAOE;AAAA,QAAC;AAAC,iBAAS,IAAG;AAAC,mBAAQN,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAE,UAAU,QAAOA,KAAI,CAAAD,KAAEA,GAAE,OAAO,EAAE,UAAUC,EAAC,CAAC,CAAC;AAAE,iBAAOD;AAAA,QAAC;AAAC,iBAAS,IAAG;AAAC,mBAAQA,KAAE,GAAEC,KAAE,GAAEC,KAAE,UAAU,QAAOD,KAAEC,IAAED,KAAI,CAAAD,MAAG,UAAUC,EAAC,EAAE;AAAO,cAAIE,KAAE,MAAMH,EAAC,GAAEI,KAAE;AAAE,eAAIH,KAAE,GAAEA,KAAEC,IAAED,KAAI,UAAQI,KAAE,UAAUJ,EAAC,GAAEK,KAAE,GAAEC,KAAEF,GAAE,QAAOC,KAAEC,IAAED,MAAIF,KAAI,CAAAD,GAAEC,EAAC,IAAEC,GAAEC,EAAC;AAAE,iBAAOH;AAAA,QAAC;AAAC,iBAAS,EAAEH,IAAEC,IAAEC,IAAE;AAAC,cAAGA,MAAG,MAAI,UAAU,OAAO,UAAQC,IAAEC,KAAE,GAAEC,KAAEJ,GAAE,QAAOG,KAAEC,IAAED,KAAI,EAACD,MAAGC,MAAKH,OAAIE,OAAIA,KAAE,MAAM,UAAU,MAAM,KAAKF,IAAE,GAAEG,EAAC,IAAGD,GAAEC,EAAC,IAAEH,GAAEG,EAAC;AAAG,iBAAOJ,GAAE,OAAOG,MAAG,MAAM,UAAU,MAAM,KAAKF,EAAC,CAAC;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAE;AAAC,iBAAO,gBAAgB,KAAG,KAAK,IAAEA,IAAE,QAAM,IAAI,EAAEA,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,cAAG,CAAC,OAAO,cAAc,OAAM,IAAI,UAAU,sCAAsC;AAAE,cAAIC,IAAEC,KAAEF,GAAE,MAAMF,IAAEC,MAAG,CAAC,CAAC,GAAEI,KAAE,CAAC;AAAE,iBAAOF,KAAE,CAAC,GAAEG,GAAE,MAAM,GAAEA,GAAE,OAAO,GAAEA,GAAE,QAAQ,GAAEH,GAAE,OAAO,aAAa,IAAE,WAAU;AAAC,mBAAO;AAAA,UAAI,GAAEA;AAAE,mBAASG,GAAEN,IAAE;AAAC,YAAAI,GAAEJ,EAAC,MAAIG,GAAEH,EAAC,IAAE,SAASC,IAAE;AAAC,qBAAO,IAAI,QAAS,SAASC,IAAEC,IAAE;AAAC,gBAAAE,GAAE,KAAK,CAACL,IAAEC,IAAEC,IAAEC,EAAC,CAAC,IAAE,KAAGI,GAAEP,IAAEC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,UAAE;AAAC,mBAASM,GAAEP,IAAEC,IAAE;AAAC,gBAAG;AAAC,eAACC,KAAEE,GAAEJ,EAAC,EAAEC,EAAC,GAAG,iBAAiB,IAAE,QAAQ,QAAQC,GAAE,MAAM,CAAC,EAAE,KAAKM,IAAEC,EAAC,IAAEC,GAAEL,GAAE,CAAC,EAAE,CAAC,GAAEH,EAAC;AAAA,YAAC,SAAOF,IAAE;AAAC,cAAAU,GAAEL,GAAE,CAAC,EAAE,CAAC,GAAEL,EAAC;AAAA,YAAC;AAAC,gBAAIE;AAAA,UAAC;AAAC,mBAASM,GAAER,IAAE;AAAC,YAAAO,GAAE,QAAOP,EAAC;AAAA,UAAC;AAAC,mBAASS,GAAET,IAAE;AAAC,YAAAO,GAAE,SAAQP,EAAC;AAAA,UAAC;AAAC,mBAASU,GAAEV,IAAEC,IAAE;AAAC,YAAAD,GAAEC,EAAC,GAAEI,GAAE,MAAM,GAAEA,GAAE,UAAQE,GAAEF,GAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,CAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAEL,IAAE;AAAC,cAAIC,IAAEC;AAAE,iBAAOD,KAAE,CAAC,GAAEE,GAAE,MAAM,GAAEA,GAAE,SAAS,SAASH,IAAE;AAAC,kBAAMA;AAAA,UAAC,CAAE,GAAEG,GAAE,QAAQ,GAAEF,GAAE,OAAO,QAAQ,IAAE,WAAU;AAAC,mBAAO;AAAA,UAAI,GAAEA;AAAE,mBAASE,GAAEA,IAAEC,IAAE;AAAC,YAAAH,GAAEE,EAAC,IAAEH,GAAEG,EAAC,IAAE,SAASF,IAAE;AAAC,sBAAOC,KAAE,CAACA,MAAG,EAAC,OAAM,EAAEF,GAAEG,EAAC,EAAEF,EAAC,CAAC,GAAE,MAAK,aAAWE,GAAC,IAAEC,KAAEA,GAAEH,EAAC,IAAEA;AAAA,YAAC,IAAEG;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAEJ,IAAE;AAAC,cAAG,CAAC,OAAO,cAAc,OAAM,IAAI,UAAU,sCAAsC;AAAE,cAAIC,IAAEC,KAAEF,GAAE,OAAO,aAAa;AAAE,iBAAOE,KAAEA,GAAE,KAAKF,EAAC,KAAGA,KAAE,EAAEA,EAAC,GAAEC,KAAE,CAAC,GAAEE,GAAE,MAAM,GAAEA,GAAE,OAAO,GAAEA,GAAE,QAAQ,GAAEF,GAAE,OAAO,aAAa,IAAE,WAAU;AAAC,mBAAO;AAAA,UAAI,GAAEA;AAAG,mBAASE,GAAED,IAAE;AAAC,YAAAD,GAAEC,EAAC,IAAEF,GAAEE,EAAC,KAAG,SAASD,IAAE;AAAC,qBAAO,IAAI,QAAS,SAASE,IAAEC,IAAE;AAAC,iBAAC,SAASJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,0BAAQ,QAAQA,EAAC,EAAE,KAAM,SAASF,IAAE;AAAC,oBAAAD,GAAE,EAAC,OAAMC,IAAE,MAAKC,GAAC,CAAC;AAAA,kBAAC,GAAGD,EAAC;AAAA,gBAAC,EAAEE,IAAEC,KAAGH,KAAED,GAAEE,EAAC,EAAED,EAAC,GAAG,MAAKA,GAAE,KAAK;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAEC,IAAE;AAAC,iBAAO,OAAO,iBAAe,OAAO,eAAeD,IAAE,OAAM,EAAC,OAAMC,GAAC,CAAC,IAAED,GAAE,MAAIC,IAAED;AAAA,QAAC;AAAC,YAAI,IAAE,OAAO,SAAO,SAASA,IAAEC,IAAE;AAAC,iBAAO,eAAeD,IAAE,WAAU,EAAC,YAAW,MAAG,OAAMC,GAAC,CAAC;AAAA,QAAC,IAAE,SAASD,IAAEC,IAAE;AAAC,UAAAD,GAAE,UAAQC;AAAA,QAAC;AAAE,iBAAS,EAAED,IAAE;AAAC,cAAGA,MAAGA,GAAE,WAAW,QAAOA;AAAE,cAAIC,KAAE,CAAC;AAAE,cAAG,QAAMD,GAAE,UAAQE,MAAKF,GAAE,eAAYE,MAAG,OAAO,UAAU,eAAe,KAAKF,IAAEE,EAAC,KAAG,EAAED,IAAED,IAAEE,EAAC;AAAE,iBAAO,EAAED,IAAED,EAAC,GAAEC;AAAA,QAAC;AAAC,iBAAS,EAAED,IAAE;AAAC,iBAAOA,MAAGA,GAAE,aAAWA,KAAE,EAAC,SAAQA,GAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAG,QAAMD,MAAG,CAACC,GAAE,OAAM,IAAI,UAAU,+CAA+C;AAAE,cAAG,cAAY,OAAOF,KAAED,OAAIC,MAAG,CAACE,KAAE,CAACF,GAAE,IAAID,EAAC,EAAE,OAAM,IAAI,UAAU,0EAA0E;AAAE,iBAAM,QAAME,KAAEC,KAAE,QAAMD,KAAEC,GAAE,KAAKH,EAAC,IAAEG,KAAEA,GAAE,QAAMF,GAAE,IAAID,EAAC;AAAA,QAAC;AAAC,iBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAG,QAAMD,GAAE,OAAM,IAAI,UAAU,gCAAgC;AAAE,cAAG,QAAMA,MAAG,CAACC,GAAE,OAAM,IAAI,UAAU,+CAA+C;AAAE,cAAG,cAAY,OAAOH,KAAED,OAAIC,MAAG,CAACG,KAAE,CAACH,GAAE,IAAID,EAAC,EAAE,OAAM,IAAI,UAAU,yEAAyE;AAAE,iBAAM,QAAMG,KAAEC,GAAE,KAAKJ,IAAEE,EAAC,IAAEE,KAAEA,GAAE,QAAMF,KAAED,GAAE,IAAID,IAAEE,EAAC,GAAEA;AAAA,QAAC;AAAA,MAAC,GAAE,KAAI,CAACF,IAAEC,IAAEC,OAAI;AAAC,QAAAD,GAAE,KAAGA,GAAE,KAAGA,GAAE,KAAG;AAAO,YAAIE,KAAED,GAAE,GAAG,GAAE,IAAE,WAAU;AAAC,mBAASF,KAAG;AAAC,iBAAK,YAAU,oBAAI;AAAA,UAAG;AAAC,iBAAOA,GAAE,UAAU,cAAY,SAASA,IAAEC,IAAE;AAAC,iBAAK,UAAU,IAAID,IAAEC,EAAC;AAAA,UAAC,GAAED,GAAE,UAAU,iBAAe,SAASA,IAAE;AAAC,iBAAK,UAAU,IAAIA,EAAC,KAAG,KAAK,UAAU,OAAOA,EAAC;AAAA,UAAC,GAAEA,GAAE,UAAU,OAAK,SAASA,IAAE;AAAC,iBAAK,UAAU,QAAS,SAASC,IAAE;AAAC,qBAAOA,GAAED,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC,GAAEA;AAAA,QAAC,EAAE;AAAE,QAAAC,GAAE,KAAG;AAAE,YAAI,IAAE,SAASD,IAAE;AAAC,mBAASC,KAAG;AAAC,mBAAO,SAAOD,MAAGA,GAAE,MAAM,MAAK,SAAS,KAAG;AAAA,UAAI;AAAC,iBAAOG,GAAE,UAAUF,IAAED,EAAC,GAAEC,GAAE,UAAU,OAAK,SAASD,IAAE;AAAC,gBAAIC,IAAEC;AAAE,gBAAG;AAAC,uBAAQE,KAAED,GAAE,SAAS,MAAM,KAAK,KAAK,UAAU,OAAO,CAAC,CAAC,GAAEE,KAAED,GAAE,KAAK,GAAE,CAACC,GAAE,MAAKA,KAAED,GAAE,KAAK,EAAE,KAAG,WAAM,GAAEC,GAAE,OAAOL,EAAC,EAAE,QAAM;AAAA,YAAE,SAAOA,IAAE;AAAC,cAAAC,KAAE,EAAC,OAAMD,GAAC;AAAA,YAAC,UAAC;AAAQ,kBAAG;AAAC,gBAAAK,MAAG,CAACA,GAAE,SAAOH,KAAEE,GAAE,WAASF,GAAE,KAAKE,EAAC;AAAA,cAAC,UAAC;AAAQ,oBAAGH,GAAE,OAAMA,GAAE;AAAA,cAAK;AAAA,YAAC;AAAC,mBAAM;AAAA,UAAE,GAAEA;AAAA,QAAC,EAAE,CAAC;AAAE,QAAAA,GAAE,KAAG;AAAE,YAAI,IAAE,SAASD,IAAE;AAAC,mBAASC,KAAG;AAAC,mBAAO,SAAOD,MAAGA,GAAE,MAAM,MAAK,SAAS,KAAG;AAAA,UAAI;AAAC,iBAAOG,GAAE,UAAUF,IAAED,EAAC,GAAEC,GAAE,UAAU,UAAQ,SAASD,IAAE;AAAC,gBAAIC,IAAEC,IAAEE,KAAEJ;AAAE,gBAAG;AAAC,uBAAQK,KAAEF,GAAE,SAAS,KAAK,IAAI,GAAEG,KAAED,GAAE,KAAK,GAAE,CAACC,GAAE,MAAKA,KAAED,GAAE,KAAK,EAAE,CAAAD,MAAG,GAAEE,GAAE,OAAOF,EAAC;AAAA,YAAC,SAAOJ,IAAE;AAAC,cAAAC,KAAE,EAAC,OAAMD,GAAC;AAAA,YAAC,UAAC;AAAQ,kBAAG;AAAC,gBAAAM,MAAG,CAACA,GAAE,SAAOJ,KAAEG,GAAE,WAASH,GAAE,KAAKG,EAAC;AAAA,cAAC,UAAC;AAAQ,oBAAGJ,GAAE,OAAMA,GAAE;AAAA,cAAK;AAAA,YAAC;AAAC,mBAAOG;AAAA,UAAC,GAAEH;AAAA,QAAC,EAAE,WAAU;AAAC,mBAASD,KAAG;AAAC,iBAAK,SAAO,oBAAI,OAAI,KAAK,OAAK,CAAC;AAAA,UAAC;AAAC,iBAAOA,GAAE,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,iBAAK,OAAO,IAAID,EAAC,KAAG,KAAK,MAAMA,EAAC,GAAE,KAAK,OAAO,IAAIA,IAAEC,EAAC,GAAE,KAAK,KAAK,KAAKA,EAAC;AAAA,UAAC,GAAED,GAAE,UAAU,QAAM,SAASA,IAAE;AAAC,gBAAG,KAAK,OAAO,IAAIA,EAAC,GAAE;AAAC,kBAAIC,KAAE,KAAK,OAAO,IAAID,EAAC;AAAE,mBAAK,OAAO,OAAOA,EAAC;AAAE,kBAAIE,KAAE,KAAK,KAAK,QAAQD,EAAC;AAAE,cAAAC,MAAG,KAAG,KAAK,KAAK,OAAOA,IAAE,CAAC;AAAA,YAAC;AAAA,UAAC,GAAEF;AAAA,QAAC,EAAE,CAAC;AAAE,QAAAC,GAAE,KAAG;AAAA,MAAC,EAAC,GAAE,IAAE,CAAC;AAAE,eAAS,EAAEE,IAAE;AAAC,YAAI,IAAE,EAAEA,EAAC;AAAE,YAAG,WAAS,EAAE,QAAO,EAAE;AAAQ,YAAI,IAAE,EAAEA,EAAC,IAAE,EAAC,SAAQ,CAAC,EAAC;AAAE,eAAO,EAAEA,EAAC,EAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAE;AAAA,MAAO;AAAC,QAAE,IAAE,CAACH,IAAEC,OAAI;AAAC,iBAAQE,MAAKF,GAAE,GAAE,EAAEA,IAAEE,EAAC,KAAG,CAAC,EAAE,EAAEH,IAAEG,EAAC,KAAG,OAAO,eAAeH,IAAEG,IAAE,EAAC,YAAW,MAAG,KAAIF,GAAEE,EAAC,EAAC,CAAC;AAAA,MAAC,GAAE,EAAE,IAAE,CAACH,IAAEC,OAAI,OAAO,UAAU,eAAe,KAAKD,IAAEC,EAAC,GAAE,EAAE,IAAE,CAAAD,OAAG;AAAC,uBAAa,OAAO,UAAQ,OAAO,eAAa,OAAO,eAAeA,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,MAAC;AAAE,UAAI,IAAE,CAAC;AAAE,OAAC,MAAI;AAAC,UAAE,EAAE,CAAC,GAAE,EAAE,EAAE,GAAE,EAAC,YAAW,MAAI,GAAE,iBAAgB,MAAI,GAAE,QAAO,MAAI,GAAE,MAAK,MAAI,GAAE,aAAY,MAAI,GAAE,eAAc,MAAI,GAAE,YAAW,MAAI,EAAC,CAAC;AAAE,YAAIA,KAAE;AAAE,iBAASC,KAAG;AAAC,iBAAO,KAAK,IAAI,EAAE,SAAS,KAAGD,MAAK,SAAS;AAAA,QAAC;AAAC,YAAI,IAAE,EAAE,GAAG,GAAE,IAAE,WAAU;AAAC,mBAASA,GAAEA,IAAEE,IAAE;AAAC,gBAAG,KAAK,aAAW,OAAG,KAAK,aAAW,OAAG,KAAK,SAAO,EAAC,UAAS,IAAI,EAAE,KAAE,GAAE,CAACF,MAAG,CAACE,GAAE,OAAM,IAAI,MAAM,4EAA4E;AAAE,iBAAK,KAAGD,GAAE,GAAE,KAAK,OAAKD,IAAE,KAAK,KAAGE,IAAE,KAAK,KAAK,mBAAkB,KAAK,GAAG;AAAA,UAAiB;AAAC,iBAAOF,GAAE,UAAU,WAAS,WAAU;AAAC,iBAAK,OAAO,SAAS,KAAK,GAAE,KAAK,KAAK,mBAAkB,KAAK,GAAG,mBAAkB,KAAK,aAAW;AAAA,UAAE,GAAEA;AAAA,QAAC,EAAE,GAAE,IAAE,SAASA,IAAEE,IAAE;AAAC,cAAG,CAACF,MAAG,CAACE,GAAE,OAAM,IAAI,MAAM,4EAA4E;AAAE,eAAK,KAAGD,GAAE,GAAE,KAAK,OAAKD,IAAE,KAAK,KAAGE;AAAA,QAAC,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,WAAU;AAAC,mBAASF,KAAG;AAAC,iBAAK,WAAS,oBAAI,OAAI,KAAK,SAAO,CAAC,GAAE,KAAK,eAAa,CAAC,GAAE,KAAK,aAAW,oBAAI,OAAI,KAAK,kBAAgB,oBAAI,IAAI,CAAC,CAAC,WAAU,CAAC,CAAC,CAAC,CAAC,GAAE,KAAK,WAAS,OAAG,KAAK,SAAO,EAAC,wBAAuB,IAAI,EAAE,MAAG,kBAAiB,IAAI,EAAE,MAAG,eAAc,IAAI,EAAE,MAAG,SAAQ,IAAI,EAAE,MAAG,kBAAiB,IAAI,EAAE,MAAG,YAAW,IAAI,EAAE,MAAG,qBAAoB,IAAI,EAAE,MAAG,eAAc,IAAI,EAAE,MAAG,iBAAgB,IAAI,EAAE,MAAG,wBAAuB,IAAI,EAAE,MAAG,kBAAiB,IAAI,EAAE,MAAG,iBAAgB,IAAI,EAAE,MAAG,WAAU,IAAI,EAAE,KAAE,GAAE,KAAK,QAAM,EAAC,MAAK,IAAI,EAAE,MAAG,MAAK,IAAI,EAAE,KAAE;AAAA,UAAC;AAAC,iBAAO,OAAO,eAAeA,GAAE,WAAU,SAAQ,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAM,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAE,OAAO,eAAeA,GAAE,WAAU,eAAc,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAY,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAE,OAAO,eAAeA,GAAE,WAAU,aAAY,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAU,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAE,OAAO,eAAeA,GAAE,WAAU,kBAAiB,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAe,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAE,OAAO,eAAeA,GAAE,WAAU,WAAU,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAQ,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAE,OAAO,eAAeA,GAAE,WAAU,WAAU,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAQ,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAEA,GAAE,UAAU,mBAAiB,SAASA,IAAEC,IAAEC,IAAE;AAAC,uBAASA,OAAIA,KAAE,YAAW,KAAK,OAAO,uBAAuB,KAAK,EAAC,UAASF,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC,MAAI,KAAK,WAAW,IAAIF,IAAEC,EAAC,GAAE,KAAK,eAAe,IAAIC,EAAC,KAAG,KAAK,gBAAgB,IAAIA,IAAE,CAAC,CAAC,GAAE,KAAK,eAAe,IAAIA,EAAC,EAAE,KAAKF,EAAC,GAAE,KAAK,OAAO,iBAAiB,KAAK,EAAC,UAASA,IAAE,MAAKC,IAAE,UAASC,GAAC,CAAC;AAAA,UAAE,GAAEF,GAAE,UAAU,UAAQ,SAASA,IAAE;AAAC,gBAAG,CAAC,KAAK,OAAO,cAAc,KAAKA,EAAC,EAAE,QAAOA,GAAE,eAAe,IAAI,GAAE,KAAK,OAAO,KAAKA,EAAC,GAAE,KAAK,OAAO,QAAQ,KAAKA,EAAC,GAAEA;AAAA,UAAC,GAAEA,GAAE,UAAU,aAAW,SAASA,IAAE;AAAC,gBAAIC,KAAE;AAAK,gBAAG,KAAK,MAAM,SAASD,EAAC,GAAE;AAAC,kBAAG,KAAK,OAAO,iBAAiB,KAAKA,EAAC,EAAE;AAAO,mBAAK,YAAY,OAAQ,SAASC,IAAE;AAAC,uBAAOA,GAAE,KAAK,WAASD,MAAGC,GAAE,GAAG,WAASD;AAAA,cAAC,CAAE,EAAE,QAAS,SAASA,IAAE;AAAC,uBAAOC,GAAE,iBAAiBD,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,OAAO,OAAO,KAAK,MAAM,QAAQA,EAAC,GAAE,CAAC,GAAE,KAAK,OAAO,WAAW,KAAKA,EAAC;AAAA,YAAC;AAAA,UAAC,GAAEA,GAAE,UAAU,gBAAc,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK,gBAAgBF,IAAEC,EAAC;AAAE,gBAAGC,MAAG,CAAC,KAAK,OAAO,oBAAoB,KAAK,EAAC,MAAKF,IAAE,IAAGC,GAAC,CAAC,GAAE;AAAC,kBAAIE,KAAE,IAAI,EAAED,GAAE,MAAKA,GAAE,EAAE;AAAE,qBAAO,KAAK,aAAa,KAAKC,EAAC,GAAE,KAAK,OAAO,cAAc,KAAKA,EAAC,GAAEA;AAAA,YAAC;AAAA,UAAC,GAAEH,GAAE,UAAU,mBAAiB,SAASA,IAAE;AAAC,gBAAG,KAAK,YAAY,SAASA,EAAC,GAAE;AAAC,kBAAG,KAAK,OAAO,uBAAuB,KAAKA,EAAC,EAAE;AAAO,cAAAA,GAAE,SAAS,GAAE,KAAK,aAAa,OAAO,KAAK,YAAY,QAAQA,EAAC,GAAE,CAAC,GAAE,KAAK,OAAO,iBAAiB,KAAKA,EAAC;AAAA,YAAC;AAAA,UAAC,GAAEA,GAAE,UAAU,kBAAgB,SAASA,IAAEC,IAAE;AAAC,gBAAG,CAACD,MAAG,CAACC,GAAE,QAAM;AAAG,gBAAGD,GAAE,WAASC,GAAE,OAAO,QAAM;AAAG,gBAAGD,GAAE,WAAS,CAACC,GAAE,SAAQ;AAAC,kBAAIC,KAAEF;AAAE,cAAAA,KAAEC,IAAEA,KAAEC;AAAA,YAAC;AAAC,mBAAM,EAAEF,GAAE,WAAS,CAACC,GAAE,YAAU,CAAC,KAAK,YAAY,KAAM,SAASC,IAAE;AAAC,qBAAOA,GAAE,SAAOF,MAAGE,GAAE,OAAKD;AAAA,YAAC,CAAE,KAAG,CAAC,KAAK,OAAO,gBAAgB,KAAK,EAAC,MAAKD,IAAE,IAAGC,GAAC,CAAC,KAAG,IAAI,EAAED,IAAEC,EAAC;AAAA,UAAC,GAAED,GAAE,UAAU,OAAK,SAASA,IAAE;AAAC,gBAAIC,IAAEC,IAAEC,IAAEC;AAAE,gBAAG;AAAC,mBAAK,WAAS;AAAG,uBAAQC,KAAE,CAAC,GAAEC,KAAE,KAAK,YAAY,SAAO,GAAEA,MAAG,GAAEA,KAAI,MAAK,iBAAiB,KAAK,YAAYA,EAAC,CAAC;AAAE,mBAAIA,KAAE,KAAK,MAAM,SAAO,GAAEA,MAAG,GAAEA,KAAI,MAAK,WAAW,KAAK,MAAMA,EAAC,CAAC;AAAE,kBAAG;AAAC,yBAAQE,MAAG,GAAE,EAAE,UAAUR,GAAE,KAAK,GAAES,KAAED,GAAE,KAAK,GAAE,CAACC,GAAE,MAAKA,KAAED,GAAE,KAAK,GAAE;AAAC,sBAAIE,KAAED,GAAE,OAAME,KAAE,KAAK,UAAU,IAAID,GAAE,IAAI;AAAE,sBAAGC,IAAE;AAAC,wBAAIC,KAAE,IAAID;AAAE,yBAAK,QAAQC,EAAC,GAAEA,GAAE,KAAKF,EAAC;AAAA,kBAAC,MAAM,CAAAL,GAAE,KAAK,aAAa,OAAOK,GAAE,MAAK,oBAAoB,CAAC;AAAA,gBAAC;AAAA,cAAC,SAAOV,IAAE;AAAC,gBAAAC,KAAE,EAAC,OAAMD,GAAC;AAAA,cAAC,UAAC;AAAQ,oBAAG;AAAC,kBAAAS,MAAG,CAACA,GAAE,SAAOP,KAAEM,GAAE,WAASN,GAAE,KAAKM,EAAC;AAAA,gBAAC,UAAC;AAAQ,sBAAGP,GAAE,OAAMA,GAAE;AAAA,gBAAK;AAAA,cAAC;AAAC,kBAAG;AAAC,yBAAQY,MAAG,GAAE,EAAE,UAAUb,GAAE,WAAW,GAAE,IAAEa,GAAE,KAAK,GAAE,CAAC,EAAE,MAAK,IAAEA,GAAE,KAAK,GAAE;AAAC,sBAAI,IAAE,EAAE,OAAM,IAAE,KAAK,kBAAkB,EAAE,IAAI,GAAE,IAAE,KAAK,kBAAkB,EAAE,EAAE;AAAE,sBAAG,EAAE,KAAG,GAAE;AAAC,wBAAI,IAAE,KAAK,cAAc,GAAE,CAAC;AAAE,wBAAE,EAAE,KAAG,EAAE,KAAGR,GAAE,KAAK,oCAAoC,OAAO,EAAE,MAAK,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC;AAAA,kBAAC,MAAM,CAAAA,GAAE,KAAK,oCAAoC,OAAO,EAAE,EAAE,CAAC;AAAA,sBAAO,CAAAA,GAAE,KAAK,oCAAoC,OAAO,EAAE,IAAI,CAAC;AAAA,gBAAC;AAAA,cAAC,SAAOL,IAAE;AAAC,gBAAAG,KAAE,EAAC,OAAMH,GAAC;AAAA,cAAC,UAAC;AAAQ,oBAAG;AAAC,uBAAG,CAAC,EAAE,SAAOI,KAAES,GAAE,WAAST,GAAE,KAAKS,EAAC;AAAA,gBAAC,UAAC;AAAQ,sBAAGV,GAAE,OAAMA,GAAE;AAAA,gBAAK;AAAA,cAAC;AAAC,qBAAO,KAAK,MAAM,KAAK,QAAQH,EAAC,GAAEK,GAAE,QAAS,SAASL,IAAE;AAAC,uBAAO,QAAQ,KAAKA,EAAC;AAAA,cAAC,CAAE,GAAEK;AAAA,YAAC,UAAC;AAAQ,mBAAK,WAAS;AAAA,YAAE;AAAA,UAAC,GAAEL,GAAE,UAAU,OAAK,WAAU;AAAC,gBAAIA,KAAE,EAAC,OAAM,KAAK,MAAM,IAAK,SAASA,IAAE;AAAC,qBAAOA,GAAE,KAAK;AAAA,YAAC,CAAE,GAAE,aAAY,KAAK,YAAY,IAAK,SAASA,IAAE;AAAC,qBAAM,EAAC,IAAGA,GAAE,IAAG,MAAKA,GAAE,KAAK,IAAG,IAAGA,GAAE,GAAG,GAAE;AAAA,YAAC,CAAE,EAAC;AAAE,mBAAO,KAAK,MAAM,KAAK,QAAQA,EAAC;AAAA,UAAC,GAAEA,GAAE,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAM,CAAC,KAAK,OAAO,gBAAgB,KAAKA,EAAC,MAAI,KAAK,SAAS,IAAIA,EAAC,GAAEA,GAAE,SAAS,IAAI,GAAE,KAAK,OAAO,UAAU,KAAKA,EAAC,GAAE;AAAA,UAAG,GAAEA,GAAE,UAAU,aAAW,SAASA,IAAE;AAAC,mBAAO,WAASA,OAAIA,KAAE,KAAIA,KAAEC,GAAE;AAAA,UAAC,GAAED,GAAE,UAAU,oBAAkB,SAASA,IAAE;AAAC,gBAAIC,IAAEC,IAAEC,IAAEC;AAAE,gBAAG;AAAC,uBAAQC,MAAG,GAAE,EAAE,UAAU,KAAK,KAAK,GAAEC,KAAED,GAAE,KAAK,GAAE,CAACC,GAAE,MAAKA,KAAED,GAAE,KAAK,GAAE;AAAC,oBAAIG,KAAEF,GAAE;AAAM,oBAAG;AAAC,2BAAQG,MAAGN,KAAE,SAAQ,GAAE,EAAE,UAAUK,GAAE,WAAW,KAAK,CAAC,IAAGE,KAAED,GAAE,KAAK,GAAE,CAACC,GAAE,MAAKA,KAAED,GAAE,KAAK,GAAE;AAAC,wBAAIE,KAAED,GAAE;AAAM,wBAAGF,GAAE,WAAW,IAAIG,EAAC,EAAE,OAAKX,GAAE,QAAOQ,GAAE,WAAW,IAAIG,EAAC;AAAA,kBAAC;AAAA,gBAAC,SAAOX,IAAE;AAAC,kBAAAG,KAAE,EAAC,OAAMH,GAAC;AAAA,gBAAC,UAAC;AAAQ,sBAAG;AAAC,oBAAAU,MAAG,CAACA,GAAE,SAAON,KAAEK,GAAE,WAASL,GAAE,KAAKK,EAAC;AAAA,kBAAC,UAAC;AAAQ,wBAAGN,GAAE,OAAMA,GAAE;AAAA,kBAAK;AAAA,gBAAC;AAAA,cAAC;AAAA,YAAC,SAAOH,IAAE;AAAC,cAAAC,KAAE,EAAC,OAAMD,GAAC;AAAA,YAAC,UAAC;AAAQ,kBAAG;AAAC,gBAAAM,MAAG,CAACA,GAAE,SAAOJ,KAAEG,GAAE,WAASH,GAAE,KAAKG,EAAC;AAAA,cAAC,UAAC;AAAQ,oBAAGJ,GAAE,OAAMA,GAAE;AAAA,cAAK;AAAA,YAAC;AAAA,UAAC,GAAED;AAAA,QAAC,EAAE,GAAE,IAAE,WAAU;AAAC,mBAASA,GAAEA,IAAEE,IAAE;AAAC,iBAAK,SAAO,EAAC,oBAAmB,IAAI,EAAE,MAAG,gBAAe,IAAI,EAAE,MAAG,UAAS,IAAI,EAAE,MAAG,SAAQ,IAAI,EAAE,KAAE,GAAE,KAAK,QAAM,EAAC,MAAK,IAAI,EAAE,MAAG,MAAK,IAAI,EAAE,KAAE,GAAE,KAAK,mBAAiB,GAAE,KAAK,SAAO,MAAK,KAAK,SAAOF,IAAE,KAAK,UAAQE,IAAE,KAAK,KAAG,QAAMD,GAAE;AAAA,UAAC;AAAC,iBAAO,OAAO,eAAeD,GAAE,WAAU,mBAAkB,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAgB,GAAE,KAAI,SAASA,IAAE;AAAC,iBAAK,mBAAiBA,IAAE,KAAK,OAAO,mBAAmB,KAAKA,EAAC;AAAA,UAAC,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAE,OAAO,eAAeA,GAAE,WAAU,SAAQ,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAM,GAAE,KAAI,SAASA,IAAE;AAAC,iBAAK,OAAO,eAAe,KAAKA,EAAC,MAAI,KAAK,SAAOA,IAAE,KAAK,OAAO,SAAS,KAAKA,EAAC;AAAA,UAAE,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAEA,GAAE,UAAU,OAAK,SAASA,IAAE;AAAC,iBAAK,KAAGA,GAAE,IAAG,KAAK,QAAMA,GAAE,OAAM,KAAK,MAAM,KAAK,QAAQA,EAAC;AAAA,UAAC,GAAEA,GAAE,UAAU,OAAK,WAAU;AAAC,gBAAIA,KAAE,EAAC,IAAG,KAAK,IAAG,OAAM,KAAK,MAAK;AAAE,mBAAO,KAAK,MAAM,KAAK,QAAQA,EAAC;AAAA,UAAC,GAAEA;AAAA,QAAC,EAAE,GAAE,IAAE,WAAU;AAAC,mBAASA,GAAEA,IAAEC,IAAEC,IAAE;AAAC,iBAAK,SAAO,EAAC,gBAAe,IAAI,EAAE,MAAG,UAAS,IAAI,EAAE,MAAG,SAAQ,IAAI,EAAE,KAAE,GAAE,KAAK,kBAAgBF,IAAE,KAAK,mBAAiBE,IAAE,KAAK,SAAOD;AAAA,UAAC;AAAC,iBAAO,OAAO,eAAeD,GAAE,WAAU,SAAQ,EAAC,KAAI,WAAU;AAAC,mBAAO,KAAK;AAAA,UAAM,GAAE,KAAI,SAASA,IAAE;AAAC,iBAAK,OAAO,eAAe,KAAKA,EAAC,MAAI,KAAK,SAAOA,IAAE,KAAK,OAAO,SAAS,KAAKA,EAAC;AAAA,UAAE,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAEA;AAAA,QAAC,EAAE,GAAE,IAAE,WAAU;AAAC,mBAASA,KAAG;AAAC,iBAAK,KAAG,UAAQC,GAAE,GAAE,KAAK,aAAW,oBAAI,OAAI,KAAK,UAAQ,oBAAI,OAAI,KAAK,QAAM,CAAC,GAAE,KAAK,SAAO,EAAC,oBAAmB,IAAI,EAAE,MAAG,cAAa,IAAI,EAAE,MAAG,uBAAsB,IAAI,EAAE,MAAG,iBAAgB,IAAI,EAAE,MAAG,iBAAgB,IAAI,EAAE,MAAG,WAAU,IAAI,EAAE,MAAG,oBAAmB,IAAI,EAAE,MAAG,cAAa,IAAI,EAAE,MAAG,QAAO,IAAI,EAAE,KAAE,GAAE,KAAK,QAAM,EAAC,MAAK,IAAI,EAAE,MAAG,MAAK,IAAI,EAAE,KAAE;AAAA,UAAC;AAAC,iBAAO,OAAO,eAAeD,GAAE,WAAU,mBAAkB,EAAC,KAAI,WAAU;AAAC,gBAAIA,KAAE,CAAC;AAAE,mBAAO,KAAK,WAAW,QAAS,SAASC,IAAEC,IAAE;AAAC,cAAAD,GAAE,YAAUD,GAAEE,EAAC,IAAED;AAAA,YAAE,CAAE,GAAED;AAAA,UAAC,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAE,OAAO,eAAeA,GAAE,WAAU,oBAAmB,EAAC,KAAI,WAAU;AAAC,gBAAIA,KAAE,CAAC;AAAE,mBAAO,KAAK,WAAW,QAAS,SAASC,IAAEC,IAAE;AAAC,cAAAD,GAAE,YAAUD,GAAEE,EAAC,IAAED;AAAA,YAAE,CAAE,GAAED;AAAA,UAAC,GAAE,YAAW,OAAG,cAAa,KAAE,CAAC,GAAEA,GAAE,UAAU,OAAK,SAASA,IAAE;AAAC,gBAAIC,KAAE;AAAK,iBAAK,KAAGD,GAAE,IAAG,KAAK,OAAKA,GAAE,MAAK,KAAK,QAAMA,GAAE,OAAMA,GAAE,QAAQ,QAAS,SAASA,IAAE;AAAC,kBAAIE,MAAG,GAAE,EAAE,QAAQF,IAAE,CAAC,GAAEG,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,cAAAD,GAAE,QAAQ,IAAIE,EAAC,MAAIF,GAAE,QAAQ,IAAIE,EAAC,EAAE,QAAMC;AAAA,YAAE,CAAE,GAAEJ,GAAE,WAAW,QAAS,SAASA,IAAE;AAAC,kBAAIE,MAAG,GAAE,EAAE,QAAQF,IAAE,CAAC,GAAEG,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,cAAAD,GAAE,WAAW,IAAIE,EAAC,KAAGF,GAAE,WAAW,IAAIE,EAAC,EAAE,KAAKC,EAAC;AAAA,YAAC,CAAE,GAAE,KAAK,MAAM,KAAK,QAAQJ,EAAC;AAAA,UAAC,GAAEA,GAAE,UAAU,OAAK,WAAU;AAAC,gBAAIA,KAAE,EAAC,MAAK,KAAK,MAAK,IAAG,KAAK,IAAG,MAAK,KAAK,MAAK,SAAQ,MAAM,KAAK,KAAK,QAAQ,QAAQ,CAAC,EAAE,IAAK,SAASA,IAAE;AAAC,kBAAIC,MAAG,GAAE,EAAE,QAAQD,IAAE,CAAC;AAAE,qBAAM,CAACC,GAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,KAAK;AAAA,YAAC,CAAE,GAAE,OAAM,KAAK,OAAM,YAAW,MAAM,KAAK,KAAK,WAAW,QAAQ,CAAC,EAAE,IAAK,SAASD,IAAE;AAAC,kBAAIC,MAAG,GAAE,EAAE,QAAQD,IAAE,CAAC;AAAE,qBAAM,CAACC,GAAE,CAAC,GAAEA,GAAE,CAAC,EAAE,KAAK,CAAC;AAAA,YAAC,CAAE,EAAC;AAAE,mBAAO,KAAK,MAAM,KAAK,QAAQD,EAAC;AAAA,UAAC,GAAEA,GAAE,UAAU,YAAU,SAASA,IAAE;AAAA,UAAC,GAAEA,GAAE,UAAU,oBAAkB,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAE;AAAK,gBAAG,WAASF,OAAIA,KAAE,OAAM,CAAC,KAAK,OAAO,mBAAmB,KAAK,EAAC,MAAKF,IAAE,SAAQ,MAAG,QAAOC,IAAE,cAAaC,GAAC,CAAC,GAAE;AAAC,kBAAIG,KAAE,KAAK,aAAa,MAAGL,IAAEC,EAAC;AAAE,qBAAOI,GAAE,OAAO,SAAS,YAAY,MAAM,WAAU;AAAC,uBAAOD,GAAE,OAAO,OAAO,KAAK,EAAC,MAAKJ,IAAE,WAAUK,GAAC,CAAC;AAAA,cAAC,CAAE,GAAEA,GAAE,QAAMH,IAAE,OAAO,QAAQC,MAAG,CAAC,CAAC,EAAE,QAAS,SAASH,IAAE;AAAC,oBAAIC,MAAG,GAAE,EAAE,QAAQD,IAAE,CAAC,GAAEE,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,gBAAAI,GAAEH,EAAC,IAAEC;AAAA,cAAC,CAAE,GAAE,KAAK,OAAO,aAAa,KAAKE,EAAC,GAAEA;AAAA,YAAC;AAAA,UAAC,GAAEL,GAAE,UAAU,qBAAmB,SAASA,IAAEC,IAAE;AAAC,gBAAG,CAAC,KAAK,OAAO,mBAAmB,KAAK,EAAC,MAAKD,IAAE,SAAQ,MAAE,CAAC,GAAE;AAAC,kBAAIE,KAAE,KAAK,aAAa,OAAGF,EAAC;AAAE,qBAAO,OAAO,QAAQC,MAAG,CAAC,CAAC,EAAE,QAAS,SAASD,IAAE;AAAC,oBAAIC,MAAG,GAAE,EAAE,QAAQD,IAAE,CAAC,GAAEG,KAAEF,GAAE,CAAC,GAAEG,KAAEH,GAAE,CAAC;AAAE,gBAAAC,GAAEC,EAAC,IAAEC;AAAA,cAAC,CAAE,GAAE,KAAK,OAAO,aAAa,KAAKF,EAAC,GAAEA;AAAA,YAAC;AAAA,UAAC,GAAEF,GAAE,UAAU,kBAAgB,SAASA,IAAE;AAAC,gBAAIC,KAAE,MAAKC,KAAE,KAAK,aAAaF,EAAC;AAAE,gBAAGE,IAAE;AAAC,kBAAG,KAAK,OAAO,sBAAsB,KAAKA,EAAC,EAAE;AAAO,kBAAGA,GAAE,kBAAgB,GAAE;AAAC,oBAAG,CAAC,KAAK,eAAe,OAAM,IAAI,MAAM,yFAAyF;AAAE,qBAAK,eAAe,YAAY,OAAQ,SAASF,IAAE;AAAC,yBAAOA,GAAE,SAAOE,MAAGF,GAAE,OAAKE;AAAA,gBAAC,CAAE,EAAE,QAAS,SAASF,IAAE;AAAC,kBAAAC,GAAE,eAAe,iBAAiBD,EAAC;AAAA,gBAAC,CAAE;AAAA,cAAC;AAAC,mBAAK,WAAW,OAAOA,EAAC,GAAE,KAAK,OAAO,gBAAgB,KAAKE,EAAC;AAAA,YAAC;AAAA,UAAC,GAAEF,GAAE,UAAU,YAAU,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAE;AAAK,gBAAG,WAASH,OAAIA,KAAE,OAAM,CAAC,KAAK,OAAO,gBAAgB,KAAK,EAAC,MAAKF,IAAE,WAAUC,IAAE,cAAaC,IAAE,kBAAiBC,GAAC,CAAC,GAAE;AAAC,kBAAIG,KAAE,IAAI,EAAEL,IAAEC,IAAEC,EAAC;AAAE,qBAAO,OAAO,QAAQC,MAAG,CAAC,CAAC,EAAE,QAAS,SAASJ,IAAE;AAAC,oBAAIC,MAAG,GAAE,EAAE,QAAQD,IAAE,CAAC,GAAEE,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,gBAAAK,GAAEJ,EAAC,IAAEC;AAAA,cAAC,CAAE,GAAEG,GAAE,OAAO,SAAS,YAAY,MAAM,WAAU;AAAC,gBAAAD,GAAE,OAAO,OAAO,KAAK,EAAC,MAAKL,IAAE,QAAOM,GAAC,CAAC;AAAA,cAAC,CAAE,GAAE,KAAK,QAAQ,IAAIN,IAAEM,EAAC,GAAE,KAAK,OAAO,UAAU,KAAK,EAAC,MAAKN,IAAE,QAAOM,GAAC,CAAC,GAAEA;AAAA,YAAC;AAAA,UAAC,GAAEN,GAAE,UAAU,eAAa,SAASA,IAAE;AAAC,gBAAG,KAAK,QAAQ,IAAIA,EAAC,GAAE;AAAC,kBAAIC,KAAE,KAAK,QAAQ,IAAID,EAAC;AAAE,kBAAG,KAAK,OAAO,mBAAmB,KAAK,EAAC,MAAKA,IAAE,QAAOC,GAAC,CAAC,EAAE;AAAO,mBAAK,QAAQ,OAAOD,EAAC,GAAE,KAAK,OAAO,aAAa,KAAK,EAAC,MAAKA,IAAE,QAAOC,GAAC,CAAC;AAAA,YAAC;AAAA,UAAC,GAAED,GAAE,UAAU,eAAa,SAASA,IAAE;AAAC,gBAAG,CAAC,KAAK,WAAW,IAAIA,EAAC,EAAE,OAAM,IAAI,MAAM,uBAAuB,OAAOA,IAAE,GAAG,CAAC;AAAE,mBAAO,KAAK,WAAW,IAAIA,EAAC;AAAA,UAAC,GAAEA,GAAE,UAAU,iBAAe,SAASA,IAAE;AAAC,gBAAG,CAAC,KAAK,QAAQ,IAAIA,EAAC,EAAE,OAAM,IAAI,MAAM,oBAAoB,OAAOA,IAAE,GAAG,CAAC;AAAE,mBAAO,KAAK,QAAQ,IAAIA,EAAC,EAAE;AAAA,UAAK,GAAEA,GAAE,UAAU,iBAAe,SAASA,IAAEC,IAAE;AAAC,gBAAG,CAAC,KAAK,QAAQ,IAAID,EAAC,EAAE,OAAM,IAAI,MAAM,oBAAoB,OAAOA,IAAE,GAAG,CAAC;AAAE,iBAAK,QAAQ,IAAIA,EAAC,EAAE,QAAMC;AAAA,UAAC,GAAED,GAAE,UAAU,iBAAe,SAASA,IAAE;AAAC,iBAAK,iBAAeA;AAAA,UAAC,GAAEA,GAAE,UAAU,eAAa,SAASA,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAE,IAAI,EAAE,MAAKH,EAAC;AAAE,mBAAOG,GAAE,SAAOD,IAAE,KAAK,WAAW,IAAID,IAAEE,EAAC,GAAEA;AAAA,UAAC,GAAEH;AAAA,QAAC,EAAE;AAAE,iBAAS,EAAEA,IAAE;AAAC,iBAAM,cAAY,OAAOA,KAAEA,GAAE,IAAEA;AAAA,QAAC;AAAC,YAAI,IAAE,WAAU;AAAC,mBAASA,GAAEA,IAAEC,IAAE;AAAC,iBAAK,OAAK,IAAG,KAAK,OAAK,IAAG,KAAK,QAAM,CAAC,GAAE,KAAK,UAAQ,oBAAI,OAAI,KAAK,OAAKD,IAAE,KAAK,OAAKA,IAAE,KAAK,uBAAqBC;AAAA,UAAC;AAAC,iBAAOD,GAAE,UAAU,QAAM,WAAU;AAAC,mBAAOA,KAAE,KAAK,MAAKC,KAAE,KAAK,MAAKC,KAAE,KAAK,sBAAqBC,KAAE,KAAK,OAAMC,KAAE,KAAK,SAAQC,KAAE,KAAK,cAAa,SAASC,IAAE;AAAC,uBAASE,KAAG;AAAC,oBAAIH,IAAEG,IAAEC,KAAEH,GAAE,KAAK,IAAI,KAAG;AAAK,gBAAAG,GAAE,OAAKT,IAAES,GAAE,OAAKR,IAAEC,MAAG,OAAO,OAAOO,IAAEP,EAAC;AAAE,oBAAG;AAAC,2BAAQQ,MAAG,GAAE,EAAE,UAAUP,EAAC,GAAEQ,KAAED,GAAE,KAAK,GAAE,CAACC,GAAE,MAAKA,KAAED,GAAE,KAAK,GAAE;AAAC,wBAAIG,KAAEF,GAAE;AAAM,oBAAAE,GAAE,UAAQJ,GAAE,kBAAkBI,GAAE,MAAKA,GAAE,QAAO,EAAEA,GAAE,YAAY,GAAEA,GAAE,oBAAoB,IAAEJ,GAAE,mBAAmBI,GAAE,MAAKA,GAAE,oBAAoB;AAAA,kBAAC;AAAA,gBAAC,SAAOb,IAAE;AAAC,kBAAAK,KAAE,EAAC,OAAML,GAAC;AAAA,gBAAC,UAAC;AAAQ,sBAAG;AAAC,oBAAAW,MAAG,CAACA,GAAE,SAAOH,KAAEE,GAAE,WAASF,GAAE,KAAKE,EAAC;AAAA,kBAAC,UAAC;AAAQ,wBAAGL,GAAE,OAAMA,GAAE;AAAA,kBAAK;AAAA,gBAAC;AAAC,uBAAO,MAAM,KAAKD,GAAE,QAAQ,CAAC,EAAE,QAAS,SAASJ,IAAE;AAAC,sBAAIC,MAAG,GAAE,EAAE,QAAQD,IAAE,CAAC,GAAEE,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,kBAAAQ,GAAE,UAAUP,IAAEC,GAAE,iBAAgB,EAAEA,GAAE,KAAK,GAAEA,GAAE,kBAAiBA,GAAE,oBAAoB;AAAA,gBAAC,CAAE,GAAEM;AAAA,cAAC;AAAC,sBAAO,GAAE,EAAE,WAAWD,IAAEF,EAAC,GAAEE,GAAE,UAAU,YAAU,SAASR,IAAE;AAAC,oBAAGK,GAAE,QAAOA,GAAE,KAAK,MAAK,MAAKL,EAAC;AAAA,cAAC,GAAEQ;AAAA,YAAC,EAAE,CAAC;AAAE,gBAAIR,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC;AAAA,UAAC,GAAEL,GAAE,UAAU,UAAQ,SAASA,IAAE;AAAC,mBAAO,KAAK,OAAKA,IAAE;AAAA,UAAI,GAAEA,GAAE,UAAU,oBAAkB,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAO,WAASD,OAAIA,KAAE,OAAM,KAAK,kBAAkBA,EAAC,GAAE,KAAK,MAAM,KAAK,EAAC,SAAQ,MAAG,MAAKF,IAAE,QAAOC,IAAE,cAAaC,IAAE,sBAAqBC,GAAC,CAAC,GAAE;AAAA,UAAI,GAAEH,GAAE,UAAU,qBAAmB,SAASA,IAAEC,IAAE;AAAC,mBAAO,KAAK,MAAM,KAAK,EAAC,SAAQ,OAAG,MAAKD,IAAE,sBAAqBC,GAAC,CAAC,GAAE;AAAA,UAAI,GAAED,GAAE,UAAU,YAAU,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAO,WAASF,OAAIA,KAAE,OAAM,KAAK,kBAAkBA,EAAC,GAAE,KAAK,QAAQ,IAAIF,IAAE,EAAC,OAAME,IAAE,iBAAgBD,IAAE,kBAAiBE,IAAE,sBAAqBC,GAAC,CAAC,GAAE;AAAA,UAAI,GAAEJ,GAAE,UAAU,cAAY,SAASA,IAAE;AAAC,mBAAO,KAAK,eAAaA,IAAE;AAAA,UAAI,GAAEA,GAAE,UAAU,oBAAkB,SAASA,IAAE;AAAC,gBAAG,YAAU,OAAOA,MAAG,SAAOA,GAAE,OAAM,IAAI,MAAM,uFAAuF;AAAA,UAAC,GAAEA;AAAA,QAAC,EAAE;AAAA,MAAC,GAAG,GAAE,QAAQ,aAAW,EAAE,YAAW,QAAQ,kBAAgB,EAAE,iBAAgB,QAAQ,SAAO,EAAE,QAAO,QAAQ,OAAK,EAAE,MAAK,QAAQ,cAAY,EAAE,aAAY,QAAQ,gBAAc,EAAE,eAAc,QAAQ,aAAW,EAAE,YAAW,OAAO,eAAe,SAAQ,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,IAAC,GAAG;AAAA;AAAA;", "names": ["e", "t", "n", "o", "r", "i", "a", "s", "c", "u", "f", "p", "l", "d"]}
'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-04-23 22:23:09
LastEditors: zhai
LastEditTime: 2025-07-21 18:10:09
'''
from enum import Enum
from pydantic import BaseModel, ConfigDict, Field, constr, conlist, model_validator

from datetime import datetime, date
from typing import Any, Dict, List, Optional
from api.v1.fishery.device.schema import Ref_DeviceDTO
from fastapi_crud_tortoise import MODEL_ID_TYPE


class RecordType(str, Enum):
    """记录类型枚举"""
    ElecPeriod = "ElecPeriod"
    GearPeriodFusion = "GearPeriodFusion"
    GearPulses = "GearPulses"
    GearPeriod = "GearPeriod"
    GearPhysFusion = "GearPhysFusion"
    ElecSignal = "ElecSignal"
    GearPhys = "GearPhys"
    Thermals = "Thermals"

    
class RecordQueryParams(BaseModel):
    """SSE记录查询参数"""
    record_type: RecordType = Field(..., description="记录类型")
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")
    
    @model_validator(mode='before')
    def validate_time_range(cls, values):
        """验证时间范围"""
        if values.get('start_time') > values.get('end_time'):
            raise ValueError('开始时间不能大于结束时间')
        return values


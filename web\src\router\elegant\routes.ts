/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'about',
    path: '/about',
    component: 'layout.base$view.about',
    meta: {
      title: 'about',
      i18nKey: 'route.about',
      icon: 'fluent:book-information-24-regular',
      order: 10
    }
  },
  {
    name: 'alova',
    path: '/alova',
    component: 'layout.base',
    meta: {
      title: 'alova',
      i18nKey: 'route.alova',
      icon: 'carbon:http',
      order: 7
    },
    children: [
      {
        name: 'alova_request',
        path: '/alova/request',
        component: 'view.alova_request',
        meta: {
          title: 'alova_request',
          i18nKey: 'route.alova_request',
          order: 1
        }
      },
      {
        name: 'alova_scenes',
        path: '/alova/scenes',
        component: 'view.alova_scenes',
        meta: {
          title: 'alova_scenes',
          i18nKey: 'route.alova_scenes',
          icon: 'cbi:scene-dynamic',
          order: 3
        }
      },
      {
        name: 'alova_user',
        path: '/alova/user',
        component: 'view.alova_user',
        meta: {
          title: 'alova_user',
          i18nKey: 'route.alova_user',
          icon: 'carbon:user-multiple',
          order: 2
        }
      }
    ]
  },
  {
    name: 'demo-route',
    path: '/demo-route',
    component: 'layout.base',
    meta: {
      title: 'demo-route',
      i18nKey: 'route.demo-route'
    },
    children: [
      {
        name: 'demo-route_child',
        path: '/demo-route/child',
        component: 'view.demo-route_child',
        meta: {
          title: 'demo-route_child',
          i18nKey: 'route.demo-route_child'
        }
      }
    ]
  },
  {
    name: 'device',
    path: '/device',
    component: 'layout.base',
    meta: {
      title: 'device',
      i18nKey: 'route.device'
    },
    children: [
      {
        name: 'device_blocks',
        path: '/device/blocks',
        component: 'view.device_blocks',
        meta: {
          title: 'device_blocks',
          i18nKey: 'route.device_blocks'
        }
      },
      {
        name: 'device_flow',
        path: '/device/flow',
        component: 'view.device_flow',
        meta: {
          title: 'device_flow',
          i18nKey: 'route.device_flow'
        }
      },
      {
        name: 'device_record',
        path: '/device/record',
        component: 'view.device_record',
        meta: {
          title: 'device_record',
          i18nKey: 'route.device_record'
        }
      },
      {
        name: 'device_remote',
        path: '/device/remote',
        component: 'view.device_remote',
        meta: {
          title: 'device_remote',
          i18nKey: 'route.device_remote'
        }
      },
      {
        name: 'device_review',
        path: '/device/review',
        component: 'view.device_review',
        meta: {
          title: 'device_review',
          i18nKey: 'route.device_review'
        }
      },
      {
        name: 'device_terminals',
        path: '/device/terminals',
        component: 'view.device_terminals',
        meta: {
          title: 'device_terminals',
          i18nKey: 'route.device_terminals'
        }
      }
    ]
  },
  {
    name: 'dummy',
    path: '/dummy',
    component: 'layout.base',
    meta: {
      title: 'dummy',
      i18nKey: 'route.dummy'
    },
    children: [
      {
        name: 'dummy_department',
        path: '/dummy/department',
        component: 'view.dummy_department',
        meta: {
          title: 'dummy_department',
          i18nKey: 'route.dummy_department'
        }
      },
      {
        name: 'dummy_dummy',
        path: '/dummy/dummy',
        component: 'view.dummy_dummy',
        meta: {
          title: 'dummy_dummy',
          i18nKey: 'route.dummy_dummy'
        }
      }
    ]
  },
  {
    name: 'function',
    path: '/function',
    component: 'layout.base',
    meta: {
      title: 'function',
      i18nKey: 'route.function',
      icon: 'icon-park-outline:all-application',
      order: 6
    },
    children: [
      {
        name: 'function_hide-child',
        path: '/function/hide-child',
        meta: {
          title: 'function_hide-child',
          i18nKey: 'route.function_hide-child',
          icon: 'material-symbols:filter-list-off',
          order: 2
        },
        redirect: '/function/hide-child/one',
        children: [
          {
            name: 'function_hide-child_one',
            path: '/function/hide-child/one',
            component: 'view.function_hide-child_one',
            meta: {
              title: 'function_hide-child_one',
              i18nKey: 'route.function_hide-child_one',
              icon: 'material-symbols:filter-list-off',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          },
          {
            name: 'function_hide-child_three',
            path: '/function/hide-child/three',
            component: 'view.function_hide-child_three',
            meta: {
              title: 'function_hide-child_three',
              i18nKey: 'route.function_hide-child_three',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          },
          {
            name: 'function_hide-child_two',
            path: '/function/hide-child/two',
            component: 'view.function_hide-child_two',
            meta: {
              title: 'function_hide-child_two',
              i18nKey: 'route.function_hide-child_two',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          }
        ]
      },
      {
        name: 'function_multi-tab',
        path: '/function/multi-tab',
        component: 'view.function_multi-tab',
        meta: {
          title: 'function_multi-tab',
          i18nKey: 'route.function_multi-tab',
          icon: 'ic:round-tab',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'function_tab'
        }
      },
      {
        name: 'function_request',
        path: '/function/request',
        component: 'view.function_request',
        meta: {
          title: 'function_request',
          i18nKey: 'route.function_request',
          icon: 'carbon:network-overlay',
          order: 3
        }
      },
      {
        name: 'function_super-page',
        path: '/function/super-page',
        component: 'view.function_super-page',
        meta: {
          title: 'function_super-page',
          i18nKey: 'route.function_super-page',
          icon: 'ic:round-supervisor-account',
          order: 5,
          roles: ['R_SUPER']
        }
      },
      {
        name: 'function_tab',
        path: '/function/tab',
        component: 'view.function_tab',
        meta: {
          title: 'function_tab',
          i18nKey: 'route.function_tab',
          icon: 'ic:round-tab',
          order: 1
        }
      },
      {
        name: 'function_toggle-auth',
        path: '/function/toggle-auth',
        component: 'view.function_toggle-auth',
        meta: {
          title: 'function_toggle-auth',
          i18nKey: 'route.function_toggle-auth',
          icon: 'ic:round-construction',
          order: 4
        }
      }
    ]
  },
  {
    name: 'history-data',
    path: '/history-data',
    component: 'layout.base',
    meta: {
      title: 'history-data',
      i18nKey: 'route.history-data'
    },
    children: [
      {
        name: 'history-data_electrical',
        path: '/history-data/electrical',
        component: 'view.history-data_electrical',
        meta: {
          title: 'history-data_electrical',
          i18nKey: 'route.history-data_electrical'
        }
      },
      {
        name: 'history-data_steam',
        path: '/history-data/steam',
        component: 'view.history-data_steam',
        meta: {
          title: 'history-data_steam',
          i18nKey: 'route.history-data_steam'
        }
      },
      {
        name: 'history-data_torsion',
        path: '/history-data/torsion',
        component: 'view.history-data_torsion',
        meta: {
          title: 'history-data_torsion',
          i18nKey: 'route.history-data_torsion'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home'
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage',
      icon: 'carbon:cloud-service-management',
      order: 9,
      roles: ['R_ADMIN']
    },
    children: [
      {
        name: 'manage_api',
        path: '/manage/api',
        component: 'view.manage_api',
        meta: {
          title: 'manage_api',
          i18nKey: 'route.manage_api'
        }
      },
      {
        name: 'manage_log',
        path: '/manage/log',
        component: 'view.manage_log',
        meta: {
          title: 'manage_log',
          i18nKey: 'route.manage_log'
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: 'manage_menu',
          i18nKey: 'route.manage_menu',
          icon: 'material-symbols:route',
          order: 3,
          roles: ['R_ADMIN'],
          keepAlive: true
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role',
          icon: 'carbon:user-role',
          order: 2,
          roles: ['R_SUPER']
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user',
          icon: 'ic:round-manage-accounts',
          order: 1,
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        props: true,
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail',
          hideInMenu: true,
          roles: ['R_ADMIN'],
          activeMenu: 'manage_user'
        }
      }
    ]
  },
  {
    name: 'multi-menu',
    path: '/multi-menu',
    component: 'layout.base',
    meta: {
      title: 'multi-menu',
      i18nKey: 'route.multi-menu',
      order: 8
    },
    children: [
      {
        name: 'multi-menu_first',
        path: '/multi-menu/first',
        meta: {
          title: 'multi-menu_first',
          i18nKey: 'route.multi-menu_first',
          order: 1
        },
        children: [
          {
            name: 'multi-menu_first_child',
            path: '/multi-menu/first/child',
            component: 'view.multi-menu_first_child',
            meta: {
              title: 'multi-menu_first_child',
              i18nKey: 'route.multi-menu_first_child'
            }
          }
        ]
      },
      {
        name: 'multi-menu_second',
        path: '/multi-menu/second',
        meta: {
          title: 'multi-menu_second',
          i18nKey: 'route.multi-menu_second',
          order: 2
        },
        children: [
          {
            name: 'multi-menu_second_child',
            path: '/multi-menu/second/child',
            meta: {
              title: 'multi-menu_second_child',
              i18nKey: 'route.multi-menu_second_child'
            },
            children: [
              {
                name: 'multi-menu_second_child_home',
                path: '/multi-menu/second/child/home',
                component: 'view.multi-menu_second_child_home',
                meta: {
                  title: 'multi-menu_second_child_home',
                  i18nKey: 'route.multi-menu_second_child_home'
                }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    name: 'online-monitor',
    path: '/online-monitor',
    component: 'layout.base',
    meta: {
      title: 'online-monitor',
      i18nKey: 'route.online-monitor'
    },
    children: [
      {
        name: 'online-monitor_electrical',
        path: '/online-monitor/electrical',
        component: 'view.online-monitor_electrical',
        meta: {
          title: 'online-monitor_electrical',
          i18nKey: 'route.online-monitor_electrical'
        }
      },
      {
        name: 'online-monitor_main',
        path: '/online-monitor/main',
        component: 'view.online-monitor_main',
        meta: {
          title: 'online-monitor_main',
          i18nKey: 'route.online-monitor_main'
        }
      },
      {
        name: 'online-monitor_steam',
        path: '/online-monitor/steam',
        component: 'view.online-monitor_steam',
        meta: {
          title: 'online-monitor_steam',
          i18nKey: 'route.online-monitor_steam'
        }
      },
      {
        name: 'online-monitor_torsion',
        path: '/online-monitor/torsion',
        component: 'view.online-monitor_torsion',
        meta: {
          title: 'online-monitor_torsion',
          i18nKey: 'route.online-monitor_torsion'
        }
      }
    ]
  },
  {
    name: 'plugin',
    path: '/plugin',
    component: 'layout.base',
    meta: {
      title: '插件示例',
      i18nKey: 'route.plugin',
      order: 7,
      icon: 'clarity:plugin-line'
    },
    children: [
      {
        name: 'plugin_barcode',
        path: '/plugin/barcode',
        component: 'view.plugin_barcode',
        meta: {
          title: 'plugin_barcode',
          i18nKey: 'route.plugin_barcode',
          icon: 'ic:round-barcode'
        }
      },
      {
        name: 'plugin_charts',
        path: '/plugin/charts',
        meta: {
          title: 'plugin_charts',
          i18nKey: 'route.plugin_charts',
          icon: 'mdi:chart-areaspline'
        },
        children: [
          {
            name: 'plugin_charts_antv',
            path: '/plugin/charts/antv',
            component: 'view.plugin_charts_antv',
            meta: {
              title: 'plugin_charts_antv',
              i18nKey: 'route.plugin_charts_antv',
              icon: 'hugeicons:flow-square'
            }
          },
          {
            name: 'plugin_charts_echarts',
            path: '/plugin/charts/echarts',
            component: 'view.plugin_charts_echarts',
            meta: {
              title: 'plugin_charts_echarts',
              i18nKey: 'route.plugin_charts_echarts',
              icon: 'simple-icons:apacheecharts'
            }
          },
          {
            name: 'plugin_charts_vchart',
            path: '/plugin/charts/vchart',
            component: 'view.plugin_charts_vchart',
            meta: {
              title: 'plugin_charts_vchart',
              i18nKey: 'route.plugin_charts_vchart',
              localIcon: 'visactor'
            }
          }
        ]
      },
      {
        name: 'plugin_copy',
        path: '/plugin/copy',
        component: 'view.plugin_copy',
        meta: {
          title: 'plugin_copy',
          i18nKey: 'route.plugin_copy',
          icon: 'mdi:clipboard-outline'
        }
      },
      {
        name: 'plugin_editor',
        path: '/plugin/editor',
        meta: {
          title: 'plugin_editor',
          i18nKey: 'route.plugin_editor',
          icon: 'icon-park-outline:editor'
        },
        children: [
          {
            name: 'plugin_editor_markdown',
            path: '/plugin/editor/markdown',
            component: 'view.plugin_editor_markdown',
            meta: {
              title: 'plugin_editor_markdown',
              i18nKey: 'route.plugin_editor_markdown',
              icon: 'ri:markdown-line'
            }
          },
          {
            name: 'plugin_editor_quill',
            path: '/plugin/editor/quill',
            component: 'view.plugin_editor_quill',
            meta: {
              title: 'plugin_editor_quill',
              i18nKey: 'route.plugin_editor_quill',
              icon: 'mdi:file-document-edit-outline'
            }
          }
        ]
      },
      {
        name: 'plugin_excel',
        path: '/plugin/excel',
        component: 'view.plugin_excel',
        meta: {
          title: 'plugin_excel',
          i18nKey: 'route.plugin_excel',
          icon: 'ri:file-excel-2-line',
          keepAlive: true
        }
      },
      {
        name: 'plugin_gantt',
        path: '/plugin/gantt',
        meta: {
          title: 'plugin_gantt',
          i18nKey: 'route.plugin_gantt',
          icon: 'ant-design:bar-chart-outlined'
        },
        children: [
          {
            name: 'plugin_gantt_dhtmlx',
            path: '/plugin/gantt/dhtmlx',
            component: 'view.plugin_gantt_dhtmlx',
            meta: {
              title: 'plugin_gantt_dhtmlx',
              i18nKey: 'route.plugin_gantt_dhtmlx'
            }
          },
          {
            name: 'plugin_gantt_vtable',
            path: '/plugin/gantt/vtable',
            component: 'view.plugin_gantt_vtable',
            meta: {
              title: 'plugin_gantt_vtable',
              i18nKey: 'route.plugin_gantt_vtable',
              localIcon: 'visactor'
            }
          }
        ]
      },
      {
        name: 'plugin_icon',
        path: '/plugin/icon',
        component: 'view.plugin_icon',
        meta: {
          title: 'plugin_icon',
          i18nKey: 'route.plugin_icon',
          localIcon: 'custom-icon'
        }
      },
      {
        name: 'plugin_map',
        path: '/plugin/map',
        component: 'view.plugin_map',
        meta: {
          title: 'plugin_map',
          i18nKey: 'route.plugin_map',
          icon: 'mdi:map'
        }
      },
      {
        name: 'plugin_pdf',
        path: '/plugin/pdf',
        component: 'view.plugin_pdf',
        meta: {
          title: 'plugin_pdf',
          i18nKey: 'route.plugin_pdf',
          icon: 'uiw:file-pdf'
        }
      },
      {
        name: 'plugin_pinyin',
        path: '/plugin/pinyin',
        component: 'view.plugin_pinyin',
        meta: {
          title: 'plugin_pinyin',
          i18nKey: 'route.plugin_pinyin',
          icon: 'entypo-social:google-hangouts'
        }
      },
      {
        name: 'plugin_print',
        path: '/plugin/print',
        component: 'view.plugin_print',
        meta: {
          title: 'plugin_print',
          i18nKey: 'route.plugin_print',
          icon: 'mdi:printer'
        }
      },
      {
        name: 'plugin_swiper',
        path: '/plugin/swiper',
        component: 'view.plugin_swiper',
        meta: {
          title: 'plugin_swiper',
          i18nKey: 'route.plugin_swiper',
          icon: 'simple-icons:swiper'
        }
      },
      {
        name: 'plugin_tables',
        path: '/plugin/tables',
        meta: {
          title: 'plugin_tables',
          i18nKey: 'route.plugin_tables',
          icon: 'icon-park-outline:table'
        },
        children: [
          {
            name: 'plugin_tables_vtable',
            path: '/plugin/tables/vtable',
            component: 'view.plugin_tables_vtable',
            meta: {
              title: 'plugin_tables_vtable',
              i18nKey: 'route.plugin_tables_vtable',
              localIcon: 'visactor'
            }
          }
        ]
      },
      {
        name: 'plugin_typeit',
        path: '/plugin/typeit',
        component: 'view.plugin_typeit',
        meta: {
          title: 'plugin_typeit',
          i18nKey: 'route.plugin_typeit',
          icon: 'mdi:typewriter'
        }
      },
      {
        name: 'plugin_video',
        path: '/plugin/video',
        component: 'view.plugin_video',
        meta: {
          title: 'plugin_video',
          i18nKey: 'route.plugin_video',
          icon: 'mdi:video'
        }
      }
    ]
  },
  {
    name: 'task',
    path: '/task',
    component: 'layout.base',
    meta: {
      title: 'task',
      i18nKey: 'route.task'
    },
    children: [
      {
        name: 'task_log',
        path: '/task/log',
        component: 'view.task_log',
        meta: {
          title: 'task_log',
          i18nKey: 'route.task_log'
        }
      },
      {
        name: 'task_scheduler',
        path: '/task/scheduler',
        component: 'view.task_scheduler',
        meta: {
          title: 'task_scheduler',
          i18nKey: 'route.task_scheduler'
        }
      }
    ]
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      hideInMenu: true
    }
  }
];

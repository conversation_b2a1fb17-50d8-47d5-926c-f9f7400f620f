###
 # @Descripttion: 
 # @version: 0.x
 # @Author: zhai
 # @Date: 2025-02-24 19:24:15
 # @LastEditors: zhai
 # @LastEditTime: 2025-05-03 23:53:07
### 
# backend service base url, prod environment
VITE_SERVICE_BASE_URL=/api/v1

# other backend service base url, prod environment
VITE_OTHER_SERVICE_BASE_URL= `{
  "mqtt": "/api/mqtt",
  "demo": "http://localhost:9529"
}`


VITE_MQTT_SERVER= `{
"mqtt_url": "ws://127.0.0.1:8083",
"mqtt_username": "webclient",
"mqtt_password": "w1234@*8K",
"mqtt_api": "/api/mqtt",
"mqtt_api_username": "2bda782b54c1bc97",
"mqtt_api_password": "9AzsTcvZhtaySEtE0XXjcJPOAwHndJ9A5qtrgn8YN7vuL"
}`

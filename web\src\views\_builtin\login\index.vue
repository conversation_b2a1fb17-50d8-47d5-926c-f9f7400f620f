<script setup lang="ts">
import { computed } from 'vue';
import type { Component } from 'vue';
import { getPaletteColorByNumber, mixColor } from '@sa/color';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { loginModuleRecord } from '@/constants/app';
import PwdLogin from './modules/pwd-login.vue';
import CodeLogin from './modules/code-login.vue';
import Register from './modules/register.vue';
import ResetPwd from './modules/reset-pwd.vue';
import BindWechat from './modules/bind-wechat.vue';

import loginMain from '@/assets/svg-icon/login-main.svg';
import loginBg from '@/assets/svg-icon/login-bg.svg';
import wave from './modules/wave.vue';

interface Props {
  /** The login module */
  module?: UnionKey.LoginModule;
}

const props = defineProps<Props>();

const appStore = useAppStore();
const themeStore = useThemeStore();

interface LoginModule {
  label: string;
  component: Component;
}

const moduleMap: Record<UnionKey.LoginModule, LoginModule> = {
  'pwd-login': { label: loginModuleRecord['pwd-login'], component: PwdLogin },
  'code-login': { label: loginModuleRecord['code-login'], component: CodeLogin },
  register: { label: loginModuleRecord.register, component: Register },
  'reset-pwd': { label: loginModuleRecord['reset-pwd'], component: ResetPwd },
  'bind-wechat': { label: loginModuleRecord['bind-wechat'], component: BindWechat }
};

const activeModule = computed(() => moduleMap[props.module || 'pwd-login']);

const bgThemeColor = computed(() =>
  themeStore.darkMode ? getPaletteColorByNumber(themeStore.themeColor, 600) : themeStore.themeColor
);

const bgColor = computed(() => {
  const COLOR_WHITE = '#ffffff';
  const ratio = themeStore.darkMode ? 0.5 : 0.2;
  return mixColor(COLOR_WHITE, themeStore.themeColor, ratio);
});

</script>

<template>
  <div class="relative size-full flex-center overflow-hidden" :style="{ backgroundColor: bgThemeColor }">
    <!-- <WaveBg :theme-color="bgThemeColor" /> -->

    <div class="login-left">
      <div class="login-left-img">
        <img :src="loginMain" />
      </div>
      <!-- <img :src="loginBg" class="login-left-waves" /> -->
    </div>


    <NCard :bordered="false" class="relative z-4 w-auto rd-12px mr-160px">
      <div class="w-400px lt-sm:w-300px">
        <header class="flex-y-center justify-between">
          <SystemLogo class="text-64px text-primary lt-sm:text-48px" />
          <h3 class="text-28px text-primary font-500 lt-sm:text-22px">{{ $t('system.title') }}</h3>
          <div class="i-flex-col">
            <ThemeSchemaSwitch :theme-schema="themeStore.themeScheme" :show-tooltip="false"
              class="text-20px lt-sm:text-18px" @switch="themeStore.toggleThemeScheme" />
            <LangSwitch :lang="appStore.locale" :lang-options="appStore.localeOptions" :show-tooltip="false"
              @change-lang="appStore.changeLocale" />
          </div>
        </header>
        <main class="pt-24px">
          <h3 class="text-18px text-primary font-medium">{{ $t(activeModule.label) }}</h3>
          <div class="pt-24px">
            <Transition :name="themeStore.page.animateMode" mode="out-in" appear>
              <component :is="activeModule.component" />
            </Transition>
          </div>
        </main>
      </div>
    </NCard>
    <div class="absolute bottom-0 left-0 w-full h-10%">
      <wave />
    </div>

  </div>
</template>

<style scoped lang="scss">
.login-left {
  flex: 1;
  position: relative;
  background-color: v-bind(bgThemeColor);
  margin-right: 100px;
  height: 100%;

  .login-left-logo {
    display: flex;
    align-items: center;
    position: absolute;
    top: 50px;
    left: 80px;
    z-index: 1;
    animation: logoAnimation 0.3s ease;

    img {
      width: 52px;
      height: 52px;
    }

    .login-left-logo-text {
      display: flex;
      flex-direction: column;

      span {
        margin-left: 10px;
        font-size: 28px;
        color: #26a59a;
      }

      .login-left-logo-text-msg {
        font-size: 12px;
        color: #32a99e;
      }
    }
  }

  .login-left-img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 52%;

    img {
      width: 100%;
      height: 100%;
      animation: error-num 0.6s ease;
    }
  }

  .login-left-waves {
    position: absolute;
    top: 0;
    right: -100px;
  }
}
</style>

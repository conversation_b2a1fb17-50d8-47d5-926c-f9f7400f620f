/*
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-02-24 21:23:58
 * @LastEditors: zhai
 * @LastEditTime: 2025-04-26 09:30:42
 */

import { useMQTT } from 'mqtt-vue-hook';
import { decode, encode } from '@msgpack/msgpack';
import type { App } from 'vue';
import mqttVueHook from 'mqtt-vue-hook';
import { CardInfo, useIotStore } from '@/store/iot';
import { fast_terminals_api } from '@/service/api/terminals';
import { mqttRequest } from '@/service/request';
import { getMqttClients } from '@/service/api/mqttsvr';
import { useEventStore } from '@/store/event';


const mqttHook = useMQTT();

const online_topic = "$SYS/brokers/+/clients/+/connected"
const offline_topic = "$SYS/brokers/+/clients/+/disconnected"


import json5 from 'json5';
import { hostname } from 'node:os';
const VITE_MQTT_SERVER = import.meta.env.VITE_MQTT_SERVER;
const mqtt_server = json5.parse(VITE_MQTT_SERVER);


export function setupMqtt(app: App) {
  const iotStore = useIotStore();
  iotStore.mqtt.id = `web_${Math.random().toString(16).substring(2, 10)}`;
  const host = mqtt_server.mqtt_url;
  iotStore.mqtt.host = host;


  app.use(mqttVueHook, `${host}/mqtt`, {
    clean: false,
    keepalive: 60,
    clientId: iotStore.mqtt.id,
    username: mqtt_server.mqtt_username,
    password: mqtt_server.mqtt_password,
    connectTimeout: 3000
  });
}

export const updateCardInfo = (
  {
    client_id: client_id,                  	// 必填字段
    id = undefined,          	// 可选字段默认undefined（不传则不更新）
    name = undefined,    		// 可选字段默认undefined（不传则不更新）
    connected = undefined,
    started = undefined,
    seconds = undefined,
    ip = undefined,
    sample_rate = undefined,
    channels = undefined,
    recorder = undefined,
  }: CardInfo
) => {

  const iotStore = useIotStore();
  let card = iotStore.card_map.get(client_id);

  if (card) {
    if (id != undefined)
      card.id = id;
    if (connected != undefined)
      card.connected = connected;
    if (name != undefined)
      card.name = name;
    if (started != undefined)
      card.started = started;
    if (seconds != undefined)
      card.seconds = seconds;
    if (ip != undefined)
      card.ip = ip;
    if (sample_rate != undefined)
      card.sample_rate = sample_rate;
    if (channels != undefined)
      card.channels = channels;
    if (recorder != undefined)
      card.recorder = recorder;
  }
  else if (client_id.indexOf("AdLink-") == 0) {
    card = {
      client_id: client_id,
      id: id,
      connected: !!connected,
      name: name || "undefine",
      started: started || false,
      seconds: seconds || 0,
      ip: ip || "",
      sample_rate: sample_rate || 2000,
      channels: channels,
      recorder: recorder,
    }

    // 目前显示所有设备
    iotStore.card_map.set(client_id, card);

    if (name == undefined) {
      // 通过clientid查询设备名称
      fast_terminals_api.get_one_by_filter({ "client_id": client_id })
        .then(res => {
          if (res.data.data) {
            card.name = res.data.data.name;
          }
        })
    }
  }

  return card;
}

// 登录后执行
export const initMqttClient = () => {
  const iotStore = useIotStore();
  mqttHook.subscribe(['TVS/#','terminal/#' ,online_topic, offline_topic], 1, {}, () => {
    console.log('TVS-subscribed!');
  });

  // // MQTT 模式保留（不变）
  // mqttHook.registerEvent(
  //   'TVS/data/#',
  //   (topic, message) => {
  //     console.log(topic,message)
  //   }
  // )

  mqttHook.registerEvent(
    // mqtt status: on-connect, on-reconnect, on-disconnect, on-connect-fail
    'on-connect',
    (topic, message) => {
      console.log('mqtt connected');
    },
    'string_key'
  );


  // mqttHook.registerEvent(
  //   'terminal/beat',
  //   (topic, message) => {
  //     console.log(topic, ': beat!');
  //     // const info = decode(message);

  //     let str_msg = message.toString();
  //     let json_msg = JSON.parse(str_msg);
  //     updateCardInfo(json_msg);

  //     const eventStore = useEventStore();
  //     eventStore.emit('terminal/beat', json_msg);
  //   },
  //   'string_key'
  // );

  mqttHook.registerEvent(
    online_topic,
    (topic, message) => {
      let str_msg = message.toString();
      let json_msg = JSON.parse(str_msg);
      console.log('mqtt dconnect:' + topic + " - " + json_msg.clientid);

      updateCardInfo({
        client_id: json_msg.clientid, connected: true, name: undefined, ip: json_msg.ipaddress
      })
    },
    'string_key'
  );


  mqttHook.registerEvent(
    offline_topic,
    (topic, message) => {
      let str_msg = message.toString();
      let json_msg = JSON.parse(str_msg);
      console.log('mqtt disconnect:' + topic + " - " + json_msg.clientid);

      updateCardInfo({
        client_id: json_msg.clientid,
        connected: false, name: undefined, ip: json_msg.ipaddress
      })
    },
    'string_key'
  );

  // beat
  const info = {
    name: 'web',
    id: iotStore.mqtt.id,
    on: true
  };


  // 定时检查在线状态
  // setInterval(() => {
  //   mqttHook.publish('web/beat', encode(info), 1, {}, () => {
  //     console.log('web/beat...');
  //   });
  // }, 3000); // 每隔3秒心跳


  getMqttClients().then(res => {
    if (res.data) {
      res.data.forEach((it: any) => {
        updateCardInfo(
          {
            client_id: it.clientid, connected: it.connected, name: undefined, ip: it.ip_address
          })
      });
    }
  })
};

export const releaseMqtt = () => {
  mqttHook.unSubscribe('terminal/#');
  mqttHook.unSubscribe(online_topic);
  mqttHook.unSubscribe(offline_topic);

  mqttHook.unRegisterEvent('on-connect', 'string_key');
  mqttHook.unRegisterEvent('terminal/beat', 'string_key');
};

/*
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2023-12-15 10:21:37
 * @LastEditors: zhai
 * @LastEditTime: 2023-12-15 10:26:00
 */
export function makeid(length: number=20) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
        counter += 1;
    }
    return result;
}
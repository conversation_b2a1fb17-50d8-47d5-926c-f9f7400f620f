/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-02-24 21:09:49
 * @LastEditors: zhai
 * @LastEditTime: 2025-04-26 10:25:20
 */
// stores/globalDataStore.ts
import { defineStore } from 'pinia';

export interface Recorder {
  name: string;
  desc: string;
  running: boolean;
  total_sec: number;
  record_sec: number;
}

export interface CardInfo {
  client_id: string; // 必填
  id?: string; 
  name?: string;
  connected?: boolean;
  started?: boolean;
  seconds?: number;
  ip?: string;
  sample_rate?: number;
  channels?: Array<Record<string, unknown>>;
  recorder?: Recorder;
}

// 定义全局变量的类型
export interface GlobalData {
  mqtt: {
    host: string;
    id: string;
  };

  card_map: Map<string, CardInfo>;
  cur_card: string;
}


export const useIotStore = defineStore('iotData', {
  state: (): GlobalData => ({
    mqtt: {
      host: '',
      id: ''
    },
    card_map: new Map<string, CardInfo>(),
    cur_card: ''
  })
});
